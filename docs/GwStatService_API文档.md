# GwStatService API接口文档

## 接口概述

GwStatService是一个基于oatpp框架开发的网关统计服务API控制器，主要提供以下功能：

- **监控统计**：提供网关运行状态、流量统计、性能监控等信息
- **授权管理**：处理网关授权文件上传、机器码获取、证书管理等功能
- **系统设置**：提供网关启停、配置管理、网口绑定等系统级操作
- **健康检查**：提供系统健康状态监控和任务状态管理

该服务使用Digest认证方式保护敏感接口，确保系统安全。

## 快速开始

### 基本配置
- **服务地址**：http://localhost:9876
- **认证方式**：HTTP Digest认证
- **默认账号**：用户名 `qzgw`，密码 `conf1711`

### 常用接口测试
```bash
# 1. 获取基本监控信息（无需认证）
curl -X GET http://localhost:9876/monitoring_info

# 2. 获取网卡信息（需要认证）
curl -X GET --digest -u qzgw:conf1711 http://localhost:9876/eth/eth_information

# 3. 获取任务状态（需要认证）
curl -X GET --digest -u qzgw:conf1711 http://localhost:9876/task_status

# 4. 获取最新统计信息（无需认证）
curl -X GET http://localhost:9876/last_stats
```

### 请求格式说明
- **JSON格式**：`Content-Type: application/json`
- **表单格式**：`Content-Type: application/x-www-form-urlencoded`
- **文件上传**：`Content-Type: multipart/form-data`

## 接口详情

### 1. 监控统计相关接口

#### 1.1 获取基本监控信息
- **接口路径**：`GET /monitoring_info`
- **功能说明**：获取网关基本监控信息
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "msg": "ok",
    "err": 0,
    "data": {
      "key1": "value1",
      "key2": "value2"
    }
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/monitoring_info
  ```

#### 1.2 获取统计信息
- **接口路径**：`GET /stats`
- **功能说明**：获取指定时间点后的统计信息，读取/opt/stats/目录下文件
- **认证要求**：无（注释中显示需要认证但已被注释）
- **请求参数**：
  - `startTime` (Int64, 必填)：起始时间戳
- **响应结果**：
  ```json
  {
    "msg": "ok",
    "err": 0,
    "data": [
      {
        "time_val": 1640995200,
        "recv_bytes_speed": 1024.5,
        "parser_http_cnt": 100,
        "parser_http_speed": 10.5,
        "cpu_usage": 25.6,
        "mem_usage": 45.2,
        "source_flag": 1,
        "eth_info": [
          {
            "device_name": "eth0",
            "total_packets": 1000,
            "drop_packets": 5,
            "eth_speed": 1000000000,
            "card_stat": "up"
          }
        ]
      }
    ]
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET "http://localhost:9876/stats?startTime=1640995200"
  ```

#### 1.3 获取数据源标志
- **接口路径**：`GET /source_flag` 或 `POST /source_flag`
- **功能说明**：查看source_flag，现为capture_mode
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "msg": "ok",
    "err": 0,
    "data": ["flag1", "flag2"]
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/source_flag
  curl -X POST http://localhost:9876/source_flag
  ```

#### 1.4 获取最新统计信息
- **接口路径**：`GET /last_stats`
- **功能说明**：获取最新一条状态信息
- **认证要求**：无（注释中显示需要认证但已被注释）
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "data": {
      "time_val": 1640995200,
      "recv_bytes_speed": 1024.5,
      "parser_http_cnt": 100,
      "cpu_usage": 25.6,
      "mem_usage": 45.2
    },
    "msg": "ok",
    "err": 0
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/last_stats
  ```

#### 1.5 获取错误日志
- **接口路径**：`GET /hw-stats/get_errlog`
- **功能说明**：获取系统错误日志
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": ["error log line 1", "error log line 2"]
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/hw-stats/get_errlog
  ```

#### 1.6 获取加密流量信息
- **接口路径**：`GET /hw-stats/encrypted_traffic`
- **功能说明**：获取SSL/TLS加密流量统计信息
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "data": {
      "proportion": {
        "ssl_v3": 100,
        "tls_v1": 200
      },
      "ssl_ips": {
        "***********": {
          "suite": "TLS_RSA_WITH_AES_128_CBC_SHA",
          "version": "TLSv1.2"
        }
      },
      "ssl_parser_bits": 1024000,
      "timestamp": 1640995200
    },
    "err": 0,
    "msg": "ok"
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/hw-stats/encrypted_traffic
  ```

### 2. 授权管理相关接口

#### 2.1 获取版本配置信息
- **接口路径**：`GET /hw-admin/local/getVersionConfig.do` 或 `POST /hw-admin/local/getVersionConfig.do`
- **功能说明**：获取网关基本信息和版本配置
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": {
      "version": "1.0.0",
      "build_time": "2023-01-01",
      "product_name": "Gateway"
    }
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/hw-admin/local/getVersionConfig.do
  curl -X POST http://localhost:9876/hw-admin/local/getVersionConfig.do
  ```

#### 2.2 获取授权详情
- **接口路径**：`GET /hw-admin/license/licenseDetail.do` 或 `POST /hw-admin/license/licenseDetail.do`
- **功能说明**：获取当前授权信息详情
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": {
      "license_type": "commercial",
      "expire_date": "2024-12-31",
      "max_connections": "1000"
    }
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/hw-admin/license/licenseDetail.do
  curl -X POST http://localhost:9876/hw-admin/license/licenseDetail.do
  ```

#### 2.3 获取机器码
- **接口路径**：`GET /hw-admin/license/machineCode.do` 或 `POST /hw-admin/license/machineCode.do`
- **功能说明**：获取当前设备的机器码，用于授权申请
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": {
      "machine_code": "ABC123DEF456"
    }
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/hw-admin/license/machineCode.do
  curl -X POST http://localhost:9876/hw-admin/license/machineCode.do
  ```

#### 2.4 上传授权文件
- **接口路径**：`POST /hw-admin/license/uploadLicense.do`
- **功能说明**：上传授权许可文件
- **认证要求**：无
- **请求参数**：文件上传（multipart/form-data）
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": "success"
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST -F "file=@license.lic" http://localhost:9876/hw-admin/license/uploadLicense.do
  ```

#### 2.5 导入证书
- **接口路径**：`POST /hw-license/upload_pem`
- **功能说明**：导入PEM格式证书文件
- **认证要求**：无
- **请求参数**：文件上传（multipart/form-data）
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": "success"
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST -F "file=@certificate.pem" http://localhost:9876/hw-license/upload_pem
  ```

#### 2.6 删除证书
- **接口路径**：`POST /hw-license/delete_pem`
- **功能说明**：删除指定的证书文件
- **认证要求**：无
- **请求参数**：
  - `filename` (String, 必填)：要删除的证书文件名
- **请求体示例**：
  ```
  certificate.pem
  ```
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "data": "success"
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST -H "Content-Type: text/plain" -d "certificate.pem" http://localhost:9876/hw-license/delete_pem
  ```

### 3. 系统设置相关接口

#### 3.1 域名转IP
- **接口路径**：`POST /domain_to_ip`
- **功能说明**：将域名解析为IP地址，自动配置minio、kafka、nacos等服务的域名映射
- **认证要求**：需要Digest认证
- **请求参数**：
  - **请求体格式**：JSON
  - **参数说明**：
    - `ip` (String, 必填)：要映射的IP地址
- **请求体示例**：
  ```json
  {
    "ip": "*************"
  }
  ```
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/json" \
    -d '{"ip": "*************"}' \
    http://localhost:9876/domain_to_ip
  ```

#### 3.2 丢弃HTTP文件事件
- **接口路径**：`POST /drop_http_file_event`
- **功能说明**：配置是否丢弃HTTP文件事件的规则
- **认证要求**：需要Digest认证
- **请求参数**：
  - **请求体格式**：JSON
  - **参数说明**：
    - `is_drop` (Integer, 必填)：是否丢弃文件事件，1=丢弃，0=保存
- **请求体示例**：
  ```json
  {
    "is_drop": 1
  }
  ```
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/json" \
    -d '{"is_drop": 1}' \
    http://localhost:9876/drop_http_file_event
  ```

#### 3.3 设置HTTP URL过滤
- **接口路径**：`POST /hw-filter/interactive_http_url_filter`
- **功能说明**：设置HTTP过滤的URL规则列表
- **认证要求**：无
- **请求参数**：
  - **请求体格式**：JSON
  - **参数说明**：
    - `urls` (Array[String], 必填)：要过滤的URL列表
- **请求体示例**：
  ```json
  {
    "urls": [
      "http://example.com/api/*",
      "https://test.com/upload/*",
      "*/admin/*"
    ]
  }
  ```
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "msg": "set http url filter success"
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST \
    -H "Content-Type: application/json" \
    -d '{"urls": ["http://example.com/api/*", "https://test.com/upload/*", "*/admin/*"]}' \
    http://localhost:9876/hw-filter/interactive_http_url_filter
  ```

#### 3.4 清空HTTP URL过滤
- **接口路径**：`GET /hw-filter/clean_interactive_http_url_filter`
- **功能说明**：清空所有HTTP URL过滤条件
- **认证要求**：无
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET http://localhost:9876/hw-filter/clean_interactive_http_url_filter
  ```

#### 3.5 控制策略设置
- **接口路径**：`POST /gw-hw/controlStrategy`
- **功能说明**：设置旁路阻断控制策略，支持添加和删除控制规则
- **认证要求**：需要Digest认证
- **请求参数**：
  - **请求体格式**：JSON
  - **参数说明**：
    - `parameter` (Integer, 必填)：操作类型，1=添加策略，2=删除策略
    - `id` (String, 必填)：策略ID
    - `controlName` (String, 添加时必填)：控制策略名称
    - `controlType` (String, 添加时必填)：控制类型
    - `controlContents` (Array[String], 添加时必填)：控制内容列表（如IP地址列表）
- **添加策略请求体示例**：
  ```json
  {
    "parameter": 1,
    "id": "strategy_001",
    "controlName": "阻断恶意IP",
    "controlType": "ip_block",
    "controlContents": [
      "*************",
      "*********",
      "************"
    ]
  }
  ```
- **删除策略请求体示例**：
  ```json
  {
    "parameter": 2,
    "id": "strategy_001"
  }
  ```
- **响应结果**：
  ```json
  {
    "success": "true",
    "errorCode": 0,
    "msg": "操作成功"
  }
  ```
- **调用示例**：
  ```bash
  # 添加控制策略
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/json" \
    -d '{
      "parameter": 1,
      "id": "strategy_001",
      "controlName": "阻断恶意IP",
      "controlType": "ip_block",
      "controlContents": ["*************", "*********", "************"]
    }' \
    http://localhost:9876/gw-hw/controlStrategy

  # 删除控制策略
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/json" \
    -d '{
      "parameter": 2,
      "id": "strategy_001"
    }' \
    http://localhost:9876/gw-hw/controlStrategy
  ```

### 4. 网关管理相关接口

#### 4.1 启用网关
- **接口路径**：`POST /gwhw/start_gwhw`
- **功能说明**：启动网关服务
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u username:password http://localhost:9876/gwhw/start_gwhw
  ```

#### 4.2 停用网关
- **接口路径**：`POST /gwhw/stop_gwhw`
- **功能说明**：停止网关服务
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u username:password http://localhost:9876/gwhw/stop_gwhw
  ```

#### 4.3 重启网关
- **接口路径**：`POST /gwhw/restart_gwhw`
- **功能说明**：重启网关服务
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u username:password http://localhost:9876/gwhw/restart_gwhw
  ```

#### 4.4 卸载网关
- **接口路径**：`POST /gwhw/uninstall_gwhw`
- **功能说明**：卸载网关服务
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u username:password http://localhost:9876/gwhw/uninstall_gwhw
  ```

#### 4.5 升级网关
- **接口路径**：`POST /gwhw/update_gwhw`
- **功能说明**：上传升级包并升级网关
- **认证要求**：无（注释中显示需要认证但已被注释）
- **请求参数**：文件上传（multipart/form-data）
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST -F "file=@update_package.tar.gz" http://localhost:9876/gwhw/update_gwhw
  ```

#### 4.6 状态检查
- **接口路径**：`POST /gwhw/status_check`
- **功能说明**：检查网关运行状态
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u username:password http://localhost:9876/gwhw/status_check
  ```

### 5. 网络管理相关接口

#### 5.1 网口绑定
- **接口路径**：`POST /gwhw/bind_eth`
- **功能说明**：绑定或解绑网络接口到网关，支持DPDK模式和普通模式
- **认证要求**：需要Digest认证
- **请求参数**：
  - **请求体格式**：JSON
  - **参数说明**：JSON对象，键为网卡名称，值为绑定状态
    - `网卡名称` (Integer, 必填)：1=绑定该网卡，0=解绑该网卡
- **请求体示例**：
  ```json
  {
    "eth0": 1,
    "eth1": 0,
    "eth2": 1
  }
  ```
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/json" \
    -d '{
      "eth0": 1,
      "eth1": 0,
      "eth2": 1
    }' \
    http://localhost:9876/gwhw/bind_eth
  ```

#### 5.2 获取网卡信息
- **接口路径**：`GET /eth/eth_information`
- **功能说明**：获取系统所有网卡的详细信息
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "msg": "ok",
    "err": 0,
    "data": {
      "bind": [
        {
          "eth_name": "eth0",
          "eth_speed": "1000Mbps",
          "flow": 1,
          "ip": "*************",
          "mirror": 0,
          "mirrorable": 1,
          "rx_speed": "100Mbps",
          "speed_unit": "Mbps",
          "status": "up",
          "tx_speed": "50Mbps"
        }
      ],
      "source_mode": ["mirror", "tap"],
      "unbind": [
        {
          "eth_name": "eth1",
          "eth_speed": "100Mbps",
          "flow": 0,
          "ip": "",
          "mirror": 0,
          "mirrorable": 1,
          "rx_speed": "0Mbps",
          "speed_unit": "Mbps",
          "status": "down",
          "tx_speed": "0Mbps"
        }
      ]
    }
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET --digest -c cookies -u qzgw:conf1711 http://127.0.0.1:9876/eth/eth_information
  ```

### 6. 健康检查和任务管理接口

#### 6.1 健康状态检查
- **接口路径**：`GET /health/mdr`
- **功能说明**：获取系统健康状态信息
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "msg": "ok",
    "err": 0,
    "data": {
      "info": {
        "cpu_usage": 25,
        "memory_usage": 45,
        "disk_usage": 60
      },
      "action": {
        "restart_count": 0,
        "error_count": 2
      }
    }
  }
  ```
- **调用示例**：
  ```bash
  curl -X GET --digest -u username:password http://localhost:9876/health/mdr
  ```

#### 6.2 Kafka SASL参数配置
- **接口路径**：`POST /kafka_sasl_param`
- **功能说明**：配置Kafka SASL认证参数，更新网关配置文件中的Kafka认证信息
- **认证要求**：需要Digest认证
- **请求参数**：
  - **请求体格式**：JSON
  - **参数说明**：
    - `sasl_enable` (String, 必填)：是否启用SASL认证，"true"或"false"
    - `username` (String, 必填)：Kafka认证用户名
    - `password` (String, 必填)：Kafka认证密码
    - `mechanisms` (String, 必填)：SASL机制，如"PLAIN"、"SCRAM-SHA-256"等
    - `security_protocol` (String, 必填)：安全协议，如"SASL_PLAINTEXT"、"SASL_SSL"等
- **请求体示例**：
  ```json
  {
    "sasl_enable": "true",
    "username": "kafka_user",
    "password": "kafka_password",
    "mechanisms": "PLAIN",
    "security_protocol": "SASL_PLAINTEXT"
  }
  ```
- **响应结果**：
  ```json
  {
    "error_msg": "success",
    "success": true
  }
  ```
- **调用示例**：
  ```bash
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/json" \
    -d '{
      "sasl_enable": "true",
      "username": "kafka_user",
      "password": "kafka_password",
      "mechanisms": "PLAIN",
      "security_protocol": "SASL_PLAINTEXT"
    }' \
    http://localhost:9876/kafka_sasl_param
  ```

#### 6.3 获取任务状态
- **接口路径**：`GET /task_status`
- **功能说明**：获取当前网关解析任务的运行状态
- **认证要求**：需要Digest认证
- **请求参数**：无
- **响应结果**：
  ```json
  {
    "success": "true",
    "errCode": 200,
    "msg": "start"
  }
  ```
  - **msg字段说明**：
    - `"start"`：任务正在运行
    - `"stop"`：任务已停止
- **调用示例**：
  ```bash
  curl -X GET --digest -u qzgw:conf1711 http://localhost:9876/task_status
  ```

#### 6.4 设置任务状态
- **接口路径**：`POST /task_start_stop`
- **功能说明**：启动或停止网关解析任务
- **认证要求**：需要Digest认证
- **请求参数**：
  - **请求体格式**：URL编码格式（application/x-www-form-urlencoded）
  - **参数说明**：
    - `taskFlag` (String, 必填)：任务操作标志
      - `"1"`：启动任务
      - `"2"`：停止任务
- **请求体示例**：
  ```
  taskFlag=1
  ```
- **响应结果**：
  ```json
  {
    "success": "true",
    "errCode": 200,
    "msg": "task start"
  }
  ```
  - **msg字段说明**：
    - `"task start"`：任务启动成功
    - `"task stop"`：任务停止成功
    - `"null"`：操作失败
- **调用示例**：
  ```bash
  # 启动任务
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "taskFlag=1" \
    http://localhost:9876/task_start_stop

  # 停止任务
  curl -X POST --digest -u qzgw:conf1711 \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "taskFlag=2" \
    http://localhost:9876/task_start_stop
  ```

## 数据结构说明

### 通用响应结构

#### MonitorMsg（监控消息）
```json
{
  "msg": "ok",           // 响应消息，默认"ok"
  "err": 0,              // 错误码，0表示成功
  "data": {}             // 数据内容，Map<String,String>格式
}
```

#### FlagMsg（标志消息）
```json
{
  "msg": "ok",           // 响应消息
  "err": 0,              // 错误码
  "data": []             // 字符串数组
}
```

#### BasicInfo（基本信息）
```json
{
  "success": "true",     // 操作是否成功
  "errorCode": 0,        // 错误码
  "data": {}             // 基本信息数据，Map格式
}
```

#### SettingMsg（设置消息）
```json
{
  "error_msg": "success", // 错误消息
  "success": true         // 操作是否成功
}
```

### 统计数据结构

#### StatsInfo（统计信息）
```json
{
  "time_val": 1640995200,              // 时间戳
  "recv_bytes_speed": 1024.5,          // 接收字节速度
  "parser_http_cnt": 100,              // HTTP解析数量
  "parser_http_speed": 10.5,           // HTTP解析速度
  "parser_http_zip_num": 50,           // HTTP压缩数量
  "parser_http_zip_speed": 5.2,        // HTTP压缩速度
  "parser_succ_http_cnt": 95,          // HTTP解析成功数量
  "up_kafka_cnt": 90,                  // 上传Kafka数量
  "up_succ_kafka_cnt": 88,             // 上传Kafka成功数量
  "up_succ_kafka_speed": 8.5,          // Kafka上传成功速度
  "up_succ_kafka_bytes_speed": 850.0,  // Kafka字节上传速度
  "cpu_usage": 25.6,                   // CPU使用率
  "mem_usage": 45.2,                   // 内存使用率
  "source_flag": 1,                    // 数据源标志
  "eth_info": [],                      // 网卡信息数组
  "filter": {},                        // 过滤器信息
  "ip_parser": {},                     // IP解析器信息
  "tcp_parser": {},                    // TCP解析器信息
  "tcp_stream": {},                    // TCP流信息
  "http": {},                          // HTTP信息
  "kafka": {},                         // Kafka信息
  "ftp_parser_bytes": {},              // FTP解析字节信息
  "http_parser_bytes": {},             // HTTP解析字节信息
  "ip_bytes": {},                      // IP字节信息
  "ssl_parser_bytes": {},              // SSL解析字节信息
  "tcp_single_stream": {}              // TCP单流信息
}
```

#### EthInfo（网卡信息）
```json
{
  "device_name": "eth0",     // 设备名称
  "total_packets": 1000,     // 总包数
  "drop_packets": 5,         // 丢包数
  "eth_speed": 1000000000,   // 网卡速度（bps）
  "card_stat": "up"          // 网卡状态
}
```

#### EthInfoDetail（网卡详细信息）
```json
{
  "eth_name": "eth0",        // 网卡名称
  "eth_speed": "1000Mbps",   // 网卡速度
  "flow": 1,                 // 流量标志
  "ip": "*************",     // IP地址
  "mirror": 0,               // 镜像标志
  "mirrorable": 1,           // 是否可镜像
  "rx_speed": "100Mbps",     // 接收速度
  "speed_unit": "Mbps",      // 速度单位
  "status": "up",            // 状态
  "tx_speed": "50Mbps"       // 发送速度
}
```

### SSL加密流量结构

#### SslInfo（SSL信息）
```json
{
  "proportion": {            // 加密协议比例
    "ssl_v3": 100,
    "tls_v1": 200
  },
  "ssl_ips": {              // SSL IP信息
    "***********": {
      "suite": "TLS_RSA_WITH_AES_128_CBC_SHA",
      "version": "TLSv1.2"
    }
  },
  "ssl_parser_bits": 1024000, // SSL解析位数
  "timestamp": 1640995200     // 时间戳
}
```

## 认证说明

本API服务使用HTTP Digest认证方式保护敏感接口。需要认证的接口在调用时必须提供有效的用户名和密码。

### 认证示例
```bash
# 使用curl进行Digest认证
curl -X GET --digest -u username:password http://localhost:9876/protected_endpoint

# 使用cookie保存认证信息
curl -X GET --digest -c cookies -u username:password http://localhost:9876/protected_endpoint
```

## 错误码说明

- **0**：操作成功
- **401**：未授权访问，需要提供有效的认证信息
- **其他非0值**：具体的业务错误码，详细信息在响应消息中说明

## 注意事项

1. **端口配置**：默认服务端口为9876，实际部署时请根据配置文件确认
2. **认证要求**：标记需要认证的接口必须提供有效的Digest认证信息，默认用户名密码为`qzgw:conf1711`
3. **文件上传**：涉及文件上传的接口使用multipart/form-data格式
4. **数据格式**：
   - JSON格式接口使用`Content-Type: application/json`
   - URL编码格式接口使用`Content-Type: application/x-www-form-urlencoded`
   - 纯文本接口使用`Content-Type: text/plain`
5. **时间戳**：所有时间相关字段使用Unix时间戳格式
6. **网络配置**：网卡相关操作需要管理员权限，请确保服务有足够的系统权限
7. **重要操作**：网关启停、网卡绑定等操作会影响系统运行，请谨慎操作
8. **配置文件**：部分接口会修改系统配置文件，建议操作前备份重要配置
9. **DPDK模式**：网卡绑定支持DPDK模式，绑定前请确认网卡没有配置IP地址
10. **任务管理**：任务启停操作会直接影响网关解析进程，请根据实际需要操作

## 版本信息

- **API版本**：基于oatpp框架开发
- **文档版本**：1.0
- **最后更新**：2024年
