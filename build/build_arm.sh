#!/bin/bash
source ./build_function.sh

function build_hw()
{
    VER=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n1)
	VER_FULL=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n2 | tail -n1)
	if [[ "${VER}" == "" ]]; then
		echo hw invalid version info
		return
	fi
	# 增加GIT版本号
 	GIT_VER_REVISION=
	GIT_VER_SHA_FULL=`git rev-parse HEAD`
	GIT_VER_SHA_SHORT=`git rev-parse --short HEAD`
	if [[ ! "${GIT_VER_REVISION}" == "" ]]; then
		VER=${VER}.${GIT_VER_REVISION}
	fi
	if [[ ! "${GIT_VER_SHA_SHORT}" == "" ]]; then
		VER=${VER}-${GIT_VER_SHA_SHORT}
	fi

	rm -rf "${BASEDIR}./hw/"
    mkdir -p "${BASEDIR}./hw/inst/"
    mkdir -p "${BASEDIR}./hw/inst/lib"
    mkdir -p "${BASEDIR}./hw/inst/rpm"
	mkdir -p "${BASEDIR}./hw/inst/kmod"
    mkdir -p "${BASEDIR}./hw/inst/stats_srv"
    mkdir -p "${BASEDIR}./hw/inst/tools"
	mkdir -p "${BASEDIR}./hw/inst/parser"
	mkdir -p "${BASEDIR}./hw/inst/source"
	mkdir -p "${BASEDIR}./hw/inst/upload"

    build_version

	#获取产品序列号
	if [[ -z ${PRODUCT_ID} ]];then
	    PRODUCT_ID=`cat "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h" | grep GW_PRODUCT_SERIAL_ID | awk '{print $3}' | awk -F '["]' '{print $2}'`
	else
		sed -i "s,GW_PRODUCT_SERIAL_ID.*,GW_PRODUCT_SERIAL_ID \"${PRODUCT_ID}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	fi

	# 替换GIT版本号
	#sed -i  "s,GW_VER_REVISION.*,GW_VER_REVISION ${GIT_VER_REVISION}," "${BASEDIR}../src/hw/gw_parser/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHA.*,GW_GIT_VER_SHA \"${GIT_VER_SHA_FULL}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHORT_SHA.*,GW_GIT_VER_SHORT_SHA \"${GIT_VER_SHA_SHORT}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"


	BLD_DIR=`pwd`/

    ## 编译主程序
    cd "${BLD_DIR}../src/hw/gw_parser/"
    make clean
    BUILD_SCHEME=Release_arm BUILD_ARCH=ARM BUILD_CC_TOOL=aarch64-linux-gnu-gcc RTE_SDK=/ make -j4
    if [ ! -f "./gw_parser" ]; then
        echo "gw_parser compile error!"
        exit 1
    fi

    ## 编译gw_stat_srv_oatpp
    cd "${BLD_DIR}../src/hw/gw_stats_srv_oatpp/"
    if [ -d "build" ]; then
        rm -rf "build"
    fi
    mkdir "build" && cd build/
    cmake .. -D CMAKE_BUILD_TYPE=Release -DBUILD_ARCH=ARM -DCMAKE_C_COMPILER=aarch64-linux-gnu-gcc -DCMAKE_CXX_COMPILER=aarch64-linux-gnu-g++
    make -j4

	cd "${BLD_DIR}"

	# openssl
	OPENSSL_TAR_GZ="`pwd`/hw/inst/openssl-bin-1.1.1w.tar.gz"
	cd /opt/openssl/
	tar czf "${OPENSSL_TAR_GZ}" .
	cd -

    ## 拷贝配置文件
    cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy.conf" "${BASEDIR}./hw/inst/"gw_parser.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_2.4.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_2.4.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_3.0.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_3.0.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_3.1.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_3.1.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_audit_2.7.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_audit_2.7.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_tools_task.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_tools_task.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_tools.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_tools.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_tools.conf" "${BASEDIR}./hw/inst/"gw_parser_tools.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/forward_info_rule_deploy.conf" "${BASEDIR}./hw/inst/"forward_info_rule.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/user_info_rule_deploy.conf" "${BASEDIR}./hw/inst/"user_info_rule.conf
    # supervisor服务配置文件
	cp -f "/home/<USER>/bin/supervisord" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/supervisord_hw.conf" "${BASEDIR}./hw/inst/"

	cp -f "${BASEDIR}../src/hw/rc-local.service" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/gwhw.service" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_black_list.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_black_list_tools.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_alloc_GB" "${BASEDIR}./hw/inst"
    cp -f "${BASEDIR}../src/hw/url_filter_base.file" "${BASEDIR}./hw/inst/"

	# 拷贝主进程和插件
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv_oatpp/build/gw_stats_srv-exe" "${BASEDIR}./hw/inst/stats_srv/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv_oatpp/gw_stats_srv.conf" "${BASEDIR}./hw/inst/stats_srv/"
	parser_sos="ftp_parser/ftp_parser.so hbase_parser/hbase_parser.so hdfs_parser/hdfs_parser.so hive_parser/hive_parser.so http_parser/http_parser.so http2_parser/http2_parser.so grpc_parser/grpc_parser.so mongo_parser/mongo_parser.so ssl_parser/ssl_parser.so oracle_parser/oracle_parser.so mail_parser/mail_parser.so smb_parser/smb_parser.so nfs_parser/nfs_parser.so"
	for parser_so in $parser_sos
	do
    	cp -f "${BASEDIR}../src/hw/gw_parser/parser/$parser_so" "${BASEDIR}./hw/inst/parser"
	done

	src_sos="file_source/file_source.so dpdk_source/dpdk_source.so nic_source/nic_source.so pcap_source/pcap_source.so"
	for src_so in $src_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/source/$src_so" "${BASEDIR}./hw/inst/source"
	done

	upload_sos="log_upload/log_upload.so kafka_upload/kafka_upload.so diy_upload/diy_upload.so"
	for upload_so in $upload_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/upload/$upload_so" "${BASEDIR}./hw/inst/upload"
	done

	## 拷贝第三方库
	cp -f "/home/<USER>/lib/"* "${BASEDIR}./hw/inst/lib"

	cp -f "/home/<USER>/3rd/aws-sdk-cpp/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/curl/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/dpdk-stable-23.11.3/lib64/"*.so.24 "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/iconv/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/libgcrypt/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/libgpg-error/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/libmagic/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/libpcap-1.9.1/libpcap.so.1.9.1" "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/librdkafka/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/licutils/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/nacos-sdk-cpp/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/oatpp/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/openssl/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/PF_RING-8.0.0/userland/lib/libpfring.so.8.0.0" "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/PF_RING-8.0.0/userland/lib/libpfring_min.so.8.0.0" "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/protobuf/lib/"* "${BASEDIR}./hw/inst/lib"
	cp -f "/home/<USER>/3rd/zlib/lib/"* "${BASEDIR}./hw/inst/lib"

	#文件格式解析动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/utils/file_type/libfile_type.so" "${BASEDIR}./hw/inst/lib"

	## 拷贝驱动
	cp -r "/home/<USER>/kmod/"* "${BASEDIR}./hw/inst/kmod"

	cp -f "${BASEDIR}../src/hw/init_dpdk.sh" "${BASEDIR}./hw/inst/tools"
	filter_sh_comment "${BASEDIR}./hw/inst/tools/init_dpdk.sh"
	cp -f "${BASEDIR}../src/hw/dpdk_unbind.sh" "${BASEDIR}./hw/inst/tools"
	filter_sh_comment "${BASEDIR}./hw/inst/tools/dpdk_unbind.sh"
	cp -f "${BASEDIR}../src/hw/init_qat.sh" "${BASEDIR}./hw/inst/tools"
	filter_sh_comment "${BASEDIR}./hw/inst/tools/init_qat.sh"
	cp -f "${BASEDIR}../src/tools/uninstall.sh" "${BASEDIR}./hw/inst/tools/"uninst_def_svr.sh
	filter_sh_comment "${BASEDIR}./hw/inst/tools/uninst_def_svr.sh"
    cp -f "${BASEDIR}../src/tools/file_monitor.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/burnintest.sh" "${BASEDIR}./hw/inst/tools"

	cp -f "${BASEDIR}../src/tools/update_gwhw.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/telnet" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/tcpreplay" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/rollback_gwhw.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/filter_sample.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/inst/uninstall_gw.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/gw_sys_info.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/agent_upload_quota.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/load_driver.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/set_irq_affinity" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/data_analyze.py" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/troubleshooting_tool.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/web_uninst.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/renice_sshd.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/licutils_demo" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/fuse.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/check.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/bind_eth.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/checklist.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/dpdk-devbind_arm" "${BASEDIR}./hw/inst/tools/dpdk-devbind"
	cp -f "${BASEDIR}../src/tools/check_pfring.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/status_check.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/watch_dog.sh" "${BASEDIR}./hw/inst/tools"
	cp -f "${BASEDIR}../src/tools/lspci_arm" "${BASEDIR}./hw/inst/tools/lspci"
	cp -f "${BASEDIR}../src/tools/tcpdump_arm" "${BASEDIR}./hw/inst/tools/tcpdump"
	cp -f "${BASEDIR}../src/tools/nload_arm" "${BASEDIR}./hw/inst/tools/nload"

    # 去除文件中注释
	cp -f "${BASEDIR}./../src/inst/inst_arm.sh" "${BASEDIR}./hw/inst/inst.sh"
	filter_sh_comment "${BASEDIR}./hw/inst/inst.sh"
	cp -f "${BASEDIR}./../src/inst/inst_function.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/inst_function.sh"
	cp -f "${BASEDIR}./../src/inst/inst_node_exporter.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./../src/inst/readme_hw.txt" "${BASEDIR}./hw/inst/readme.txt"
	cp -f "${BASEDIR}./../src/inst/Changelog_hw" "${BASEDIR}./hw/inst/Changelog"

	# 拷贝rpm包
	cp -r "/home/<USER>/rpm/"* "${BASEDIR}./hw/inst/rpm"

    cp "${BASEDIR}./../src/hw/gw_parser/libmagic/mgc/magic.mgc" "${BASEDIR}./hw/inst/magic.mgc"

	wget  https://installq.oss-cn-shanghai.aliyuncs.com/agent-v10/arm/qzkj_agent_server-v1.4.0_20250515065920.zip -O "${BASEDIR}./hw/inst/qzkj_agent_server.zip"

	cd "${BASEDIR}./hw/"

	zip -r inst-${VER}-${PRODUCT_ID}-$(date -u '+%Y%m%dT%H%M%SZ')_ARM.zip inst/  -x .DS_Store -x \*/.DS_Store
	cd "${BLD_DIR}"
}


function main()
{
	echo build begin $(date "+%F %T")
	echo "product id  $PRODUCT_ID "

	func_log  build_hw

	func_log  build_out

	echo build end $(date "+%F %T")
	return
}

if [ $# -eq 0 ]; then
  BUILD_TYPE=hw
elif [ $# -eq 1 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
	PRODUCT_ID=$1
	BUILD_TYPE=hw
  else
	BUILD_TYPE=$1
  fi
elif [ $# -ge 2 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
    echo "Usage: $0 [hw|pcap]"
	echo "  [hw]    hardware version, with dpdk"
	echo "  [pcap] software version, without dpdk, for pcap"
 	exit 1
  else
    BUILD_TYPE=$1
    PRODUCT_ID=$2
  fi
fi

export BUILD_TYPE=$BUILD_TYPE

echo "product id $PRODUCT_ID"
main $PRODUCT_ID
