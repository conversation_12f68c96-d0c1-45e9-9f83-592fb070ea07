/*
 */

#if defined(_CC_CLANG_PP) || defined(_CC_GNU_PP) 
// for linux clang++ g++
#undef _GNU_SOURCE
#undef _SVID_SOURCE
#undef _POSIX_C_SOURCE
#undef _XOPEN_SOURCE
#undef _POSIX_SOURCE
#include <netinet/tcp.h>
#endif

#include <sys/types.h>
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h>
#include <netinet/in_systm.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/ip_icmp.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <pthread.h>
#include <linux/if_ether.h>
#include <linux/if_packet.h>

#include "pp.h"
#include "pp_checksum.h"
#include "pp_tcp.h"
#include "util.h"
#include "utils.h"
#include "pp_hash.h"
#include <assert.h>

#if !HAVE_TCP_STATES
enum
{
    TCP_ESTABLISHED = 1,
    TCP_SYN_SENT,
    TCP_SYN_RECV,
    TCP_FIN_WAIT1,
    TCP_FIN_WAIT2,
    TCP_TIME_WAIT,
    TCP_CLOSE,
    TCP_CLOSE_WAIT,
    TCP_LAST_ACK,
    TCP_LISTEN,
    TCP_CLOSING /* now a valid state */
};

#endif

extern struct proc_node *tcp_procs;

extern int g_conf_tcp_lost_packet_neighbour_ignore;
extern int g_conf_stream_debug;
extern int g_conf_single_stream_debug;
extern int g_conf_tcp_stream_cache_max_size;
extern int g_conf_tcp_stream_first_cache_max_size;
extern int g_conf_tcp_stream_lost_hold_min_ms;
extern int g_conf_tcp_stream_lost_hold_min_bytes;
extern char g_conf_tcp_nosyn_init_state;
extern int g_conf_tcp_stream_timeout_sec;
extern int g_conf_tcp_stream_drop_size;
extern int g_conf_tcp_single_stream_packet_max_num; // 单边流量检测使用，当tcp连接未断开前，接收到到 packet 数到达 g_conf_tcp_single_stream_packet_max_num 且某一侧没有收到包，则为单边流量。
extern int g_conf_tcp_single_stream_packet_min_num; // 单边流量检测使用，当tcp连接断开时，接收到的 packet 数小于 g_conf_tcp_single_stream_packet_min_num 不进行单边流量判断。

extern int interrupt_enable;
extern char interrupt_ip[16];
extern char interrupt_eth_name[16];

extern bool is_hit_control_strategy(const char* src_ip, const char* dst_ip);

addr_port_hit_func p_hit_func = NULL;
port_filter_hit_func p_port_filter_func = NULL;
port_white_hit_func  p_port_white_func = NULL;


volatile tcp_parser_lost_packets tcp_lost = {0};
uint8_t tcp_lost_count;
pthread_mutex_t tcp_lost_mutex = PTHREAD_MUTEX_INITIALIZER;
__thread tcp_parser_lost_packets *tcp_lost_per_thread = NULL;
tcp_parser_lost_packets *tcp_lost_array[64];

void tcp_lost_register(tcp_parser_lost_packets *tcp_lost)
{
  pthread_mutex_lock(&tcp_lost_mutex);
  tcp_lost_array[tcp_lost_count] = tcp_lost;
  tcp_lost_count++;
  pthread_mutex_unlock(&tcp_lost_mutex);
}

volatile tcp_stream_lost_bytes stream_lost = {0};

static void add_tcp_closing_timeout(void *par, struct tcp_stream *a_tcp)
{
    worker_params_t *pwp = (worker_params_t *)par;
    struct tcp_timeout *newto;
    
    if (!gw_pp_params.tcp_workarounds)
        return;
    
    if (a_tcp->timeout)
    {
        newto = a_tcp->timeout;
        newto->timeout.tv_sec = pwp->pp_last_pcap_header->ts.tv_sec + g_conf_tcp_stream_timeout_sec;
        
        if (newto->prev)
        {
            newto->prev->next = newto->next;
        }
        else
        {
            pwp->pp_tcp_timeouts = newto->next;
        }
        
        if (newto->next)
        {
            newto->next->prev = newto->prev;
        }
        else
        {
            pwp->pp_tcp_timeouts_tail = newto->prev;
        }
    }
    else
    {
        newto = malloc(sizeof(struct tcp_timeout));
        if (!newto)
        {
            gw_pp_params.no_mem("add_tcp_closing_timeout");
        }
        newto->a_tcp = a_tcp;
        newto->timeout.tv_sec = pwp->pp_last_pcap_header->ts.tv_sec + g_conf_tcp_stream_timeout_sec;
        newto->prev = 0;
    }
    
    if (pwp->pp_tcp_timeouts_tail )
    {
        pwp->pp_tcp_timeouts_tail->next = newto;
        newto->prev = pwp->pp_tcp_timeouts_tail;
        newto->next = NULL;
        pwp->pp_tcp_timeouts_tail = newto;
    }
    else
    {
        pwp->pp_tcp_timeouts = pwp->pp_tcp_timeouts_tail = newto;
        newto->prev = NULL;
        newto->next = NULL;
    }
    
    a_tcp->timeout = newto;
    return;
}

static void del_tcp_closing_timeout(void *par, struct tcp_stream *a_tcp)
{
    worker_params_t *pwp = (worker_params_t *)par;
    struct tcp_timeout *to;
    
    if (!gw_pp_params.tcp_workarounds)
        return;
    
    if (a_tcp->timeout)
    {
        to = a_tcp->timeout;
        
        if (to->prev)
        {
            to->prev->next = to->next;
        }
        else
        {
            pwp->pp_tcp_timeouts = to->next;
        }
        
        if (to->next)
        {
            to->next->prev = to->prev;
        }
        else
        {
            pwp->pp_tcp_timeouts_tail = to->prev;
        }
        free(to);
        a_tcp->timeout = NULL;
        return ;
    }
}

int free_tcp_stream(void *par, struct tcp_stream *a_tcp) // modify name
{
    worker_params_t *pwp = (worker_params_t *)par;
    int hash_index = a_tcp->hash_index;
    int num = 0;
    
    del_tcp_closing_timeout(pwp, a_tcp);
    //    num += purge_queue(&a_tcp->server);
    //    num += purge_queue(&a_tcp->client);
    
    if (a_tcp->next_node)
        a_tcp->next_node->prev_node = a_tcp->prev_node;
    if (a_tcp->prev_node)
        a_tcp->prev_node->next_node = a_tcp->next_node;
    else
        pwp->tcp_stream_table[hash_index] = a_tcp->next_node;
    if (a_tcp->client.data)
    {
        free(a_tcp->client.data);
    }
    
    if (a_tcp->client.count>a_tcp->client.offset)
    {
        num++;
    }
    if (a_tcp->server.count>a_tcp->server.offset)
    {
        num++;
    }
    
    
    if (a_tcp->server.data)
    {
        free(a_tcp->server.data);
    }

    if (1 == a_tcp->is_http)
    {
        pwp->http_num--;

        // del from http list
        if (a_tcp == pwp->http_oldest)
        {
            pwp->http_oldest = a_tcp->prev_http;
        }

        if (a_tcp == pwp->http_latest)
        {
            pwp->http_latest = a_tcp->next_http;
        }

        if (NULL != a_tcp->next_http)
        {
            a_tcp->next_http->prev_http = a_tcp->prev_http;
        }

        if (NULL != a_tcp->prev_http)
        {
            a_tcp->prev_http->next_http = a_tcp->next_http;
        }
    }
    else
    {
        // del from time list
        if (a_tcp->next_time)
            a_tcp->next_time->prev_time = a_tcp->prev_time;
        if (a_tcp->prev_time)
            a_tcp->prev_time->next_time = a_tcp->next_time;
        if (a_tcp == pwp->tcp_oldest)
            pwp->tcp_oldest = a_tcp->prev_time;
        if (a_tcp == pwp->tcp_latest)
            pwp->tcp_latest = a_tcp->next_time;
    }

    a_tcp->is_http = 0;
    a_tcp->p_session = NULL;
    a_tcp->next_free = pwp->free_streams;
    pwp->free_streams = a_tcp;
    pwp->tcp_num--;
    return num;
}

static void pushRcvStream(struct half_stream *rcv)
{
    struct skbuff *head = rcv->head;
    struct skbuff *node = head;
    
    // 预先多分配200字节,用于保存丢包的信息,缓存100MB以上的数据时可能会发生异常
    rcv->data = (char *)malloc(rcv->stream_cache_size + 200);
    rcv->data_len = 0;
    rcv->count = 0;
    rcv->complete = 1;

    int lost_len = 0;

    // 最多只记录5次,超过5次记录了也没意义
    int missing_record_count = 0;
    char missing_info[30] = {0};
    
    for (; node->next != NULL; )
    {
        memcpy(rcv->data + rcv->data_len, node->data, node->len);
        rcv->data_len += node->len;
        rcv->count += node->len;
        
        if ((node->seq + node->len) != node->next->seq)
        {
            if (rcv->first_lost_offset == 0)
            {
                //第一次丢包的数据偏移
                rcv->first_lost_offset = rcv->data_len;
                rcv->complete = 0;
            }
            lost_len = node->next->seq - node->seq - node->len;

            rcv->lost_len += lost_len;

            if (missing_record_count < 5)
            {
                memset(missing_info, 0, 30);
                sprintf(missing_info, "[%d bytes missing]", lost_len);
                int len = strlen(missing_info);
                memcpy(rcv->data + rcv->data_len, missing_info, len);
                rcv->data_len += len;
                rcv->count += len;
                rcv->missing_info_len += len;
                missing_record_count++;
            }
        }
        
        rcv->head = rcv->head->next;
        rcv->head->prev = NULL;
        
        SAFE_FREE(node->data);
        SAFE_FREE(node);
        
        node = rcv->head;
    }
    
    if (node->next == NULL)
    {
        memcpy(rcv->data + rcv->data_len, node->data, node->len);
        rcv->data_len += node->len;
        rcv->count += node->len;
        
        SAFE_FREE(node->data);
        SAFE_FREE(node);
    }
    
    if(rcv->node)
    {
        rcv->head = rcv->node;
        rcv->tail = rcv->node;
        rcv->ack = rcv->node->ack;
        rcv->seq = rcv->node->seq;
        rcv->stream_cache_size = rcv->node->len;
        rcv->node = NULL;
    }
    else
    {
        rcv->head = NULL;
        rcv->tail = NULL;
        rcv->ack = 0;
        rcv->seq = 0;
        rcv->stream_cache_size = 0;
    }
    
    
    return;
}

static void close_tcp_stream(void *par, struct tcp_stream *a_tcp, int state)
{
    if (NULL == a_tcp || NULL == par)
    {
        return;
    }
    
    worker_params_t *pwp = (worker_params_t *)par;
    
    // TODO 会话解释释放session等资源，后面需要优化
    if (a_tcp->client.head != NULL)
    {
        a_tcp->pp_state = PP_DATA;
        pushRcvStream(&a_tcp->client);
        a_tcp->dir = STREAM_REQ;
        tcp_procs->item(pwp, a_tcp, 0);
    }

    if (a_tcp->server.head != NULL)
    {
        a_tcp->pp_state = PP_DATA;
        pushRcvStream(&a_tcp->server);
        a_tcp->dir = STREAM_RSP;
        tcp_procs->item(pwp, a_tcp, 0);
    }
    
    //TODO 状态太多
    if (state == PP_SESSION_OVERFULL)
    {
        a_tcp->pp_state = PP_TIMED_OUT;
    }
    else
    {
        a_tcp->pp_state = state;
    }
    tcp_procs->item(pwp, a_tcp, 0);
    
    int num = free_tcp_stream(pwp, a_tcp);
    
    if(num <= 0)
        return;
    
    switch(state)
    {
        case PP_TIMED_OUT:
            __sync_fetch_and_add(&(tcp_lost.timeout), 1);
            break;
        case PP_SESSION_OVERFULL:
            __sync_fetch_and_add(&(tcp_lost.session_overfull), 1);
            break;
        case PP_RESET:
            __sync_fetch_and_add(&(tcp_lost.reset), 1);
            break;
        case PP_CLOSE:
            __sync_fetch_and_add(&(tcp_lost.session_close), 1);
            break;
        default:
            break;
    }
    
}

static void cacheFirstPacket(struct half_stream *snd, int datalen, struct tcphdr *tcphdr, u_int seq, u_int ack)
{
    struct skbuff *packet = NULL;
    
    packet = mknew(struct skbuff);
    packet->len = datalen;
    packet->data = (char*)malloc(datalen);
    memcpy(packet->data, (char *)(tcphdr) + 4 * tcphdr->th_off,  datalen);
    packet->seq = seq;
    packet->ack = ack;
    snd->ack = ack;
    snd->seq = seq;
//    snd->current_packet_ack = ack;
    
    packet->next = NULL;
    packet->prev = NULL;
    snd->head = packet;
    snd->tail = packet;
    snd->first_seq = seq;
    snd->stream_ack = ack;
    snd->stream_cache_size = datalen;
}

static void cacheNewHalfStream(struct half_stream *snd, int datalen, struct tcphdr *tcphdr, u_int seq, u_int ack)
{
    
    //TODO, 不将该数据放到列表中可能会更好，push中的逻辑可以更清晰
    struct skbuff *packet = NULL;
    
    packet = mknew(struct skbuff);
    packet->len = datalen;
    packet->data = (char*)malloc(datalen);
    memcpy(packet->data, (char *)(tcphdr) + 4 * tcphdr->th_off,  datalen);
    packet->seq = seq;
    packet->ack = ack;
    packet->next = NULL;
    packet->prev = NULL;
    
    snd->node = packet;
    
//    snd->current_packet_ack = ack;
//
//    packet->next = NULL;
//    packet->prev = snd->tail;
//    snd->tail->next = packet;
//    snd->tail = packet;
}

static void cacheLaterHalfStream(struct half_stream *snd, int datalen, struct tcphdr *tcphdr, u_int seq, u_int ack, struct skbuff *node)
{
    struct skbuff *packet = NULL;
    
    packet = mknew(struct skbuff);
    snd->stream_cache_size += datalen;
    packet->len = datalen;
    packet->data = (char*)malloc(datalen);
    memcpy(packet->data, (char *)(tcphdr) + 4 * tcphdr->th_off,  datalen);
    packet->seq = seq;
    packet->ack = ack;
//    snd->current_packet_ack = ack;
    
    if(node)
    {
        if(node == snd->tail)
        {
            packet->next = NULL;
            packet->prev = snd->tail;
            snd->tail->next = packet;
            snd->tail = packet;
            snd->seq = packet->seq;
        }
        else
        {
            packet->next = node->next;
            node->next->prev = packet;
            
            node->next = packet;
            packet->prev = node;
        }
    }
    else
    {
        //插入到第一帧
        snd->first_seq = seq;
        packet->prev = NULL;
        packet->next = snd->head;
        snd->head->prev = packet;
        snd->head = packet;
        
    }
    return;
    
}

static struct skbuff* getInsertLocation(struct half_stream *snd, u_int seq, int* insert)
{
    struct skbuff *node = NULL;
    node = snd->tail;
    
    if (node == NULL)
    {
        return NULL;
    }
    
    //找到要插入在后面的node节点
    do{
        if(node->seq < seq)
        {
            if((node->seq + node->len) <= seq)
            {
                break;
            }
            else
            {
                *insert = 0;
                return NULL;
            }
        }
        
        if(node->seq == seq)
        {
            //重复包丢弃
            *insert = 0;
            return NULL;
        }
        
        node = node->prev;
    }while(node);
    
    return node;
}

static void cacheStream(struct half_stream *snd, int datalen, struct tcphdr *tcphdr, u_int seq, u_int ack)
{
    //滞后包丢弃，可能会造成单边或数据不完整，但是也没办法，不然方案就很复杂了
    if (ack < snd->ack)
    {
        return;
    }
    
    //上报数据后的第一条数据
    if (snd->head == NULL)
    {
        cacheFirstPacket(snd, datalen, tcphdr, seq, ack);
        return;
    }
    else if (ack == snd->ack)
    {
        // 超出阈值丢弃数据
        if (snd->stream_cache_size > g_conf_tcp_stream_cache_max_size)
        {
            __sync_fetch_and_add(&(tcp_lost.cache_overfull), 1);
            return;
        }
        int insert = 1;
        struct skbuff *node = getInsertLocation(snd, seq, &insert);
        
        if (insert != 1)
        {
            return;
        }
        
        cacheLaterHalfStream(snd, datalen, tcphdr, seq, ack, node);
        
        return;
    }
    else if (ack > snd->ack)
    {
        //收集不更新当前数据流的seq ack信息
        cacheNewHalfStream(snd, datalen, tcphdr, seq, ack);
        return;
    }
}

static void pushStream(struct half_stream *snd, struct half_stream *rcv, struct tcphdr *tcphdr, worker_params_t *pwp, struct tcp_stream *a_tcp)
{
    //只要有新数据流肯定上传缓存的数据
    if (snd->node && snd->head)
    {
        add_tcp_closing_timeout(pwp, a_tcp);
        pushRcvStream(snd);
        a_tcp->pp_state = PP_DATA;
        tcp_procs->item(pwp, a_tcp, 0);
        SAFE_FREE(snd->data);
        snd->data = NULL;
        snd->first_lost_offset = 0;
        snd->lost_len = 0;
        rcv->missing_info_len = 0;
        snd->first_seq = snd->seq;
        snd->stream_ack = snd->ack;
    }
    
    if (tcphdr->th_flags & TH_FIN)
    {
        snd->fin_state = 1;
        if (snd->fin_state && rcv->fin_state)
        {
            close_tcp_stream(pwp, a_tcp, PP_CLOSE);
            snd->fin_state = 0;
            rcv->fin_state = 0;
        }
    }
    
    return;
}

void tcp_check_timeouts(void *par, struct timeval *now)
{
    worker_params_t *pwp = (worker_params_t *)par;
    struct tcp_timeout *to;
    struct tcp_timeout *next;
    
    for (to = pwp->pp_tcp_timeouts; to; to = next)
    {
        if (now->tv_sec < to->timeout.tv_sec)
            return;
        
        next = to->next;
        close_tcp_stream(pwp, to->a_tcp, PP_TIMED_OUT);
    }
}

/* 兼容IPv4和IPv6 */
static int mk_hash_index_force_new(void *par, struct tuple4 addr, int is_from_client)
{
    worker_params_t *pwp = (worker_params_t *)par;
    u_int saddr = 0;
    u_int daddr = 0;
    if (addr.i_ip_type == 0) //ipv4
    {
        saddr = addr.saddr;
        daddr = addr.daddr;
    }
    else // ipv6
    {
        //printf ("saddr1 = 0x%4x, saddr2 = 0x%4x, saddr3 = 0x%4x, saddr4 = 0x%4x\n", addr.a_saddr[0], addr.a_saddr[1], addr.a_saddr[2], addr.a_saddr[3]);
        //printf ("daddr1 = 0x%4x, daddr2 = 0x%4x, daddr3 = 0x%4x, daddr4 = 0x%4x\n", addr.a_daddr[0], addr.a_daddr[1], addr.a_daddr[2], addr.a_daddr[3]);
        saddr = addr.a_saddr[0] ^ addr.a_saddr[1] ^ addr.a_saddr[2] ^ addr.a_saddr[3];
        daddr = addr.a_daddr[0] ^ addr.a_daddr[1] ^ addr.a_daddr[2] ^ addr.a_daddr[3];
    }
    
    if (pwp->i_gre_flag == 1) // gre
    {
        if (pwp->i_gre_ip_type == 0) // gre ipv4
        {
            if (is_from_client)
            {
                saddr = (saddr ^ pwp->gre_saddr);
                daddr = (daddr ^ pwp->gre_daddr);
            }
            else
            {
                saddr = (saddr ^ pwp->gre_daddr);
                daddr = (daddr ^ pwp->gre_saddr);
            }
        }
        else // ipv6
        {
            if (is_from_client)
            {
                saddr = (saddr ^ pwp->a_gre_in6_saddr[0] ^ pwp->a_gre_in6_saddr[1] ^ pwp->a_gre_in6_saddr[2] ^ pwp->a_gre_in6_saddr[3]);
                daddr = (daddr ^ pwp->a_gre_in6_daddr[0] ^ pwp->a_gre_in6_daddr[1] ^ pwp->a_gre_in6_daddr[2] ^ pwp->a_gre_in6_daddr[3]);
            }
            else
            {
                saddr = (saddr ^ pwp->a_gre_in6_daddr[0] ^ pwp->a_gre_in6_daddr[1] ^ pwp->a_gre_in6_daddr[2] ^ pwp->a_gre_in6_daddr[3]);
                daddr = (daddr ^ pwp->a_gre_in6_saddr[0] ^ pwp->a_gre_in6_saddr[1] ^ pwp->a_gre_in6_saddr[2] ^ pwp->a_gre_in6_saddr[3]);
            }
        }
    }
    
    int hash = mkhash(saddr, addr.source, daddr, addr.dest);
    
    //printf ("saddr = %u, daddr = %u\n", saddr, daddr);
    return hash % pwp->tcp_stream_table_size;
}

// static int get_ts(struct tcphdr *this_tcphdr, unsigned int *ts)
// {
//     int len = 4 * this_tcphdr->th_off;
//     unsigned int tmp_ts;
//     unsigned char *options = (unsigned char *)(this_tcphdr + 1);
//     int ind = 0, ret = 0;
//     while (ind <= len - (int)sizeof(struct tcphdr) - 10)
//         switch (options[ind])
//         {
//             case 0: /* TCPOPT_EOL */
//                 return ret;
//             case 1: /* TCPOPT_NOP */
//                 ind++;
//                 continue;
//             case 8: /* TCPOPT_TIMESTAMP */
//                 memcpy((char *)&tmp_ts, options + ind + 2, 4);
//                 *ts = ntohl(tmp_ts);
//                 ret = 1;
//                 /* no break, intentionally */
//             default:
//                 if (options[ind + 1] < 2) /* "silly option" */
//                     return ret;
//                 ind += options[ind + 1];
//         }
    
//     return ret;
// }

// static int get_wscale(struct tcphdr *this_tcphdr, unsigned int *ws)
// {
//     int len = 4 * this_tcphdr->th_off;
//     unsigned int tmp_ws;
//     unsigned char *options = (unsigned char *)(this_tcphdr + 1);
//     int ind = 0, ret = 0;
//     *ws = 1;
//     while (ind <= len - (int)sizeof(struct tcphdr) - 3)
//         switch (options[ind])
//         {
//             case 0: /* TCPOPT_EOL */
//                 return ret;
//             case 1: /* TCPOPT_NOP */
//                 ind++;
//                 continue;
//             case 3: /* TCPOPT_WSCALE */
//                 tmp_ws = options[ind + 2];
//                 if (tmp_ws > 14)
//                     tmp_ws = 14;
//                 *ws = 1 << tmp_ws;
//                 ret = 1;
//                 /* no break, intentionally */
//             default:
//                 if (options[ind + 1] < 2) /* "silly option" */
//                     return ret;
//                 ind += options[ind + 1];
//         }
    
//     return ret;
// }

static void add_new_tcp_new(void *par, struct tcphdr *this_tcphdr, void *this_ip)
{
    worker_params_t *pwp = (worker_params_t *)par;
    struct tcp_stream *to_link = NULL;
    struct tcp_stream *a_tcp = NULL;
    int hash_index = 0;
    struct tuple4 addr;
    memset(&addr, 0, sizeof(struct tuple4));
    struct ip *this_iphdr = NULL;
    struct ip6_hdr *this_ip6hdr = NULL;
    
    addr.source = ntohs(this_tcphdr->th_sport);
    
    addr.dest = ntohs(this_tcphdr->th_dport);
    if (pwp->i_ip_type == 0) // ipv4
    {
        this_iphdr = (struct ip *)this_ip;
        addr.i_ip_type = 0;
        addr.saddr = this_iphdr->ip_src.s_addr;
        addr.daddr = this_iphdr->ip_dst.s_addr;
    }
    else // ipv6
    {
        this_ip6hdr = (struct ip6_hdr *)this_ip;
        addr.i_ip_type = 1;
        memcpy(addr.a_saddr, this_ip6hdr->ip6_src.s6_addr32, sizeof(addr.a_saddr));
        memcpy(addr.a_daddr, this_ip6hdr->ip6_dst.s6_addr32, sizeof(addr.a_daddr));
    }
    
    hash_index = mk_hash_index_force_new(pwp, addr, 1);
    if (pwp->tcp_num > pwp->max_stream)
    {
        struct tcp_stream* close_stream = pwp->tcp_oldest;
        if (NULL == close_stream)
        {
            __sync_fetch_and_add(&(tcp_lost.close_http),1);
            close_stream = pwp->http_oldest;
        }

        if (1 == close_stream->is_http)
        {
            __sync_fetch_and_add(&(tcp_lost.close_http_http),1);
        }

        close_tcp_stream(pwp, close_stream, PP_SESSION_OVERFULL);
    }
    
    a_tcp = pwp->free_streams;
    if (!a_tcp)
    {
        fprintf(stderr, "gdb me ... \n");
        pause();
    }
    
    pwp->free_streams = a_tcp->next_free;
    pwp->tcp_num++;
    if (pwp->tcp_num > pwp->tcp_run_max_stream) {
        pwp->tcp_run_max_stream = pwp->tcp_num;
    }
    to_link = pwp->tcp_stream_table[hash_index];
    memset(a_tcp, 0, sizeof(struct tcp_stream));
    a_tcp->hash_index = hash_index;
    a_tcp->addr = addr;
    if (pwp->p_mac && pwp->bool_need_mac)
    {
        memcpy(a_tcp->server_mac, pwp->p_mac, 6);
        memcpy(a_tcp->client_mac, pwp->p_mac + 6, 6);
    }
    
    if (pwp->pcap_filename)
    {
        strcpy(a_tcp->pcap_filename, pwp->pcap_filename);
    }
    a_tcp->next_node = to_link;
    a_tcp->prev_node = 0;
    a_tcp->ts = pwp->pp_last_pcap_header->ts.tv_sec;
    a_tcp->ts_us = pwp->pp_last_pcap_header->ts.tv_usec;
    a_tcp->vlan_id = pwp->vlan_id;
    
    if (to_link)
    {
        to_link->prev_node = a_tcp;
    }
    pwp->tcp_stream_table[hash_index] = a_tcp;
    a_tcp->next_time = pwp->tcp_latest;
    a_tcp->prev_time = 0;
    if (!pwp->tcp_oldest)
    {
        pwp->tcp_oldest = a_tcp;
    }
    if (pwp->tcp_latest)
    {
        pwp->tcp_latest->prev_time = a_tcp;
    }
    pwp->tcp_latest = a_tcp;
    
    a_tcp->pwp = pwp;
    
    add_tcp_closing_timeout(par, a_tcp);
}
/* 兼容ipv4和ipv6 */
struct tcp_stream *find_tcp_stream_ex_new(void *par, struct tuple4 *addr, int is_from_client)
{
    worker_params_t *pwp = (worker_params_t *)par;
    int hash_index = 0;
    struct tcp_stream *a_tcp = NULL;
    
    hash_index = mk_hash_index_force_new(pwp, *addr, is_from_client);
    //printf ("find tcp stream hash index = %d\n", hash_index);
    
    for (a_tcp = pwp->tcp_stream_table[hash_index]; a_tcp && memcmp(&a_tcp->addr, addr, sizeof(struct tuple4)); a_tcp = a_tcp->next_node)
    {
        ;
    }
    
    return a_tcp ? a_tcp : 0;
}

/* 兼容IPv4和IPv6 */
/* 需要注意的是这里的代码有一个探测的功能，以一个建立session的包作为snd，可能会发生翻转 */
struct tcp_stream *find_stream_new(void *par, struct tcphdr *this_tcphdr, void *this_ip, int *from_client)
{
    worker_params_t *pwp = (worker_params_t *)par;
    struct tuple4 this_addr;
    struct tuple4 reversed;
    memset(&this_addr, 0, sizeof(struct tuple4));
    memset(&reversed, 0, sizeof(struct tuple4));
    struct ip *this_iphdr = NULL;
    struct ip6_hdr *this_ip6hdr = NULL;
    struct tcp_stream *a_tcp = NULL;
    
    if (pwp->i_ip_type == 0) // IPv4
    {
        this_iphdr = (struct ip *)this_ip;
        this_addr.i_ip_type = 0;
        this_addr.source = ntohs(this_tcphdr->th_sport);
        this_addr.dest = ntohs(this_tcphdr->th_dport);
        this_addr.saddr = this_iphdr->ip_src.s_addr;
        this_addr.daddr = this_iphdr->ip_dst.s_addr;
        
        a_tcp = find_tcp_stream_ex_new(pwp, &this_addr, 1);
        if (a_tcp)
        {
            *from_client = 1;
            return a_tcp;
        }
        
        reversed.i_ip_type = 0;
        reversed.source = ntohs(this_tcphdr->th_dport);
        reversed.dest = ntohs(this_tcphdr->th_sport);
        reversed.saddr = this_iphdr->ip_dst.s_addr;
        reversed.daddr = this_iphdr->ip_src.s_addr;
        
        a_tcp = find_tcp_stream_ex_new(pwp, &reversed, 0);
        if (a_tcp)
        {
            *from_client = 0;
            return a_tcp;
        }
    }
    else //IPv6
    {
        this_ip6hdr = (struct ip6_hdr *)this_ip;
        this_addr.i_ip_type = 1;
        this_addr.source = ntohs(this_tcphdr->th_sport);
        this_addr.dest = ntohs(this_tcphdr->th_dport);
        memcpy(this_addr.a_saddr, this_ip6hdr->ip6_src.s6_addr32, sizeof(this_addr.a_saddr));
        memcpy(this_addr.a_daddr, this_ip6hdr->ip6_dst.s6_addr32, sizeof(this_addr.a_daddr));
        char a_src_buf[64] = {0};
        char a_dst_buf[64] = {0};
        get_ip6addr_str(this_ip6hdr->ip6_src.s6_addr32, a_src_buf, 64);
        get_ip6addr_str(this_ip6hdr->ip6_dst.s6_addr32, a_dst_buf, 64);
        //printf ("src buf = %s, dst buf = %s\n", a_src_buf, a_dst_buf);
        
        a_tcp = find_tcp_stream_ex_new(pwp, &this_addr, 1);
        if (a_tcp)
        {
            *from_client = 1;
            return a_tcp;
        }
        
        reversed.i_ip_type = 1;
        reversed.source = ntohs(this_tcphdr->th_dport);
        reversed.dest = ntohs(this_tcphdr->th_sport);
        memcpy(reversed.a_saddr, this_ip6hdr->ip6_dst.s6_addr32, sizeof(reversed.a_saddr));
        memcpy(reversed.a_daddr, this_ip6hdr->ip6_src.s6_addr32, sizeof(reversed.a_daddr));
        
        a_tcp = find_tcp_stream_ex_new(pwp, &reversed, 0);
        if (a_tcp)
        {
            *from_client = 0;
            return a_tcp;
        }
    }
    
    return 0;
}

void tcp_exit(void *par)
{
    worker_params_t *pwp = (worker_params_t *)par;
    int i;
    struct lurker_node *j;
    struct tcp_stream *a_tcp, *t_tcp;
    
    if (!pwp->tcp_stream_table || !pwp->streams_pool)
        return;
    for (i = 0; i < pwp->tcp_stream_table_size; i++)
    {
        a_tcp = pwp->tcp_stream_table[i];
        while (a_tcp)
        {
            // free_tcp_cache(pwp, a_tcp, &a_tcp->client);
            // free_tcp_cache(pwp, a_tcp, &a_tcp->server);
            t_tcp = a_tcp;
            a_tcp = a_tcp->next_node;
            for (j = t_tcp->listeners; j; j = j->next)
            {
                t_tcp->pp_state = PP_EXITING;
                (j->item)(pwp, t_tcp, &j->data);
            }
            free_tcp_stream(pwp, t_tcp);
        }
    }
    free(pwp->tcp_stream_table);
    pwp->tcp_stream_table = NULL;
    free(pwp->streams_pool);
    pwp->streams_pool = NULL;
    /* FIXME: anything else we should free? */
    /* yes plz.. */
    pwp->tcp_latest = pwp->tcp_oldest = NULL;
    pwp->tcp_num = 0;
}

inline static void swap_half_stream(struct half_stream** snd, struct half_stream** rcv) {
    struct half_stream* tmp = NULL;
    tmp = *snd;
    *snd = *rcv;
    *rcv = tmp;
}


// static void free_packet(struct skbuff *packet, struct skbuff **head, struct skbuff **tail)
// {
//     assert(packet != NULL);
//     assert(head != NULL);
//     assert(tail != NULL);
//     if (packet->prev)
//         packet->prev->next = packet->next;
//     else
//         *head = packet->next;
//     if (packet->next)
//         packet->next->prev = packet->prev;
//     else
//         *tail = packet->prev;
//     free(packet->data);
//     free(packet);
// }

static int port_white_hit(worker_params_t *pwp, unsigned short port)
{
    if (!p_port_white_func)
    {
        return 0;
    }
    
    return p_port_white_func(pwp, port);
}

static int port_filter_hit(worker_params_t *pwp, unsigned short port) 
{
    if (!p_port_filter_func) {
        return 0;
    }
    
    return p_port_filter_func(pwp, port);
}

void interrupt(struct tcp_stream* a_tcp, void* data, int from_client)
{
    struct ip* ip_header = (struct ip *)data;
    struct tcphdr* tcp_header = (struct tcphdr *)((char *)data + 4 * ip_header->ip_hl);
    if (!(tcp_header->th_flags & TH_ACK) || (tcp_header->th_flags & TH_RST) || (tcp_header->th_flags & TH_SYN) || (tcp_header->th_flags & TH_FIN))
    {
        fprintf(stderr, "error\n");
        return;
    }

    int fd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
    struct sockaddr_ll addr;
    memset(&addr, 0, sizeof(addr));
    addr.sll_family = AF_PACKET;
    addr.sll_protocol = htons(ETH_P_ALL);
    addr.sll_ifindex = if_nametoindex(interrupt_eth_name);
    if (bind(fd, (struct sockaddr*)&addr, sizeof(addr)) < 0)
    {
        fprintf(stderr, "bind fail, %s\n", strerror(errno));
        return;
    }
    
    char buf[54] = {0};
    int index = 0;
    
    //阻断客户端
    
    //link
    buf[index++] = a_tcp->client_mac[0];//dst_mac
    buf[index++] = a_tcp->client_mac[1];
    buf[index++] = a_tcp->client_mac[2];
    buf[index++] = a_tcp->client_mac[3];
    buf[index++] = a_tcp->client_mac[4];
    buf[index++] = a_tcp->client_mac[5];
    buf[index++] = a_tcp->server_mac[0];//src_mac
    buf[index++] = a_tcp->server_mac[1];
    buf[index++] = a_tcp->server_mac[2];
    buf[index++] = a_tcp->server_mac[3];
    buf[index++] = a_tcp->server_mac[4];
    buf[index++] = a_tcp->server_mac[5];
    buf[index++] = 0x08;//type
    buf[index++] = 0x00;
    
    //ip
    int ipIndex = index;
    buf[index++] = 0x45;//version、head len
    buf[index++] = 0x00;//TOS
    buf[index++] = 0x00;//total len
    buf[index++] = 0x28;
    buf[index++] = 0xaa;//indentification
    buf[index++] = 0xbb;
    buf[index++] = 0x40;//fragment
    buf[index++] = 0x00;
    buf[index++] = 0x40;//ttl
    buf[index++] = 0x06;//protocol
    int ipChecksumIndex = index;
    buf[index++] = 0x00;//checksum
    buf[index++] = 0x00;
    int srcIpIndex = index;
    
    int dstIpIndex = 0;
    if (from_client)
    {
        buf[index++] = ip_header->ip_dst.s_addr & 0xFF;//src_ip
        buf[index++] = (ip_header->ip_dst.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 24) & 0xFF;
        dstIpIndex = index;
        buf[index++] = ip_header->ip_src.s_addr & 0xFF;//dst_ip
        buf[index++] = (ip_header->ip_src.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 24) & 0xFF;
    }
    else
    {
        buf[index++] = ip_header->ip_src.s_addr & 0xFF;//src_ip
        buf[index++] = (ip_header->ip_src.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 24) & 0xFF;
        dstIpIndex = index;
        buf[index++] = ip_header->ip_dst.s_addr & 0xFF;//dst_ip
        buf[index++] = (ip_header->ip_dst.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 24) & 0xFF;
    }
    
    //ip checksum计算
    uint64_t checksum = 0;
    uint16_t* temp = (uint16_t*)(buf + ipIndex);
    int i = 0;
    for (i = 0; i < 10; i++)
    {
        checksum += ntohs(*temp);
        temp++;
    }
    // printf("ip\n");
    // printf("checksum original=%lu\n", checksum);
    checksum = ~((checksum & 0xFFFF) + (checksum >> 16)) & 0xFFFF;
    // printf("checksum process=%lu\n", checksum);
    buf[ipChecksumIndex] = (checksum >> 8) & 0xFF;
    buf[ipChecksumIndex + 1] = checksum & 0xFF;
    
    //tcp
    int tcpIndex = index;
    
    if (from_client)
    {
        buf[index++] = tcp_header->th_dport & 0xFF;//src_port
        buf[index++] = (tcp_header->th_dport >> 8) & 0xFF;
        buf[index++] = tcp_header->th_sport & 0xFF;//dst_port
        buf[index++] = (tcp_header->th_sport >> 8) & 0xFF;
        
        buf[index++] = tcp_header->th_ack & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 24 ) & 0xFF;//seq
        
        buf[index++] = tcp_header->th_seq & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 24 ) & 0xFF;//ack
    }
    else
    {
        buf[index++] = tcp_header->th_sport & 0xFF;
        buf[index++] = (tcp_header->th_sport >> 8) & 0xFF;//src_port
        buf[index++] = tcp_header->th_dport & 0xFF;
        buf[index++] = (tcp_header->th_dport >> 8) & 0xFF;//src_port
        
        buf[index++] = tcp_header->th_seq & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 24 ) & 0xFF;//seq
        
        buf[index++] = tcp_header->th_ack & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 24 ) & 0xFF;//ack
    }
    
    buf[index++] = 0x50;//head len、reserved
    buf[index++] = 0x14;//flag
    buf[index++] = 0xaa;//win
    buf[index++] = 0xbb;
    int tcpChecksumIndex = index;
    buf[index++] = 0x00;//checksum
    buf[index++] = 0x00;
    buf[index++] = 0x00;//urg
    buf[index++] = 0x00;
    
    //tcp checksum计算
    checksum = 0;
    checksum += ntohs(*((uint16_t*)(buf + srcIpIndex)))
    + ntohs(*((uint16_t*)(buf + srcIpIndex + 2)))
    + ntohs(*((uint16_t*)(buf + dstIpIndex)))
    + ntohs(*((uint16_t*)(buf + dstIpIndex + 2)))
    + 0x0006
    + 0x0014;
    temp = (uint16_t*)(buf + tcpIndex);
    for (i = 0; i < 10; i++)
    {
        checksum += ntohs(*temp);
        temp++;
    }
    // printf("tcp\n");
    // printf("checksum original=%lu\n", checksum);
    checksum = ~((checksum & 0xFFFF) + (checksum >> 16)) & 0xFFFF;
    // printf("checksum process=%lu\n", checksum);
    buf[tcpChecksumIndex] = (checksum >> 8) & 0xFF;
    buf[tcpChecksumIndex + 1] = checksum & 0xFF;
    
    if (send(fd, buf, index, 0) < 0)
    {
        fprintf(stderr, "send client interrupt fail, %s", strerror(errno));
    }
    else
    {
        fprintf(stderr, "send client interrupt success");
    }
    
    for (i = 0; i < 54; i++)
    {
        if (i % 16 == 0)
        {
            fprintf(stderr, "\n");
        }
        
        fprintf(stderr, "%02x ", buf[i] & 0xFF);
    }
    fprintf(stderr, "\n");
    
    
    
    
    
    //阻断服务端
    
    index = 0;
    
    //link
    buf[index++] = a_tcp->server_mac[0];//dst_mac
    buf[index++] = a_tcp->server_mac[1];
    buf[index++] = a_tcp->server_mac[2];
    buf[index++] = a_tcp->server_mac[3];
    buf[index++] = a_tcp->server_mac[4];
    buf[index++] = a_tcp->server_mac[5];
    buf[index++] = a_tcp->client_mac[0];//dst_mac
    buf[index++] = a_tcp->client_mac[1];
    buf[index++] = a_tcp->client_mac[2];
    buf[index++] = a_tcp->client_mac[3];
    buf[index++] = a_tcp->client_mac[4];
    buf[index++] = a_tcp->client_mac[5];
    buf[index++] = 0x08;//type
    buf[index++] = 0x00;
    
    //ip
    buf[index++] = 0x45;//version、head len
    buf[index++] = 0x00;//TOS
    buf[index++] = 0x00;//total len
    buf[index++] = 0x28;
    buf[index++] = 0xaa;//indentification
    buf[index++] = 0xbb;
    buf[index++] = 0x40;//fragment
    buf[index++] = 0x00;
    buf[index++] = 0x40;//ttl
    buf[index++] = 0x06;//protocol
    buf[index++] = 0x00;//checksum
    buf[index++] = 0x00;
    
    dstIpIndex = 0;
    if (from_client)
    {
        buf[index++] = ip_header->ip_src.s_addr & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 24) & 0xFF;//src_ip
        dstIpIndex = index;
        buf[index++] = ip_header->ip_dst.s_addr & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 24) & 0xFF;//dst_ip
    }
    else
    {
        buf[index++] = ip_header->ip_dst.s_addr & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_dst.s_addr >> 24) & 0xFF;//src_ip
        dstIpIndex = index;
        buf[index++] = ip_header->ip_src.s_addr & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 8) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 16) & 0xFF;
        buf[index++] = (ip_header->ip_src.s_addr >> 24) & 0xFF;//dst_ip
    }
    
    //ip checksum计算
    checksum = 0;
    temp = (uint16_t*)(buf + ipIndex);
    for (i = 0; i < 10; i++)
    {
        checksum += ntohs(*temp);
        temp++;
    }
    // printf("ip\n");
    // printf("checksum original=%lu\n", checksum);
    checksum = ~((checksum & 0xFFFF) + (checksum >> 16)) & 0xFFFF;
    // printf("checksum process=%lu\n", checksum);
    buf[ipChecksumIndex] = (checksum >> 8) & 0xFF;
    buf[ipChecksumIndex + 1] = checksum & 0xFF;
    
    
    //tcp
    tcpIndex = index;
    if (from_client)
    {
        buf[index++] = tcp_header->th_sport & 0xFF;
        buf[index++] = (tcp_header->th_sport >> 8) & 0xFF;//src_port
        buf[index++] = tcp_header->th_dport & 0xFF;
        buf[index++] = (tcp_header->th_dport >> 8) & 0xFF;//src_port
        
        buf[index++] = tcp_header->th_seq & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 24 ) & 0xFF;//seq
        
        buf[index++] = tcp_header->th_ack & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 24 ) & 0xFF;//ack
    }
    else
    {
        buf[index++] = tcp_header->th_dport & 0xFF;
        buf[index++] = (tcp_header->th_dport >> 8) & 0xFF;//src_port
        buf[index++] = tcp_header->th_sport & 0xFF;
        buf[index++] = (tcp_header->th_sport >> 8) & 0xFF;//dst_port
        
        buf[index++] = tcp_header->th_ack & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_ack >> 24 ) & 0xFF;//seq
        
        buf[index++] = tcp_header->th_seq & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 8 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 16 ) & 0xFF;
        buf[index++] = (tcp_header->th_seq >> 24 ) & 0xFF;//ack
    }
    
    buf[index++] = 0x50;//head len、reserved
    buf[index++] = 0x14;//flag
    buf[index++] = 0xaa;//win
    buf[index++] = 0xbb;
    tcpChecksumIndex = index;
    buf[index++] = 0x00;//checksum
    buf[index++] = 0x00;
    buf[index++] = 0x00;//urg
    buf[index++] = 0x00;
    
    checksum = 0;
    checksum += ntohs(*((uint16_t*)(buf + srcIpIndex)))
    + ntohs(*((uint16_t*)(buf + srcIpIndex + 2)))
    + ntohs(*((uint16_t*)(buf + dstIpIndex)))
    + ntohs(*((uint16_t*)(buf + dstIpIndex + 2)))
    + 0x0006
    + 0x0014;
    temp = (uint16_t*)(buf + tcpIndex);
    for (i = 0; i < 10; i++)
    {
        checksum += ntohs(*temp);
        temp++;
    }
    // printf("tcp\n");
    // printf("checksum original=%lu\n", checksum);
    checksum = ~((checksum & 0xFFFF) + (checksum >> 16)) & 0xFFFF;
    // printf("checksum process=%lu\n", checksum);
    buf[tcpChecksumIndex] = (checksum >> 8) & 0xFF;
    buf[tcpChecksumIndex + 1] = checksum & 0xFF;
    
    if (send(fd, buf, index, 0) < 0)
    {
        fprintf(stderr, "send server interrupt fail, %s", strerror(errno));
    }
    else
    {
        fprintf(stderr, "send server interrupt success");
    }
    
    for (i = 0; i < 54; i++)
    {
        if (i % 16 == 0)
        {
            fprintf(stderr, "\n");
        }
        
        fprintf(stderr, "%02x ", buf[i] & 0xFF);
    }
    fprintf(stderr, "\n");
    
    close(fd);
}

int is_interrupt(struct tcp_stream *a_tcp)
{
  if (!interrupt_enable)
  {
    return 0;
  }

  char src_ip[16] = {0};
  char dst_ip[16] = {0};
  inet_ntop(AF_INET, &a_tcp->addr.saddr, src_ip, sizeof(src_ip));
  inet_ntop(AF_INET, &a_tcp->addr.daddr, dst_ip, sizeof(dst_ip));

  if (strlen(interrupt_ip) > 0 &&
      (strcmp(src_ip, interrupt_ip) == 0 || strcmp(src_ip, interrupt_ip) == 0))
  {
    return 1;
  }

  if (is_hit_control_strategy(src_ip, dst_ip))
  {
    return 1;
  }

  return 0;
}

void process_tcp(void *par, void *data, int skblen)
{
    worker_params_t *pwp = (worker_params_t *)par;
    struct ip *this_iphdr = NULL;
    struct ip6_hdr *this_ip6hdr = NULL;
    struct tcphdr *this_tcphdr = NULL;
    struct tcp_stream *a_tcp = NULL;
    int from_client = 1;
    struct half_stream *snd = NULL;
    struct half_stream *rcv = NULL;
    
    int datalen = 0;
    int iplen = 0;

    if (NULL == tcp_lost_per_thread)
    {
        tcp_lost_per_thread = (tcp_parser_lost_packets*)malloc(sizeof(tcp_parser_lost_packets));
        memset(tcp_lost_per_thread, 0, sizeof(tcp_parser_lost_packets));
        tcp_lost_register(tcp_lost_per_thread);
    }

    tcp_lost_per_thread->total += 1;
        
    if (pwp->i_ip_type == 0)
    {
        this_iphdr = (struct ip *)data;
        this_tcphdr = (struct tcphdr *)((char *)data + 4 * this_iphdr->ip_hl);
        pwp->ugly_iphdr = this_iphdr;
        iplen = ntohs(this_iphdr->ip_len);
        if ((unsigned)iplen < 4 * this_iphdr->ip_hl + sizeof(struct tcphdr))
        {
            gw_pp_params.syslog(PP_WARN_TCP, PP_WARN_TCP_HDR, this_iphdr, this_tcphdr, 0);
            __sync_fetch_and_add(&(tcp_lost.invalid),1);
            return;
        }
        
        datalen = iplen - 4 * this_iphdr->ip_hl - 4 * this_tcphdr->th_off;
        if (datalen < 0)
        {
            gw_pp_params.syslog(PP_WARN_TCP, PP_WARN_TCP_HDR, this_iphdr, this_tcphdr, 0);
            __sync_fetch_and_add(&(tcp_lost.invalid),1);
            return;
        }
        
        if ((datalen <= 0) && !((this_tcphdr->th_flags & TH_RST)||(this_tcphdr->th_flags & TH_FIN)))
        {
            return;
        }
        
        if ((this_iphdr->ip_src.s_addr | this_iphdr->ip_dst.s_addr) == 0)
        {
            gw_pp_params.syslog(PP_WARN_TCP, PP_WARN_TCP_HDR, this_iphdr, this_tcphdr, 0);
            __sync_fetch_and_add(&(tcp_lost.invalid),1);
            return;
        }
        
        u_int32_t server_port = ntohs(this_tcphdr->th_dport);
        u_int32_t client_port = ntohs(this_tcphdr->th_sport);
        
        if( !(port_white_hit(pwp, server_port) || port_white_hit(pwp, client_port)))
        {
            if (port_filter_hit(pwp, server_port) || port_filter_hit(pwp, client_port))
            {
                __sync_fetch_and_add(&(tcp_lost.port_hit),1);
                return ;
            }
        }
        
        
        //以第一个报文为client
        if (!(a_tcp = find_stream_new(pwp, this_tcphdr, this_iphdr, &from_client)))
        {
            if (datalen <=0)
            {
                return;
            }
            //只是add
            add_new_tcp_new(pwp, this_tcphdr, this_iphdr);
            a_tcp = find_stream_new(pwp, this_tcphdr, this_iphdr, &from_client);
            // TODO会造成流量丢失
            assert (a_tcp != NULL);
            if (!a_tcp) return;
            a_tcp->direction_confirmed = 0;
        }
        
        // 进行阻断
        if (is_interrupt(a_tcp))
        {
            interrupt(a_tcp, data, from_client);
        }
    }
    else if (pwp->i_ip_type == 1)
    {
        //printf ("ipv6\n");
        this_ip6hdr = (struct ip6_hdr *)data;
        this_tcphdr = (struct tcphdr *)((char *)data + pwp->i_ipv6_header_len);
        pwp->ugly_ip6hdr = this_ip6hdr;
        iplen = ntohs(this_ip6hdr->ip6_plen); /* 不包括IPv6基本数头 */
        unsigned u_extra_len = (unsigned)(pwp->i_ipv6_header_len - (int)sizeof(struct ip6_hdr));
        if ((unsigned)iplen < u_extra_len + sizeof(struct tcphdr))
        {
            gw_pp_params.syslog(PP_WARN_TCP, PP_WARN_TCP_HDR, this_ip6hdr, this_tcphdr, 1);
            __sync_fetch_and_add(&(tcp_lost.invalid),1);
            return;
        }
        
        datalen = iplen - u_extra_len - 4 * this_tcphdr->th_off;
        if (datalen < 0)
        {
            gw_pp_params.syslog(PP_WARN_TCP, PP_WARN_TCP_HDR, this_ip6hdr, this_tcphdr, 1);
            __sync_fetch_and_add(&(tcp_lost.invalid),1);
            return;
        }
        
        if ((datalen <= 0) && !((this_tcphdr->th_flags & TH_RST)||(this_tcphdr->th_flags & TH_FIN)))
        {
            return;
        }
        
        u_int32_t server_port = ntohs(this_tcphdr->th_dport);
        u_int32_t client_port = ntohs(this_tcphdr->th_sport);
        
        if( !(port_white_hit(pwp, server_port) || port_white_hit(pwp, client_port)))
        {
            if (port_filter_hit(pwp, server_port) || port_filter_hit(pwp, client_port))
            {
                __sync_fetch_and_add(&(tcp_lost.port_hit),1);
                return ;
            }
        }
        
        //以第一个报文为client
        if (!(a_tcp = find_stream_new(pwp, this_tcphdr, this_ip6hdr, &from_client)))
        {
            if (datalen <= 0)
            {
                return;
            }
            //只是add
            add_new_tcp_new(pwp, this_tcphdr, this_ip6hdr);
            a_tcp = find_stream_new(pwp, this_tcphdr, this_ip6hdr, &from_client);
            // TODO会造成流量丢失
            assert (a_tcp != NULL);
            if (!a_tcp) return;
            a_tcp->direction_confirmed = 0;
        }
        
    }
    else {
        a_tcp->ts = pwp->pp_last_pcap_header->ts.tv_sec;
        a_tcp->ts_us = pwp->pp_last_pcap_header->ts.tv_usec;
    }
    
    if (from_client)
    {
        snd = &a_tcp->client;
        rcv = &a_tcp->server;
        a_tcp->dir = STREAM_REQ;
    }
    else
    {
        snd = &a_tcp->server;
        rcv = &a_tcp->client;
        a_tcp->dir = STREAM_RSP;
    }
    
    u_int seq = ntohl(this_tcphdr->th_seq);
    u_int ack = ntohl(this_tcphdr->th_ack);
    
    // keep-alive 0x0的情况
    char* p = (char *)(this_tcphdr) + 4 * this_tcphdr->th_off;
    if(datalen == 1 && (uint8_t)*p == 0)
    {
        return;
    }

    // 非应用层数据不再缓存
    if (a_tcp->probe_num > 7)
    {
        return;
    }
    
    if (datalen > 0)
    {
        cacheStream(snd, datalen, this_tcphdr, seq, ack);
    }
    
    pushStream(snd, rcv, this_tcphdr, pwp, a_tcp);
    
    return;
}

void pp_tcp_discard(struct tcp_stream *a_tcp, int num) // modify name
{
    if (num < a_tcp->read)
        a_tcp->read = num;
}

// /* 改写上面函数 */
// void tcp_discard(struct tcp_stream *a_tcp, int num)
// {
//   a_tcp->read = num;
// }

void pp_tcp_discard_force(struct tcp_stream *a_tcp, int num) // modify name
{
    if (num >= 0 && num <= a_tcp->total)
        a_tcp->read = num;
}

void pp_tcp_discard_force_update(struct tcp_stream *a_tcp, struct half_stream *rcv, int num)
{
    int total;
    total = a_tcp->read;
    if (num >= 0 && num <= rcv->count - rcv->offset)
        a_tcp->read = num;
    
    if (a_tcp->read > total - rcv->count_new)
        rcv->count_new = total - a_tcp->read;
    
    if (a_tcp->read > 0)
    {
        memmove(rcv->data, rcv->data + a_tcp->read, rcv->count - rcv->offset - a_tcp->read);
        rcv->offset += a_tcp->read;
    }
}

void pp_register_tcp(void(*x)) // modify name
{
    register_callback(&tcp_procs, x);
}

void pp_unregister_tcp(void(*x)) // modify name
{
    unregister_callback(&tcp_procs, x);
}


void pp_register_port_filter_hit(void(*x))
{
    p_port_filter_func = (port_filter_hit_func)x;
}

void pp_register_port_white_hit(void(*x))
{
    p_port_white_func = (port_white_hit_func)x;
}

int tcp_init(void *par, int size)
{
    worker_params_t *pwp = (worker_params_t *)par;
    int i;
    struct tcp_timeout *tmp;
    
    memset((void*)&tcp_lost,0,sizeof(tcp_lost));
    
    if (!size)
        return 0;
    pwp->tcp_stream_table_size = size;
    pwp->tcp_stream_table = (struct tcp_stream **)calloc(pwp->tcp_stream_table_size, sizeof(char *));
    if (!pwp->tcp_stream_table)
    {
        gw_pp_params.no_mem("tcp_init");
        return -1;
    }
    pwp->max_stream = 3 * pwp->tcp_stream_table_size / 4;
    pwp->streams_pool = (struct tcp_stream *)malloc((pwp->max_stream + 1) * sizeof(struct tcp_stream));
    if (!pwp->streams_pool)
    {
        gw_pp_params.no_mem("tcp_init");
        return -1;
    }
    for (i = 0; i < pwp->max_stream; i++)
        pwp->streams_pool[i].next_free = &(pwp->streams_pool[i + 1]);
    pwp->streams_pool[pwp->max_stream].next_free = 0;
    pwp->free_streams = pwp->streams_pool;
    //  init_hash(); // fix
    while (pwp->pp_tcp_timeouts)
    {
        tmp = pwp->pp_tcp_timeouts->next;
        free(pwp->pp_tcp_timeouts);
        pwp->pp_tcp_timeouts = tmp;
    }
    return 0;
}
