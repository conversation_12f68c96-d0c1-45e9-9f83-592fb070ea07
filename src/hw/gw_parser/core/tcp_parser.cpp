/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <inttypes.h>
#include <arpa/inet.h>
#ifndef __FAVOR_BSD
#define __FAVOR_BSD
#include <netinet/tcp.h>
#endif
#include <unistd.h>
#include <fstream>
#include <cstring>

#include "tcp_parser.h"

#include "pp.h"
#include "ip_fragment.h"
#include "pp_tcp.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session.h"

#include "gw_stats.h"
#include "filter_rule.h"
#include "ipfilter_rule.h"
#include "portfilter_rule.h"
#include "gw_license.h"
#include "display_stats_define.h"
#include "utils.h"
#include "cJSON.h"

#include "event_analyze.h"
#include "gw_i_parser.h"
#include "gw_i_upload.h"
#include "task_worker.h"
#include "pp_hash.h"

#define IPV4_FLAG  (4)
#define IPV6_FLAG  (6)

// struct tuple4 contains addresses and port numbers of the TCP connections
// the following auxiliary function produces a string looking like
// ********,1024,********,23
static inline char *adres(char buf[256], struct tuple4 addr)
{
  //static char buf[256];
  strcpy(buf, int_ntoa(addr.saddr));
  sprintf(buf + strlen(buf), ",%i,", addr.source);
  strcat(buf, int_ntoa(addr.daddr));
  sprintf(buf + strlen(buf), ",%i", addr.dest);
  return buf;
}

// // 最大线程数据
// //#define WORKER_PARAMS_MAX_NUM (32)
// #define WORKER_PARAMS_MAX_NUM (64)

static struct pp_chksum_ctl g_nochksumchk = {0};

static worker_params_t g_worker_params[WORKER_PARAMS_MAX_NUM] = {0};

volatile uint64_t CTcpParser::m_worker_cnt[WORKER_PARAMS_MAX_NUM] = {0};
volatile int CTcpParser::m_worker_state[WORKER_PARAMS_MAX_NUM] = {0};
int g_conf_tcp_lost_packet_neighbour_ignore = 0;
int g_conf_tcp_stream_cache_max_size = 10485760;
int g_conf_tcp_stream_first_cache_max_size = 20971520;
int g_conf_tcp_stream_lost_hold_min_ms = 0;
int g_conf_tcp_stream_lost_hold_min_bytes = 0;
int g_conf_tcp_stream_timeout_sec = 120;
char g_conf_tcp_nosyn_init_state = TCP_CONNECT_FIRST_PACKET_LOST;
int g_conf_tcp_stream_drop_size = 104857600;
int interrupt_enable = 0;
char interrupt_ip[16] = {0};
char interrupt_eth_name[16] = {0};
int g_conf_data_offset = 0;
int g_conf_data_offset_ex = 0;

#define FILTER_TOTAL "filter"
#define FILTER_DROP_BLACK_IP "black ip"
#define FILTER_DROP_NOT_IP "not ip"
// volatile uint64_t g_u64_filter_packets = 0;
volatile uint64_t g_u64_filter_blackip_packets = 0;
volatile uint64_t g_u64_filter_notip_packets = 0;

#define FILTER_LIMIT_GLOBAL "global limit"
#define FILTER_LIMIT_EXPIRE "license expire"
#define FILTER_LIMIT_WHITE_IP "ip limit"
#define FILTER_LIMIT_NOT_WHITE_IP "ip except limit"
#define FILTER_LIMIT_LICENSE "license limit"
volatile uint64_t g_u64_filter_global_limit_packets = 0;
volatile uint64_t g_u64_filter_expire_limit_packets = 0;
volatile uint64_t g_u64_filter_ip_limit_packets = 0;
volatile uint64_t g_u64_filter_ip_except_limit_packets = 0;
volatile uint64_t g_u64_filter_license_limit_packets = 0;

const char *msg_type = "tcp";

struct tcp_upload_info
{
  TaskWorkerData twd;
  size_t mem_size;

  struct tuple4 addr;
  int total_flow, c2s_flow, s2c_flow;
  double tm;
};

class CTaskWorkerTcpUpload : public CTaskWorker
{
public:
  virtual CWorkerQueue *get_wq(void) const {return m_pwq;}
  virtual int deal_data(const TaskWorkerData *ptwd)
  {
    tcp_upload_info* p = (tcp_upload_info*)ptwd;
    return get_tcp_parser()->worker_rutine_tcp_upload_info_inner(p);
  }
  virtual void free_data(const TaskWorkerData *ptwd)
  {
    tcp_upload_info* p = (tcp_upload_info*)ptwd;
    return get_tcp_parser()->free_tcp_upload_info(p);
  }

  virtual void init(void){}
  virtual void fini(void){}
  virtual void release(void) const{delete this;}

public:
  virtual ~CTaskWorkerTcpUpload(){}

  inline void set_wq(CWorkerQueue *pwq)
  {
    m_pwq = pwq;
  }

  inline void set_tcp_parser(CTcpParser *parser)
  {
    m_parser = parser;
  }

  inline CTcpParser *get_tcp_parser(void)
  {
    return m_parser;
  }

protected:
  CWorkerQueue *m_pwq;
  CTcpParser *m_parser;
};

void CTcpParser::send_tcp_session_info(const struct tcp_stream *a_tcp)
{
  // tcp_upload_info* p = (tcp_upload_info*)malloc(sizeof(tcp_upload_info));
  // memset((void*)p, 0, sizeof(tcp_upload_info));
  if (!m_conf_tcp_upload) {
    return;
  }
  tcp_upload_info* p = new tcp_upload_info({0});
  p->addr = a_tcp->addr;
  p->c2s_flow = a_tcp->client.count;
  p->s2c_flow = a_tcp->server.count;
  p->total_flow = p->c2s_flow + p->s2c_flow;
  p->tm = get_ts(a_tcp);
  if (!get_wq_upload_msg()->queue_put_data(p, p->mem_size)) {
    GWLOG_WARN(m_comm, "tcp parser upload msg error\n");
    free_tcp_upload_info(p);
    delete p;
  }
}

void CTcpParser::add_id_json(char *p_event_id)
{
  char a_unique_code[64] = {0};
  uint64_t u64_time_val = 0;
  get_ms_timeval(&u64_time_val);

  if (g_u64_tcp_upload_ms == 0)
  {
      g_u64_tcp_upload_ms = u64_time_val;
      g_u32_tcp_upload_index = 1;
  }
  else
  {
      if (u64_time_val == g_u64_tcp_upload_ms)
      {
          g_u32_tcp_upload_index ++;
      }
      else
      {
          g_u64_tcp_upload_ms = u64_time_val;
          g_u32_tcp_upload_index = 1;
      }
  }

  /* 获取唯一标识ID */
  get_unique_event_id(m_str_gw_ip.c_str(), g_u64_tcp_upload_ms, g_u32_tcp_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

  /* 将unique_code进行base64编码 */
  base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

  return;
}

const char* tcp_upload_template = "{"
                                    "\"tm\":%.3f,"
                                    "\"src_ip\":\"%s\","
                                    "\"src_port\":%d,"
                                    "\"dst_ip\":\"%s\","
                                    "\"dst_port\":%d,"
                                    "\"total_flow\":%d,"
                                    "\"c2s_flow\":%d,"
                                    "\"s2c_flow\":%d,"
                                    "\"unique_id\":{"
                                      "\"event_id\":\"%s\"}"
                                    "}";

int CTcpParser::worker_rutine_tcp_upload_info_inner(const tcp_upload_info* p)
{
  __sync_fetch_and_add(&m_tcp_msg_cnt, 1);
  char src_ip[40] = {0};
  char dst_ip[40] = {0};
  if (p->addr.i_ip_type == 0)
  {
      strncpy(src_ip, int_ntoa(p->addr.saddr), COUNTOF(src_ip) - 1);
      strncpy(dst_ip, int_ntoa(p->addr.daddr), COUNTOF(dst_ip) - 1);
  }
  else
  {

      get_ip6addr_str((uint32_t*)p->addr.a_saddr, src_ip, COUNTOF(src_ip));
      get_ip6addr_str((uint32_t*)p->addr.a_daddr, dst_ip, COUNTOF(dst_ip));
  }
  char a_unique_id[128] = {0};
  add_id_json(a_unique_id);
  char* s = (char*)malloc(512);
  if (!s) {
    return -1;
  }
  sprintf(s, tcp_upload_template, p->tm
                                , src_ip
                                , p->addr.source
                                , dst_ip
                                , p->addr.dest
                                , p->total_flow
                                , p->c2s_flow
                                , p->s2c_flow
                                , a_unique_id);
  send_cb_tcp_upload_msg(s);
  return 0;
}

void CTcpParser::free_upload_msg(const UploadMsg *pum)
{
  ASSERT(pum != NULL);
  delete pum;
}

void CTcpParser::free_tcp_upload_info(const tcp_upload_info *)
{

}

void CTcpParser::send_cb_tcp_upload_msg(const char *s)
{
  size_t length = strlen(s);

  if (unlikely(m_upload == NULL))
  {
    GWLOG_INFO(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    SAFE_FREE((void*)s);
    return;
  }
  UploadMsg *pum = new UploadMsg;
  memset(pum, 0, sizeof(UploadMsg));

  pum->cb = sizeof(UploadMsg);
  pum->destroy_func = free_upload_msg;
  pum->parser = this;
  pum->length = length;
  pum->s = s;
  pum->msgtype = msg_type;
  pum->mem_size = sizeof(UploadMsg) + pum->length;

  m_upload->put_msg(pum);
}

CWorkerQueue *CTcpParser::new_wq_upload_msg()
{
  m_p_wq[TCP_WQ_UPLOAD_MSG] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_upload_msg();
  if (pwq == NULL)
  {
    return NULL;
  }

  CTaskWorkerTcpUpload *ptw = new CTaskWorkerTcpUpload();
  ptw->set_tcp_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[TCP_WQ_UPLOAD_MSG] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_tcp_upload_msg_queue_max_num, m_conf_tcp_upload_msg_queue_memory_max_size_bytes);
  pwq->set_queue_name(TCP_UPLOAD_MSG);
  // pwq->set_queue_destroy_callback((q_destroy_func_t)free_ssl_parser_data_msg);
  pwq->init();
  pwq->create_queue();
  // pwq->create_thread(m_conf_ssl_parser_data_thread_num, worker_routine_parser_data, this);
  pwq->adjust_worker_thread_num(m_conf_tcp_upload_msg_thread_num);

  //m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq->get_stats_task_data(), 40);
  m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, 40);
  m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());
  return pwq;
}

/**
 * CTcpParser implementation
 *
 * 网络包数据TCP层及IP层协议，还原出完整的会话流。主要功能包括IP分片重组，TCP流重组，含有TCP流会话管理。
 */

CTcpParser *CTcpParser::m_tcp_parser = NULL;
__thread stats_forward_t *CTcpParser::m_p_stats_ip_fwd_per_thread = NULL;
__thread uint64_t *CTcpParser::m_worker_cnt_per_thread = NULL;
__thread int *CTcpParser::m_worker_state_per_thread = NULL;

CTcpParser::CTcpParser() : m_comm(NULL)
                         , m_quit_signal(0)
                         , m_tcp_callback(NULL)
                         , m_p_stats_ip_fwd(NULL)
                         , m_p_stats_port_fwd(NULL)
                         , m_p_wq{NULL}
                         , m_p_tw{NULL}
                         , m_tcp_msg_cnt(0)
                         , m_p_stats_ip_fwd_count(0)
                         , m_p_stats_ip_fwd_mutex(PTHREAD_MUTEX_INITIALIZER)
                         , m_worker_cnt_array{NULL}
                         , m_worker_state_array{NULL}
                         , m_ipfilter_rule(NULL)
                         , m_portfilter_rule(NULL)
                         , m_conf_tcp_lost_packet_neighbour_ignore(0)
                         , m_conf_split_flow_mode(0)
                         , m_work_param_num(0)
                         , m_need_mac(0)
                         , m_upload(NULL)
                         , g_u64_tcp_upload_ms(0)
                         , g_u32_tcp_upload_index(0)
                         , m_str_gw_ip("127.0.0.1")
                         , m_conf_upload_name("log")
                         , m_conf_tcp_upload(0)
                         , m_conf_tcp_upload_msg_queue_max_num(40000)
                         , m_conf_tcp_upload_msg_queue_memory_max_size_bytes(10485760)
                         , m_conf_tcp_upload_msg_thread_num(1)
{
  if (m_tcp_parser == NULL)
  {
    m_tcp_parser = this;
  }
  // m_conf_tcp_lost_packet_neighbour_ignore = 0;
  // m_conf_split_flow_mode = 0;  /* 默认使用 src IP和 dst IP进行分流 */
}

CTcpParser::~CTcpParser()
{
  if (m_tcp_parser == this)
  {
    ASSERT(m_tcp_parser != NULL);
    m_tcp_parser = NULL;
  }
}

void CTcpParser::init(std::vector<std::string> &vec_source_name)
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  memset((void *)m_worker_cnt, 0, sizeof(m_worker_cnt));
  memset((void *)m_worker_state, 0, sizeof(m_worker_state));
  m_p_stats_ip_fwd = new stats_forward_t();
  memset(m_p_stats_ip_fwd, 0, sizeof(stats_forward_t));
  m_p_stats_port_fwd = new stats_forward_t();
  memset(m_p_stats_port_fwd, 0, sizeof(stats_forward_t));
  m_p_stats_ip_fwd->cb = sizeof(stats_forward_t);
  m_p_stats_port_fwd->cb = sizeof(stats_forward_t);
  memset(g_worker_params, 0, sizeof(g_worker_params));
  memset(&g_nochksumchk, 0, sizeof(g_nochksumchk));

  m_comm->get_gw_stats()->set_tcp_parser(m_tcp_parser);
  m_comm->get_gw_stats()->set_stats_callback(STATS_TCP, print_tcp_stats_callback, this);
  m_comm->get_gw_stats()->set_qps(IP_BYTES_QPS, &(m_p_stats_ip_fwd->st_cnt.cnt_bytes), 50);
  m_comm->get_gw_stats()->set_qps(IP_FORWATD_BYTES_QPS, &(m_p_stats_ip_fwd->st_cnt_fwd.cnt_bytes), 50);
  m_comm->get_gw_license()->set_ip_bytes_stat_name(IP_FORWATD_BYTES_QPS);
  m_comm->get_gw_stats()->set_qps(IP_PACKET_QPS, &(m_p_stats_ip_fwd->st_cnt.cnt), 50);
  m_comm->get_gw_stats()->set_qps(TCP_UPLOAD_QPS, &m_tcp_msg_cnt, 50);

  int dpdk_tcp_thread_num = 0;
  int file_tcp_thread_num = 0;
  int nic_tcp_thread_num = 0;


  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,NULL, &(m_p_stats_ip_fwd->st_cnt.cnt), 20);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_DROP_BLACK_IP, &g_u64_filter_blackip_packets);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_DROP_NOT_IP, &g_u64_filter_notip_packets);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_LIMIT_GLOBAL, &g_u64_filter_global_limit_packets);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_LIMIT_EXPIRE, &g_u64_filter_expire_limit_packets);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_LIMIT_WHITE_IP, &g_u64_filter_ip_limit_packets);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_LIMIT_NOT_WHITE_IP, &g_u64_filter_ip_except_limit_packets);
  m_comm->get_gw_stats()->set_stats(FILTER_TOTAL,FILTER_LIMIT_LICENSE, &g_u64_filter_license_limit_packets);


  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,NULL, &(ip_lost.total_packets), 30);
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_INVALID, &(ip_lost.invalid_ip_packets));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_INVALID_V4_GRE, &(ip_lost.invalid_v4_gre_packets));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_INVALID_V6_IN_V4, &(ip_lost.invalid_v6_in_v4_packets));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_INVALID_V6, &(ip_lost.invalid_v6_packets));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_INVALID_V6_GRE, &(ip_lost.invalid_v6_gre_packets));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_INVALID_V6_IN_V6, &(ip_lost.invalid_v6_in_v6_packets));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PAESER_REASSEMBLE_TIMEOUT, &(reassemble_lost.timeout));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PAESER_REASSEMBLE_OVER_CACHE, &(reassemble_lost.over_cache));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PAESER_REASSEMBLE_NO_MEMORY, &(reassemble_lost.no_memory));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PAESER_REASSEMBLE_FAILED, &(reassemble_lost.failed));
  m_comm->get_gw_stats()->set_stats(IP_PARSER_TOTAL,IP_PARSER_NOT_TCP, &(ip_lost.not_tcp_packets));

  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,NULL, &(tcp_lost.total), 40);
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_PORT, &(tcp_lost.port_hit));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_INVALID, &(tcp_lost.invalid));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_TIMEOUT, &(tcp_lost.timeout));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_CACHE_OVERFUL, &(tcp_lost.cache_overfull));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_SESSION_OVERFUL, &(tcp_lost.session_overfull));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_NO_MEMORY, &(tcp_lost.no_memory));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_TS_CHECK, &(tcp_lost.ts_check));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_NOT_REGIST, &(tcp_lost.not_regist));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_RESET, &(tcp_lost.reset));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_SESSION_CLOSE, &(tcp_lost.session_close));
  m_comm->get_gw_stats()->set_stats(TCP_PARSER_TOTAL,TCP_PARSER_DUPLICATE, &(tcp_lost.duplicate));


  m_comm->get_gw_stats()->set_stats(TCP_STREAM_DATA_TOTAL,NULL, &(stream_lost.stream_total), 50);
  m_comm->get_gw_stats()->set_stats(TCP_STREAM_DATA_TOTAL,TCP_STREAM_LOST, &(stream_lost.lost));

  m_comm->get_gw_stats()->set_byte_stats(IP_BYTES, NULL, &(m_p_stats_ip_fwd->st_cnt.cnt_bytes));

  auto iter = vec_source_name.begin();
  while (iter != vec_source_name.end())
  {
    if ((*iter).find("CDpdkSource") != std::string::npos)
    {
      cJSON *dpdk_cap = m_comm->get_gw_config()->get_section("cap");
      cJSON *dpdk_args = cJSON_GetObjectItem(dpdk_cap, "args");

      int size = cJSON_GetArraySize(dpdk_args);
      for (int i = 0; i < size; i++)
      {
        cJSON *arg_obj = cJSON_GetArrayItem(dpdk_args, i);
        if (cJSON_IsString(arg_obj))
        {
          if (arg_obj->valuestring_size >= (int)strlen("--work-thread="))
          {
            if (!memcmp(arg_obj->valuestring, "--work-thread=", strlen("--work-thread=")))
            {
              char *p_value = arg_obj->valuestring + strlen("--work-thread=");
              dpdk_tcp_thread_num = atoi(p_value);
              break;
            }
          }
        }
      }
    }
    else if ((*iter).find("CNicSource") != std::string::npos)
    {
      nic_tcp_thread_num = m_comm->get_gw_config()->read_conf_int("parser", "nic_tcp_work_thread", 1);
    }
    else if ((*iter).find("CPcapSource") != std::string::npos)
    {
      nic_tcp_thread_num = m_comm->get_gw_config()->read_conf_int("parser", "nic_tcp_work_thread", 1);
    }
    else if ((*iter).find("CFileSource") != std::string::npos)
    {
      file_tcp_thread_num = m_comm->get_gw_config()->read_conf_int("parser", "pcap_tcp_work_thread", 1);
    }
    else
    {
      GWLOG_ERROR(m_comm, "source name error\n");
    }

    iter++;
  }

  /*
  不同source在g_worker_params中可用的位置分配如下
  dpdk 0-31，偏移量0
  pcap 32-39，偏移量32
  file 40-47，偏移量40
  nic 48-55，偏移量48
  */
  m_work_param_num = WORKER_PARAMS_MAX_NUM;
  for (int i = 0; i < m_work_param_num; ++i)
  {
    worker_params_t *pwp = &g_worker_params[i];
    pwp->linktype = DLT_EN10MB;
    pwp->session = m_comm->get_session_mgt(i);
  }

  g_nochksumchk.netaddr = 0;
  g_nochksumchk.mask = 0;
  g_nochksumchk.action = PP_DONT_CHKSUM;
  pp_register_chksum_ctl(&g_nochksumchk, 1);

  // 从配置文件中读取参数
  int conf_ip_hosts = m_comm->get_gw_config()->read_conf_int("parser", "ip_hosts", 10240);
  int conf_tcp_streams = m_comm->get_gw_config()->read_conf_int("parser", "tcp_streams", 100000);
  gw_pp_params.n_tcp_streams = conf_tcp_streams;
  gw_pp_params.n_hosts = conf_ip_hosts;
  if (!pp_init(g_worker_params, m_work_param_num))
  {
    fprintf(stderr, "pp_init err\n");
    exit(1);

  }

  pp_register_tcp((void(*))pp_tcp_callback);
  pp_register_port_filter_hit((void*)pp_tcp_port_filter_hit);
  pp_register_port_white_hit((void*)pp_tcp_port_white_hit);

  load_conf(NULL);
}

void CTcpParser::free_worker_queue(CWorkerQueue *p)
{
  if (p == NULL)
  {
    return;
  }
  p->set_quit_signal();
  p->wait_for_stop();

  p->delete_queue();

  p->fini();

  if (m_comm != NULL)
  {
    m_comm->destory_worker_queue(p);
  }
}

void CTcpParser::free_task_worker(CTaskWorker *p)
{
  if (p == NULL)
  {
    return;
  }
  // task_worker的init和fini在work_queue的线程中调用，不在这里调用。20190701 by lmw
  // p->fini();
  p->release();
}

void CTcpParser::fini()
{
  ASSERT(m_comm != NULL);
  pp_unregister_tcp((void(*))pp_tcp_callback);
  pp_exit(g_worker_params, COUNTOF(g_worker_params));

  if (m_p_stats_ip_fwd)
  {
    delete m_p_stats_ip_fwd;
  }

  if (m_p_stats_port_fwd)
  {
    delete m_p_stats_port_fwd;
  }
  m_ipfilter_rule = NULL;
  m_portfilter_rule = NULL;
  for (CTaskWorker** pp = m_p_tw; pp < m_p_tw + TCP_WQ_MAX_NUM; pp++)
    free_task_worker(*pp);
  for (CWorkerQueue** pp = m_p_wq; pp < m_p_wq + TCP_WQ_MAX_NUM; pp++)
    free_worker_queue(*pp);
}

void CTcpParser::run()
{
  ASSERT(m_comm != NULL);
  if (m_conf_tcp_upload)
    new_wq_upload_msg();
}

/**
 * 设置全局公共类对象实例。
 * @param CCommon *comm
 */
void CTcpParser::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CTcpParser::load_conf(const char *)
{
  CGwConfig *pgwc = m_comm->get_gw_config();

  // 从配置文件中读取参数
  m_conf_tcp_lost_packet_neighbour_ignore = pgwc->read_conf_int("parser", "tcp_lost_packet_neighbour_ignore", m_conf_tcp_lost_packet_neighbour_ignore);
  g_conf_tcp_lost_packet_neighbour_ignore = m_conf_tcp_lost_packet_neighbour_ignore;

  g_conf_tcp_stream_cache_max_size = pgwc->read_conf_int("parser", "tcp_stream_cache_max_size", g_conf_tcp_stream_cache_max_size);
  g_conf_tcp_stream_first_cache_max_size = pgwc->read_conf_int("parser", "tcp_stream_first_cache_max_size", g_conf_tcp_stream_first_cache_max_size);
  g_conf_tcp_stream_lost_hold_min_ms = pgwc->read_conf_int("parser", "tcp_stream_lost_hold_min_ms", g_conf_tcp_stream_lost_hold_min_ms);
  g_conf_tcp_stream_lost_hold_min_bytes = pgwc->read_conf_int("parser", "tcp_stream_lost_hold_min_bytes", g_conf_tcp_stream_lost_hold_min_bytes);
  g_conf_tcp_stream_timeout_sec = pgwc->read_conf_int("parser", "tcp_stream_timeout_sec", g_conf_tcp_stream_timeout_sec);
  g_conf_tcp_stream_drop_size = pgwc->read_conf_int("parser", "tcp_stream_drop_size", g_conf_tcp_stream_drop_size);
  m_conf_tcp_upload_msg_queue_max_num = pgwc->read_conf_int("parser", "tcp_upload_msg_queue_num", m_conf_tcp_upload_msg_queue_max_num);
  m_conf_tcp_upload_msg_queue_memory_max_size_bytes = pgwc->read_conf_int("parser", "tcp_upload_msg_queue_mem_size", m_conf_tcp_upload_msg_queue_memory_max_size_bytes / (1024ULL * 1024ULL)) * (1024ULL * 1024ULL);
  m_conf_tcp_upload_msg_thread_num = pgwc->read_conf_int("parser", "tcp_upload_msg_queue_thread_num", m_conf_tcp_upload_msg_thread_num);
  m_conf_tcp_upload = pgwc->read_conf_int("parser", "tcp_upload", m_conf_tcp_upload);
  g_conf_data_offset = pgwc->read_conf_int("parser", "data_offset", g_conf_data_offset);
  g_conf_data_offset_ex = pgwc->read_conf_int("parser", "data_offset_ex", g_conf_data_offset_ex);

  std::string tcp_nosyn_init_state = pgwc->read_conf_string("parser", "tcp_nosyn_init_state");

  gw_pp_params.tcp_workarounds = pgwc->read_conf_int("parser", "tcp_workarounds", gw_pp_params.tcp_workarounds);
  gw_pp_params.tcp_flow_timeout = pgwc->read_conf_int("parser", "tcp_flow_timeout", gw_pp_params.tcp_flow_timeout);
  m_need_mac = pgwc->read_conf_int("parser", "need_mac_addr", m_need_mac);

  if (strcmp(tcp_nosyn_init_state.c_str(), "cache") == 0)
  {
    g_conf_tcp_nosyn_init_state = TCP_CONNECT_CACHE;
  }
  else {
    g_conf_tcp_nosyn_init_state = TCP_CONNECT_FIRST_PACKET_LOST;
  }

  m_conf_split_flow_mode = pgwc->read_conf_int("parser", "split_flow_mode", m_conf_split_flow_mode);

  if (m_comm->get_verbose())
  {
    GWLOG_INFO (m_comm, "m_conf_split_flow_mode = %d\n", m_conf_split_flow_mode);
    GWLOG_INFO (m_comm, "tcp work param num = %d\n", m_work_param_num);
  }
  std::string str_gw_ip = pgwc->read_conf_string("parser", "gw_ip");
  if (str_gw_ip.size() > 0)
  {
    m_str_gw_ip = str_gw_ip;
  }

  std::string str_upload_name = pgwc->read_conf_string("parser", "upload_mode");
  if (str_upload_name.size() > 0)
  {
    m_conf_upload_name = str_upload_name;
  }
  m_upload = m_comm->get_upload_from_name(m_conf_upload_name.c_str());
  if (m_upload == NULL)
  {
    GWLOG_ERROR(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
  }

  interrupt_enable = pgwc->read_conf_int("parser", "interrupt_enable", interrupt_enable);
  strncpy(interrupt_ip, pgwc->read_conf_string("parser", "interrupt_ip").c_str(), sizeof(interrupt_ip) - 1);
  strncpy(interrupt_eth_name, pgwc->read_conf_string("parser", "interrupt_eth_name").c_str(), sizeof(interrupt_eth_name) - 1);

  return true;
}

/**
 * 触发退出信号时处理
 */
void CTcpParser::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CTcpParser::wait_for_stop(void)
{
}

/**
 * 设置TCP层回调函数。
 * @param TCP_CALLBACK callback
 */
void CTcpParser::set_tcp_callback(TCP_CALLBACK callback)
{
  m_tcp_callback = callback;
}


/**
 * 设置IP过滤规则。
 * @param CFilterRule*rule
 */
void CTcpParser::set_ip_filter_rule(CFilterRule *rule)
{
  ASSERT(m_comm != NULL);
  ASSERT(rule != NULL);

  m_ipfilter_rule = (CIpfilterRule*)rule;
  std::string str_ip_filter;
  std::string str_ip_white;
  CGwConfig *p_gwconf = m_comm->get_gw_config();
  str_ip_filter = p_gwconf->read_conf_string("parser", "ip_filter");
  str_ip_white = p_gwconf->read_conf_string("parser", "ip_white");

  m_ipfilter_rule->set_ip_filter(str_ip_filter.c_str());
  m_ipfilter_rule->set_ip_white(str_ip_white.c_str());

  return;
}

/**
 * 设置PORT过滤规则。
 * @param CFilterRule*rule
 */
void CTcpParser::set_port_filter_rule(CFilterRule *rule)
{
  ASSERT(m_comm != NULL);
  ASSERT(rule != NULL);

  m_portfilter_rule = (CPortfilterRule*)rule;
  std::string str_port_filter;
  std::string str_port_white;
  CGwConfig *p_gwconf = m_comm->get_gw_config();
  str_port_filter = p_gwconf->read_conf_string("parser", "port_filter");
  str_port_white = p_gwconf->read_conf_string("parser", "port_white");

  m_portfilter_rule->set_port_filter(str_port_filter.c_str());
  m_portfilter_rule->set_port_white(str_port_white.c_str());

  return;
}

int CTcpParser::callback_ip(pkt_info_t *ppi, unsigned int size, void *userdata)
{
  int ret = 0;
  unsigned int i;
  pkt_info_t *p;
  struct ip *iph;
  struct ip6_hdr *ip6h;
  struct tcphdr *this_tcphdr;
  unsigned char *data;
  int linkoffset = 0;
  int st = 0;
  unsigned int src_addr;
  unsigned int dst_addr;
  int i_is_ipv6_packet = 0;
  int i_offset = 0;
  ASSERT(m_comm != NULL);
  ASSERT(m_ipfilter_rule != NULL);
  CGwStats *pgst = m_comm->get_gw_stats();
  ASSERT(pgst != NULL);
  int i_stats_speed_limit = pgst->get_stats_speed_limit();
  int magic = 0;

  if (NULL == m_p_stats_ip_fwd_per_thread)
  {
    m_p_stats_ip_fwd_per_thread = new stats_forward_t();
    memset(m_p_stats_ip_fwd_per_thread, 0, sizeof(stats_forward_t));
    stats_ip_fwd_register(m_p_stats_ip_fwd_per_thread);
  }

  for (i = 0; i < size; ++i)
  {
    p = &ppi[i];

    // TODO
    if (g_conf_data_offset > 0 && g_conf_data_offset_ex == 0 && g_conf_data_offset < (int)p->pkt_size && p->pkt_size > 14)
    {
        memmove(static_cast<char*>(p->buf) + 14, static_cast<char*>(p->buf) + g_conf_data_offset, p->size - g_conf_data_offset);
        //按照g_conf_data_offset进行偏移,保留最外层的链路层
        p->size = p->size - g_conf_data_offset + 14;
        p->pkt_size = p->pkt_size - g_conf_data_offset + 14;
    }
    else if (g_conf_data_offset > 0  && g_conf_data_offset_ex == 1 && g_conf_data_offset < (int)p->pkt_size && p->pkt_size > 14)
    {
        memmove(static_cast<char*>(p->buf), static_cast<char*>(p->buf) + g_conf_data_offset, p->size - g_conf_data_offset);
        //按照g_conf_data_offset进行偏移,使用隧道协议传输的链路层
        p->size = p->size - g_conf_data_offset;
        p->pkt_size = p->pkt_size - g_conf_data_offset;
    }

    m_p_stats_ip_fwd_per_thread->st_cnt.cnt += 1;
    m_p_stats_ip_fwd_per_thread->st_cnt.cnt_bytes += p->pkt_size;
    if (unlikely((i_stats_speed_limit & 1) == 1))
    {
      __sync_fetch_and_add(&g_u64_filter_global_limit_packets,1);
    }

    if (unlikely(m_comm->get_gw_license()->verify_product_exprie() == 1))
    {
      m_comm->set_gwparser_exit();
      __sync_fetch_and_add(&g_u64_filter_expire_limit_packets,1);
      goto skip;
    }

    magic = -1;
    if (unlikely(m_comm->get_gw_license()->verify_limit_rate() == 1))
    {
      magic = m_comm->get_gw_license()->get_magic();
      __sync_fetch_and_add(&g_u64_filter_license_limit_packets,1);
    }

    data = (unsigned char *)p->buf;
    if (unlikely(p->size < 16))
    {
      __sync_fetch_and_add(&g_u64_filter_notip_packets,1);
      goto skip;
    }

    ret = parser_data_link_layer(data, &linkoffset);
    if (ret == 0)
    {
      i_is_ipv6_packet = 0;
    }
    else if (ret == 1)
    {
      i_is_ipv6_packet = 1;
    }
    else
    {
      __sync_fetch_and_add(&g_u64_filter_notip_packets,1);
      goto skip;
    }

    if (i_is_ipv6_packet == 0)
    {
      m_p_stats_ip_fwd_per_thread->st_cnt.cnt_ipv4 += 1;
      m_p_stats_ip_fwd_per_thread->st_cnt.cnt_ipv4_bytes += p->pkt_size;
      if (unlikely(p->size < linkoffset + sizeof(struct ip)))
      {
        __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4), 1);
        __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4_bytes), p->pkt_size);
        __sync_fetch_and_add(&g_u64_filter_notip_packets,1);
        goto skip;
      }

      iph = (struct ip *)((char *)p->buf + linkoffset);


      src_addr = ntohl(iph->ip_src.s_addr);
      dst_addr = ntohl(iph->ip_dst.s_addr);

      if (magic > -1)
      {
        if (((src_addr ^ dst_addr) % DROP_MOD) <= (unsigned)magic)
        {
          // GWLOG_INFO (m_comm, "src_addr=%u, dst_addr=%u\n", src_addr, dst_addr);
          goto skip;
        }
      }

      {
        if (m_ipfilter_rule->ip_white_hit(src_addr) || m_ipfilter_rule->ip_white_hit(dst_addr))
        {
          // 命中 白名单 解析指定IP下的流量数据
          if ((i_stats_speed_limit & 4) == 4)
          {
            // IP白名单的限流 随机丢弃
            __sync_fetch_and_add(&g_u64_filter_ip_limit_packets,1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4), 1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4_bytes), p->pkt_size);
            goto skip;
          }
        }
        else
        {
          // 源IP和目的IP中，有一个是命中的，则不再解析
          if (m_ipfilter_rule->ip_filter_hit(src_addr) || m_ipfilter_rule->ip_filter_hit(dst_addr))
          {
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4), 1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4_bytes), p->pkt_size);
            __sync_fetch_and_add(&g_u64_filter_blackip_packets,1);
            goto skip;
          }
          if ((i_stats_speed_limit & 2) == 2)
          {
            // 除IP白名单之外的限流 随机丢弃
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4), 1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4_bytes), p->pkt_size);
            __sync_fetch_and_add(&g_u64_filter_ip_except_limit_packets,1);
            goto skip;
          }
        }
      }

      if (m_conf_split_flow_mode == 0)
      {
        st = (src_addr ^ dst_addr) & 0xff;
      }
      else if (iph->ip_p == IPPROTO_TCP)
      {
        this_tcphdr = (struct tcphdr *)((char *)p->buf + linkoffset + 4 * iph->ip_hl);
        uint16_t server_port = ntohs(this_tcphdr->th_dport);
        uint16_t client_port = ntohs(this_tcphdr->th_sport);
        st = (src_addr ^ dst_addr ^ server_port ^ client_port) & 0xff;
      }

      ppi[i].o_st = (st & 0xff) + 1;
      ret++;
      m_p_stats_ip_fwd_per_thread->st_cnt_fwd.cnt += 1;
      m_p_stats_ip_fwd_per_thread->st_cnt_fwd.cnt_ipv4 += 1;
      m_p_stats_ip_fwd_per_thread->st_cnt_fwd.cnt_bytes += p->pkt_size;
      m_p_stats_ip_fwd_per_thread->st_cnt_fwd.cnt_ipv4_bytes += p->pkt_size;
      continue;
    }
    else
    {
      __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt.cnt_ipv6), 1);
      __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt.cnt_ipv6_bytes), p->pkt_size);
      if (p->size < linkoffset + sizeof(struct ip6_hdr))
      {
        __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6), 1);
        __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes), p->pkt_size);
        __sync_fetch_and_add(&g_u64_filter_notip_packets,1);
        goto skip;
      }

      ip6h = (struct ip6_hdr *)((char *)p->buf + linkoffset);
      uint8_t u8_next_header_type = 0;
      int i_payload_len = p->size - linkoffset;

      i_offset = get_in6_l4_protocol((char*)p->buf + linkoffset, i_payload_len, &u8_next_header_type, NULL);
      if (i_offset < 0)
      {
        __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6), 1);
        __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes), p->pkt_size);
        __sync_fetch_and_add(&g_u64_filter_notip_packets,1);
        goto skip;
      }

      if(u8_next_header_type == IPPROTO_IPV6)
      {
        ip6h = (struct ip6_hdr *)((char *)p->buf + linkoffset + i_offset);
        i_offset = get_in6_l4_protocol((char*)p->buf + linkoffset + i_offset, i_payload_len, &u8_next_header_type, NULL);
        if (i_offset < 0)
        {
          __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6), 1);
          __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes), p->pkt_size);
          __sync_fetch_and_add(&g_u64_filter_notip_packets,1);
          goto skip;
        }
      }

      {
        /* 名单检查 */
        // 源IP和目的IP中，有一个是命中的，将继续解析
        if (m_ipfilter_rule->ip6_white_hit(ip6h->ip6_src.s6_addr16) || m_ipfilter_rule->ip6_white_hit(ip6h->ip6_dst.s6_addr16))
        {
          // 命中 白名单 解析指定IP下的流量数据
          if ((i_stats_speed_limit & 4) == 4)
          {
            // IP白名单的限流 随机丢弃
            __sync_fetch_and_add(&g_u64_filter_ip_limit_packets,1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6), 1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes), p->pkt_size);
            goto skip;
          }
        }
        else
        {
          // 源IP和目的IP中，有一个是命中的，则不再解析
          if (m_ipfilter_rule->ip6_filter_hit(ip6h->ip6_src.s6_addr16) || m_ipfilter_rule->ip6_filter_hit(ip6h->ip6_dst.s6_addr16))
          {
            __sync_fetch_and_add(&g_u64_filter_blackip_packets,1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6), 1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes), p->pkt_size);
            goto skip;
          }
          if ((i_stats_speed_limit & 2) == 2)
          {
            // 除IP白名单之外的限流 随机丢弃
            __sync_fetch_and_add(&g_u64_filter_ip_except_limit_packets,1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6), 1);
            __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes), p->pkt_size);
            goto skip;
          }
        }
      }

      int j = 0;
      for (j = 0; j < 8; j++)
      {
        st ^= (ip6h->ip6_src.s6_addr16[i] ^ ip6h->ip6_dst.s6_addr16[i]);
      }

      ppi[i].o_st = (st & 0xff) + 1;
      ret++;
      __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_fwd.cnt), 1);
      __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv6), 1);
      __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_fwd.cnt_bytes), p->pkt_size);
      __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv6_bytes), p->pkt_size);
      continue;
    }

  skip:
    __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt), 1);
    __sync_fetch_and_add(&(m_p_stats_ip_fwd->st_cnt_drop.cnt_bytes), p->pkt_size);
    continue;
  }

  return ret;
}

void CTcpParser::callback_tcp(pcap_info_t *ppi, void *userdata, int st)
{
  struct pcap_pkthdr hdr;
  int no = st < 0 ? 0 : (st & (WORKER_PARAMS_MAX_NUM - 1));
  worker_params_t *pwp = &g_worker_params[no];
  
  if (NULL == ppi && NULL != userdata) //进入超时处理的特殊判断条件
  {
    if (NULL != pwp->pp_tcp_timeouts)
    {
      struct timeval tv;
      gettimeofday(&tv, NULL);
      tcp_check_timeouts(pwp, &tv);
    }

    return;
  }

  hdr.ts.tv_sec = ppi->timestamp;
  hdr.ts.tv_usec = ppi->microseconds;
  hdr.caplen = ppi->packet_length;
  hdr.len = ppi->packet_length_wire;

  if (NULL == m_worker_cnt_per_thread)
  {
    m_worker_cnt_per_thread = (uint64_t*)malloc(sizeof(uint64_t));
    *m_worker_cnt_per_thread = 0;
    worker_cnt_register(m_worker_cnt_per_thread, no);
  }

  (*m_worker_cnt_per_thread)++;

  if (NULL == m_worker_state_per_thread)
  {
    m_worker_state_per_thread = (int*)malloc(sizeof(int));
    *m_worker_state_per_thread = 0;
    worker_state_register(m_worker_state_per_thread, no);
  }

  *m_worker_state_per_thread = WORKER_STATE_RUN;

  pwp->pcap_filename = ppi->filename;
  pwp->bool_need_mac = m_need_mac;
  pp_pcap_handler((u_char *)pwp, &hdr, (u_char *)ppi->buf);
  *m_worker_state_per_thread = WORKER_STATE_IDLE;
}

void fastcall CTcpParser::pp_tcp_callback(void *userdata, struct tcp_stream *a_tcp, void **this_time_not_needed)
{
  CTcpParser *pThis = m_tcp_parser;
  ASSERT(pThis != NULL);

  if (pThis != NULL)
  {
    pThis->tcp_callback(userdata, a_tcp, this_time_not_needed);
  }

  return;
}

void CTcpParser::tcp_callback(void *userdata, struct tcp_stream *a_tcp, void **this_time_not_needed)
{
  struct conn pcon[1] = {0};


  ASSERT(m_comm != NULL);
  ASSERT(a_tcp != NULL);
  ASSERT(m_portfilter_rule);
  get_conn_addr(a_tcp, pcon);

  // TODO
  // 按服务器端口过滤
  if (likely(1))
  {
    // PORT 名单检查
    if (a_tcp->pp_state == PP_JUST_EST)
    {
      __sync_fetch_and_add(&(m_p_stats_port_fwd->st_cnt.cnt), 1);
    }
    // 服务器端口是命中的，则不再解析
    if (m_portfilter_rule->port_filter_hit(pcon->server.port))
    {
      if (a_tcp->pp_state == PP_JUST_EST)
      {
        __sync_fetch_and_add(&(m_p_stats_port_fwd->st_cnt_drop.cnt), 1);
      }
      return;
    }

    if (a_tcp->pp_state == PP_JUST_EST)
    {
      __sync_fetch_and_add(&(m_p_stats_port_fwd->st_cnt_fwd.cnt), 1);
    }
  }

  ASSERT(m_tcp_callback != NULL);
  if (m_tcp_callback == NULL)
  {
    return;
  }

  worker_params_t *pwp = (worker_params_t *)userdata;
  CSessionMgt *psm = (CSessionMgt *)pwp->session;
  m_tcp_callback(psm, a_tcp, this_time_not_needed, pcon);
}


int fastcall CTcpParser::pp_tcp_port_filter_hit(void *userdata, unsigned short port)
{
  CTcpParser *pThis = m_tcp_parser;
  ASSERT(pThis != NULL);

  if (pThis != NULL)
  {
    return pThis->tcp_port_filter_hit(userdata, port);
  }

  return 0;
}

int fastcall CTcpParser::pp_tcp_port_white_hit(void *userdata, unsigned short port)
{
  CTcpParser *pThis = m_tcp_parser;
  ASSERT(pThis != NULL);

  if (pThis != NULL)
  {
    return pThis->tcp_port_white_hit(userdata, port);
  }

  return 0;
}

int CTcpParser::tcp_port_filter_hit(void *userdata, unsigned short port) {
  return m_portfilter_rule->port_filter_hit(port);
}

int CTcpParser::tcp_port_white_hit(void *userdata, unsigned short port)
{
  return m_portfilter_rule->port_white_hit(port);
}

void CTcpParser::tcp_drop_data(struct tcp_stream *a_tcp, int drop_reason)
{
  a_tcp->drop_data_reason = drop_reason;
}

bool CTcpParser::tcp_discard(struct tcp_stream *a_tcp, int dir, int num)
{
  pp_tcp_discard_force(a_tcp, num);
  return true;
}

bool CTcpParser::tcp_discard_and_update(struct tcp_stream *a_tcp, int dir, int num)
{
  struct half_stream *rcv;
  switch (dir)
  {
  default:
    GWLOG_DEBUG(m_comm, "dir=%d\n", dir);
    return false;

  case STREAM_REQ:
    if (a_tcp->reverse == 0) {
      rcv = &a_tcp->server;
    }
    else {
      rcv = &a_tcp->client;
    }

    break;

  case STREAM_RSP:
    if (a_tcp->reverse == 0) {
      rcv = &a_tcp->client;
    }
    else {
      rcv = &a_tcp->server;
    }
    break;
  }

  pp_tcp_discard_force_update(a_tcp, rcv, num);
  return true;
}

const char *CTcpParser::get_data(const struct tcp_stream *a_tcp, int dir, int *data_len, int *offset_out)
{
  const struct half_stream *hlf;

  switch (dir)
  {
  default:
    GWLOG_DEBUG(m_comm, "dir=%d\n", dir);
    return NULL;

  case STREAM_REQ:
    hlf = &a_tcp->client;

    break;

  case STREAM_RSP:
    hlf = &a_tcp->server;

    break;
  }

  *data_len = hlf->count;

  return hlf->data;
}

int CTcpParser::get_data_dir(const struct tcp_stream *a_tcp)
{
//  int dir = STREAM_REQ;
//
//  if (a_tcp->server.count_new_urg)
//  {
//    // new byte of urgent data has arrived
//    // TODO
//    //a_tcp->server.urgdata
//    return -1;
//  }
//  if (a_tcp->client.count_new_urg)
//  {
//    // new byte of urgent data has arrived
//    // TODO
//    //a_tcp->client.urgdata
//    return -1;
//  }
//
//  if ((a_tcp->pp_state != PP_DATA) && (a_tcp->connect_state == TCP_CONNECT_NORMAL))
//  {
//    return dir;
//  }
//
//  // We don't have to check if urgent data to client has arrived,
//  // because we haven't increased a_tcp->client.collect_urg variable.
//  // So, we have some normal data to take care of.
//  if (a_tcp->client.count_new)
//  {
//    dir = STREAM_RSP;
//    // // new data for client
//    // hlf = &a_tcp->client;
//  }
//  else if (a_tcp->server.count_new)
//  {
//    dir = STREAM_REQ;
//    // hlf = &a_tcp->server;
//  }

  return a_tcp->dir;
}

void CTcpParser::get_conn_addr(const struct tcp_stream *a_tcp, struct conn *pcon_out)
{
  if (a_tcp->addr.i_ip_type == 0)
  {
    pcon_out->client.v = IPV4_FLAG;
    pcon_out->server.v = IPV4_FLAG;

    pcon_out->client.ipv4 = a_tcp->addr.saddr;
    pcon_out->server.ipv4 = a_tcp->addr.daddr;
  }
  else
  {
    pcon_out->client.v = IPV6_FLAG;
    pcon_out->server.v = IPV6_FLAG;
    memcpy(pcon_out->client.ipv6, a_tcp->addr.a_saddr, sizeof(pcon_out->client.ipv6));
    memcpy(pcon_out->server.ipv6, a_tcp->addr.a_daddr, sizeof(pcon_out->server.ipv6));
  }
  pcon_out->client.port = a_tcp->addr.source;
  pcon_out->server.port = a_tcp->addr.dest;
  pcon_out->pcap_filename = a_tcp->pcap_filename;
  pcon_out->p_server_mac = a_tcp->server_mac;
  pcon_out->p_client_mac = a_tcp->client_mac;
}

double CTcpParser::get_ts(const struct tcp_stream *a_tcp) const
{
  return (a_tcp->ts + ((double)a_tcp->ts_us / 1000000));
}

int64_t CTcpParser::get_ts_ms(const struct tcp_stream *a_tcp) const
{
  return (int64_t)a_tcp->ts * 1000LL + a_tcp->ts_us / (1000LL);
}

static inline int clone_half_stream_data(struct half_stream *p_hs_dst, const struct half_stream *p_hs_src)
{
  p_hs_dst->offset = p_hs_src->offset;
  p_hs_dst->count = p_hs_src->count;
  p_hs_dst->count_new = p_hs_src->count_new;
  //p_hs_dst->ack = p_hs_src->ack;
  //p_hs_dst->seq = p_hs_src->seq;
  p_hs_dst->first_seq = p_hs_src->first_seq;
  p_hs_dst->stream_ack = p_hs_src->stream_ack;
  int length = p_hs_src->count - p_hs_src->offset;

    if(p_hs_src->data)
    {
        p_hs_dst->data = new char[length];
        memcpy(p_hs_dst->data, p_hs_src->data, length);
    }

  return length;
}

struct tcp_stream *CTcpParser::clone_stream_data(const struct tcp_stream *a_tcp)
{
  struct tcp_stream *p_tcp = new tcp_stream();

  p_tcp->addr = a_tcp->addr;
  p_tcp->pp_state = a_tcp->pp_state;
  p_tcp->read = a_tcp->read;
  p_tcp->total = a_tcp->total;
  p_tcp->ts = a_tcp->ts;
  p_tcp->ts_us = a_tcp->ts_us;
  clone_half_stream_data(&p_tcp->server, &a_tcp->server);
  clone_half_stream_data(&p_tcp->client, &a_tcp->client);
  p_tcp->next_node = a_tcp->next_node;
  p_tcp->prev_node = a_tcp->prev_node;
  p_tcp->hash_index = a_tcp->hash_index;
  p_tcp->p_session = a_tcp->p_session;
  p_tcp->vlan_id = a_tcp->vlan_id;
  p_tcp->pwp = a_tcp->pwp;

  return p_tcp;
}

static inline void delete_half_stream_data(const struct half_stream *p_hs)
{
  delete[] p_hs->data;
}

void CTcpParser::delete_stream_data(const struct tcp_stream *a_tcp)
{
  // const struct half_stream *p_hs;
  if (a_tcp == NULL)
  {
    return;
  }

  delete_half_stream_data(&a_tcp->server);
  delete_half_stream_data(&a_tcp->client);

  delete a_tcp;
}

void CTcpParser::get_forward_info(char *log_buf, size_t log_buf_len) const
{
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\n%-20s %20s %20s %20s\n", "stats forward ", "total", "drop", "fwd");
  snprintf (log_buf + strlen(log_buf), log_buf_len - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        IP_FORWATD,
        m_p_stats_ip_fwd->st_cnt.cnt,
        m_p_stats_ip_fwd->st_cnt_drop.cnt,
        m_p_stats_ip_fwd->st_cnt_fwd.cnt);
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        IPV4_FORWARD,
        m_p_stats_ip_fwd->st_cnt.cnt_ipv4,
        m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4,
        m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4);
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        IPV6_FORWARD,
        m_p_stats_ip_fwd->st_cnt.cnt_ipv6,
        m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6,
        m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv6);
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        IP_FORWATD_BYTES,
        m_p_stats_ip_fwd->st_cnt.cnt_bytes,
        m_p_stats_ip_fwd->st_cnt_drop.cnt_bytes,
        m_p_stats_ip_fwd->st_cnt_fwd.cnt_bytes);
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        "ipv4 forward bytes",
        m_p_stats_ip_fwd->st_cnt.cnt_ipv4_bytes,
        m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4_bytes,
        m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4_bytes);

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        "ipv6 forward bytes",
        m_p_stats_ip_fwd->st_cnt.cnt_ipv6_bytes,
        m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes,
        m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv6_bytes);
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
        "tcp stream data",
        stream_lost.stream_total,
        stream_lost.lost,
        stream_lost.stream_total - stream_lost.lost);

  return;
}

void CTcpParser::get_drop_info(char *log_buf, size_t log_buf_len) const
{

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf),
            "\n%s"
            "\nblack ip:         %10" PRIu64
            "\nnot ip:           %10" PRIu64
            "\nglobal limit:     %10" PRIu64
            "\nlicense expire:   %10" PRIu64
            "\nip limit:         %10" PRIu64
            "\nip except limit:  %10" PRIu64
            "\nlicense limit:    %10" PRIu64
            "\n\n"
            , "limit drop"
            , g_u64_filter_blackip_packets
            , g_u64_filter_notip_packets
            , g_u64_filter_global_limit_packets
            , g_u64_filter_expire_limit_packets
            , g_u64_filter_ip_limit_packets
            , g_u64_filter_ip_except_limit_packets
            , g_u64_filter_license_limit_packets);

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf),
            "\n%s"
            "\ninvalid ip:       %10" PRIu64
            "\ninvalid v4 gre:   %10" PRIu64
            "\ninvalid v6 in v4: %10" PRIu64
            "\ninvalid v6:       %10" PRIu64
            "\ninvalid v6 gre:   %10" PRIu64
            "\ninvalid v6 in v6: %10" PRIu64
            "\n\n"
            ,"ip drop"
            , ip_lost.invalid_ip_packets
            , ip_lost.invalid_v4_gre_packets
            , ip_lost.invalid_v6_in_v4_packets
            , ip_lost.invalid_v6_packets
            , ip_lost.invalid_v6_gre_packets
            , ip_lost.invalid_v6_in_v6_packets);

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf),
            "\n%s"
            "\ntimeout:          %10" PRIu64
            "\noverfull:         %10" PRIu64
            "\nno momery:        %10" PRIu64
            "\nfailed:           %10" PRIu64
            "\nnot tcp:          %10" PRIu64
            "\nipv4 defrag:      %10" PRIu64
            "\nipv6_defrag:      %10" PRIu64
            "\n\n"
            , "ip drop"
            , reassemble_lost.timeout
            , reassemble_lost.over_cache
            , reassemble_lost.no_memory
            , reassemble_lost.failed
            , ip_lost.not_tcp_packets
            , ip_lost.ipv4_defrag_packets
            , ip_lost.ipv6_defrag_packets);

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf),
            "\n%s"
            "\ntotal:            %10" PRIu64
            "\nport hit:         %10" PRIu64
            "\ninvalid:          %10" PRIu64
            "\ntimeout:          %10" PRIu64
            "\ncache overfull:   %10" PRIu64
            "\nsession overfull: %10" PRIu64
            "\nno memory:        %10" PRIu64
            "\nclose http:       %10" PRIu64
            "\nclose http http:  %10" PRIu64
            "\n\n"
            , "tcp drop"
            , tcp_lost.total
            , tcp_lost.port_hit
            , tcp_lost.invalid
            , tcp_lost.timeout
            , tcp_lost.cache_overfull
            , tcp_lost.session_overfull
            , tcp_lost.no_memory
            , tcp_lost.close_http
            , tcp_lost.close_http_http);

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf),
            "\n%s"
            "\nts check:         %10" PRIu64
            "\nnot regist:       %10" PRIu64
            "\nreset:            %10" PRIu64
            "\nclose:            %10" PRIu64
            "\nduplicate:        %10" PRIu64
            "\n\n"
            , "tcp drop"
            , tcp_lost.ts_check
            , tcp_lost.not_regist
            , tcp_lost.reset
            , tcp_lost.session_close
            , tcp_lost.duplicate);

  return;
}

void CTcpParser::get_tcp_stream_info(char *log_buf, size_t log_buf_len) const
{
  char buf[64] = {0};

  // show worker count
#define WORKER_CNT_ITEM_MAX_NUM (20 + 4)
  unsigned int i = 0;
  char str[COUNTOF(m_worker_cnt) * WORKER_CNT_ITEM_MAX_NUM + 4 * 4] = {0};
  char *p = str;
  for (i = 0; i < COUNTOF(m_worker_cnt); ++i)
  {
    if ((i & (4 - 1)) == (0) && i > 0)
    {
      strcat(p, "\n ");
      p += strlen(p);
    }

    if (m_worker_cnt[i] > 0)
    {
      snprintf(buf, COUNTOF(buf) - 1, "%" PRIu64 "[%X]", m_worker_cnt[i], m_worker_state[i]);
    }
    else
    {
      snprintf(buf, COUNTOF(buf) - 1, "%" PRIu64 "", m_worker_cnt[i]);
    }
    snprintf(p, WORKER_CNT_ITEM_MAX_NUM, "%15s ", buf);

    p += strlen(p);
  }

  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\nworker count : \n %s\n\n", str);

  memset(str, 0, sizeof(str));
  p = str;
  for (i = 0; i < COUNTOF(m_worker_cnt); ++i)
  {
    if ((i & (4 - 1)) == (0) && i > 0)
    {
      strcat(p, "\n ");
      p += strlen(p);
    }

    if (g_worker_params[i].tcp_run_max_stream == 0 && i >= 5) {
      break;
    }

    snprintf(buf,COUNTOF(buf) - 1, "%" PRIu32 "/%" PRIu32, g_worker_params[i].tcp_num, g_worker_params[i].tcp_run_max_stream);
    snprintf(p, WORKER_CNT_ITEM_MAX_NUM, "%15s ", buf);

    p += strlen(p);
  }
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\ntcp stream count; tcp_num/max_tcp_num : \n %s\n\n", str);

  memset(str, 0, sizeof(str));
  p = str;
  for (i = 0; i < COUNTOF(m_worker_cnt); ++i)
  {
    if ((i & (4 - 1)) == (0) && i > 0)
    {
      strcat(p, "\n ");
      p += strlen(p);
    }

    if (g_worker_params[i].tcp_run_max_stream == 0 && i >= 5) {
      break;
    }

    snprintf(buf,COUNTOF(buf) - 1, "%" PRIu32 "/%" PRIu32, g_worker_params[i].http_num, g_worker_params[i].http_max_num);
    snprintf(p, WORKER_CNT_ITEM_MAX_NUM, "%15s ", buf);

    p += strlen(p);
  }
  snprintf (log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\nhttp stream count; http_num/max_http_num : \n %s\n\n", str);
}

/**
 * 获取工作线程的状态
 * @param int no
 */
int CTcpParser::get_worker_state(int no) const
{
  ASSERT(no >= 0 && (unsigned int)no < COUNTOF(m_worker_state));
  if (no >= 0 && (unsigned int)no < COUNTOF(m_worker_state))
  {
    return m_worker_state[no];
  }
  return WORKER_STATE_UNKNOWN;
}

/**
 * 获取工作线程的调用次数
 * @param int no
 */
uint64_t CTcpParser::get_worker_count(int no) const
{
  ASSERT(no >= 0 && (unsigned int)no < COUNTOF(m_worker_cnt));
  if (no >= 0 && (unsigned int)no < COUNTOF(m_worker_cnt))
  {
    return m_worker_cnt[no];
  }
  return 0;
}

/**
 *  等待旧配置不再使用
 */
int CTcpParser::wait_for_worker_use_conf(void)
{
  int worker_state[WORKER_PARAMS_MAX_NUM] = {0};
  // 等待旧配置不在使用
  while (check_worker_use_conf(worker_state))
  {
    if (m_quit_signal)
    {
      return 1;
    }
    usleep(100 * 1000L);
    //memset(worker_state, 0, sizeof(worker_state));
  }

  return 0;
}


void CTcpParser::print_tcp_stats_callback(void *p)
{
  const CTcpParser *pThis = (const CTcpParser *)p;
  ASSERT(pThis != NULL);

  pThis->print_forward_stats();
  pThis->print_drop_stats();
  pThis->print_worker_stats();
}

void CTcpParser::print_forward_stats(void) const
{
  char log_buf[LOG_BUF_LEN] = {0};
  get_forward_info(log_buf, LOG_BUF_LEN);
  printf ("%s", log_buf);
  // printf ("\n%-20s %20s %20s %20s\n", "stats forward ", "total", "drop", "fwd");
  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       IP_FORWATD,
  //       m_p_stats_ip_fwd->st_cnt.cnt,
  //       m_p_stats_ip_fwd->st_cnt_drop.cnt,
  //       m_p_stats_ip_fwd->st_cnt_fwd.cnt);
  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       IPV4_FORWARD,
  //       m_p_stats_ip_fwd->st_cnt.cnt_ipv4,
  //       m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4,
  //       m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4);
  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       IPV6_FORWARD,
  //       m_p_stats_ip_fwd->st_cnt.cnt_ipv6,
  //       m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6,
  //       m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv6);
  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       IP_FORWATD_BYTES,
  //       m_p_stats_ip_fwd->st_cnt.cnt_bytes,
  //       m_p_stats_ip_fwd->st_cnt_drop.cnt_bytes,
  //       m_p_stats_ip_fwd->st_cnt_fwd.cnt_bytes);
  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       "ipv4 forward bytes",
  //       m_p_stats_ip_fwd->st_cnt.cnt_ipv4_bytes,
  //       m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv4_bytes,
  //       m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4_bytes);

  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       "ipv6 forward bytes",
  //       m_p_stats_ip_fwd->st_cnt.cnt_ipv6_bytes,
  //       m_p_stats_ip_fwd->st_cnt_drop.cnt_ipv6_bytes,
  //       m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv6_bytes);
  // printf ("%-20s %20" PRIu64 " %20" PRIu64 " %20" PRIu64 "\n",
  //       "tcp stream data",
  //       stream_lost.stream_total,
  //       stream_lost.lost,
  //       stream_lost.stream_total - stream_lost.lost);
}

void CTcpParser::print_worker_stats(void) const
{
  /*
  worker count :
           51[2]           64[2]           10[2]               0
               0               0               0               0
               0               0               0               0
               0               0               0               0
               0               0               0               0
               0               0               0               0
               0               0               0               0
               0               0               0               0
  */
 char log_buf[LOG_BUF_LEN] = {0};
 get_tcp_stream_info(log_buf, LOG_BUF_LEN);
 printf ("%s", log_buf);

//   char buf[64] = {0};

//   if (unlikely(0))
//   {
//     return;
//   }

//   // show worker count
// #define WORKER_CNT_ITEM_MAX_NUM (20 + 4)
//   unsigned int i = 0;
//   char str[COUNTOF(m_worker_cnt) * WORKER_CNT_ITEM_MAX_NUM + 4 * 4] = {0};
//   char *p = str;
//   for (i = 0; i < COUNTOF(m_worker_cnt); ++i)
//   {
//     if ((i & (4 - 1)) == (0) && i > 0)
//     {
//       strcat(p, "\n ");
//       p += strlen(p);
//     }

//     if (m_worker_cnt[i] > 0)
//     {
//       snprintf(buf, COUNTOF(buf) - 1, "%" PRIu64 "[%X]", m_worker_cnt[i], m_worker_state[i]);
//     }
//     else
//     {
//       snprintf(buf, COUNTOF(buf) - 1, "%" PRIu64 "", m_worker_cnt[i]);
//     }
//     snprintf(p, WORKER_CNT_ITEM_MAX_NUM, "%15s ", buf);

//     p += strlen(p);
//   }
//   printf("\nworker count : \n %s\n\n", str);
//   print_tcp_stream_stats();
}

void CTcpParser::print_drop_stats(void) const
{
  char log_buf[LOG_BUF_LEN] = {0};
  get_drop_info(log_buf, LOG_BUF_LEN);
  printf ("%s", log_buf);

}

void CTcpParser::print_tcp_stream_stats(void)  const
{
  char buf[64] = {0};

  if (unlikely(0))
  {
    return;
  }


#define TCP_STREAM_STATS_ITEM_MAX_NUM (20 + 4)
  unsigned int i = 0;
  char str[COUNTOF(m_worker_cnt) * TCP_STREAM_STATS_ITEM_MAX_NUM + 4 * 4] = {0};
  char *p = str;
  for (i = 0; i < COUNTOF(m_worker_cnt); ++i)
  {
    if ((i & (4 - 1)) == (0) && i > 0)
    {
      strcat(p, "\n ");
      p += strlen(p);
    }

    if (g_worker_params[i].tcp_run_max_stream == 0 && i >= 5) {
      break;
    }

    snprintf(buf,COUNTOF(buf) - 1, "%" PRIu32 "/%" PRIu32, g_worker_params[i].tcp_num, g_worker_params[i].tcp_run_max_stream);
    snprintf(p, WORKER_CNT_ITEM_MAX_NUM, "%15s ", buf);

    p += strlen(p);
  }
  printf("\ntcp stream count; tcp_num/max_tcp_num : \n %s\n\n", str);
}


int CTcpParser::check_worker_use_conf(int worker_state[WORKER_PARAMS_MAX_NUM])
{
  int i;

  if (worker_state == NULL)
  {
    return 1;
  }

  for (i = 0; i < WORKER_PARAMS_MAX_NUM; i++)
  {
    if (get_worker_count(i) > 0)
    {
      if (get_worker_state(i) == 2)
      {
        worker_state[i] = -1;
      }
      else
      {
        if (worker_state[i] == 0)
        {
          // 当前状态为运行中，记录当前的任务数量;
          // 下次检查任务数量更新时，则可以认为不再使用旧配置了
          worker_state[i] = get_worker_count(i) % 1000000;
        }
        else if (labs(worker_state[i] - (signed)get_worker_count(i) % 1000000) >= 2)
        {
          worker_state[i] = -1;
        }
      }
    }
    else
    {
      worker_state[i] = -1;
    }
  }

  for (i = 0; i < WORKER_PARAMS_MAX_NUM; i++)
  {
    if (worker_state[i] >= 0)
    {
      return 1;
    }
  }

  return 0;
}

void CTcpParser::stats_ip_fwd_register(stats_forward_t *stats_ip_fwd)
{
  pthread_mutex_lock(&m_p_stats_ip_fwd_mutex);
  m_p_stats_ip_fwd_array[m_p_stats_ip_fwd_count] = stats_ip_fwd;
  m_p_stats_ip_fwd_count++;
  pthread_mutex_unlock(&m_p_stats_ip_fwd_mutex);
}

void CTcpParser::stats_ip_fwd_collect()
{
  m_p_stats_ip_fwd->st_cnt.cnt = 0;
  m_p_stats_ip_fwd->st_cnt.cnt_bytes = 0;

  m_p_stats_ip_fwd->st_cnt.cnt_ipv4 = 0;
  m_p_stats_ip_fwd->st_cnt.cnt_ipv4_bytes = 0;

  m_p_stats_ip_fwd->st_cnt_fwd.cnt = 0;
  m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4 = 0;
  m_p_stats_ip_fwd->st_cnt_fwd.cnt_bytes = 0;
  m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4_bytes = 0;

  uint8_t i = 0;
  for (i = 0; i < m_p_stats_ip_fwd_count; i++)
  {
    m_p_stats_ip_fwd->st_cnt.cnt += m_p_stats_ip_fwd_array[i]->st_cnt.cnt;
    m_p_stats_ip_fwd->st_cnt.cnt_bytes += m_p_stats_ip_fwd_array[i]->st_cnt.cnt_bytes;

    m_p_stats_ip_fwd->st_cnt.cnt_ipv4 += m_p_stats_ip_fwd_array[i]->st_cnt.cnt_ipv4;
    m_p_stats_ip_fwd->st_cnt.cnt_ipv4_bytes += m_p_stats_ip_fwd_array[i]->st_cnt.cnt_ipv4_bytes;

    m_p_stats_ip_fwd->st_cnt_fwd.cnt += m_p_stats_ip_fwd_array[i]->st_cnt_fwd.cnt;
    m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4 += m_p_stats_ip_fwd_array[i]->st_cnt_fwd.cnt_ipv4;
    m_p_stats_ip_fwd->st_cnt_fwd.cnt_bytes += m_p_stats_ip_fwd_array[i]->st_cnt_fwd.cnt_bytes;
    m_p_stats_ip_fwd->st_cnt_fwd.cnt_ipv4_bytes += m_p_stats_ip_fwd_array[i]->st_cnt_fwd.cnt_ipv4_bytes;
  }

  ip_lost.total_packets = 0;

  for (i = 0; i < ip_lost_count; i++)
  {
    ip_lost.total_packets += ip_lost_array[i]->total_packets;
  }

  tcp_lost.total = 0;

  for (i = 0; i < tcp_lost_count; i++)
  {
    tcp_lost.total += tcp_lost_array[i]->total;
  }

  for (i = 0; i < WORKER_PARAMS_MAX_NUM; i++)
  {
    if (NULL == m_worker_cnt_array[i]) continue;
    m_worker_cnt[i] = *(m_worker_cnt_array[i]);
  }

  for (i = 0; i < WORKER_PARAMS_MAX_NUM; i++)
  {
    if (NULL == m_worker_state_array[i]) continue;
    m_worker_state[i] = *(m_worker_state_array[i]);
  }
}

void CTcpParser::worker_cnt_register(uint64_t* worker_cnt, int no)
{
  if (NULL != m_worker_cnt_array[no])
  {
    fprintf(stderr, "%s have error, no=%d\n", __func__, no);
  }

  m_worker_cnt_array[no] = worker_cnt;
}

void CTcpParser::worker_state_register(int* worker_state, int no)
{
  if (NULL != m_worker_state_array[no])
  {
    fprintf(stderr, "%s have error, no=%d\n", __func__, no);
  }

  m_worker_state_array[no] = worker_state;
}
