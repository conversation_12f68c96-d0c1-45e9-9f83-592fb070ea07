#ifndef _FTP_PARSER_H_
#define _FTP_PARSER_H_

#include <sys/types.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define FTP_SERVER_READY_STATUS            (220)
#define FTP_CLOSE_DATA_CONN_STATUS         (226)
#define COMMAND_ARGS_BUF_LEN               (256)
#define IS_NUM(c) ((c) >= '0' && (c) <= '9')

/* request command */
#define FTP_COMMAND_MAP(XX) \
    XX(0, USER, USER)       \
    XX(1, ACCT, ACCT)       \
    XX(2, ABOR, ABOR)       \
    XX(3, ALLO, ALLO)       \
    XX(4, APPE, APPE)       \
    XX(5, CDUP, CDUP)       \
    XX(6, CWD, CWD)         \
    XX(7, DELE, DELE)       \
    XX(8, HELP, HELP)       \
    XX(9, LIST, LIST)       \
    XX(10, MODE, MODE)      \
    XX(11, MKD, MKD)        \
    XX(12, NLST, NLST)      \
    XX(13, NOOP, NOOP)      \
    XX(14, OPTS, OPTS)      \
    XX(15, PASS, PASS)      \
    XX(16, PASV, PASV)      \
    XX(17, PORT, PORT)      \
    XX(18, PWD, PWD)        \
    XX(19, QUIT, QUIT)      \
    XX(20, REIN, REIN)      \
    XX(21, REST, REST)      \
    XX(22, RETR, RETR)      \
    XX(23, RMD, RMD)        \
    XX(24, RNFR, RNFR)      \
    XX(25, RNTO, RNTO)      \
    XX(26, SITE, SITE)      \
    XX(27, SIZE, SIZE)      \
    XX(28, SMNT, SMNT)      \
    XX(29, STAT, STAT)      \
    XX(30, STOR, STOR)      \
    XX(31, STRU, STRU)      \
    XX(32, SYST, SYST)      \
    XX(33, TYPE, TYPE)      \
    XX(34, AUTH, AUTH)      \
    XX(35, EPSV, EPSV)      \
    XX(36, EPRT, EPRT)      \
    XX(37, FEAT, FEAT)      \
    XX(38, MDTM, MDTM)      \
    XX(39, XCCT, XCCT)      \
    XX(40, XCUP, XCUP)      \
    XX(41, XMKD, XMKD)      \
    XX(42, XPWD, XPWD)      \
    XX(43, XRCP, XRCP)      \
    XX(44, XRCT, XRCT)      \
    XX(45, XRMD, XRMD)      \
    XX(46, XRSQ, XRSQ)      \
    XX(47, XSCT, XSCT)      \
    XX(48, XSEM, XSEM)      \
    XX(49, XSEN, XSEN)      \
    XX(50, XSET, XSET)      \

enum ftp_command
{
#define XX(num, name, string) FTP_##name = num,
    FTP_COMMAND_MAP(XX)
#undef XX
};

enum ftp_parser_type
{
    FTP_REQUEST,
    FTP_RESPONSE,
    FTP_BOTH
};

#define FTP_ERRNO_MAP(XX) \
    XX(0, OK, "no error") \
    XX(-1, INVAILD_PARAM, "param invaild") \
    XX(-2, INVAILD_TYPE, "ftp type invaild") \
    XX(-3, INVAILD_STATUS, "invaild rsp status") \
    XX(-4, FORMAT_ERR, "format error") \
    XX(-5, PASV_IP_FORMAT_ERR, "pasv ip format invaild") \
    XX(-6, PASV_IP_ERR, "pasv ip err") \
    XX(-7, PASV_PORT_ERR, "pasv port err") \
    XX(-8, INVAILD_COMMAND, "invaild command") \
    XX(-9, INVAILD_RSP_ARG, "invaild rsp arg value") \
    XX(10, INVAILD_REQ_ARG, "invaild req arg value") \

enum ftp_errno
{
#define FTP_ERRNO_GEN(num, n, s) FTPE_##n = num,
    FTP_ERRNO_MAP(FTP_ERRNO_GEN)
#undef FTP_ERRNO_GEN
};

typedef struct
{
    uint8_t u8_ftp_type;
    uint8_t u8_ftp_command;
    uint16_t u16_rsp_state;
    char a_command_arg[COMMAND_ARGS_BUF_LEN];
    char a_rsp_arg[COMMAND_ARGS_BUF_LEN];
} ftp_parser_t;

void ftp_parser_init(ftp_parser_t *p_ftp_parser, enum ftp_parser_type type);

int ftp_parser_execute(ftp_parser_t *p_ftp_parser, const char *p_data, size_t len);

const char *ftp_method_str (enum ftp_command m);

#ifdef __cplusplus
}
#endif

#endif //_FTP_PARSER_H_