#ifndef __FTP_PARSER_COMMON_H__
#define __FTP_PARSER_COMMON_H__

#include "proto_ftp_parser.h"
#include "utils.h"

#define FTP_HEADER_MAX_LENGTH (1024) /* FTP头部最大长度 */
#define FTP_CB_CF_NONE (0)
#define FTP_CB_CF_BREAK (1)
#define FTP_CB_CF_CONTINUE (2)

#define FTP_RECV_MAX_BODY_LEN (52428800) /* 接受最大FTP数据量 */

static volatile time_t g_last_check_time = 0;
static volatile uint64_t g_u64_ftp_upload_ms = 0;
static volatile uint32_t g_u32_ftp_upload_index = 0;

#define FTP_DATA_LINK_NUM (10000)

typedef struct ftp_stream
{
    ftp_parser_ext_t *p_ftp_parser;
    ftp_data_link_t *p_ftp_data_parser;
    int ftp_link_type;                             /* 1: 表示控制链路 2: 表示数据链路  其他值表示不是FTP数据 */
    struct{
        const char* p_d;
        int offset;
    }rsp, req;
}ftp_stream_t;

typedef struct ftp_data_map
{
    int i_state;                       /* 是否使用 1 使用 0 未使用 */
    data_link_info_t st_ftp_data_link; /* 控制链路指定的数据链路的ip、端口 */
    //struct session_t *p_session;       /* 控制链路的session指针 */
    struct StreamData *p_stream_data; /* 控制链路的stream data 指针 */
} ftp_data_map_t;

typedef struct parser_header_body_params
{
    CSessionMgt *psm;
    CSession *p_session;
    StreamData *psd;
    const app_stream_t *a_app;
    struct tcp_stream *a_tcp;
    const struct conn *pcon;
    // struct half_stream *hlf;
    // http_parser_ext_t *p_parser;
    enum ftp_parser_type dir;

    const char *data;
    //int offset;
    //int len_new;
    //int len;
    int data_len;

    size_t parsed; // for out
    int ret;       // for out HTTP_CB_xxx

    //parser_request_response_params_t req_rsp_param[1]; // for parser
} parser_header_body_params_t;

#endif //__FTP_PARSER_COMMON_H__