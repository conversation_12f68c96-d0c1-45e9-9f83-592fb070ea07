/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __FTP_PARSER_H__
#define __FTP_PARSER_H__

#include <pthread.h>
#include <stdint.h>
#include <string>
#include "gw_i_parser.h"
#include "ftp_parser_inner.h"
#include "task_worker.h"
#include "gw_ver.h"
#include "simple_json.h"

#define FTPPARSER_VER GW_VER_STRING(GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION)

class CWorkerQueue;
class CTaskWorker;

typedef struct
{
    volatile uint64_t u64_ftp_parser_cnt;        /* 解析FTP数据包数量 */
    volatile uint64_t u64_ftp_parser_bytes;      /* 解析FTP数据包字节数 */
    volatile uint64_t u64_ftp_parser_succ;       /* 解析FTP数据包成功的数量 */
    volatile uint64_t u64_ftp_parser_fail;       /* 解析FTP数据包失败的数据量 */
}stats_ftp_ctrl_parser_t;

typedef struct
{
  volatile uint64_t u64_ftp_match_cnt;      // 匹配总数
  volatile uint64_t u64_ftp_match_succ;     // 匹配成功数量
  volatile uint64_t u64_ftp_match_fail;     // 匹配失败数量
} stats_ftp_ctrl_match_t;

typedef struct 
{
    volatile uint64_t u64_ftp_data_match_cnt;
    volatile uint64_t u64_ftp_data_match_succ;
    volatile uint64_t u64_ftp_data_match_fail;
}stats_ftp_data_match_t;
 
typedef struct
{
    volatile stats_ftp_ctrl_parser_t st_ftp_ctrl_parser;
    volatile stats_ftp_ctrl_parser_t st_req_ftp_ctrl_parser;
    volatile stats_ftp_ctrl_parser_t st_rsp_ftp_ctrl_parser;
    volatile uint64_t u64_ftp_ctrl_match_cnt;                   /* 控制链路请求和响应匹配的个数 */
    volatile stats_ftp_data_match_t st_ftp_data_match;          /* 数据链路匹配控制链链路信息 */
    volatile uint64_t u64_ftp_upload_event_cnt;                 /* 上传事件个数 */
}stats_ftp_t;

typedef struct upload_ftp_info
{
    TaskWorkerData twd;
    size_t mem_size;
    /* FTP 控制链路参数 */
    ftp_ctrl_info_t st_ctrl_info;
    /* FTP数据链路参数 */
    ftp_data_info_t st_data_info;
    char a_event_id[128];
    int login;
}upload_ftp_info_t;

struct tcp_stream;
struct ftp_stream;
struct ftp_data_map;
struct StreamData;
struct ftp_parser_ext;
struct ftp_data_link;
struct UploadMsg;
struct parser_header_body_params;
struct cJSON;
struct ftp_ctrl_info;
struct ftp_data_info;
struct net_info;

struct upload_ftp_login_info;
typedef struct ftp_stream ftp_stream_t;

struct ftp_parser_info;

class CGwCommon;
class CSession;

class CUpload;

class CMinioUpload;

class CFtpParser : public CParser
{
public:
    CFtpParser(void);

    virtual ~CFtpParser(void);
public:
  virtual void cache_clean();
  /**
   * 在接收数据时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual bool probe(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  /**
   * 在连接关闭时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual bool probe_on_close(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  /**
   * 在连接重置时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual bool probe_on_reset(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  /**
   * 在接收数据时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  virtual int parse_clear(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  /**
   * 在连接关闭时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse_on_close(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  /**
   * 在连接重置时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse_on_reset(CSessionMgt *, const app_stream *,const struct conn *, CSession *);

  /**
   * 获取当前流解析出来的数据。
   * @param struct StreamData *
   * @param int dir
   * @param int *data_len
   * @param int *offset_out
   */
  virtual const char *get_data(const struct StreamData *, int dir, int *data_len, int *offset_out);

  /**
   * 已处理字节数。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard(struct StreamData *, int dir, int num);

  /**
   * 已处理字节数，同时更新数据。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard_and_update(struct StreamData *, int dir, int num);

  // /**
  //  * 删除解析对象中在会话管理中的单边数据。
  //  * @param HalfStreamData*
  //  */
  // virtual void del_session_half_stream(HalfStreamData *) = 0;

  /**
   * @param StreamData*
   */
  virtual void del_session_stream(StreamData *);

  /**
   * @param SessionMgtData*
   */
  virtual void del_session_param(SessionMgtData *);

  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
   */
  virtual const char *get_name(void) const;

  /**
   * 获取版本号。
   */
  virtual const char *get_version(void) const;

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置过滤规则。
   * @param CFilterRule *rule
   */
  virtual void set_url_filter_rule(CFilterRule *rule);

  /**
   *  设置账号过滤规则 
   *  @param CFilterRule *rule
   */
  virtual void set_accout_filter_rule(CFilterRule *rule);

  virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule);
  /**
   * 增加上层协议解析对象。
   * @param CParser *parser
   */
  virtual void add_upstream(CParser *parser);

  /**
   * 清空上层协议解析对象
   */
  virtual void reset_upstream(void);

  /**
   * 推送到上层消息(异步方式, Json序列化数据)
   * @param char *s
   * @param size_t *length
   */
  virtual void push_upstream_msg(char *s, size_t length);

  /**
   * 是否使用当前协议解析流数据
   * @param struct StreamData*
   */
  virtual bool is_parsed(const struct StreamData *) const;

  /**
   * 克隆会话流数据到队列中使用(预留)
   * @param struct StreamData*
   */
  virtual struct StreamData *clone_stream_data(const struct StreamData *);

  /**
   *  获取解析http数量(针对http parser) 
   */
  virtual uint64_t get_parser_http_cnt();

  /**
   *  获取解析http成功的数量(针对http parser) 
   */
  virtual uint64_t get_succ_parser_http_cnt();

  /**
   *  获取解析parser的状态数据，以便于进行查看Parser内部状态
   */
  virtual void* get_parser_status();

  /**
   * 设置解析对象type
   */
  virtual void set_parser_type(int type);

  virtual void read_conf_urlbase_for_mon();

  virtual void read_conf_filetype_for_mon();

  virtual void get_log_buf(char *log_buf, size_t log_buf_len) const;

  virtual uint32_t parser_status() const;

protected:
    int m_quit_signal;
    CGwCommon *m_comm;
    char m_name[32];
    
    ftp_data_map *m_ftp_data_map;
    
    uint64_t m_u64_ftp_upload_ms;
    uint64_t m_u32_ftp_upload_index;
    std::string m_str_gw_ip;
    std::string m_conf_upload_name;
    // int m_conf_kfk_priority;

    stats_ftp_t m_stats_ftp;
    pthread_rwlock_t m_ftp_rwlock;
    int m_ftp_type;
    uint32_t m_u32_conf_ftp_upload_max_size;
    int m_conf_probe_data_limit;
    CUpload *m_upload;
    bool m_upload_file;
    CMinioUpload *m_upload_obj;
    CWorkerQueue *m_upload_data_wq;
    CTaskWorker *m_upload_data_wk;
    int m_conf_ftp_upload_queue_max_num;
    int m_conf_ftp_upload_queue_memory_max_size_bytes;
    int m_conf_ftp_upload_thread_num;
    int m_use_new_event_format;
    int m_conf_parser_enable;

    uint64_t ftp_bytes;
protected:

  friend class CTaskWorkerUploadMsg;
  void send_ftp_data(upload_ftp_info_t*);
  inline CWorkerQueue *get_wq_upload_msg(void) const
  {
    return m_upload_data_wq;
  }
  CWorkerQueue *new_wq_upload_msg();
  int worker_rutine_ftp_upload_data_inner(upload_ftp_info_t*);
  void free_ftp_upload_data(upload_ftp_info_t*);

protected:
  static void print_ftp_stats_callback(void *p);
  void print_ftp_stats(void) const; 
  void modify_stats(int enable);

private:
    StreamData *get_stream_data_from_session(CSession *p_session, int dir);
    int check_ftp_ctrl_link(const struct conn *pcon, const char *p_data, int i_data_len, ftp_parser_type dir, ftp_stream_t *p_fs, CSession *p_session);
    int check_ftp_data_link(struct StreamData *p_stream_data, const struct conn *pcon, CSession *p_session);
    int init_ftp_data_map();
    void uninit_ftp_data_map();
    int find_ftp_data_map(struct StreamData *p_stream_data, const struct addr *p_addr, int i_index);
    void get_parser_from_session(CSession *p_session, enum ftp_parser_type dir, StreamData *&psd_out, void** pp_ftp_parser);
    int ftp_parser_header_body(parser_header_body_params *p_param, ftp_parser_ext *p_ftp_parser, size_t ftp_offset);
    void ftp_cb_parser_merge(enum ftp_parser_type i_dir,  parser_header_body_params *p_param, ftp_parser_ext *p_ftp_parser, CSession *p_session);
    void ftp_cb_parser_response(parser_header_body_params *p_param, ftp_parser_ext *p_ftp_parser, CSession *p_session);
    void ftp_cb_parser_request(parser_header_body_params *p_param, ftp_parser_ext *p_ftp_parser, CSession *p_session);
    void parser_ftp_args(ftp_parser_ext *p_ftp_parser, parser_header_body_params *p_param, ftp_parser_info *p_ftp_rsp_parser_info, ftp_parser_info *p_ftp_req_parser_info);
    void parser_ftp_args_2yx(ftp_parser_ext *p_ftp_parser, parser_header_body_params *p_param, ftp_parser_info *p_ftp_rsp_parser_info);
    void check_ftp_data_map(ftp_parser_ext *p_ftp_parser);
    void release_ftp_data_map(StreamData *psd);
    void new_ftp_data_link_map(ftp_parser_ext *p_ftp_parser, StreamData *p_stream_data);
    int iden_ftp_link_type(CSessionMgt *psm, const struct conn *pcon, CSession *p_session);
    int parser_ctrl(ftp_parser_type dir, CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session);
    int parser_data(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session);
    int parser_ctrl_close(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session);
    int parser_data_close(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session);
    void upload_ftp_login_event(const ftp_parser_ext *p_ftp_parser, CSession *p_session);
    void upload_ftp_data_only_ctrl(ftp_parser_ext *p_ftp_ctrl_parser, double ts);
    void upload_ftp_data(struct StreamData *p_stream_data, const struct conn *pcon, double ts, CSession *p_session);
    char* upload_ftp_data_encode(upload_ftp_info_t& st_upload_ftp_info, size_t *p_s_len);
    char* upload_ftp_new_format(upload_ftp_info_t& st_upload_ftp_info, size_t *p_s_len);
    void add_net_json(struct net_info *p_net_info, const struct conn *pcon, bool reverse);
    void add_ctrl_json(ftp_parser_ext *p_ftp_parser, struct ftp_ctrl_info *p_ftp_ctrl_info, CSession *p_session);
    void add_data_json(ftp_parser_ext *p_ftp_ctrl_parser, ftp_data_link *p_ftp_data_parser, const struct conn *pcon, double ts, struct ftp_data_info *p_data_info, CSession *p_session);
    void add_id_json(char *p_event_id);
    void ftp_cb_upload_msg(const char *s, size_t s_len);
    void free_sessionn_ftp_parser(ftp_parser_ext *p_ftp_parser, int dir, ftp_parser_info *p_ftp_parser_info);
    static void free_upload_msg(const struct UploadMsg *pum);
    char *ftp_login_new_format(upload_ftp_login_info *p_st_upload_ftp_login_info, size_t *p_s_len);
};

#endif //__FTP_PARSER_H__
