#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "ftp_parser_upload_task_worker.hpp"
#include "ftp_parser.h"
#include "ftp_parser_common.h"
#include "gw_logger.h"
#include "cJSON.h"
#include "simple_json.h"
#include "gw_i_upload.h"
#include "gw_common.h"
#include "utils.h"
#include "get_file_type.h"
#include "file_info_deal.h"
#include "minio_upload.h"
#include "worker_queue.h"
#include "task_worker.h"
#include "session.h"

#include "gw_stats.h"

#define FTP_DATA_UPLOAD_QUEUE "ftp data upload queue"
#define IF_FREE(x) if ( NULL != (x) ) { cJSON_free(x); }

static const char msg_type[] = "ftp";
const char* g_p_event_header = "FTP_EVENT 1.0";
const size_t g_event_header_len = strlen(g_p_event_header);

const char* g_p_s_ip = "S_IP";
const size_t g_s_ip_len = strlen(g_p_s_ip);

const char* g_p_d_ip = "D_IP";
const size_t g_d_ip_len = strlen(g_p_d_ip);

const char* g_p_id = "ID";
const size_t g_id_len = strlen(g_p_id);

const char* g_p_tm = "TM";
const size_t g_tm_len = strlen(g_p_id);

const char* g_p_file = "FILE";
const size_t g_file_len = strlen(g_p_file);

const char* g_p_user = "USER";
const size_t g_user_len = strlen(g_p_user);

const char* g_p_passwd = "PASSWD";
const size_t g_passwd_len = strlen(g_p_passwd);

const char* g_p_login = "LOGIN";
const size_t g_login_len = strlen(g_p_login);

static const char *p_complete_new = "COMPLETE";
static const size_t complete_new_len = strlen(p_complete_new);

static const char *p_incomplete_new = "IN_COMPLETE";
static const size_t incomplete_new_len = strlen(p_incomplete_new);

static const char *p_type_reliable_new = "TYPE_RELIABLE";
static const size_t type_reliable_new_len = strlen(p_type_reliable_new);

static const char *g_p_d_s_ip = "D_S_IP";
static const size_t g_d_s_ip_len = strlen(g_p_d_s_ip);

static const char *g_p_d_s_port = "D_S_PORT";
static const size_t g_d_s_port_len = strlen(g_p_d_s_port);

static const char *g_p_d_d_ip = "D_D_IP";
static const size_t g_d_d_ip_len = strlen(g_p_d_d_ip);

static const char *g_p_d_d_port = "D_D_PORT";
static const size_t g_d_d_port_len = strlen(g_p_d_d_port);

static const char *g_p_d_tm = "D_TM";
static const size_t g_d_tm = strlen(g_p_d_tm);

static const char *g_p_trans_mode = "TRANS_MODE";
static const size_t g_trans_mode = strlen(g_p_trans_mode);

static const char *g_p_conn_mode = "CONN_MODE";
static const size_t g_conn_mode_len = strlen(g_p_conn_mode);

static const char *g_p_status = "STATUS";
static const size_t g_status_len = strlen(g_p_status);

static const char *g_p_file_path = "FILE_PATH";
static const size_t g_file_path_len = strlen(g_p_file_path);

static const char *g_p_file_data = "FILE_DATA";
static const size_t g_file_data_len = strlen(g_p_file_data);

static const char *p_end_new = "$END$";
static const size_t end_new_len = strlen(p_end_new);

static const char *g_p_null_content = "$NULL$";
static const size_t g_null_content_len = strlen(g_p_null_content);

const char *ftp_template="{"
                                "\"login\":%d,"
                                "\"ctrl\":"
                                "{"
                                    "\"user\":\"%s\","
                                    "\"passwd\":\"%s\","
                                    "\"trans_mode\":\"%s\","
                                    "\"conn_mode\":\"%s\","
                                    "\"command\":{\"%s\":%s},"
                                    "\"status\":%d, "
                                    "\"src_ip\":\"%s\","
                                    "\"src_port\":%hu,"
                                    "\"dst_ip\":\"%s\","
                                    "\"dst_port\":%hu,"
                                    "\"ctrl_ts\":%.3f,"
                                    "\"file_path\":\"%s\""
                                "},"
                                "\"data\":{"
                                    "\"file_info\":["
                                        "%s"
                                    "],"
                                    "\"src_ip\":\"%s\","
                                    "\"src_port\":%hu,"
                                    "\"dst_ip\":\"%s\","
                                    "\"dst_port\":%hu,"
                                    "\"data_ts\":%.3f"
                                "},"
                                "\"unique_id\":{\"event_id\":\"%s\"}}";

/* 登录事件上传 */
void CFtpParser::upload_ftp_login_event(const ftp_parser_ext_t *p_ftp_parser, CSession *p_session)
{
    char *s = NULL;
    size_t s_len = 0;
    upload_ftp_login_info_t st_upload_ftp_login_info;
    memset(&st_upload_ftp_login_info, 0, sizeof(upload_ftp_login_info_t));

    st_upload_ftp_login_info.st_ctrl_info.ctrl_tm = p_ftp_parser->pcap_ts;
    memcpy(st_upload_ftp_login_info.st_ctrl_info.p_user, p_ftp_parser->a_user, MIN(strlen(p_ftp_parser->a_user), 255));

    /* 添加密码信息 */
    memcpy(st_upload_ftp_login_info.st_ctrl_info.p_passwd, p_ftp_parser->a_passwd, MIN(strlen(p_ftp_parser->a_passwd), 255));


    add_net_json(&(st_upload_ftp_login_info.st_ctrl_info.st_ctrl_net_info), &(p_ftp_parser->con), p_ftp_parser->reverse);
    add_id_json(st_upload_ftp_login_info.a_event_id);
    st_upload_ftp_login_info.login = 1;

    if (!m_use_new_event_format)
    {
        s = simple_ftp_login_json_encode(&st_upload_ftp_login_info, &s_len);
    }   
    else
    {
        s = ftp_login_new_format(&st_upload_ftp_login_info, &s_len);
    } 

    ftp_cb_upload_msg(s, s_len);

    return;
}

char *CFtpParser::ftp_login_new_format(upload_ftp_login_info_t *p_st_upload_ftp_login_info, size_t *p_s_len)
{
    if (p_st_upload_ftp_login_info == NULL)
    {
        return NULL;
    }

    size_t len = 1000;
    size_t real_len = 0;

    ftp_ctrl_info_t *p_ctrl_info = &(p_st_upload_ftp_login_info->st_ctrl_info);
    size_t user_len = 0;
    size_t passwd_len = 0;
    size_t src_ip_len = 0;
    size_t dst_ip_len = 0;
    size_t event_id_len = 0;

    user_len = strlen(p_ctrl_info->p_user);
    passwd_len = strlen(p_ctrl_info->p_passwd);

    src_ip_len = strlen(p_ctrl_info->st_ctrl_net_info.a_src_ip);
    dst_ip_len = strlen(p_ctrl_info->st_ctrl_net_info.a_dst_ip);

    event_id_len = strlen(p_st_upload_ftp_login_info->a_event_id);

    len += user_len + passwd_len + src_ip_len + dst_ip_len + event_id_len;

    char *buffer = (char *)malloc(len);
    if (NULL == buffer)
    {
        return NULL;
    }

    memcpy(buffer + real_len, g_p_event_header, g_event_header_len);
    real_len += g_event_header_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_s_ip, g_s_ip_len);
    real_len += g_s_ip_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ctrl_info->st_ctrl_net_info.a_src_ip, src_ip_len);
    real_len += src_ip_len;
    buffer[real_len++] = ' ';
    ntos(p_ctrl_info->st_ctrl_net_info.src_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_d_ip, g_d_ip_len);
    real_len += g_d_ip_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ctrl_info->st_ctrl_net_info.a_dst_ip, dst_ip_len);
    real_len += dst_ip_len;
    buffer[real_len++] = ' ';
    ntos(p_ctrl_info->st_ctrl_net_info.dst_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_id, g_id_len);
    real_len += g_id_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_st_upload_ftp_login_info->a_event_id, event_id_len);
    real_len += event_id_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_tm, g_tm_len);
    real_len += g_tm_len;
    buffer[real_len++] = ' ';
    uint64_t ctrl_tm = p_ctrl_info->ctrl_tm * 1000;
    ntos(ctrl_tm, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_file, g_file_len);
    real_len += g_file_len;
    buffer[real_len++] = ' ';
    ntos(0, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_user, g_user_len);
    real_len += g_user_len;
    buffer[real_len++] = ' ';
    ntos(user_len, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ctrl_info->p_user, user_len);
    real_len += user_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_passwd, g_passwd_len);
    real_len += g_passwd_len;
    buffer[real_len++] = ' ';
    ntos(passwd_len, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ctrl_info->p_passwd, passwd_len);
    real_len += passwd_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_login, g_login_len);
    real_len += g_login_len;
    buffer[real_len++] = ' ';
    ntos(1, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len , p_end_new, end_new_len);
    real_len += end_new_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    *p_s_len = real_len;
    return buffer;
}

void CFtpParser::upload_ftp_data_only_ctrl(ftp_parser_ext_t *p_ftp_ctrl_parser, double ts)
{
    upload_ftp_info_t* p_ftp_info = new upload_ftp_info_t();
    upload_ftp_info_t &st_upload_ftp_info = *p_ftp_info;
    memset(&st_upload_ftp_info, 0, sizeof(upload_ftp_info_t));

    ftp_data_link_t data_link = {0};
    add_ctrl_json(p_ftp_ctrl_parser, &(st_upload_ftp_info.st_ctrl_info),nullptr);
    add_data_json(p_ftp_ctrl_parser, &data_link, NULL, ts, &(st_upload_ftp_info.st_data_info),nullptr);
    st_upload_ftp_info.st_data_info.is_incomplete = 1;
    add_id_json(st_upload_ftp_info.a_event_id);
    st_upload_ftp_info.login = 0;

    send_ftp_data(&st_upload_ftp_info);
    return;
}

void CFtpParser::upload_ftp_data(struct StreamData *p_stream_data, const struct conn *pcon, double ts, CSession *p_session)
{
    upload_ftp_info_t* p_ftp_info = new upload_ftp_info_t();
    upload_ftp_info_t &st_upload_ftp_info = *p_ftp_info;
    memset(&st_upload_ftp_info, 0, sizeof(upload_ftp_info_t));

    // struct StreamData *p_ftp_ctrl_streamdata = NULL;
    ftp_parser_ext_t *p_ftp_ctrl_parser = NULL;
    ftp_data_link_t *p_ftp_data_parser = p_stream_data->p_ftp_stream->p_ftp_data_parser;
    // int i_ftp_data_map_index = p_ftp_data_parser->i_ftp_data_map_index;
    p_ftp_ctrl_parser = &p_ftp_data_parser->ctrl_data;
    // m_ftp_data_map[i_ftp_data_map_index].p_stream_data;
    // if (p_ftp_ctrl_streamdata == NULL || p_ftp_ctrl_streamdata->p_ftp_stream == NULL)
    // {
    //     return;
    // }
    // p_ftp_ctrl_parser = p_ftp_ctrl_streamdata->p_ftp_stream->p_ftp_parser;

    add_ctrl_json(p_ftp_ctrl_parser, &(st_upload_ftp_info.st_ctrl_info),p_session);
    add_data_json(p_ftp_ctrl_parser, p_ftp_data_parser, pcon, ts, &(st_upload_ftp_info.st_data_info),p_session);
    add_id_json(st_upload_ftp_info.a_event_id);
    st_upload_ftp_info.login = 0;

    send_ftp_data(&st_upload_ftp_info);
    return;
}

char* CFtpParser::upload_ftp_new_format(upload_ftp_info_t& st_upload_ftp_info, size_t *p_s_len)
{
    upload_ftp_info_t *p_upload_ftp_info = &st_upload_ftp_info;
    size_t len = 1000;
    size_t real_len = 0;
    char *buffer = NULL;

    ftp_ctrl_info_t *p_ftp_ctrl_info = &(p_upload_ftp_info->st_ctrl_info);
    ftp_data_info_t *p_ftp_data_info = &(p_upload_ftp_info->st_data_info);
    int file_num = 0;
    size_t user_len = 0;
    size_t passwd_len = 0;
    size_t file_path_len = 0;
    size_t trandfer_mode_len = 0;
    size_t conn_mode_len = 0;
    size_t command_len = 0;
    size_t command_value_len = 0;
    size_t ctrl_src_ip_len = 0;
    size_t ctrl_dst_ip_len = 0;
    size_t data_src_ip_len = 0;
    size_t data_dst_ip_len = 0;
    size_t file_type_len = 0;
    size_t file_name_len = 0;
    size_t event_id_len = 0;
    size_t sha256_len = 0;
    const char* p_complete = NULL;
    size_t complete_len = 0;
    upload_stats_t result;

    /* ctrl */
    if (p_ftp_ctrl_info->p_user != NULL)
    {
        user_len = strlen(p_ftp_ctrl_info->p_user);
    }
    len += user_len;

    if (p_ftp_ctrl_info->p_passwd != NULL)
    {
        passwd_len = strlen(p_ftp_ctrl_info->p_passwd);
    }
    len += passwd_len;

    if (p_ftp_ctrl_info->file_path != NULL)
    {
        file_path_len = strlen(p_ftp_ctrl_info->file_path);
    }
    len += file_path_len;

    trandfer_mode_len = strlen(p_ftp_ctrl_info->a_transfer_mode);
    len += trandfer_mode_len;
    conn_mode_len = strlen(p_ftp_ctrl_info->a_conn_mode);
    len += conn_mode_len;

    if (p_ftp_ctrl_info->p_command != NULL)
    {
        command_len = strlen(p_ftp_ctrl_info->p_command);
    }
    len += command_len;

    if (p_ftp_ctrl_info->p_command_value != NULL)
    {
        command_value_len = strlen(p_ftp_ctrl_info->p_command_value);
    }
    len += command_value_len;

    ctrl_src_ip_len = strlen(p_ftp_ctrl_info->st_ctrl_net_info.a_src_ip);
    len += ctrl_src_ip_len;
    ctrl_dst_ip_len = strlen(p_ftp_ctrl_info->st_ctrl_net_info.a_dst_ip);
    len += ctrl_dst_ip_len;

    data_src_ip_len = strlen(p_ftp_data_info->st_data_net_info.a_src_ip);
    len += data_src_ip_len;
    data_dst_ip_len = strlen(p_ftp_data_info->st_data_net_info.a_dst_ip);
    len += data_dst_ip_len;

    bstr_t body;
    body = p_ftp_data_info->data_body;
    if (p_ftp_data_info->up_down == 0 || p_ftp_data_info->up_down == 1)
    {
        file_num = 1;
        file_type_len = strlen(p_ftp_data_info->a_file_type);
        file_name_len = strlen(p_ftp_data_info->a_file_name);
        
        FileUpload* p_minio_upload = m_comm->get_minio_upload();
        result = p_minio_upload->upload_file(body.p_value, body.length, NULL);
        if (result.ret != 0) /* 上传失败 */
        {    
            if (body.length > m_u32_conf_ftp_upload_max_size)
            {
                p_complete = p_incomplete_new;
                complete_len = complete_new_len;
            }
            else
            {
                p_complete = p_complete_new;
                complete_len = incomplete_new_len;
            }
            body.length = MIN(body.length, (size_t)m_u32_conf_ftp_upload_max_size);
            len += g_null_content_len;
        }
        else
        {
            sha256_len = result.str_sha256.size(); 
            len += sha256_len;
            body.p_value = NULL;
            body.length = 0;
            p_complete = p_complete_new;
            complete_len = complete_new_len;
            
        }

        len += type_reliable_new_len;
        len += complete_len;
    }
    else
    {
        body.length = MIN(body.length, (size_t)m_u32_conf_ftp_upload_max_size);
    }
    len += body.length;

    event_id_len = strlen(p_upload_ftp_info->a_event_id);
    len += event_id_len;

    buffer = (char*)malloc(len);
    if (buffer == NULL)
    {
        return NULL;
    }

    memcpy(buffer + real_len, g_p_event_header, g_event_header_len);
    real_len += g_event_header_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_s_ip, g_s_ip_len);
    real_len += g_s_ip_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->st_ctrl_net_info.a_src_ip, ctrl_src_ip_len);
    real_len += ctrl_src_ip_len;
    buffer[real_len++] = ' ';
    ntos(p_ftp_ctrl_info->st_ctrl_net_info.src_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_d_ip, g_d_ip_len);
    real_len += g_d_ip_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->st_ctrl_net_info.a_dst_ip, ctrl_dst_ip_len);
    real_len += ctrl_dst_ip_len;
    buffer[real_len++] = ' ';
    ntos(p_ftp_ctrl_info->st_ctrl_net_info.dst_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_id, g_id_len);
    real_len += g_id_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_upload_ftp_info->a_event_id, event_id_len);
    real_len += event_id_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_tm, g_tm_len);
    real_len += g_tm_len;
    buffer[real_len++] = ' ';
    uint64_t ctrl_tm = p_ftp_ctrl_info->ctrl_tm * 1000;
    ntos(ctrl_tm, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_file, g_file_len);
    real_len += g_file_len;
    buffer[real_len++] = ' ';
    ntos(file_num, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';
    if (file_num > 0)
    {
        if (p_ftp_data_info->up_down == 0)
        {
            buffer[real_len++] = 'R';
        }
        else
        {
            buffer[real_len++] = 'W';
        }
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_complete, complete_len);
        real_len += complete_len;
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_type_reliable_new, type_reliable_new_len);
        real_len += type_reliable_new_len;
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_ftp_data_info->a_file_type, file_type_len);
        real_len += file_type_len;
        buffer[real_len++] = ' ';
        ntos(p_ftp_data_info->file_len, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        ntos(file_name_len, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_ftp_data_info->a_file_name, file_name_len);
        real_len += file_name_len;
        buffer[real_len++] = ' ';
        if (sha256_len > 0)
        {
            memcpy(buffer + real_len, result.str_sha256.c_str(), sha256_len);
            real_len += sha256_len;
        }
        else
        {
            memcpy(buffer + real_len, g_p_null_content, g_null_content_len);
            real_len += g_null_content_len;
        }
        buffer[real_len++] = '\r';
        buffer[real_len++] = '\n';

    }

    memcpy(buffer + real_len, g_p_user, g_user_len);
    real_len += g_user_len;
    buffer[real_len++] = ' ';
    ntos(user_len, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->p_user, user_len);
    real_len += user_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_passwd, g_passwd_len);
    real_len += g_passwd_len;
    buffer[real_len++] = ' ';
    ntos(passwd_len, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->p_passwd, passwd_len);
    real_len += passwd_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_login, g_login_len);
    real_len += g_login_len;
    buffer[real_len++] = ' ';
    ntos(0, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_d_s_ip, g_d_s_ip_len);
    real_len += g_d_s_ip_len;
    buffer[real_len++] = ' ';
    if (data_src_ip_len > 0)
    {
        memcpy(buffer + real_len, p_ftp_data_info->st_data_net_info.a_src_ip, data_src_ip_len);
        real_len += data_src_ip_len;
    }
    else
    {
        memcpy(buffer + real_len, g_p_null_content, g_null_content_len);
        real_len += g_null_content_len;
    }
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_d_s_port, g_d_s_port_len);
    real_len += g_d_s_port_len;
    buffer[real_len++] = ' ';
    ntos(p_ftp_data_info->st_data_net_info.src_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';
 
    memcpy(buffer + real_len, g_p_d_d_ip, g_d_d_ip_len);
    real_len += g_d_d_ip_len;
    buffer[real_len++] = ' ';
    if (data_dst_ip_len > 0)
    {
        memcpy(buffer + real_len, p_ftp_data_info->st_data_net_info.a_dst_ip, data_dst_ip_len);
        real_len += data_dst_ip_len;
    }
    else
    {
        memcpy(buffer + real_len, g_p_null_content, g_null_content_len);
        real_len += g_null_content_len;
    }
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_d_d_port, g_d_d_port_len);
    real_len += g_d_d_port_len;
    buffer[real_len++] = ' ';
    ntos(p_ftp_data_info->st_data_net_info.dst_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_d_tm, g_d_tm);
    real_len += g_d_tm;
    buffer[real_len++] = ' ';
    uint64_t data_tm = p_ftp_data_info->data_tm * 1000;
    ntos(data_tm, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_trans_mode, g_trans_mode);
    real_len += g_trans_mode;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->a_transfer_mode, trandfer_mode_len);
    real_len += trandfer_mode_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_conn_mode, g_conn_mode_len);
    real_len += g_conn_mode_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->a_conn_mode, conn_mode_len);
    real_len += conn_mode_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_status, g_status_len);
    real_len += g_status_len;
    buffer[real_len++] = ' ';
    ntos(p_ftp_ctrl_info->status, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_file_path, g_file_path_len);
    real_len += g_file_path_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_ftp_ctrl_info->file_path, file_path_len);
    real_len += file_path_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, g_p_file_data, g_file_data_len);
    real_len += g_file_data_len;
    buffer[real_len++] = ' ';
    ntos(body.length, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, body.p_value, body.length);
    real_len += body.length;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len , p_end_new, end_new_len);
    real_len += end_new_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    *p_s_len = real_len;
    return buffer;
}

char* CFtpParser::upload_ftp_data_encode(upload_ftp_info_t& st_upload_ftp_info, size_t *p_s_len) 
{
    upload_ftp_info_t *p_upload_ftp_info = &st_upload_ftp_info;
    //size_t str_size = 0;
    size_t len = 1000;

    ftp_ctrl_info_t *p_ftp_ctrl_info = &(p_upload_ftp_info->st_ctrl_info);
    ftp_data_info_t *p_ftp_data_info = &(p_upload_ftp_info->st_data_info);

    /* ctrl */
    if (p_ftp_ctrl_info->p_user != NULL)
    {
        len += strlen(p_ftp_ctrl_info->p_user);
    }

    if (p_ftp_ctrl_info->p_passwd != NULL)
    {
        len += strlen(p_ftp_ctrl_info->p_passwd);
    }

    if (p_ftp_ctrl_info->file_path != NULL)
    {
        len += strlen(p_ftp_ctrl_info->file_path);
    }

    len += strlen(p_ftp_ctrl_info->a_transfer_mode);
    len += strlen(p_ftp_ctrl_info->a_conn_mode);

    if (p_ftp_ctrl_info->p_command != NULL)
    {
        len += strlen(p_ftp_ctrl_info->p_command);
    }

    const char* p_json_command_value = cJSON_EscapeString(p_ftp_ctrl_info->p_command_value);
    len += strlen(p_json_command_value);

    len += strlen(p_ftp_ctrl_info->st_ctrl_net_info.a_src_ip);
    len += strlen(p_ftp_ctrl_info->st_ctrl_net_info.a_dst_ip);

    // /* data */
    // char *p_file_type = NULL;
    // char *p_file_name = NULL;
    // char *p_file_warn = NULL;

    // if (strlen(p_ftp_data_info->a_file_type) > 0)
    // {
    //     p_file_type = cJSON_EscapeString(p_ftp_data_info->a_file_type);
    //     len += strlen(p_file_type);
    // }
    // else
    // {
    //     len += 4;
    // }

    // if (strlen(p_ftp_data_info->a_file_name) > 0)
    // {
    //     p_file_name = cJSON_EscapeString(p_ftp_data_info->a_file_name);
    //     len += strlen(p_file_name);
    // }
    // else
    // {
    //     len += 4;
    // }

    // if (strlen(p_ftp_data_info->a_file_warn) > 0)
    // {
    //     p_file_warn = cJSON_EscapeString(p_ftp_data_info->a_file_warn);
    //     len += strlen(p_file_warn);
    // }
    // else
    // {
    //     len += 4;
    // }

    len += strlen(p_ftp_data_info->st_data_net_info.a_src_ip);
    len += strlen(p_ftp_data_info->st_data_net_info.a_dst_ip);

    // if (p_ftp_data_info->data_body.length > 0)
    // {   
    //     size_t escape_str_len = 0;
    //     p_ftp_data_info->data_body.p_value = cJSON_EscapeStringWithBufferSize(p_ftp_data_info->data_body.p_value
    //                                                         , p_ftp_data_info->data_body.length
    //                                                         , &escape_str_len);
    //     len += escape_str_len;
    // }
    // else
    // {
    //     p_ftp_data_info->data_body.p_value = NULL;
    //     len += 4;
    // }
    //FileUpload *upload = m_comm->get_minio_upload();
    const char* const p_file_info_json = file_info_format(p_ftp_data_info->a_file_name, strlen(p_ftp_data_info->a_file_name),
                                                        p_ftp_data_info->a_file_type, strlen(p_ftp_data_info->a_file_type),
                                                        p_ftp_data_info->a_file_warn, strlen(p_ftp_data_info->a_file_warn),
                                                        p_ftp_data_info->data_body.p_value, p_ftp_data_info->data_body.length, 
                                                        p_ftp_data_info->is_incomplete, p_ftp_data_info->up_down, m_upload_obj, m_u32_conf_ftp_upload_max_size);

    len += p_file_info_json ? strlen(p_file_info_json) : 0;
    len += strlen(p_upload_ftp_info->a_event_id);    
    
    char* buffer = (char*)cJSON_malloc(len);
    if (NULL == buffer)
    {
        goto end;
        //return NULL;
    }
    memset(buffer, 0, len);
    
    sprintf (buffer
           , ftp_template
           , p_upload_ftp_info->login
           , or_empty(p_ftp_ctrl_info->p_user)
           , or_empty(p_ftp_ctrl_info->p_passwd)
           , or_empty(p_ftp_ctrl_info->a_transfer_mode)
           , or_empty(p_ftp_ctrl_info->a_conn_mode)
           , or_empty(p_ftp_ctrl_info->p_command)
           , or_empty(p_json_command_value)
           , p_ftp_ctrl_info->status
           , or_null(p_ftp_ctrl_info->st_ctrl_net_info.a_src_ip)
           , p_ftp_ctrl_info->st_ctrl_net_info.src_port
           , or_null(p_ftp_ctrl_info->st_ctrl_net_info.a_dst_ip)
           , p_ftp_ctrl_info->st_ctrl_net_info.dst_port
           , p_ftp_ctrl_info->ctrl_tm
           , p_ftp_ctrl_info->file_path
           , or_empty(p_file_info_json)
           , or_null(p_ftp_data_info->st_data_net_info.a_src_ip)
           , p_ftp_data_info->st_data_net_info.src_port
           , or_null(p_ftp_data_info->st_data_net_info.a_dst_ip)
           , p_ftp_data_info->st_data_net_info.dst_port
           , or_empty(p_upload_ftp_info->a_event_id),
           p_upload_ftp_info->st_data_info.data_tm);

end:
    IF_FREE((void*)p_json_command_value);
    SAFE_FREE((void*)p_file_info_json);
    *p_s_len = strlen(buffer);
    return buffer;
}

void CFtpParser::add_net_json(net_info_t *p_net_info, const struct conn *pcon, bool reverse)
{
    if (NULL == p_net_info || NULL == pcon)
    {
        return;
    }

    if (pcon->client.v == 4)
    {
        if (reverse) 
        {
            strncpy(p_net_info->a_src_ip, int_ntoa(pcon->server.ipv4), COUNTOF(p_net_info->a_src_ip) - 1);
            strncpy(p_net_info->a_dst_ip, int_ntoa(pcon->client.ipv4), COUNTOF(p_net_info->a_dst_ip) - 1);
        }
        else
        {
            strncpy(p_net_info->a_src_ip, int_ntoa(pcon->client.ipv4), COUNTOF(p_net_info->a_src_ip) - 1);
            strncpy(p_net_info->a_dst_ip, int_ntoa(pcon->server.ipv4), COUNTOF(p_net_info->a_dst_ip) - 1);
        }
    }
    else
    {
        if (reverse) 
        {
            get_ip6addr_str((uint32_t*)pcon->server.ipv6, p_net_info->a_src_ip, COUNTOF(p_net_info->a_src_ip));
            get_ip6addr_str((uint32_t*)pcon->client.ipv6, p_net_info->a_dst_ip, COUNTOF(p_net_info->a_dst_ip));
        }
        else
        {
            get_ip6addr_str((uint32_t*)pcon->client.ipv6, p_net_info->a_src_ip, COUNTOF(p_net_info->a_src_ip));
            get_ip6addr_str((uint32_t*)pcon->server.ipv6, p_net_info->a_dst_ip, COUNTOF(p_net_info->a_dst_ip));
        }
    }

    if (reverse) 
    {
        p_net_info->src_port = pcon->server.port;
        p_net_info->dst_port = pcon->client.port;
    }
    else
    {
        p_net_info->src_port = pcon->client.port;
        p_net_info->dst_port = pcon->server.port;
    }

    return;
}


void CFtpParser::add_ctrl_json(ftp_parser_ext_t *p_ftp_parser, ftp_ctrl_info_t *p_ftp_ctrl_info, CSession *p_session)
{
    if (NULL == p_ftp_parser || NULL == p_ftp_ctrl_info)
    {
        return ;
    }

    /* 添加用户名信息 */
    memcpy(p_ftp_ctrl_info->p_user, p_ftp_parser->a_user, MIN(strlen(p_ftp_parser->a_user), 255));

    /* 添加密码信息 */
    memcpy(p_ftp_ctrl_info->p_passwd, p_ftp_parser->a_passwd, MIN(strlen(p_ftp_parser->a_passwd), 255));

    memcpy(p_ftp_ctrl_info->file_path, p_ftp_parser->dir_path, MIN(strlen(p_ftp_parser->dir_path), 4095));

    /* 添加数据传输模式 */
    if (p_ftp_parser->u16_transfer_mode == ASCII_TRANSFER_MODE)
    {
        snprintf (p_ftp_ctrl_info->a_transfer_mode, COUNTOF(p_ftp_ctrl_info->a_transfer_mode) - 1, "%s", "ascii");
    }
    else if (p_ftp_parser->u16_transfer_mode == BINARY_TRANSFER_MODE)
    {
        snprintf (p_ftp_ctrl_info->a_transfer_mode, COUNTOF(p_ftp_ctrl_info->a_transfer_mode) - 1, "%s", "binary");
    }

    /* 添加连接模式(主动模式或被动模式) */
    if (p_ftp_parser->i_data_link_mode == FTP_PASV_MODE)
    {
        snprintf (p_ftp_ctrl_info->a_conn_mode, COUNTOF(p_ftp_ctrl_info->a_conn_mode) - 1, "%s", "pasv");
    }
    else if (p_ftp_parser->i_data_link_mode == FTP_PORT_MODE)
    {
        snprintf (p_ftp_ctrl_info->a_conn_mode, COUNTOF(p_ftp_ctrl_info->a_conn_mode) - 1, "%s", "port");
    }
    else if (p_ftp_parser->i_data_link_mode == FTP_EPSV_MODE)
    {
        snprintf (p_ftp_ctrl_info->a_conn_mode, COUNTOF(p_ftp_ctrl_info->a_conn_mode) - 1, "%s", "epsv");
    }
    else if (p_ftp_parser->i_data_link_mode == FTP_EPRT_MODE)
    {
        snprintf (p_ftp_ctrl_info->a_conn_mode, COUNTOF(p_ftp_ctrl_info->a_conn_mode) - 1, "%s", "eprt");
    }

    /* 添加控制链路的command 命令 */
    p_ftp_ctrl_info->p_command = ftp_method_str((ftp_command)p_ftp_parser->u8_ftp_command);
    memcpy(p_ftp_ctrl_info->p_command_value, p_ftp_parser->a_command_arg, MIN(strlen(p_ftp_parser->a_command_arg), 4095));

    /* 添加响应状态码 */
    p_ftp_ctrl_info->status = p_ftp_parser->u16_rsp_state;

    /* 添加控制链路四元组信息 */
    add_net_json(&(p_ftp_ctrl_info->st_ctrl_net_info), &(p_ftp_parser->con), p_ftp_parser->reverse);

    /* 添加控制链路的时间戳信息 */
    p_ftp_ctrl_info->ctrl_tm = p_ftp_parser->pcap_ts;

    return;
}

void CFtpParser::add_data_json(ftp_parser_ext_t *p_ftp_ctrl_parser, ftp_data_link_t *p_ftp_data_parser, const struct conn *pcon, double ts, ftp_data_info_t *p_data_info, CSession *p_session)
{
    /* 如果是上传下载文件，在这里需解析文件类型，文件名以及文件长度等信息 TBD */
    uint8_t u8_ftp_command = p_ftp_ctrl_parser->u8_ftp_command;
    if (u8_ftp_command == FTP_STOR || u8_ftp_command == FTP_RETR)
    {
        if (u8_ftp_command == FTP_STOR)
        {
            p_data_info->up_down = RW_FLAG_WRITE;
        }
        else
        {
            p_data_info->up_down = RW_FLAG_READ;
        }
       
        char a_file_type[128] = {0};
        char a_file_name[128] = {0};

        get_file_info_by_name((const char*)p_ftp_data_parser->p_data, p_ftp_data_parser->u64_data_len, p_ftp_ctrl_parser->a_command_arg, a_file_name, 128, a_file_type, 128);
        
        snprintf (p_data_info->a_file_type, COUNTOF(p_data_info->a_file_type) - 1, "%s", a_file_type);
        snprintf (p_data_info->a_file_name, COUNTOF(p_data_info->a_file_name) - 1, "%s", a_file_name);
        p_data_info->file_len = p_ftp_data_parser->u64_data_len;
        if (p_ftp_data_parser->u64_data_len >= m_u32_conf_ftp_upload_max_size)
        {
            memcpy(p_data_info->a_file_warn, "large", strlen("large"));
        }
    }
    else
    {
        p_data_info->up_down = RW_FLAG_OTHER;
    }

    /* 添加数据链路四元组信息 */
    add_net_json(&(p_data_info->st_data_net_info), pcon, 0);

    /* 添加数据链路时间戳信息 */
    p_data_info->data_tm = ts;

    /* 添加body信息 */
    char* p_data = (char*)malloc(p_ftp_data_parser->u64_data_len + 1);
    memset(p_data, 0, p_ftp_data_parser->u64_data_len + 1);
    memcpy(p_data, p_ftp_data_parser->p_data, p_ftp_data_parser->u64_data_len);
    p_data_info->data_body.p_value = p_data;
    p_data_info->data_body.length = p_ftp_data_parser->u64_data_len;

    return;
}

void CFtpParser::add_id_json(char *p_event_id)
{
    char a_unique_code[64] = {0};
    uint64_t u64_time_val = 0;
    get_ms_timeval(&u64_time_val);

    if (m_u64_ftp_upload_ms == 0)
    {
        m_u64_ftp_upload_ms = u64_time_val;
        m_u32_ftp_upload_index = 1;
    }
    else
    {
        if (u64_time_val == m_u64_ftp_upload_ms)
        {
            m_u32_ftp_upload_index ++;
        }
        else
        {
            m_u64_ftp_upload_ms = u64_time_val;
            m_u32_ftp_upload_index = 1;
        }
    }

    /* 获取唯一标识ID */
    get_unique_event_id(m_str_gw_ip.c_str(), m_u64_ftp_upload_ms, m_u32_ftp_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

    /* 将unique_code进行base64编码 */
    base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

    return;
}

void CFtpParser::ftp_cb_upload_msg(const char *s, size_t s_len)
{
    //size_t length = strlen(s);
    size_t length = s_len;

    if (unlikely(m_upload == NULL))
    {
        GWLOG_INFO(m_comm, "ftp parser, upload null(%s)\n", m_conf_upload_name.c_str());
        SAFE_FREE((void*)s);
        return;
    }

    UploadMsg *pum = new UploadMsg;
    memset(pum, 0, sizeof(UploadMsg));

    pum->cb = sizeof(UploadMsg);
    pum->destroy_func = free_upload_msg;
    pum->parser = this;
    pum->length = length;
    pum->s = s;
    pum->msgtype = msg_type;
    // pum->priproty = m_conf_kfk_priority;
    pum->mem_size = sizeof(UploadMsg) + pum->length;

    __sync_fetch_and_add(&m_stats_ftp.u64_ftp_upload_event_cnt, 1);
    m_upload->put_msg(pum);
}

void CFtpParser::free_upload_msg(const struct UploadMsg *pum)
{
  ASSERT(pum != NULL);

  delete pum;
}

void CFtpParser::send_ftp_data(upload_ftp_info_t* p) 
{
    p->mem_size = sizeof(upload_ftp_info_t);
    if( !m_upload_data_wq->queue_put_data(p, p->mem_size) ) 
    {
        free_ftp_upload_data(p);
    }
}

CWorkerQueue *CFtpParser::new_wq_upload_msg() 
{
    m_upload_data_wq = m_comm->create_worker_queue();
    if (m_upload_data_wq == NULL)
    {
        return NULL;
    }
    
    CTaskWorkerUploadMsg *ptw = new CTaskWorkerUploadMsg();
    ptw->set_wq(m_upload_data_wq);
    ptw->set_parser(this);

    m_upload_data_wk = ptw;

    m_upload_data_wq->set_gw_common(m_comm);
    m_upload_data_wq->set_watchdog(m_comm->get_watchdog());
    m_upload_data_wq->set_task_worker(ptw);

    m_upload_data_wq->set_queue_num_and_bytes(m_conf_ftp_upload_queue_max_num, m_conf_ftp_upload_queue_memory_max_size_bytes);
    m_upload_data_wq->set_queue_name(FTP_DATA_UPLOAD_QUEUE);
    m_upload_data_wq->init();
    m_upload_data_wq->create_queue();

    m_upload_data_wq->adjust_worker_thread_num(m_conf_ftp_upload_thread_num);

    m_comm->get_gw_stats()->set_task(m_upload_data_wq->get_queue_name(), m_upload_data_wq, 50);
    m_comm->get_gw_stats()->set_mem_stat(m_upload_data_wq->get_queue_name(), &m_upload_data_wq->get_queue_mem_size(), &m_upload_data_wq->get_queue_max_mem_size());

    return m_upload_data_wq;
}

int CFtpParser::worker_rutine_ftp_upload_data_inner(upload_ftp_info_t* p) 
{
    size_t s_len = 0;
    char *s = NULL;
    if (! m_use_new_event_format)
    {
        s = upload_ftp_data_encode(*p, &s_len);
    }
    else
    {
        s = upload_ftp_new_format(*p, &s_len);
    }
    ftp_cb_upload_msg(s, s_len);
    return 0;
}
void CFtpParser::free_ftp_upload_data(upload_ftp_info_t* p) 
{
    SAFE_FREE((void*)p->st_data_info.data_body.p_value);
    delete p;
}