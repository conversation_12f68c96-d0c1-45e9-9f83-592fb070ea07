
#include "ftp_parser_upload_task_worker.hpp"
#include "ftp_parser.h"

int CTaskWorkerUploadMsg::deal_data(const TaskWorkerData* _p) 
{
  upload_ftp_info_t *p = (upload_ftp_info_t*)_p;
  return get_parser()->worker_rutine_ftp_upload_data_inner(p);
}

void CTaskWorkerUploadMsg::free_data(const TaskWorkerData* _p) 
{
  upload_ftp_info_t *p = (upload_ftp_info_t*)_p;
  get_parser()->free_ftp_upload_data(p);
}
