
ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif

MKFILE_PATH :=$(abspath $(lastword $(MAKEFILE_LIST)))
MKFILE_DIR :=$(patsubst %/, %, $(dir $(MKFILE_PATH)))
MKFILE_DIR_STRIP :=$(strip $(MKFILE_DIR))
ROOT_DIR :=$(MKFILE_DIR_STRIP)/../..

ifeq ("$(BUILD_SCHEME)", "UnitTest")
CFLAGS          =  -fvisibility=default
CFLAGS += -DNDEBUG
else ifeq ("$(BUILD_SCHEME)", "CiUnitTest")
CFLAGS          =  -fvisibility=default
CFLAGS += -DNDEBUG
else
CFLAGS          =  -fvisibility=hidden
endif

CFLAGS         += -g  -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././utils/ -I../.././utils/file_type/ -I../.././core/l4/ -I../.././core/

ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS         += -I/home/<USER>/3rd/libpcap-1.9.1/
endif

LDFLAGS         = -shared

ifeq ("$(BUILD_ARCH)", "x86")
LDFLAGS        += -lstdc++ -lz -lpthread -L$(ROOT_DIR)/utils/file_type/ -lfile_type

CFLAGS         += -I/usr/libiconv/include
LDFLAGS        += -L/usr/libiconv/lib/
else ifeq ("$(BUILD_ARCH)", "ARM")
LDFLAGS        += -lstdc++ -L/home/<USER>/3rd/zlib/lib/ -lz -lpthread -L$(ROOT_DIR)/utils/file_type/ -lfile_type

CFLAGS         += -I/home/<USER>/3rd/iconv/include/
LDFLAGS        += -L/home/<USER>/3rd/iconv/lib/
endif


include ../../flags.make

O_FILES = ftp_parser.o proto_ftp_parser.o ftp_parser_inner.o
O_FILES += ftp_parser_parser_msg.o
O_FILES += ftp_parser_deal_probe.o
O_FILES += ftp_parser_deal_parser.o

O_FILES += module_mgt_ftp_parser.o
O_FILES += cJSON.o cJSON_Utils.o pp_hash.o utils.o file_info_deal.o ftp_parser_upload_task_worker.o


.PHONY: all clean


all: ftp_parser.so

%.o:%.cpp
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON.o: ../.././utils/cjson/cJSON.c ../.././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON_Utils.o: ../.././utils/cjson/cJSON_Utils.c ../.././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

file_info_deal.o: ../../core/file_info_deal.cpp ../../include/file_info_deal.h
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

# parser_gene_file.o: ../../utils/file_type/parser_gene_file.c ../../utils/file_type/parser_gene_file.h
# 	$(CC) -c $(CFLAGS)	$(LIBS_CFLAGS) $<

# get_file_type.o: ../.././utils/file_type/get_file_type.c ../.././utils/file_type/get_file_type.h
# 	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

pp_hash.o: ../../core/l4/pp_hash.c ../../core/l4/pp_hash.h
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

utils.o: ../../core/utils.c ../../include/utils.h
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<
ftp_parser.so: $(O_FILES)
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)

clean:
	rm -f *.o *~ ftp_parser.so
