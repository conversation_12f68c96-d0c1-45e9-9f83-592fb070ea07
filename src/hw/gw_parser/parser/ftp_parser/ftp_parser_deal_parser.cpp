#include <string.h>
#include <stdlib.h>
#include <fstream>

#include "ftp_parser.h"
#include "utils.h"
#include "ftp_parser_common.h"
#include "proto_ftp_parser.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"
#include "pp.h"

/* 判断控制链路还是数据链路 */
int CFtpParser::iden_ftp_link_type(CSessionMgt *psm, const struct conn *pcon, CSession *p_session)
{
    //CSession *p_session = NULL;
    StreamData *psd = NULL;
    //p_session = psm->get_session(pcon);
    ASSERT(p_session != NULL);
    psd = p_session->get_stream_data_from_type(m_ftp_type);
    ASSERT(psd != NULL);

    return psd->p_ftp_stream->ftp_link_type;
}
/**
 * 在接收数据时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CFtpParser::parse(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    if (!m_conf_parser_enable)
    {
        p_session->tcp_drop_data(this);
        return 0;
    }

    enum ftp_parser_type dir = FTP_BOTH;
    StreamData* p_tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
    if (p_tcp_data == NULL) {
        return -1;
    }

    tcp_stream* a_tcp = p_tcp_data->a_tcp;
    if (a_tcp == NULL) {
        return -1;
    }

    if(a_tcp->reverse == 0)
    {
        dir = static_cast<ftp_parser_type>(a_app->dir);
    }
    else if(a_tcp->reverse == 1)
    {
        dir = static_cast<ftp_parser_type>((!a_app->dir)&0x01);
    }

    /* 判断控制链路还是数据链路 */
    int i_ret = 0;
    int ftp_link_type = 0;
    ftp_link_type = iden_ftp_link_type(psm, pcon, p_session);

    if (ftp_link_type == FTP_CTRL_LINK)
    {
        i_ret = parser_ctrl(dir, psm, a_app, pcon, p_session);
    }
    else if (ftp_link_type == FTP_DATA_LINK)
    {
        i_ret = parser_data(psm, a_app, pcon, p_session);
    }
    else
    {
        return -1;
    }

    return i_ret;
}

int CFtpParser::parse_clear(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    return 0;
}



int CFtpParser::parser_ctrl(ftp_parser_type dir, CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    int i_ret = 0;
    ftp_parser_ext_t *p_ftp_parser = NULL;
    size_t ftp_offset = 0;
    parser_header_body_params_t parser_header_body_params[1] = {0};

    parser_header_body_params->dir = dir;
    parser_header_body_params->psm = psm;
    parser_header_body_params->a_app = a_app;
    parser_header_body_params->pcon = pcon;

    do
    {
        //CSession *p_session = NULL;
        const char *data = NULL;
        int data_len = 0;
        int offset_out = 0;
        StreamData *psd = NULL;

        //p_session = psm->get_session(pcon);
        if (unlikely(0))
        {
            // TOOD 模似保留连接会话上的数据
            p_session->discard(this, a_app->dir, 0);
            return 0;
        }

        data = p_session->get_data(this, a_app->dir, &data_len, &offset_out);
        if (data == NULL)
        {
            p_session->discard(this, a_app->dir, ftp_offset);
        }
        parser_header_body_params->data = data + ftp_offset;
        parser_header_body_params->data_len = data_len - ftp_offset;
        if (parser_header_body_params->data_len <= 0)
        {
            //没有新数据
            break;
        }

        get_parser_from_session(p_session, dir, psd, (void**)&p_ftp_parser);
        if (p_ftp_parser == NULL)
        {
            GWLOG_DEBUG(m_comm, "new parser error\n");
            p_session->discard(this, a_app->dir, ftp_offset);
            return 1;
        }

        parser_header_body_params->p_session = p_session;
        parser_header_body_params->psd = psd;
        //parser_header_body_params->p_parser = p_parser;

        p_ftp_parser->reverse = p_session->get_reverse();

        i_ret = ftp_parser_header_body(parser_header_body_params, p_ftp_parser, ftp_offset);
        if (i_ret == FTP_CB_CF_BREAK)
        {
            break;
        }
        else if (i_ret == FTP_CB_CF_CONTINUE)
        {
            ftp_offset = parser_header_body_params->parsed;
            continue;
        }

        /* FTP解析完成 */
        ftp_cb_parser_merge(dir, parser_header_body_params, p_ftp_parser,p_session);

        ftp_offset = parser_header_body_params->parsed;
        p_session->discard(this, a_app->dir, ftp_offset);

    }while(1);

    return 0;
}

int CFtpParser::parser_data(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    ftp_data_link_t *p_ftp_data_link = NULL;
    //CSession *p_session = NULL;
    size_t ftp_offset = 0;
    enum ftp_parser_type dir = FTP_REQUEST;
    StreamData *psd = NULL;
    StreamData *psd_ctrl = NULL;
    int data_len = 0;
    int offset_out = 0;
    const char *data = NULL;

    //p_session = psm->get_session(pcon);


    data = p_session->get_data(this, a_app->dir, &data_len, &offset_out);
    if (data == NULL)
    {
        p_session->discard(this, a_app->dir, ftp_offset);
    }

    get_parser_from_session(p_session, dir, psd, (void**)&p_ftp_data_link);
    if (p_ftp_data_link == NULL)
    {
        GWLOG_DEBUG(m_comm, "new parser error\n");
        p_session->discard(this, a_app->dir, ftp_offset);
        return 1;
    }

    psd_ctrl = m_ftp_data_map[p_ftp_data_link->i_ftp_data_map_index].p_stream_data;
    if (psd_ctrl == NULL || psd_ctrl->p_ftp_stream == NULL || psd_ctrl->p_ftp_stream->p_ftp_parser == NULL)
    {
        p_session->tcp_drop_data(this);
        return 0;
    }

    /* 连接数据链路的模式。即主控模式或被动模式 */
    int i_data_link_mode = psd_ctrl->p_ftp_stream->p_ftp_parser->i_data_link_mode;

    if (a_app->dir == STREAM_REQ)
    {
        if (i_data_link_mode == FTP_PASV_MODE)
        {
            dir = FTP_REQUEST;
            if (p_ftp_data_link->i_dir == FTP_REQUEST)
                p_session->set_reverse(true);
        }
        else
        {
            dir = FTP_RESPONSE;
        }
    }
    else
    {
        if (i_data_link_mode == FTP_PASV_MODE)
        {
            dir = FTP_RESPONSE;
        }
        else
        {
            dir = FTP_REQUEST;
        }
    }

    p_ftp_data_link->i_dir = (int)dir;

    p_ftp_data_link->p_data = data;
    p_ftp_data_link->u64_data_len = data_len;
    if (p_ftp_data_link->u64_data_len > FTP_RECV_MAX_BODY_LEN)
    {
        parse_on_close(psm, a_app, pcon, p_session);
        p_session->tcp_drop_data(this);
    }
    else
    {
        p_session->discard(this,a_app->dir, ftp_offset);
    }
    return 0;
}

/**
 * 在连接关闭时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CFtpParser::parse_on_close(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    if (!m_conf_parser_enable)
    {
        p_session->tcp_drop_data(this);
        return 0;
    }
    int i_ret = 0;
    int ftp_link_type = 0;
    ftp_link_type = iden_ftp_link_type(psm, pcon, p_session);

    if (ftp_link_type == FTP_CTRL_LINK)
    {
        i_ret = parser_ctrl_close(psm, a_app, pcon, p_session);
    }
    else if (ftp_link_type == FTP_DATA_LINK)
    {
        i_ret = parser_data_close(psm, a_app, pcon, p_session);
    }
    else
    {
        return -1;
    }
    /*
    // ssl_cnt,http_cnt,ftp_cnt,total_cnt
    std::ifstream ifs("/opt/stats/proportion.file");
    std::string buf = "";
    if(ifs.is_open())
    {
        getline(ifs,buf);
    }
    ifs.close();
    std::ofstream ofs("/opt/stats/proportion.file");
	if(!ofs.is_open())
	{
		GWLOG_INFO(m_comm,"open proportion file fail!");
	}
	if(buf == "")
	{
		buf = "0,0,0";
	}
	unsigned long long ssl_cnt = 0,ftp_cnt = 0,http_cnt = 0;

	sscanf(buf.c_str(),"%llu,%llu,%llu",&ssl_cnt,&http_cnt,&ftp_cnt);

	char result[128];
	sprintf(result,"%llu,%llu,%llu",ssl_cnt,http_cnt,ftp_bytes);

	ofs << result;
    ofs.close();
    */

    return i_ret;
}

/**
 * 在连接重置时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CFtpParser::parse_on_reset(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    return parse_on_close(psm, a_app, pcon, p_session);
}

int CFtpParser::parser_ctrl_close(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    //CSession *p_session = NULL;
    StreamData *psd = NULL;

    //p_session = psm->get_session(pcon);
    if (NULL == p_session)
    {
        return 0;
    }

    psd = p_session->get_stream_data_from_type(m_ftp_type);
    if (NULL == psd)
    {
        return 0;
    }

    check_ftp_data_map(psd->p_ftp_stream->p_ftp_parser);

    return 0;
}

int CFtpParser::parser_data_close(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    //CSession *p_session = NULL;
    StreamData *psd = NULL;

    //p_session = psm->get_session(pcon);
    if (NULL == p_session)
    {
        return 0;
    }

    psd = p_session->get_stream_data_from_type(m_ftp_type);
    if (NULL == psd)
    {
        return 0;
    }
    double ts = p_session->get_ts();
    __sync_fetch_and_add(&ftp_bytes,psd->p_ftp_stream->p_ftp_data_parser->u64_data_len);

    /* 上传数据 */
    upload_ftp_data(psd, pcon, ts, p_session);

    release_ftp_data_map(psd);

    return 0;
}

void CFtpParser::get_parser_from_session(CSession *p_session, enum ftp_parser_type dir, StreamData *&psd_out, void** pp_ftp_parser)
{
    list_session_ftp_parser_t *p_lsp = nullptr;
    StreamData *psd = NULL;
    ftp_stream_t *p_fs = NULL;

    psd = get_stream_data_from_session(p_session, dir);
    if (psd == NULL || psd->p_ftp_stream == NULL)
    {
        return ;
    }

    p_fs = psd->p_ftp_stream;

    if (p_fs->ftp_link_type == FTP_CTRL_LINK)
    {
        if (p_fs->p_ftp_parser == NULL)
        {
            p_fs->p_ftp_parser = (ftp_parser_ext_t *)malloc(sizeof(ftp_parser_ext_t));
            if (unlikely(p_fs->p_ftp_parser == NULL))
            {
                return;
            }
            init_ftp_parser_ext(p_fs->p_ftp_parser);
            memcpy(&(p_fs->p_ftp_parser->con), p_session->get_conn(), sizeof(struct conn));
        }

        //*pp_ftp_parser = (void *)p_fs->p_ftp_parser;

        ftp_parser_ext_t *p_ftp_parser = p_fs->p_ftp_parser;

        if (dir == FTP_REQUEST)
        {
            p_lsp = p_ftp_parser->p_req_parser;
        }
        else
        {
            p_lsp = p_ftp_parser->p_rsp_parser;
        }

        if (p_lsp != NULL && p_lsp->st_ftp_parser_info.i_parser_complete == 0)
        {
            *pp_ftp_parser = (void *)p_ftp_parser;
        }
        else
        {
            p_lsp = new list_session_ftp_parser_t();
            init_ftp_parser_innfo(&(p_lsp->st_ftp_parser_info));
            p_lsp->next = nullptr;
            p_lsp->pre = nullptr;
            if (dir == FTP_REQUEST)
            {
                p_lsp->next = p_ftp_parser->p_req_parser;
                p_ftp_parser->p_req_parser = p_lsp;

                if (p_ftp_parser->p_req_parser_last == nullptr)
                {
                    p_ftp_parser->p_req_parser_last = p_lsp;
                }
            }
            else
            {
                p_lsp->next = p_ftp_parser->p_rsp_parser;
                p_ftp_parser->p_rsp_parser = p_lsp;
                if (p_ftp_parser->p_rsp_parser_last == nullptr)
                {
                    p_ftp_parser->p_rsp_parser_last = p_lsp;
                }
            }

            if (p_lsp->next)
            {
                p_lsp->next->pre = p_lsp;
            }
            *pp_ftp_parser = (void *)p_ftp_parser;
        }
    }
    else if (p_fs->ftp_link_type == FTP_DATA_LINK)
    {
        *pp_ftp_parser = (void*)p_fs->p_ftp_data_parser;
    }
    else
    {
        return;
    }

    psd_out = psd;

    return;
}

int CFtpParser::ftp_parser_header_body(parser_header_body_params_t *p_param, ftp_parser_ext_t *p_ftp_parser, size_t ftp_offset)
{
    int i_ret = 0;
    int more_line = 0;      /* ftp响应会有多行 */
    CSession *p_session = p_param->p_session;
    /* 查找 '/r/n' */
    const char *p = NULL;
    p = my_strstr(p_param->data, p_param->data_len, "\r\n", strlen("\r\n"));
    if (p == NULL)
    {
        if (p_param->data_len >= FTP_HEADER_MAX_LENGTH)
        {
            return FTP_CB_CF_BREAK;
        }

        p_session->discard(this, p_param->dir, ftp_offset); // !CR FTPS是否支持？
        return FTP_CB_CF_BREAK;
    }

    ftp_parser_info_t *p_parser_info = NULL;
    if (p_param->dir == FTP_RESPONSE)
    {
        p_parser_info = &(p_ftp_parser->p_rsp_parser->st_ftp_parser_info);
        if (*(p_param->data + 3) == '-' || *(p_param->data) == ' ')
        {
            more_line = 1;
        }
    }
    else
    {
        p_parser_info = &(p_ftp_parser->p_req_parser->st_ftp_parser_info);
    }

    p_parser_info->u32_ftp_header_len = p - p_param->data + strlen("\r\n");
    if (more_line)
    {
        p_param->parsed += p_parser_info->u32_ftp_header_len;
        return FTP_CB_CF_CONTINUE;
    }
    i_ret = tcp_ftp_parser(p_parser_info, p_param->dir, p_param->data, p_parser_info->u32_ftp_header_len);
    if (i_ret < 0)
    {
        __sync_fetch_and_add(&m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_cnt, 1);
        __sync_fetch_and_add(&m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_fail, 1);
        switch (p_param->dir)
        {
        case FTP_RESPONSE:
            __sync_fetch_and_add(&m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_cnt, 1);
            __sync_fetch_and_add(&m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_fail, 1);
            break;
        case FTP_REQUEST:
            __sync_fetch_and_add(&m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_cnt, 1);
            __sync_fetch_and_add(&m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_fail, 1);
            break;
        default:
            break;
        }
        /* 解析失败 */
        //del_session(p_param->psp, p_param->pcon);
        p_session->tcp_drop_data(this);
        //p_param->psm->del_session(p_param->pcon);
        return FTP_CB_CF_BREAK;
    }
    else
    {
        __sync_fetch_and_add(&m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_cnt, 1);
        __sync_fetch_and_add(&m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_succ, 1);
        switch (p_param->dir)
        {
        case FTP_RESPONSE:
            __sync_fetch_and_add(&m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_cnt, 1);
            __sync_fetch_and_add(&m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_succ, 1);
            break;
        case FTP_REQUEST:
            __sync_fetch_and_add(&m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_cnt, 1);
            __sync_fetch_and_add(&m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_succ, 1);
            break;
        default:
            break;

        }
    }

    p_param->parsed += (size_t)i_ret;
    __sync_fetch_and_add(&m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_bytes,i_ret);
    __sync_fetch_and_add(&ftp_bytes,i_ret);

    return FTP_CB_CF_NONE;
}

void CFtpParser::free_sessionn_ftp_parser(ftp_parser_ext *p_ftp_parser, int dir, ftp_parser_info_t *p_ftp_parser_info)
{
    list_session_ftp_parser_t *curr = NULL;
    list_session_ftp_parser_t *pre = NULL;
    list_session_ftp_parser_t **last = NULL;
    list_session_ftp_parser_t **root = NULL;

    if (p_ftp_parser == NULL)
    {
        return;
    }

    if (dir == STREAM_REQ)
    {
        last = &(p_ftp_parser->p_req_parser_last);
        root = &(p_ftp_parser->p_req_parser);
    }
    else
    {
        last = &(p_ftp_parser->p_rsp_parser_last);
        root = &(p_ftp_parser->p_rsp_parser);
    }

    if (*root == NULL)
    {
        return;
    }

    curr = *root;
    pre = *root;
    while (curr != NULL)
    {
        if (p_ftp_parser_info == &(curr->st_ftp_parser_info))
        {
            break;
        }

        pre = curr;
        curr = curr->next;
    }

    if (curr == NULL)
    {
        return ;
    }

    if (*root == curr)
    {
        *root = curr->next;
    }
    else
    {
        pre->next = curr->next;
    }

    if (curr->next)
    {
        curr->next->pre = curr->pre;
    }

    if (curr->pre)
    {
        curr->pre->next = curr->next;
    }

    if (*root == NULL)
    {
        *last = NULL;
    }
    else if (curr->next == NULL)
    {
        *last = pre;
    }

    delete curr;
    curr = NULL;
}

void CFtpParser::ftp_cb_parser_merge(enum ftp_parser_type dir,  parser_header_body_params_t *p_param, ftp_parser_ext *p_ftp_parser, CSession *p_session)
{
    if (dir == FTP_RESPONSE)
    {
        ftp_cb_parser_response(p_param, p_ftp_parser,p_session);
    }
    else
    {
        ftp_cb_parser_request(p_param, p_ftp_parser,p_session);
    }

    return;
}

void CFtpParser::ftp_cb_parser_response(parser_header_body_params_t *p_param, ftp_parser_ext *p_ftp_parser, CSession *p_session)
{
    ftp_parser_info_t *p_ftp_rsp_parser_info = &(p_ftp_parser->p_rsp_parser->st_ftp_parser_info);
    /* 判断是否是FTP服务器准备好的响应码或数据传输成功的响应码(不做任何处理) */
    if (p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state == FTP_SERVER_READY_STATUS
     || p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state == FTP_CLOSE_DATA_CONN_STATUS)
    // if (p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state - 200 < 100)
    {
        //p_ftp_parser->st_rsp_ftp_parser_info.i_parser_complete = 0;
        if (p_ftp_parser->match_data != 1) {
            parser_ftp_args_2yx(p_ftp_parser, p_param, p_ftp_rsp_parser_info);
        }
        p_ftp_parser->match_data = 0;
        free_sessionn_ftp_parser(p_ftp_parser, STREAM_RSP, p_ftp_rsp_parser_info);
        return;
    }

    if (p_ftp_parser->p_req_parser_last)
    {
        ftp_parser_info_t *p_ftp_req_parser_info = &(p_ftp_parser->p_req_parser_last->st_ftp_parser_info);
        /* 判断request是否解析完成 */
        if (p_ftp_req_parser_info->i_parser_complete == 1)
        {
            __sync_fetch_and_add(&m_stats_ftp.u64_ftp_ctrl_match_cnt, 1);
            parser_ftp_args(p_ftp_parser, p_param, p_ftp_rsp_parser_info, p_ftp_req_parser_info);
            free_sessionn_ftp_parser(p_ftp_parser, STREAM_RSP, p_ftp_rsp_parser_info);
            free_sessionn_ftp_parser(p_ftp_parser, STREAM_REQ, p_ftp_req_parser_info);
        }
    }

    return;
}

void CFtpParser::ftp_cb_parser_request(parser_header_body_params_t *p_param, ftp_parser_ext_t *p_ftp_parser, CSession *p_session)
{
    ftp_parser_info_t *p_ftp_req_parser_info = &(p_ftp_parser->p_req_parser->st_ftp_parser_info);

    if (p_ftp_parser->p_rsp_parser_last)
    {
        ftp_parser_info_t *p_ftp_rsp_parser_info = &(p_ftp_parser->p_rsp_parser_last->st_ftp_parser_info);
        /* 判断response是否解析完成 */
        if (p_ftp_rsp_parser_info->i_parser_complete == 1)
        {
            __sync_fetch_and_add(&m_stats_ftp.u64_ftp_ctrl_match_cnt, 1);
            parser_ftp_args(p_ftp_parser, p_param, p_ftp_rsp_parser_info, p_ftp_req_parser_info);
        }
    }

    return;
}
void CFtpParser::parser_ftp_args_2yx(ftp_parser_ext_t *p_ftp_parser, parser_header_body_params_t *p_param, ftp_parser_info_t *p_ftp_rsp_parser_info)
{
    uint8_t u8_command_type = p_ftp_parser->u8_ftp_command;
    // uint16_t u16_rsp_cose = p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state;
    int index = p_ftp_parser->i_ftp_data_map_index;
    if (-1 == index || m_ftp_data_map[index].i_state == 0)
    {
        return;
    }
    // check_ftp_data_map(p_ftp_parser);
    switch (u8_command_type)
    {
    case FTP_STOR:
    case FTP_LIST:
    case FTP_RETR:
        // upload_ftp_data_only_ctrl(p_ftp_parser, p_ftp_parser->pcap_ts);
        break;

    default:
        break;
    }
}

void CFtpParser::parser_ftp_args(ftp_parser_ext_t *p_ftp_parser, parser_header_body_params_t *p_param, ftp_parser_info_t *p_ftp_rsp_parser_info, ftp_parser_info_t *p_ftp_req_parser_info)
{
    /* 目前至关注 "USER", "PASS", "PORT", "PASV", "TYPE", "AUTH" "PWD" "CMD" */
    //print_ftp_parser(p_ftp_parser);
    const struct conn *pcon = NULL;
    CSession *p_session = p_param->p_session;
    uint8_t u8_command_type = p_ftp_req_parser_info->st_ftp_parser.u8_ftp_command;
    switch (u8_command_type)
    {
    case FTP_USER:
        memcpy(p_ftp_parser->a_user, p_ftp_req_parser_info->st_ftp_parser.a_command_arg, COMMAND_ARGS_BUF_LEN);
        break;
    case FTP_PASS:
        memcpy(p_ftp_parser->a_passwd, p_ftp_req_parser_info->st_ftp_parser.a_command_arg, COMMAND_ARGS_BUF_LEN);
        break;
    case FTP_PASV:
        check_ftp_data_map(p_ftp_parser);
        get_data_link_info(p_ftp_parser, p_ftp_req_parser_info, p_ftp_rsp_parser_info->st_ftp_parser.a_rsp_arg, FTP_PASV_MODE, 0);
        break;
    case FTP_PORT:
        check_ftp_data_map(p_ftp_parser);
        get_data_link_info(p_ftp_parser, p_ftp_req_parser_info, p_ftp_req_parser_info->st_ftp_parser.a_command_arg, FTP_PORT_MODE, 0);
        break;
    case FTP_EPSV:
        check_ftp_data_map(p_ftp_parser);
        pcon = p_session->get_conn();
        get_data_link_info(p_ftp_parser, p_ftp_req_parser_info, p_ftp_rsp_parser_info->st_ftp_parser.a_rsp_arg, FTP_EPSV_MODE, pcon);
        break;
    case FTP_EPRT:
        check_ftp_data_map(p_ftp_parser);
        get_data_link_info(p_ftp_parser,  p_ftp_req_parser_info, p_ftp_req_parser_info->st_ftp_parser.a_command_arg, FTP_EPRT_MODE, 0);
        break;
    case FTP_TYPE:
        get_transfer_mode(p_ftp_parser, p_ftp_req_parser_info);
        break;
    case FTP_AUTH:

        if (p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state == 530)
        {
            break;
        }
        /* 若参数是"TLS",则不再接受数据(暂时不支持FTPS) */
        if (strncmp(p_ftp_req_parser_info->st_ftp_parser.a_command_arg, "TLS", 3) == 0
         || strncmp(p_ftp_req_parser_info->st_ftp_parser.a_command_arg, "SSL", 3) == 0)
        {
            p_session->tcp_drop_data(this);
        }

        break;
    case FTP_PWD:
        if (p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state != 257)
        {
            break;
        }

        p_ftp_parser->op_pwd_path[0] = '/';
        if (p_ftp_rsp_parser_info->st_ftp_parser.a_rsp_arg[0] == '"')
        {
            memcpy(p_ftp_parser->op_pwd_path + 1, p_ftp_rsp_parser_info->st_ftp_parser.a_rsp_arg + 1, strlen(p_ftp_rsp_parser_info->st_ftp_parser.a_rsp_arg) - 2);
        }
        else
        {
            strcpy(p_ftp_parser->op_pwd_path + 1, p_ftp_rsp_parser_info->st_ftp_parser.a_rsp_arg);
        }

        if (strlen(p_ftp_parser->pwd_path) == 0 && (strlen(p_ftp_parser->dir_path) == 1 && p_ftp_parser->dir_path[0] == '/'))
        {
            strcpy(p_ftp_parser->pwd_path, p_ftp_parser->op_pwd_path + 1);
        }

        break;
    case FTP_CWD:   /* 切换目录 */
        parser_dir(p_ftp_parser, p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state, p_ftp_req_parser_info->st_ftp_parser.a_command_arg);
        break;
    case FTP_STOR:
        cp_protocol_info(p_ftp_parser, p_ftp_req_parser_info, p_ftp_rsp_parser_info);
        break;
    case FTP_LIST:
        cp_protocol_info(p_ftp_parser, p_ftp_req_parser_info, p_ftp_rsp_parser_info);
        break;

    case FTP_RETR:
        cp_protocol_info(p_ftp_parser, p_ftp_req_parser_info, p_ftp_rsp_parser_info);
        break;
    default:
        break;
    }

    if (u8_command_type == FTP_PASV || u8_command_type == FTP_PORT || u8_command_type == FTP_EPSV || u8_command_type == FTP_EPRT)
    {
        new_ftp_data_link_map(p_ftp_parser, p_param->psd);
    }

    p_ftp_parser->pcap_ts = p_session->get_ts();
    /* 一次登录事件 */
    if (u8_command_type == FTP_PASS && p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state ==230)
    {
        upload_ftp_login_event(p_ftp_parser, p_session);
    }

    // p_ftp_parser->st_req_ftp_parser_info.i_parser_complete = 0;
    // p_ftp_parser->st_rsp_ftp_parser_info.i_parser_complete = 0;
    return;
}

/* 检测再下一次数据链路来之前，上一次的数据链路是否被释放(出现这种情况有可能数据链路传输失败) */
void CFtpParser::check_ftp_data_map(ftp_parser_ext_t *p_ftp_parser)
{
    int i_index = p_ftp_parser->i_ftp_data_map_index;
    //printf ("check index = %d\n", i_index);
    if (i_index == -1)
    {
        return;
    }

    if (m_ftp_data_map[i_index].i_state == 1)
    {
        pthread_rwlock_wrlock(&m_ftp_rwlock);
        //printf ("check ftp data map release index = %d\n", i_index);
        m_ftp_data_map[i_index].i_state = 0;
        memset(&(m_ftp_data_map[i_index].st_ftp_data_link), 0, sizeof(data_link_info_t));
        //printf ("ip = %u, port = %hu\n", g_p_ftp_data_map[i_index].st_ftp_data_link.u32_link_ip, g_p_ftp_data_map[i_index].st_ftp_data_link.u16_link_port);

        m_ftp_data_map[i_index].p_stream_data = NULL;
        pthread_rwlock_unlock(&m_ftp_rwlock);

        p_ftp_parser->i_ftp_data_map_index = -1;
    }

    return;
}

void CFtpParser::new_ftp_data_link_map(ftp_parser_ext_t *p_ftp_parser, StreamData *p_stream_data)
{
    int i_data_hash_index = 0;
    if (p_ftp_parser->st_port_or_pass_info.i_ip_type == 0)
    {
        i_data_hash_index = mk_ftp_data_hash(p_ftp_parser->st_port_or_pass_info.u32_link_ip
                                           , p_ftp_parser->st_port_or_pass_info.u16_link_port
                                           , FTP_DATA_LINK_NUM);
    }
    else
    {
        i_data_hash_index = mk_ftp_data_hash(p_ftp_parser->st_port_or_pass_info.a_u32_link_ip[3]
                                           , p_ftp_parser->st_port_or_pass_info.u16_link_port, FTP_DATA_LINK_NUM);
        //printf ("data hash = %d\n", i_data_hash_index);
    }
    //printf ("new ftp data link map index = %d\n", i_data_hash_index);

    pthread_rwlock_wrlock(&m_ftp_rwlock);
    for (; i_data_hash_index < FTP_DATA_LINK_NUM; ++i_data_hash_index)
    {
        if (m_ftp_data_map[i_data_hash_index].i_state == 1)
        {
            continue;
        }
        else
        {
            memcpy(&(m_ftp_data_map[i_data_hash_index].st_ftp_data_link)
                 , &(p_ftp_parser->st_port_or_pass_info)
                 , sizeof(data_link_info_t));
#           if 0
            if (m_ftp_data_map[i_data_hash_index].st_ftp_data_link.i_ip_type == 0)
            {
                printf ("index = %d, port = %hu, ip = %u\n", i_data_hash_index
                                                           , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.u16_link_port
                                                           , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.u32_link_ip);
            }
            else
            {
                printf ("index = %d, port = %hu, ip1 = %u, ip2 = %u, ip3 = %u, ip4 = %u\n", i_data_hash_index
                                                                                          , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.u16_link_port
                                                                                          , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.a_u32_link_ip[0]
                                                                                          , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.a_u32_link_ip[1]
                                                                                          , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.a_u32_link_ip[2]
                                                                                          , m_ftp_data_map[i_data_hash_index].st_ftp_data_link.a_u32_link_ip[3]);
            }
#           endif
            m_ftp_data_map[i_data_hash_index].p_stream_data = p_stream_data;
            m_ftp_data_map[i_data_hash_index].i_state = 1;
            p_ftp_parser->i_ftp_data_map_index = i_data_hash_index;
            //printf ("new data hash index = %d\n", i_data_hash_index);
            break;
        }
    }
    pthread_rwlock_unlock(&m_ftp_rwlock);
    return;
}

void CFtpParser::release_ftp_data_map(StreamData *psd)
{
    //printf ("release ftp data map\n");
    int i_index = psd->p_ftp_stream->p_ftp_data_parser->i_ftp_data_map_index;

    pthread_rwlock_wrlock(&m_ftp_rwlock);

    if (m_ftp_data_map[i_index].i_state == 0)
    {
        pthread_rwlock_unlock(&m_ftp_rwlock);
        return;
    }

    //printf ("release index = %d\n", i_index);
    m_ftp_data_map[i_index].i_state = 0;
    memset(&(m_ftp_data_map[i_index].st_ftp_data_link), 0, sizeof(data_link_info_t));
    //printf ("ip = %u, port = %hu\n", g_p_ftp_data_map[i_index].st_ftp_data_link.u32_link_ip, g_p_ftp_data_map[i_index].st_ftp_data_link.u16_link_port);
    m_ftp_data_map[i_index].p_stream_data = NULL;
    pthread_rwlock_unlock(&m_ftp_rwlock);

    return;
}

StreamData *CFtpParser::get_stream_data_from_session(CSession *p_session, int dir)
{
    StreamData *p_stream_data = NULL;

    if ((p_stream_data = p_session->get_stream_data_from_type(m_ftp_type)) == NULL)
    {
        // 创建 StreamData
        p_stream_data = new StreamData();
        if (!p_session->set_parser_by_type(m_ftp_type, this, p_stream_data))
        {
            delete p_stream_data;
            return NULL;
        }

        p_stream_data->p_ftp_stream = new ftp_stream_t();
    }

    p_session->update_time(); // 更新当前会话时间

    return p_stream_data;
}

int CFtpParser::init_ftp_data_map()
{
    int i_ret = 0;
    m_ftp_data_map = (ftp_data_map_t *)malloc(sizeof(ftp_data_map_t) * FTP_DATA_LINK_NUM);
    if (NULL == m_ftp_data_map)
    {
        GWLOG_ERROR(m_comm, "malloc ftp data map failed\n");
        return -1;
    }
    memset(m_ftp_data_map, 0 , sizeof(ftp_data_map_t) * FTP_DATA_LINK_NUM);

    /* 初始化读写锁 */
    i_ret = pthread_rwlock_init(&m_ftp_rwlock, NULL);
    if (i_ret != 0)
    {
        GWLOG_ERROR(m_comm, "init rwlock failed\n");
        return -1;
    }

    return 0;
}

void CFtpParser::uninit_ftp_data_map()
{
    if (NULL == m_ftp_data_map)
    {
        return;
    }

    free(m_ftp_data_map);
    pthread_rwlock_destroy(&m_ftp_rwlock);
}
