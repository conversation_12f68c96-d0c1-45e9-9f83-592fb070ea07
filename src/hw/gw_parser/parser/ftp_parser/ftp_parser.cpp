#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "ftp_parser.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"
#include "gw_stats.h"
#include "display_stats_define.h"
#include "ftp_parser_common.h"

#include "worker_queue.h"
#include "utils.h"
#include "pp_hash.h"
#include "minio_upload.h"
#include "nacos_listen_conf.h"

#define FTP_PARSER_TOTAL "ftp_parser_bytes"

CFtpParser::CFtpParser(void) : m_quit_signal(0)
                             , m_comm(NULL)
                             , m_name{0}
                             , m_ftp_data_map(NULL)
                             , m_u64_ftp_upload_ms(0)
                             , m_u32_ftp_upload_index(0)
                             , m_str_gw_ip("127.0.0.1")
                             , m_conf_upload_name("log")
                             , m_stats_ftp{0}
                             , m_ftp_type(9)
                             , m_u32_conf_ftp_upload_max_size(2097152)
                             , m_conf_probe_data_limit(10 * 1024)
                             , m_upload(NULL)
                             , m_upload_file(0)
                             , m_upload_obj(NULL)
                             , m_conf_ftp_upload_queue_max_num(1000)
                             , m_conf_ftp_upload_queue_memory_max_size_bytes(100 * 1024 * 1024UL)
                             , m_conf_ftp_upload_thread_num(1)
                             , m_use_new_event_format(0)
                             , m_conf_parser_enable(1)
                             , ftp_bytes(0)
                                
{
    
    snprintf(m_name, COUNTOF(m_name) - 1, "CFtpParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);

    //memset(&m_stats_ftp, 0, sizeof(stats_ftp_t));
    
    // m_conf_kfk_priority = 1;
}

CFtpParser::~CFtpParser(void)
{
    
}

void CFtpParser::modify_stats(int enable) {
    GWLOG_INFO(m_comm, "CFtpParser modify stats: %d, parser_enable: %d\n", enable, m_conf_parser_enable);
    if (enable > 1) 
        enable = 1;
    if (__sync_bool_compare_and_swap(&m_conf_parser_enable, enable^1, enable)) {
        GWLOG_DEBUG(m_comm, "CFtpParser modify true\n");
        cache_clean();
    }
    GWLOG_DEBUG(m_comm, "CFtpParser after modify stats: %d, parser_enable: %d\n", enable, m_conf_parser_enable);
}

void CFtpParser::cache_clean() 
{
    if (m_upload_data_wq)
    {
        m_upload_data_wq->flush_queue();
    }
    memset(m_ftp_data_map, 0 , sizeof(ftp_data_map_t) * FTP_DATA_LINK_NUM);
}

uint32_t CFtpParser::parser_status() const
{
    if (m_upload_data_wq->queue_elements_num())
    {
        return 1;
    }

    return 0;
}

/**
 * 获取当前流解析出来的数据。
 * @param struct StreamData *
 * @param int dir
 * @param int *data_len
 * @param int *offset_out
 */
const char *CFtpParser::get_data(const struct StreamData *, int dir, int *data_len, int *offset_out)
{
  *data_len = 0;
  *offset_out = 0;
  return NULL;
}

/**
 * 已处理字节数。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CFtpParser::discard(struct StreamData *, int dir, int num)
{
    return false;
}

/**
 * 已处理字节数，同时更新数据。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CFtpParser::discard_and_update(struct StreamData *, int dir, int num)
{
    return false;
}

/**
 * @param StreamData*
 */
void CFtpParser::del_session_stream(StreamData *psd)
{
    struct ftp_stream *pfs = psd->p_ftp_stream;
    if (pfs->p_ftp_parser != NULL)
    {
        while (pfs->p_ftp_parser->p_rsp_parser != NULL)
        {
            free_sessionn_ftp_parser(pfs->p_ftp_parser, STREAM_RSP, &(pfs->p_ftp_parser->p_rsp_parser->st_ftp_parser_info));
        }
        
        while (pfs->p_ftp_parser->p_req_parser != NULL)
        {
            free_sessionn_ftp_parser(pfs->p_ftp_parser, STREAM_REQ, &(pfs->p_ftp_parser->p_req_parser->st_ftp_parser_info));
        }

        del_session_ftp_ctrl_parser(pfs->p_ftp_parser);
    }
    if (pfs->p_ftp_data_parser != NULL)
    {
        del_session_ftp_data_parser(pfs->p_ftp_data_parser);
    }
    delete psd->p_ftp_stream;
    delete psd;
}

/**
 * @param SessionMgtData*
 */
void CFtpParser::del_session_param(SessionMgtData *)
{

}

void CFtpParser::init()
{
    ASSERT(m_comm != NULL);
    m_quit_signal = 0;
    load_conf(NULL);

    m_comm->get_gw_stats()->set_byte_stats(FTP_PARSER_TOTAL,NULL, &(ftp_bytes));

    init_ftp_data_map();
    init_hash();
    m_comm->get_gw_stats()->set_stats_callback(FTP_SHOW, print_ftp_stats_callback, this);
    m_comm->get_nacos_listen_conf()->add_conf_handle_int("ftp_parser_enable", std::bind(&CFtpParser::modify_stats, this, std::placeholders::_1));
}

void CFtpParser::fini()
{
    uninit_ftp_data_map();
}

void CFtpParser::run()
{
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CFtpParser::get_name(void) const
{
    return m_name;
}

/**
 * 获取版本号。
 */
const char *CFtpParser::get_version(void) const
{
    return FTPPARSER_VER;
}   

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CFtpParser::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CFtpParser::load_conf(const char *)
{
    CGwConfig *pgwc = m_comm->get_gw_config();
    std::string str_upload_name = pgwc->read_conf_string("parser", "upload_mode");
    if (str_upload_name.size() > 0)
    {
        m_conf_upload_name = str_upload_name;
    }

    std::string str_gw_ip = pgwc->read_conf_string("parser", "gw_ip");
    if (str_gw_ip.size() > 0)
    {
        m_str_gw_ip = str_gw_ip;
    }
    m_u32_conf_ftp_upload_max_size = pgwc->read_conf_int("parser", "ftp_upload_max_size", m_u32_conf_ftp_upload_max_size);
    m_conf_probe_data_limit = pgwc->read_conf_int("parser", "probe_data_limit", m_conf_probe_data_limit);

    m_upload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
    if (m_upload == NULL)
    {
        GWLOG_ERROR(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    }

    m_upload_file =  pgwc->read_conf_int("parser", "upload_file", m_upload_file);
    if (m_upload_file)
    {
        m_upload_obj =  m_comm->get_minio_upload();
    }
    m_conf_ftp_upload_queue_max_num = pgwc->read_conf_int("parser", "ftp_upload_queue_num", m_conf_ftp_upload_queue_max_num);
    m_conf_ftp_upload_queue_memory_max_size_bytes = pgwc->read_conf_int("parser", "ftp_upload_queue_memory_size", 
                                                                             m_conf_ftp_upload_queue_memory_max_size_bytes / (1 << 20ULL)) * (1 << 20ULL);
    m_conf_ftp_upload_thread_num = pgwc->read_conf_int("parser", "ftp_upload_thread_num", m_conf_ftp_upload_thread_num);
    m_use_new_event_format = pgwc->read_conf_int("parser", "use_new_event_format", m_use_new_event_format);
    new_wq_upload_msg();
    return true;
}

/**
 * 触发退出信号时处理
 */
void CFtpParser::set_quit_signal(void)
{
    m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CFtpParser::wait_for_stop(void)
{
}

/**
 * 设置过滤规则。
 * @param CFilterRule *rule
 */
void CFtpParser::set_url_filter_rule(CFilterRule *rule)
{
    
}

/**
 *  设置账号过滤规则 
 *  @param CFilterRule *rule
 */
void CFtpParser::set_accout_filter_rule(CFilterRule *rule)
{

}

void CFtpParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) {}


 /**
 * 增加上层协议解析对象。
 * @param CParser *parser
 */
void CFtpParser::add_upstream(CParser *parser)
{

}

/**
 * 清空上层协议解析对象
 */
void CFtpParser::reset_upstream(void)
{

}

/**
 * 推送到上层消息(异步方式, Json序列化数据)
 * @param char *s
 * @param size_t *length
 */
void CFtpParser::push_upstream_msg(char *s, size_t length)
{

}

/**
 * 是否使用当前协议解析流数据
 * @param struct StreamData*
 */
bool CFtpParser::is_parsed(const struct StreamData *) const
{
    return true;
}

/**
 * 克隆会话流数据到队列中使用(预留)
 * @param struct StreamData*
 */
struct StreamData *CFtpParser::clone_stream_data(const struct StreamData *)
{
    return NULL;
}

/**
 *  获取解析http数量(针对http parser) 
 */
uint64_t CFtpParser::get_parser_http_cnt()
{
    return 0;
}

/**
 *  获取解析http成功的数量(针对http parser) 
 */
uint64_t CFtpParser::get_succ_parser_http_cnt()
{
    return 0;
}

/**
 *  获取解析parser的状态数据，以便于进行查看Parser内部状态
 */
void* CFtpParser::get_parser_status()
{
  return NULL;
}

 /**
 * 设置解析对象type
 */
void CFtpParser::set_parser_type(int type)
{
    m_ftp_type = type;
}

void CFtpParser::read_conf_urlbase_for_mon()
{
    return;
}

void CFtpParser::read_conf_filetype_for_mon()
{
    return;
}


void CFtpParser::get_log_buf(char *log_buf, size_t log_buf_len) const
{
    sprintf (log_buf + strlen(log_buf), "\n%-20s %12s %12s %12s\n", "stats ftp", "total", "success", "failure");
    sprintf (log_buf + strlen(log_buf), "ftp match          %12" PRIu64 "\n", m_stats_ftp.u64_ftp_ctrl_match_cnt);
    sprintf (log_buf + strlen(log_buf), "ftp data parser    %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_cnt
                                                                                                        , m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_succ
                                                                                                        , m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_fail);

    sprintf (log_buf + strlen(log_buf), "ftp parser         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_cnt
                                                                                                        , m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_succ
                                                                                                        , m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_fail);

    sprintf (log_buf + strlen(log_buf), "ftp request parser %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_cnt
                                                                                                        , m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_succ
                                                                                                        , m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_fail);

    sprintf (log_buf + strlen(log_buf), "ftp reponse parser %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_cnt
                                                                                                        , m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_succ
                                                                                                        , m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_fail); 
    sprintf (log_buf + strlen(log_buf), "ftp upload         %12" PRIu64 "\n", m_stats_ftp.u64_ftp_upload_event_cnt);

    return;
}

void CFtpParser::print_ftp_stats_callback(void *p)
{
    ASSERT(p != NULL);
    CFtpParser *pThis = (CFtpParser*)p;

    pThis->print_ftp_stats();
}

void CFtpParser::print_ftp_stats(void) const
{
    // printf ("\n%-20s %12s %12s %12s\n", "stats ftp", "total", "success", "failure");
    // printf ("ftp match          %12" PRIu64 "\n", m_stats_ftp.u64_ftp_ctrl_match_cnt);
    // printf ("ftp data parser    %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_cnt
    //                                                                         , m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_succ
    //                                                                         , m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_fail);

    // printf ("ftp parser         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_cnt
    //                                                                         , m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_succ
    //                                                                         , m_stats_ftp.st_ftp_ctrl_parser.u64_ftp_parser_fail);

    // printf ("ftp request parser %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_cnt
    //                                                                         , m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_succ
    //                                                                         , m_stats_ftp.st_req_ftp_ctrl_parser.u64_ftp_parser_fail);

    // printf ("ftp reponse parser %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_cnt
    //                                                                         , m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_succ
    //                                                                         , m_stats_ftp.st_rsp_ftp_ctrl_parser.u64_ftp_parser_fail); 
    // printf ("ftp upload         %12" PRIu64 "\n", m_stats_ftp.u64_ftp_upload_event_cnt);
    char log_buf[LOG_BUF_LEN] = {0};
    get_log_buf(log_buf, LOG_BUF_LEN);

    printf("%s", log_buf);
    return;
}
