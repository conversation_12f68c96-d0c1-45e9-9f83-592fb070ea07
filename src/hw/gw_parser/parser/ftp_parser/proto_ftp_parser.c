#include <stdlib.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <string.h>
#include <stdio.h>
#include "proto_ftp_parser.h"
#include "utils.h"
#include "pp_hash.h"

#ifndef IS_NUM
#define IS_NUM(c) ((c) >= '0' && (c) <= '9')
#endif

#define FIRST_IP_BASE       (1)
#define SECOND_IP_BASE      (1 << 8)
#define THIRD_IP_BASE       (1 << 16)
#define FOURTH_IP_BASE      (1 << 24)
#define FIRSR_PORT_BASE     (1 << 8)
#define SECOND_PORT_BASE    (1)

void init_ftp_parser_ext(ftp_parser_ext_t *p_ftp_parser_ext)
{
    memset(p_ftp_parser_ext, 0, sizeof(ftp_parser_ext_t));
    p_ftp_parser_ext->i_ftp_data_map_index = -1;
    p_ftp_parser_ext->op_pwd_path[0] = '/';
    p_ftp_parser_ext->dir_path[0] = '/';        /* 根目录初始化为"/" */
}

void init_ftp_parser_innfo(ftp_parser_info_t *p_ftp_parser_info)
{
    memset(p_ftp_parser_info, 0, sizeof(ftp_parser_info_t));
}

int tcp_ftp_parser(ftp_parser_info_t *p_ftp_parser_info, int i_dir, const void *p_data, uint32_t u32_data_len)
{
    int i_ret = 0;
    ftp_parser_init(&p_ftp_parser_info->st_ftp_parser, (enum ftp_parser_type)i_dir);

    i_ret = ftp_parser_execute(&p_ftp_parser_info->st_ftp_parser, (const char *)p_data, u32_data_len);
    if (i_ret != (int)u32_data_len)
    {
        printf ("ftp parser failed(%d)\n", i_ret);
        return -1;
    }

    /* 表明当前解析FTP完成 */
    p_ftp_parser_info->i_parser_complete = 1;
    return i_ret;
}

void get_data_link_info(ftp_parser_ext_t *p_ftp_parser, ftp_parser_info_t *p_ftp_req_parser_info, const char *p_args, int i_data_link_mode, const void *pcon)
{
    if (NULL == p_ftp_parser || NULL == p_args)
    {
        return ;
    }

    unsigned a_u_ip_port_info[6] = {0};
    int i_comma_index = 0;
    const char *p = NULL;
    uint16_t u16_port = 0;
    
    size_t args_len = strlen(p_args);
    if (i_data_link_mode == FTP_PASV_MODE)
    {
        p_ftp_parser->st_port_or_pass_info.i_ip_type = 0;  /* ipv4 */
        for (p = p_args; p != p_args + args_len; ++p)
        {
            if (*p != '(')
            {
                continue;
            }
            
            for (p = p + 1; p != p_args + args_len; ++p)
            {
                if (*p == ',')
                {
                    i_comma_index ++; 
                    /* 检查‘,’的个数，超过6个则跳出循环 */
                    if (i_comma_index == 6)
                    {
                        break;
                    }
                    continue;
                }

                if (*p == ')')
                {
                    break;
                }

                /* 判断是否是数字 */
                if (!IS_NUM(*p))
                {
                    return;
                }
                
                a_u_ip_port_info[i_comma_index] *= 10;
                a_u_ip_port_info[i_comma_index] += (*p - '0');
            }

            break;
        }
    }
    else if (i_data_link_mode == FTP_PORT_MODE)
    {
        p_ftp_parser->st_port_or_pass_info.i_ip_type = 0;
        for (p = p_args; p != p_args + args_len; ++p)
        {
            if (!IS_NUM(*p))
            {
                continue;
            }

            for ( ; p != p_args + args_len; ++p)
            {
                if (*p == ',')
                {
                    i_comma_index ++; 
                    /* 检查‘,’的个数，超过6个则跳出循环 */
                    if (i_comma_index == 6)
                    {
                        break;
                    }
                    continue;
                }

                if (!IS_NUM(*p))
                {
                    break;
                }

                a_u_ip_port_info[i_comma_index] *= 10;
                a_u_ip_port_info[i_comma_index] += (*p - '0');
            }
            break;
        }
    }
    else if (i_data_link_mode == FTP_EPSV_MODE)
    {
        /* 查询 EPSV的参数 */
        if (NULL == pcon)
        {
            return;
        }

        struct conn *pconn = (struct conn*)pcon;
        size_t req_args_len = strlen(p_ftp_req_parser_info->st_ftp_parser.a_command_arg);
        if (req_args_len == 0) /* 不带参数 */
        {
            /* 使用控制链路的的连接模式 */
            if (pconn->client.v == 4)
            {
                p_ftp_parser->st_port_or_pass_info.i_ip_type = 0;
                p_ftp_parser->st_port_or_pass_info.u32_link_ip = pconn->server.ipv4;
            }
            else
            {
                p_ftp_parser->st_port_or_pass_info.i_ip_type = 1;
                memcpy(p_ftp_parser->st_port_or_pass_info.a_u32_link_ip, (uint32_t*)pconn->server.ipv6, sizeof(pconn->server.ipv6));
            }
        }
        else if (req_args_len == 1) /* 1 ipv4 2 ipv6 */
        {
            int i_type = atoi(p_ftp_req_parser_info->st_ftp_parser.a_command_arg);
            //printf ("i_type = %d, addr type = %d\n", i_type, tcp->addr.i_ip_type);
            if (i_type == 1)
            {
                if (pconn->client.v != 4)
                {
                    return;
                }
                p_ftp_parser->st_port_or_pass_info.i_ip_type = 0;
                p_ftp_parser->st_port_or_pass_info.u32_link_ip = pconn->server.ipv4;
            }
            else if (i_type == 2)
            {
                if (pconn->client.v != 6)
                {
                    return;
                }
                p_ftp_parser->st_port_or_pass_info.i_ip_type = 1;
                memcpy(p_ftp_parser->st_port_or_pass_info.a_u32_link_ip, (uint32_t*)pconn->server.ipv6, sizeof(pconn->server.ipv6));
            }
            else
            {
                return;
            }
        }
        else
        {
            /* TBD(目前没有遇见过，之后完善) */
            return ;
        }

        /* 解析出数据链路的port */
        for (p = p_args; p < p_args + args_len; ++p)
        {
            if (*p != '(')
            {
                continue;
            }

            /* 下一个字符是分隔符 */
            char split = 0;
            if (p + 1 != p_args + args_len)
            {
                split = *(p + 1);
            }
            else
            {
                return;
            }
            int i_split_num = 0;
            for (p = p + 1; p < p_args + args_len; ++p)
            {
                if (*p == split)
                {
                    i_split_num++;
                    if (i_split_num == 4)
                    {
                        break;
                    }
                    continue;
                }

                if (!IS_NUM(*p))
                {
                    continue;
                }
                //printf ("*p = %c, link port = %hu\n", *p, p_ftp_parser->st_port_or_pass_info.u16_link_port);
                u16_port *= 10;
                u16_port += (*p - '0');
                
            }
        }
        p_ftp_parser->st_port_or_pass_info.u16_link_port = u16_port;
    }
    else if (i_data_link_mode == FTP_EPRT_MODE)
    {
        /* 第一个参数是分隔符 */
        char split = *p_args;
        int split_num = 0;
        char *args_prev = (char *)p_args;
        char a_ip_type[8] = {0};
        char a_ip_addr[64] = {0};
        char a_ip_port[8] = {0};
        for (p = p_args + 1; p < p_args + args_len; ++p)
        {
            if (*p != split)
            {
                if (IS_NUM(*p) || *p == '.' || *p == ':')
                {
                    continue;
                }
                else
                {
                    break;
                }
            }

            split_num++;
            if (split_num > 4)
            {
                break;
            }
            if (split_num == 2)
            {
                /* ip类型 */
                memcpy(a_ip_type, args_prev + 1, p - args_prev - 1);
                p_ftp_parser->st_port_or_pass_info.i_ip_type = atoi(a_ip_type) - 1;
                
            }
            else if (split_num == 3)
            {
                /* ip地址 */
                memcpy(a_ip_addr, args_prev + 1, p - args_prev -1);
                if (p_ftp_parser->st_port_or_pass_info.i_ip_type == 0) /* ipv4 */
                {
                    struct in_addr s;
                    inet_pton(AF_INET, a_ip_addr, (void *)&s);
                    p_ftp_parser->st_port_or_pass_info.u32_link_ip = s.s_addr; 
                }
                else if (p_ftp_parser->st_port_or_pass_info.i_ip_type == 1) /* ipv6 */
                {
                    struct in6_addr s;
                    inet_pton(AF_INET6, a_ip_addr, (void*)&s);
                    memcpy(p_ftp_parser->st_port_or_pass_info.a_u32_link_ip, s.s6_addr32, sizeof(p_ftp_parser->st_port_or_pass_info.a_u32_link_ip));
                }
                else  /* 不支持 */
                {
                    return;        
                }
            }
            else if (split_num == 4)
            {
                /* 端口号 */
                memcpy(a_ip_port, args_prev + 1, p - args_prev - 1);
                p_ftp_parser->st_port_or_pass_info.u16_link_port = atoi(a_ip_port);
            }
            else
            {
                continue;
            }
            args_prev = (char*)p;
        }
    }
    else
    {
        return;
    }
    
    if (p_ftp_parser->st_port_or_pass_info.i_ip_type == 0 && i_data_link_mode != FTP_EPSV_MODE && i_data_link_mode != FTP_EPRT_MODE)
    {
        p_ftp_parser->st_port_or_pass_info.u32_link_ip = a_u_ip_port_info[0] * FIRST_IP_BASE + a_u_ip_port_info[1] * SECOND_IP_BASE + a_u_ip_port_info[2] * THIRD_IP_BASE + a_u_ip_port_info[3] * FOURTH_IP_BASE;
        p_ftp_parser->st_port_or_pass_info.u16_link_port = a_u_ip_port_info[4] * FIRSR_PORT_BASE + a_u_ip_port_info[5] * SECOND_PORT_BASE;
    }
    
    p_ftp_parser->i_data_link_mode = i_data_link_mode;
    //printf("port = %hu\n", p_ftp_parser->st_port_or_pass_info.u16_link_port);

    return;
}

void get_transfer_mode(ftp_parser_ext_t *p_ftp_parser, ftp_parser_info_t *p_ftp_req_parser_info)
{
    char *p_command_arg = p_ftp_req_parser_info->st_ftp_parser.a_command_arg;

    while (*p_command_arg != '\0')
    {
        if (*p_command_arg == 'I')
        {
            p_ftp_parser->u16_transfer_mode = BINARY_TRANSFER_MODE;
            return;
        }
        else if (*p_command_arg == 'A')
        {
            p_ftp_parser->u16_transfer_mode = ASCII_TRANSFER_MODE;
            return;
        }
        p_command_arg++;
    }
    
    return;
}

static void split_string(const char *split_string, char split, char ***ppp_result, size_t *p_result_size)
{
    int split_num = 0;
    const char* begin = split_string;
    const char *end = split_string;
    while (*end != '\0')
    {
        if (*end == '/')
        {
            if (end == begin)
            {
                begin = begin + 1;
            }
            else
            {
                begin = end + 1;
                split_num ++;
            }
        }
        end++;
    }

    if (end != begin)
    {
        split_num ++;
    }

    char **pp_result = NULL;

    pp_result = (char**)malloc(split_num * sizeof(char*));
    
    begin = split_string;
    end = split_string;
    int i = 0;

    while (*end != '\0')
    {
        if (*end == '/')
        {
            if (begin == end)
            {
                begin = begin + 1;
            }
            else
            {
                pp_result[i] = (char *)malloc(end - begin + 1);
                memset(pp_result[i], 0, end - begin + 1);
                memcpy(pp_result[i], begin, end - begin);
                begin = end + 1;
                i++;
            }
        }
        end ++;
    }

    if (end != begin)
    {
        pp_result[i] = (char *)malloc(end - begin + 1);
        memset(pp_result[i], 0, end - begin + 1);
        memcpy(pp_result[i], begin, end - begin);
    }
    
    *ppp_result = pp_result;
    if (p_result_size)
    {
        *p_result_size = split_num;
    }

    return;
}

static void free_split_string(char **pp_spilt_string, size_t split_size)
{
    if (pp_spilt_string == NULL || split_size == 0)
    {
        return;
    }

    int i = 0;
    for (i = 0; i < split_size; i++)
    {
        SAFE_FREE(pp_spilt_string[i]);
    }

    SAFE_FREE(pp_spilt_string);

    return;
}

/* flag为1 op_pwd_path的形式 //home/<USER>/
/* flag为0 op_pwd_path的形式 /home/<USER>/
static void parser_op_pwd_path(char *op_pwd_path, char **split_path, size_t split_path_size, int flag)
{
    size_t i = 0;

    char *end = op_pwd_path + strlen(op_pwd_path) - 1;
    for (i = 0; i < split_path_size; i++)
    {
        if (split_path[i][0] == '.' && strlen(split_path[i]) == 1)   /* 当前目录 */
        {
            continue;
        }
        else if (strlen(split_path[i]) == 2 && strcmp(split_path[i], "..") == 0)   /* .. 返回上一级目录 */
        { 
            if (flag == 1)
            {
                end = strrchr(op_pwd_path, '/');
                if (end - 1 == op_pwd_path) /* 已经是根目录, 执行返回上一级目录还是当前目录 */
                {
                    continue;
                }
                memset(end, 0, strlen(op_pwd_path) - (end - op_pwd_path));
                end = end - 1;
            }
            else
            {
                char *tmp = strrchr(op_pwd_path, '/');
                if (tmp == end) /* / */
                {
                    end = end + 1;
                    strcpy(end, split_path[i]);
                    end += (strlen(split_path[i]) - 1);
                }
                else
                {
                    if (memcmp(tmp + 1, "..", end - tmp) == 0)      /* /.. */
                    {
                        end = end + 1;
                        *end = '/';
                        strcpy(end + 1, split_path[i]);
                        end += strlen(split_path[i]);
                    }
                    else    
                    {
                        if (tmp == op_pwd_path)     /* /huangh */
                        {
                            memset(tmp + 1, 0, end - tmp);
                            end = tmp;
                        }
                        else                        /* /huangh/test */
                        {
                            memset(tmp, 0, end - tmp + 1);
                            end = tmp - 1;
                        }
                    }
                }
            }
        }
        else
        {
            if (*end != '/')  /* //huangh or /huang */
            {
                end = end + 1;
                *end = '/';
            } 
            else    /* // or / */
            {

            }

            strcpy(end + 1, split_path[i]);
            end = end + strlen(split_path[i]);
        }
    }

    return;
}

static void parser_file_path(const char *pwd_path, const char *op_pwd_path, char *dir_path)
{
    size_t i = 0;
    char **pwd_path_split = NULL;
    size_t pwd_path_split_size = 0;
    char **op_pwd_path_split = NULL;
    size_t op_pwd_path_split_size = 0;

    split_string(pwd_path, '/', &pwd_path_split, &pwd_path_split_size);
    split_string(op_pwd_path, '/', &op_pwd_path_split, &op_pwd_path_split_size);

    char path[PATH_MAX] = {0};                  /* 根据ftp根目录生成相对于系统根目录的路径 "/../../.." */
    for (i = 0; i < pwd_path_split_size; i ++)
    {
        *(path + ((i + 1) * 0)) = '/';
        *(path + ((i + 1) * 1)) = '.';
        *(path + ((i + 1) * 2)) = '.';
    }
    char *end = path + strlen(path) - 1;
    for (i = 0; i < pwd_path_split_size && i < op_pwd_path_split_size; i++)
    {
        size_t len_pwd_split = strlen(pwd_path_split[i]);
        size_t len_op_pwd_split = strlen(op_pwd_path_split[i]);

        if (len_pwd_split == len_op_pwd_split)
        {
            if (strcmp(pwd_path_split[i], op_pwd_path_split[i]) == 0)
            {
                end = strrchr(path, '/');
                if (end > path)
                {
                    memset(end, 0, 3);
                    end = end - 1;
                }
                else    /* 全部相等,保留'/' */
                {
                    memset(end + 1, 0, 2);
                }
            }
            else
            {
                break;
            }
        }
        else
        {
            break;
        }
    }

    for ( ; i < op_pwd_path_split_size; i ++)
    {
        if (*end != '/')
        {   
            end = end + 1;
            *end = '/';
        }

        end = end + 1;
        strcpy(end, op_pwd_path_split[i]);
        end += (strlen(op_pwd_path_split[i]) - 1);
    }

    free_split_string(pwd_path_split, pwd_path_split_size);
    free_split_string(op_pwd_path_split, op_pwd_path_split_size);

    strcpy(dir_path, path);
    return;
}

void parser_dir(ftp_parser_ext_t *p_ftp_parser, int status, const char *p_command_arg)
{
    if (p_ftp_parser == NULL)
    {
        return;
    }

    if (status != 250) /* 确保切换目录成功 */
    {
        return;
    }
    memset(p_ftp_parser->dir_path, 0, sizeof(p_ftp_parser->dir_path));

    size_t pwd_path_len = strlen(p_ftp_parser->pwd_path);
    size_t op_pwd_path_len = strlen(p_ftp_parser->op_pwd_path);
    char *pwd_path = p_ftp_parser->pwd_path;
    char *op_pwd_path = p_ftp_parser->op_pwd_path;
    //char *p_command_arg = p_ftp_parser->st_req_ftp_parser_info.st_ftp_parser.a_command_arg;

    char **split_path = NULL;
    size_t split_path_size = 0;
    split_string(p_command_arg, '/', &split_path, &split_path_size);

    if (*p_command_arg != '/')  /* 相对路径 */
    {
        if (op_pwd_path_len == 1 && op_pwd_path[0] == '/')  /* 目前是ftp服务提供的根目录 */
        {
            parser_op_pwd_path(op_pwd_path, split_path, split_path_size, 0);
            strcpy(p_ftp_parser->dir_path, op_pwd_path);
            goto end;
        }

        else if (op_pwd_path[0] == '/' && op_pwd_path[1] == '/') /* 使用系统的绝对路径 */
        {
            parser_op_pwd_path(op_pwd_path, split_path, split_path_size, 1);

            if (pwd_path_len == 0)  /* 根目录路径没有获取到 */
            {
                memcpy(p_ftp_parser->dir_path, op_pwd_path, strlen(op_pwd_path));
            }
            else
            {
                parser_file_path(pwd_path, op_pwd_path, p_ftp_parser->dir_path);
            }
        }
        else
        {
            parser_op_pwd_path(op_pwd_path, split_path, split_path_size, 0);
            strcpy(p_ftp_parser->dir_path, op_pwd_path);
        }
    }
    else    /* 绝对路径 */
    {
        memset(op_pwd_path, 0, strlen(op_pwd_path));
        *(op_pwd_path + 0) = '/';
        *(op_pwd_path + 1) = '/';
        parser_op_pwd_path(op_pwd_path, split_path, split_path_size, 1);

        if (pwd_path_len == 0)  /* 根目录路径没有获取到 */
        {
            memcpy(p_ftp_parser->dir_path, op_pwd_path, strlen(op_pwd_path));
        }
        else
        {
            parser_file_path(pwd_path, op_pwd_path, p_ftp_parser->dir_path);
        }
    }

    end:
        free_split_string(split_path, split_path_size);
        return ;
}


int mk_ftp_data_hash(uint32_t u32_link_ip, uint16_t u16_link_port, uint32_t u32_ftp_data_map_num)
{
    int i_index = 0;

    i_index = mkhash(u32_link_ip, u16_link_port, 0, 0);
    //printf ("link ip = %u, link port = %hu, index = %d\n", u32_link_ip, u16_link_port, i_index);
    return (i_index % u32_ftp_data_map_num);
}

void del_session_ftp_ctrl_parser(ftp_parser_ext_t *p_ftp_ctrl_parser)
{
    if (p_ftp_ctrl_parser == NULL)  
    {
        return ;
    }

    if (p_ftp_ctrl_parser->p_ftp_ctrl_json_obj != NULL)
    {
        cJSON_Delete(p_ftp_ctrl_parser->p_ftp_ctrl_json_obj);
    }

    free(p_ftp_ctrl_parser);
    //p_ftp_ctrl_parser = NULL;
    return;
}

void del_session_ftp_data_parser(ftp_data_link_t *p_ftp_data_parser)
{
    if (p_ftp_data_parser == NULL)
    {
        return;
    }

    if (p_ftp_data_parser->p_ftp_data_json_obj != NULL)
    {
        cJSON_Delete(p_ftp_data_parser->p_ftp_data_json_obj);
    }

    free(p_ftp_data_parser);
    //p_ftp_data_parser = NULL;
    return;
}

void cp_protocol_info(ftp_parser_ext_t *p_ftp_parser_ext, ftp_parser_info_t *p_ftp_req_parser_info, ftp_parser_info_t *p_ftp_rsp_parser_info)
{
    p_ftp_parser_ext->u8_ftp_command = p_ftp_req_parser_info->st_ftp_parser.u8_ftp_command;
    p_ftp_parser_ext->u16_rsp_state = p_ftp_rsp_parser_info->st_ftp_parser.u16_rsp_state;

    strcpy(p_ftp_parser_ext->a_command_arg, p_ftp_req_parser_info->st_ftp_parser.a_command_arg);
}