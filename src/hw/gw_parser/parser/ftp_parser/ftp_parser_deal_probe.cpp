/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */
#include <stdlib.h>
#include <set>
#include <algorithm>

#include "ftp_parser.h"
#include "ftp_parser_common.h"
#include "session_mgt.h"
#include "session.h"
#include "gw_logger.h"
#include "pp.h"


std::set<std::string> ftp_may_set {
    "DELE",
    "LIST",
    "PASS",
    "REST",
    "RETR",
    "RETR",
    "STAT",
    "USER",
    "ABOR",
    "ACCT",
    "ALLO",
    "APPE",
    "CDUP",
    "CWD",
    "EPRT",
    "EPSV",
    "HELP",
    "MKD",
    "MODE",
    "NLST",
    "OPTS",
    "PASV",
    "PORT",
    "PWD",
    "REIN",
    "RMD",
    "RNFR",
    "RNTO",
    "SITE",
    "SIZE",
    "SMNT",
    "STOR",
    "STRU",
    "SYST",
    "TYPE",
};

std::set<std::string> ftp_skip_set {
    "AUTH",
    "NOOP",
    "QUIT",
};

inline bool in_set(const std::string& s, const std::set<std::string>& i_set)
{
    return i_set.find(s) != i_set.end();
}

inline bool in_ftp_may_set(const std::string& s)
{
    return in_set(s, ftp_may_set);
}
inline bool in_ftp_skip_set(const std::string& s)
{
    return in_set(s, ftp_skip_set);
}

inline uint32_t get_ftp_cmd_len(const char* p, uint32_t l)
{
    if (l >= 4 && (*(p + 3) == ' ' || *(p + 3) == '\r'))
    {
        return 3;
    }
    else if(l >=5 && (*(p + 4) == ' ' || *(p + 4) == '\r'))
    {
        return 4;
    }else
    {
        return 0;
    }
}

inline std::string get_ftp_cmd(const char* p, uint32_t l)
{
    return std::string(p, get_ftp_cmd_len(p, l));
}

inline std::string str_toupper(std::string s)
{
    std::transform(s.begin(), s.end(), s.begin(), [](char c){ return std::toupper(c); });
    return s;
}

/*
    [1-5][0-5][0-9]
*/
inline bool is_ftp_rsp_code(const char* p, uint32_t l)
{
    return l >= 4 &&
            *p >= '1' &&
            *p <= '5' &&
            *(p + 1) >= '0' &&
            *(p + 1) <= '5' &&
            *(p + 2) >= '0' &&
            *(p + 2) <= '9' && 
            (*(p + 3) == ' ' || *(p + 3) == '-');
}

inline bool is_ftp_rsp_code(const std::string& s)
{
    return is_ftp_rsp_code(s.c_str(), s.size());
}

/**
 * 在接收数据时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CFtpParser::probe(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
    //CSession *p_session = psm->find_session(pcon);
    //CSession *p_session_first = p_session;
    StreamData *psd;
    ftp_stream_t *p_fs = NULL;
    const char *data;
    //int dir = a_app->dir;
    enum ftp_parser_type dir = FTP_BOTH;
    int data_len;
    int offset_out;
    StreamData *tcp_data = NULL;
    tcp_stream *a_tcp = NULL;

    if (p_session == NULL)
    {
        p_session = psm->new_session(pcon);
        if (p_session == NULL)
        {
            return false;
        }
    }

//    psd = get_stream_data_from_session(p_session, dir);
//    if (NULL == psd || NULL == (p_fs = psd->p_ftp_stream))
//    {
//        return false;
//    }

    tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
    if (tcp_data == NULL) {
        return false;
    }

    a_tcp = tcp_data->a_tcp;
    ASSERT(a_tcp != NULL);

    if ((a_app->dir ^ a_tcp->reverse) == 0)
    {
        dir = FTP_REQUEST;
    }
    else
    {
        dir = FTP_RESPONSE;
    }

    psd = get_stream_data_from_session(p_session, dir);
    if (NULL == psd || NULL == (p_fs = psd->p_ftp_stream))
    {
        return false;
    }

    int i_ftp_link_type = p_fs->ftp_link_type;
    if (i_ftp_link_type == FTP_CTRL_LINK || i_ftp_link_type == FTP_DATA_LINK)
    {
        return true;
    }
    
    if (i_ftp_link_type == NOT_FTP_LINK)
    {
        return false;
    }

    data = p_session->get_data(this, dir, &data_len, &offset_out);
    if (data == NULL || data_len <= 0)
    {
        return false;
    }

    int ftp_link_type = check_ftp_ctrl_link(pcon, data, data_len, dir, p_fs, p_session);
    if (ftp_link_type == FTP_CTRL_LINK)
    {
        p_fs->ftp_link_type = FTP_CTRL_LINK;
        goto suc;
    }
    else if(ftp_link_type == FTP_CTRL_LINK_REP_SKIP)
    {
        goto may;
    }
    else if (ftp_link_type == FTP_CTRL_LINK_REQ_MAY ||
                ftp_link_type == FTP_CTRL_LINK_RSP_MAY)
    {
        p_fs->ftp_link_type = ftp_link_type;
        goto may;
    }


    p_fs->ftp_link_type = check_ftp_data_link(psd, pcon, p_session);
    if (p_fs->ftp_link_type == FTP_DATA_LINK)
    {
        goto suc;
    }
    return false;
suc:
    p_session->set_header_complete();
    p_session->set_tcp_direction_confirmed();
    return true;
may:
    if (m_conf_probe_data_limit > 0 && data_len > m_conf_probe_data_limit)
    {
        p_session->tcp_drop_data(this);
    }
    else
    {
        p_session->discard(this, a_app->dir, 0);
    }
    return false;
}

/**
 * 在连接关闭时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CFtpParser::probe_on_close(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
  bool res = true;

  res = probe(psm, a_app, pcon, p_session);
  return res;
}

/**
 * 在连接重置时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CFtpParser::probe_on_reset(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
  return probe_on_close(psm, a_app, pcon, p_session);
}

int CFtpParser::check_ftp_ctrl_link(const struct conn *pcon, const char *p_data, int i_data_len, ftp_parser_type dir, ftp_stream_t *p_fs, CSession *p_session)
{
    if (i_data_len < 5)
    {
        return 0;
    }
    const char* data = p_data;
    int len = i_data_len;

    StreamData* p_tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
    tcp_stream* a_tcp = p_tcp_data->a_tcp;

    if (dir == FTP_RESPONSE)
    {
try_rsp:
        if (p_fs->rsp.p_d == p_data && p_fs->rsp.offset <= i_data_len)
        {
            data += p_fs->rsp.offset;
            len -= p_fs->rsp.offset;
        }
        if (is_ftp_rsp_code(data, len))
        {
            if (FTP_CTRL_LINK_REQ_MAY == p_fs->ftp_link_type)
                return FTP_CTRL_LINK;
            p_fs->rsp.p_d = p_data;
            p_fs->rsp.offset = i_data_len;
            return FTP_CTRL_LINK_RSP_MAY;
        }
        else
        {
            std::string cmd = str_toupper((get_ftp_cmd(data, len)));
            if (in_ftp_may_set(cmd) || in_ftp_skip_set(cmd))
            {
                a_tcp->reverse = !a_tcp->reverse;
                p_session->set_reverse(!!a_tcp->reverse);
                goto try_req;
            }
        }
    }
    else if (dir == FTP_REQUEST)
    {
try_req:
        if (p_fs->req.p_d == p_data && p_fs->req.offset <= i_data_len)
        {
            data += p_fs->req.offset;
            len -= p_fs->req.offset;
        }
        std::string cmd = str_toupper((get_ftp_cmd(data, len)));
        if (in_ftp_may_set(cmd))
        {
            if (FTP_CTRL_LINK_RSP_MAY == p_fs->ftp_link_type)
                return FTP_CTRL_LINK;
            p_fs->req.p_d = p_data;
            p_fs->req.offset = i_data_len;
            return FTP_CTRL_LINK_REQ_MAY;
        }
        else if (in_ftp_skip_set(cmd))
        {
            p_fs->req.p_d = p_data;
            p_fs->req.offset = i_data_len;
            return FTP_CTRL_LINK_REP_SKIP;
        }
        else
        {
            if (is_ftp_rsp_code(data, len))
            {
                a_tcp->reverse = !a_tcp->reverse;
                p_session->set_reverse(!!a_tcp->reverse);
                goto try_rsp;
            }
        }
    }
    else
    {
        GWLOG_ERROR(m_comm, "ftp dir error(%d)\n", dir);
    }

    return NOT_FTP_LINK;
}

int CFtpParser::check_ftp_data_link(StreamData *p_stream_data, const struct conn *pcon, CSession *p_session)
{
    int i_client_index = 0;
    int i_server_index = 0;
    int i_ret = 0;
    __sync_fetch_and_add(&m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_cnt, 1);
    if (pcon->client.v == 4)
    {
        i_client_index = mk_ftp_data_hash(pcon->client.ipv4, pcon->client.port, FTP_DATA_LINK_NUM);
        i_server_index = mk_ftp_data_hash(pcon->server.ipv4, pcon->server.port, FTP_DATA_LINK_NUM);
    }
    else
    {
        i_client_index = mk_ftp_data_hash(pcon->client.iv6[3], pcon->client.port, FTP_DATA_LINK_NUM);
        i_server_index = mk_ftp_data_hash(pcon->server.iv6[3], pcon->server.port, FTP_DATA_LINK_NUM);
    }

    i_ret = find_ftp_data_map(p_stream_data, &(pcon->client), i_client_index);
    if (i_ret == 0)
    {
        //GWLOG_DEBUG (m_comm, "client index = %d\n", i_index);
        __sync_fetch_and_add(&m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_succ, 1);
        p_stream_data->p_ftp_stream->p_ftp_data_parser->i_dir = (int)FTP_REQUEST;
        return FTP_DATA_LINK;
    }

    i_ret = find_ftp_data_map(p_stream_data, &(pcon->server), i_server_index);
    if (i_ret == 0)
    {
        //GWLOG_DEBUG (m_comm, "server index = %d\n", i_index);
        __sync_fetch_and_add(&m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_succ, 1);
        p_stream_data->p_ftp_stream->p_ftp_data_parser->i_dir = (int)FTP_RESPONSE;
        return FTP_DATA_LINK;
    }

    __sync_fetch_and_add(&m_stats_ftp.st_ftp_data_match.u64_ftp_data_match_fail, 1);
    //GWLOG_DEBUG (m_comm, "not find\n");
    return NOT_FTP_LINK;
}

int CFtpParser::find_ftp_data_map(struct StreamData *p_stream_data, const struct addr *p_addr, int i_index)
{
    int i = i_index;
    for ( ; i < FTP_DATA_LINK_NUM; ++i)
    {
        if (m_ftp_data_map[i].i_state == 0) /* 未使用 */
        {
            continue;
        }

        if (p_addr->v == 4) /* IPv4 */
        {
            if (!(p_addr->ipv4 == m_ftp_data_map[i].st_ftp_data_link.u32_link_ip 
             && p_addr->port == m_ftp_data_map[i].st_ftp_data_link.u16_link_port))
            {
                continue;
            }
        }
        else
        {
            if (!(memcmp((uint32_t*)p_addr->ipv6, m_ftp_data_map[i].st_ftp_data_link.a_u32_link_ip, sizeof(p_addr->ipv6)) == 0 
             && p_addr->port == m_ftp_data_map[i].st_ftp_data_link.u16_link_port))
            {
                continue;
            }
        }

        if (p_stream_data->p_ftp_stream->p_ftp_data_parser == NULL)
        {
            p_stream_data->p_ftp_stream->p_ftp_data_parser = (ftp_data_link_t *)malloc(sizeof(ftp_data_link_t));
            if (p_stream_data->p_ftp_stream->p_ftp_data_parser == NULL)
            {
                GWLOG_ERROR(m_comm, "malloc ftp data parser no mem\n");
                return -1;
            }

            memset(p_stream_data->p_ftp_stream->p_ftp_data_parser, 0, sizeof(ftp_data_link_t));
            m_ftp_data_map[i].p_stream_data->p_ftp_stream->p_ftp_parser->match_data = 1;
            memcpy((void*)&p_stream_data->p_ftp_stream->p_ftp_data_parser->ctrl_data, (void*)m_ftp_data_map[i].p_stream_data->p_ftp_stream->p_ftp_parser, sizeof(ftp_parser_ext_t));
            p_stream_data->p_ftp_stream->p_ftp_data_parser->i_ftp_data_map_index = i;
        }

        return 0;
    }

    return -1;
}