#ifndef _PROTO_FTP_PARSER_H_
#define _PROTO_FTP_PARSER_H_

#include <stdint.h>
#include <dirent.h>
#include "ftp_parser_inner.h"
#include "cJSON.h"
#include "utils.h"

#ifdef __cplusplus
extern "C" {
#endif

#define NOT_FTP_LINK (-1)
#define FTP_CTRL_LINK (1)
#define FTP_DATA_LINK (2)
#define FTP_CTRL_LINK_REQ_MAY (3)
#define FTP_CTRL_LINK_RSP_MAY (4)
#define FTP_CTRL_LINK_REP_SKIP (5)

#define FTP_PASV_MODE (3)
#define FTP_PORT_MODE (4)
#define FTP_EPSV_MODE (5) /* 扩展pasv */
#define FTP_EPRT_MODE (6) /* 扩展port */

#define ASCII_TRANSFER_MODE (0)
#define BINARY_TRANSFER_MODE (1)

typedef struct
{
    int i_ip_type;
    union
    {
        uint32_t u32_link_ip;
        uint32_t a_u32_link_ip[4];
    };
    //uint32_t u32_link_ip;
    uint16_t u16_link_port;
} data_link_info_t;

typedef struct ftp_parser_info
{
    uint32_t u32_ftp_header_len;
    int i_parser_complete;
    ftp_parser_t st_ftp_parser;
} ftp_parser_info_t;

typedef struct list_session_ftp_parser
{
    ftp_parser_info_t st_ftp_parser_info;
    struct list_session_ftp_parser *next;
    struct list_session_ftp_parser *pre;
}list_session_ftp_parser_t;

typedef struct ftp_parser_ext
{
    char a_user[COMMAND_ARGS_BUF_LEN];   /* FTP用户名 */
    char a_passwd[COMMAND_ARGS_BUF_LEN]; /* FTP登录密码 */
    uint16_t u16_transfer_mode;          /* 传输模式,默认是ASCII传输模式 */
    char pwd_path[PATH_MAX];             /* ftp服务提供操作的根目录，不改变 */
    char op_pwd_path[PATH_MAX];          /* ftp目前操作的目录 */            
    char dir_path[PATH_MAX];
    uint8_t u8_ftp_command;
    uint8_t u16_rsp_state;
    char a_command_arg[COMMAND_ARGS_BUF_LEN];

    int i_data_link_mode;
    data_link_info_t st_port_or_pass_info; /* 主被模式 */

    //ftp_parser_info_t st_req_ftp_parser_info;
    //ftp_parser_info_t st_rsp_ftp_parser_info;

    struct list_session_ftp_parser *p_req_parser;
    struct list_session_ftp_parser *p_req_parser_last;

    struct list_session_ftp_parser *p_rsp_parser;
    struct list_session_ftp_parser *p_rsp_parser_last;

    cJSON *p_ftp_ctrl_json_obj;
    int i_ftp_data_map_index;
    double pcap_ts;
    struct conn con;
    int match_data;
    bool reverse;
} ftp_parser_ext_t;

typedef struct ftp_data_link
{
    int i_ftp_data_map_index;
    int i_dir;
    uint64_t u64_data_len;
    cJSON *p_ftp_data_json_obj;
    const void *p_data;
    ftp_parser_ext_t ctrl_data;
} ftp_data_link_t;

void init_ftp_parser_ext(ftp_parser_ext_t *p_ftp_parser_ext);

void init_ftp_parser_innfo(ftp_parser_info_t *p_ftp_parser_info);

int tcp_ftp_parser(ftp_parser_info_t *p_ftp_parser_info, int i_dir, const void *p_data, uint32_t u32_data_len);

void get_data_link_info(ftp_parser_ext_t *p_ftp_parser, ftp_parser_info_t *p_ftp_req_parser_info, const char *p_args, int i_data_link_mode, const void *pcon);

void get_transfer_mode(ftp_parser_ext_t *p_ftp_parser, ftp_parser_info_t *p_ftp_req_parser_info);

void parser_dir(ftp_parser_ext_t *p_ftp_parser, int status, const char *p_command_arg);

int mk_ftp_data_hash(uint32_t u32_link_ip, uint16_t u16_link_port, uint32_t u32_ftp_data_map_num);

void cp_protocol_info(ftp_parser_ext_t *p_ftp_parser_ext, ftp_parser_info_t *p_ftp_req_parser_info, ftp_parser_info_t *p_ftp_rsp_parser_info);

#if 0
int cmp_addr_info(struct addr *p_addr, data_link_info_t *p_data_link);
#endif
void del_session_ftp_ctrl_parser(ftp_parser_ext_t *p_ftp_ctrl_parser);

void del_session_ftp_data_parser(ftp_data_link_t *p_ftp_data_parser);

#ifdef __cplusplus
}
#endif

#endif
