#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "ftp_parser_inner.h"
#include "utils.h"

#define CR '\r'
#define LF '\n'
#define LOWER(c) (unsigned char)(c | 0x20)
#define IS_ALPHA(c) (LOWER(c) >= 'a' && LOWER(c) <= 'z')
#define MARK(XX)        \
do                      \
{                       \
    if (!XX##_mark)     \
    {                   \
        XX##_mark = p;  \
    }                   \
}                       \
while(0)                \

static const char *command_strings[] =
    {
#define XX(num, name, string) #string,
        FTP_COMMAND_MAP(XX)
#undef XX
};

#ifndef ARRAY_SIZE
#define ARRAY_SIZE(a) (sizeof(a)/sizeof((a)[0]))
#endif

#ifndef ELEM_AT
# define ELEM_AT(a, i, v) ((unsigned int) (i) < ARRAY_SIZE(a) ? (a)[(i)] : (v))
#endif

enum state
{
    s_rsp_first_status_code,
    s_rsp_status_code,
    s_rsp_status_end,
    //s_rsp_pasv_ip_1st,
    //s_rsp_pasv_ip_2nd,
    //s_rsp_pasv_ip_3rd,
    //s_rsp_pasv_ip_4th,
    //s_rsp_pasv_1st_port,
    //s_rsp_pasv_2nd_port,
    s_rsp_pasv_done,
    s_rsp_arg,
    s_header_done,

    s_req_command_start,
    s_req_command,
    s_req_args_start,
    s_req_args,
};

void ftp_parser_init(ftp_parser_t *p_ftp_parser, enum ftp_parser_type type)
{
    memset(p_ftp_parser, 0, sizeof(ftp_parser_t));
    p_ftp_parser->u8_ftp_type = type;
    return;
}

int ftp_parser_execute(ftp_parser_t *p_ftp_parser, const char *p_data, size_t len)
{
    if (NULL == p_ftp_parser || NULL == p_data)
    {
        return FTPE_INVAILD_PARAM;
    }
    const char *rsp_value_mark = 0;
    const char *req_value_mark = 0;
    //uint16_t a_u16_pasv_ip[4] = {0};
    //uint16_t a_u16_pasv_port[2] = {0};
    //uint16_t u16_pasv_port = 0;
    const char *p = p_data;
    enum state ftp_state = s_rsp_first_status_code;
    int i_index = 1;

    if (p_ftp_parser->u8_ftp_type == FTP_RESPONSE)
    {
        ftp_state = s_rsp_first_status_code;
    }
    else if (p_ftp_parser->u8_ftp_type == FTP_REQUEST)
    {
        ftp_state = s_req_command_start;
    }
    else
    {
        return FTPE_INVAILD_TYPE;
    }

    for (p = p_data; p != (p_data + len); ++p)
    {
        switch (ftp_state)
        {
            case s_rsp_first_status_code:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ' ')
                    {
                        break;
                    }
                    return FTPE_INVAILD_STATUS;
                }

                p_ftp_parser->u16_rsp_state = *p - '0';
                ftp_state = s_rsp_status_code;
                break;
            }

            case s_rsp_status_code:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ' ')
                    {
                        ftp_state = s_rsp_status_end;
                        break;
                    }

                    return FTPE_INVAILD_STATUS;
                }
                p_ftp_parser->u16_rsp_state *= 10;
                p_ftp_parser->u16_rsp_state += *p - '0';
                if (p_ftp_parser->u16_rsp_state > 999)
                {
                    return FTPE_INVAILD_STATUS;
                }
                break;
            }

            case s_rsp_status_end:
            {
                if (*p == CR)
                {
                    ftp_state = s_header_done;
                    break;
                }
                
                MARK(rsp_value);
                ftp_state = s_rsp_arg;
                /*
                if (*p == '(' && p_ftp_parser->u16_rsp_state == 227)
                {
                    ftp_state = s_rsp_pasv_ip_1st;
                    break;
                }
                */

                break;
            }
        
            case s_rsp_arg:
            {
                for (; p != p_data + len; ++p)
                {
                    if (*p == CR) 
                    {
                        ftp_state = s_header_done;
                        memcpy(p_ftp_parser->a_rsp_arg, rsp_value_mark, MIN((p - rsp_value_mark), sizeof(p_ftp_parser->a_rsp_arg) - 1));
                        break;
                    } 
                }

                if (p == p_data + len)
                {
                    return FTPE_INVAILD_RSP_ARG;
                }

                break;
            }

            #if 0
            case s_rsp_pasv_ip_1st:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ',')
                    {
                        ftp_state = s_rsp_pasv_ip_2nd;
                        break;
                    }

                    return FTPE_PASV_IP_FORMAT_ERR;
                }

                a_u16_pasv_ip[0] *= 10;
                a_u16_pasv_ip[0] += *p - '0';

                if (a_u16_pasv_ip[0] > 255)
                {
                    return FTPE_PASV_IP_ERR;
                }
                break;
            }

            case s_rsp_pasv_ip_2nd:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ',')
                    {
                        ftp_state = s_rsp_pasv_ip_3rd;
                        break;
                    }
                    return FTPE_PASV_IP_FORMAT_ERR;
                }

                a_u16_pasv_ip[1] *= 10;
                a_u16_pasv_ip[1] += *p - '0';

                if (a_u16_pasv_ip[1] > 255)
                {
                    return FTPE_PASV_IP_ERR;
                }
                break;
            }

            case s_rsp_pasv_ip_3rd:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ',')
                    {
                        ftp_state = s_rsp_pasv_ip_4th;
                        break;
                    }

                    return FTPE_PASV_IP_FORMAT_ERR;
                }

                a_u16_pasv_ip[2] *= 10;
                a_u16_pasv_ip[2] += *p - '0';
                if (a_u16_pasv_ip[2] > 255)
                {
                    return FTPE_PASV_IP_ERR;
                }
                break;
            }

            case s_rsp_pasv_ip_4th:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ',')
                    {
                        ftp_state = s_rsp_pasv_1st_port;
                        break;
                    }
                    return FTPE_PASV_IP_FORMAT_ERR;
                }
                a_u16_pasv_ip[3] *= 10;
                a_u16_pasv_ip[3] += *p - '0';
                if (a_u16_pasv_ip[3] > 255)
                {
                    return FTPE_PASV_IP_ERR;
                }
                break;
            }

            case s_rsp_pasv_1st_port:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ',')
                    {
                        ftp_state = s_rsp_pasv_2nd_port;
                        break;
                    }
                    return FTPE_PASV_IP_FORMAT_ERR;
                }
                a_u16_pasv_port[0] *= 10;
                a_u16_pasv_port[0] += *p - '0';
                break;
            }

            case s_rsp_pasv_2nd_port:
            {
                if (!IS_NUM(*p))
                {
                    if (*p == ')')
                    {
                        u16_pasv_port = (a_u16_pasv_port[0] * 256) + a_u16_pasv_port[1];
                        if (u16_pasv_port > 65535)
                        {
                            // !CR 永不会执行到此，建议调整上面变量的类型。
                            return FTPE_PASV_PORT_ERR;
                        }

                        ftp_state = s_rsp_pasv_done;
                        break;
                    }
                    return FTPE_PASV_IP_FORMAT_ERR;
                }
                a_u16_pasv_port[1] *= 10;
                a_u16_pasv_port[1] += *p - '0';
                break;
            }
            #endif

            case s_rsp_pasv_done:
            {
                if (*p == CR)
                {
                    ftp_state = s_header_done;
                    break;
                }

                break;
            }

            case s_header_done:
            {
                if (*p != LF)
                {
                    return FTPE_FORMAT_ERR;
                }

                return (p - p_data) + 1;
            }

            case s_req_command_start:
            {
                if (!IS_ALPHA(*p))
                {
                    return FTPE_INVAILD_COMMAND;
                }

                p_ftp_parser->u8_ftp_command = (enum ftp_command)0;
                switch (*p)
                {
                case 'A':
                    p_ftp_parser->u8_ftp_command = FTP_ACCT; /* FTP_ABOR, FTP_ALLO, FTP_APPE, FTP_AUTH */
                    break;
                case 'C':
                    p_ftp_parser->u8_ftp_command = FTP_CDUP; /* FTP_CWD */
                    break;
                case 'D':
                    p_ftp_parser->u8_ftp_command = FTP_DELE;
                    break;
                case 'E':
                    p_ftp_parser->u8_ftp_command = FTP_EPSV;
                    break;
                case 'F':
                    p_ftp_parser->u8_ftp_command = FTP_FEAT;
                    break;
                case 'H':
                    p_ftp_parser->u8_ftp_command = FTP_HELP;
                    break;
                case 'L':
                    p_ftp_parser->u8_ftp_command = FTP_LIST;
                    break;
                case 'M':
                    p_ftp_parser->u8_ftp_command = FTP_MODE; /* FTP_MKD */
                    break;
                case 'N':
                    p_ftp_parser->u8_ftp_command = FTP_NLST; /* FTP_NOOP */
                    break;
                case 'O':
                    p_ftp_parser->u8_ftp_command = FTP_OPTS; /* FTP_OPTS */
                    break;
                case 'P':
                    p_ftp_parser->u8_ftp_command = FTP_PASS; /* FTP_PASV, FTP_PORT, FTP_PWD */
                    break;
                case 'Q':
                    p_ftp_parser->u8_ftp_command = FTP_QUIT;
                    break;
                case 'R':
                    p_ftp_parser->u8_ftp_command = FTP_REIN; /* FTP_REST, FTP_RETR, FTP_RMD, FTP_RNFR, FTP_RNTO */
                    break;
                case 'S':
                    p_ftp_parser->u8_ftp_command = FTP_SITE; /* FTP_SMNT, FTP_STAT, FTP_STOR, FTP_STRU, FTP_SYST */
                    break;
                case 'T':
                    p_ftp_parser->u8_ftp_command = FTP_TYPE;
                    break;
                case 'U':
                    p_ftp_parser->u8_ftp_command = FTP_USER;
                    break;
                case 'X':
                    p_ftp_parser->u8_ftp_command = FTP_XCCT;
                    break;
                default:
                    return FTPE_INVAILD_COMMAND;
                }

                ftp_state = s_req_command;
                break;
            }

            case s_req_command:
            {
                const char *p_command_string = NULL;
                p_command_string = command_strings[p_ftp_parser->u8_ftp_command];

                if (*p == ' ' && p_command_string[i_index] == '\0') /* 带有参数 */
                {
                    ftp_state = s_req_args_start;
                }
                else if (*p == CR && p_command_string[i_index] == '\0') /* 不带有参数 */
                {
                    ftp_state = s_header_done;
                }
                else if (*p == p_command_string[i_index])
                {
                    ; /* 什么都不做 */
                }
                else if (IS_ALPHA(*p))
                {
                    switch (p_ftp_parser->u8_ftp_command << 16 | i_index << 8 | *p)
                    {
#                           define XX(comm, pos, ch, new_comm)                        \
                            case (FTP_##comm << 16 | pos << 8 | ch):               \
                                p_ftp_parser->u8_ftp_command = FTP_##new_comm;     \
                                break;
                            XX(ACCT, 1, 'B', ABOR)
                            XX(ACCT, 1, 'L', ALLO)
                            XX(ACCT, 1, 'P', APPE)
                            XX(ACCT, 1, 'U', AUTH)
                            XX(CDUP, 1, 'W', CWD)
                            XX(EPSV, 2, 'R', EPRT)
                            XX(MODE, 1, 'K', MKD)
                            XX(MODE, 1, 'D', MDTM)
                            XX(NLST, 1, 'O', NOOP)
                            XX(PASS, 3, 'V', PASV)
                            XX(PASS, 1, 'O', PORT)
                            XX(PASS, 1, 'W', PWD)
                            XX(REIN, 2, 'S', REST)
                            XX(REIN, 2, 'T', RETR)
                            XX(REIN, 1, 'M', RMD)
                            XX(REIN, 1, 'N', RNFR)
                            XX(RNFR, 2, 'T', RNTO)
                            XX(SITE, 1, 'M', SMNT)
                            XX(SITE, 2, 'Z', SIZE)
                            XX(SITE, 1, 'T', STAT)
                            XX(SITE, 1, 'Y', SYST)
                            XX(STAT, 2, 'O', STOR)
                            XX(STAT, 2, 'R', STRU)
                            XX(FEAT, 1, 'E', FEAT)
                            XX(XCCT, 1, 'C', XCUP)
                            XX(XCCT, 1, 'M', XMKD)
                            XX(XCCT, 1, 'P', XPWD)
                            XX(XCCT, 1, 'R', XRCT)
                            XX(XRCT, 2, 'C', XRCP)
                            XX(XRCT, 2, 'M', XRMD)
                            XX(XRCT, 2, 'S', XRSQ)
                            XX(XCCT, 1, 'S', XSET)
                            XX(XSET, 3, 'M', XSEM)
                            XX(XSET, 3, 'N', XSEN)
#                           undef XX
                        default:
                            return FTPE_INVAILD_COMMAND;
                    }
                }
                else
                {
                    return FTPE_INVAILD_COMMAND;
                }

                i_index++;
                break;
            }

            case s_req_args_start:
            {
                MARK(req_value);
                ftp_state = s_req_args;
                break;
            }

            case s_req_args:
            {
                for ( ; p != p_data + len; ++p)
                {
                    if (*p == CR)
                    {
                        memcpy(p_ftp_parser->a_command_arg, req_value_mark, MIN((p - req_value_mark), sizeof(p_ftp_parser->a_command_arg) - 1));
                        ftp_state = s_header_done;
                        break;
                    }
                }

                if (p == p_data + len)
                {
                    return FTPE_INVAILD_REQ_ARG;
                }
                break;
            }
        }
    }

    return 0;
}

const char *ftp_method_str (enum ftp_command m)
{
  return ELEM_AT(command_strings, m, "<unknown>");
}