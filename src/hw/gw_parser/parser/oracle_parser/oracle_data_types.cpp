/*
 * Oracle数据类型支持模块实现
 * 基于ojdbc源码分析实现的Oracle数据类型编码解码功能
 * <AUTHOR> @date 2025
 */

#include "oracle_data_types.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <math.h>

OracleDataTypes::OracleDataTypes()
{
}

OracleDataTypes::~OracleDataTypes()
{
}

// NUMBER类型处理实现
int OracleDataTypes::decode_number(const uint8_t *data, size_t data_len, oracle_number_t *number)
{
    if (!data || data_len == 0 || !number) {
        return ORACLE_TYPE_ERROR;
    }

    memset(number, 0, sizeof(oracle_number_t));

    // 检查NULL值
    if (data_len == 1 && data[0] == 0x80) {
        number->is_null = true;
        return ORACLE_TYPE_SUCCESS;
    }

    // 检查零值
    if (data_len == 1 && data[0] == OracleTypeConstants::NUMBER_ZERO) {
        number->is_zero = true;
        return ORACLE_TYPE_SUCCESS;
    }

    // 检查正无穷大
    if (data_len == 2 && data[0] == OracleTypeConstants::NUMBER_POSITIVE_INFINITY && data[1] == 0x65) {
        // 正无穷大的特殊处理
        strcpy((char*)number->mantissa, "INFINITY");
        number->mantissa_length = 8;
        return ORACLE_TYPE_SUCCESS;
    }

    // 检查负无穷大
    if (data_len == 2 && data[0] == OracleTypeConstants::NUMBER_NEGATIVE_INFINITY && data[1] == 0x66) {
        // 负无穷大的特殊处理
        number->is_negative = true;
        strcpy((char*)number->mantissa, "INFINITY");
        number->mantissa_length = 8;
        return ORACLE_TYPE_SUCCESS;
    }

    // 解析指数和符号
    bool is_negative;
    int exponent;
    int ret = parse_number_exponent(data[0], &is_negative, &exponent);
    if (ret != ORACLE_TYPE_SUCCESS) {
        return ret;
    }

    number->is_negative = is_negative;
    number->exponent = exponent;

    // 解析尾数
    if (data_len > 1) {
        ret = parse_number_mantissa(data + 1, data_len - 1, number);
        if (ret != ORACLE_TYPE_SUCCESS) {
            return ret;
        }
    }

    // 标准化数字
    return normalize_number(number);
}

int OracleDataTypes::number_to_string(const oracle_number_t *number, char *str, size_t str_size)
{
    if (!number || !str || str_size == 0) {
        return ORACLE_TYPE_ERROR;
    }

    if (number->is_null) {
        strncpy(str, "NULL", str_size - 1);
        str[str_size - 1] = '\0';
        return ORACLE_TYPE_SUCCESS;
    }

    if (number->is_zero) {
        strncpy(str, "0", str_size - 1);
        str[str_size - 1] = '\0';
        return ORACLE_TYPE_SUCCESS;
    }

    // 检查无穷大
    if (number->mantissa_length == 8 && strcmp((const char*)number->mantissa, "INFINITY") == 0) {
        if (number->is_negative) {
            strncpy(str, "-INFINITY", str_size - 1);
        } else {
            strncpy(str, "INFINITY", str_size - 1);
        }
        str[str_size - 1] = '\0';
        return ORACLE_TYPE_SUCCESS;
    }

    // 构建数字字符串
    char temp_str[64] = {0};
    size_t pos = 0;

    // 添加符号
    if (number->is_negative && pos < sizeof(temp_str) - 1) {
        temp_str[pos++] = '-';
    }

    // 添加数字
    int decimal_pos = number->exponent + 1;
    
    for (int i = 0; i < number->mantissa_length && pos < sizeof(temp_str) - 1; i++) {
        if (i == decimal_pos && decimal_pos > 0 && decimal_pos < number->mantissa_length) {
            temp_str[pos++] = '.';
            if (pos >= sizeof(temp_str) - 1) break;
        }
        temp_str[pos++] = '0' + number->mantissa[i];
    }

    // 处理小数点位置
    if (decimal_pos <= 0) {
        // 需要在前面添加0.00...
        snprintf(str, str_size, "%s0.%0*d%s", 
                number->is_negative ? "-" : "", 
                -decimal_pos, 0, 
                (const char*)number->mantissa);
    } else if (decimal_pos >= number->mantissa_length) {
        // 需要在后面添加0
        snprintf(str, str_size, "%s%.*s%0*d", 
                number->is_negative ? "-" : "",
                number->mantissa_length, (const char*)number->mantissa,
                decimal_pos - number->mantissa_length, 0);
    } else {
        strncpy(str, temp_str, str_size - 1);
        str[str_size - 1] = '\0';
    }

    return ORACLE_TYPE_SUCCESS;
}

// DATE类型处理实现
int OracleDataTypes::decode_date(const uint8_t *data, size_t data_len, oracle_date_t *date)
{
    if (!data || data_len != 7 || !date) {
        return ORACLE_TYPE_ERROR;
    }

    memset(date, 0, sizeof(oracle_date_t));

    // Oracle DATE格式：7字节
    // 字节0: 世纪 + 100
    // 字节1: 年份 + 100
    // 字节2: 月份
    // 字节3: 日期
    // 字节4: 小时 + 1
    // 字节5: 分钟 + 1
    // 字节6: 秒 + 1

    int century = data[0] - OracleTypeConstants::DATE_CENTURY_OFFSET;
    int year_in_century = data[1] - OracleTypeConstants::DATE_YEAR_OFFSET;
    
    date->year = century * 100 + year_in_century;
    date->month = data[2];
    date->day = data[3];
    date->hour = data[4] - OracleTypeConstants::DATE_TIME_OFFSET;
    date->minute = data[5] - OracleTypeConstants::DATE_TIME_OFFSET;
    date->second = data[6] - OracleTypeConstants::DATE_TIME_OFFSET;

    // 验证日期有效性
    if (!is_valid_date(date)) {
        return ORACLE_TYPE_INVALID_DATA;
    }

    return ORACLE_TYPE_SUCCESS;
}

int OracleDataTypes::date_to_string(const oracle_date_t *date, char *str, size_t str_size)
{
    if (!date || !str || str_size < 20) {
        return ORACLE_TYPE_ERROR;
    }

    if (date->is_null) {
        strncpy(str, "NULL", str_size - 1);
        str[str_size - 1] = '\0';
        return ORACLE_TYPE_SUCCESS;
    }

    snprintf(str, str_size, "%04d-%02d-%02d %02d:%02d:%02d",
             date->year, date->month, date->day,
             date->hour, date->minute, date->second);

    return ORACLE_TYPE_SUCCESS;
}

// VARCHAR2/CHAR类型处理实现
int OracleDataTypes::decode_varchar2(const uint8_t *data, size_t data_len, char *str, size_t str_size, size_t *actual_len)
{
    if (!data || !str || str_size == 0) {
        return ORACLE_TYPE_ERROR;
    }

    // VARCHAR2通常直接存储字符串数据
    size_t copy_len = (data_len < str_size - 1) ? data_len : str_size - 1;
    memcpy(str, data, copy_len);
    str[copy_len] = '\0';

    if (actual_len) {
        *actual_len = copy_len;
    }

    return ORACLE_TYPE_SUCCESS;
}

int OracleDataTypes::decode_char(const uint8_t *data, size_t data_len, char *str, size_t str_size, size_t *actual_len)
{
    if (!data || !str || str_size == 0) {
        return ORACLE_TYPE_ERROR;
    }

    // CHAR类型可能包含尾随空格
    size_t copy_len = (data_len < str_size - 1) ? data_len : str_size - 1;
    memcpy(str, data, copy_len);
    str[copy_len] = '\0';

    // 移除尾随空格（CHAR类型特性）
    trim_trailing_spaces(str);

    if (actual_len) {
        *actual_len = strlen(str);
    }

    return ORACLE_TYPE_SUCCESS;
}

// RAW类型处理实现
int OracleDataTypes::decode_raw(const uint8_t *data, size_t data_len, uint8_t *raw, size_t raw_size, size_t *actual_len)
{
    if (!data || !raw || raw_size == 0) {
        return ORACLE_TYPE_ERROR;
    }

    size_t copy_len = (data_len < raw_size) ? data_len : raw_size;
    memcpy(raw, data, copy_len);

    if (actual_len) {
        *actual_len = copy_len;
    }

    return ORACLE_TYPE_SUCCESS;
}

int OracleDataTypes::raw_to_hex_string(const uint8_t *raw, size_t raw_len, char *hex_str, size_t hex_str_size)
{
    if (!raw || !hex_str || hex_str_size < raw_len * 2 + 1) {
        return ORACLE_TYPE_ERROR;
    }

    for (size_t i = 0; i < raw_len; i++) {
        snprintf(hex_str + i * 2, 3, "%02X", raw[i]);
    }

    return ORACLE_TYPE_SUCCESS;
}

// ROWID类型处理实现
int OracleDataTypes::decode_rowid(const uint8_t *data, size_t data_len, oracle_rowid_t *rowid)
{
    if (!data || data_len != 10 || !rowid) {
        return ORACLE_TYPE_ERROR;
    }

    memset(rowid, 0, sizeof(oracle_rowid_t));

    // Oracle ROWID格式：10字节
    // 字节0-3: 对象ID
    // 字节4-5: 文件ID
    // 字节6-9: 块ID和行ID

    rowid->object_id = (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
    rowid->file_id = (data[4] << 8) | data[5];
    rowid->block_id = (data[6] << 16) | (data[7] << 8) | data[8];
    rowid->row_id = data[9];

    return ORACLE_TYPE_SUCCESS;
}

int OracleDataTypes::rowid_to_string(const oracle_rowid_t *rowid, char *str, size_t str_size)
{
    if (!rowid || !str || str_size < 19) {
        return ORACLE_TYPE_ERROR;
    }

    if (rowid->is_null) {
        strncpy(str, "NULL", str_size - 1);
        str[str_size - 1] = '\0';
        return ORACLE_TYPE_SUCCESS;
    }

    // Oracle ROWID字符串格式：OOOOOOFFFBBBBBBRRR
    snprintf(str, str_size, "%06X%03X%06X%03X",
             rowid->object_id, rowid->file_id, rowid->block_id, rowid->row_id);

    return ORACLE_TYPE_SUCCESS;
}

// 通用类型判断和转换
bool OracleDataTypes::is_null_value(const uint8_t *data, size_t data_len, uint8_t type_code)
{
    if (!data || data_len == 0) {
        return true;
    }

    // 不同类型的NULL值表示方式可能不同
    switch (type_code) {
        case ORACLE_SQLT_NUM:
            return (data_len == 1 && data[0] == 0x80);
        case ORACLE_SQLT_CHR:
        case ORACLE_SQLT_VCS:
            return (data_len == 0);
        case ORACLE_SQLT_DAT:
            return (data_len != 7);
        default:
            return (data_len == 0);
    }
}

const char* OracleDataTypes::get_type_name(uint8_t type_code)
{
    switch (type_code) {
        case ORACLE_SQLT_CHR:             return "VARCHAR2";
        case ORACLE_SQLT_NUM:             return "NUMBER";
        case ORACLE_SQLT_INT:             return "INTEGER";
        case ORACLE_SQLT_FLT:             return "FLOAT";
        case ORACLE_SQLT_STR:             return "STRING";
        case ORACLE_SQLT_VNU:             return "VARNUM";
        case ORACLE_SQLT_PDN:             return "PACKED_DECIMAL";
        case ORACLE_SQLT_LNG:             return "LONG";
        case ORACLE_SQLT_VCS:             return "VARCHAR";
        case ORACLE_SQLT_NON:             return "NULL";
        case ORACLE_SQLT_RID:             return "ROWID";
        case ORACLE_SQLT_DAT:             return "DATE";
        case ORACLE_SQLT_VBI:             return "VARRAW";
        case ORACLE_SQLT_BIN:             return "RAW";
        case ORACLE_SQLT_LBI:             return "LONG_RAW";
        case ORACLE_SQLT_UIN:             return "UNSIGNED_INTEGER";
        case ORACLE_SQLT_SLS:             return "SIGN_LEADING_SEPARATE";
        case ORACLE_SQLT_LVC:             return "LONGER_LONGS_CHAR";
        case ORACLE_SQLT_LVB:             return "LONGER_LONGS_BINARY";
        case ORACLE_SQLT_AFC:             return "ANSI_FIXED_CHAR";
        case ORACLE_SQLT_AVC:             return "ANSI_VAR_CHAR";
        case ORACLE_SQLT_BINARY_FLOAT:    return "BINARY_FLOAT";
        case ORACLE_SQLT_BINARY_DOUBLE:   return "BINARY_DOUBLE";
        case ORACLE_SQLT_CUR:             return "CURSOR";
        case ORACLE_SQLT_RDD:             return "ROWID_DESCRIPTOR";
        case ORACLE_SQLT_LAB:             return "LABEL";
        case ORACLE_SQLT_OSL:             return "MLSLABEL";
        case ORACLE_SQLT_NTY:             return "NAMED_OBJECT_TYPE";
        case ORACLE_SQLT_XMLTYPE:         return "XMLTYPE";
        case ORACLE_SQLT_REF:             return "REF";
        case ORACLE_SQLT_CLOB:            return "CLOB";
        case ORACLE_SQLT_BLOB:            return "BLOB";
        case ORACLE_SQLT_BFILEE:          return "BFILE";
        case ORACLE_SQLT_CFILEE:          return "CFILE";
        case ORACLE_SQLT_RSET:            return "RESULT_SET";
        case ORACLE_SQLT_JSON:            return "JSON";
        case ORACLE_SQLT_NCO:             return "NAMED_COLLECTION";
        case ORACLE_SQLT_VST:             return "OCI_STRING_TYPE";
        case ORACLE_SQLT_ODT:             return "OCI_DATE_TYPE";
        case ORACLE_SQLT_TIMESTAMP:       return "TIMESTAMP";
        case ORACLE_SQLT_TIMESTAMP_TZ:    return "TIMESTAMP_WITH_TIME_ZONE";
        case ORACLE_SQLT_INTERVAL_YM:     return "INTERVAL_YEAR_TO_MONTH";
        case ORACLE_SQLT_INTERVAL_DS:     return "INTERVAL_DAY_TO_SECOND";
        case ORACLE_SQLT_TIMESTAMP_LTZ:   return "TIMESTAMP_WITH_LOCAL_TIME_ZONE";
        case ORACLE_SQLT_BOOLEAN:         return "BOOLEAN";
        default:                          return "UNKNOWN";
    }
}

bool OracleDataTypes::is_fixed_length_type(uint8_t type_code)
{
    switch (type_code) {
        case ORACLE_SQLT_DAT:    // DATE: 7字节
        case ORACLE_SQLT_RDD:    // ROWID: 10字节
        case ORACLE_SQLT_INT:    // INTEGER: 固定长度
        case ORACLE_SQLT_FLT:    // FLOAT: 固定长度
            return true;
        default:
            return false;
    }
}

bool OracleDataTypes::is_variable_length_type(uint8_t type_code)
{
    return !is_fixed_length_type(type_code);
}

// 内部辅助函数实现
int OracleDataTypes::parse_number_exponent(uint8_t exp_byte, bool *is_negative, int *exponent)
{
    if (!is_negative || !exponent) {
        return ORACLE_TYPE_ERROR;
    }

    if (exp_byte & 0x80) {
        // 正数
        *is_negative = false;
        *exponent = (exp_byte & 0x7F) - 65;
    } else {
        // 负数
        *is_negative = true;
        *exponent = 62 - (exp_byte & 0x7F);
    }

    return ORACLE_TYPE_SUCCESS;
}

int OracleDataTypes::parse_number_mantissa(const uint8_t *mantissa_data, size_t len, oracle_number_t *number)
{
    if (!mantissa_data || len == 0 || !number) {
        return ORACLE_TYPE_ERROR;
    }

    number->mantissa_length = 0;

    for (size_t i = 0; i < len && number->mantissa_length < 20; i++) {
        uint8_t byte_val = mantissa_data[i];
        
        if (number->is_negative) {
            byte_val = 101 - byte_val;
        } else {
            byte_val = byte_val - 1;
        }

        // 每个字节表示两位数字
        uint8_t high_digit = byte_val / 10;
        uint8_t low_digit = byte_val % 10;

        if (number->mantissa_length < 20) {
            number->mantissa[number->mantissa_length++] = high_digit;
        }
        if (number->mantissa_length < 20) {
            number->mantissa[number->mantissa_length++] = low_digit;
        }
    }

    return ORACLE_TYPE_SUCCESS;
}

int OracleDataTypes::normalize_number(oracle_number_t *number)
{
    if (!number) {
        return ORACLE_TYPE_ERROR;
    }

    // 移除前导零
    while (number->mantissa_length > 1 && number->mantissa[0] == 0) {
        memmove(number->mantissa, number->mantissa + 1, number->mantissa_length - 1);
        number->mantissa_length--;
        number->exponent--;
    }

    // 移除尾随零
    while (number->mantissa_length > 1 && number->mantissa[number->mantissa_length - 1] == 0) {
        number->mantissa_length--;
    }

    // 检查是否为零
    if (number->mantissa_length == 1 && number->mantissa[0] == 0) {
        number->is_zero = true;
        number->is_negative = false;
        number->exponent = 0;
    }

    return ORACLE_TYPE_SUCCESS;
}

bool OracleDataTypes::is_valid_date(const oracle_date_t *date)
{
    if (!date) {
        return false;
    }

    if (date->year < OracleTypeConstants::DATE_MIN_YEAR || 
        date->year > OracleTypeConstants::DATE_MAX_YEAR) {
        return false;
    }

    if (date->month < 1 || date->month > 12) {
        return false;
    }

    if (date->day < 1 || date->day > get_days_in_month(date->year, date->month)) {
        return false;
    }

    if (date->hour < 0 || date->hour > 23) {
        return false;
    }

    if (date->minute < 0 || date->minute > 59) {
        return false;
    }

    if (date->second < 0 || date->second > 59) {
        return false;
    }

    return true;
}

int OracleDataTypes::get_days_in_month(int year, int month)
{
    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    if (month < 1 || month > 12) {
        return 0;
    }

    if (month == 2 && is_leap_year(year)) {
        return 29;
    }

    return days_in_month[month - 1];
}

bool OracleDataTypes::is_leap_year(int year)
{
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

void OracleDataTypes::trim_trailing_spaces(char *str)
{
    if (!str) {
        return;
    }

    size_t len = strlen(str);
    while (len > 0 && str[len - 1] == ' ') {
        str[--len] = '\0';
    }
}
