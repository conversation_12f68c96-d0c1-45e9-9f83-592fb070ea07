/*
 * Oracle高级认证处理器头文件
 * 实现O5Logon、<PERSON><PERSON><PERSON>等高级认证协议的解析
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_AUTH_HANDLER_H__
#define __ORACLE_AUTH_HANDLER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <string>
#include "oracle_parser_common.h"

// 认证处理结果状态
#define AUTH_PARSE_SUCCESS           0
#define AUTH_PARSE_NEED_MORE_DATA    1
#define AUTH_PARSE_ERROR            -1
#define AUTH_PARSE_INVALID_DATA     -2
#define AUTH_PARSE_UNSUPPORTED      -3
#define AUTH_PARSE_AUTH_FAILED      -4
#define AUTH_PARSE_MEMORY_ERROR     -5

// Oracle认证类型
#define ORACLE_AUTH_TYPE_PASSWORD    1  // 密码认证
#define ORACLE_AUTH_TYPE_O3LOGON     2  // O3LOGON认证
#define ORACLE_AUTH_TYPE_O5LOGON     3  // O5LOGON认证
#define ORACLE_AUTH_TYPE_KERBEROS    4  // Kerberos认证
#define ORACLE_AUTH_TYPE_SSL         5  // SSL认证
#define ORACLE_AUTH_TYPE_RADIUS      6  // RADIUS认证
#define ORACLE_AUTH_TYPE_LDAP        7  // LDAP认证
#define ORACLE_AUTH_TYPE_OS          8  // 操作系统认证
#define ORACLE_AUTH_TYPE_PROXY       9  // 代理认证
#define ORACLE_AUTH_TYPE_TOKEN      10  // 令牌认证

// 认证状态
#define ORACLE_AUTH_STATUS_INIT      0  // 初始化
#define ORACLE_AUTH_STATUS_CHALLENGE 1  // 挑战
#define ORACLE_AUTH_STATUS_RESPONSE  2  // 响应
#define ORACLE_AUTH_STATUS_SUCCESS   3  // 成功
#define ORACLE_AUTH_STATUS_FAILED    4  // 失败
#define ORACLE_AUTH_STATUS_EXPIRED   5  // 过期

// 加密算法类型
#define ORACLE_CRYPTO_DES            1  // DES
#define ORACLE_CRYPTO_3DES           2  // 3DES
#define ORACLE_CRYPTO_AES128         3  // AES-128
#define ORACLE_CRYPTO_AES192         4  // AES-192
#define ORACLE_CRYPTO_AES256         5  // AES-256
#define ORACLE_CRYPTO_RC4            6  // RC4
#define ORACLE_CRYPTO_MD5            7  // MD5
#define ORACLE_CRYPTO_SHA1           8  // SHA-1
#define ORACLE_CRYPTO_SHA256         9  // SHA-256

// 认证挑战信息
typedef struct oracle_auth_challenge
{
    uint8_t  challenge_type;        // 挑战类型
    uint32_t challenge_length;      // 挑战长度
    char     *challenge_data;       // 挑战数据
    uint32_t session_key_length;    // 会话密钥长度
    char     *session_key;          // 会话密钥
    uint64_t timestamp;             // 时间戳
    uint32_t sequence_number;       // 序列号
} oracle_auth_challenge_t;

// 认证响应信息
typedef struct oracle_auth_response
{
    uint8_t  response_type;         // 响应类型
    uint32_t response_length;       // 响应长度
    char     *response_data;        // 响应数据
    uint32_t auth_hash_length;      // 认证哈希长度
    char     *auth_hash;            // 认证哈希
    uint32_t client_nonce_length;   // 客户端随机数长度
    char     *client_nonce;         // 客户端随机数
} oracle_auth_response_t;

// O5LOGON认证信息
typedef struct oracle_o5logon_auth
{
    uint8_t  version;               // 版本
    uint8_t  auth_mode;             // 认证模式
    uint32_t salt_length;           // 盐值长度
    char     *salt;                 // 盐值
    uint32_t verifier_length;       // 验证器长度
    char     *verifier;             // 验证器
    uint32_t server_key_length;     // 服务器密钥长度
    char     *server_key;           // 服务器密钥
    uint32_t client_key_length;     // 客户端密钥长度
    char     *client_key;           // 客户端密钥
} oracle_o5logon_auth_t;

// Kerberos认证信息
typedef struct oracle_kerberos_auth
{
    uint8_t  kerberos_version;      // Kerberos版本
    uint32_t ticket_length;         // 票据长度
    char     *ticket_data;          // 票据数据
    uint32_t authenticator_length;  // 认证器长度
    char     *authenticator_data;   // 认证器数据
    char     service_principal[256]; // 服务主体
    char     client_principal[256];  // 客户端主体
    char     realm[128];            // 域
    uint64_t ticket_expiry;         // 票据过期时间
} oracle_kerberos_auth_t;

// SSL认证信息
typedef struct oracle_ssl_auth
{
    uint8_t  ssl_version;           // SSL版本
    uint32_t certificate_length;   // 证书长度
    char     *certificate_data;     // 证书数据
    uint32_t private_key_length;    // 私钥长度
    char     *private_key_data;     // 私钥数据
    char     cipher_suite[64];      // 加密套件
    char     distinguished_name[256]; // 可分辨名称
} oracle_ssl_auth_t;

// 认证会话信息
typedef struct oracle_auth_session
{
    uint32_t session_id;            // 会话ID
    uint8_t  auth_type;             // 认证类型
    uint8_t  auth_status;           // 认证状态
    char     username[128];         // 用户名
    char     schema[128];           // 模式
    char     client_info[256];      // 客户端信息
    
    // 认证数据
    oracle_auth_challenge_t challenge; // 挑战
    oracle_auth_response_t response;   // 响应
    
    // 特定认证类型数据
    union {
        oracle_o5logon_auth_t o5logon;
        oracle_kerberos_auth_t kerberos;
        oracle_ssl_auth_t ssl;
    } auth_data;
    
    // 会话安全信息
    uint8_t  encryption_algorithm;  // 加密算法
    uint8_t  integrity_algorithm;   // 完整性算法
    uint32_t session_key_length;    // 会话密钥长度
    char     *session_key;          // 会话密钥
    
    // 时间信息
    uint64_t auth_start_time;       // 认证开始时间
    uint64_t auth_end_time;         // 认证结束时间
    uint64_t session_timeout;       // 会话超时
    uint64_t last_activity_time;    // 最后活动时间
} oracle_auth_session_t;

// 认证处理器类
class OracleAuthHandler
{
public:
    OracleAuthHandler();
    ~OracleAuthHandler();

    // 认证消息解析
    int parse_auth_message(const char *data, size_t data_len, uint8_t message_type,
                          oracle_status_t *status, oracle_parsed_data_t *result);

    // 认证类型解析
    int parse_password_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session);
    int parse_o3logon_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session);
    int parse_o5logon_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session);
    int parse_kerberos_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session);
    int parse_ssl_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session);

    // O5LOGON认证处理
    int parse_o5logon_challenge(const char *data, size_t data_len, oracle_o5logon_auth_t *o5logon);
    int parse_o5logon_response(const char *data, size_t data_len, oracle_o5logon_auth_t *o5logon);
    int validate_o5logon_verifier(const oracle_o5logon_auth_t *o5logon);
    int extract_o5logon_session_key(const oracle_o5logon_auth_t *o5logon, char **session_key, uint32_t *key_length);

    // Kerberos认证处理
    int parse_kerberos_ticket(const char *data, size_t data_len, oracle_kerberos_auth_t *kerberos);
    int parse_kerberos_authenticator(const char *data, size_t data_len, oracle_kerberos_auth_t *kerberos);
    int validate_kerberos_ticket(const oracle_kerberos_auth_t *kerberos);
    int extract_kerberos_principals(const oracle_kerberos_auth_t *kerberos);

    // SSL认证处理
    int parse_ssl_certificate(const char *data, size_t data_len, oracle_ssl_auth_t *ssl);
    int validate_ssl_certificate(const oracle_ssl_auth_t *ssl);
    int extract_ssl_subject_info(const oracle_ssl_auth_t *ssl);

    // 认证挑战处理
    int parse_auth_challenge(const char *data, size_t data_len, oracle_auth_challenge_t *challenge);
    int generate_auth_response(const oracle_auth_challenge_t *challenge, const char *password, 
                              oracle_auth_response_t *response);
    int validate_auth_response(const oracle_auth_challenge_t *challenge, const oracle_auth_response_t *response);

    // 加密解密处理
    int decrypt_auth_data(const char *encrypted_data, size_t data_len, uint8_t crypto_algorithm,
                         const char *key, size_t key_len, char **decrypted_data, size_t *decrypted_len);
    int encrypt_auth_data(const char *plain_data, size_t data_len, uint8_t crypto_algorithm,
                         const char *key, size_t key_len, char **encrypted_data, size_t *encrypted_len);
    int compute_auth_hash(const char *data, size_t data_len, uint8_t hash_algorithm,
                         char **hash, size_t *hash_len);

    // 会话密钥管理
    int derive_session_key(const oracle_auth_session_t *auth_session, char **session_key, uint32_t *key_length);
    int validate_session_key(const oracle_auth_session_t *auth_session);
    int refresh_session_key(oracle_auth_session_t *auth_session);

    // 认证会话管理
    int create_auth_session(uint32_t session_id, uint8_t auth_type, oracle_auth_session_t **auth_session);
    int update_auth_session_status(oracle_auth_session_t *auth_session, uint8_t new_status);
    int complete_auth_session(oracle_auth_session_t *auth_session);
    void cleanup_auth_session(oracle_auth_session_t *auth_session);

    // 认证安全检查
    int validate_auth_timing(const oracle_auth_session_t *auth_session);
    int check_auth_replay_attack(const oracle_auth_session_t *auth_session);
    int verify_auth_integrity(const oracle_auth_session_t *auth_session);
    int audit_auth_attempt(const oracle_auth_session_t *auth_session, bool success);

    // 内存管理
    void free_auth_challenge(oracle_auth_challenge_t *challenge);
    void free_auth_response(oracle_auth_response_t *response);
    void free_o5logon_auth(oracle_o5logon_auth_t *o5logon);
    void free_kerberos_auth(oracle_kerberos_auth_t *kerberos);
    void free_ssl_auth(oracle_ssl_auth_t *ssl);

    // 工具方法
    const char* get_auth_type_name(uint8_t auth_type);
    const char* get_auth_status_name(uint8_t auth_status);
    const char* get_crypto_algorithm_name(uint8_t crypto_algorithm);
    bool is_auth_type_supported(uint8_t auth_type);
    bool is_strong_auth_type(uint8_t auth_type);

    // 统计信息
    void get_auth_statistics(oracle_auth_stats_t *stats);
    void reset_auth_statistics();
    void update_auth_metrics(const oracle_auth_session_t *auth_session);

    // 调试和日志
    void dump_auth_session(const oracle_auth_session_t *auth_session);
    void dump_auth_challenge(const oracle_auth_challenge_t *challenge);
    void dump_auth_response(const oracle_auth_response_t *response);

    // 配置管理
    void set_auth_timeout(uint64_t timeout_ms) { m_auth_timeout = timeout_ms; }
    void set_max_auth_attempts(uint32_t max_attempts) { m_max_auth_attempts = max_attempts; }
    void set_require_strong_auth(bool require) { m_require_strong_auth = require; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }

private:
    // 内部工具方法
    uint16_t read_uint16_be(const char *data);
    uint32_t read_uint32_be(const char *data);
    uint64_t read_uint64_be(const char *data);

    // 认证数据解析
    int parse_auth_header(const char *data, size_t data_len, size_t *offset, oracle_auth_session_t *auth_session);
    int parse_username_password(const char *data, size_t data_len, size_t *offset, oracle_auth_session_t *auth_session);
    int parse_client_info(const char *data, size_t data_len, size_t *offset, oracle_auth_session_t *auth_session);

    // 加密算法实现
    int des_encrypt(const char *plain, size_t plain_len, const char *key, char **cipher, size_t *cipher_len);
    int des_decrypt(const char *cipher, size_t cipher_len, const char *key, char **plain, size_t *plain_len);
    int aes_encrypt(const char *plain, size_t plain_len, const char *key, size_t key_len, char **cipher, size_t *cipher_len);
    int aes_decrypt(const char *cipher, size_t cipher_len, const char *key, size_t key_len, char **plain, size_t *plain_len);

    // 哈希算法实现
    int md5_hash(const char *data, size_t data_len, char *hash);
    int sha1_hash(const char *data, size_t data_len, char *hash);
    int sha256_hash(const char *data, size_t data_len, char *hash);

    // 安全检查内部方法
    bool is_auth_session_expired(const oracle_auth_session_t *auth_session);
    bool is_replay_attack(const oracle_auth_session_t *auth_session);
    bool validate_auth_sequence(const oracle_auth_session_t *auth_session);

    // 会话管理
    std::map<uint32_t, oracle_auth_session_t*> m_auth_sessions;
    uint32_t m_next_session_id;

    // 安全配置
    uint64_t m_auth_timeout;
    uint32_t m_max_auth_attempts;
    bool m_require_strong_auth;
    bool m_debug_enabled;

    // 统计信息
    uint64_t m_total_auth_attempts;
    uint64_t m_successful_auths;
    uint64_t m_failed_auths;
    uint64_t m_password_auths;
    uint64_t m_o5logon_auths;
    uint64_t m_kerberos_auths;
    uint64_t m_ssl_auths;
};

// 认证统计信息
typedef struct oracle_auth_stats
{
    uint64_t total_attempts;        // 总认证尝试数
    uint64_t successful_auths;      // 成功认证数
    uint64_t failed_auths;          // 失败认证数
    uint64_t password_auths;        // 密码认证数
    uint64_t o3logon_auths;         // O3LOGON认证数
    uint64_t o5logon_auths;         // O5LOGON认证数
    uint64_t kerberos_auths;        // Kerberos认证数
    uint64_t ssl_auths;             // SSL认证数
    uint64_t strong_auths;          // 强认证数
    uint64_t weak_auths;            // 弱认证数
    double   success_rate;          // 成功率
    uint64_t average_auth_time;     // 平均认证时间
} oracle_auth_stats_t;

// 认证工具函数命名空间
namespace OracleAuthUtils
{
    // 认证类型判断
    bool is_password_based_auth(uint8_t auth_type);
    bool is_certificate_based_auth(uint8_t auth_type);
    bool is_token_based_auth(uint8_t auth_type);
    bool requires_encryption(uint8_t auth_type);

    // 安全强度评估
    uint8_t get_auth_strength_level(uint8_t auth_type);
    bool is_secure_auth_method(uint8_t auth_type);
    bool supports_mutual_auth(uint8_t auth_type);

    // 加密算法工具
    bool is_symmetric_algorithm(uint8_t crypto_algorithm);
    bool is_asymmetric_algorithm(uint8_t crypto_algorithm);
    uint32_t get_key_length_for_algorithm(uint8_t crypto_algorithm);
    bool is_deprecated_algorithm(uint8_t crypto_algorithm);
}

#endif /* __ORACLE_AUTH_HANDLER_H__ */
