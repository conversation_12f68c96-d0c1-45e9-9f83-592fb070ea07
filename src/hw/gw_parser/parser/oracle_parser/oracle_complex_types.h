/*
 * Oracle复杂数据类型支持头文件
 * 实现LOB、游标、对象类型、集合类型、JSON、XML等复杂数据类型的处理
 * 优先级3功能：完善复杂数据类型支持
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_COMPLEX_TYPES_H__
#define __ORACLE_COMPLEX_TYPES_H__

#include <inttypes.h>
#include <map>
#include <vector>
#include <string>
#include "oracle_parser_common.h"

// LOB类型定义
typedef enum {
    LOB_TYPE_CLOB,              // 字符LOB
    LOB_TYPE_BLOB,              // 二进制LOB
    LOB_TYPE_BFILE,             // 外部文件LOB
    LOB_TYPE_NCLOB              // 国家字符集LOB
} oracle_lob_type_t;

// LOB定位符结构
typedef struct oracle_lob_locator
{
    oracle_lob_type_t lob_type; // LOB类型
    uint32_t lob_id;            // LOB标识符
    uint64_t lob_length;        // LOB长度
    uint32_t chunk_size;        // 块大小
    bool is_temporary;          // 是否临时LOB
    bool is_open;               // 是否已打开
    char directory_name[128];   // 目录名（BFILE）
    char file_name[256];        // 文件名（BFILE）
    void *lob_data;             // LOB数据缓存
    size_t cached_size;         // 缓存大小
} oracle_lob_locator_t;

// 游标类型定义
typedef enum {
    CURSOR_TYPE_EXPLICIT,       // 显式游标
    CURSOR_TYPE_IMPLICIT,       // 隐式游标
    CURSOR_TYPE_REF_CURSOR,     // REF CURSOR
    CURSOR_TYPE_SYS_REFCURSOR   // SYS_REFCURSOR
} oracle_cursor_type_t;

// 游标结构
typedef struct oracle_cursor_info
{
    oracle_cursor_type_t cursor_type; // 游标类型
    uint32_t cursor_id;         // 游标ID
    char cursor_name[64];       // 游标名称
    bool is_open;               // 是否已打开
    bool is_scrollable;         // 是否可滚动
    uint32_t current_row;       // 当前行号
    uint32_t total_rows;        // 总行数
    oracle_define_variable_t *columns; // 列定义
    uint16_t column_count;      // 列数量
} oracle_cursor_info_t;

// 对象类型定义
typedef struct oracle_object_type
{
    char type_name[128];        // 类型名称
    char schema_name[64];       // 模式名称
    uint16_t attribute_count;   // 属性数量
    struct oracle_object_attribute *attributes; // 属性数组
    uint32_t type_oid;          // 类型OID
    uint16_t type_version;      // 类型版本
} oracle_object_type_t;

// 对象属性定义
typedef struct oracle_object_attribute
{
    char attribute_name[64];    // 属性名称
    uint8_t data_type;          // 数据类型
    uint16_t max_length;        // 最大长度
    uint8_t precision;          // 精度
    uint8_t scale;              // 标度
    bool is_null;               // 是否可为空
    void *default_value;        // 默认值
} oracle_object_attribute_t;

// 集合类型定义
typedef enum {
    COLLECTION_TYPE_VARRAY,     // 变长数组
    COLLECTION_TYPE_NESTED_TABLE, // 嵌套表
    COLLECTION_TYPE_ASSOCIATIVE_ARRAY // 关联数组
} oracle_collection_type_t;

// 集合结构
typedef struct oracle_collection
{
    oracle_collection_type_t collection_type; // 集合类型
    uint8_t element_type;       // 元素数据类型
    uint32_t max_elements;      // 最大元素数（VARRAY）
    uint32_t current_count;     // 当前元素数
    void **elements;            // 元素数组
    size_t *element_sizes;      // 元素大小数组
    bool *element_nulls;        // 元素空值标志
} oracle_collection_t;

// JSON数据类型支持
typedef struct oracle_json_data
{
    char *json_text;            // JSON文本
    size_t json_length;         // JSON长度
    bool is_binary;             // 是否二进制JSON
    uint32_t json_version;      // JSON版本
    void *parsed_json;          // 解析后的JSON对象
} oracle_json_data_t;

// XML数据类型支持
typedef struct oracle_xml_data
{
    char *xml_text;             // XML文本
    size_t xml_length;          // XML长度
    bool is_binary;             // 是否二进制XML
    char encoding[32];          // 编码格式
    void *parsed_xml;           // 解析后的XML对象
} oracle_xml_data_t;

// Oracle复杂类型处理器类
class OracleComplexTypes
{
public:
    OracleComplexTypes();
    ~OracleComplexTypes();

    // LOB类型处理
    int create_lob_locator(oracle_lob_type_t lob_type, uint32_t lob_id, oracle_lob_locator_t **locator);
    int read_lob_data(oracle_lob_locator_t *locator, uint64_t offset, uint32_t length, void **data, size_t *data_size);
    int write_lob_data(oracle_lob_locator_t *locator, uint64_t offset, const void *data, size_t data_size);
    int get_lob_length(oracle_lob_locator_t *locator, uint64_t *length);
    int trim_lob(oracle_lob_locator_t *locator, uint64_t new_length);
    int free_lob_locator(oracle_lob_locator_t *locator);

    // 游标类型处理
    int create_cursor_info(oracle_cursor_type_t cursor_type, uint32_t cursor_id, 
                          const char *cursor_name, oracle_cursor_info_t **cursor_info);
    int open_ref_cursor(oracle_cursor_info_t *cursor_info, const char *sql_text);
    int fetch_cursor_data(oracle_cursor_info_t *cursor_info, uint32_t fetch_rows, void **row_data);
    int close_cursor(oracle_cursor_info_t *cursor_info);
    int free_cursor_info(oracle_cursor_info_t *cursor_info);

    // 对象类型处理
    int create_object_type(const char *type_name, const char *schema_name, oracle_object_type_t **object_type);
    int add_object_attribute(oracle_object_type_t *object_type, const char *attr_name, 
                            uint8_t data_type, uint16_t max_length);
    int create_object_instance(oracle_object_type_t *object_type, void **object_instance);
    int set_object_attribute_value(void *object_instance, const char *attr_name, const void *value, size_t value_size);
    int get_object_attribute_value(void *object_instance, const char *attr_name, void **value, size_t *value_size);
    int free_object_type(oracle_object_type_t *object_type);

    // 集合类型处理
    int create_collection(oracle_collection_type_t collection_type, uint8_t element_type, 
                         uint32_t max_elements, oracle_collection_t **collection);
    int add_collection_element(oracle_collection_t *collection, const void *element, size_t element_size);
    int get_collection_element(oracle_collection_t *collection, uint32_t index, void **element, size_t *element_size);
    int remove_collection_element(oracle_collection_t *collection, uint32_t index);
    int get_collection_count(oracle_collection_t *collection, uint32_t *count);
    int free_collection(oracle_collection_t *collection);

    // JSON类型处理
    int create_json_data(const char *json_text, size_t json_length, oracle_json_data_t **json_data);
    int parse_json_data(oracle_json_data_t *json_data);
    int get_json_value(oracle_json_data_t *json_data, const char *path, void **value, size_t *value_size);
    int set_json_value(oracle_json_data_t *json_data, const char *path, const void *value, size_t value_size);
    int json_to_string(oracle_json_data_t *json_data, char **json_string, size_t *string_length);
    int free_json_data(oracle_json_data_t *json_data);

    // XML类型处理
    int create_xml_data(const char *xml_text, size_t xml_length, const char *encoding, oracle_xml_data_t **xml_data);
    int parse_xml_data(oracle_xml_data_t *xml_data);
    int get_xml_element(oracle_xml_data_t *xml_data, const char *xpath, void **element, size_t *element_size);
    int set_xml_element(oracle_xml_data_t *xml_data, const char *xpath, const void *element, size_t element_size);
    int xml_to_string(oracle_xml_data_t *xml_data, char **xml_string, size_t *string_length);
    int free_xml_data(oracle_xml_data_t *xml_data);

    // 复杂类型绑定变量处理
    int bind_lob_variable(oracle_bind_variable_t *bind_var, oracle_lob_locator_t *lob_locator);
    int bind_cursor_variable(oracle_bind_variable_t *bind_var, oracle_cursor_info_t *cursor_info);
    int bind_object_variable(oracle_bind_variable_t *bind_var, oracle_object_type_t *object_type, void *object_instance);
    int bind_collection_variable(oracle_bind_variable_t *bind_var, oracle_collection_t *collection);
    int bind_json_variable(oracle_bind_variable_t *bind_var, oracle_json_data_t *json_data);
    int bind_xml_variable(oracle_bind_variable_t *bind_var, oracle_xml_data_t *xml_data);

    // 数组绑定（批量绑定）支持
    int create_array_bind(uint8_t data_type, uint32_t array_size, uint32_t element_size, void **array_data);
    int set_array_element(void *array_data, uint32_t index, const void *element, size_t element_size);
    int get_array_element(void *array_data, uint32_t index, void **element, size_t *element_size);
    int bind_array_variable(oracle_bind_variable_t *bind_var, void *array_data, uint32_t array_size);
    int free_array_bind(void *array_data);

    // 类型转换和验证
    int convert_data_type(const void *source_data, uint8_t source_type, 
                         void **target_data, uint8_t target_type, size_t *target_size);
    bool validate_data_type(const void *data, uint8_t data_type, size_t data_size);
    int get_data_type_size(uint8_t data_type, size_t *type_size);
    const char* get_data_type_name(uint8_t data_type);

    // 内存管理优化
    void* allocate_complex_type_memory(size_t size, uint8_t type_hint);
    void free_complex_type_memory(void *ptr, uint8_t type_hint);
    void cleanup_expired_objects(uint32_t timeout_seconds);

    // 调试和诊断
    void dump_lob_locator(const oracle_lob_locator_t *locator);
    void dump_cursor_info(const oracle_cursor_info_t *cursor_info);
    void dump_object_type(const oracle_object_type_t *object_type);
    void dump_collection(const oracle_collection_t *collection);

private:
    // 内部管理方法
    uint32_t generate_lob_id();
    uint32_t generate_cursor_id();
    int validate_lob_locator(const oracle_lob_locator_t *locator);
    int validate_cursor_info(const oracle_cursor_info_t *cursor_info);
    
    // 数据成员
    std::map<uint32_t, oracle_lob_locator_t*> m_lob_locators;     // LOB定位符映射
    std::map<uint32_t, oracle_cursor_info_t*> m_cursor_infos;     // 游标信息映射
    std::map<std::string, oracle_object_type_t*> m_object_types;  // 对象类型映射
    
    uint32_t m_next_lob_id;         // 下一个LOB ID
    uint32_t m_next_cursor_id;      // 下一个游标ID
    
    // 统计信息
    uint64_t m_lob_operations;      // LOB操作次数
    uint64_t m_cursor_operations;   // 游标操作次数
    uint64_t m_object_operations;   // 对象操作次数
    uint64_t m_collection_operations; // 集合操作次数
};

// 复杂类型工具函数
namespace OracleComplexTypesUtils
{
    // LOB工具
    bool is_lob_type(uint8_t data_type);
    oracle_lob_type_t get_lob_type_from_sqlt(uint8_t sqlt_type);
    uint32_t calculate_lob_chunk_size(uint64_t lob_length);
    
    // 游标工具
    bool is_cursor_type(uint8_t data_type);
    oracle_cursor_type_t get_cursor_type_from_sqlt(uint8_t sqlt_type);
    
    // 对象类型工具
    bool is_object_type(uint8_t data_type);
    int parse_object_type_name(const char *full_name, char *schema_name, char *type_name);
    
    // 集合类型工具
    bool is_collection_type(uint8_t data_type);
    oracle_collection_type_t get_collection_type_from_sqlt(uint8_t sqlt_type);
    
    // JSON/XML工具
    bool is_json_type(uint8_t data_type);
    bool is_xml_type(uint8_t data_type);
    bool is_valid_json(const char *json_text, size_t json_length);
    bool is_valid_xml(const char *xml_text, size_t xml_length);
    
    // 数组绑定工具
    uint32_t calculate_array_memory_size(uint8_t element_type, uint32_t array_size, uint32_t element_size);
    int validate_array_bind_parameters(uint8_t data_type, uint32_t array_size, uint32_t element_size);
}

#endif /* __ORACLE_COMPLEX_TYPES_H__ */
