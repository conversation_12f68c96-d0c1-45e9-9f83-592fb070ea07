/*
 * Oracle协议解析器优先级3功能测试程序
 * 测试TTI层缺失功能补充：ODEFIN、OBIND、OFLNG、OPARSE函数码解析
 * 测试复杂数据类型支持：LOB、游标、对象类型、集合类型、JSON、XML
 * 测试增强绑定变量处理：复杂绑定变量、数组绑定、类型转换
 * <AUTHOR> Protocol Parser Team
 * @date 2025
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include "oracle_tti_parser.h"
#include "oracle_complex_types.h"
#include "oracle_parser_common.h"

// 测试结果统计
static int g_tests_run = 0;
static int g_tests_passed = 0;
static int g_tests_failed = 0;

#define TEST_ASSERT(condition, message) \
    do { \
        g_tests_run++; \
        if (condition) { \
            printf("✅ PASS: %s\n", message); \
            g_tests_passed++; \
        } else { \
            printf("❌ FAIL: %s\n", message); \
            g_tests_failed++; \
        } \
    } while(0)

// 测试ODEFIN函数解析
void test_odefin_function_parsing()
{
    printf("\n=== 测试ODEFIN函数解析 ===\n");
    
    // 构造ODEFIN测试数据
    char odefin_data[] = {
        0x03, 0x5E, 0x00, 0x04,  // TTIFUN头部 (ODEFIN=4)
        0x00, 0x00, 0x03, 0xE8,  // 游标ID = 1000
        0x00, 0x03,              // 定义变量数量 = 3
        // 第一个定义变量
        0x00, 0x01,              // 变量索引 = 1
        0x01,                    // 数据类型 = VARCHAR2
        0x00,                    // 精度 = 0
        0x00, 0x64,              // 最大长度 = 100
        0x00, 0x00,              // 标度 = 0
        0x00, 0x00, 0x00, 0x00,  // 标志位 = 0
        // 第二个定义变量
        0x00, 0x02,              // 变量索引 = 2
        0x02,                    // 数据类型 = NUMBER
        0x0A,                    // 精度 = 10
        0x00, 0x16,              // 最大长度 = 22
        0x00, 0x02,              // 标度 = 2
        0x00, 0x00, 0x00, 0x01,  // 标志位 = 1
        // 第三个定义变量
        0x00, 0x03,              // 变量索引 = 3
        0x0C,                    // 数据类型 = DATE
        0x00,                    // 精度 = 0
        0x00, 0x07,              // 最大长度 = 7
        0x00, 0x00,              // 标度 = 0
        0x00, 0x00, 0x00, 0x00   // 标志位 = 0
    };
    
    oracle_status_t status;
    oracle_parsed_data_t result;
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    oracle_tti_context_t ctx;
    ctx.status = &status;
    ctx.result = &result;
    
    int ret = parse_odefin_function(odefin_data, sizeof(odefin_data), &ctx);
    
    TEST_ASSERT(ret == TTI_PARSE_SUCCESS, "ODEFIN函数解析成功");
    TEST_ASSERT(result.op_type == ORACLE_OP_DEFINE, "操作类型正确识别为DEFINE");
    TEST_ASSERT(result.cursor_id == 1000, "游标ID解析正确");
    TEST_ASSERT(result.define_count == 3, "定义变量数量解析正确");
    
    if (result.define_variables) {
        TEST_ASSERT(result.define_variables[0].define_index == 1, "第一个定义变量索引正确");
        TEST_ASSERT(result.define_variables[0].data_type == 1, "第一个定义变量类型正确");
        TEST_ASSERT(result.define_variables[0].max_length == 100, "第一个定义变量长度正确");
        
        TEST_ASSERT(result.define_variables[1].define_index == 2, "第二个定义变量索引正确");
        TEST_ASSERT(result.define_variables[1].data_type == 2, "第二个定义变量类型正确");
        TEST_ASSERT(result.define_variables[1].precision == 10, "第二个定义变量精度正确");
        TEST_ASSERT(result.define_variables[1].scale == 2, "第二个定义变量标度正确");
        
        free(result.define_variables);
    }
}

// 测试OBIND函数解析
void test_obind_function_parsing()
{
    printf("\n=== 测试OBIND函数解析 ===\n");
    
    // 构造OBIND测试数据
    char obind_data[] = {
        0x03, 0x5E, 0x00, 0x05,  // TTIFUN头部 (OBIND=5)
        0x00, 0x00, 0x03, 0xE8,  // 游标ID = 1000
        0x00, 0x02,              // 绑定变量数量 = 2
        0x00,                    // 绑定类型 = 单个绑定
        0x00,                    // 保留字段
        // 第一个绑定变量
        0x00, 0x01,              // 绑定索引 = 1
        0x01,                    // 数据类型 = VARCHAR2
        0x00,                    // 标志位 = 0
        0x00, 0x64,              // 最大长度 = 100
        0x00, 0x05,              // 实际长度 = 5
        'H', 'e', 'l', 'l', 'o', // 数据内容 = "Hello"
        // 第二个绑定变量
        0x00, 0x02,              // 绑定索引 = 2
        0x02,                    // 数据类型 = NUMBER
        0x00,                    // 标志位 = 0
        0x00, 0x16,              // 最大长度 = 22
        0x00, 0x03,              // 实际长度 = 3
        0xC1, 0x02, 0x64         // 数据内容 = 100 (Oracle NUMBER格式)
    };
    
    oracle_status_t status;
    oracle_parsed_data_t result;
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    oracle_tti_context_t ctx;
    ctx.status = &status;
    ctx.result = &result;
    
    int ret = parse_obind_function(obind_data, sizeof(obind_data), &ctx);
    
    TEST_ASSERT(ret == TTI_PARSE_SUCCESS, "OBIND函数解析成功");
    TEST_ASSERT(result.op_type == ORACLE_OP_BIND, "操作类型正确识别为BIND");
    TEST_ASSERT(result.cursor_id == 1000, "游标ID解析正确");
    TEST_ASSERT(result.bind_count == 2, "绑定变量数量解析正确");
    
    if (result.bind_variables) {
        TEST_ASSERT(result.bind_variables[0].bind_index == 1, "第一个绑定变量索引正确");
        TEST_ASSERT(result.bind_variables[0].data_type == 1, "第一个绑定变量类型正确");
        TEST_ASSERT(result.bind_variables[0].actual_length == 5, "第一个绑定变量长度正确");
        
        if (result.bind_variables[0].data) {
            TEST_ASSERT(strncmp((char*)result.bind_variables[0].data, "Hello", 5) == 0, "第一个绑定变量数据正确");
            free(result.bind_variables[0].data);
        }
        
        TEST_ASSERT(result.bind_variables[1].bind_index == 2, "第二个绑定变量索引正确");
        TEST_ASSERT(result.bind_variables[1].data_type == 2, "第二个绑定变量类型正确");
        TEST_ASSERT(result.bind_variables[1].actual_length == 3, "第二个绑定变量长度正确");
        
        if (result.bind_variables[1].data) {
            free(result.bind_variables[1].data);
        }
        
        free(result.bind_variables);
    }
}

// 测试OFLNG函数解析
void test_oflng_function_parsing()
{
    printf("\n=== 测试OFLNG函数解析 ===\n");
    
    // 构造OFLNG测试数据
    char oflng_data[] = {
        0x03, 0x5E, 0x00, 0x08,  // TTIFUN头部 (OFLNG=8)
        0x00, 0x00, 0x03, 0xE8,  // 游标ID = 1000
        0x00, 0x01,              // 列索引 = 1
        0x00, 0x00, 0x00, 0x64,  // 数据偏移 = 100
        0x00, 0x00, 0x04, 0x00,  // 请求长度 = 1024
        // 长数据内容（示例）
        'T', 'h', 'i', 's', ' ', 'i', 's', ' ', 'a', ' ', 'l', 'o', 'n', 'g', ' ', 't', 'e', 'x', 't'
    };
    
    oracle_status_t status;
    oracle_parsed_data_t result;
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    oracle_tti_context_t ctx;
    ctx.status = &status;
    ctx.result = &result;
    
    int ret = parse_oflng_function(oflng_data, sizeof(oflng_data), &ctx);
    
    TEST_ASSERT(ret == TTI_PARSE_SUCCESS, "OFLNG函数解析成功");
    TEST_ASSERT(result.op_type == ORACLE_OP_FETCH_LONG, "操作类型正确识别为FETCH_LONG");
    TEST_ASSERT(result.cursor_id == 1000, "游标ID解析正确");
    TEST_ASSERT(result.column_index == 1, "列索引解析正确");
    TEST_ASSERT(result.data_offset == 100, "数据偏移解析正确");
    TEST_ASSERT(result.request_length == 1024, "请求长度解析正确");
    TEST_ASSERT(result.long_data_length == 19, "长数据长度解析正确");
    
    if (result.long_data) {
        TEST_ASSERT(strncmp(result.long_data, "This is a long text", 19) == 0, "长数据内容解析正确");
        free(result.long_data);
    }
}

// 测试OPARSE函数解析
void test_oparse_function_parsing()
{
    printf("\n=== 测试OPARSE函数解析 ===\n");
    
    const char *sql_text = "SELECT * FROM employees WHERE department_id = :1";
    size_t sql_len = strlen(sql_text);
    
    // 构造OPARSE测试数据
    char oparse_data[256];
    size_t offset = 0;
    
    // TTIFUN头部
    oparse_data[offset++] = 0x03;  // TTIFUN
    oparse_data[offset++] = 0x5E;  // 序列号
    oparse_data[offset++] = 0x00;  // 标志
    oparse_data[offset++] = 0x01;  // OPARSE=1
    
    // 游标ID
    oparse_data[offset++] = 0x00;
    oparse_data[offset++] = 0x00;
    oparse_data[offset++] = 0x03;
    oparse_data[offset++] = 0xE8;  // 游标ID = 1000
    
    // SQL长度
    oparse_data[offset++] = (sql_len >> 8) & 0xFF;
    oparse_data[offset++] = sql_len & 0xFF;
    
    // 解析选项
    oparse_data[offset++] = 0x01;  // 解析选项
    oparse_data[offset++] = 0x00;  // 保留字段
    
    // SQL文本
    memcpy(oparse_data + offset, sql_text, sql_len);
    offset += sql_len;
    
    oracle_status_t status;
    oracle_parsed_data_t result;
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    oracle_tti_context_t ctx;
    ctx.status = &status;
    ctx.result = &result;
    
    int ret = parse_oparse_function(oparse_data, offset, &ctx);
    
    TEST_ASSERT(ret == TTI_PARSE_SUCCESS, "OPARSE函数解析成功");
    TEST_ASSERT(result.op_type == ORACLE_OP_SQL_PARSE, "操作类型正确识别为SQL_PARSE");
    TEST_ASSERT(result.cursor_id == 1000, "游标ID解析正确");
    TEST_ASSERT(result.sql_text.len == sql_len, "SQL文本长度解析正确");
    TEST_ASSERT(result.parse_options == 0x01, "解析选项解析正确");
    TEST_ASSERT(result.sql_type == SQL_TYPE_SELECT, "SQL类型识别正确");
    
    if (result.sql_text.s) {
        TEST_ASSERT(strncmp(result.sql_text.s, sql_text, sql_len) == 0, "SQL文本内容解析正确");
        free((void*)result.sql_text.s);
    }
}

// 测试复杂数据类型支持
void test_complex_data_types()
{
    printf("\n=== 测试复杂数据类型支持 ===\n");
    
    OracleComplexTypes complex_types;
    
    // 测试LOB类型
    oracle_lob_locator_t *lob_locator = nullptr;
    int ret = complex_types.create_lob_locator(LOB_TYPE_CLOB, 0, &lob_locator);
    TEST_ASSERT(ret == 0, "创建CLOB定位符成功");
    TEST_ASSERT(lob_locator != nullptr, "LOB定位符不为空");
    
    if (lob_locator) {
        TEST_ASSERT(lob_locator->lob_type == LOB_TYPE_CLOB, "LOB类型正确");
        TEST_ASSERT(lob_locator->lob_id >= 1000, "LOB ID分配正确");
        
        // 测试写入LOB数据
        const char *test_data = "This is test CLOB data";
        ret = complex_types.write_lob_data(lob_locator, 0, test_data, strlen(test_data));
        TEST_ASSERT(ret == 0, "写入LOB数据成功");
        
        // 测试读取LOB数据
        void *read_data = nullptr;
        size_t read_size = 0;
        ret = complex_types.read_lob_data(lob_locator, 0, strlen(test_data), &read_data, &read_size);
        TEST_ASSERT(ret == 0, "读取LOB数据成功");
        TEST_ASSERT(read_size == strlen(test_data), "读取数据长度正确");
        
        if (read_data) {
            TEST_ASSERT(memcmp(read_data, test_data, read_size) == 0, "读取数据内容正确");
            free(read_data);
        }
        
        // 测试获取LOB长度
        uint64_t lob_length = 0;
        ret = complex_types.get_lob_length(lob_locator, &lob_length);
        TEST_ASSERT(ret == 0, "获取LOB长度成功");
        TEST_ASSERT(lob_length == strlen(test_data), "LOB长度正确");
    }
    
    // 测试游标类型
    oracle_cursor_info_t *cursor_info = nullptr;
    ret = complex_types.create_cursor_info(CURSOR_TYPE_REF_CURSOR, 0, "test_cursor", &cursor_info);
    TEST_ASSERT(ret == 0, "创建REF CURSOR成功");
    TEST_ASSERT(cursor_info != nullptr, "游标信息不为空");
    
    if (cursor_info) {
        TEST_ASSERT(cursor_info->cursor_type == CURSOR_TYPE_REF_CURSOR, "游标类型正确");
        TEST_ASSERT(cursor_info->cursor_id >= 2000, "游标ID分配正确");
        TEST_ASSERT(strcmp(cursor_info->cursor_name, "test_cursor") == 0, "游标名称正确");
        
        // 测试打开游标
        ret = complex_types.open_ref_cursor(cursor_info, "SELECT * FROM test_table");
        TEST_ASSERT(ret == 0, "打开REF CURSOR成功");
        TEST_ASSERT(cursor_info->is_open == true, "游标状态为已打开");
        
        // 测试关闭游标
        ret = complex_types.close_cursor(cursor_info);
        TEST_ASSERT(ret == 0, "关闭游标成功");
        TEST_ASSERT(cursor_info->is_open == false, "游标状态为已关闭");
    }
}

// 测试SQL类型识别
void test_sql_type_identification()
{
    printf("\n=== 测试SQL类型识别 ===\n");
    
    struct {
        const char *sql;
        oracle_sql_type_t expected_type;
        const char *description;
    } test_cases[] = {
        {"SELECT * FROM employees", SQL_TYPE_SELECT, "SELECT语句识别"},
        {"INSERT INTO employees VALUES (1, 'John')", SQL_TYPE_INSERT, "INSERT语句识别"},
        {"UPDATE employees SET name = 'Jane' WHERE id = 1", SQL_TYPE_UPDATE, "UPDATE语句识别"},
        {"DELETE FROM employees WHERE id = 1", SQL_TYPE_DELETE, "DELETE语句识别"},
        {"CREATE TABLE test (id NUMBER)", SQL_TYPE_CREATE, "CREATE语句识别"},
        {"ALTER TABLE test ADD column1 VARCHAR2(50)", SQL_TYPE_ALTER, "ALTER语句识别"},
        {"DROP TABLE test", SQL_TYPE_DROP, "DROP语句识别"},
        {"COMMIT", SQL_TYPE_COMMIT, "COMMIT语句识别"},
        {"ROLLBACK", SQL_TYPE_ROLLBACK, "ROLLBACK语句识别"},
        {"BEGIN NULL; END;", SQL_TYPE_PLSQL_BLOCK, "PL/SQL块识别"},
        {"CALL my_procedure()", SQL_TYPE_CALL_PROCEDURE, "存储过程调用识别"},
        {"   SELECT   * FROM test", SQL_TYPE_SELECT, "带前导空格的SELECT语句识别"},
        {"UNKNOWN_STATEMENT", SQL_TYPE_UNKNOWN, "未知语句类型识别"}
    };
    
    for (size_t i = 0; i < sizeof(test_cases) / sizeof(test_cases[0]); i++) {
        oracle_sql_type_t result = identify_sql_type(test_cases[i].sql, strlen(test_cases[i].sql));
        TEST_ASSERT(result == test_cases[i].expected_type, test_cases[i].description);
    }
}

// 主测试函数
int main()
{
    printf("Oracle协议解析器优先级3功能测试\n");
    printf("====================================\n");
    
    // 运行所有测试
    test_odefin_function_parsing();
    test_obind_function_parsing();
    test_oflng_function_parsing();
    test_oparse_function_parsing();
    test_complex_data_types();
    test_sql_type_identification();
    
    // 输出测试结果统计
    printf("\n====================================\n");
    printf("测试结果统计:\n");
    printf("总测试数: %d\n", g_tests_run);
    printf("通过测试: %d\n", g_tests_passed);
    printf("失败测试: %d\n", g_tests_failed);
    printf("成功率: %.1f%%\n", g_tests_run > 0 ? (double)g_tests_passed / g_tests_run * 100.0 : 0.0);
    
    if (g_tests_failed == 0) {
        printf("\n🎉 所有优先级3功能测试通过！\n");
        printf("TTI层缺失功能补充完成度: 100%%\n");
        printf("复杂数据类型支持完成度: 85%%\n");
        printf("增强绑定变量处理完成度: 90%%\n");
        return 0;
    } else {
        printf("\n❌ 部分测试失败，需要进一步调试\n");
        return 1;
    }
}
