/*
 * Oracle TTI消息层解析器头文件
 * 基于ojdbc源码分析实现的TTI消息解析功能
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_TTI_PARSER_H__
#define __ORACLE_TTI_PARSER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <string>
#include "oracle_parser_common.h"

// TTI解析结果状态
#define TTI_PARSE_SUCCESS           0
#define TTI_PARSE_NEED_MORE_DATA    1
#define TTI_PARSE_ERROR            -1
#define TTI_PARSE_INVALID_DATA     -2
#define TTI_PARSE_UNSUPPORTED      -3
#define TTI_PARSE_MEMORY_ERROR     -4
#define TTI_PARSE_INVALID_FUNCTION -5

// Oracle函数码解析状态
#define ORACLE_FUNCTION_SUCCESS     0
#define ORACLE_FUNCTION_ERROR      -1
#define ORACLE_FUNCTION_UNSUPPORTED -2

// TTI上下文结构
typedef struct oracle_tti_context
{
    uint16_t function_code;         // 当前处理的函数码
    const char *function_name;      // 函数名称
    bool requires_cursor;           // 是否需要游标
    bool modifies_data;             // 是否修改数据
    oracle_status_t *status;        // 连接状态
    oracle_parsed_data_t *result;   // 解析结果
    void *user_data;                // 用户数据
} oracle_tti_context_t;

// Oracle函数码描述符
typedef struct oracle_function_descriptor
{
    uint16_t function_code;         // 函数码
    const char *function_name;      // 函数名称
    int (*parser_func)(const char *data, size_t len, oracle_tti_context_t *ctx);
    bool requires_cursor;           // 是否需要游标
    bool modifies_data;             // 是否修改数据
    const char *description;        // 功能描述
} oracle_function_descriptor_t;

// Oracle绑定变量结构
typedef struct oracle_bind_variable
{
    uint16_t bind_index;            // 绑定索引
    uint8_t  data_type;             // Oracle数据类型
    uint16_t max_length;            // 最大长度
    uint16_t actual_length;         // 实际长度
    void     *data;                 // 数据指针
    bool     is_null;               // 是否为NULL
    uint8_t  precision;             // 精度（数字类型）
    uint8_t  scale;                 // 小数位数（数字类型）
    char     *name;                 // 绑定变量名（如果有）
} oracle_bind_variable_t;

// Oracle定义变量结构（用于SELECT语句的输出列）
typedef struct oracle_define_variable
{
    uint16_t define_index;          // 定义索引
    uint8_t  data_type;             // Oracle数据类型
    uint16_t max_length;            // 最大长度
    char     *column_name;          // 列名
    uint8_t  precision;             // 精度
    uint8_t  scale;                 // 小数位数
    bool     nullable;              // 是否可为空
} oracle_define_variable_t;

// TTI数据类型描述符
typedef struct tti_column_descriptor
{
    uint8_t  data_type;         // Oracle数据类型
    uint16_t max_length;        // 最大长度
    uint8_t  precision;         // 精度
    uint8_t  scale;             // 小数位数
    uint8_t  nullable;          // 是否可为空
    char     column_name[128];  // 列名
} tti_column_descriptor_t;

// TTI结果集元数据
typedef struct tti_resultset_metadata
{
    uint16_t column_count;      // 列数
    tti_column_descriptor_t *columns; // 列描述符数组
} tti_resultset_metadata_t;

// TTI数据行
typedef struct tti_data_row
{
    uint16_t column_count;      // 列数
    b_string_t *column_values;  // 列值数组
    uint8_t *null_indicators;   // NULL指示符数组
} tti_data_row_t;

// TTI结果集
typedef struct tti_resultset
{
    tti_resultset_metadata_t metadata; // 元数据
    uint32_t row_count;         // 行数
    tti_data_row_t *rows;       // 数据行数组
    bool more_rows;             // 是否还有更多行
} tti_resultset_t;

// TTI SQL语句信息
typedef struct tti_sql_info
{
    b_string_t sql_text;        // SQL文本
    uint8_t sql_type;           // SQL类型
    uint32_t cursor_id;         // 游标ID
    uint16_t bind_count;        // 绑定变量数量
    b_string_t *bind_values;    // 绑定变量值
} tti_sql_info_t;

// TTI错误信息
typedef struct tti_error_info
{
    uint32_t error_code;        // 错误码
    b_string_t error_message;   // 错误消息
    uint32_t error_position;    // 错误位置
} tti_error_info_t;

// TTI解析器类
class OracleTtiParser
{
public:
    OracleTtiParser();
    ~OracleTtiParser();

    // TTI消息解析主入口
    int parse_tti_message(const char *data, size_t data_len, uint8_t message_type,
                         oracle_status_t *status, oracle_parsed_data_t *result);

    // Oracle函数码解析方法
    int parse_oracle_function(const char *data, size_t data_len, uint16_t function_code,
                             oracle_tti_context_t *ctx);

    // 具体函数码解析器
    int parse_oall7_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_osql7_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oall8_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_ocommit_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_orollback_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_ofetch_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oopen_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oclose_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oauth_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_ologoff_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oexfet_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oflng_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oparse_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_oexec_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_odefin_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);
    int parse_obind_function(const char *data, size_t data_len, oracle_tti_context_t *ctx);

    // 绑定变量和定义变量处理
    int parse_bind_variables(const char *data, size_t data_len, size_t *offset,
                           oracle_bind_variable_t **bind_vars, uint16_t *bind_count);
    int parse_single_bind_variable(const char *data, size_t data_len, size_t *offset,
                                  oracle_bind_variable_t *bind_var);
    int parse_define_variables(const char *data, size_t data_len, size_t *offset,
                              oracle_define_variable_t **define_vars, uint16_t *define_count);

    // 工具方法
    const oracle_function_descriptor_t* find_function_descriptor(uint16_t function_code);
    int map_function_to_operation_type(uint16_t function_code);
    const char* get_function_name(uint16_t function_code);

    // SQL解析相关方法
    int parse_sql_statement(const char *data, size_t data_len, tti_sql_info_t *sql_info);
    int extract_sql_text(const char *data, size_t data_len, size_t *offset, b_string_t *sql_text);
    int extract_bind_variables(const char *data, size_t data_len, size_t *offset, tti_sql_info_t *sql_info);
    uint8_t determine_sql_type(const b_string_t *sql_text);

    // 结果集解析相关方法
    int parse_resultset_metadata(const char *data, size_t data_len, size_t *offset, tti_resultset_metadata_t *metadata);
    int parse_resultset_data(const char *data, size_t data_len, size_t *offset, tti_resultset_t *resultset);
    int parse_data_row(const char *data, size_t data_len, size_t *offset, 
                      const tti_resultset_metadata_t *metadata, tti_data_row_t *row);

    // Oracle数据类型处理
    int decode_oracle_number(const char *data, size_t data_len, char *result, size_t result_size);
    int decode_oracle_date(const char *data, size_t data_len, char *result, size_t result_size);
    int decode_oracle_varchar(const char *data, size_t data_len, b_string_t *result);
    int decode_oracle_clob(const char *data, size_t data_len, b_string_t *result);
    int decode_oracle_blob(const char *data, size_t data_len, b_string_t *result);
    int decode_oracle_raw(const char *data, size_t data_len, b_string_t *result);

    // 数据类型转换
    const char* get_oracle_type_name(uint8_t type_code);
    bool is_numeric_type(uint8_t type_code);
    bool is_string_type(uint8_t type_code);
    bool is_date_type(uint8_t type_code);
    bool is_lob_type(uint8_t type_code);

    // 错误处理
    int parse_error_message(const char *data, size_t data_len, tti_error_info_t *error_info);
    int handle_oracle_error(const tti_error_info_t *error_info, oracle_status_t *status);

    // 游标操作
    int parse_cursor_open(const char *data, size_t data_len, uint32_t *cursor_id);
    int parse_cursor_close(const char *data, size_t data_len, uint32_t *cursor_id);
    int parse_cursor_fetch(const char *data, size_t data_len, uint32_t *cursor_id, uint32_t *fetch_count);

    // 事务操作
    int parse_commit_response(const char *data, size_t data_len, oracle_status_t *status);
    int parse_rollback_response(const char *data, size_t data_len, oracle_status_t *status);

    // 认证相关
    int parse_auth_response(const char *data, size_t data_len, oracle_status_t *status);
    int parse_session_info(const char *data, size_t data_len, oracle_status_t *status);

    // 内存管理
    void free_resultset(tti_resultset_t *resultset);
    void free_sql_info(tti_sql_info_t *sql_info);
    void free_error_info(tti_error_info_t *error_info);

    // 调试和日志
    void dump_sql_info(const tti_sql_info_t *sql_info);
    void dump_resultset_metadata(const tti_resultset_metadata_t *metadata);
    void dump_data_row(const tti_data_row_t *row, const tti_resultset_metadata_t *metadata);
    void enable_debug(bool enable) { m_debug_enabled = enable; }

private:
    // 内部工具方法
    uint8_t read_uint8(const char *data);
    uint16_t read_uint16(const char *data);
    uint32_t read_uint32(const char *data);
    uint64_t read_uint64(const char *data);
    
    // TTI数据读取
    int read_tti_length(const char *data, size_t data_len, size_t *offset, uint32_t *length);
    int read_tti_string(const char *data, size_t data_len, size_t *offset, b_string_t *str);
    int read_tti_number(const char *data, size_t data_len, size_t *offset, char *number, size_t number_size);
    int read_tti_date(const char *data, size_t data_len, size_t *offset, char *date, size_t date_size);
    
    // Oracle NUMBER解码
    int decode_number_internal(const uint8_t *data, size_t len, char *result, size_t result_size);
    int convert_oracle_exponent(uint8_t exp_byte);
    int convert_oracle_mantissa(const uint8_t *mantissa, size_t len, char *digits, size_t digits_size);
    
    // Oracle DATE解码
    int decode_date_internal(const uint8_t *data, size_t len, char *result, size_t result_size);
    int convert_oracle_century_year(uint8_t century, uint8_t year);
    
    // SQL类型识别
    uint8_t identify_dml_type(const char *sql, size_t len);
    uint8_t identify_ddl_type(const char *sql, size_t len);
    uint8_t identify_dcl_type(const char *sql, size_t len);
    
    // 列描述符解析
    int parse_column_descriptor(const char *data, size_t data_len, size_t *offset, tti_column_descriptor_t *desc);
    int extract_column_name(const char *data, size_t data_len, size_t *offset, char *name, size_t name_size);
    
    // 数据值解析
    int parse_column_value(const char *data, size_t data_len, size_t *offset, 
                          const tti_column_descriptor_t *desc, b_string_t *value, uint8_t *null_indicator);
    
    // 内部状态
    bool m_debug_enabled;
    std::map<uint32_t, tti_resultset_metadata_t> m_cursor_metadata;
    size_t m_bytes_processed;
};

// TTI工具函数
namespace OracleTtiUtils
{
    // SQL类型判断
    bool is_select_statement(const char *sql, size_t len);
    bool is_dml_statement(const char *sql, size_t len);
    bool is_ddl_statement(const char *sql, size_t len);
    bool is_transaction_statement(const char *sql, size_t len);
    
    // 数据类型工具
    size_t get_oracle_type_max_length(uint8_t type_code);
    bool is_variable_length_type(uint8_t type_code);
    bool requires_length_prefix(uint8_t type_code);
    
    // 字符串工具
    void trim_whitespace(char *str);
    void to_uppercase(char *str);
    bool starts_with_ignore_case(const char *str, const char *prefix);
    
    // 数值转换工具
    bool is_valid_oracle_number(const char *data, size_t len);
    bool is_valid_oracle_date(const char *data, size_t len);
    
    // Oracle错误码映射
    const char* map_oracle_error_to_string(uint32_t error_code);
    bool is_recoverable_error(uint32_t error_code);
}

#endif /* __ORACLE_TTI_PARSER_H__ */
