/*
 * Oracle协议解析器
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_PARSER_H__
#define __ORACLE_PARSER_H__

#include <string>
#include <memory.h>
#include <vector>
#include <map>
#include <inttypes.h>
#include <arpa/inet.h>

#include "gw_i_parser.h"
#include "oracle_parser_common.h"
#include "gw_ver.h"
#include "../../include/utils.h"
#include "gw_i_upload.h"

// 前向声明
class OracleTnsParser;
class OracleTtcParser;
class OracleTtiParser;
class OracleVersionCompat;

#define ORACLEPARSER_VER GW_VER_STRING(GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION)

#define ORACLE_PARSER_WQ_ORACLE_PARSER_MSG 0
#define ORACLE_PARSER_WQ_UPSTREAM 1
#define ORACLE_PARSER_WQ_MAX_NUM 4

// 统计信息结构
typedef struct stats_oracle_parser
{
    volatile uint64_t cnt_session_total;
    volatile uint64_t cnt_session_closed;
    
    volatile uint64_t cnt_parser_total;
    volatile uint64_t cnt_parser_remaining;
    volatile uint64_t cnt_parser_matched;
    
    volatile uint64_t cnt_login;
    volatile uint64_t cnt_logout;
    volatile uint64_t cnt_select;
    volatile uint64_t cnt_dml;
    volatile uint64_t cnt_ddl;
    volatile uint64_t cnt_commit;
    volatile uint64_t cnt_rollback;
} stats_oracle_t;

class CSession;
class CWorkerQueue;

struct cJSON;

class COracleParser : public CParser
{
public:
    COracleParser(void);
    virtual ~COracleParser(void);

public:
    virtual void cache_clean();
    
    // 协议探测方法
    virtual bool probe(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual bool probe_on_close(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual bool probe_on_reset(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    
    // 协议解析方法
    virtual int parse(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual int parse_clear(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual int parse_on_close(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual int parse_on_reset(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    
    // 数据获取和处理方法
    virtual const char *get_data(const struct StreamData *, int dir, int *data_len, int *offset_out);
    virtual bool discard(struct StreamData *, int dir, int num);
    virtual bool discard_and_update(struct StreamData *, int dir, int num);
    virtual void del_session_stream(StreamData *);
    virtual void del_session_param(SessionMgtData *);
    
    // 生命周期管理
    virtual void init();
    virtual void fini();
    virtual void run();
    
    // 信息获取
    virtual const char *get_name(void) const;
    virtual const char *get_version(void) const;
    virtual void set_gw_common(CGwCommon *comm);
    
    // 配置管理
    virtual bool load_conf(const char *);
    virtual void set_quit_signal(void);
    virtual void wait_for_stop(void);
    
    // 过滤规则设置
    virtual void set_accout_filter_rule(CFilterRule *rule);
    virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule);
    virtual void set_url_filter_rule(CFilterRule *rule);
    
    // 上游协议处理
    virtual void add_upstream(CParser *parser);
    virtual void reset_upstream(void);
    virtual void push_upstream_msg(char *s, size_t length);
    
    // 状态查询
    virtual bool is_parsed(const struct StreamData *) const;
    virtual struct StreamData *clone_stream_data(const struct StreamData *);
    virtual uint64_t get_parser_http_cnt();
    virtual uint64_t get_succ_parser_http_cnt();
    virtual void* get_parser_status();
    
    // 新增纯虚函数实现
    virtual void set_parser_type(int type);
    virtual void read_conf_urlbase_for_mon();
    virtual void read_conf_filetype_for_mon();
    virtual void get_log_buf(char *log_buf, size_t log_buf_len) const;
    virtual uint32_t parser_status() const;
    
    // 统计信息打印
    virtual void print_stats();
    
    void free_worker_queue(CWorkerQueue *p);

private:
    // 内部工具方法
    StreamData *get_stream_data_from_session(CSession *p_session, int dir);
    bool is_oracle_protocol(CSession *p_sess, int dir);
    void keep_bstring_data(b_string_t *bstr);
    void set_ip_port(const struct conn *p_conn, oracle_status_t *p_status);
    void free_oracle_parsed_data_in_session(oracle_stream_t *p_stream, int a_dir);
    
    // 协议解析方法
    int parse_tns_packet(const char *pkt, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_ttc_message(const char *data, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_tti_message(const char *data, size_t len, uint8_t message_type, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);

    // 兼容性方法（保留原有接口）
    int parse_connect_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status);
    int parse_data_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_login_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_sql_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    
    // 数据组装和发送
    oracle_parsed_data_t *oracle_parsed_data_merge(oracle_parsed_data_t *p_dst, oracle_parsed_data_t *p_src, int src_dir);
    char *bstring_to_cjson_str(const b_string_t *bstr);
    char *oracle_json_dump_event(const oracle_parsed_data_t *p_opd);
    void oracle_send_data(const oracle_parsed_data_t *p_result);
    
    // 会话管理
    void free_session_oracle_parser_data(oracle_stream_t *p_stream, int dir, oracle_parsed_data_t *p_data);
    bool oracle_parser_merge(oracle_stream_t *p_stream, oracle_parsed_data_t *p_data, int from_dir);
    void insert_into_parser_header(oracle_stream_t *p_stream, int dir, oracle_half_stream_t *p_ohs);
    
    // 消息上传
    void oracle_cb_upload_msg(const char *s, const char* msgtype);
    static void free_upload_msg_cb(const struct UploadMsg *p_um);
    
    // 调试工具
    void print_mem(const void * mem, size_t size);
    
    // 数据读取工具函数
    inline uint16_t read_uint2(const char *A)
    {
        return (uint16_t)(((uint8_t)(A[0])) + ((uint8_t)(A[1]) << 8));
    }
    
    inline uint32_t read_uint4(const char *A)
    {
        return (uint32_t)(((uint8_t)(A[0])) +
                        (((uint8_t)(A[1])) << 8) +
                        (((uint8_t)(A[2])) << 16) +
                        (((uint8_t)(A[3])) << 24));
    }
    
    inline uint64_t read_uint8(const char *A)
    {
        return ((uint64_t)(((uint8_t)(A[0])) +
                        (((uint8_t)(A[1])) << 8) +
                        (((uint8_t)(A[2])) << 16) +
                        (((uint8_t)(A[3])) << 24)) +
            (((uint64_t)(((uint8_t)(A[4])) +
                        (((uint8_t)(A[5])) << 8) +
                        (((uint8_t)(A[6])) << 16) +
                        (((uint8_t)(A[7])) << 24))) << 32));
    }
    
    // TNS包长度读取
    inline uint32_t read_tns_packet_length(const char *A)
    {
        return (uint32_t)((uint8_t)(A[0]) + ((uint8_t)(A[1]) << 8) + ((uint8_t)(A[2]) << 16));
    }

    void oracle_check_show_info(CSession *p_session, const struct conn *pcon);
    static void print_oracle_stats_callback(void *p);
    void print_oracle_stats(void);

protected:
    CGwCommon *m_comm;
    volatile int m_quit_signal;
    char m_name[32];

protected:
    CWorkerQueue *m_p_wq[ORACLE_PARSER_WQ_MAX_NUM];

private:
    stats_oracle_t m_stats_oracle;
    CUpload *m_pUpload;

    // Oracle协议解析器组件
    OracleTnsParser *m_tns_parser;
    OracleTtcParser *m_ttc_parser;
    OracleTtiParser *m_tti_parser;
    OracleVersionCompat *m_version_compat;

private:
    int m_conf_oracle_parser_queue_max_num;
    int m_conf_oracle_parser_thread_num;
    uint64_t m_conf_oracle_parser_queue_memory_max_size_bytes;
    int m_conf_run_mode;
    int m_conf_pcap_timestamp;
    std::string m_conf_upload_name;
    int m_conf_oracle_upstream_thread_num;
    int m_conf_oracle_upstream_queue_max_num;
    uint64_t m_conf_oracle_upstream_queue_memory_max_size_bytes;
    int m_conf_upstream;
    std::vector<CParser *> m_vec_upstream_parser;
};

#endif /* __ORACLE_PARSER_H__ */