/*
 * Oracle协议解析器内存管理器
 * 确保所有内存分配和释放的正确配对
 * 提供内存泄漏检测和统计功能
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_MEMORY_MANAGER_H__
#define __ORACLE_MEMORY_MANAGER_H__

#include <inttypes.h>
#include <map>
#include <mutex>
#include <string>

// 内存管理状态
#define MEMORY_SUCCESS           0
#define MEMORY_ERROR            -1
#define MEMORY_LEAK_DETECTED    -2
#define MEMORY_DOUBLE_FREE      -3
#define MEMORY_INVALID_POINTER  -4

// 内存分配类型
#define MEMORY_TYPE_GENERAL     0
#define MEMORY_TYPE_TNS_PACKET  1
#define MEMORY_TYPE_TTC_MESSAGE 2
#define MEMORY_TYPE_AUTH_DATA   3
#define MEMORY_TYPE_LOB_DATA    4
#define MEMORY_TYPE_BATCH_DATA  5
#define MEMORY_TYPE_TEMP        6

// 内存分配信息
typedef struct memory_allocation_info
{
    void     *ptr;              // 内存指针
    size_t   size;              // 分配大小
    uint8_t  type;              // 分配类型
    const char *file;           // 分配文件
    int      line;              // 分配行号
    const char *function;       // 分配函数
    uint64_t timestamp;         // 分配时间戳
    uint32_t allocation_id;     // 分配ID
} memory_allocation_info_t;

// 内存统计信息
typedef struct memory_statistics
{
    uint64_t total_allocations;     // 总分配次数
    uint64_t total_deallocations;   // 总释放次数
    uint64_t current_allocations;   // 当前分配数
    uint64_t peak_allocations;      // 峰值分配数
    uint64_t total_bytes_allocated; // 总分配字节数
    uint64_t total_bytes_freed;     // 总释放字节数
    uint64_t current_bytes_used;    // 当前使用字节数
    uint64_t peak_bytes_used;       // 峰值使用字节数
    uint64_t memory_leaks;          // 内存泄漏数
    uint64_t double_frees;          // 重复释放数
    uint64_t invalid_frees;         // 无效释放数
} memory_statistics_t;

// Oracle内存管理器类
class OracleMemoryManager
{
public:
    static OracleMemoryManager& getInstance();
    
    // 内存分配和释放
    void* allocate_memory(size_t size, uint8_t type, const char *file, int line, const char *function);
    int free_memory(void *ptr, const char *file, int line, const char *function);
    void* reallocate_memory(void *ptr, size_t new_size, const char *file, int line, const char *function);
    
    // 内存检查
    bool is_valid_pointer(void *ptr);
    bool is_allocated_pointer(void *ptr);
    size_t get_allocation_size(void *ptr);
    uint8_t get_allocation_type(void *ptr);
    
    // 内存泄漏检测
    int detect_memory_leaks();
    int report_memory_leaks(char *report, size_t report_size);
    void dump_allocation_info(void *ptr);
    void dump_all_allocations();
    
    // 统计信息
    void get_memory_statistics(memory_statistics_t *stats);
    void reset_statistics();
    void print_memory_report();
    
    // 配置管理
    void set_leak_detection_enabled(bool enabled) { m_leak_detection_enabled = enabled; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }
    void set_max_allocations(uint64_t max_allocs) { m_max_allocations = max_allocs; }
    
    // 清理和关闭
    void cleanup_all_allocations();
    void shutdown();

private:
    OracleMemoryManager();
    ~OracleMemoryManager();
    
    // 禁用拷贝构造和赋值
    OracleMemoryManager(const OracleMemoryManager&) = delete;
    OracleMemoryManager& operator=(const OracleMemoryManager&) = delete;
    
    // 内部方法
    uint32_t generate_allocation_id();
    uint64_t get_current_timestamp();
    const char* get_allocation_type_name(uint8_t type);
    void update_statistics_on_alloc(size_t size);
    void update_statistics_on_free(size_t size);
    
    // 数据成员
    std::map<void*, memory_allocation_info_t> m_allocations;
    std::mutex m_mutex;
    memory_statistics_t m_statistics;
    uint32_t m_next_allocation_id;
    bool m_leak_detection_enabled;
    bool m_debug_enabled;
    uint64_t m_max_allocations;
    bool m_shutdown_called;
};

// 内存管理宏定义
#define ORACLE_MALLOC(size, type) \
    OracleMemoryManager::getInstance().allocate_memory(size, type, __FILE__, __LINE__, __FUNCTION__)

#define ORACLE_FREE(ptr) \
    OracleMemoryManager::getInstance().free_memory(ptr, __FILE__, __LINE__, __FUNCTION__)

#define ORACLE_REALLOC(ptr, size) \
    OracleMemoryManager::getInstance().reallocate_memory(ptr, size, __FILE__, __LINE__, __FUNCTION__)

// 类型特定的内存分配宏
#define ORACLE_MALLOC_TNS(size) \
    ORACLE_MALLOC(size, MEMORY_TYPE_TNS_PACKET)

#define ORACLE_MALLOC_TTC(size) \
    ORACLE_MALLOC(size, MEMORY_TYPE_TTC_MESSAGE)

#define ORACLE_MALLOC_AUTH(size) \
    ORACLE_MALLOC(size, MEMORY_TYPE_AUTH_DATA)

#define ORACLE_MALLOC_LOB(size) \
    ORACLE_MALLOC(size, MEMORY_TYPE_LOB_DATA)

#define ORACLE_MALLOC_BATCH(size) \
    ORACLE_MALLOC(size, MEMORY_TYPE_BATCH_DATA)

#define ORACLE_MALLOC_TEMP(size) \
    ORACLE_MALLOC(size, MEMORY_TYPE_TEMP)

// 安全字符串复制宏
#define ORACLE_SAFE_STRDUP(str, type) \
    oracle_safe_strdup(str, type, __FILE__, __LINE__, __FUNCTION__)

#define ORACLE_SAFE_STRNDUP(str, n, type) \
    oracle_safe_strndup(str, n, type, __FILE__, __LINE__, __FUNCTION__)

// 内存管理工具函数
namespace OracleMemoryUtils
{
    // 安全字符串操作
    char* oracle_safe_strdup(const char *str, uint8_t type, const char *file, int line, const char *function);
    char* oracle_safe_strndup(const char *str, size_t n, uint8_t type, const char *file, int line, const char *function);
    
    // 安全内存操作
    void* oracle_safe_memcpy(void *dest, const void *src, size_t n);
    void* oracle_safe_memmove(void *dest, const void *src, size_t n);
    int oracle_safe_memcmp(const void *s1, const void *s2, size_t n);
    void oracle_safe_memset(void *s, int c, size_t n);
    
    // 内存对齐
    void* oracle_aligned_alloc(size_t alignment, size_t size, uint8_t type, const char *file, int line, const char *function);
    void oracle_aligned_free(void *ptr, const char *file, int line, const char *function);
    
    // 内存池管理
    void* oracle_pool_alloc(size_t size, uint8_t type);
    void oracle_pool_free(void *ptr);
    void oracle_pool_cleanup();
    
    // 内存使用分析
    double calculate_memory_efficiency();
    size_t get_memory_fragmentation();
    bool is_memory_pressure_high();
    
    // 内存调试
    void dump_memory_map();
    void check_heap_integrity();
    bool validate_memory_pattern(void *ptr, size_t size, uint8_t pattern);
}

// 智能指针类模板（用于自动内存管理）
template<typename T>
class OracleSmartPtr
{
public:
    explicit OracleSmartPtr(T* ptr = nullptr, uint8_t type = MEMORY_TYPE_GENERAL) 
        : m_ptr(ptr), m_type(type) {}
    
    ~OracleSmartPtr() {
        if (m_ptr) {
            ORACLE_FREE(m_ptr);
        }
    }
    
    // 禁用拷贝构造和赋值
    OracleSmartPtr(const OracleSmartPtr&) = delete;
    OracleSmartPtr& operator=(const OracleSmartPtr&) = delete;
    
    // 移动构造和赋值
    OracleSmartPtr(OracleSmartPtr&& other) noexcept 
        : m_ptr(other.m_ptr), m_type(other.m_type) {
        other.m_ptr = nullptr;
    }
    
    OracleSmartPtr& operator=(OracleSmartPtr&& other) noexcept {
        if (this != &other) {
            if (m_ptr) {
                ORACLE_FREE(m_ptr);
            }
            m_ptr = other.m_ptr;
            m_type = other.m_type;
            other.m_ptr = nullptr;
        }
        return *this;
    }
    
    // 访问操作
    T* get() const { return m_ptr; }
    T& operator*() const { return *m_ptr; }
    T* operator->() const { return m_ptr; }
    
    // 释放所有权
    T* release() {
        T* ptr = m_ptr;
        m_ptr = nullptr;
        return ptr;
    }
    
    // 重置指针
    void reset(T* ptr = nullptr) {
        if (m_ptr) {
            ORACLE_FREE(m_ptr);
        }
        m_ptr = ptr;
    }
    
    // 布尔转换
    explicit operator bool() const {
        return m_ptr != nullptr;
    }

private:
    T* m_ptr;
    uint8_t m_type;
};

// 类型别名
using OracleCharPtr = OracleSmartPtr<char>;
using OracleBytePtr = OracleSmartPtr<uint8_t>;

// 内存管理异常类
class OracleMemoryException : public std::exception
{
public:
    explicit OracleMemoryException(const std::string& message) : m_message(message) {}
    virtual const char* what() const noexcept override { return m_message.c_str(); }

private:
    std::string m_message;
};

#endif /* __ORACLE_MEMORY_MANAGER_H__ */
