/*
 * Oracle存储过程调用处理器头文件
 * 实现存储过程、函数调用的参数解析和结果处理
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_PROCEDURE_HANDLER_H__
#define __ORACLE_PROCEDURE_HANDLER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <string>
#include "oracle_parser_common.h"

// 存储过程处理结果状态
#define PROC_PARSE_SUCCESS           0
#define PROC_PARSE_NEED_MORE_DATA    1
#define PROC_PARSE_ERROR            -1
#define PROC_PARSE_INVALID_DATA     -2
#define PROC_PARSE_UNSUPPORTED      -3
#define PROC_PARSE_MEMORY_ERROR     -4

// 存储过程调用类型
#define ORACLE_PROC_TYPE_PROCEDURE   1  // 存储过程
#define ORACLE_PROC_TYPE_FUNCTION    2  // 函数
#define ORACLE_PROC_TYPE_PACKAGE     3  // 包过程/函数
#define ORACLE_PROC_TYPE_ANONYMOUS   4  // 匿名块

// 参数方向
#define ORACLE_PARAM_DIR_IN          1  // 输入参数
#define ORACLE_PARAM_DIR_OUT         2  // 输出参数
#define ORACLE_PARAM_DIR_INOUT       3  // 输入输出参数
#define ORACLE_PARAM_DIR_RETURN      4  // 返回值

// 参数传递模式
#define ORACLE_PARAM_MODE_BY_VALUE   1  // 按值传递
#define ORACLE_PARAM_MODE_BY_REF     2  // 按引用传递
#define ORACLE_PARAM_MODE_NOCOPY     3  // NOCOPY模式

// 存储过程执行状态
#define ORACLE_PROC_STATUS_PENDING   0
#define ORACLE_PROC_STATUS_EXECUTING 1
#define ORACLE_PROC_STATUS_COMPLETED 2
#define ORACLE_PROC_STATUS_FAILED    3
#define ORACLE_PROC_STATUS_TIMEOUT   4

// 存储过程参数信息
typedef struct oracle_procedure_parameter
{
    uint16_t param_index;           // 参数索引
    char     param_name[128];       // 参数名
    uint8_t  param_direction;       // 参数方向
    uint8_t  param_mode;            // 传递模式
    uint8_t  data_type;             // 数据类型
    uint16_t max_length;            // 最大长度
    uint8_t  precision;             // 精度
    uint8_t  scale;                 // 小数位数
    bool     is_nullable;           // 是否可为空
    bool     has_default;           // 是否有默认值
    
    // 参数值
    oracle_value_t input_value;     // 输入值
    oracle_value_t output_value;    // 输出值
    oracle_value_t default_value;   // 默认值
    
    // 元数据
    char     type_name[64];         // 类型名称
    char     type_schema[64];       // 类型模式
    uint32_t type_oid;              // 类型OID
} oracle_procedure_parameter_t;

// 存储过程调用信息
typedef struct oracle_procedure_call
{
    uint8_t  call_type;             // 调用类型
    uint8_t  call_status;           // 调用状态
    uint32_t call_id;               // 调用ID
    
    // 过程信息
    b_string_t procedure_name;      // 过程名
    b_string_t schema_name;         // 模式名
    b_string_t package_name;        // 包名
    b_string_t full_name;           // 完整名称
    
    // 参数信息
    uint16_t parameter_count;       // 参数数量
    oracle_procedure_parameter_t *parameters; // 参数数组
    
    // 返回值信息
    bool     has_return_value;      // 是否有返回值
    oracle_value_t return_value;    // 返回值
    
    // 执行信息
    uint64_t start_time;            // 开始时间
    uint64_t end_time;              // 结束时间
    uint64_t execution_time;        // 执行时间
    uint32_t cpu_time;              // CPU时间
    uint32_t io_time;               // IO时间
    
    // 错误信息
    uint32_t error_code;            // 错误码
    char     error_message[512];    // 错误消息
    uint32_t error_position;        // 错误位置
    
    // 统计信息
    uint32_t logical_reads;         // 逻辑读
    uint32_t physical_reads;        // 物理读
    uint32_t buffer_gets;           // 缓冲区获取
    uint32_t rows_processed;        // 处理行数
} oracle_procedure_call_t;

// 存储过程结果集
typedef struct oracle_procedure_resultset
{
    uint16_t cursor_id;             // 游标ID
    uint16_t column_count;          // 列数
    tti_column_descriptor_t *columns; // 列描述符
    uint32_t row_count;             // 行数
    tti_data_row_t *rows;           // 数据行
    bool     more_rows;             // 是否还有更多行
} oracle_procedure_resultset_t;

// 存储过程会话
typedef struct oracle_procedure_session
{
    uint32_t session_id;            // 会话ID
    uint32_t active_calls;          // 活跃调用数
    uint64_t total_calls;           // 总调用数
    uint64_t successful_calls;      // 成功调用数
    uint64_t failed_calls;          // 失败调用数
    uint64_t total_execution_time;  // 总执行时间
    std::map<uint32_t, oracle_procedure_call_t*> active_procedures; // 活跃过程
} oracle_procedure_session_t;

// 存储过程处理器类
class OracleProcedureHandler
{
public:
    OracleProcedureHandler();
    ~OracleProcedureHandler();

    // 存储过程调用解析
    int parse_procedure_call(const char *data, size_t data_len, uint8_t message_type,
                            oracle_status_t *status, oracle_parsed_data_t *result);

    // 存储过程调用类型解析
    int parse_procedure_call_message(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int parse_function_call_message(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int parse_package_call_message(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int parse_anonymous_block_message(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);

    // 参数解析
    int parse_procedure_parameters(const char *data, size_t data_len, size_t *offset, oracle_procedure_call_t *proc_call);
    int parse_parameter_metadata(const char *data, size_t data_len, size_t *offset, oracle_procedure_parameter_t *param);
    int parse_parameter_value(const char *data, size_t data_len, size_t *offset, oracle_procedure_parameter_t *param);

    // 输入参数处理
    int parse_input_parameters(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int validate_input_parameter(const oracle_procedure_parameter_t *param);
    int convert_input_parameter(oracle_procedure_parameter_t *param);

    // 输出参数处理
    int parse_output_parameters(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int extract_output_parameter(const char *data, size_t data_len, size_t *offset, oracle_procedure_parameter_t *param);
    int convert_output_parameter(oracle_procedure_parameter_t *param);

    // 返回值处理
    int parse_return_value(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int extract_function_return_value(const char *data, size_t data_len, oracle_value_t *return_value);

    // 结果集处理
    int parse_procedure_resultset(const char *data, size_t data_len, oracle_procedure_resultset_t *resultset);
    int parse_cursor_definition(const char *data, size_t data_len, size_t *offset, oracle_procedure_resultset_t *resultset);
    int parse_cursor_data(const char *data, size_t data_len, size_t *offset, oracle_procedure_resultset_t *resultset);

    // 异常处理
    int parse_procedure_exception(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int handle_user_defined_exception(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);
    int handle_system_exception(const char *data, size_t data_len, oracle_procedure_call_t *proc_call);

    // 存储过程会话管理
    int create_procedure_session(uint32_t session_id, oracle_procedure_session_t **session);
    int register_procedure_call(oracle_procedure_session_t *session, oracle_procedure_call_t *proc_call);
    int complete_procedure_call(oracle_procedure_session_t *session, uint32_t call_id);
    void cleanup_procedure_session(oracle_procedure_session_t *session);

    // 存储过程性能分析
    int analyze_procedure_performance(const oracle_procedure_call_t *proc_call);
    int identify_performance_bottlenecks(const oracle_procedure_call_t *proc_call);
    int generate_performance_report(const oracle_procedure_call_t *proc_call, char *report, size_t report_size);

    // 存储过程缓存管理
    int cache_procedure_metadata(const oracle_procedure_call_t *proc_call);
    int get_cached_procedure_metadata(const char *procedure_name, oracle_procedure_call_t **cached_call);
    void clear_procedure_cache();

    // 内存管理
    oracle_procedure_call_t* create_procedure_call(uint8_t call_type);
    void free_procedure_call(oracle_procedure_call_t *proc_call);
    void free_procedure_parameter(oracle_procedure_parameter_t *param);
    void free_procedure_resultset(oracle_procedure_resultset_t *resultset);

    // 工具方法
    const char* get_procedure_type_name(uint8_t call_type);
    const char* get_parameter_direction_name(uint8_t direction);
    const char* get_parameter_mode_name(uint8_t mode);
    const char* get_procedure_status_name(uint8_t status);
    bool is_procedure_type_supported(uint8_t call_type);

    // 统计信息
    void get_procedure_statistics(oracle_procedure_stats_t *stats);
    void reset_procedure_statistics();
    void update_procedure_metrics(const oracle_procedure_call_t *proc_call);

    // 调试和日志
    void dump_procedure_call(const oracle_procedure_call_t *proc_call);
    void dump_procedure_parameters(const oracle_procedure_parameter_t *params, uint16_t count);
    void dump_procedure_resultset(const oracle_procedure_resultset_t *resultset);

    // 配置管理
    void set_max_parameter_count(uint16_t max_count) { m_max_parameter_count = max_count; }
    void set_max_resultset_rows(uint32_t max_rows) { m_max_resultset_rows = max_rows; }
    void set_procedure_timeout(uint64_t timeout_ms) { m_procedure_timeout = timeout_ms; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }

private:
    // 内部工具方法
    uint16_t read_uint16_be(const char *data);
    uint32_t read_uint32_be(const char *data);
    uint64_t read_uint64_be(const char *data);

    // 存储过程名称解析
    int parse_procedure_name(const char *data, size_t data_len, size_t *offset, oracle_procedure_call_t *proc_call);
    int extract_schema_name(const char *full_name, char *schema_name, size_t schema_size);
    int extract_package_name(const char *full_name, char *package_name, size_t package_size);
    int extract_procedure_name(const char *full_name, char *proc_name, size_t proc_size);

    // 参数类型处理
    int resolve_parameter_type(oracle_procedure_parameter_t *param);
    int validate_parameter_compatibility(const oracle_procedure_parameter_t *param);
    int convert_parameter_value(oracle_procedure_parameter_t *param, uint8_t target_type);

    // 结果集内部处理
    int allocate_resultset_memory(oracle_procedure_resultset_t *resultset);
    int parse_resultset_row(const char *data, size_t data_len, size_t *offset, 
                           const oracle_procedure_resultset_t *resultset, tti_data_row_t *row);

    // 性能分析内部方法
    double calculate_procedure_efficiency(const oracle_procedure_call_t *proc_call);
    uint64_t estimate_procedure_memory_usage(const oracle_procedure_call_t *proc_call);
    bool is_procedure_performance_acceptable(const oracle_procedure_call_t *proc_call);

    // 会话管理
    std::map<uint32_t, oracle_procedure_session_t*> m_procedure_sessions;
    uint32_t m_next_session_id;
    uint32_t m_next_call_id;

    // 过程缓存
    std::map<std::string, oracle_procedure_call_t*> m_procedure_cache;
    size_t m_cache_size;
    size_t m_max_cache_size;

    // 配置参数
    uint16_t m_max_parameter_count;
    uint32_t m_max_resultset_rows;
    uint64_t m_procedure_timeout;
    bool m_debug_enabled;

    // 统计信息
    uint64_t m_total_procedure_calls;
    uint64_t m_successful_calls;
    uint64_t m_failed_calls;
    uint64_t m_total_execution_time;
    uint64_t m_cache_hits;
    uint64_t m_cache_misses;
};

// 存储过程统计信息
typedef struct oracle_procedure_stats
{
    uint64_t total_calls;           // 总调用数
    uint64_t successful_calls;      // 成功调用数
    uint64_t failed_calls;          // 失败调用数
    uint64_t procedure_calls;       // 存储过程调用数
    uint64_t function_calls;        // 函数调用数
    uint64_t package_calls;         // 包调用数
    uint64_t anonymous_blocks;      // 匿名块调用数
    uint64_t total_execution_time;  // 总执行时间
    uint64_t average_execution_time; // 平均执行时间
    uint64_t max_execution_time;    // 最大执行时间
    uint64_t min_execution_time;    // 最小执行时间
    uint64_t cache_hits;            // 缓存命中数
    uint64_t cache_misses;          // 缓存未命中数
} oracle_procedure_stats_t;

// 存储过程工具函数命名空间
namespace OracleProcedureUtils
{
    // 存储过程类型判断
    bool is_stored_procedure(const char *call_text);
    bool is_function_call(const char *call_text);
    bool is_package_call(const char *call_text);
    bool is_anonymous_block(const char *call_text);

    // 参数处理
    uint16_t count_procedure_parameters(const char *call_text);
    bool has_return_value(uint8_t call_type);
    bool supports_result_sets(uint8_t call_type);
    bool requires_transaction_control(uint8_t call_type);

    // 性能优化
    bool should_cache_procedure_metadata(const oracle_procedure_call_t *proc_call);
    uint32_t estimate_procedure_complexity(const oracle_procedure_call_t *proc_call);
    bool is_long_running_procedure(const oracle_procedure_call_t *proc_call);

    // 错误处理
    bool is_recoverable_procedure_error(uint32_t error_code);
    bool should_retry_procedure_call(uint32_t error_code, uint32_t retry_count);
    uint32_t get_retry_delay_for_procedure_error(uint32_t error_code);
}

#endif /* __ORACLE_PROCEDURE_HANDLER_H__ */
