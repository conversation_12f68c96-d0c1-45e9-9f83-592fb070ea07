/*
 * Oracle协议解析器高级特性测试程序
 * 测试LOB、批量操作、存储过程、认证、会话管理等高级功能
 * <AUTHOR> @date 2025
 */

#include "oracle_lob_handler.h"
#include "oracle_batch_handler.h"
#include "oracle_procedure_handler.h"
#include "oracle_auth_handler.h"
#include "oracle_session_manager.h"
#include "oracle_advanced_types.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

// 测试LOB处理功能
void test_lob_handler()
{
    printf("=== Testing LOB Handler ===\n");
    
    OracleLobHandler lob_handler;
    oracle_lob_locator_t locator;
    oracle_lob_chunk_t chunk;
    oracle_lob_operation_t operation;
    
    // 测试LOB定位符解析
    const char lob_locator_data[] = {
        0x00, 0x00, 0x00, 0x20,  // 定位符长度: 32字节
        0x01,                    // LOB类型: CLOB
        0x02,                    // 标志位: 已打开
        0x00, 0x00, 0x12, 0x34,  // LOB ID: 0x1234
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, // LOB大小: 4096字节
        0x00, 0x00, 0x08, 0x00,  // 块大小: 2048字节
        // 额外数据...
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08
    };
    
    printf("Testing LOB locator parsing...\n");
    int ret = lob_handler.parse_lob_locator(lob_locator_data, sizeof(lob_locator_data), &locator);
    printf("LOB locator parse result: %d\n", ret);
    if (ret == LOB_PARSE_SUCCESS) {
        printf("LOB type: %s, ID: %u, size: %llu, chunk_size: %u\n",
               lob_handler.get_lob_type_name(locator.lob_type),
               locator.lob_id, locator.lob_size, locator.chunk_size);
    }
    
    // 测试LOB数据块解析
    const char lob_chunk_data[] = {
        0x00, 0x00, 0x00, 0x01,  // 块ID: 1
        0x00, 0x00, 0x00, 0x00,  // 块偏移: 0
        0x00, 0x00, 0x00, 0x10,  // 块大小: 16字节
        0x01,                    // 标志位: 最后一块
        0x00, 0x00, 0x00, 0x0C,  // 数据长度: 12字节
        'H', 'e', 'l', 'l', 'o', ' ', 'W', 'o', 'r', 'l', 'd', '!'
    };
    
    printf("\nTesting LOB chunk parsing...\n");
    ret = lob_handler.parse_lob_chunk(lob_chunk_data, sizeof(lob_chunk_data), &chunk);
    printf("LOB chunk parse result: %d\n", ret);
    if (ret == LOB_PARSE_SUCCESS) {
        printf("Chunk ID: %u, offset: %u, size: %u, data_len: %u, last: %s\n",
               chunk.chunk_id, chunk.chunk_offset, chunk.chunk_size, chunk.data_length,
               chunk.is_last_chunk ? "yes" : "no");
        if (chunk.data && chunk.data_length > 0) {
            printf("Chunk data: %.*s\n", (int)chunk.data_length, chunk.data);
        }
    }
    
    lob_handler.free_lob_locator(&locator);
    lob_handler.free_lob_chunk(&chunk);
    
    printf("LOB Handler test completed.\n\n");
}

// 测试批量操作处理功能
void test_batch_handler()
{
    printf("=== Testing Batch Handler ===\n");
    
    OracleBatchHandler batch_handler;
    oracle_batch_operation_t *batch_op;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 创建批量操作
    printf("Testing batch operation creation...\n");
    batch_op = batch_handler.create_batch_operation(ORACLE_BATCH_OP_INSERT, 100);
    if (batch_op) {
        printf("Batch operation created: type=%s, size=%u\n",
               batch_handler.get_batch_operation_name(batch_op->operation_type),
               batch_op->batch_size);
        
        // 模拟批量INSERT数据
        const char batch_insert_data[] = {
            0x00, 0x00, 0x00, 0x64,  // 批量大小: 100
            0x00, 0x03,              // 绑定变量数: 3
            // 绑定变量1: ID (NUMBER)
            0x00, 0x01,              // 绑定索引: 1
            0x02,                    // 数据类型: NUMBER
            0x00, 0x16,              // 最大长度: 22
            0x00, 0x00, 0x00, 0x64,  // 数组大小: 100
            // 绑定变量2: NAME (VARCHAR2)
            0x00, 0x02,              // 绑定索引: 2
            0x01,                    // 数据类型: VARCHAR2
            0x00, 0x64,              // 最大长度: 100
            0x00, 0x00, 0x00, 0x64,  // 数组大小: 100
            // 绑定变量3: CREATED_DATE (DATE)
            0x00, 0x03,              // 绑定索引: 3
            0x0C,                    // 数据类型: DATE
            0x00, 0x07,              // 最大长度: 7
            0x00, 0x00, 0x00, 0x64,  // 数组大小: 100
        };
        
        printf("\nTesting batch INSERT parsing...\n");
        int ret = batch_handler.parse_batch_insert(batch_insert_data, sizeof(batch_insert_data), batch_op);
        printf("Batch INSERT parse result: %d\n", ret);
        if (ret == BATCH_PARSE_SUCCESS) {
            printf("Batch mode: %s, bind count: %u\n",
                   batch_handler.get_batch_mode_name(batch_op->batch_mode),
                   batch_op->bind_count);
        }
        
        batch_handler.free_batch_operation(batch_op);
    }
    
    printf("Batch Handler test completed.\n\n");
}

// 测试存储过程处理功能
void test_procedure_handler()
{
    printf("=== Testing Procedure Handler ===\n");
    
    OracleProcedureHandler proc_handler;
    oracle_procedure_call_t *proc_call;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 创建存储过程调用
    printf("Testing procedure call creation...\n");
    proc_call = proc_handler.create_procedure_call(ORACLE_PROC_TYPE_PROCEDURE);
    if (proc_call) {
        printf("Procedure call created: type=%s\n",
               proc_handler.get_procedure_type_name(proc_call->call_type));
        
        // 模拟存储过程调用数据
        const char proc_call_data[] = {
            0x01,                    // 调用类型: PROCEDURE
            0x00,                    // 调用状态: PENDING
            0x00, 0x00, 0x12, 0x34,  // 调用ID: 0x1234
            // 过程名长度和内容
            0x00, 0x10,              // 过程名长度: 16
            'T', 'E', 'S', 'T', '_', 'P', 'R', 'O', 'C', 'E', 'D', 'U', 'R', 'E', '(', ')',
            // 参数数量
            0x00, 0x02,              // 参数数量: 2
            // 参数1: IN参数
            0x00, 0x01,              // 参数索引: 1
            0x01,                    // 参数方向: IN
            0x01,                    // 参数模式: BY_VALUE
            0x02,                    // 数据类型: NUMBER
            0x00, 0x16,              // 最大长度: 22
            // 参数2: OUT参数
            0x00, 0x02,              // 参数索引: 2
            0x02,                    // 参数方向: OUT
            0x01,                    // 参数模式: BY_VALUE
            0x01,                    // 数据类型: VARCHAR2
            0x00, 0x64,              // 最大长度: 100
        };
        
        printf("\nTesting procedure call parsing...\n");
        int ret = proc_handler.parse_procedure_call_message(proc_call_data, sizeof(proc_call_data), proc_call);
        printf("Procedure call parse result: %d\n", ret);
        if (ret == PROC_PARSE_SUCCESS) {
            printf("Call ID: %u, parameter count: %u\n",
                   proc_call->call_id, proc_call->parameter_count);
        }
        
        proc_handler.free_procedure_call(proc_call);
    }
    
    printf("Procedure Handler test completed.\n\n");
}

// 测试认证处理功能
void test_auth_handler()
{
    printf("=== Testing Auth Handler ===\n");
    
    OracleAuthHandler auth_handler;
    oracle_auth_session_t *auth_session;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 创建认证会话
    printf("Testing auth session creation...\n");
    int ret = auth_handler.create_auth_session(1001, ORACLE_AUTH_TYPE_O5LOGON, &auth_session);
    if (ret == AUTH_PARSE_SUCCESS && auth_session) {
        printf("Auth session created: ID=%u, type=%s\n",
               auth_session->session_id,
               auth_handler.get_auth_type_name(auth_session->auth_type));
        
        // 模拟O5LOGON认证数据
        const char o5logon_data[] = {
            0x05,                    // 版本: 5
            0x01,                    // 认证模式: 标准
            0x00, 0x00, 0x00, 0x10,  // 盐值长度: 16字节
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
            0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10, // 盐值
            0x00, 0x00, 0x00, 0x14,  // 验证器长度: 20字节
            0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
            0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20,
            0x21, 0x22, 0x23, 0x24,  // 验证器
        };
        
        printf("\nTesting O5LOGON auth parsing...\n");
        ret = auth_handler.parse_o5logon_auth(o5logon_data, sizeof(o5logon_data), auth_session);
        printf("O5LOGON auth parse result: %d\n", ret);
        if (ret == AUTH_PARSE_SUCCESS) {
            printf("O5LOGON version: %u, mode: %u\n",
                   auth_session->auth_data.o5logon.version,
                   auth_session->auth_data.o5logon.auth_mode);
        }
        
        auth_handler.cleanup_auth_session(auth_session);
        delete auth_session;
    }
    
    printf("Auth Handler test completed.\n\n");
}

// 测试会话管理功能
void test_session_manager()
{
    printf("=== Testing Session Manager ===\n");
    
    OracleSessionManager session_mgr;
    oracle_connection_pool_t *pool;
    oracle_connection_info_t *connection;
    
    // 创建连接池
    printf("Testing connection pool creation...\n");
    int ret = session_mgr.create_connection_pool("TEST_POOL", ORACLE_POOL_TYPE_DEDICATED, 5, 20, &pool);
    if (ret == SESSION_MGR_SUCCESS && pool) {
        printf("Connection pool created: name=%s, type=%s, min=%u, max=%u\n",
               pool->pool_name, "DEDICATED", pool->min_connections, pool->max_connections);
        
        // 从连接池获取连接
        printf("\nTesting connection acquisition...\n");
        ret = session_mgr.get_connection_from_pool(pool->pool_id, &connection);
        if (ret == SESSION_MGR_SUCCESS && connection) {
            printf("Connection acquired: ID=%u, state=%u\n",
                   connection->connection_id, connection->connection_state);
            
            // 更新连接活动
            session_mgr.update_session_activity(connection->session_id, time(NULL));
            
            // 归还连接到池
            session_mgr.return_connection_to_pool(connection->connection_id);
            printf("Connection returned to pool\n");
        }
        
        // 获取连接池统计信息
        oracle_pool_stats_t pool_stats;
        session_mgr.get_pool_statistics(pool->pool_id, &pool_stats);
        printf("\nPool statistics: total=%u, active=%u, idle=%u\n",
               pool_stats.total_connections, pool_stats.active_connections, pool_stats.idle_connections);
        
        session_mgr.destroy_connection_pool(pool->pool_id);
    }
    
    printf("Session Manager test completed.\n\n");
}

// 测试高级数据类型功能
void test_advanced_types()
{
    printf("=== Testing Advanced Types ===\n");
    
    OracleAdvancedTypes advanced_types;
    oracle_json_value_t json_value;
    oracle_xml_node_t *xml_root;
    
    // 测试JSON解析
    const char json_data[] = "{\"name\":\"test\",\"value\":123,\"active\":true}";
    printf("Testing JSON parsing...\n");
    int ret = advanced_types.parse_json_value(json_data, strlen(json_data), &json_value);
    printf("JSON parse result: %d\n", ret);
    if (ret == ADV_TYPE_SUCCESS) {
        printf("JSON type: %s\n", advanced_types.get_json_type_name(json_value.json_type));
        if (json_value.json_type == JSON_TYPE_OBJECT) {
            printf("JSON object with %u properties\n", json_value.value.object_value.property_count);
        }
    }
    
    // 测试XML解析
    const char xml_data[] = "<root><item id=\"1\">Test</item><item id=\"2\">Data</item></root>";
    printf("\nTesting XML parsing...\n");
    ret = advanced_types.parse_xml_document(xml_data, strlen(xml_data), &xml_root);
    printf("XML parse result: %d\n", ret);
    if (ret == ADV_TYPE_SUCCESS && xml_root) {
        printf("XML root node: %s, children: %u\n", 
               xml_root->node_name ? xml_root->node_name : "NULL", xml_root->child_count);
    }
    
    // 清理资源
    advanced_types.free_json_value(&json_value);
    if (xml_root) {
        advanced_types.free_xml_node(xml_root);
    }
    
    printf("Advanced Types test completed.\n\n");
}

int main()
{
    printf("Oracle Protocol Parser Advanced Features Test Suite\n");
    printf("===================================================\n\n");
    
    // 运行各个高级特性的测试
    test_lob_handler();
    test_batch_handler();
    test_procedure_handler();
    test_auth_handler();
    test_session_manager();
    test_advanced_types();
    
    printf("All advanced features tests completed successfully!\n");
    return 0;
}
