/*
 * Oracle批量操作处理器实现
 * 实现批量INSERT、UPDATE、DELETE操作的解析和统计
 * <AUTHOR> @date 2025
 */

#include "oracle_batch_handler.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <math.h>

// 日志宏定义
#define BATCH_LOG_DEBUG(fmt, ...) printf("[BATCH-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define BATCH_LOG_INFO(fmt, ...)  printf("[BATCH-INFO] " fmt "\n", ##__VA_ARGS__)
#define BATCH_LOG_WARN(fmt, ...)  printf("[BATCH-WARN] " fmt "\n", ##__VA_ARGS__)
#define BATCH_LOG_ERROR(fmt, ...) printf("[BATCH-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleBatchHandler::OracleBatchHandler()
    : m_next_session_id(1)
    , m_next_batch_id(1)
    , m_max_batch_size(10000)
    , m_default_commit_batch_size(1000)
    , m_performance_monitoring(true)
    , m_debug_enabled(false)
    , m_total_batch_operations(0)
    , m_total_rows_processed(0)
    , m_total_execution_time(0)
    , m_successful_batches(0)
    , m_failed_batches(0)
    , m_average_batch_size(0.0)
    , m_average_throughput(0.0)
{
    BATCH_LOG_INFO("Oracle Batch Handler initialized");
}

OracleBatchHandler::~OracleBatchHandler()
{
    // 清理所有批量会话
    for (auto& session_pair : m_batch_sessions) {
        cleanup_batch_session(session_pair.second);
        delete session_pair.second;
    }
    m_batch_sessions.clear();

    BATCH_LOG_INFO("Oracle Batch Handler destroyed, processed %llu operations, %llu rows",
                  m_total_batch_operations, m_total_rows_processed);
}

int OracleBatchHandler::parse_batch_operation(const char *data, size_t data_len, uint8_t message_type,
                                             oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len == 0 || !status) {
        BATCH_LOG_ERROR("Invalid parameters for batch operation parsing");
        return BATCH_PARSE_ERROR;
    }

    BATCH_LOG_DEBUG("Parsing batch operation: message_type=%u, length=%zu", message_type, data_len);

    // 更新统计信息
    m_total_batch_operations++;

    // 检测批量操作类型
    uint8_t batch_mode;
    int ret = detect_batch_mode(data, data_len, &batch_mode);
    if (ret != BATCH_PARSE_SUCCESS) {
        return ret;
    }

    // 创建批量操作对象
    oracle_batch_operation_t *batch_op = create_batch_operation(ORACLE_BATCH_OP_INSERT, 0);
    if (!batch_op) {
        return BATCH_PARSE_MEMORY_ERROR;
    }

    batch_op->batch_mode = batch_mode;

    // 解析批量操作头部
    size_t offset = 0;
    ret = parse_batch_header(data, data_len, &offset, batch_op);
    if (ret != BATCH_PARSE_SUCCESS) {
        free_batch_operation(batch_op);
        return ret;
    }

    // 根据操作类型进行具体解析
    switch (batch_op->operation_type) {
        case ORACLE_BATCH_OP_INSERT:
            ret = parse_batch_insert(data + offset, data_len - offset, batch_op);
            break;
            
        case ORACLE_BATCH_OP_UPDATE:
            ret = parse_batch_update(data + offset, data_len - offset, batch_op);
            break;
            
        case ORACLE_BATCH_OP_DELETE:
            ret = parse_batch_delete(data + offset, data_len - offset, batch_op);
            break;
            
        default:
            BATCH_LOG_WARN("Unsupported batch operation type: %u", batch_op->operation_type);
            ret = BATCH_PARSE_UNSUPPORTED;
            break;
    }

    if (ret == BATCH_PARSE_SUCCESS && result) {
        // 填充解析结果
        result->op_type = batch_op->operation_type;
        result->rows_processed = batch_op->batch_size;
        result->success = (batch_op->batch_status == ORACLE_BATCH_STATUS_COMPLETED) ? 1 : 0;
        
        // 生成批量操作的SQL描述
        if (batch_op->sql_template.s && batch_op->sql_template.len > 0) {
            result->sql_text = batch_op->sql_template;
        }
    }

    // 更新性能指标
    if (m_performance_monitoring) {
        update_performance_metrics(batch_op);
    }

    free_batch_operation(batch_op);
    return ret;
}

int OracleBatchHandler::parse_batch_insert(const char *data, size_t data_len, oracle_batch_operation_t *batch_op)
{
    if (!data || data_len < 8 || !batch_op) {
        return BATCH_PARSE_ERROR;
    }

    BATCH_LOG_DEBUG("Parsing batch INSERT operation");

    size_t offset = 0;

    // 解析批量大小
    batch_op->batch_size = read_uint32_le(data + offset);
    offset += 4;

    if (batch_op->batch_size == 0 || batch_op->batch_size > m_max_batch_size) {
        BATCH_LOG_ERROR("Invalid batch size: %u", batch_op->batch_size);
        return BATCH_PARSE_INVALID_DATA;
    }

    // 解析绑定变量数量
    batch_op->bind_count = read_uint16_le(data + offset);
    offset += 2;

    BATCH_LOG_DEBUG("Batch INSERT: size=%u, bind_count=%u", batch_op->batch_size, batch_op->bind_count);

    // 解析绑定变量
    if (batch_op->bind_count > 0) {
        int ret = parse_bind_variables(data, data_len, &offset, batch_op);
        if (ret != BATCH_PARSE_SUCCESS) {
            return ret;
        }
    }

    // 根据批量模式进行具体解析
    switch (batch_op->batch_mode) {
        case ORACLE_BATCH_MODE_ARRAY:
            return parse_array_insert(data + offset, data_len - offset, batch_op);
            
        case ORACLE_BATCH_MODE_MULTI:
            return parse_multi_insert(data + offset, data_len - offset, batch_op);
            
        default:
            BATCH_LOG_WARN("Unsupported batch INSERT mode: %u", batch_op->batch_mode);
            return BATCH_PARSE_UNSUPPORTED;
    }
}

int OracleBatchHandler::parse_array_insert(const char *data, size_t data_len, oracle_batch_operation_t *batch_op)
{
    if (!data || !batch_op) {
        return BATCH_PARSE_ERROR;
    }

    BATCH_LOG_DEBUG("Parsing array INSERT operation with %u rows", batch_op->batch_size);

    // 数组绑定模式下，所有数据都在绑定变量中
    // 这里主要是验证数据完整性和更新统计信息

    // 验证绑定变量数组大小
    for (uint16_t i = 0; i < batch_op->bind_count; i++) {
        oracle_bind_variable_t *bind_var = &batch_op->bind_variables[i];
        if (bind_var->is_array_bind && bind_var->array_size != batch_op->batch_size) {
            BATCH_LOG_ERROR("Array bind size mismatch: expected %u, got %u", 
                           batch_op->batch_size, bind_var->array_size);
            return BATCH_PARSE_INVALID_DATA;
        }
    }

    // 更新操作统计
    batch_op->batch_status = ORACLE_BATCH_STATUS_EXECUTING;
    batch_op->successful_rows = batch_op->batch_size; // 假设全部成功，实际需要从结果中解析
    
    return BATCH_PARSE_SUCCESS;
}

int OracleBatchHandler::parse_multi_insert(const char *data, size_t data_len, oracle_batch_operation_t *batch_op)
{
    if (!data || !batch_op) {
        return BATCH_PARSE_ERROR;
    }

    BATCH_LOG_DEBUG("Parsing multi-statement INSERT operation");

    // 多语句模式下，每个INSERT语句都是独立的
    // 需要解析多个SQL语句

    size_t offset = 0;
    uint32_t statement_count = 0;

    // 解析语句数量
    if (data_len >= 4) {
        statement_count = read_uint32_le(data + offset);
        offset += 4;
    }

    if (statement_count != batch_op->batch_size) {
        BATCH_LOG_WARN("Statement count mismatch: expected %u, got %u", 
                      batch_op->batch_size, statement_count);
    }

    // 解析每个INSERT语句
    for (uint32_t i = 0; i < statement_count && offset < data_len; i++) {
        // 解析SQL语句长度
        if (offset + 2 > data_len) {
            break;
        }
        
        uint16_t sql_length = read_uint16_le(data + offset);
        offset += 2;

        if (offset + sql_length > data_len) {
            BATCH_LOG_ERROR("Invalid SQL length at statement %u: %u", i, sql_length);
            return BATCH_PARSE_INVALID_DATA;
        }

        // 记录第一个SQL语句作为模板
        if (i == 0 && sql_length > 0) {
            batch_op->sql_template.s = data + offset;
            batch_op->sql_template.len = sql_length;
        }

        offset += sql_length;
        
        BATCH_LOG_DEBUG("Parsed INSERT statement %u: length=%u", i, sql_length);
    }

    batch_op->batch_status = ORACLE_BATCH_STATUS_EXECUTING;
    return BATCH_PARSE_SUCCESS;
}

int OracleBatchHandler::parse_bind_variables(const char *data, size_t data_len, size_t *offset, 
                                           oracle_batch_operation_t *batch_op)
{
    if (!data || !offset || !batch_op || batch_op->bind_count == 0) {
        return BATCH_PARSE_ERROR;
    }

    BATCH_LOG_DEBUG("Parsing %u bind variables", batch_op->bind_count);

    // 分配绑定变量数组
    batch_op->bind_variables = (oracle_bind_variable_t*)calloc(batch_op->bind_count, 
                                                              sizeof(oracle_bind_variable_t));
    if (!batch_op->bind_variables) {
        return BATCH_PARSE_MEMORY_ERROR;
    }

    // 解析每个绑定变量
    for (uint16_t i = 0; i < batch_op->bind_count; i++) {
        if (*offset >= data_len) {
            BATCH_LOG_ERROR("Insufficient data for bind variable %u", i);
            return BATCH_PARSE_NEED_MORE_DATA;
        }

        oracle_bind_variable_t *bind_var = &batch_op->bind_variables[i];
        
        // 解析绑定变量头部
        if (*offset + 8 > data_len) {
            return BATCH_PARSE_NEED_MORE_DATA;
        }

        bind_var->bind_index = read_uint16_le(data + *offset);
        *offset += 2;
        
        bind_var->data_type = data[*offset];
        *offset += 1;
        
        bind_var->max_length = read_uint16_le(data + *offset);
        *offset += 2;
        
        bind_var->array_size = read_uint32_le(data + *offset);
        *offset += 4;
        
        bind_var->is_array_bind = (bind_var->array_size > 1);

        BATCH_LOG_DEBUG("Bind variable %u: index=%u, type=%u, max_len=%u, array_size=%u",
                       i, bind_var->bind_index, bind_var->data_type, 
                       bind_var->max_length, bind_var->array_size);

        // 解析数组绑定数据
        if (bind_var->is_array_bind) {
            int ret = parse_array_bind_variable(data, data_len, offset, bind_var);
            if (ret != BATCH_PARSE_SUCCESS) {
                return ret;
            }
        }
    }

    return BATCH_PARSE_SUCCESS;
}

int OracleBatchHandler::parse_array_bind_variable(const char *data, size_t data_len, size_t *offset,
                                                 oracle_bind_variable_t *bind_var)
{
    if (!data || !offset || !bind_var || !bind_var->is_array_bind) {
        return BATCH_PARSE_ERROR;
    }

    BATCH_LOG_DEBUG("Parsing array bind variable with %u elements", bind_var->array_size);

    // 分配数组
    int ret = allocate_bind_arrays(bind_var, bind_var->array_size);
    if (ret != BATCH_PARSE_SUCCESS) {
        return ret;
    }

    // 解析每个数组元素
    for (uint32_t i = 0; i < bind_var->array_size; i++) {
        if (*offset >= data_len) {
            BATCH_LOG_ERROR("Insufficient data for array element %u", i);
            return BATCH_PARSE_NEED_MORE_DATA;
        }

        // 解析长度指示符
        if (*offset + 2 > data_len) {
            return BATCH_PARSE_NEED_MORE_DATA;
        }
        
        bind_var->lengths[i] = read_uint16_le(data + *offset);
        *offset += 2;

        // 解析NULL指示符
        if (*offset + 1 > data_len) {
            return BATCH_PARSE_NEED_MORE_DATA;
        }
        
        bind_var->indicators[i] = data[*offset];
        *offset += 1;

        // 解析实际数据
        if (bind_var->indicators[i] == 0 && bind_var->lengths[i] > 0) {
            if (*offset + bind_var->lengths[i] > data_len) {
                return BATCH_PARSE_NEED_MORE_DATA;
            }

            // 分配并复制数据
            bind_var->values[i] = malloc(bind_var->lengths[i]);
            if (bind_var->values[i]) {
                memcpy(bind_var->values[i], data + *offset, bind_var->lengths[i]);
            }
            
            *offset += bind_var->lengths[i];
        }
    }

    return BATCH_PARSE_SUCCESS;
}

oracle_batch_operation_t* OracleBatchHandler::create_batch_operation(uint8_t operation_type, uint32_t batch_size)
{
    oracle_batch_operation_t *batch_op = (oracle_batch_operation_t*)calloc(1, sizeof(oracle_batch_operation_t));
    if (!batch_op) {
        return NULL;
    }

    batch_op->operation_type = operation_type;
    batch_op->batch_size = batch_size;
    batch_op->batch_status = ORACLE_BATCH_STATUS_PENDING;
    batch_op->auto_commit = true;
    batch_op->continue_on_error = false;
    batch_op->commit_batch_size = m_default_commit_batch_size;

    return batch_op;
}

void OracleBatchHandler::free_batch_operation(oracle_batch_operation_t *batch_op)
{
    if (!batch_op) {
        return;
    }

    // 释放绑定变量
    if (batch_op->bind_variables) {
        for (uint16_t i = 0; i < batch_op->bind_count; i++) {
            free_bind_variable(&batch_op->bind_variables[i]);
        }
        free(batch_op->bind_variables);
    }

    // 释放行结果
    if (batch_op->row_results) {
        for (uint32_t i = 0; i < batch_op->batch_size; i++) {
            free_batch_row(&batch_op->row_results[i]);
        }
        free(batch_op->row_results);
    }

    free(batch_op);
}

void OracleBatchHandler::free_bind_variable(oracle_bind_variable_t *bind_var)
{
    if (!bind_var) {
        return;
    }

    if (bind_var->bind_name) {
        free(bind_var->bind_name);
    }

    if (bind_var->values) {
        for (uint32_t i = 0; i < bind_var->array_size; i++) {
            if (bind_var->values[i]) {
                free(bind_var->values[i]);
            }
        }
        free(bind_var->values);
    }

    if (bind_var->lengths) {
        free(bind_var->lengths);
    }

    if (bind_var->indicators) {
        free(bind_var->indicators);
    }

    memset(bind_var, 0, sizeof(oracle_bind_variable_t));
}

const char* OracleBatchHandler::get_batch_operation_name(uint8_t operation_type)
{
    switch (operation_type) {
        case ORACLE_BATCH_OP_INSERT: return "BATCH_INSERT";
        case ORACLE_BATCH_OP_UPDATE: return "BATCH_UPDATE";
        case ORACLE_BATCH_OP_DELETE: return "BATCH_DELETE";
        case ORACLE_BATCH_OP_MERGE:  return "BATCH_MERGE";
        case ORACLE_BATCH_OP_CALL:   return "BATCH_CALL";
        default:                     return "UNKNOWN";
    }
}

const char* OracleBatchHandler::get_batch_mode_name(uint8_t batch_mode)
{
    switch (batch_mode) {
        case ORACLE_BATCH_MODE_ARRAY:  return "ARRAY_BIND";
        case ORACLE_BATCH_MODE_FORALL: return "FORALL";
        case ORACLE_BATCH_MODE_BULK:   return "BULK_COLLECT";
        case ORACLE_BATCH_MODE_MULTI:  return "MULTI_STATEMENT";
        default:                       return "UNKNOWN";
    }
}

// 内部工具方法实现
uint16_t OracleBatchHandler::read_uint16_le(const char *data)
{
    return (uint16_t)(((uint8_t)data[0]) | (((uint8_t)data[1]) << 8));
}

uint32_t OracleBatchHandler::read_uint32_le(const char *data)
{
    return (uint32_t)(((uint8_t)data[0]) |
                     (((uint8_t)data[1]) << 8) |
                     (((uint8_t)data[2]) << 16) |
                     (((uint8_t)data[3]) << 24));
}

int OracleBatchHandler::detect_batch_mode(const char *data, size_t data_len, uint8_t *batch_mode)
{
    if (!data || data_len < 4 || !batch_mode) {
        return BATCH_PARSE_ERROR;
    }

    // 简化的批量模式检测逻辑
    // 实际实现需要根据Oracle协议的具体格式来判断
    
    uint32_t first_word = read_uint32_le(data);
    
    if (first_word & 0x01) {
        *batch_mode = ORACLE_BATCH_MODE_ARRAY;
    } else if (first_word & 0x02) {
        *batch_mode = ORACLE_BATCH_MODE_FORALL;
    } else if (first_word & 0x04) {
        *batch_mode = ORACLE_BATCH_MODE_BULK;
    } else {
        *batch_mode = ORACLE_BATCH_MODE_MULTI;
    }

    BATCH_LOG_DEBUG("Detected batch mode: %s", get_batch_mode_name(*batch_mode));
    return BATCH_PARSE_SUCCESS;
}

int OracleBatchHandler::allocate_bind_arrays(oracle_bind_variable_t *bind_var, uint32_t array_size)
{
    if (!bind_var || array_size == 0) {
        return BATCH_PARSE_ERROR;
    }

    bind_var->values = (void**)calloc(array_size, sizeof(void*));
    bind_var->lengths = (uint16_t*)calloc(array_size, sizeof(uint16_t));
    bind_var->indicators = (uint8_t*)calloc(array_size, sizeof(uint8_t));

    if (!bind_var->values || !bind_var->lengths || !bind_var->indicators) {
        if (bind_var->values) free(bind_var->values);
        if (bind_var->lengths) free(bind_var->lengths);
        if (bind_var->indicators) free(bind_var->indicators);
        return BATCH_PARSE_MEMORY_ERROR;
    }

    return BATCH_PARSE_SUCCESS;
}
