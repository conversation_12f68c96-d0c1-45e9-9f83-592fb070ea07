ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif

CFLAGS          = -g -fvisibility=hidden -fPIC -std=c++11 -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/
CFLAGS_C        = -g -fvisibility=hidden -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/

LDFLAGS         = -shared
LDFLAGS        += -lstdc++ -lz

include ../../flags.make

O_FILES = oracle_parser.o oracle_parser_deal_parser.o oracle_parser_deal_probe.o
O_FILES += oracle_tns_parser.o oracle_ttc_parser.o oracle_tti_parser.o
O_FILES += oracle_ttc_stack_parser.o oracle_auth_complete.o oracle_memory_manager.o
O_FILES += oracle_data_descriptor.o oracle_sql_executor.o
O_FILES += oracle_transaction_manager.o oracle_error_handler.o
O_FILES += oracle_data_types.o oracle_version_compat.o
O_FILES += oracle_lob_handler.o oracle_batch_handler.o oracle_procedure_handler.o
O_FILES += oracle_auth_handler.o oracle_session_manager.o oracle_advanced_types.o
O_FILES += oracle_complex_types.o oracle_tti_parser_complete.o
O_FILES += module_mgt_oracle_parser.o
O_FILES += cJSON.o cJSON_Utils.o utils.o

CFLAGS += -I. -I/usr/include

LIBS += -L../../core -lgw_core

CFLAGS         += -I/usr/libiconv/include
LDFLAGS        += -L/usr/libiconv/lib/

.PHONY: all clean

all: oracle_parser.so

%.o:%.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS_C) $(LIBS_CFLAGS) $<

cJSON.o: ../.././utils/cjson/cJSON.c ../.././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS_C) $(LIBS_CFLAGS) $<

cJSON_Utils.o: ../.././utils/cjson/cJSON_Utils.c ../.././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS_C) $(LIBS_CFLAGS) $<

utils.o: ../../core/utils.c ../../include/utils.h
	$(CC) -c $(CFLAGS_C) $(LIBS_CFLAGS) $<

oracle_parser.so: $(O_FILES)
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)

# 测试程序编译
test: test_oracle_parser test_advanced_features test_tns_large_packet test_priority1_fixes test_priority1_implementation test_priority2_implementation test_priority3_implementation
	./test_oracle_parser
	./test_advanced_features
	./test_tns_large_packet
	./test_priority1_fixes
	./test_priority1_implementation
	./test_priority2_implementation
	./test_priority3_implementation

test_oracle_parser: test_oracle_parser.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread

test_oracle_parser.o: test_oracle_parser.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

test_advanced_features: test_advanced_features.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread -lz -lssl -lcrypto

test_advanced_features.o: test_advanced_features.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

test_tns_large_packet: test_tns_large_packet.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread

test_tns_large_packet.o: test_tns_large_packet.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

test_priority1_fixes: test_priority1_fixes.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread -lssl -lcrypto

test_priority1_fixes.o: test_priority1_fixes.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

test_priority1_implementation: test_priority1_implementation.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread -lssl -lcrypto -lz

test_priority1_implementation.o: test_priority1_implementation.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

test_priority2_implementation: test_priority2_implementation.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread -lssl -lcrypto -lz

test_priority2_implementation.o: test_priority2_implementation.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

test_priority3_implementation: test_priority3_implementation.o $(O_FILES)
	$(CC) -o $@ $^ $(LIBS) -lpthread -lssl -lcrypto -lz

test_priority3_implementation.o: test_priority3_implementation.cpp
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

clean:
	rm -f *.o *~ oracle_parser.so test_oracle_parser test_advanced_features test_tns_large_packet test_priority1_fixes test_priority1_implementation test_priority2_implementation test_priority3_implementation

.PHONY: all clean test