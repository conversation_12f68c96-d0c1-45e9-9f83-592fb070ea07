/*
 * Oracle高级数据类型支持头文件
 * 实现JSON、XML、XMLTYPE等高级数据类型的解析
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_ADVANCED_TYPES_H__
#define __ORACLE_ADVANCED_TYPES_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <string>
#include "oracle_parser_common.h"

// 高级数据类型处理结果状态
#define ADV_TYPE_SUCCESS           0
#define ADV_TYPE_NEED_MORE_DATA    1
#define ADV_TYPE_ERROR            -1
#define ADV_TYPE_INVALID_DATA     -2
#define ADV_TYPE_UNSUPPORTED      -3
#define ADV_TYPE_PARSE_ERROR      -4
#define ADV_TYPE_MEMORY_ERROR     -5

// Oracle高级数据类型
#define ORACLE_TYPE_JSON           200  // JSON类型
#define ORACLE_TYPE_XML            201  // XML类型
#define ORACLE_TYPE_XMLTYPE        202  // XMLType类型
#define ORACLE_TYPE_OBJECT         203  // 对象类型
#define ORACLE_TYPE_VARRAY         204  // 变长数组
#define ORACLE_TYPE_NESTED_TABLE   205  // 嵌套表
#define ORACLE_TYPE_REF            206  // 引用类型
#define ORACLE_TYPE_CURSOR         207  // 游标类型
#define ORACLE_TYPE_COLLECTION     208  // 集合类型
#define ORACLE_TYPE_ANYDATA        209  // AnyData类型
#define ORACLE_TYPE_ANYTYPE        210  // AnyType类型
#define ORACLE_TYPE_ANYDATASET     211  // AnyDataSet类型
#define ORACLE_TYPE_SPATIAL        212  // 空间数据类型
#define ORACLE_TYPE_MEDIA          213  // 多媒体类型
#define ORACLE_TYPE_VECTOR         214  // 向量类型

// JSON数据类型
#define JSON_TYPE_NULL             0
#define JSON_TYPE_BOOLEAN          1
#define JSON_TYPE_NUMBER           2
#define JSON_TYPE_STRING           3
#define JSON_TYPE_ARRAY            4
#define JSON_TYPE_OBJECT           5

// XML节点类型
#define XML_NODE_ELEMENT           1
#define XML_NODE_ATTRIBUTE         2
#define XML_NODE_TEXT              3
#define XML_NODE_CDATA             4
#define XML_NODE_COMMENT           5
#define XML_NODE_PROCESSING_INST   6
#define XML_NODE_DOCUMENT          7

// Oracle JSON值结构
typedef struct oracle_json_value
{
    uint8_t  json_type;             // JSON类型
    union {
        bool     boolean_value;     // 布尔值
        double   number_value;      // 数字值
        b_string_t string_value;    // 字符串值
        struct {
            uint32_t element_count;
            struct oracle_json_value **elements;
        } array_value;              // 数组值
        struct {
            uint32_t property_count;
            char **property_names;
            struct oracle_json_value **property_values;
        } object_value;             // 对象值
    } value;
    
    // 元数据
    size_t   original_length;       // 原始长度
    char     *original_text;        // 原始文本
    bool     is_parsed;             // 是否已解析
} oracle_json_value_t;

// Oracle XML节点结构
typedef struct oracle_xml_node
{
    uint8_t  node_type;             // 节点类型
    char     *node_name;            // 节点名称
    char     *node_value;           // 节点值
    char     *namespace_uri;        // 命名空间URI
    char     *namespace_prefix;     // 命名空间前缀
    
    // 属性
    uint32_t attribute_count;       // 属性数量
    char     **attribute_names;     // 属性名数组
    char     **attribute_values;    // 属性值数组
    
    // 子节点
    uint32_t child_count;           // 子节点数量
    struct oracle_xml_node **children; // 子节点数组
    
    // 父节点和兄弟节点
    struct oracle_xml_node *parent; // 父节点
    struct oracle_xml_node *next_sibling; // 下一个兄弟节点
    struct oracle_xml_node *prev_sibling; // 上一个兄弟节点
} oracle_xml_node_t;

// Oracle XMLType结构
typedef struct oracle_xmltype
{
    uint32_t xml_length;            // XML长度
    char     *xml_data;             // XML数据
    oracle_xml_node_t *root_node;   // 根节点
    char     *schema_url;           // 模式URL
    char     *encoding;             // 编码
    bool     is_schema_based;       // 是否基于模式
    bool     is_binary_xml;         // 是否为二进制XML
    
    // 解析状态
    bool     is_parsed;             // 是否已解析
    char     *parse_error;          // 解析错误
    uint32_t error_line;            // 错误行号
    uint32_t error_column;          // 错误列号
} oracle_xmltype_t;

// Oracle对象类型结构
typedef struct oracle_object_type
{
    char     *type_name;            // 类型名称
    char     *schema_name;          // 模式名称
    uint32_t type_oid;              // 类型OID
    uint16_t attribute_count;       // 属性数量
    
    // 属性定义
    struct {
        char     *attr_name;        // 属性名
        uint8_t  attr_type;         // 属性类型
        uint16_t attr_length;       // 属性长度
        uint8_t  attr_precision;    // 属性精度
        uint8_t  attr_scale;        // 属性小数位数
        bool     is_nullable;       // 是否可为空
    } *attributes;
    
    // 方法定义
    uint16_t method_count;          // 方法数量
    struct {
        char     *method_name;      // 方法名
        uint8_t  method_type;       // 方法类型
        uint16_t param_count;       // 参数数量
        uint8_t  return_type;       // 返回类型
    } *methods;
} oracle_object_type_t;

// Oracle对象实例结构
typedef struct oracle_object_instance
{
    oracle_object_type_t *object_type; // 对象类型
    oracle_value_t *attribute_values;   // 属性值数组
    bool     is_null;               // 是否为空
    uint32_t ref_count;             // 引用计数
} oracle_object_instance_t;

// Oracle集合类型结构
typedef struct oracle_collection_type
{
    uint8_t  collection_type;       // 集合类型（VARRAY/嵌套表）
    char     *type_name;            // 类型名称
    uint8_t  element_type;          // 元素类型
    uint32_t max_elements;          // 最大元素数（VARRAY）
    oracle_object_type_t *element_object_type; // 元素对象类型
} oracle_collection_type_t;

// Oracle集合实例结构
typedef struct oracle_collection_instance
{
    oracle_collection_type_t *collection_type; // 集合类型
    uint32_t element_count;         // 元素数量
    oracle_value_t *elements;       // 元素数组
    bool     *null_indicators;      // NULL指示符数组
    bool     is_null;               // 是否为空
} oracle_collection_instance_t;

// Oracle高级数据类型处理器类
class OracleAdvancedTypes
{
public:
    OracleAdvancedTypes();
    ~OracleAdvancedTypes();

    // JSON类型处理
    int parse_json_value(const char *data, size_t data_len, oracle_json_value_t *json_value);
    int parse_json_object(const char *data, size_t data_len, size_t *offset, oracle_json_value_t *json_value);
    int parse_json_array(const char *data, size_t data_len, size_t *offset, oracle_json_value_t *json_value);
    int parse_json_string(const char *data, size_t data_len, size_t *offset, b_string_t *string_value);
    int parse_json_number(const char *data, size_t data_len, size_t *offset, double *number_value);
    int convert_json_to_string(const oracle_json_value_t *json_value, char **json_string, size_t *string_len);

    // XML类型处理
    int parse_xml_document(const char *data, size_t data_len, oracle_xml_node_t **root_node);
    int parse_xml_element(const char *data, size_t data_len, size_t *offset, oracle_xml_node_t *element);
    int parse_xml_attributes(const char *data, size_t data_len, size_t *offset, oracle_xml_node_t *element);
    int parse_xml_text_content(const char *data, size_t data_len, size_t *offset, oracle_xml_node_t *text_node);
    int convert_xml_to_string(const oracle_xml_node_t *root_node, char **xml_string, size_t *string_len);

    // XMLType处理
    int parse_xmltype(const char *data, size_t data_len, oracle_xmltype_t *xmltype);
    int parse_binary_xml(const char *data, size_t data_len, oracle_xmltype_t *xmltype);
    int validate_xml_schema(const oracle_xmltype_t *xmltype);
    int extract_xml_fragments(const oracle_xmltype_t *xmltype, const char *xpath, 
                             std::vector<oracle_xml_node_t*> &fragments);

    // 对象类型处理
    int parse_object_type_definition(const char *data, size_t data_len, oracle_object_type_t *object_type);
    int parse_object_instance(const char *data, size_t data_len, const oracle_object_type_t *object_type,
                             oracle_object_instance_t *object_instance);
    int serialize_object_instance(const oracle_object_instance_t *object_instance, 
                                 char **serialized_data, size_t *data_len);

    // 集合类型处理
    int parse_collection_type_definition(const char *data, size_t data_len, oracle_collection_type_t *collection_type);
    int parse_varray_instance(const char *data, size_t data_len, const oracle_collection_type_t *collection_type,
                             oracle_collection_instance_t *collection_instance);
    int parse_nested_table_instance(const char *data, size_t data_len, const oracle_collection_type_t *collection_type,
                                   oracle_collection_instance_t *collection_instance);

    // 引用类型处理
    int parse_ref_value(const char *data, size_t data_len, oracle_ref_t *ref_value);
    int dereference_ref(const oracle_ref_t *ref_value, oracle_object_instance_t **object_instance);
    int validate_ref_integrity(const oracle_ref_t *ref_value);

    // 游标类型处理
    int parse_cursor_definition(const char *data, size_t data_len, oracle_cursor_t *cursor);
    int parse_cursor_data(const char *data, size_t data_len, oracle_cursor_t *cursor);
    int extract_cursor_metadata(const oracle_cursor_t *cursor, oracle_cursor_metadata_t *metadata);

    // AnyData类型处理
    int parse_anydata_value(const char *data, size_t data_len, oracle_anydata_t *anydata);
    int extract_anydata_type_info(const oracle_anydata_t *anydata, oracle_type_info_t *type_info);
    int convert_anydata_to_native_type(const oracle_anydata_t *anydata, oracle_value_t *native_value);

    // 空间数据类型处理
    int parse_sdo_geometry(const char *data, size_t data_len, oracle_sdo_geometry_t *geometry);
    int parse_spatial_coordinates(const char *data, size_t data_len, size_t *offset, 
                                 oracle_coordinate_array_t *coordinates);
    int convert_geometry_to_wkt(const oracle_sdo_geometry_t *geometry, char **wkt_string, size_t *string_len);

    // 内存管理
    void free_json_value(oracle_json_value_t *json_value);
    void free_xml_node(oracle_xml_node_t *xml_node);
    void free_xmltype(oracle_xmltype_t *xmltype);
    void free_object_type(oracle_object_type_t *object_type);
    void free_object_instance(oracle_object_instance_t *object_instance);
    void free_collection_type(oracle_collection_type_t *collection_type);
    void free_collection_instance(oracle_collection_instance_t *collection_instance);

    // 工具方法
    const char* get_advanced_type_name(uint8_t type_code);
    const char* get_json_type_name(uint8_t json_type);
    const char* get_xml_node_type_name(uint8_t node_type);
    bool is_advanced_type_supported(uint8_t type_code);
    bool is_structured_type(uint8_t type_code);

    // 类型转换
    int convert_advanced_type_to_string(uint8_t type_code, const void *type_data, 
                                       char **string_repr, size_t *string_len);
    int convert_string_to_advanced_type(const char *string_data, size_t string_len, 
                                       uint8_t target_type, void **type_data);

    // 验证和校验
    int validate_json_syntax(const char *json_data, size_t data_len);
    int validate_xml_well_formed(const char *xml_data, size_t data_len);
    int validate_object_constraints(const oracle_object_instance_t *object_instance);
    int validate_collection_bounds(const oracle_collection_instance_t *collection_instance);

    // 调试和日志
    void dump_json_value(const oracle_json_value_t *json_value, int indent_level);
    void dump_xml_node(const oracle_xml_node_t *xml_node, int indent_level);
    void dump_object_instance(const oracle_object_instance_t *object_instance);
    void dump_collection_instance(const oracle_collection_instance_t *collection_instance);

    // 配置管理
    void set_max_json_depth(uint32_t max_depth) { m_max_json_depth = max_depth; }
    void set_max_xml_depth(uint32_t max_depth) { m_max_xml_depth = max_depth; }
    void set_max_collection_size(uint32_t max_size) { m_max_collection_size = max_size; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }

private:
    // JSON解析内部方法
    int skip_json_whitespace(const char *data, size_t data_len, size_t *offset);
    int parse_json_literal(const char *data, size_t data_len, size_t *offset, const char *literal);
    int parse_json_escape_sequence(const char *data, size_t data_len, size_t *offset, char *escaped_char);
    int validate_json_number_format(const char *data, size_t data_len, size_t offset, size_t length);

    // XML解析内部方法
    int skip_xml_whitespace(const char *data, size_t data_len, size_t *offset);
    int parse_xml_name(const char *data, size_t data_len, size_t *offset, char **name);
    int parse_xml_attribute_value(const char *data, size_t data_len, size_t *offset, char **value);
    int resolve_xml_entity_references(char *text);

    // 对象类型内部方法
    int allocate_object_attributes(oracle_object_type_t *object_type, uint16_t attribute_count);
    int allocate_object_methods(oracle_object_type_t *object_type, uint16_t method_count);
    int validate_object_type_definition(const oracle_object_type_t *object_type);

    // 集合类型内部方法
    int allocate_collection_elements(oracle_collection_instance_t *collection_instance, uint32_t element_count);
    int validate_collection_element_type(const oracle_collection_type_t *collection_type, const oracle_value_t *element);
    int resize_collection_if_needed(oracle_collection_instance_t *collection_instance, uint32_t required_size);

    // 配置参数
    uint32_t m_max_json_depth;
    uint32_t m_max_xml_depth;
    uint32_t m_max_collection_size;
    bool m_debug_enabled;

    // 统计信息
    uint64_t m_json_values_parsed;
    uint64_t m_xml_documents_parsed;
    uint64_t m_objects_parsed;
    uint64_t m_collections_parsed;
    uint64_t m_parse_errors;
};

// 高级数据类型工具函数命名空间
namespace OracleAdvancedTypeUtils
{
    // 类型判断
    bool is_json_type(uint8_t type_code);
    bool is_xml_type(uint8_t type_code);
    bool is_object_type(uint8_t type_code);
    bool is_collection_type(uint8_t type_code);
    bool is_spatial_type(uint8_t type_code);

    // JSON工具
    bool is_valid_json_character(char c);
    size_t estimate_json_memory_usage(const oracle_json_value_t *json_value);
    int compare_json_values(const oracle_json_value_t *value1, const oracle_json_value_t *value2);

    // XML工具
    bool is_valid_xml_name_char(char c);
    size_t estimate_xml_memory_usage(const oracle_xml_node_t *xml_node);
    int find_xml_node_by_name(const oracle_xml_node_t *root, const char *name, oracle_xml_node_t **found_node);

    // 对象工具
    size_t calculate_object_size(const oracle_object_instance_t *object_instance);
    int compare_object_instances(const oracle_object_instance_t *obj1, const oracle_object_instance_t *obj2);
    bool is_object_type_compatible(const oracle_object_type_t *type1, const oracle_object_type_t *type2);

    // 集合工具
    size_t calculate_collection_size(const oracle_collection_instance_t *collection_instance);
    int sort_collection_elements(oracle_collection_instance_t *collection_instance);
    bool is_collection_element_unique(const oracle_collection_instance_t *collection_instance, const oracle_value_t *element);
}

#endif /* __ORACLE_ADVANCED_TYPES_H__ */
