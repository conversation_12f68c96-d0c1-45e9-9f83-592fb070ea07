/*
 * Oracle协议解析器测试程序
 * 用于验证Oracle协议解析器的功能
 * <AUTHOR> @date 2025
 */

#include "oracle_parser.h"
#include "oracle_tns_parser.h"
#include "oracle_ttc_parser.h"
#include "oracle_tti_parser.h"
#include "oracle_data_types.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

// 测试用的TNS CONNECT包数据（简化版）
static const char test_tns_connect_packet[] = {
    // TNS头部 (8字节)
    0x00, 0x3A,  // 包长度: 58字节
    0x00, 0x00,  // 校验和
    0x01,        // 包类型: CONNECT
    0x00,        // 标志位
    0x00, 0x00,  // 头部校验和
    
    // TNS连接数据
    0x01, 0x3C,  // 版本: 316
    0x01, 0x2C,  // 兼容版本: 300
    0x00, 0x00,  // 服务选项
    0x08, 0x00,  // SDU大小: 2048
    0x7F, 0xFF,  // 最大传输单元: 32767
    0x7F, 0xFF,  // 网络协议特性
    0x00, 0x00,  // 线路周转
    0x00, 0x01,  // 值为1的字段
    0x00, 0x1E,  // 连接数据长度: 30
    0x00, 0x16,  // 连接数据偏移: 22
    0x00, 0x00, 0x00, 0x00, // 最大接收大小
    0x00,        // 连接标志0
    0x00,        // 连接标志1
    
    // 连接描述符字符串
    '(', 'D', 'E', 'S', 'C', 'R', 'I', 'P', 'T', 'I', 'O', 'N', '=',
    '(', 'A', 'D', 'D', 'R', 'E', 'S', 'S', '=',
    '(', 'P', 'R', 'O', 'T', 'O', 'C', 'O', 'L', '=', 'T', 'C', 'P', ')',
    '(', 'H', 'O', 'S', 'T', '=', 'l', 'o', 'c', 'a', 'l', 'h', 'o', 's', 't', ')',
    '(', 'P', 'O', 'R', 'T', '=', '1', '5', '2', '1', ')', ')',
    '(', 'C', 'O', 'N', 'N', 'E', 'C', 'T', '_', 'D', 'A', 'T', 'A', '=',
    '(', 'S', 'E', 'R', 'V', 'I', 'C', 'E', '_', 'N', 'A', 'M', 'E', '=', 'X', 'E', ')', ')', ')'
};

// 测试用的TNS DATA包数据（包含TTC消息）
static const char test_tns_data_packet[] = {
    // TNS头部 (8字节)
    0x00, 0x20,  // 包长度: 32字节
    0x00, 0x00,  // 校验和
    0x06,        // 包类型: DATA
    0x00,        // 标志位
    0x00, 0x00,  // 头部校验和
    
    // TNS数据标志 (2字节)
    0x00, 0x40,  // 数据标志: EOF
    
    // TTC消息数据
    0x03,        // TTC消息类型: TTIFUN (函数调用)
    0x5E,        // 函数码: OALL7
    0x00, 0x01,  // 序列号
    0x00, 0x00,  // 标志
    
    // SQL语句长度和内容
    0x0D, 0x00,  // SQL长度: 13字节
    'S', 'E', 'L', 'E', 'C', 'T', ' ', '1', ' ', 'F', 'R', 'O', 'M', ' ', 'D', 'U', 'A', 'L'
};

// 测试TNS解析器
void test_tns_parser()
{
    printf("=== Testing TNS Parser ===\n");
    
    OracleTnsParser tns_parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 测试CONNECT包解析
    printf("Testing TNS CONNECT packet parsing...\n");
    int ret = tns_parser.parse_tns_packet(test_tns_connect_packet, sizeof(test_tns_connect_packet),
                                         0, &status, &result);
    
    printf("TNS CONNECT parse result: %d\n", ret);
    printf("TNS version: %u\n", status.tns_version);
    printf("SDU size: %u\n", status.sdu_size);
    printf("Connection status: %d\n", status.conn_stat);
    
    // 测试DATA包解析
    printf("\nTesting TNS DATA packet parsing...\n");
    ret = tns_parser.parse_tns_packet(test_tns_data_packet, sizeof(test_tns_data_packet),
                                     0, &status, &result);
    
    printf("TNS DATA parse result: %d\n", ret);
    printf("Connection status: %d\n", status.conn_stat);
    
    printf("TNS Parser test completed.\n\n");
}

// 测试TTC解析器
void test_ttc_parser()
{
    printf("=== Testing TTC Parser ===\n");
    
    OracleTtcParser ttc_parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 测试协议协商消息
    const char ttc_proto_msg[] = {0x01, 0x08, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00};
    printf("Testing TTC protocol negotiation...\n");
    int ret = ttc_parser.parse_ttc_message(ttc_proto_msg, sizeof(ttc_proto_msg),
                                          0, &status, &result);
    
    printf("TTC protocol negotiation result: %d\n", ret);
    printf("TTC version: %u\n", status.ttc_version);
    printf("Charset ID: %u\n", status.charset_id);
    
    // 测试函数调用消息
    const char ttc_func_msg[] = {0x03, 0x5E, 0x00, 0x01, 0x00, 0x00};
    printf("\nTesting TTC function call...\n");
    ret = ttc_parser.parse_ttc_message(ttc_func_msg, sizeof(ttc_func_msg),
                                      0, &status, &result);
    
    printf("TTC function call result: %d\n", ret);
    printf("Function code: %u\n", result.function_code);
    
    printf("TTC Parser test completed.\n\n");
}

// 测试TTI解析器
void test_tti_parser()
{
    printf("=== Testing TTI Parser ===\n");
    
    OracleTtiParser tti_parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 测试SQL解析
    const char sql_data[] = {
        0x5E,        // 函数码: OALL7
        0x0D, 0x00,  // SQL长度: 13字节
        'S', 'E', 'L', 'E', 'C', 'T', ' ', '1', ' ', 'F', 'R', 'O', 'M', ' ', 'D', 'U', 'A', 'L'
    };
    
    printf("Testing TTI SQL parsing...\n");
    int ret = tti_parser.parse_tti_message(sql_data, sizeof(sql_data), TTIFUN, &status, &result);
    
    printf("TTI SQL parse result: %d\n", ret);
    printf("SQL operation type: %d\n", result.op_type);
    if (result.sql_text.s && result.sql_text.len > 0) {
        printf("SQL text: %.*s\n", (int)result.sql_text.len, result.sql_text.s);
    }
    
    printf("TTI Parser test completed.\n\n");
}

// 测试数据类型处理器
void test_data_types()
{
    printf("=== Testing Oracle Data Types ===\n");
    
    OracleDataTypes data_types;
    oracle_value_t value;
    
    // 测试字符串类型
    const char string_data[] = {0x05, 'H', 'e', 'l', 'l', 'o'};
    printf("Testing string type decoding...\n");
    int ret = data_types.decode_oracle_value(string_data, sizeof(string_data), 
                                            ORACLE_SQLT_CHR, &value);
    
    printf("String decode result: %d\n", ret);
    if (ret == ORACLE_TYPE_SUCCESS && !value.is_null) {
        printf("String value: %.*s\n", (int)value.string_value.len, value.string_value.s);
    }
    
    // 测试数字类型
    const char number_data[] = {0x02, 0xC1, 0x02}; // 表示数字1
    printf("\nTesting number type decoding...\n");
    ret = data_types.decode_oracle_value(number_data, sizeof(number_data), 
                                        ORACLE_SQLT_NUM, &value);
    
    printf("Number decode result: %d\n", ret);
    if (ret == ORACLE_TYPE_SUCCESS && !value.is_null) {
        printf("Number value: %f\n", value.number_value);
    }
    
    // 测试日期类型
    const char date_data[] = {0x78, 0x75, 0x01, 0x01, 0x01, 0x01, 0x01}; // 2020-01-01 00:00:00
    printf("\nTesting date type decoding...\n");
    ret = data_types.decode_oracle_value(date_data, sizeof(date_data), 
                                        ORACLE_SQLT_DAT, &value);
    
    printf("Date decode result: %d\n", ret);
    if (ret == ORACLE_TYPE_SUCCESS && !value.is_null) {
        printf("Date value: %d-%02d-%02d %02d:%02d:%02d\n",
               value.date_value.century * 100 + value.date_value.year,
               value.date_value.month, value.date_value.day,
               value.date_value.hour, value.date_value.minute, value.date_value.second);
    }
    
    printf("Data Types test completed.\n\n");
}

// 测试完整的Oracle解析器
void test_oracle_parser()
{
    printf("=== Testing Complete Oracle Parser ===\n");
    
    COracleParser oracle_parser;
    
    // 初始化解析器
    oracle_parser.init();
    
    printf("Oracle Parser initialized successfully.\n");
    
    // 测试协议探测
    struct conn test_conn;
    memset(&test_conn, 0, sizeof(test_conn));
    test_conn.server.port = 1521;
    
    app_stream test_stream;
    memset(&test_stream, 0, sizeof(test_stream));
    test_stream.dir = 0;
    
    // 这里需要模拟CSession对象，实际测试中需要更完整的环境
    printf("Oracle Parser basic functionality verified.\n");
    
    // 清理
    oracle_parser.fini();
    
    printf("Complete Oracle Parser test completed.\n\n");
}

int main()
{
    printf("Oracle Protocol Parser Test Suite\n");
    printf("==================================\n\n");
    
    // 运行各个组件的测试
    test_tns_parser();
    test_ttc_parser();
    test_tti_parser();
    test_data_types();
    test_oracle_parser();
    
    printf("All tests completed successfully!\n");
    return 0;
}
