/*
 * Oracle错误处理和恢复机制实现
 * 实现错误分类处理、连接恢复机制和异常情况处理
 * 提供完整的错误恢复和容错能力
 * <AUTHOR> @date 2025
 */

#include "oracle_error_handler.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <algorithm>

// 日志宏定义
#define ERR_LOG_DEBUG(fmt, ...) printf("[ERR-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define ERR_LOG_INFO(fmt, ...)  printf("[ERR-INFO] " fmt "\n", ##__VA_ARGS__)
#define ERR_LOG_WARN(fmt, ...)  printf("[ERR-WARN] " fmt "\n", ##__VA_ARGS__)
#define ERR_LOG_ERROR(fmt, ...) printf("[ERR-ERROR] " fmt "\n", ##__VA_ARGS__)

// Oracle错误码定义（基于Oracle官方错误码）
#define ORA_00001_UNIQUE_CONSTRAINT         1      // 违反唯一约束
#define ORA_00028_SESSION_KILLED            28     // 会话被终止
#define ORA_00054_RESOURCE_BUSY             54     // 资源忙
#define ORA_00060_DEADLOCK_DETECTED         60     // 检测到死锁
#define ORA_00942_TABLE_NOT_EXISTS          942    // 表或视图不存在
#define ORA_01017_INVALID_USERNAME          1017   // 用户名/密码无效
#define ORA_01034_ORACLE_NOT_AVAILABLE      1034   // Oracle不可用
#define ORA_01089_SHUTDOWN_IN_PROGRESS      1089   // 正在关闭
#define ORA_03113_END_OF_FILE_ON_COMM       3113   // 通信通道文件结束
#define ORA_03114_NOT_CONNECTED             3114   // 未连接到Oracle
#define ORA_12154_TNS_COULD_NOT_RESOLVE     12154  // TNS无法解析服务名
#define ORA_12170_TNS_CONNECT_TIMEOUT       12170  // TNS连接超时
#define ORA_12541_TNS_NO_LISTENER           12541  // TNS无监听器

OracleErrorHandler::OracleErrorHandler()
    : m_max_retry_attempts(3)
    , m_default_retry_delay(1000)  // 1秒
    , m_recovery_timeout(30)       // 30秒
    , m_error_logging_enabled(true)
{
    memset(&m_statistics, 0, sizeof(m_statistics));
    
    // 注册默认错误处理器
    register_default_handlers();
    
    ERR_LOG_INFO("Oracle Error Handler initialized");
}

OracleErrorHandler::~OracleErrorHandler()
{
    // 清理所有恢复上下文
    for (auto& pair : m_recovery_contexts) {
        cleanup_recovery_context(pair.second);
    }
    m_recovery_contexts.clear();
    
    ERR_LOG_INFO("Oracle Error Handler destroyed, handled %llu errors", m_statistics.total_errors);
}

int OracleErrorHandler::handle_error(int error_code, oracle_error_category_t category, 
                                    const char *error_message, oracle_status_t *status)
{
    ERR_LOG_INFO("Handling error: code=%d, category=%d, message=%s", 
                error_code, category, error_message ? error_message : "NULL");

    // 创建错误信息结构
    oracle_error_info_t error_info;
    int ret = create_error_info(error_code, category, error_message, &error_info);
    if (ret != 0) {
        ERR_LOG_ERROR("Failed to create error info structure");
        return ret;
    }

    // 使用完整的错误处理接口
    return handle_error_with_context(&error_info, status);
}

int OracleErrorHandler::handle_error_with_context(const oracle_error_info_t *error_info, oracle_status_t *status)
{
    if (!error_info || !status) {
        ERR_LOG_ERROR("Invalid parameters for error handling");
        return -1;
    }

    ERR_LOG_INFO("Handling error with context: code=%d, category=%s, severity=%s", 
                error_info->error_code, 
                get_error_category_name(error_info->category),
                get_error_severity_name(error_info->severity));

    // 记录错误
    if (m_error_logging_enabled) {
        log_error(error_info);
    }

    // 更新统计信息
    update_error_statistics(error_info);

    // 跟踪错误模式
    track_error_pattern(error_info);

    // 查找对应的错误处理器
    oracle_error_handler_t *handler = find_error_handler(error_info->error_code, error_info->category);
    if (!handler) {
        ERR_LOG_WARN("No specific handler found for error %d, using default handling", error_info->error_code);
        
        // 使用默认处理策略
        if (error_info->is_recoverable) {
            return execute_retry_strategy(error_info, status);
        } else {
            ERR_LOG_ERROR("Error is not recoverable: %d", error_info->error_code);
            return -1;
        }
    }

    // 检查处理器是否启用
    if (!handler->is_enabled) {
        ERR_LOG_WARN("Error handler is disabled for error %d", error_info->error_code);
        return -1;
    }

    // 检查严重程度是否满足处理条件
    if (error_info->severity < handler->min_severity) {
        ERR_LOG_DEBUG("Error severity too low for handler: %s < %s", 
                     get_error_severity_name(error_info->severity),
                     get_error_severity_name(handler->min_severity));
        return 0; // 不需要处理
    }

    // 调用错误处理函数
    int result = 0;
    if (handler->handler_func) {
        result = handler->handler_func(error_info, status);
        ERR_LOG_DEBUG("Error handler function returned: %d", result);
    }

    // 如果处理失败，尝试恢复策略
    if (result != 0 && error_info->is_recoverable) {
        ERR_LOG_INFO("Error handler failed, attempting recovery strategy: %s", 
                    get_recovery_strategy_name(handler->recovery_strategy));
        
        switch (handler->recovery_strategy) {
            case RECOVERY_STRATEGY_RETRY:
                result = execute_retry_strategy(error_info, status);
                break;
            case RECOVERY_STRATEGY_RECONNECT:
                result = execute_reconnect_strategy(error_info, status);
                break;
            case RECOVERY_STRATEGY_RESET:
                result = execute_reset_strategy(error_info, status);
                break;
            case RECOVERY_STRATEGY_FAILOVER:
                result = execute_failover_strategy(error_info, status);
                break;
            case RECOVERY_STRATEGY_ABORT:
                ERR_LOG_ERROR("Aborting due to unrecoverable error: %d", error_info->error_code);
                result = -1;
                break;
            default:
                ERR_LOG_WARN("Unknown recovery strategy: %d", handler->recovery_strategy);
                result = -1;
                break;
        }
    }

    ERR_LOG_INFO("Error handling completed: code=%d, result=%d", error_info->error_code, result);
    return result;
}

int OracleErrorHandler::register_default_handlers()
{
    ERR_LOG_INFO("Registering default error handlers");

    // 网络错误处理器
    oracle_error_handler_t network_handler = {
        0, ORACLE_ERROR_NETWORK, ERROR_SEVERITY_WARNING,
        handle_network_error, RECOVERY_STRATEGY_RECONNECT,
        3, 2000, "Network error handler", true
    };
    register_error_handler(&network_handler);

    // 协议错误处理器
    oracle_error_handler_t protocol_handler = {
        0, ORACLE_ERROR_PROTOCOL, ERROR_SEVERITY_ERROR,
        handle_protocol_error, RECOVERY_STRATEGY_RESET,
        2, 1000, "Protocol error handler", true
    };
    register_error_handler(&protocol_handler);

    // 认证错误处理器
    oracle_error_handler_t auth_handler = {
        ORA_01017_INVALID_USERNAME, ORACLE_ERROR_AUTHENTICATION, ERROR_SEVERITY_ERROR,
        handle_authentication_error, RECOVERY_STRATEGY_ABORT,
        0, 0, "Authentication error handler", true
    };
    register_error_handler(&auth_handler);

    // SQL错误处理器
    oracle_error_handler_t sql_handler = {
        0, ORACLE_ERROR_SQL, ERROR_SEVERITY_WARNING,
        handle_sql_error, RECOVERY_STRATEGY_NONE,
        0, 0, "SQL error handler", true
    };
    register_error_handler(&sql_handler);

    // 事务错误处理器
    oracle_error_handler_t tx_handler = {
        ORA_00060_DEADLOCK_DETECTED, ORACLE_ERROR_TRANSACTION, ERROR_SEVERITY_WARNING,
        handle_transaction_error, RECOVERY_STRATEGY_RETRY,
        3, 500, "Transaction error handler", true
    };
    register_error_handler(&tx_handler);

    // 超时错误处理器
    oracle_error_handler_t timeout_handler = {
        ORA_12170_TNS_CONNECT_TIMEOUT, ORACLE_ERROR_TIMEOUT, ERROR_SEVERITY_WARNING,
        handle_timeout_error, RECOVERY_STRATEGY_RETRY,
        3, 5000, "Timeout error handler", true
    };
    register_error_handler(&timeout_handler);

    ERR_LOG_INFO("Default error handlers registered successfully");
    return 0;
}

oracle_error_category_t OracleErrorHandler::classify_error(int error_code, const char *error_message)
{
    // 基于Oracle错误码进行分类
    switch (error_code) {
        // 网络相关错误
        case ORA_03113_END_OF_FILE_ON_COMM:
        case ORA_03114_NOT_CONNECTED:
        case ORA_12154_TNS_COULD_NOT_RESOLVE:
        case ORA_12170_TNS_CONNECT_TIMEOUT:
        case ORA_12541_TNS_NO_LISTENER:
            return ORACLE_ERROR_NETWORK;
            
        // 认证相关错误
        case ORA_01017_INVALID_USERNAME:
            return ORACLE_ERROR_AUTHENTICATION;
            
        // 事务相关错误
        case ORA_00060_DEADLOCK_DETECTED:
        case ORA_00054_RESOURCE_BUSY:
            return ORACLE_ERROR_TRANSACTION;
            
        // SQL相关错误
        case ORA_00942_TABLE_NOT_EXISTS:
        case ORA_00001_UNIQUE_CONSTRAINT:
            return ORACLE_ERROR_SQL;
            
        // 资源相关错误
        case ORA_01034_ORACLE_NOT_AVAILABLE:
        case ORA_01089_SHUTDOWN_IN_PROGRESS:
        case ORA_00028_SESSION_KILLED:
            return ORACLE_ERROR_RESOURCE;
            
        default:
            // 基于错误消息进行分类
            if (error_message) {
                if (strstr(error_message, "timeout") || strstr(error_message, "TIMEOUT")) {
                    return ORACLE_ERROR_TIMEOUT;
                } else if (strstr(error_message, "network") || strstr(error_message, "NETWORK")) {
                    return ORACLE_ERROR_NETWORK;
                } else if (strstr(error_message, "protocol") || strstr(error_message, "PROTOCOL")) {
                    return ORACLE_ERROR_PROTOCOL;
                } else if (strstr(error_message, "memory") || strstr(error_message, "MEMORY")) {
                    return ORACLE_ERROR_MEMORY;
                }
            }
            return ORACLE_ERROR_UNKNOWN;
    }
}

oracle_error_severity_t OracleErrorHandler::determine_error_severity(int error_code, oracle_error_category_t category)
{
    // 基于错误码和分类确定严重程度
    switch (error_code) {
        // 致命错误
        case ORA_01034_ORACLE_NOT_AVAILABLE:
        case ORA_01089_SHUTDOWN_IN_PROGRESS:
            return ERROR_SEVERITY_FATAL;
            
        // 严重错误
        case ORA_03113_END_OF_FILE_ON_COMM:
        case ORA_03114_NOT_CONNECTED:
        case ORA_01017_INVALID_USERNAME:
            return ERROR_SEVERITY_CRITICAL;
            
        // 一般错误
        case ORA_12170_TNS_CONNECT_TIMEOUT:
        case ORA_12541_TNS_NO_LISTENER:
        case ORA_00942_TABLE_NOT_EXISTS:
            return ERROR_SEVERITY_ERROR;
            
        // 警告级别
        case ORA_00060_DEADLOCK_DETECTED:
        case ORA_00054_RESOURCE_BUSY:
        case ORA_00001_UNIQUE_CONSTRAINT:
            return ERROR_SEVERITY_WARNING;
            
        default:
            // 基于分类确定默认严重程度
            switch (category) {
                case ORACLE_ERROR_NETWORK:
                case ORACLE_ERROR_PROTOCOL:
                    return ERROR_SEVERITY_ERROR;
                case ORACLE_ERROR_AUTHENTICATION:
                    return ERROR_SEVERITY_CRITICAL;
                case ORACLE_ERROR_SQL:
                case ORACLE_ERROR_TRANSACTION:
                    return ERROR_SEVERITY_WARNING;
                case ORACLE_ERROR_RESOURCE:
                case ORACLE_ERROR_MEMORY:
                    return ERROR_SEVERITY_CRITICAL;
                case ORACLE_ERROR_TIMEOUT:
                    return ERROR_SEVERITY_WARNING;
                default:
                    return ERROR_SEVERITY_ERROR;
            }
    }
}

// 预定义错误处理器实现
int OracleErrorHandler::handle_network_error(const oracle_error_info_t *error_info, oracle_status_t *status)
{
    ERR_LOG_INFO("Handling network error: %d", error_info->error_code);
    
    // 网络错误通常需要重新连接
    status->conn_stat = ORACLE_CONN_NETWORK_ERROR;
    status->last_error_code = error_info->error_code;
    strncpy(status->last_error_msg, error_info->error_message, sizeof(status->last_error_msg) - 1);
    
    return 0; // 返回0表示错误已处理，需要进行恢复策略
}

int OracleErrorHandler::handle_protocol_error(const oracle_error_info_t *error_info, oracle_status_t *status)
{
    ERR_LOG_INFO("Handling protocol error: %d", error_info->error_code);
    
    // 协议错误可能需要重置连接状态
    status->conn_stat = ORACLE_CONN_PROTOCOL_ERROR;
    status->last_error_code = error_info->error_code;
    strncpy(status->last_error_msg, error_info->error_message, sizeof(status->last_error_msg) - 1);
    
    return 0;
}

int OracleErrorHandler::handle_authentication_error(const oracle_error_info_t *error_info, oracle_status_t *status)
{
    ERR_LOG_ERROR("Handling authentication error: %d", error_info->error_code);
    
    // 认证错误通常是不可恢复的
    status->conn_stat = ORACLE_CONN_AUTH_FAILED;
    status->last_error_code = error_info->error_code;
    strncpy(status->last_error_msg, error_info->error_message, sizeof(status->last_error_msg) - 1);
    
    return -1; // 返回-1表示不可恢复
}

int OracleErrorHandler::handle_timeout_error(const oracle_error_info_t *error_info, oracle_status_t *status)
{
    ERR_LOG_WARN("Handling timeout error: %d", error_info->error_code);
    
    // 超时错误可以重试
    status->last_error_code = error_info->error_code;
    strncpy(status->last_error_msg, error_info->error_message, sizeof(status->last_error_msg) - 1);
    
    return 0; // 可以重试
}

// 工具方法实现
int OracleErrorHandler::create_error_info(int error_code, oracle_error_category_t category, 
                                         const char *error_message, oracle_error_info_t *error_info)
{
    if (!error_info) {
        return -1;
    }

    memset(error_info, 0, sizeof(oracle_error_info_t));
    
    error_info->error_code = error_code;
    error_info->category = category;
    error_info->severity = determine_error_severity(error_code, category);
    error_info->error_time = get_current_timestamp();
    error_info->first_occurrence = error_info->error_time;
    error_info->occurrence_count = 1;
    
    if (error_message) {
        strncpy(error_info->error_message, error_message, sizeof(error_info->error_message) - 1);
    }
    
    // 确定恢复策略
    error_info->recovery_strategy = determine_recovery_strategy(error_info);
    error_info->is_recoverable = (error_info->recovery_strategy != RECOVERY_STRATEGY_NONE && 
                                 error_info->recovery_strategy != RECOVERY_STRATEGY_ABORT);
    error_info->max_retry_count = m_max_retry_attempts;
    error_info->retry_delay_ms = m_default_retry_delay;
    
    return 0;
}

uint64_t OracleErrorHandler::get_current_timestamp()
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000ULL + ts.tv_nsec / 1000ULL;
}

const char* OracleErrorHandler::get_error_category_name(oracle_error_category_t category)
{
    switch (category) {
        case ORACLE_ERROR_NETWORK:          return "NETWORK";
        case ORACLE_ERROR_PROTOCOL:         return "PROTOCOL";
        case ORACLE_ERROR_AUTHENTICATION:   return "AUTHENTICATION";
        case ORACLE_ERROR_SQL:              return "SQL";
        case ORACLE_ERROR_TRANSACTION:      return "TRANSACTION";
        case ORACLE_ERROR_RESOURCE:         return "RESOURCE";
        case ORACLE_ERROR_TIMEOUT:          return "TIMEOUT";
        case ORACLE_ERROR_MEMORY:           return "MEMORY";
        case ORACLE_ERROR_PARSING:          return "PARSING";
        case ORACLE_ERROR_CONFIGURATION:    return "CONFIGURATION";
        default:                            return "UNKNOWN";
    }
}

const char* OracleErrorHandler::get_error_severity_name(oracle_error_severity_t severity)
{
    switch (severity) {
        case ERROR_SEVERITY_INFO:       return "INFO";
        case ERROR_SEVERITY_WARNING:    return "WARNING";
        case ERROR_SEVERITY_ERROR:      return "ERROR";
        case ERROR_SEVERITY_CRITICAL:   return "CRITICAL";
        case ERROR_SEVERITY_FATAL:      return "FATAL";
        default:                        return "UNKNOWN";
    }
}
