/*
 * Oracle LOB数据处理器实现
 * 实现CLOB、BLOB、NCLOB等大对象数据的解析和处理
 * <AUTHOR> @date 2025
 */

#include "oracle_lob_handler.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <zlib.h>

// 日志宏定义
#define LOB_LOG_DEBUG(fmt, ...) printf("[LOB-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define LOB_LOG_INFO(fmt, ...)  printf("[LOB-INFO] " fmt "\n", ##__VA_ARGS__)
#define LOB_LOG_WARN(fmt, ...)  printf("[LOB-WARN] " fmt "\n", ##__VA_ARGS__)
#define LOB_LOG_ERROR(fmt, ...) printf("[LOB-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleLobHandler::OracleLobHandler()
    : m_cache_size(0)
    , m_max_cache_size(64 * 1024 * 1024)  // 64MB默认缓存
    , m_next_session_id(1)
    , m_max_chunk_size(32 * 1024)         // 32KB默认块大小
    , m_compression_enabled(true)
    , m_debug_enabled(false)
    , m_total_lob_operations(0)
    , m_total_lob_bytes_processed(0)
    , m_cache_hits(0)
    , m_cache_misses(0)
    , m_compression_ratio(0)
{
    LOB_LOG_INFO("Oracle LOB Handler initialized");
}

OracleLobHandler::~OracleLobHandler()
{
    // 清理所有LOB会话
    for (auto& session_pair : m_lob_sessions) {
        cleanup_lob_session(session_pair.second);
        delete session_pair.second;
    }
    m_lob_sessions.clear();

    // 清理LOB缓存
    clear_lob_cache();

    LOB_LOG_INFO("Oracle LOB Handler destroyed, processed %llu operations, %llu bytes",
                m_total_lob_operations, m_total_lob_bytes_processed);
}

int OracleLobHandler::parse_lob_message(const char *data, size_t data_len, uint8_t message_type,
                                       oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len == 0 || !status) {
        LOB_LOG_ERROR("Invalid parameters for LOB message parsing");
        return LOB_PARSE_ERROR;
    }

    LOB_LOG_DEBUG("Parsing LOB message: type=%u, length=%zu", message_type, data_len);

    // 更新统计信息
    m_total_lob_operations++;
    m_total_lob_bytes_processed += data_len;

    // 根据消息类型进行解析
    switch (message_type) {
        case TTILOBD:  // LOB数据消息
            return parse_lob_data_message(data, data_len, status, result);
            
        case TTILOB:   // LOB操作消息
            return parse_lob_operation_message(data, data_len, status, result);
            
        default:
            LOB_LOG_WARN("Unsupported LOB message type: %u", message_type);
            return LOB_PARSE_UNSUPPORTED;
    }
}

int OracleLobHandler::parse_lob_locator(const char *data, size_t data_len, oracle_lob_locator_t *locator)
{
    if (!data || data_len < 8 || !locator) {
        return LOB_PARSE_ERROR;
    }

    memset(locator, 0, sizeof(oracle_lob_locator_t));

    // 解析定位符长度
    locator->locator_length = read_uint32_be(data);
    if (locator->locator_length > data_len) {
        LOB_LOG_ERROR("Invalid LOB locator length: %u > %zu", locator->locator_length, data_len);
        return LOB_PARSE_INVALID_DATA;
    }

    // 解析LOB类型
    locator->lob_type = data[4];
    if (!is_lob_type_supported(locator->lob_type)) {
        LOB_LOG_ERROR("Unsupported LOB type: %u", locator->lob_type);
        return LOB_PARSE_UNSUPPORTED;
    }

    // 解析标志位
    locator->flags = data[5];
    parse_locator_flags(locator->flags, locator);

    // 解析LOB ID
    if (data_len >= 10) {
        locator->lob_id = read_uint32_be(data + 6);
    }

    // 解析LOB大小
    if (data_len >= 18) {
        locator->lob_size = read_uint64_be(data + 10);
    }

    // 解析块大小
    if (data_len >= 22) {
        locator->chunk_size = read_uint32_be(data + 18);
        if (locator->chunk_size == 0) {
            locator->chunk_size = m_max_chunk_size;
        }
    }

    // 复制定位符数据
    if (locator->locator_length > 22) {
        size_t extra_data_len = locator->locator_length - 22;
        locator->locator_data = (char*)malloc(extra_data_len);
        if (locator->locator_data) {
            memcpy(locator->locator_data, data + 22, extra_data_len);
        }
    }

    LOB_LOG_DEBUG("Parsed LOB locator: type=%s, id=%u, size=%llu, chunk_size=%u",
                 get_lob_type_name(locator->lob_type), locator->lob_id, 
                 locator->lob_size, locator->chunk_size);

    return LOB_PARSE_SUCCESS;
}

int OracleLobHandler::parse_lob_chunk(const char *data, size_t data_len, oracle_lob_chunk_t *chunk)
{
    if (!data || data_len < 12 || !chunk) {
        return LOB_PARSE_ERROR;
    }

    memset(chunk, 0, sizeof(oracle_lob_chunk_t));

    // 解析块头部
    chunk->chunk_id = read_uint32_be(data);
    chunk->chunk_offset = read_uint32_be(data + 4);
    chunk->chunk_size = read_uint32_be(data + 8);

    if (chunk->chunk_size > data_len - 12) {
        LOB_LOG_ERROR("Invalid chunk size: %u > %zu", chunk->chunk_size, data_len - 12);
        return LOB_PARSE_INVALID_DATA;
    }

    // 解析标志位
    if (data_len >= 13) {
        uint8_t flags = data[12];
        chunk->is_last_chunk = (flags & 0x01) != 0;
        chunk->is_compressed = (flags & 0x02) != 0;
    }

    // 解析数据长度
    size_t data_start = 13;
    if (data_len >= data_start + 4) {
        chunk->data_length = read_uint32_be(data + data_start);
        data_start += 4;
    } else {
        chunk->data_length = chunk->chunk_size;
    }

    // 复制数据内容
    if (chunk->data_length > 0 && data_len >= data_start + chunk->data_length) {
        chunk->data = (char*)malloc(chunk->data_length);
        if (chunk->data) {
            memcpy(chunk->data, data + data_start, chunk->data_length);
        } else {
            return LOB_PARSE_MEMORY_ERROR;
        }
    }

    LOB_LOG_DEBUG("Parsed LOB chunk: id=%u, offset=%u, size=%u, data_len=%u, last=%s, compressed=%s",
                 chunk->chunk_id, chunk->chunk_offset, chunk->chunk_size, chunk->data_length,
                 chunk->is_last_chunk ? "yes" : "no", chunk->is_compressed ? "yes" : "no");

    return LOB_PARSE_SUCCESS;
}

int OracleLobHandler::parse_lob_read_operation(const char *data, size_t data_len, oracle_lob_operation_t *operation)
{
    if (!data || data_len < 16 || !operation) {
        return LOB_PARSE_ERROR;
    }

    memset(operation, 0, sizeof(oracle_lob_operation_t));
    operation->operation_type = ORACLE_LOB_OP_READ;

    // 解析操作参数
    operation->offset = read_uint64_be(data);
    operation->length = read_uint64_be(data + 8);

    LOB_LOG_DEBUG("Parsing LOB READ operation: offset=%llu, length=%llu", 
                 operation->offset, operation->length);

    // 解析LOB定位符
    if (data_len > 16) {
        operation->locator = (oracle_lob_locator_t*)malloc(sizeof(oracle_lob_locator_t));
        if (operation->locator) {
            int ret = parse_lob_locator(data + 16, data_len - 16, operation->locator);
            if (ret != LOB_PARSE_SUCCESS) {
                free(operation->locator);
                operation->locator = NULL;
                return ret;
            }
        }
    }

    return LOB_PARSE_SUCCESS;
}

int OracleLobHandler::parse_lob_write_operation(const char *data, size_t data_len, oracle_lob_operation_t *operation)
{
    if (!data || data_len < 20 || !operation) {
        return LOB_PARSE_ERROR;
    }

    memset(operation, 0, sizeof(oracle_lob_operation_t));
    operation->operation_type = ORACLE_LOB_OP_WRITE;

    // 解析操作参数
    operation->offset = read_uint64_be(data);
    operation->length = read_uint64_be(data + 8);
    operation->chunk_count = read_uint32_be(data + 16);

    LOB_LOG_DEBUG("Parsing LOB WRITE operation: offset=%llu, length=%llu, chunks=%u", 
                 operation->offset, operation->length, operation->chunk_count);

    // 解析数据块
    if (operation->chunk_count > 0 && data_len > 20) {
        operation->chunks = (oracle_lob_chunk_t*)calloc(operation->chunk_count, sizeof(oracle_lob_chunk_t));
        if (!operation->chunks) {
            return LOB_PARSE_MEMORY_ERROR;
        }

        size_t offset = 20;
        for (uint32_t i = 0; i < operation->chunk_count && offset < data_len; i++) {
            int ret = parse_lob_chunk(data + offset, data_len - offset, &operation->chunks[i]);
            if (ret != LOB_PARSE_SUCCESS) {
                // 清理已分配的块
                for (uint32_t j = 0; j < i; j++) {
                    free_lob_chunk(&operation->chunks[j]);
                }
                free(operation->chunks);
                operation->chunks = NULL;
                return ret;
            }
            
            // 计算下一个块的偏移
            offset += 17 + operation->chunks[i].data_length; // 头部(17字节) + 数据
        }
    }

    return LOB_PARSE_SUCCESS;
}

int OracleLobHandler::convert_clob_to_string(const oracle_lob_operation_t *operation, b_string_t *result)
{
    if (!operation || !result || operation->operation_type != ORACLE_LOB_OP_READ) {
        return LOB_PARSE_ERROR;
    }

    if (!operation->locator || !OracleLobUtils::is_character_lob(operation->locator->lob_type)) {
        return LOB_PARSE_INVALID_DATA;
    }

    // 计算总数据大小
    size_t total_size = 0;
    for (uint32_t i = 0; i < operation->chunk_count; i++) {
        total_size += operation->chunks[i].data_length;
    }

    if (total_size == 0) {
        result->s = NULL;
        result->len = 0;
        return LOB_PARSE_SUCCESS;
    }

    // 分配内存并组装数据
    char *assembled_data = (char*)malloc(total_size + 1);
    if (!assembled_data) {
        return LOB_PARSE_MEMORY_ERROR;
    }

    size_t offset = 0;
    for (uint32_t i = 0; i < operation->chunk_count; i++) {
        if (operation->chunks[i].data && operation->chunks[i].data_length > 0) {
            memcpy(assembled_data + offset, operation->chunks[i].data, operation->chunks[i].data_length);
            offset += operation->chunks[i].data_length;
        }
    }
    assembled_data[total_size] = '\0';

    result->s = assembled_data;
    result->len = total_size;

    LOB_LOG_DEBUG("Converted CLOB to string: %zu bytes", total_size);
    return LOB_PARSE_SUCCESS;
}

int OracleLobHandler::convert_blob_to_hex(const oracle_lob_operation_t *operation, b_string_t *result)
{
    if (!operation || !result || operation->operation_type != ORACLE_LOB_OP_READ) {
        return LOB_PARSE_ERROR;
    }

    if (!operation->locator || !OracleLobUtils::is_binary_lob(operation->locator->lob_type)) {
        return LOB_PARSE_INVALID_DATA;
    }

    // 计算总数据大小
    size_t total_size = 0;
    for (uint32_t i = 0; i < operation->chunk_count; i++) {
        total_size += operation->chunks[i].data_length;
    }

    if (total_size == 0) {
        result->s = NULL;
        result->len = 0;
        return LOB_PARSE_SUCCESS;
    }

    // 分配十六进制字符串内存（每字节需要2个字符）
    char *hex_string = (char*)malloc(total_size * 2 + 1);
    if (!hex_string) {
        return LOB_PARSE_MEMORY_ERROR;
    }

    size_t hex_offset = 0;
    for (uint32_t i = 0; i < operation->chunk_count; i++) {
        if (operation->chunks[i].data && operation->chunks[i].data_length > 0) {
            for (uint32_t j = 0; j < operation->chunks[i].data_length; j++) {
                sprintf(hex_string + hex_offset, "%02X", (unsigned char)operation->chunks[i].data[j]);
                hex_offset += 2;
            }
        }
    }
    hex_string[total_size * 2] = '\0';

    result->s = hex_string;
    result->len = total_size * 2;

    LOB_LOG_DEBUG("Converted BLOB to hex string: %zu bytes -> %zu chars", total_size, total_size * 2);
    return LOB_PARSE_SUCCESS;
}

void OracleLobHandler::free_lob_locator(oracle_lob_locator_t *locator)
{
    if (locator) {
        if (locator->locator_data) {
            free(locator->locator_data);
            locator->locator_data = NULL;
        }
        memset(locator, 0, sizeof(oracle_lob_locator_t));
    }
}

void OracleLobHandler::free_lob_chunk(oracle_lob_chunk_t *chunk)
{
    if (chunk) {
        if (chunk->data) {
            free(chunk->data);
            chunk->data = NULL;
        }
        memset(chunk, 0, sizeof(oracle_lob_chunk_t));
    }
}

const char* OracleLobHandler::get_lob_type_name(uint8_t lob_type)
{
    switch (lob_type) {
        case ORACLE_LOB_TYPE_CLOB:  return "CLOB";
        case ORACLE_LOB_TYPE_BLOB:  return "BLOB";
        case ORACLE_LOB_TYPE_NCLOB: return "NCLOB";
        case ORACLE_LOB_TYPE_BFILE: return "BFILE";
        default:                    return "UNKNOWN";
    }
}

const char* OracleLobHandler::get_lob_operation_name(uint8_t operation_type)
{
    switch (operation_type) {
        case ORACLE_LOB_OP_READ:    return "READ";
        case ORACLE_LOB_OP_WRITE:   return "WRITE";
        case ORACLE_LOB_OP_APPEND:  return "APPEND";
        case ORACLE_LOB_OP_TRIM:    return "TRIM";
        case ORACLE_LOB_OP_ERASE:   return "ERASE";
        case ORACLE_LOB_OP_COPY:    return "COPY";
        case ORACLE_LOB_OP_COMPARE: return "COMPARE";
        case ORACLE_LOB_OP_SUBSTR:  return "SUBSTR";
        case ORACLE_LOB_OP_INSTR:   return "INSTR";
        case ORACLE_LOB_OP_LENGTH:  return "LENGTH";
        default:                    return "UNKNOWN";
    }
}

bool OracleLobHandler::is_lob_type_supported(uint8_t lob_type)
{
    return (lob_type >= ORACLE_LOB_TYPE_CLOB && lob_type <= ORACLE_LOB_TYPE_BFILE);
}

// 内部工具方法实现
uint32_t OracleLobHandler::read_uint32_be(const char *data)
{
    return (uint32_t)(((uint8_t)data[0] << 24) |
                     ((uint8_t)data[1] << 16) |
                     ((uint8_t)data[2] << 8) |
                     ((uint8_t)data[3]));
}

uint64_t OracleLobHandler::read_uint64_be(const char *data)
{
    return ((uint64_t)read_uint32_be(data) << 32) | read_uint32_be(data + 4);
}

int OracleLobHandler::parse_locator_flags(uint8_t flags, oracle_lob_locator_t *locator)
{
    locator->is_temporary = (flags & 0x01) != 0;
    locator->is_open = (flags & 0x02) != 0;
    
    LOB_LOG_DEBUG("LOB locator flags: temporary=%s, open=%s",
                 locator->is_temporary ? "yes" : "no",
                 locator->is_open ? "yes" : "no");
    
    return LOB_PARSE_SUCCESS;
}
