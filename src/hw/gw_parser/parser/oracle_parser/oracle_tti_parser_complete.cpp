/*
 * Oracle TTI解析器完整实现示例
 * 展示如何补充缺失的TTI函数码解析逻辑
 * 基于ojdbc源码和Oracle协议规范
 * <AUTHOR> @date 2025
 */

#include "oracle_tti_parser.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>

// 日志宏定义
#define TTI_LOG_DEBUG(fmt, ...) printf("[TTI-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define TTI_LOG_INFO(fmt, ...)  printf("[TTI-INFO] " fmt "\n", ##__VA_ARGS__)
#define TTI_LOG_WARN(fmt, ...)  printf("[TTI-WARN] " fmt "\n", ##__VA_ARGS__)
#define TTI_LOG_ERROR(fmt, ...) printf("[TTI-ERROR] " fmt "\n", ##__VA_ARGS__)

// Oracle函数码解析器映射表
typedef struct oracle_function_descriptor {
    uint16_t function_code;
    const char *function_name;
    int (*parser_func)(const char *data, size_t len, oracle_tti_context_t *ctx);
    bool requires_cursor;
    bool modifies_data;
    const char *description;
} oracle_function_descriptor_t;

// 函数码解析器映射表
static const oracle_function_descriptor_t g_function_descriptors[] = {
    {OOPEN,     "OOPEN",     parse_oopen_function,     true,  false, "打开游标"},
    {OFETCH,    "OFETCH",    parse_ofetch_function,    true,  false, "获取数据"},
    {OCLOSE,    "OCLOSE",    parse_oclose_function,    true,  false, "关闭游标"},
    {OLOGOFF,   "OLOGOFF",   parse_ologoff_function,   false, false, "登出"},
    {OCOMMIT,   "OCOMMIT",   parse_ocommit_function,   false, true,  "提交事务"},
    {OROLLBACK, "OROLLBACK", parse_orollback_function, false, true,  "回滚事务"},
    {OALL7,     "OALL7",     parse_oall7_function,     false, true,  "ALL7 SQL执行"},
    {OSQL7,     "OSQL7",     parse_osql7_function,     false, true,  "SQL7 语句执行"},
    {OALL8,     "OALL8",     parse_oall8_function,     false, true,  "ALL8 增强SQL执行"},
    {OAUTH,     "OAUTH",     parse_oauth_function,     false, false, "认证"},
    {OEXFET,    "OEXFET",    parse_oexfet_function,    true,  false, "执行并获取"},
    {OFLNG,     "OFLNG",     parse_oflng_function,     true,  false, "获取长数据"},
    // 优先级3新增函数码
    {OPARSE,    "OPARSE",    parse_oparse_function,    false, false, "解析SQL"},
    {ODEFIN,    "ODEFIN",    parse_odefin_function,    false, false, "定义输出变量"},
    {OBIND,     "OBIND",     parse_obind_function,     false, false, "绑定变量"},
    {0, NULL, NULL, false, false, NULL} // 结束标记
};

// TTI解析器主入口
int OracleTtiParser::parse_tti_message(const char *data, size_t data_len, uint8_t message_type,
                                      oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 4 || !status || !result) {
        TTI_LOG_ERROR("Invalid parameters for TTI message parsing");
        return TTI_PARSE_ERROR;
    }

    TTI_LOG_INFO("Parsing TTI message: length=%zu", data_len);

    // 解析TTIFUN消息头部
    if (data[0] != TTIFUN) {
        TTI_LOG_ERROR("Expected TTIFUN message, got type %u", data[0]);
        return TTI_PARSE_INVALID_DATA;
    }

    // 提取函数码
    uint16_t function_code = read_uint16_be(data + 2);
    TTI_LOG_DEBUG("Function code: %u (0x%04x)", function_code, function_code);

    // 查找函数码描述符
    const oracle_function_descriptor_t *descriptor = find_function_descriptor(function_code);
    if (!descriptor) {
        TTI_LOG_WARN("Unsupported function code: %u", function_code);
        return TTI_PARSE_UNSUPPORTED;
    }

    TTI_LOG_INFO("Processing %s function", descriptor->function_name);

    // 创建TTI上下文
    oracle_tti_context_t tti_ctx;
    memset(&tti_ctx, 0, sizeof(tti_ctx));
    tti_ctx.function_code = function_code;
    tti_ctx.function_name = descriptor->function_name;
    tti_ctx.requires_cursor = descriptor->requires_cursor;
    tti_ctx.modifies_data = descriptor->modifies_data;
    tti_ctx.status = status;
    tti_ctx.result = result;

    // 调用具体的函数解析器
    int ret = descriptor->parser_func(data, data_len, &tti_ctx);
    
    // 更新解析结果
    if (ret == TTI_PARSE_SUCCESS) {
        result->function_code = function_code;
        result->op_type = map_function_to_operation_type(function_code);
        result->success = 1;

        TTI_LOG_INFO("%s function parsed successfully", descriptor->function_name);
    } else {
        TTI_LOG_ERROR("%s function parsing failed: %d", descriptor->function_name, ret);
    }
    return ret;
}

// OALL7函数解析器实现
int parse_oall7_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_DEBUG("Parsing OALL7 function");
    
    if (data_len < 16) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    size_t offset = 4; // 跳过TTIFUN头部

    // OALL7消息结构（基于ojdbc源码分析）：
    // 字节4-7: 游标ID
    // 字节8-9: SQL语句长度
    // 字节10+: SQL语句文本
    // 之后: 绑定变量信息

    uint32_t cursor_id = read_uint32_be(data + offset);
    offset += 4;

    uint16_t sql_length = read_uint16_be(data + offset);
    offset += 2;

    TTI_LOG_DEBUG("OALL7: cursor_id=%u, sql_length=%u", cursor_id, sql_length);

    // 验证SQL长度合理性
    if (sql_length == 0 || sql_length > 32768) {
        TTI_LOG_ERROR("Invalid SQL length: %u", sql_length);
        return TTI_PARSE_INVALID_DATA;
    }

    if (offset + sql_length > data_len) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    // 提取SQL语句
    char *sql_text = (char*)malloc(sql_length + 1);
    if (!sql_text) {
        return TTI_PARSE_MEMORY_ERROR;
    }

    memcpy(sql_text, data + offset, sql_length);
    sql_text[sql_length] = '\0';
    offset += sql_length;

    // 存储到结果中
    ctx->result->sql_text.s = sql_text;
    ctx->result->sql_text.len = sql_length;
    ctx->result->cursor_id = cursor_id;

    TTI_LOG_INFO("OALL7 SQL: %.*s", (int)sql_length, sql_text);

    // 解析绑定变量（如果有）
    if (offset < data_len) {
        int ret = parse_bind_variables(data, data_len, &offset, ctx);
        if (ret != TTI_PARSE_SUCCESS) {
            TTI_LOG_WARN("Failed to parse bind variables: %d", ret);
            // 不是致命错误，继续处理
        }
    }

    return TTI_PARSE_SUCCESS;
}

// OCOMMIT函数解析器实现
int parse_ocommit_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_DEBUG("Parsing OCOMMIT function");
    
    // OCOMMIT通常是简单的固定长度消息
    if (data_len < 8) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    size_t offset = 4; // 跳过TTIFUN头部

    // OCOMMIT消息结构：
    // 字节4-7: 事务标识符（可选）
    uint32_t transaction_id = 0;
    if (offset + 4 <= data_len) {
        transaction_id = read_uint32_be(data + offset);
        offset += 4;
    }

    TTI_LOG_DEBUG("OCOMMIT: transaction_id=%u", transaction_id);

    // 更新连接状态
    ctx->status->transaction_active = 0;
    ctx->result->transaction_id = transaction_id;
    ctx->result->op_type = ORACLE_OP_COMMIT;

    TTI_LOG_INFO("Transaction committed: ID=%u", transaction_id);
    return TTI_PARSE_SUCCESS;
}

// OROLLBACK函数解析器实现
int parse_orollback_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_DEBUG("Parsing OROLLBACK function");
    
    if (data_len < 8) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    size_t offset = 4; // 跳过TTIFUN头部

    // OROLLBACK消息结构：
    // 字节4-7: 事务标识符（可选）
    // 字节8-9: 保存点名称长度（可选）
    // 字节10+: 保存点名称（可选）

    uint32_t transaction_id = 0;
    if (offset + 4 <= data_len) {
        transaction_id = read_uint32_be(data + offset);
        offset += 4;
    }

    char *savepoint_name = NULL;
    uint16_t savepoint_length = 0;
    
    if (offset + 2 <= data_len) {
        savepoint_length = read_uint16_be(data + offset);
        offset += 2;
        
        if (savepoint_length > 0 && offset + savepoint_length <= data_len) {
            savepoint_name = (char*)malloc(savepoint_length + 1);
            if (savepoint_name) {
                memcpy(savepoint_name, data + offset, savepoint_length);
                savepoint_name[savepoint_length] = '\0';
                offset += savepoint_length;
            }
        }
    }

    TTI_LOG_DEBUG("OROLLBACK: transaction_id=%u, savepoint=%s", 
                 transaction_id, savepoint_name ? savepoint_name : "NULL");

    // 更新连接状态
    if (!savepoint_name) {
        // 完全回滚
        ctx->status->transaction_active = 0;
    }
    
    ctx->result->transaction_id = transaction_id;
    ctx->result->op_type = ORACLE_OP_ROLLBACK;
    
    if (savepoint_name) {
        // 存储保存点名称
        ctx->result->savepoint_name = savepoint_name;
        TTI_LOG_INFO("Transaction rolled back to savepoint: %s", savepoint_name);
    } else {
        TTI_LOG_INFO("Transaction rolled back completely: ID=%u", transaction_id);
    }

    return TTI_PARSE_SUCCESS;
}

// OFETCH函数解析器实现
int parse_ofetch_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_DEBUG("Parsing OFETCH function");
    
    if (data_len < 12) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    size_t offset = 4; // 跳过TTIFUN头部

    // OFETCH消息结构：
    // 字节4-7: 游标ID
    // 字节8-11: 获取行数
    // 字节12-13: 获取方向（可选）

    uint32_t cursor_id = read_uint32_be(data + offset);
    offset += 4;

    uint32_t fetch_rows = read_uint32_be(data + offset);
    offset += 4;

    uint16_t fetch_direction = 0; // 默认向前
    if (offset + 2 <= data_len) {
        fetch_direction = read_uint16_be(data + offset);
        offset += 2;
    }

    TTI_LOG_DEBUG("OFETCH: cursor_id=%u, rows=%u, direction=%u", 
                 cursor_id, fetch_rows, fetch_direction);

    // 验证参数合理性
    if (fetch_rows == 0 || fetch_rows > 10000) {
        TTI_LOG_ERROR("Invalid fetch row count: %u", fetch_rows);
        return TTI_PARSE_INVALID_DATA;
    }

    // 存储到结果中
    ctx->result->cursor_id = cursor_id;
    ctx->result->fetch_rows = fetch_rows;
    ctx->result->fetch_direction = fetch_direction;
    ctx->result->op_type = ORACLE_OP_FETCH;

    TTI_LOG_INFO("OFETCH: cursor=%u, rows=%u", cursor_id, fetch_rows);
    return TTI_PARSE_SUCCESS;
}

// 绑定变量解析
int parse_bind_variables(const char *data, size_t data_len, size_t *offset, oracle_tti_context_t *ctx)
{
    if (*offset + 2 > data_len) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    uint16_t bind_count = read_uint16_be(data + *offset);
    *offset += 2;

    TTI_LOG_DEBUG("Parsing %u bind variables", bind_count);

    if (bind_count == 0) {
        return TTI_PARSE_SUCCESS;
    }

    if (bind_count > 1000) { // 合理性检查
        TTI_LOG_ERROR("Too many bind variables: %u", bind_count);
        return TTI_PARSE_INVALID_DATA;
    }

    // 分配绑定变量数组
    oracle_bind_variable_t *bind_vars = (oracle_bind_variable_t*)calloc(bind_count, sizeof(oracle_bind_variable_t));
    if (!bind_vars) {
        return TTI_PARSE_MEMORY_ERROR;
    }

    // 解析每个绑定变量
    for (uint16_t i = 0; i < bind_count; i++) {
        int ret = parse_bind_variable_data(data, data_len, offset, &bind_vars[i], 0);
        if (ret != TTI_PARSE_SUCCESS) {
            free(bind_vars);
            return ret;
        }
    }

    // 存储到结果中
    ctx->result->bind_variables = bind_vars;
    ctx->result->bind_count = bind_count;

    TTI_LOG_INFO("Parsed %u bind variables successfully", bind_count);
    return TTI_PARSE_SUCCESS;
}

// 工具函数实现
const oracle_function_descriptor_t* find_function_descriptor(uint16_t function_code)
{
    for (int i = 0; g_function_descriptors[i].function_code != 0; i++) {
        if (g_function_descriptors[i].function_code == function_code) {
            return &g_function_descriptors[i];
        }
    }
    return NULL;
}

int map_function_to_operation_type(uint16_t function_code)
{
    switch (function_code) {
        case OALL7:
        case OSQL7:
        case OALL8:
            return ORACLE_OP_SQL_EXECUTE;
        case OCOMMIT:
            return ORACLE_OP_COMMIT;
        case OROLLBACK:
            return ORACLE_OP_ROLLBACK;
        case OFETCH:
            return ORACLE_OP_FETCH;
        case OOPEN:
            return ORACLE_OP_CURSOR_OPEN;
        case OCLOSE:
            return ORACLE_OP_CURSOR_CLOSE;
        case OLOGOFF:
            return ORACLE_OP_LOGOUT;
        case OAUTH:
            return ORACLE_OP_LOGIN;
        case OPARSE:
            return ORACLE_OP_SQL_PARSE;
        case ODEFIN:
            return ORACLE_OP_DEFINE;
        case OBIND:
            return ORACLE_OP_BIND;
        case OFLNG:
            return ORACLE_OP_FETCH;
        default:
            return ORACLE_OP_UNKNOWN;
    }
}

// ========== 优先级3：补充缺失的Oracle函数码解析 ==========

// ODEFIN函数解析（定义输出变量）
int parse_odefin_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_INFO("Parsing ODEFIN (Define Output Variables) function");

    if (data_len < 8) {
        TTI_LOG_ERROR("ODEFIN data too short: %zu bytes", data_len);
        return TTI_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 4; // 跳过TTIFUN头部
    size_t payload_len = data_len - 4;
    size_t offset = 0;

    // ODEFIN消息结构（基于ojdbc源码分析）：
    // 字节0-3: 游标ID
    // 字节4-5: 定义变量数量
    // 之后: 每个定义变量的描述信息

    uint32_t cursor_id = read_uint32_be(payload + offset);
    offset += 4;

    uint16_t define_count = read_uint16_be(payload + offset);
    offset += 2;

    TTI_LOG_DEBUG("ODEFIN: cursor_id=%u, define_count=%u", cursor_id, define_count);

    // 验证参数合理性
    if (define_count == 0 || define_count > 1000) {
        TTI_LOG_ERROR("Invalid define variable count: %u", define_count);
        return TTI_PARSE_INVALID_DATA;
    }

    // 分配定义变量数组
    oracle_define_variable_t *define_vars = (oracle_define_variable_t*)calloc(define_count, sizeof(oracle_define_variable_t));
    if (!define_vars) {
        TTI_LOG_ERROR("Failed to allocate memory for define variables");
        return TTI_PARSE_MEMORY_ERROR;
    }

    // 解析每个定义变量
    for (uint16_t i = 0; i < define_count; i++) {
        if (offset + 12 > payload_len) {
            free(define_vars);
            return TTI_PARSE_NEED_MORE_DATA;
        }

        // 定义变量结构：
        // 字节0-1: 变量索引
        // 字节2: 数据类型
        // 字节3: 精度
        // 字节4-5: 最大长度
        // 字节6-7: 标度
        // 字节8-11: 标志位

        define_vars[i].define_index = read_uint16_be(payload + offset);
        offset += 2;

        define_vars[i].data_type = payload[offset];
        offset += 1;

        define_vars[i].precision = payload[offset];
        offset += 1;

        define_vars[i].max_length = read_uint16_be(payload + offset);
        offset += 2;

        define_vars[i].scale = read_uint16_be(payload + offset);
        offset += 2;

        define_vars[i].flags = read_uint32_be(payload + offset);
        offset += 4;

        define_vars[i].is_null = false;
        define_vars[i].actual_length = 0;

        TTI_LOG_DEBUG("Define var %u: index=%u, type=%u, max_len=%u",
                     i, define_vars[i].define_index, define_vars[i].data_type, define_vars[i].max_length);
    }

    // 更新解析结果
    if (ctx && ctx->result) {
        ctx->result->op_type = ORACLE_OP_DEFINE;
        ctx->result->cursor_id = cursor_id;
        ctx->result->define_variables = define_vars;
        ctx->result->define_count = define_count;
        ctx->result->success = 1;
    }

    TTI_LOG_INFO("ODEFIN parsed successfully: cursor=%u, %u define variables", cursor_id, define_count);
    return TTI_PARSE_SUCCESS;
}

// OBIND函数解析（独立绑定变量）
int parse_obind_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_INFO("Parsing OBIND (Bind Variables) function");

    if (data_len < 8) {
        TTI_LOG_ERROR("OBIND data too short: %zu bytes", data_len);
        return TTI_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 4; // 跳过TTIFUN头部
    size_t payload_len = data_len - 4;
    size_t offset = 0;

    // OBIND消息结构：
    // 字节0-3: 游标ID
    // 字节4-5: 绑定变量数量
    // 字节6: 绑定类型（单个/批量）
    // 字节7: 保留字段
    // 之后: 每个绑定变量的数据

    uint32_t cursor_id = read_uint32_be(payload + offset);
    offset += 4;

    uint16_t bind_count = read_uint16_be(payload + offset);
    offset += 2;

    uint8_t bind_type = payload[offset]; // 0=单个绑定, 1=批量绑定
    offset += 1;

    uint8_t reserved = payload[offset];
    offset += 1;

    TTI_LOG_DEBUG("OBIND: cursor_id=%u, bind_count=%u, bind_type=%u", cursor_id, bind_count, bind_type);

    // 验证参数合理性
    if (bind_count == 0 || bind_count > 1000) {
        TTI_LOG_ERROR("Invalid bind variable count: %u", bind_count);
        return TTI_PARSE_INVALID_DATA;
    }

    // 分配绑定变量数组
    oracle_bind_variable_t *bind_vars = (oracle_bind_variable_t*)calloc(bind_count, sizeof(oracle_bind_variable_t));
    if (!bind_vars) {
        TTI_LOG_ERROR("Failed to allocate memory for bind variables");
        return TTI_PARSE_MEMORY_ERROR;
    }

    // 解析每个绑定变量
    for (uint16_t i = 0; i < bind_count; i++) {
        int ret = parse_bind_variable_data(payload, payload_len, &offset, &bind_vars[i], bind_type);
        if (ret != TTI_PARSE_SUCCESS) {
            free(bind_vars);
            return ret;
        }

        TTI_LOG_DEBUG("Bind var %u: index=%u, type=%u, length=%u",
                     i, bind_vars[i].bind_index, bind_vars[i].data_type, bind_vars[i].actual_length);
    }

    // 更新解析结果
    if (ctx && ctx->result) {
        ctx->result->op_type = ORACLE_OP_BIND;
        ctx->result->cursor_id = cursor_id;
        ctx->result->bind_variables = bind_vars;
        ctx->result->bind_count = bind_count;
        ctx->result->success = 1;
    }

    TTI_LOG_INFO("OBIND parsed successfully: cursor=%u, %u bind variables", cursor_id, bind_count);
    return TTI_PARSE_SUCCESS;
}

// OFLNG函数解析（长数据处理）
int parse_oflng_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_INFO("Parsing OFLNG (Fetch Long Data) function");

    if (data_len < 12) {
        TTI_LOG_ERROR("OFLNG data too short: %zu bytes", data_len);
        return TTI_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 4; // 跳过TTIFUN头部
    size_t payload_len = data_len - 4;
    size_t offset = 0;

    // OFLNG消息结构：
    // 字节0-3: 游标ID
    // 字节4-5: 列索引
    // 字节6-9: 数据偏移
    // 字节10-13: 请求长度
    // 之后: 长数据内容（如果有）

    uint32_t cursor_id = read_uint32_be(payload + offset);
    offset += 4;

    uint16_t column_index = read_uint16_be(payload + offset);
    offset += 2;

    uint32_t data_offset = read_uint32_be(payload + offset);
    offset += 4;

    uint32_t request_length = read_uint32_be(payload + offset);
    offset += 4;

    TTI_LOG_DEBUG("OFLNG: cursor_id=%u, column=%u, offset=%u, length=%u",
                 cursor_id, column_index, data_offset, request_length);

    // 验证参数合理性
    if (request_length > 1024 * 1024 * 100) { // 100MB限制
        TTI_LOG_ERROR("OFLNG request length too large: %u", request_length);
        return TTI_PARSE_INVALID_DATA;
    }

    // 检查是否有长数据内容
    const char *long_data = nullptr;
    size_t long_data_len = 0;
    if (offset < payload_len) {
        long_data = payload + offset;
        long_data_len = payload_len - offset;
    }

    // 更新解析结果
    if (ctx && ctx->result) {
        ctx->result->op_type = ORACLE_OP_FETCH_LONG;
        ctx->result->cursor_id = cursor_id;
        ctx->result->column_index = column_index;
        ctx->result->data_offset = data_offset;
        ctx->result->request_length = request_length;

        if (long_data && long_data_len > 0) {
            // 分配内存存储长数据
            ctx->result->long_data = (char*)malloc(long_data_len);
            if (ctx->result->long_data) {
                memcpy(ctx->result->long_data, long_data, long_data_len);
                ctx->result->long_data_length = long_data_len;
            }
        }

        ctx->result->success = 1;
    }

    TTI_LOG_INFO("OFLNG parsed successfully: cursor=%u, column=%u, data_len=%zu",
                cursor_id, column_index, long_data_len);
    return TTI_PARSE_SUCCESS;
}

// OPARSE函数解析（SQL解析）
int parse_oparse_function(const char *data, size_t data_len, oracle_tti_context_t *ctx)
{
    TTI_LOG_INFO("Parsing OPARSE (Parse SQL) function");

    if (data_len < 10) {
        TTI_LOG_ERROR("OPARSE data too short: %zu bytes", data_len);
        return TTI_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 4; // 跳过TTIFUN头部
    size_t payload_len = data_len - 4;
    size_t offset = 0;

    // OPARSE消息结构：
    // 字节0-3: 游标ID
    // 字节4-5: SQL语句长度
    // 字节6: 解析选项
    // 字节7: 保留字段
    // 之后: SQL语句文本

    uint32_t cursor_id = read_uint32_be(payload + offset);
    offset += 4;

    uint16_t sql_length = read_uint16_be(payload + offset);
    offset += 2;

    uint8_t parse_options = payload[offset];
    offset += 1;

    uint8_t reserved = payload[offset];
    offset += 1;

    TTI_LOG_DEBUG("OPARSE: cursor_id=%u, sql_length=%u, options=0x%02x",
                 cursor_id, sql_length, parse_options);

    // 验证SQL长度
    if (sql_length == 0 || sql_length > 32767) {
        TTI_LOG_ERROR("Invalid SQL length: %u", sql_length);
        return TTI_PARSE_INVALID_DATA;
    }

    if (offset + sql_length > payload_len) {
        TTI_LOG_ERROR("SQL text exceeds payload length");
        return TTI_PARSE_NEED_MORE_DATA;
    }

    // 提取SQL文本
    const char *sql_text = payload + offset;
    offset += sql_length;

    // 分配内存存储SQL文本
    char *sql_copy = (char*)malloc(sql_length + 1);
    if (!sql_copy) {
        TTI_LOG_ERROR("Failed to allocate memory for SQL text");
        return TTI_PARSE_MEMORY_ERROR;
    }
    memcpy(sql_copy, sql_text, sql_length);
    sql_copy[sql_length] = '\0';

    // 识别SQL类型
    oracle_sql_type_t sql_type = identify_sql_type(sql_copy, sql_length);

    // 更新解析结果
    if (ctx && ctx->result) {
        ctx->result->op_type = ORACLE_OP_SQL_PARSE;
        ctx->result->cursor_id = cursor_id;
        ctx->result->sql_text.s = sql_copy;
        ctx->result->sql_text.len = sql_length;
        ctx->result->sql_type = sql_type;
        ctx->result->parse_options = parse_options;
        ctx->result->success = 1;
    }

    TTI_LOG_INFO("OPARSE parsed successfully: cursor=%u, SQL type=%d, length=%u",
                cursor_id, sql_type, sql_length);
    TTI_LOG_DEBUG("SQL text: %.*s", (int)sql_length, sql_text);

    return TTI_PARSE_SUCCESS;
}

// ========== 工具函数实现 ==========

// 大端序读取函数
uint16_t read_uint16_be(const char *data)
{
    return (uint16_t)((((uint8_t)data[0]) << 8) | ((uint8_t)data[1]));
}

uint32_t read_uint32_be(const char *data)
{
    return (uint32_t)((((uint8_t)data[0]) << 24) |
                     (((uint8_t)data[1]) << 16) |
                     (((uint8_t)data[2]) << 8) |
                     ((uint8_t)data[3]));
}

// 绑定变量数据解析
int parse_bind_variable_data(const char *data, size_t data_len, size_t *offset,
                            oracle_bind_variable_t *bind_var, uint8_t bind_type)
{
    if (*offset + 8 > data_len) {
        return TTI_PARSE_NEED_MORE_DATA;
    }

    // 绑定变量数据结构：
    // 字节0-1: 绑定索引
    // 字节2: 数据类型
    // 字节3: 标志位
    // 字节4-5: 最大长度
    // 字节6-7: 实际长度
    // 之后: 数据内容

    bind_var->bind_index = read_uint16_be(data + *offset);
    *offset += 2;

    bind_var->data_type = data[*offset];
    *offset += 1;

    uint8_t flags = data[*offset];
    *offset += 1;

    bind_var->max_length = read_uint16_be(data + *offset);
    *offset += 2;

    bind_var->actual_length = read_uint16_be(data + *offset);
    *offset += 2;

    bind_var->is_null = (flags & 0x01) != 0;

    // 如果不是NULL值，读取数据内容
    if (!bind_var->is_null && bind_var->actual_length > 0) {
        if (*offset + bind_var->actual_length > data_len) {
            return TTI_PARSE_NEED_MORE_DATA;
        }

        // 分配内存存储绑定变量数据
        bind_var->data = malloc(bind_var->actual_length);
        if (!bind_var->data) {
            return TTI_PARSE_MEMORY_ERROR;
        }

        memcpy(bind_var->data, data + *offset, bind_var->actual_length);
        *offset += bind_var->actual_length;
    } else {
        bind_var->data = nullptr;
    }

    return TTI_PARSE_SUCCESS;
}

// SQL类型识别函数
oracle_sql_type_t identify_sql_type(const char *sql_text, size_t sql_length)
{
    if (!sql_text || sql_length == 0) {
        return SQL_TYPE_UNKNOWN;
    }

    // 跳过前导空白字符
    const char *p = sql_text;
    while (p < sql_text + sql_length && isspace(*p)) {
        p++;
    }

    if (p >= sql_text + sql_length) {
        return SQL_TYPE_UNKNOWN;
    }

    // 检查SQL关键字（不区分大小写）
    if (strncasecmp(p, "SELECT", 6) == 0) {
        return SQL_TYPE_SELECT;
    } else if (strncasecmp(p, "INSERT", 6) == 0) {
        return SQL_TYPE_INSERT;
    } else if (strncasecmp(p, "UPDATE", 6) == 0) {
        return SQL_TYPE_UPDATE;
    } else if (strncasecmp(p, "DELETE", 6) == 0) {
        return SQL_TYPE_DELETE;
    } else if (strncasecmp(p, "MERGE", 5) == 0) {
        return SQL_TYPE_MERGE;
    } else if (strncasecmp(p, "CREATE", 6) == 0) {
        return SQL_TYPE_CREATE;
    } else if (strncasecmp(p, "ALTER", 5) == 0) {
        return SQL_TYPE_ALTER;
    } else if (strncasecmp(p, "DROP", 4) == 0) {
        return SQL_TYPE_DROP;
    } else if (strncasecmp(p, "TRUNCATE", 8) == 0) {
        return SQL_TYPE_TRUNCATE;
    } else if (strncasecmp(p, "COMMIT", 6) == 0) {
        return SQL_TYPE_COMMIT;
    } else if (strncasecmp(p, "ROLLBACK", 8) == 0) {
        return SQL_TYPE_ROLLBACK;
    } else if (strncasecmp(p, "SAVEPOINT", 9) == 0) {
        return SQL_TYPE_SAVEPOINT;
    } else if (strncasecmp(p, "CALL", 4) == 0) {
        return SQL_TYPE_CALL_PROCEDURE;
    } else if (strncasecmp(p, "BEGIN", 5) == 0 || strncasecmp(p, "DECLARE", 7) == 0) {
        return SQL_TYPE_PLSQL_BLOCK;
    }

    return SQL_TYPE_UNKNOWN;
}
