/*
 * Oracle协议解析器内存管理器实现
 * 确保所有内存分配和释放的正确配对
 * 提供内存泄漏检测和统计功能
 * <AUTHOR> @date 2025
 */

#include "oracle_memory_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <chrono>

// 日志宏定义
#define MEM_LOG_DEBUG(fmt, ...) if (m_debug_enabled) printf("[MEM-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MEM_LOG_INFO(fmt, ...)  printf("[MEM-INFO] " fmt "\n", ##__VA_ARGS__)
#define MEM_LOG_WARN(fmt, ...)  printf("[MEM-WARN] " fmt "\n", ##__VA_ARGS__)
#define MEM_LOG_ERROR(fmt, ...) printf("[MEM-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleMemoryManager& OracleMemoryManager::getInstance()
{
    static OracleMemoryManager instance;
    return instance;
}

OracleMemoryManager::OracleMemoryManager()
    : m_next_allocation_id(1)
    , m_leak_detection_enabled(true)
    , m_debug_enabled(false)
    , m_max_allocations(100000)
    , m_shutdown_called(false)
{
    memset(&m_statistics, 0, sizeof(m_statistics));
    MEM_LOG_INFO("Oracle Memory Manager initialized");
}

OracleMemoryManager::~OracleMemoryManager()
{
    if (!m_shutdown_called) {
        shutdown();
    }
}

void* OracleMemoryManager::allocate_memory(size_t size, uint8_t type, const char *file, int line, const char *function)
{
    if (size == 0) {
        MEM_LOG_WARN("Attempted to allocate 0 bytes at %s:%d", file ? file : "unknown", line);
        return nullptr;
    }
    
    if (m_shutdown_called) {
        MEM_LOG_ERROR("Memory allocation attempted after shutdown");
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 检查分配限制
    if (m_statistics.current_allocations >= m_max_allocations) {
        MEM_LOG_ERROR("Maximum allocations limit reached: %llu", m_max_allocations);
        return nullptr;
    }
    
    // 分配内存
    void *ptr = malloc(size);
    if (!ptr) {
        MEM_LOG_ERROR("Failed to allocate %zu bytes at %s:%d", size, file ? file : "unknown", line);
        return nullptr;
    }
    
    // 记录分配信息
    memory_allocation_info_t alloc_info;
    alloc_info.ptr = ptr;
    alloc_info.size = size;
    alloc_info.type = type;
    alloc_info.file = file;
    alloc_info.line = line;
    alloc_info.function = function;
    alloc_info.timestamp = get_current_timestamp();
    alloc_info.allocation_id = generate_allocation_id();
    
    m_allocations[ptr] = alloc_info;
    
    // 更新统计信息
    update_statistics_on_alloc(size);
    
    MEM_LOG_DEBUG("Allocated %zu bytes at %p (ID: %u, type: %s) from %s:%d",
                 size, ptr, alloc_info.allocation_id, get_allocation_type_name(type),
                 file ? file : "unknown", line);
    
    return ptr;
}

int OracleMemoryManager::free_memory(void *ptr, const char *file, int line, const char *function)
{
    if (!ptr) {
        // 释放NULL指针是安全的，但记录一下
        MEM_LOG_DEBUG("Attempted to free NULL pointer at %s:%d", file ? file : "unknown", line);
        return MEMORY_SUCCESS;
    }
    
    if (m_shutdown_called) {
        MEM_LOG_WARN("Memory free attempted after shutdown");
        return MEMORY_ERROR;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 查找分配记录
    auto it = m_allocations.find(ptr);
    if (it == m_allocations.end()) {
        MEM_LOG_ERROR("Attempted to free invalid pointer %p at %s:%d", ptr, file ? file : "unknown", line);
        m_statistics.invalid_frees++;
        return MEMORY_INVALID_POINTER;
    }
    
    // 获取分配信息
    memory_allocation_info_t alloc_info = it->second;
    
    // 释放内存
    free(ptr);
    
    // 移除分配记录
    m_allocations.erase(it);
    
    // 更新统计信息
    update_statistics_on_free(alloc_info.size);
    
    MEM_LOG_DEBUG("Freed %zu bytes at %p (ID: %u, type: %s) from %s:%d",
                 alloc_info.size, ptr, alloc_info.allocation_id, 
                 get_allocation_type_name(alloc_info.type),
                 file ? file : "unknown", line);
    
    return MEMORY_SUCCESS;
}

void* OracleMemoryManager::reallocate_memory(void *ptr, size_t new_size, const char *file, int line, const char *function)
{
    if (!ptr) {
        // 相当于malloc
        return allocate_memory(new_size, MEMORY_TYPE_GENERAL, file, line, function);
    }
    
    if (new_size == 0) {
        // 相当于free
        free_memory(ptr, file, line, function);
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 查找原分配记录
    auto it = m_allocations.find(ptr);
    if (it == m_allocations.end()) {
        MEM_LOG_ERROR("Attempted to realloc invalid pointer %p at %s:%d", ptr, file ? file : "unknown", line);
        return nullptr;
    }
    
    memory_allocation_info_t old_alloc_info = it->second;
    
    // 重新分配内存
    void *new_ptr = realloc(ptr, new_size);
    if (!new_ptr) {
        MEM_LOG_ERROR("Failed to reallocate %zu bytes at %s:%d", new_size, file ? file : "unknown", line);
        return nullptr;
    }
    
    // 移除旧记录
    m_allocations.erase(it);
    
    // 添加新记录
    memory_allocation_info_t new_alloc_info;
    new_alloc_info.ptr = new_ptr;
    new_alloc_info.size = new_size;
    new_alloc_info.type = old_alloc_info.type;
    new_alloc_info.file = file;
    new_alloc_info.line = line;
    new_alloc_info.function = function;
    new_alloc_info.timestamp = get_current_timestamp();
    new_alloc_info.allocation_id = generate_allocation_id();
    
    m_allocations[new_ptr] = new_alloc_info;
    
    // 更新统计信息
    m_statistics.current_bytes_used = m_statistics.current_bytes_used - old_alloc_info.size + new_size;
    if (m_statistics.current_bytes_used > m_statistics.peak_bytes_used) {
        m_statistics.peak_bytes_used = m_statistics.current_bytes_used;
    }
    
    MEM_LOG_DEBUG("Reallocated %p to %p, size %zu -> %zu (ID: %u) from %s:%d",
                 ptr, new_ptr, old_alloc_info.size, new_size, new_alloc_info.allocation_id,
                 file ? file : "unknown", line);
    
    return new_ptr;
}

bool OracleMemoryManager::is_valid_pointer(void *ptr)
{
    if (!ptr) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_allocations.find(ptr) != m_allocations.end();
}

bool OracleMemoryManager::is_allocated_pointer(void *ptr)
{
    return is_valid_pointer(ptr);
}

size_t OracleMemoryManager::get_allocation_size(void *ptr)
{
    if (!ptr) {
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_allocations.find(ptr);
    return (it != m_allocations.end()) ? it->second.size : 0;
}

uint8_t OracleMemoryManager::get_allocation_type(void *ptr)
{
    if (!ptr) {
        return MEMORY_TYPE_GENERAL;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_allocations.find(ptr);
    return (it != m_allocations.end()) ? it->second.type : MEMORY_TYPE_GENERAL;
}

int OracleMemoryManager::detect_memory_leaks()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_allocations.empty()) {
        MEM_LOG_INFO("No memory leaks detected");
        return MEMORY_SUCCESS;
    }
    
    MEM_LOG_WARN("Memory leaks detected: %zu allocations not freed", m_allocations.size());
    m_statistics.memory_leaks = m_allocations.size();
    
    return MEMORY_LEAK_DETECTED;
}

int OracleMemoryManager::report_memory_leaks(char *report, size_t report_size)
{
    if (!report || report_size == 0) {
        return MEMORY_ERROR;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    size_t offset = 0;
    offset += snprintf(report + offset, report_size - offset, 
                      "Memory Leak Report\n==================\n");
    
    if (m_allocations.empty()) {
        offset += snprintf(report + offset, report_size - offset, "No memory leaks detected.\n");
        return MEMORY_SUCCESS;
    }
    
    offset += snprintf(report + offset, report_size - offset, 
                      "Total leaks: %zu allocations, %llu bytes\n\n",
                      m_allocations.size(), m_statistics.current_bytes_used);
    
    int leak_count = 0;
    for (const auto& pair : m_allocations) {
        const memory_allocation_info_t& info = pair.second;
        
        if (offset + 200 > report_size) {
            offset += snprintf(report + offset, report_size - offset, "... (truncated)\n");
            break;
        }
        
        offset += snprintf(report + offset, report_size - offset,
                          "Leak #%d: %p, %zu bytes, type=%s, ID=%u\n"
                          "  Allocated at: %s:%d in %s\n\n",
                          ++leak_count, info.ptr, info.size, 
                          get_allocation_type_name(info.type), info.allocation_id,
                          info.file ? info.file : "unknown", info.line,
                          info.function ? info.function : "unknown");
    }
    
    return MEMORY_LEAK_DETECTED;
}

void OracleMemoryManager::get_memory_statistics(memory_statistics_t *stats)
{
    if (!stats) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    *stats = m_statistics;
}

void OracleMemoryManager::print_memory_report()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    printf("\nOracle Memory Manager Report\n");
    printf("============================\n");
    printf("Total allocations:     %llu\n", m_statistics.total_allocations);
    printf("Total deallocations:   %llu\n", m_statistics.total_deallocations);
    printf("Current allocations:   %llu\n", m_statistics.current_allocations);
    printf("Peak allocations:      %llu\n", m_statistics.peak_allocations);
    printf("Total bytes allocated: %llu\n", m_statistics.total_bytes_allocated);
    printf("Total bytes freed:     %llu\n", m_statistics.total_bytes_freed);
    printf("Current bytes used:    %llu\n", m_statistics.current_bytes_used);
    printf("Peak bytes used:       %llu\n", m_statistics.peak_bytes_used);
    printf("Memory leaks:          %llu\n", m_statistics.memory_leaks);
    printf("Double frees:          %llu\n", m_statistics.double_frees);
    printf("Invalid frees:         %llu\n", m_statistics.invalid_frees);
    
    if (m_statistics.total_allocations > 0) {
        double efficiency = (double)m_statistics.total_deallocations / m_statistics.total_allocations * 100.0;
        printf("Memory efficiency:     %.2f%%\n", efficiency);
    }
    
    printf("\n");
}

void OracleMemoryManager::cleanup_all_allocations()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    MEM_LOG_INFO("Cleaning up %zu remaining allocations", m_allocations.size());
    
    for (auto& pair : m_allocations) {
        free(pair.first);
        MEM_LOG_DEBUG("Cleaned up allocation %p (ID: %u)", 
                     pair.first, pair.second.allocation_id);
    }
    
    m_allocations.clear();
    m_statistics.current_allocations = 0;
    m_statistics.current_bytes_used = 0;
}

void OracleMemoryManager::shutdown()
{
    if (m_shutdown_called) {
        return;
    }
    
    MEM_LOG_INFO("Shutting down Oracle Memory Manager");
    
    // 检测内存泄漏
    detect_memory_leaks();
    
    // 打印最终报告
    print_memory_report();
    
    // 清理所有分配
    cleanup_all_allocations();
    
    m_shutdown_called = true;
    MEM_LOG_INFO("Oracle Memory Manager shutdown completed");
}

// 内部工具方法实现
uint32_t OracleMemoryManager::generate_allocation_id()
{
    return m_next_allocation_id++;
}

uint64_t OracleMemoryManager::get_current_timestamp()
{
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

const char* OracleMemoryManager::get_allocation_type_name(uint8_t type)
{
    switch (type) {
        case MEMORY_TYPE_GENERAL:     return "GENERAL";
        case MEMORY_TYPE_TNS_PACKET:  return "TNS_PACKET";
        case MEMORY_TYPE_TTC_MESSAGE: return "TTC_MESSAGE";
        case MEMORY_TYPE_AUTH_DATA:   return "AUTH_DATA";
        case MEMORY_TYPE_LOB_DATA:    return "LOB_DATA";
        case MEMORY_TYPE_BATCH_DATA:  return "BATCH_DATA";
        case MEMORY_TYPE_TEMP:        return "TEMP";
        default:                      return "UNKNOWN";
    }
}

void OracleMemoryManager::update_statistics_on_alloc(size_t size)
{
    m_statistics.total_allocations++;
    m_statistics.current_allocations++;
    m_statistics.total_bytes_allocated += size;
    m_statistics.current_bytes_used += size;

    if (m_statistics.current_allocations > m_statistics.peak_allocations) {
        m_statistics.peak_allocations = m_statistics.current_allocations;
    }

    if (m_statistics.current_bytes_used > m_statistics.peak_bytes_used) {
        m_statistics.peak_bytes_used = m_statistics.current_bytes_used;
    }
}

void OracleMemoryManager::update_statistics_on_free(size_t size)
{
    m_statistics.total_deallocations++;
    m_statistics.current_allocations--;
    m_statistics.total_bytes_freed += size;
    m_statistics.current_bytes_used -= size;
}

// 内存管理工具函数实现
namespace OracleMemoryUtils
{
    char* oracle_safe_strdup(const char *str, uint8_t type, const char *file, int line, const char *function)
    {
        if (!str) {
            return nullptr;
        }

        size_t len = strlen(str);
        char *dup = (char*)OracleMemoryManager::getInstance().allocate_memory(len + 1, type, file, line, function);
        if (dup) {
            memcpy(dup, str, len + 1);
        }

        return dup;
    }

    char* oracle_safe_strndup(const char *str, size_t n, uint8_t type, const char *file, int line, const char *function)
    {
        if (!str) {
            return nullptr;
        }

        size_t len = strnlen(str, n);
        char *dup = (char*)OracleMemoryManager::getInstance().allocate_memory(len + 1, type, file, line, function);
        if (dup) {
            memcpy(dup, str, len);
            dup[len] = '\0';
        }

        return dup;
    }

    void* oracle_safe_memcpy(void *dest, const void *src, size_t n)
    {
        if (!dest || !src || n == 0) {
            return dest;
        }

        // 检查指针有效性
        OracleMemoryManager& mgr = OracleMemoryManager::getInstance();
        if (!mgr.is_valid_pointer(dest)) {
            printf("[MEM-ERROR] Invalid destination pointer in memcpy: %p\n", dest);
            return nullptr;
        }

        // 检查缓冲区大小
        size_t dest_size = mgr.get_allocation_size(dest);
        if (dest_size > 0 && n > dest_size) {
            printf("[MEM-ERROR] Buffer overflow in memcpy: copying %zu bytes to %zu byte buffer\n", n, dest_size);
            return nullptr;
        }

        return memcpy(dest, src, n);
    }

    void* oracle_safe_memmove(void *dest, const void *src, size_t n)
    {
        if (!dest || !src || n == 0) {
            return dest;
        }

        // 检查指针有效性
        OracleMemoryManager& mgr = OracleMemoryManager::getInstance();
        if (!mgr.is_valid_pointer(dest)) {
            printf("[MEM-ERROR] Invalid destination pointer in memmove: %p\n", dest);
            return nullptr;
        }

        return memmove(dest, src, n);
    }

    int oracle_safe_memcmp(const void *s1, const void *s2, size_t n)
    {
        if (!s1 || !s2) {
            return (s1 == s2) ? 0 : (s1 ? 1 : -1);
        }

        return memcmp(s1, s2, n);
    }

    void oracle_safe_memset(void *s, int c, size_t n)
    {
        if (!s || n == 0) {
            return;
        }

        // 检查指针有效性
        OracleMemoryManager& mgr = OracleMemoryManager::getInstance();
        if (!mgr.is_valid_pointer(s)) {
            printf("[MEM-ERROR] Invalid pointer in memset: %p\n", s);
            return;
        }

        memset(s, c, n);
    }

    double calculate_memory_efficiency()
    {
        memory_statistics_t stats;
        OracleMemoryManager::getInstance().get_memory_statistics(&stats);

        if (stats.total_allocations == 0) {
            return 100.0;
        }

        return (double)stats.total_deallocations / stats.total_allocations * 100.0;
    }

    size_t get_memory_fragmentation()
    {
        memory_statistics_t stats;
        OracleMemoryManager::getInstance().get_memory_statistics(&stats);

        // 简化的碎片化计算
        if (stats.peak_bytes_used == 0) {
            return 0;
        }

        return (stats.peak_bytes_used - stats.current_bytes_used) * 100 / stats.peak_bytes_used;
    }

    bool is_memory_pressure_high()
    {
        memory_statistics_t stats;
        OracleMemoryManager::getInstance().get_memory_statistics(&stats);

        // 如果当前分配数超过峰值的80%，认为内存压力较高
        return stats.current_allocations > (stats.peak_allocations * 0.8);
    }

    void dump_memory_map()
    {
        printf("\nMemory Map Dump\n");
        printf("===============\n");

        memory_statistics_t stats;
        OracleMemoryManager::getInstance().get_memory_statistics(&stats);

        printf("Current allocations: %llu\n", stats.current_allocations);
        printf("Current bytes used:  %llu\n", stats.current_bytes_used);
        printf("Memory efficiency:   %.2f%%\n", calculate_memory_efficiency());
        printf("Memory fragmentation: %zu%%\n", get_memory_fragmentation());
        printf("Memory pressure:     %s\n", is_memory_pressure_high() ? "HIGH" : "NORMAL");
        printf("\n");
    }

    bool validate_memory_pattern(void *ptr, size_t size, uint8_t pattern)
    {
        if (!ptr || size == 0) {
            return false;
        }

        uint8_t *bytes = (uint8_t*)ptr;
        for (size_t i = 0; i < size; i++) {
            if (bytes[i] != pattern) {
                return false;
            }
        }

        return true;
    }
}
