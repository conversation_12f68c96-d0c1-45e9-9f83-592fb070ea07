/*
 * Oracle TNS协议解析器头文件
 * 基于ojdbc源码分析实现的TNS协议解析功能
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_TNS_PARSER_H__
#define __ORACLE_TNS_PARSER_H__

#include <inttypes.h>
#include "oracle_parser_common.h"

// TNS解析结果状态
#define TNS_PARSE_SUCCESS           0
#define TNS_PARSE_NEED_MORE_DATA    1
#define TNS_PARSE_ERROR            -1
#define TNS_PARSE_INVALID_PACKET   -2
#define TNS_PARSE_UNSUPPORTED      -3

// TNS解析器类
class OracleTnsParser
{
public:
    OracleTnsParser();
    ~OracleTnsParser();

    // TNS数据包解析主入口
    int parse_tns_packet(const char *data, size_t data_len, int direction, 
                        oracle_status_t *status, oracle_parsed_data_t *result);

    // TNS头部解析
    int parse_tns_header(const char *data, size_t data_len, tns_header_t *header, bool *is_large_packet);
    
    // 各种TNS包类型的解析方法
    int parse_connect_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_accept_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_ack_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_data_packet(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_null_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_refuse_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_redirect_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_abort_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_resend_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_marker_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_attention_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_control_packet(const char *data, size_t data_len, oracle_status_t *status);
    int parse_data_descriptor_packet(const char *data, size_t data_len, oracle_status_t *status);

    // TNS连接数据解析
    int parse_connect_data(const char *data, size_t data_len, oracle_status_t *status);
    int extract_connect_string(const char *connect_data, size_t data_len, 
                              b_string_t *service_name, b_string_t *instance_name);

    // TNS协议版本协商
    int negotiate_protocol_version(uint16_t client_version, uint16_t server_version, oracle_status_t *status);
    
    // TNS数据包验证
    bool validate_tns_packet(const char *data, size_t data_len);
    bool validate_tns_checksum(const tns_header_t *header, const char *data, size_t data_len);
    
    // TNS数据包长度计算
    uint32_t get_tns_packet_length(const char *data, bool is_large_packet);
    uint32_t calculate_required_length(const char *data, size_t available_len);
    
    // TNS错误处理
    int handle_tns_error(uint8_t error_code, oracle_status_t *status);
    const char* get_tns_packet_type_name(uint8_t packet_type);
    const char* get_tns_error_message(int error_code);

    // TNS数据包分片处理
    int handle_fragmented_packet(const char *data, size_t data_len, oracle_status_t *status);
    bool is_packet_complete(const char *data, size_t data_len);
    
    // TNS流量控制
    int handle_flow_control(const tns_header_t *header, oracle_status_t *status);
    
    // 调试和日志
    void dump_tns_header(const tns_header_t *header);
    void dump_tns_packet(const char *data, size_t data_len);

private:
    // 内部工具方法
    uint16_t read_uint16_be(const char *data);  // 大端序读取
    uint32_t read_uint32_be(const char *data);  // 大端序读取
    uint16_t read_uint16_le(const char *data);  // 小端序读取
    uint32_t read_uint32_le(const char *data);  // 小端序读取
    
    // TNS校验和计算
    uint16_t calculate_tns_checksum(const char *data, size_t len);
    uint16_t calculate_header_checksum(const tns_header_t *header);
    
    // TNS连接字符串解析
    int parse_connect_descriptor(const char *desc, size_t len, oracle_status_t *status);
    int extract_parameter_value(const char *desc, size_t len, const char *param_name, 
                               char *value, size_t value_size);
    
    // TNS协议能力协商
    int negotiate_capabilities(const char *data, size_t len, oracle_status_t *status);
    
    // 内部状态
    bool m_debug_enabled;
    uint32_t m_packet_sequence;
    size_t m_bytes_processed;
};

// TNS协议工具函数
namespace OracleTnsUtils
{
    // TNS包类型判断
    bool is_tns_connect_packet(uint8_t packet_type);
    bool is_tns_data_packet(uint8_t packet_type);
    bool is_tns_control_packet(uint8_t packet_type);
    
    // TNS版本处理
    bool is_supported_tns_version(uint16_t version);
    uint16_t get_compatible_tns_version(uint16_t client_version, uint16_t server_version);
    
    // TNS SDU大小处理
    uint16_t negotiate_sdu_size(uint16_t client_sdu, uint16_t server_sdu);
    bool is_large_sdu_supported(uint16_t version);
    
    // TNS错误码映射
    const char* map_tns_error_to_string(uint8_t error_code);
    int map_tns_error_to_oracle_error(uint8_t tns_error);
}

#endif /* __ORACLE_TNS_PARSER_H__ */
