/*
 * Oracle SQL执行流程管理器实现
 * 实现完整的SQL执行流程：解析→绑定→执行→获取结果
 * 支持SQL类型识别、游标生命周期管理和结果集处理
 * <AUTHOR> @date 2025
 */

#include "oracle_sql_executor.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <algorithm>
#include <sstream>

// 日志宏定义
#define SQL_LOG_DEBUG(fmt, ...) printf("[SQL-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define SQL_LOG_INFO(fmt, ...)  printf("[SQL-INFO] " fmt "\n", ##__VA_ARGS__)
#define SQL_LOG_WARN(fmt, ...)  printf("[SQL-WARN] " fmt "\n", ##__VA_ARGS__)
#define SQL_LOG_ERROR(fmt, ...) printf("[SQL-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleSqlExecutor::OracleSqlExecutor()
    : m_next_context_id(1)
    , m_max_contexts(10000)
    , m_context_timeout(3600)  // 1小时超时
    , m_default_fetch_array_size(100)
{
    memset(&m_statistics, 0, sizeof(m_statistics));
    SQL_LOG_INFO("Oracle SQL Executor initialized");
}

OracleSqlExecutor::~OracleSqlExecutor()
{
    // 清理所有SQL上下文
    for (auto& pair : m_sql_contexts) {
        cleanup_sql_context(pair.second);
    }
    m_sql_contexts.clear();
    
    SQL_LOG_INFO("Oracle SQL Executor destroyed, processed %llu SQL statements",
                m_statistics.total_sql_executed);
}

int OracleSqlExecutor::create_sql_context(uint32_t session_id, uint32_t cursor_id, 
                                         const char *sql_text, size_t sql_length,
                                         oracle_sql_context_t **context)
{
    if (!sql_text || sql_length == 0 || !context) {
        SQL_LOG_ERROR("Invalid parameters for SQL context creation");
        return -1;
    }

    // 检查上下文数量限制
    if (m_sql_contexts.size() >= m_max_contexts) {
        SQL_LOG_WARN("Maximum SQL contexts limit reached: %u", m_max_contexts);
        cleanup_expired_contexts(m_context_timeout / 2);
    }

    // 创建新的SQL上下文
    oracle_sql_context_t *ctx = (oracle_sql_context_t*)calloc(1, sizeof(oracle_sql_context_t));
    if (!ctx) {
        return -1;
    }

    // 初始化基本信息
    ctx->session_id = session_id;
    ctx->cursor_id = cursor_id;
    ctx->state = SQL_STATE_INIT;
    ctx->create_time = get_current_timestamp();
    ctx->last_activity_time = ctx->create_time;
    ctx->fetch_array_size = m_default_fetch_array_size;

    // 复制SQL文本
    ctx->sql_text = (char*)malloc(sql_length + 1);
    if (!ctx->sql_text) {
        free(ctx);
        return -1;
    }
    memcpy(ctx->sql_text, sql_text, sql_length);
    ctx->sql_text[sql_length] = '\0';
    ctx->sql_length = sql_length;

    // 识别SQL类型
    ctx->sql_type = identify_sql_type(sql_text, sql_length);
    ctx->sql_hash = calculate_sql_hash(sql_text, sql_length);

    // 分析SQL结构
    analyze_sql_structure(ctx);

    // 添加到上下文映射
    uint64_t context_key = ((uint64_t)session_id << 32) | cursor_id;
    m_sql_contexts[context_key] = ctx;

    *context = ctx;

    SQL_LOG_INFO("Created SQL context: session=%u, cursor=%u, type=%s, hash=0x%08x",
                session_id, cursor_id, get_sql_type_name(ctx->sql_type), ctx->sql_hash);

    return 0;
}

oracle_sql_type_t OracleSqlExecutor::identify_sql_type(const char *sql_text, size_t sql_length)
{
    if (!sql_text || sql_length == 0) {
        return SQL_TYPE_UNKNOWN;
    }

    // 跳过前导空白字符
    const char *p = sql_text;
    while (p < sql_text + sql_length && isspace(*p)) {
        p++;
    }

    if (p >= sql_text + sql_length) {
        return SQL_TYPE_UNKNOWN;
    }

    // 检查SQL关键字（不区分大小写）
    if (strncasecmp(p, "SELECT", 6) == 0) {
        return SQL_TYPE_SELECT;
    } else if (strncasecmp(p, "INSERT", 6) == 0) {
        return SQL_TYPE_INSERT;
    } else if (strncasecmp(p, "UPDATE", 6) == 0) {
        return SQL_TYPE_UPDATE;
    } else if (strncasecmp(p, "DELETE", 6) == 0) {
        return SQL_TYPE_DELETE;
    } else if (strncasecmp(p, "MERGE", 5) == 0) {
        return SQL_TYPE_MERGE;
    } else if (strncasecmp(p, "CREATE", 6) == 0) {
        return SQL_TYPE_CREATE;
    } else if (strncasecmp(p, "ALTER", 5) == 0) {
        return SQL_TYPE_ALTER;
    } else if (strncasecmp(p, "DROP", 4) == 0) {
        return SQL_TYPE_DROP;
    } else if (strncasecmp(p, "TRUNCATE", 8) == 0) {
        return SQL_TYPE_TRUNCATE;
    } else if (strncasecmp(p, "COMMIT", 6) == 0) {
        return SQL_TYPE_COMMIT;
    } else if (strncasecmp(p, "ROLLBACK", 8) == 0) {
        return SQL_TYPE_ROLLBACK;
    } else if (strncasecmp(p, "SAVEPOINT", 9) == 0) {
        return SQL_TYPE_SAVEPOINT;
    } else if (strncasecmp(p, "CALL", 4) == 0) {
        return SQL_TYPE_CALL_PROCEDURE;
    } else if (strncasecmp(p, "BEGIN", 5) == 0 || strncasecmp(p, "DECLARE", 7) == 0) {
        return SQL_TYPE_PLSQL_BLOCK;
    }

    return SQL_TYPE_UNKNOWN;
}

int OracleSqlExecutor::analyze_sql_structure(oracle_sql_context_t *context)
{
    if (!context || !context->sql_text) {
        return -1;
    }

    SQL_LOG_DEBUG("Analyzing SQL structure for context %u:%u", context->session_id, context->cursor_id);

    // 设置结果集标志
    context->has_result_set = (context->sql_type == SQL_TYPE_SELECT);

    // 标准化SQL文本
    int ret = normalize_sql_text(context->sql_text, context->sql_length, &context->normalized_sql);
    if (ret != 0) {
        SQL_LOG_WARN("Failed to normalize SQL text");
    }

    // 检查是否包含绑定变量
    bool has_bind_vars = OracleSqlExecutorUtils::contains_bind_variables(context->sql_text, context->sql_length);
    if (has_bind_vars) {
        SQL_LOG_DEBUG("SQL contains bind variables");
    }

    // 设置自动提交标志（DDL语句通常自动提交）
    context->is_autocommit = (context->sql_type == SQL_TYPE_CREATE ||
                             context->sql_type == SQL_TYPE_ALTER ||
                             context->sql_type == SQL_TYPE_DROP ||
                             context->sql_type == SQL_TYPE_TRUNCATE);

    SQL_LOG_DEBUG("SQL analysis completed: type=%s, has_result_set=%s, autocommit=%s",
                 get_sql_type_name(context->sql_type),
                 context->has_result_set ? "yes" : "no",
                 context->is_autocommit ? "yes" : "no");

    return 0;
}

int OracleSqlExecutor::process_sql_parse(oracle_sql_context_t *context, const char *parse_data, size_t parse_data_len)
{
    if (!context) {
        return -1;
    }

    SQL_LOG_DEBUG("Processing SQL parse for context %u:%u", context->session_id, context->cursor_id);

    uint64_t start_time = get_current_timestamp();

    // 转换到解析状态
    int ret = transition_to_state(context, SQL_STATE_PARSE);
    if (ret != 0) {
        return ret;
    }

    // 这里可以解析Oracle特定的解析数据
    // 例如：解析计划、优化器提示等
    if (parse_data && parse_data_len > 0) {
        SQL_LOG_DEBUG("Parse data length: %zu bytes", parse_data_len);
        // 实际的解析数据处理逻辑
    }

    // 更新统计信息
    context->parse_time = get_current_timestamp() - start_time;
    context->is_prepared = true;
    context->last_activity_time = get_current_timestamp();

    SQL_LOG_INFO("SQL parse completed: session=%u, cursor=%u, time=%llu us",
                context->session_id, context->cursor_id, context->parse_time);

    m_statistics.total_parse_time += context->parse_time;
    return 0;
}

int OracleSqlExecutor::process_sql_bind(oracle_sql_context_t *context, 
                                       const oracle_bind_variable_t *bind_vars, uint16_t bind_count)
{
    if (!context) {
        return -1;
    }

    SQL_LOG_DEBUG("Processing SQL bind for context %u:%u, %u variables", 
                 context->session_id, context->cursor_id, bind_count);

    uint64_t start_time = get_current_timestamp();

    // 转换到绑定状态
    int ret = transition_to_state(context, SQL_STATE_BIND);
    if (ret != 0) {
        return ret;
    }

    // 分配绑定变量数组
    if (bind_count > 0) {
        ret = allocate_bind_variables(context, bind_count);
        if (ret != 0) {
            return ret;
        }

        // 复制绑定变量
        for (uint16_t i = 0; i < bind_count; i++) {
            ret = copy_bind_variable(&context->bind_vars[i], &bind_vars[i]);
            if (ret != 0) {
                SQL_LOG_ERROR("Failed to copy bind variable %u", i);
                return ret;
            }
        }

        context->bind_count = bind_count;

        // 验证绑定变量
        ret = validate_bind_variables(context);
        if (ret != 0) {
            SQL_LOG_ERROR("Bind variable validation failed");
            return ret;
        }
    }

    // 更新统计信息
    context->bind_time = get_current_timestamp() - start_time;
    context->is_bound = true;
    context->last_activity_time = get_current_timestamp();

    SQL_LOG_INFO("SQL bind completed: session=%u, cursor=%u, variables=%u, time=%llu us",
                context->session_id, context->cursor_id, bind_count, context->bind_time);

    return 0;
}

int OracleSqlExecutor::process_sql_execute(oracle_sql_context_t *context, 
                                          const char *execute_data, size_t execute_data_len)
{
    if (!context) {
        return -1;
    }

    SQL_LOG_DEBUG("Processing SQL execute for context %u:%u", context->session_id, context->cursor_id);

    uint64_t start_time = get_current_timestamp();

    // 转换到执行状态
    int ret = transition_to_state(context, SQL_STATE_EXECUTE);
    if (ret != 0) {
        return ret;
    }

    // 处理执行数据
    if (execute_data && execute_data_len > 0) {
        SQL_LOG_DEBUG("Execute data length: %zu bytes", execute_data_len);
        // 这里可以解析执行结果、影响行数等信息
        
        // 简化的行数解析（实际需要根据Oracle协议格式）
        if (execute_data_len >= 4) {
            context->rows_processed = read_uint32_be(execute_data);
            SQL_LOG_DEBUG("Rows processed: %u", context->rows_processed);
        }
    }

    // 更新统计信息
    context->execute_time = get_current_timestamp() - start_time;
    context->is_executed = true;
    context->last_activity_time = get_current_timestamp();

    // 更新全局统计
    m_statistics.total_sql_executed++;
    m_statistics.total_execute_time += context->execute_time;
    m_statistics.total_rows_processed += context->rows_processed;

    // 根据SQL类型更新统计
    switch (context->sql_type) {
        case SQL_TYPE_SELECT:
            m_statistics.select_statements++;
            break;
        case SQL_TYPE_INSERT:
            m_statistics.insert_statements++;
            break;
        case SQL_TYPE_UPDATE:
            m_statistics.update_statements++;
            break;
        case SQL_TYPE_DELETE:
            m_statistics.delete_statements++;
            break;
        case SQL_TYPE_CREATE:
        case SQL_TYPE_ALTER:
        case SQL_TYPE_DROP:
        case SQL_TYPE_TRUNCATE:
            m_statistics.ddl_statements++;
            break;
        case SQL_TYPE_PLSQL_BLOCK:
            m_statistics.plsql_blocks++;
            break;
        case SQL_TYPE_CALL_PROCEDURE:
        case SQL_TYPE_CALL_FUNCTION:
            m_statistics.procedure_calls++;
            break;
        default:
            break;
    }

    SQL_LOG_INFO("SQL execute completed: session=%u, cursor=%u, type=%s, rows=%u, time=%llu us",
                context->session_id, context->cursor_id, get_sql_type_name(context->sql_type),
                context->rows_processed, context->execute_time);

    return 0;
}

// 工具方法实现
uint64_t OracleSqlExecutor::get_current_timestamp()
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000ULL + ts.tv_nsec / 1000ULL;
}

const char* OracleSqlExecutor::get_sql_type_name(oracle_sql_type_t sql_type)
{
    switch (sql_type) {
        case SQL_TYPE_SELECT:           return "SELECT";
        case SQL_TYPE_INSERT:           return "INSERT";
        case SQL_TYPE_UPDATE:           return "UPDATE";
        case SQL_TYPE_DELETE:           return "DELETE";
        case SQL_TYPE_MERGE:            return "MERGE";
        case SQL_TYPE_CREATE:           return "CREATE";
        case SQL_TYPE_ALTER:            return "ALTER";
        case SQL_TYPE_DROP:             return "DROP";
        case SQL_TYPE_TRUNCATE:         return "TRUNCATE";
        case SQL_TYPE_PLSQL_BLOCK:      return "PL/SQL_BLOCK";
        case SQL_TYPE_CALL_PROCEDURE:   return "CALL_PROCEDURE";
        case SQL_TYPE_CALL_FUNCTION:    return "CALL_FUNCTION";
        case SQL_TYPE_COMMIT:           return "COMMIT";
        case SQL_TYPE_ROLLBACK:         return "ROLLBACK";
        case SQL_TYPE_SAVEPOINT:        return "SAVEPOINT";
        default:                        return "UNKNOWN";
    }
}

uint32_t OracleSqlExecutor::calculate_sql_hash(const char *sql_text, size_t sql_length)
{
    // 简单的哈希算法（实际可以使用更复杂的算法）
    uint32_t hash = 0;
    for (size_t i = 0; i < sql_length; i++) {
        hash = hash * 31 + (unsigned char)sql_text[i];
    }
    return hash;
}
