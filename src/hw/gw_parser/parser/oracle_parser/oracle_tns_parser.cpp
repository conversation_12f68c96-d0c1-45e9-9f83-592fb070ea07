/*
 * Oracle TNS协议解析器实现
 * 基于ojdbc源码分析实现的TNS协议解析功能
 * <AUTHOR> @date 2025
 */

#include "oracle_tns_parser.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

// 日志宏定义
#define TNS_LOG_DEBUG(fmt, ...) printf("[TNS-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define TNS_LOG_INFO(fmt, ...)  printf("[TNS-INFO] " fmt "\n", ##__VA_ARGS__)
#define TNS_LOG_WARN(fmt, ...)  printf("[TNS-WARN] " fmt "\n", ##__VA_ARGS__)
#define TNS_LOG_ERROR(fmt, ...) printf("[TNS-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleTnsParser::OracleTnsParser()
    : m_debug_enabled(false)
    , m_packet_sequence(0)
    , m_bytes_processed(0)
{
    TNS_LOG_INFO("Oracle TNS Parser initialized");
}

OracleTnsParser::~OracleTnsParser()
{
    TNS_LOG_INFO("Oracle TNS Parser destroyed, processed %zu bytes", m_bytes_processed);
}

int OracleTnsParser::parse_tns_packet(const char *data, size_t data_len, int direction, 
                                     oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < TNS_HEADER_SIZE || !status) {
        TNS_LOG_ERROR("Invalid parameters for TNS packet parsing");
        return TNS_PARSE_ERROR;
    }

    // 解析TNS头部
    tns_header_t header;
    bool is_large_packet = false;
    int ret = parse_tns_header(data, data_len, &header, &is_large_packet);
    if (ret != TNS_PARSE_SUCCESS) {
        TNS_LOG_ERROR("Failed to parse TNS header, ret=%d", ret);
        return ret;
    }

    // 验证数据包完整性
    if (data_len < header.length) {
        TNS_LOG_DEBUG("Incomplete TNS packet: have %zu bytes, need %u bytes", 
                     data_len, header.length);
        return TNS_PARSE_NEED_MORE_DATA;
    }

    // 更新统计信息
    m_packet_sequence++;
    m_bytes_processed += header.length;

    if (m_debug_enabled) {
        dump_tns_header(&header);
    }

    // 根据包类型进行解析
    switch (header.type) {
        case TNS_PACKET_TYPE_CONNECT:
            return parse_connect_packet(data, header.length, status);

        case TNS_PACKET_TYPE_ACCEPT:
            return parse_accept_packet(data, header.length, status);

        case TNS_PACKET_TYPE_ACK:
            return parse_ack_packet(data, header.length, status);

        case TNS_PACKET_TYPE_DATA:
            return parse_data_packet(data, header.length, status, result);

        case TNS_PACKET_TYPE_NULL:
            return parse_null_packet(data, header.length, status);

        case TNS_PACKET_TYPE_REFUSE:
            return parse_refuse_packet(data, header.length, status);

        case TNS_PACKET_TYPE_REDIRECT:
            return parse_redirect_packet(data, header.length, status);

        case TNS_PACKET_TYPE_ABORT:
            return parse_abort_packet(data, header.length, status);

        case TNS_PACKET_TYPE_RESEND:
            return parse_resend_packet(data, header.length, status);

        case TNS_PACKET_TYPE_MARKER:
            return parse_marker_packet(data, header.length, status);

        case TNS_PACKET_TYPE_ATTENTION:
            return parse_attention_packet(data, header.length, status);

        case TNS_PACKET_TYPE_CONTROL:
            return parse_control_packet(data, header.length, status);

        case TNS_PACKET_TYPE_DATA_DESCRIPTOR:
            return parse_data_descriptor_packet(data, header.length, status);
            
        default:
            TNS_LOG_WARN("Unsupported TNS packet type: %u", header.type);
            return TNS_PARSE_UNSUPPORTED;
    }
}

int OracleTnsParser::parse_tns_header(const char *data, size_t data_len, 
                                     tns_header_t *header, bool *is_large_packet)
{
    if (!data || data_len < TNS_HEADER_SIZE || !header || !is_large_packet) {
        return TNS_PARSE_ERROR;
    }

    memset(header, 0, sizeof(tns_header_t));
    *is_large_packet = false;

    // 读取包长度（前2字节，网络字节序）
    uint16_t initial_length = ntohs(*(uint16_t*)data);

    // 检查是否为大包格式
    if (initial_length == 0) {
        // 大包格式：长度字段为4字节，从偏移2开始
        if (data_len < TNS_LARGE_HEADER_SIZE) {
            return TNS_PARSE_NEED_MORE_DATA;
        }

        // 大包格式结构：
        // 字节0-1: 0x0000 (大包标识)
        // 字节2-5: 实际长度 (4字节，网络字节序)
        // 字节6: 包类型
        // 字节7: 标志位
        header->length = ntohl(*(uint32_t*)(data + 2));
        header->checksum = 0; // 大包格式没有包校验和
        header->type = data[6];
        header->flags = data[7];
        header->header_checksum = 0; // 大包格式没有头部校验和
        *is_large_packet = true;

        TNS_LOG_DEBUG("Large packet detected: length=%u", header->length);

        // 验证大包长度的合理性
        if (header->length < TNS_LARGE_HEADER_SIZE || header->length > TNS_MAX_LARGE_PACKET_SIZE) {
            TNS_LOG_ERROR("Invalid large packet length: %u", header->length);
            return TNS_PARSE_INVALID_PACKET;
        }

    } else {
        // 标准包格式
        // 字节0-1: 包长度
        // 字节2-3: 包校验和
        // 字节4: 包类型
        // 字节5: 标志位
        // 字节6-7: 头部校验和
        header->length = initial_length;
        header->checksum = ntohs(*(uint16_t*)(data + 2));
        header->type = data[4];
        header->flags = data[5];
        header->header_checksum = ntohs(*(uint16_t*)(data + 6));
        *is_large_packet = false;

        // 验证标准包长度的合理性
        if (header->length < TNS_HEADER_SIZE || header->length > TNS_MAX_PACKET_SIZE) {
            TNS_LOG_ERROR("Invalid standard packet length: %u", header->length);
            return TNS_PARSE_INVALID_PACKET;
        }
    }

    // 验证包长度的合理性
    if (header->length < TNS_HEADER_SIZE || header->length > TNS_MAX_LARGE_PACKET_SIZE) {
        TNS_LOG_ERROR("Invalid TNS packet length: %u", header->length);
        return TNS_PARSE_INVALID_PACKET;
    }

    // 验证包类型
    if (header->type < TNS_PACKET_TYPE_CONNECT || header->type > TNS_PACKET_TYPE_CONTROL) {
        TNS_LOG_ERROR("Invalid TNS packet type: %u", header->type);
        return TNS_PARSE_INVALID_PACKET;
    }

    TNS_LOG_DEBUG("Parsed TNS header: type=%u, length=%u, flags=0x%02x, large=%s",
                 header->type, header->length, header->flags, 
                 *is_large_packet ? "yes" : "no");

    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_connect_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    if (data_len < TNS_HEADER_SIZE + 20) { // 最小连接包大小
        return TNS_PARSE_NEED_MORE_DATA;
    }

    TNS_LOG_INFO("Parsing TNS CONNECT packet");

    // 跳过TNS头部，解析连接数据
    const char *connect_data = data + TNS_HEADER_SIZE;
    size_t connect_data_len = data_len - TNS_HEADER_SIZE;

    // 解析协议版本（网络字节序）
    if (connect_data_len >= 2) {
        status->tns_version = ntohs(*(uint16_t*)connect_data);
        TNS_LOG_DEBUG("Client TNS version: %u", status->tns_version);
    }

    // 解析兼容版本
    if (connect_data_len >= 4) {
        status->tns_compatible_version = ntohs(*(uint16_t*)(connect_data + 2));
        TNS_LOG_DEBUG("Client compatible version: %u", status->tns_compatible_version);
    }

    // 解析SDU大小
    if (connect_data_len >= 8) {
        status->sdu_size = ntohs(*(uint16_t*)(connect_data + 6));
        TNS_LOG_DEBUG("Client SDU size: %u", status->sdu_size);
    }

    // 解析最大传输单元大小
    if (connect_data_len >= 10) {
        status->max_transmission_unit = ntohs(*(uint16_t*)(connect_data + 8));
        TNS_LOG_DEBUG("Client MTU size: %u", status->max_transmission_unit);
    }

    // 查找连接描述符字符串
    if (connect_data_len > 20) {
        // 连接描述符通常在固定偏移之后
        const char *desc_start = connect_data + 20;
        size_t desc_len = connect_data_len - 20;
        
        // 查找连接字符串的开始
        for (size_t i = 0; i < desc_len - 10; i++) {
            if (desc_start[i] == '(' && strncmp(desc_start + i, "(DESCRIPTION", 11) == 0) {
                parse_connect_descriptor(desc_start + i, desc_len - i, status);
                break;
            }
        }
    }

    // 更新连接状态
    status->conn_stat = ORACLE_CONN_TNS_CONNECT;
    
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_accept_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    if (data_len < TNS_HEADER_SIZE + 8) {
        return TNS_PARSE_NEED_MORE_DATA;
    }

    TNS_LOG_INFO("Parsing TNS ACCEPT packet");

    // 跳过TNS头部
    const char *accept_data = data + TNS_HEADER_SIZE;
    size_t accept_data_len = data_len - TNS_HEADER_SIZE;

    // 解析服务器版本信息
    if (accept_data_len >= 2) {
        uint16_t server_version = ntohs(*(uint16_t*)accept_data);
        TNS_LOG_DEBUG("Server TNS version: %u", server_version);
        
        // 协商最终使用的协议版本
        negotiate_protocol_version(status->tns_version, server_version, status);
    }

    // 更新连接状态
    status->conn_stat = ORACLE_CONN_TNS_ACCEPT;
    
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_data_packet(const char *data, size_t data_len, 
                                      oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (data_len < TNS_HEADER_SIZE + 2) {
        return TNS_PARSE_NEED_MORE_DATA;
    }

    TNS_LOG_DEBUG("Parsing TNS DATA packet, length=%zu", data_len);

    // 跳过TNS头部，获取数据载荷
    const char *payload = data + TNS_HEADER_SIZE;
    size_t payload_len = data_len - TNS_HEADER_SIZE;

    // 读取数据标志位（前2字节）
    uint16_t data_flags = ntohs(*(uint16_t*)payload);
    TNS_LOG_DEBUG("TNS data flags: 0x%04x", data_flags);

    // 检查是否有更多数据
    bool has_more_data = (data_flags & TNS_DATA_FLAG_MORE_DATA) != 0;
    bool is_eof = (data_flags & TNS_DATA_FLAG_EOF) != 0;

    TNS_LOG_DEBUG("Data packet: more_data=%s, eof=%s", 
                 has_more_data ? "yes" : "no", is_eof ? "yes" : "no");

    // 跳过数据标志位，获取实际的TTC数据
    const char *ttc_data = payload + 2;
    size_t ttc_data_len = payload_len - 2;

    // 这里应该调用TTC解析器来处理TTC消息
    // 暂时返回成功，实际实现需要调用TTC解析器
    TNS_LOG_DEBUG("TTC data length: %zu bytes", ttc_data_len);

    // 更新连接状态
    if (status->conn_stat == ORACLE_CONN_TNS_ACCEPT) {
        status->conn_stat = ORACLE_CONN_HANDSHAKE;
    }

    return TNS_PARSE_SUCCESS;
}

void OracleTnsParser::dump_tns_header(const tns_header_t *header)
{
    if (!header || !m_debug_enabled) {
        return;
    }

    TNS_LOG_DEBUG("=== TNS Header Dump ===");
    TNS_LOG_DEBUG("Length: %u", header->length);
    TNS_LOG_DEBUG("Checksum: 0x%04x", header->checksum);
    TNS_LOG_DEBUG("Type: %u (%s)", header->type, get_tns_packet_type_name(header->type));
    TNS_LOG_DEBUG("Flags: 0x%02x", header->flags);
    TNS_LOG_DEBUG("Header Checksum: 0x%04x", header->header_checksum);
    TNS_LOG_DEBUG("======================");
}

const char* OracleTnsParser::get_tns_packet_type_name(uint8_t packet_type)
{
    switch (packet_type) {
        case TNS_PACKET_TYPE_CONNECT:   return "CONNECT";
        case TNS_PACKET_TYPE_ACCEPT:    return "ACCEPT";
        case TNS_PACKET_TYPE_ACK:       return "ACK";
        case TNS_PACKET_TYPE_REFUSE:    return "REFUSE";
        case TNS_PACKET_TYPE_REDIRECT:  return "REDIRECT";
        case TNS_PACKET_TYPE_DATA:      return "DATA";
        case TNS_PACKET_TYPE_NULL:      return "NULL";
        case TNS_PACKET_TYPE_ABORT:     return "ABORT";
        case TNS_PACKET_TYPE_RESEND:    return "RESEND";
        case TNS_PACKET_TYPE_MARKER:    return "MARKER";
        case TNS_PACKET_TYPE_ATTENTION: return "ATTENTION";
        case TNS_PACKET_TYPE_CONTROL:   return "CONTROL";
        default:                        return "UNKNOWN";
    }
}

int OracleTnsParser::negotiate_protocol_version(uint16_t client_version, uint16_t server_version,
                                               oracle_status_t *status)
{
    // 选择双方都支持的最高版本
    uint16_t negotiated_version = (client_version < server_version) ? client_version : server_version;

    // 确保版本在支持范围内
    if (negotiated_version < TNS_VERSION_MIN) {
        negotiated_version = TNS_VERSION_MIN;
    } else if (negotiated_version > TNS_VERSION_MAX) {
        negotiated_version = TNS_VERSION_MAX;
    }

    status->tns_version = negotiated_version;
    TNS_LOG_INFO("Negotiated TNS version: %u (client=%u, server=%u)",
                negotiated_version, client_version, server_version);

    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_refuse_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS REFUSE packet");

    if (data_len > TNS_HEADER_SIZE) {
        const char *refuse_data = data + TNS_HEADER_SIZE;
        size_t refuse_data_len = data_len - TNS_HEADER_SIZE;

        // 解析拒绝原因
        if (refuse_data_len >= 2) {
            uint16_t refuse_reason = ntohs(*(uint16_t*)refuse_data);
            TNS_LOG_WARN("Connection refused, reason code: %u", refuse_reason);

            status->last_error_code = refuse_reason;
            snprintf(status->last_error_msg, sizeof(status->last_error_msg),
                    "TNS connection refused, code: %u", refuse_reason);
        }
    }

    status->conn_stat = ORACLE_CONN_CLOSED;
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_redirect_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS REDIRECT packet");

    // 重定向包通常包含新的连接信息
    if (data_len > TNS_HEADER_SIZE + 4) {
        const char *redirect_data = data + TNS_HEADER_SIZE;
        size_t redirect_data_len = data_len - TNS_HEADER_SIZE;

        // 查找重定向地址信息
        for (size_t i = 0; i < redirect_data_len - 10; i++) {
            if (redirect_data[i] == '(' && strncmp(redirect_data + i, "(ADDRESS", 8) == 0) {
                TNS_LOG_DEBUG("Found redirect address at offset %zu", i);
                // 这里可以解析新的连接地址
                break;
            }
        }
    }

    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_marker_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_DEBUG("Parsing TNS MARKER packet");

    if (data_len > TNS_HEADER_SIZE) {
        const char *marker_data = data + TNS_HEADER_SIZE;
        size_t marker_data_len = data_len - TNS_HEADER_SIZE;

        if (marker_data_len >= 1) {
            uint8_t marker_type = marker_data[0];
            TNS_LOG_DEBUG("Marker type: %u", marker_type);

            // 处理不同类型的标记
            switch (marker_type) {
                case 1: // Break marker
                    TNS_LOG_DEBUG("Break marker received");
                    break;
                case 2: // Reset marker
                    TNS_LOG_DEBUG("Reset marker received");
                    break;
                default:
                    TNS_LOG_DEBUG("Unknown marker type: %u", marker_type);
                    break;
            }
        }
    }

    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_attention_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_DEBUG("Parsing TNS ATTENTION packet");

    // 注意包通常用于中断当前操作
    if (data_len > TNS_HEADER_SIZE) {
        const char *attention_data = data + TNS_HEADER_SIZE;
        size_t attention_data_len = data_len - TNS_HEADER_SIZE;

        if (attention_data_len >= 1) {
            uint8_t attention_type = attention_data[0];
            TNS_LOG_DEBUG("Attention type: %u", attention_type);
        }
    }

    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_control_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_DEBUG("Parsing TNS CONTROL packet");

    if (data_len > TNS_HEADER_SIZE) {
        const char *control_data = data + TNS_HEADER_SIZE;
        size_t control_data_len = data_len - TNS_HEADER_SIZE;

        if (control_data_len >= 2) {
            uint16_t control_type = ntohs(*(uint16_t*)control_data);
            TNS_LOG_DEBUG("Control type: %u", control_type);

            // 处理不同类型的控制消息
            switch (control_type) {
                case 1: // Dead connection detection
                    TNS_LOG_DEBUG("Dead connection detection");
                    break;
                case 2: // Keep alive
                    TNS_LOG_DEBUG("Keep alive message");
                    break;
                default:
                    TNS_LOG_DEBUG("Unknown control type: %u", control_type);
                    break;
            }
        }
    }

    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_connect_descriptor(const char *desc, size_t len, oracle_status_t *status)
{
    if (!desc || len == 0 || !status) {
        return TNS_PARSE_ERROR;
    }

    TNS_LOG_DEBUG("Parsing connect descriptor, length=%zu", len);

    // 查找SERVICE_NAME参数
    const char *service_start = strstr(desc, "SERVICE_NAME=");
    if (service_start && (service_start - desc) < (int)len) {
        service_start += 13; // 跳过"SERVICE_NAME="
        const char *service_end = strchr(service_start, ')');
        if (service_end && service_end > service_start) {
            size_t service_len = service_end - service_start;
            if (service_len > 0 && service_len < 256) {
                status->service_name.s = service_start;
                status->service_name.len = service_len;
                TNS_LOG_DEBUG("Found service name: %.*s", (int)service_len, service_start);
            }
        }
    }

    // 查找SID参数（旧格式）
    if (status->service_name.s == NULL) {
        const char *sid_start = strstr(desc, "SID=");
        if (sid_start && (sid_start - desc) < (int)len) {
            sid_start += 4; // 跳过"SID="
            const char *sid_end = strchr(sid_start, ')');
            if (sid_end && sid_end > sid_start) {
                size_t sid_len = sid_end - sid_start;
                if (sid_len > 0 && sid_len < 256) {
                    status->service_name.s = sid_start;
                    status->service_name.len = sid_len;
                    TNS_LOG_DEBUG("Found SID: %.*s", (int)sid_len, sid_start);
                }
            }
        }
    }

    return TNS_PARSE_SUCCESS;
}

// 新增的TNS包类型解析方法实现
int OracleTnsParser::parse_ack_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS ACK packet");

    // ACK包通常很简单，只包含确认信息
    if (data_len > TNS_HEADER_SIZE) {
        const char *ack_data = data + TNS_HEADER_SIZE;
        size_t ack_data_len = data_len - TNS_HEADER_SIZE;

        // 解析ACK数据（如果有）
        if (ack_data_len >= 2) {
            uint16_t ack_flags = ntohs(*(uint16_t*)ack_data);
            TNS_LOG_DEBUG("ACK flags: 0x%04x", ack_flags);

            // 根据ojdbc源码，ACK包可能包含序列号确认
            if (ack_data_len >= 4) {
                uint16_t ack_sequence = ntohs(*(uint16_t*)(ack_data + 2));
                TNS_LOG_DEBUG("ACK sequence: %u", ack_sequence);
            }
        }
    }

    // ACK包通常不改变连接状态，只是确认收到
    TNS_LOG_DEBUG("TNS ACK packet processed successfully");
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_null_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS NULL packet");

    // NULL包用于保持连接活跃，通常没有载荷数据
    // 基于ojdbc源码，NULL包主要用于心跳检测

    if (data_len > TNS_HEADER_SIZE) {
        const char *null_data = data + TNS_HEADER_SIZE;
        size_t null_data_len = data_len - TNS_HEADER_SIZE;

        if (null_data_len > 0) {
            TNS_LOG_DEBUG("NULL packet contains %zu bytes of data", null_data_len);
            // 通常NULL包不应该有数据，但如果有，记录下来
        }
    }

    // 更新最后活动时间，保持连接活跃
    status->last_activity_time = time(NULL);

    TNS_LOG_DEBUG("TNS NULL packet processed successfully");
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_abort_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS ABORT packet");

    // ABORT包用于异常终止连接
    if (data_len > TNS_HEADER_SIZE) {
        const char *abort_data = data + TNS_HEADER_SIZE;
        size_t abort_data_len = data_len - TNS_HEADER_SIZE;

        // 解析中止原因
        if (abort_data_len >= 2) {
            uint16_t abort_reason = ntohs(*(uint16_t*)abort_data);
            TNS_LOG_WARN("Connection aborted, reason code: %u", abort_reason);

            status->last_error_code = abort_reason;
            snprintf(status->last_error_msg, sizeof(status->last_error_msg),
                    "TNS connection aborted, code: %u", abort_reason);
        }

        // 如果有额外的错误信息
        if (abort_data_len > 2) {
            const char *error_msg = abort_data + 2;
            size_t msg_len = abort_data_len - 2;

            // 查找错误消息的结束位置
            size_t actual_msg_len = 0;
            for (size_t i = 0; i < msg_len && error_msg[i] != '\0'; i++) {
                actual_msg_len++;
            }

            if (actual_msg_len > 0) {
                TNS_LOG_ERROR("Abort message: %.*s", (int)actual_msg_len, error_msg);
            }
        }
    }

    // 设置连接状态为已关闭
    status->conn_stat = ORACLE_CONN_CLOSED;

    TNS_LOG_DEBUG("TNS ABORT packet processed successfully");
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_resend_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS RESEND packet");

    // RESEND包用于请求重发丢失的数据包
    if (data_len > TNS_HEADER_SIZE) {
        const char *resend_data = data + TNS_HEADER_SIZE;
        size_t resend_data_len = data_len - TNS_HEADER_SIZE;

        // 解析重发请求信息
        if (resend_data_len >= 4) {
            uint16_t start_sequence = ntohs(*(uint16_t*)resend_data);
            uint16_t end_sequence = ntohs(*(uint16_t*)(resend_data + 2));

            TNS_LOG_DEBUG("RESEND request: sequence %u to %u", start_sequence, end_sequence);

            // 这里应该触发重发机制，但在解析器中我们只记录
            status->resend_requests++;
        }

        // 如果有重发原因码
        if (resend_data_len >= 6) {
            uint16_t resend_reason = ntohs(*(uint16_t*)(resend_data + 4));
            TNS_LOG_DEBUG("RESEND reason: %u", resend_reason);
        }
    }

    TNS_LOG_DEBUG("TNS RESEND packet processed successfully");
    return TNS_PARSE_SUCCESS;
}

int OracleTnsParser::parse_data_descriptor_packet(const char *data, size_t data_len, oracle_status_t *status)
{
    TNS_LOG_INFO("Parsing TNS DATA_DESCRIPTOR packet");

    // DATA_DESCRIPTOR包用于描述大数据的分片信息
    // 这是处理大结果集和LOB数据的关键包类型

    if (data_len > TNS_HEADER_SIZE) {
        const char *desc_data = data + TNS_HEADER_SIZE;
        size_t desc_data_len = data_len - TNS_HEADER_SIZE;

        // 解析数据描述符头部
        if (desc_data_len >= 16) {
            uint16_t descriptor_type = ntohs(*(uint16_t*)desc_data);
            uint32_t total_length = ntohl(*(uint32_t*)(desc_data + 2));
            uint16_t fragment_count = ntohs(*(uint16_t*)(desc_data + 6));
            uint32_t sequence_number = ntohl(*(uint32_t*)(desc_data + 10));

            TNS_LOG_DEBUG("DATA_DESCRIPTOR: type=%u, length=%u, fragments=%u, seq=%u",
                         descriptor_type, total_length, fragment_count, sequence_number);

            // 更新状态信息
            status->data_descriptor_received = 1;
            status->expected_data_length = total_length;
            status->expected_fragments = fragment_count;
            status->data_sequence_number = sequence_number;
        }
    }

    TNS_LOG_DEBUG("TNS DATA_DESCRIPTOR packet processed successfully");
    return TNS_PARSE_SUCCESS;
}
