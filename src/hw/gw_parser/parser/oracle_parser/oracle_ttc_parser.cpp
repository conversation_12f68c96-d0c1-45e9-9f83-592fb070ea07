/*
 * Oracle TTC协议解析器实现
 * 基于ojdbc源码分析实现的TTC协议解析功能
 * <AUTHOR> @date 2025
 */

#include "oracle_ttc_parser.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

// 日志宏定义
#define TTC_LOG_DEBUG(fmt, ...) printf("[TTC-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define TTC_LOG_INFO(fmt, ...)  printf("[TTC-INFO] " fmt "\n", ##__VA_ARGS__)
#define TTC_LOG_WARN(fmt, ...)  printf("[TTC-WARN] " fmt "\n", ##__VA_ARGS__)
#define TTC_LOG_ERROR(fmt, ...) printf("[TTC-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleTtcParser::OracleTtcParser()
    : m_debug_enabled(false)
    , m_next_sequence_number(1)
    , m_bytes_processed(0)
{
    TTC_LOG_INFO("Oracle TTC Parser initialized");
}

OracleTtcParser::~OracleTtcParser()
{
    TTC_LOG_INFO("Oracle TTC Parser destroyed, processed %zu bytes", m_bytes_processed);
}

int OracleTtcParser::parse_ttc_message(const char *data, size_t data_len, int direction,
                                      oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 1 || !status) {
        TTC_LOG_ERROR("Invalid parameters for TTC message parsing");
        return TTC_PARSE_ERROR;
    }

    // 读取TTC消息类型（第一个字节）
    uint8_t message_type = data[0];
    
    TTC_LOG_DEBUG("Parsing TTC message: type=%u (%s), length=%zu, direction=%d",
                 message_type, get_ttc_message_type_name(message_type), data_len, direction);

    // 更新统计信息
    m_bytes_processed += data_len;

    // 根据消息类型进行分发处理
    switch (message_type) {
        case TTIPRO:  // 协议协商
            return parse_protocol_negotiation(data, data_len, status);
            
        case TTIDTY:  // 数据类型定义
            return parse_data_type_negotiation(data, data_len, status);
            
        case TTIFUN:  // 函数调用
            return parse_function_call(data, data_len, status, result);
            
        case TTIOER:  // 错误消息
            return parse_error_message(data, data_len, status, result);
            
        case TTIRXH:  // 结果集头部
            return parse_resultset_header(data, data_len, status, result);
            
        case TTIRXD:  // 结果集数据
            return parse_resultset_data(data, data_len, status, result);
            
        case TTIRPA:  // 返回参数
            return parse_return_parameters(data, data_len, status, result);
            
        case TTISTA:  // 状态信息
            return parse_status_info(data, data_len, status, result);
            
        case TTIIOV:  // I/O向量
            return parse_io_vector(data, data_len, status, result);
            
        case TTISLG:  // 会话日志
            return parse_session_log(data, data_len, status, result);
            
        case TTIOAC:  // 输出参数
            return parse_output_parameters(data, data_len, status, result);
            
        case TTILOBD: // LOB数据
            return parse_lob_data(data, data_len, status, result);
            
        case TTIWRN:  // 警告消息
            return parse_warning_message(data, data_len, status, result);
            
        case TTIINIT: // 初始化
            return parse_initialization(data, data_len, status, result);

        // 新增的TTC消息类型处理
        case TTIDCB:  // 数据库回调
            return parse_database_callback(data, data_len, status, result);

        case TTIPFN:  // 预取函数
            return parse_prefetch_function(data, data_len, status, result);

        case TTIFOB:  // 函数对象
            return parse_function_object(data, data_len, status, result);

        case TTIBVC:  // 批量变量
            return parse_bulk_variables(data, data_len, status, result);

        case TTISPF:  // 特殊函数
            return parse_special_function(data, data_len, status, result);

        case TTIQC:   // 查询缓存
            return parse_query_cache(data, data_len, status, result);

        case TTIRSH:  // 结果集句柄
            return parse_resultset_handle(data, data_len, status, result);

        case TTIONEWAYFN: // 单向函数
            return parse_oneway_function(data, data_len, status, result);

        case TTIIMPLRES:  // 隐式结果
            return parse_implicit_result(data, data_len, status, result);

        case TTIRENEG:    // 重新协商
            return parse_renegotiation(data, data_len, status, result);

        case TTIKEYVAL:   // 键值对
            return parse_key_value(data, data_len, status, result);

        default:
            TTC_LOG_WARN("Unsupported TTC message type: %u", message_type);
            return TTC_PARSE_UNSUPPORTED;
    }
}

int OracleTtcParser::parse_protocol_negotiation(const char *data, size_t data_len, oracle_status_t *status)
{
    if (data_len < 8) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    TTC_LOG_INFO("Parsing TTC protocol negotiation");

    // 跳过消息类型字节
    const char *payload = data + 1;
    size_t payload_len = data_len - 1;

    // 解析TTC版本
    if (payload_len >= 1) {
        status->ttc_version = payload[0];
        TTC_LOG_DEBUG("TTC version: %u", status->ttc_version);
    }

    // 解析字符集ID
    if (payload_len >= 2) {
        status->charset_id = payload[1];
        TTC_LOG_DEBUG("Charset ID: %u", status->charset_id);
    }

    // 解析国家字符集ID
    if (payload_len >= 3) {
        status->ncharset_id = payload[2];
        TTC_LOG_DEBUG("National charset ID: %u", status->ncharset_id);
    }

    // 解析服务器能力
    if (payload_len >= 7) {
        status->server_capabilities = read_uint32(payload + 3);
        TTC_LOG_DEBUG("Server capabilities: 0x%08x", status->server_capabilities);
    }

    // 更新连接状态
    status->conn_stat = ORACLE_CONN_PROTOCOL_NEG;

    return TTC_PARSE_SUCCESS;
}

int OracleTtcParser::parse_data_type_negotiation(const char *data, size_t data_len, oracle_status_t *status)
{
    if (data_len < 2) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    TTC_LOG_INFO("Parsing TTC data type negotiation");

    // 跳过消息类型字节
    const char *payload = data + 1;
    size_t payload_len = data_len - 1;

    // 解析数据类型数量
    if (payload_len >= 1) {
        uint8_t type_count = payload[0];
        TTC_LOG_DEBUG("Data type count: %u", type_count);
        
        // 这里可以进一步解析具体的数据类型信息
        // 实际实现中需要根据ojdbc的具体格式来解析
    }

    // 更新连接状态
    status->conn_stat = ORACLE_CONN_DATA_TYPE_NEG;

    return TTC_PARSE_SUCCESS;
}

int OracleTtcParser::parse_function_call(const char *data, size_t data_len, 
                                        oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (data_len < 3) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    TTC_LOG_DEBUG("Parsing TTC function call");

    // 跳过消息类型字节
    const char *payload = data + 1;
    size_t payload_len = data_len - 1;

    // 解析函数码
    if (payload_len >= 1) {
        uint8_t function_code = payload[0];
        TTC_LOG_DEBUG("Function code: %u", function_code);
        
        if (result) {
            result->function_code = function_code;
        }

        // 根据函数码进行具体解析
        switch (function_code) {
            case OOPEN:     // 打开游标
                return parse_open_cursor_function(payload, payload_len, status, result);
                
            case OFETCH:    // 获取数据
                return parse_fetch_function(payload, payload_len, status, result);
                
            case OCLOSE:    // 关闭游标
                return parse_close_cursor_function(payload, payload_len, status, result);
                
            case OLOGOFF:   // 登出
                return parse_logoff_function(payload, payload_len, status, result);
                
            case OCOMMIT:   // 提交
                return parse_commit_function(payload, payload_len, status, result);
                
            case OROLLBACK: // 回滚
                return parse_rollback_function(payload, payload_len, status, result);
                
            case OALL7:     // ALL7
                return parse_all7_function(payload, payload_len, status, result);
                
            case OSQL7:     // SQL7
                return parse_sql7_function(payload, payload_len, status, result);
                
            case O3LOGON:   // 3LOGON
                return parse_3logon_function(payload, payload_len, status, result);
                
            case OALL8:     // ALL8
                return parse_all8_function(payload, payload_len, status, result);
                
            case OAUTH:     // 认证
                return parse_auth_function(payload, payload_len, status, result);
                
            default:
                TTC_LOG_WARN("Unsupported function code: %u", function_code);
                return TTC_PARSE_UNSUPPORTED;
        }
    }

    return TTC_PARSE_SUCCESS;
}

int OracleTtcParser::parse_error_message(const char *data, size_t data_len,
                                        oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (data_len < 4) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    TTC_LOG_INFO("Parsing TTC error message");

    // 跳过消息类型字节
    const char *payload = data + 1;
    size_t payload_len = data_len - 1;

    // 解析错误码
    if (payload_len >= 2) {
        uint16_t error_code = read_uint16(payload);
        TTC_LOG_ERROR("Oracle error code: %u", error_code);
        
        status->last_error_code = error_code;
        
        if (result) {
            result->err_code = error_code;
            result->success = 0;
        }
    }

    // 解析错误消息
    if (payload_len > 2) {
        const char *error_msg = payload + 2;
        size_t msg_len = payload_len - 2;
        
        // 查找错误消息的结束位置
        size_t actual_msg_len = 0;
        for (size_t i = 0; i < msg_len && error_msg[i] != '\0'; i++) {
            actual_msg_len++;
        }
        
        if (actual_msg_len > 0 && actual_msg_len < sizeof(status->last_error_msg)) {
            memcpy(status->last_error_msg, error_msg, actual_msg_len);
            status->last_error_msg[actual_msg_len] = '\0';
            
            TTC_LOG_ERROR("Oracle error message: %s", status->last_error_msg);
            
            if (result) {
                result->err_msg = status->last_error_msg;
            }
        }
    }

    return TTC_PARSE_SUCCESS;
}

int OracleTtcParser::parse_resultset_header(const char *data, size_t data_len,
                                           oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (data_len < 4) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    TTC_LOG_DEBUG("Parsing TTC resultset header");

    // 跳过消息类型字节
    const char *payload = data + 1;
    size_t payload_len = data_len - 1;

    // 解析列数
    if (payload_len >= 2) {
        uint16_t column_count = read_uint16(payload);
        TTC_LOG_DEBUG("Column count: %u", column_count);
        
        if (result) {
            result->column_count = column_count;
            result->op_type = ORACLE_OP_SELECT;
        }
    }

    // 这里可以进一步解析列的详细信息
    // 包括列名、数据类型、长度等

    return TTC_PARSE_SUCCESS;
}

int OracleTtcParser::parse_resultset_data(const char *data, size_t data_len,
                                         oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (data_len < 2) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    TTC_LOG_DEBUG("Parsing TTC resultset data");

    // 跳过消息类型字节
    const char *payload = data + 1;
    size_t payload_len = data_len - 1;

    // 解析行数据
    // 这里需要根据之前解析的列信息来解析具体的行数据
    // 实际实现会比较复杂，需要处理各种Oracle数据类型

    if (result) {
        result->op_type = ORACLE_OP_SELECT;
        // 这里应该增加行数统计
        result->rows_processed++;
    }

    return TTC_PARSE_SUCCESS;
}

const char* OracleTtcParser::get_ttc_message_type_name(uint8_t message_type)
{
    switch (message_type) {
        case TTIPRO:    return "TTIPRO";
        case TTIDTY:    return "TTIDTY";
        case TTIFUN:    return "TTIFUN";
        case TTIOER:    return "TTIOER";
        case TTIRXH:    return "TTIRXH";
        case TTIRXD:    return "TTIRXD";
        case TTIRPA:    return "TTIRPA";
        case TTISTA:    return "TTISTA";
        case TTIIOV:    return "TTIIOV";
        case TTISLG:    return "TTISLG";
        case TTIOAC:    return "TTIOAC";
        case TTILOBD:   return "TTILOBD";
        case TTIWRN:    return "TTIWRN";
        case TTIDCB:    return "TTIDCB";
        case TTIPFN:    return "TTIPFN";
        case TTIFOB:    return "TTIFOB";
        case TTIBVC:    return "TTIBVC";
        case TTISPF:    return "TTISPF";
        case TTIQC:     return "TTIQC";
        case TTIRSH:    return "TTIRSH";
        case TTIONEWAYFN: return "TTIONEWAYFN";
        case TTIIMPLRES: return "TTIIMPLRES";
        case TTIRENEG:  return "TTIRENEG";
        case TTIKEYVAL: return "TTIKEYVAL";
        case TTICOOKIE: return "TTICOOKIE";
        case TTITKN:    return "TTITKN";
        case TTIINIT:   return "TTIINIT";
        default:        return "UNKNOWN";
    }
}

// 内部工具方法实现
uint8_t OracleTtcParser::read_uint8(const char *data)
{
    return (uint8_t)data[0];
}

uint16_t OracleTtcParser::read_uint16(const char *data)
{
    return (uint16_t)(((uint8_t)data[0]) | (((uint8_t)data[1]) << 8));
}

uint32_t OracleTtcParser::read_uint32(const char *data)
{
    return (uint32_t)(((uint8_t)data[0]) |
                     (((uint8_t)data[1]) << 8) |
                     (((uint8_t)data[2]) << 16) |
                     (((uint8_t)data[3]) << 24));
}

uint64_t OracleTtcParser::read_uint64(const char *data)
{
    return ((uint64_t)read_uint32(data)) | (((uint64_t)read_uint32(data + 4)) << 32);
}

// ========== 新增TTC消息类型解析实现 ==========

// TTIBVC（批量变量）消息解析
int OracleTtcParser::parse_bulk_variables(const char *data, size_t data_len,
                                         oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIBVC (Bulk Variables) message");

    if (data_len < 4) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;
    size_t offset = 0;

    // TTIBVC消息结构（基于ojdbc源码分析）：
    // 字节0-1: 变量数量
    // 字节2-5: 批量大小
    // 之后: 变量定义和数据

    uint16_t variable_count = read_uint16_be(payload + offset);
    offset += 2;

    uint32_t batch_size = read_uint32_be(payload + offset);
    offset += 4;

    TTC_LOG_DEBUG("TTIBVC: variable_count=%u, batch_size=%u", variable_count, batch_size);

    // 验证参数合理性
    if (variable_count == 0 || variable_count > 1000) {
        TTC_LOG_ERROR("Invalid variable count: %u", variable_count);
        return TTC_PARSE_INVALID_DATA;
    }

    if (batch_size == 0 || batch_size > 100000) {
        TTC_LOG_ERROR("Invalid batch size: %u", batch_size);
        return TTC_PARSE_INVALID_DATA;
    }

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_BIND;
        result->bind_count = variable_count;
        result->rows_processed = batch_size;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIBVC parsed successfully: %u variables, batch size %u", variable_count, batch_size);
    return TTC_PARSE_SUCCESS;
}

// TTIDCB（数据库回调）消息解析
int OracleTtcParser::parse_database_callback(const char *data, size_t data_len,
                                            oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIDCB (Database Callback) message");

    if (data_len < 3) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIDCB消息结构：
    // 字节0: 回调类型
    // 字节1-2: 回调ID
    // 之后: 回调数据

    uint8_t callback_type = payload[0];
    uint16_t callback_id = read_uint16_be(payload + 1);

    TTC_LOG_DEBUG("TTIDCB: callback_type=%u, callback_id=%u", callback_type, callback_id);

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_UNKNOWN;
        result->function_code = callback_type;
        result->cursor_id = callback_id;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIDCB parsed successfully: type=%u, id=%u", callback_type, callback_id);
    return TTC_PARSE_SUCCESS;
}

// TTIPFN（预取函数）消息解析
int OracleTtcParser::parse_prefetch_function(const char *data, size_t data_len,
                                            oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIPFN (Prefetch Function) message");

    if (data_len < 5) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIPFN消息结构：
    // 字节0-3: 游标ID
    // 字节4-5: 预取行数
    // 之后: 预取选项

    uint32_t cursor_id = read_uint32_be(payload);
    uint16_t prefetch_rows = read_uint16_be(payload + 4);

    TTC_LOG_DEBUG("TTIPFN: cursor_id=%u, prefetch_rows=%u", cursor_id, prefetch_rows);

    // 验证参数合理性
    if (prefetch_rows == 0 || prefetch_rows > 10000) {
        TTC_LOG_ERROR("Invalid prefetch row count: %u", prefetch_rows);
        return TTC_PARSE_INVALID_DATA;
    }

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_FETCH;
        result->cursor_id = cursor_id;
        result->fetch_rows = prefetch_rows;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIPFN parsed successfully: cursor=%u, prefetch=%u rows", cursor_id, prefetch_rows);
    return TTC_PARSE_SUCCESS;
}

// TTIFOB（函数对象）消息解析
int OracleTtcParser::parse_function_object(const char *data, size_t data_len,
                                          oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIFOB (Function Object) message");

    if (data_len < 6) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIFOB消息结构：
    // 字节0-1: 对象类型
    // 字节2-5: 对象ID
    // 之后: 对象数据

    uint16_t object_type = read_uint16_be(payload);
    uint32_t object_id = read_uint32_be(payload + 2);

    TTC_LOG_DEBUG("TTIFOB: object_type=%u, object_id=%u", object_type, object_id);

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_CALL;
        result->function_code = object_type;
        result->cursor_id = object_id;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIFOB parsed successfully: type=%u, id=%u", object_type, object_id);
    return TTC_PARSE_SUCCESS;
}

// TTISPF（特殊函数）消息解析
int OracleTtcParser::parse_special_function(const char *data, size_t data_len,
                                           oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTISPF (Special Function) message");

    if (data_len < 3) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTISPF消息结构：
    // 字节0: 特殊函数类型
    // 字节1-2: 函数参数
    // 之后: 函数数据

    uint8_t function_type = payload[0];
    uint16_t function_param = read_uint16_be(payload + 1);

    TTC_LOG_DEBUG("TTISPF: function_type=%u, function_param=%u", function_type, function_param);

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_UNKNOWN;
        result->function_code = function_type;
        result->cursor_id = function_param;
        result->success = 1;
    }

    TTC_LOG_INFO("TTISPF parsed successfully: type=%u, param=%u", function_type, function_param);
    return TTC_PARSE_SUCCESS;
}

// TTIQC（查询缓存）消息解析
int OracleTtcParser::parse_query_cache(const char *data, size_t data_len,
                                      oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIQC (Query Cache) message");

    if (data_len < 8) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIQC消息结构：
    // 字节0-3: 查询哈希
    // 字节4-5: 缓存操作类型
    // 字节6-7: 缓存大小
    // 之后: 缓存数据

    uint32_t query_hash = read_uint32_be(payload);
    uint16_t cache_operation = read_uint16_be(payload + 4);
    uint16_t cache_size = read_uint16_be(payload + 6);

    TTC_LOG_DEBUG("TTIQC: query_hash=0x%08x, operation=%u, size=%u",
                 query_hash, cache_operation, cache_size);

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_SELECT;
        result->sql_hash = query_hash;
        result->function_code = cache_operation;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIQC parsed successfully: hash=0x%08x, operation=%u", query_hash, cache_operation);
    return TTC_PARSE_SUCCESS;
}

// TTIRSH（结果集句柄）消息解析
int OracleTtcParser::parse_resultset_handle(const char *data, size_t data_len,
                                           oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIRSH (Resultset Handle) message");

    if (data_len < 8) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIRSH消息结构：
    // 字节0-3: 结果集句柄ID
    // 字节4-7: 结果集大小
    // 之后: 结果集元数据

    uint32_t handle_id = read_uint32_be(payload);
    uint32_t resultset_size = read_uint32_be(payload + 4);

    TTC_LOG_DEBUG("TTIRSH: handle_id=%u, resultset_size=%u", handle_id, resultset_size);

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_SELECT;
        result->cursor_id = handle_id;
        result->rows_processed = resultset_size;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIRSH parsed successfully: handle=%u, size=%u", handle_id, resultset_size);
    return TTC_PARSE_SUCCESS;
}

// TTIONEWAYFN（单向函数）消息解析
int OracleTtcParser::parse_oneway_function(const char *data, size_t data_len,
                                          oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIONEWAYFN (Oneway Function) message");

    if (data_len < 2) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIONEWAYFN消息结构：
    // 字节0: 函数类型
    // 之后: 函数数据

    uint8_t function_type = payload[0];

    TTC_LOG_DEBUG("TTIONEWAYFN: function_type=%u", function_type);

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_UNKNOWN;
        result->function_code = function_type;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIONEWAYFN parsed successfully: type=%u", function_type);
    return TTC_PARSE_SUCCESS;
}

// TTIIMPLRES（隐式结果）消息解析
int OracleTtcParser::parse_implicit_result(const char *data, size_t data_len,
                                          oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIIMPLRES (Implicit Result) message");

    if (data_len < 4) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIIMPLRES消息结构：
    // 字节0-3: 结果集数量
    // 之后: 结果集数据

    uint32_t result_count = read_uint32_be(payload);

    TTC_LOG_DEBUG("TTIIMPLRES: result_count=%u", result_count);

    // 验证参数合理性
    if (result_count > 1000) {
        TTC_LOG_ERROR("Invalid result count: %u", result_count);
        return TTC_PARSE_INVALID_DATA;
    }

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_SELECT;
        result->rows_processed = result_count;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIIMPLRES parsed successfully: %u results", result_count);
    return TTC_PARSE_SUCCESS;
}

// TTIRENEG（重新协商）消息解析
int OracleTtcParser::parse_renegotiation(const char *data, size_t data_len,
                                        oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIRENEG (Renegotiation) message");

    if (data_len < 4) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIRENEG消息结构：
    // 字节0-1: 协商类型
    // 字节2-3: 协商版本
    // 之后: 协商数据

    uint16_t negotiation_type = read_uint16_be(payload);
    uint16_t negotiation_version = read_uint16_be(payload + 2);

    TTC_LOG_DEBUG("TTIRENEG: type=%u, version=%u", negotiation_type, negotiation_version);

    // 更新连接状态
    if (status) {
        status->ttc_version = negotiation_version;
    }

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_UNKNOWN;
        result->function_code = negotiation_type;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIRENEG parsed successfully: type=%u, version=%u", negotiation_type, negotiation_version);
    return TTC_PARSE_SUCCESS;
}

// TTIKEYVAL（键值对）消息解析
int OracleTtcParser::parse_key_value(const char *data, size_t data_len,
                                    oracle_status_t *status, oracle_parsed_data_t *result)
{
    TTC_LOG_INFO("Parsing TTIKEYVAL (Key-Value) message");

    if (data_len < 4) {
        return TTC_PARSE_NEED_MORE_DATA;
    }

    const char *payload = data + 1; // 跳过消息类型字节
    size_t payload_len = data_len - 1;

    // TTIKEYVAL消息结构：
    // 字节0-1: 键值对数量
    // 字节2-3: 数据长度
    // 之后: 键值对数据

    uint16_t pair_count = read_uint16_be(payload);
    uint16_t data_length = read_uint16_be(payload + 2);

    TTC_LOG_DEBUG("TTIKEYVAL: pair_count=%u, data_length=%u", pair_count, data_length);

    // 验证参数合理性
    if (pair_count > 100) {
        TTC_LOG_ERROR("Invalid key-value pair count: %u", pair_count);
        return TTC_PARSE_INVALID_DATA;
    }

    // 更新解析结果
    if (result) {
        result->op_type = ORACLE_OP_UNKNOWN;
        result->bind_count = pair_count;
        result->success = 1;
    }

    TTC_LOG_INFO("TTIKEYVAL parsed successfully: %u pairs", pair_count);
    return TTC_PARSE_SUCCESS;
}

// 大端序读取工具函数
uint16_t OracleTtcParser::read_uint16_be(const char *data)
{
    return (uint16_t)((((uint8_t)data[0]) << 8) | ((uint8_t)data[1]));
}

uint32_t OracleTtcParser::read_uint32_be(const char *data)
{
    return (uint32_t)((((uint8_t)data[0]) << 24) |
                     (((uint8_t)data[1]) << 16) |
                     (((uint8_t)data[2]) << 8) |
                     ((uint8_t)data[3]));
}
