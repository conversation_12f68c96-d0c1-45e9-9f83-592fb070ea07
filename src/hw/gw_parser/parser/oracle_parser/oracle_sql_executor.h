/*
 * Oracle SQL执行流程管理器头文件
 * 实现完整的SQL执行流程：解析→绑定→执行→获取结果
 * 支持SQL类型识别、游标生命周期管理和结果集处理
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_SQL_EXECUTOR_H__
#define __ORACLE_SQL_EXECUTOR_H__

#include <inttypes.h>
#include <map>
#include <vector>
#include <string>
#include "oracle_parser_common.h"

// SQL执行状态
typedef enum {
    SQL_STATE_INIT,             // 初始状态
    SQL_STATE_PARSE,            // SQL解析阶段
    SQL_STATE_BIND,             // 变量绑定阶段
    SQL_STATE_EXECUTE,          // SQL执行阶段
    SQL_STATE_DEFINE,           // 输出变量定义阶段
    SQL_STATE_FETCH,            // 数据获取阶段
    SQL_STATE_COMPLETE,         // 执行完成
    SQL_STATE_ERROR,            // 执行错误
    SQL_STATE_CANCELLED         // 执行取消
} oracle_sql_execution_state_t;

// SQL类型
typedef enum {
    SQL_TYPE_SELECT,            // SELECT查询
    SQL_TYPE_INSERT,            // INSERT插入
    SQL_TYPE_UPDATE,            // UPDATE更新
    SQL_TYPE_DELETE,            // DELETE删除
    SQL_TYPE_MERGE,             // MERGE合并
    SQL_TYPE_CREATE,            // CREATE创建
    SQL_TYPE_ALTER,             // ALTER修改
    SQL_TYPE_DROP,              // DROP删除
    SQL_TYPE_TRUNCATE,          // TRUNCATE截断
    SQL_TYPE_PLSQL_BLOCK,       // PL/SQL块
    SQL_TYPE_CALL_PROCEDURE,    // 存储过程调用
    SQL_TYPE_CALL_FUNCTION,     // 函数调用
    SQL_TYPE_COMMIT,            // 提交
    SQL_TYPE_ROLLBACK,          // 回滚
    SQL_TYPE_SAVEPOINT,         // 保存点
    SQL_TYPE_UNKNOWN            // 未知类型
} oracle_sql_type_t;

// SQL执行上下文
typedef struct oracle_sql_context
{
    uint32_t session_id;                    // 会话ID
    uint32_t cursor_id;                     // 游标ID
    oracle_sql_execution_state_t state;    // 执行状态
    oracle_sql_type_t sql_type;            // SQL类型
    
    // SQL文本信息
    char     *sql_text;                     // SQL文本
    size_t   sql_length;                    // SQL长度
    char     *normalized_sql;               // 标准化SQL
    uint32_t sql_hash;                      // SQL哈希值
    
    // 绑定变量信息
    oracle_bind_variable_t *bind_vars;      // 绑定变量数组
    uint16_t bind_count;                    // 绑定变量数量
    uint16_t bind_capacity;                 // 绑定变量容量
    
    // 定义变量信息（SELECT语句的输出列）
    oracle_define_variable_t *define_vars;  // 定义变量数组
    uint16_t define_count;                  // 定义变量数量
    uint16_t define_capacity;               // 定义变量容量
    
    // 执行结果信息
    uint32_t rows_processed;                // 处理的行数
    uint32_t rows_fetched;                  // 获取的行数
    bool     more_data_available;           // 是否还有更多数据
    uint32_t fetch_array_size;              // 批量获取大小
    
    // 性能统计
    uint64_t parse_time;                    // 解析时间（微秒）
    uint64_t bind_time;                     // 绑定时间（微秒）
    uint64_t execute_time;                  // 执行时间（微秒）
    uint64_t fetch_time;                    // 获取时间（微秒）
    uint64_t total_time;                    // 总时间（微秒）
    
    // 错误信息
    int      error_code;                    // 错误码
    char     error_message[512];            // 错误消息
    uint32_t error_position;                // 错误位置
    
    // 时间戳
    uint64_t create_time;                   // 创建时间
    uint64_t last_activity_time;            // 最后活动时间
    uint64_t complete_time;                 // 完成时间
    
    // 状态标志
    bool     is_prepared;                   // 是否已准备
    bool     is_bound;                      // 是否已绑定
    bool     is_executed;                   // 是否已执行
    bool     is_complete;                   // 是否已完成
    bool     has_result_set;                // 是否有结果集
    bool     is_autocommit;                 // 是否自动提交
} oracle_sql_context_t;

// SQL执行统计信息
typedef struct oracle_sql_statistics
{
    uint64_t total_sql_executed;            // 总执行SQL数
    uint64_t select_statements;             // SELECT语句数
    uint64_t insert_statements;             // INSERT语句数
    uint64_t update_statements;             // UPDATE语句数
    uint64_t delete_statements;             // DELETE语句数
    uint64_t ddl_statements;                // DDL语句数
    uint64_t plsql_blocks;                  // PL/SQL块数
    uint64_t procedure_calls;               // 存储过程调用数
    
    uint64_t total_rows_processed;          // 总处理行数
    uint64_t total_rows_fetched;            // 总获取行数
    uint64_t total_parse_time;              // 总解析时间
    uint64_t total_execute_time;            // 总执行时间
    uint64_t total_fetch_time;              // 总获取时间
    
    uint64_t parse_errors;                  // 解析错误数
    uint64_t bind_errors;                   // 绑定错误数
    uint64_t execute_errors;                // 执行错误数
    uint64_t fetch_errors;                  // 获取错误数
    
    double   avg_parse_time;                // 平均解析时间
    double   avg_execute_time;              // 平均执行时间
    double   avg_fetch_time;                // 平均获取时间
} oracle_sql_statistics_t;

// Oracle SQL执行器类
class OracleSqlExecutor
{
public:
    OracleSqlExecutor();
    ~OracleSqlExecutor();

    // SQL执行流程管理
    int create_sql_context(uint32_t session_id, uint32_t cursor_id, const char *sql_text, size_t sql_length,
                          oracle_sql_context_t **context);
    
    int process_sql_parse(oracle_sql_context_t *context, const char *parse_data, size_t parse_data_len);
    int process_sql_bind(oracle_sql_context_t *context, const oracle_bind_variable_t *bind_vars, uint16_t bind_count);
    int process_sql_execute(oracle_sql_context_t *context, const char *execute_data, size_t execute_data_len);
    int process_sql_define(oracle_sql_context_t *context, const oracle_define_variable_t *define_vars, uint16_t define_count);
    int process_sql_fetch(oracle_sql_context_t *context, uint32_t fetch_rows, const char *fetch_data, size_t fetch_data_len);
    
    // SQL类型识别和分析
    oracle_sql_type_t identify_sql_type(const char *sql_text, size_t sql_length);
    int analyze_sql_structure(oracle_sql_context_t *context);
    int normalize_sql_text(const char *sql_text, size_t sql_length, char **normalized_sql);
    uint32_t calculate_sql_hash(const char *sql_text, size_t sql_length);
    
    // 游标生命周期管理
    int open_cursor(uint32_t session_id, uint32_t cursor_id, uint32_t cursor_options);
    int close_cursor(uint32_t session_id, uint32_t cursor_id);
    oracle_sql_context_t* find_cursor_context(uint32_t session_id, uint32_t cursor_id);
    
    // 结果集处理
    int process_result_set_header(oracle_sql_context_t *context, const char *header_data, size_t header_data_len);
    int process_result_set_data(oracle_sql_context_t *context, const char *data, size_t data_len);
    int extract_result_rows(oracle_sql_context_t *context, const char *row_data, size_t row_data_len);
    
    // 事务处理
    int process_commit(uint32_t session_id, uint32_t transaction_id);
    int process_rollback(uint32_t session_id, uint32_t transaction_id, const char *savepoint_name);
    int process_savepoint(uint32_t session_id, const char *savepoint_name);
    
    // 错误处理
    int handle_sql_error(oracle_sql_context_t *context, int error_code, const char *error_message, uint32_t error_position);
    int recover_from_error(oracle_sql_context_t *context);
    
    // 性能监控
    void update_execution_statistics(oracle_sql_context_t *context);
    void get_sql_statistics(oracle_sql_statistics_t *stats);
    void reset_statistics();
    
    // 内存管理
    void cleanup_sql_context(oracle_sql_context_t *context);
    void cleanup_expired_contexts(uint32_t timeout_seconds);
    
    // 调试和诊断
    void dump_sql_context(const oracle_sql_context_t *context);
    void dump_execution_plan(const oracle_sql_context_t *context);
    
    // 配置管理
    void set_max_contexts(uint32_t max_contexts) { m_max_contexts = max_contexts; }
    void set_context_timeout(uint32_t timeout_seconds) { m_context_timeout = timeout_seconds; }
    void set_fetch_array_size(uint32_t array_size) { m_default_fetch_array_size = array_size; }

private:
    // 内部SQL分析方法
    bool is_select_statement(const char *sql_text, size_t sql_length);
    bool is_dml_statement(const char *sql_text, size_t sql_length);
    bool is_ddl_statement(const char *sql_text, size_t sql_length);
    bool is_plsql_block(const char *sql_text, size_t sql_length);
    bool is_procedure_call(const char *sql_text, size_t sql_length);
    
    // SQL文本处理
    int remove_sql_comments(const char *sql_text, size_t sql_length, char **clean_sql);
    int extract_sql_keywords(const char *sql_text, size_t sql_length, std::vector<std::string> &keywords);
    int validate_sql_syntax(const char *sql_text, size_t sql_length);
    
    // 绑定变量处理
    int allocate_bind_variables(oracle_sql_context_t *context, uint16_t bind_count);
    int copy_bind_variable(oracle_bind_variable_t *dest, const oracle_bind_variable_t *src);
    int validate_bind_variables(oracle_sql_context_t *context);
    
    // 定义变量处理
    int allocate_define_variables(oracle_sql_context_t *context, uint16_t define_count);
    int copy_define_variable(oracle_define_variable_t *dest, const oracle_define_variable_t *src);
    int validate_define_variables(oracle_sql_context_t *context);
    
    // 状态转换
    int transition_to_state(oracle_sql_context_t *context, oracle_sql_execution_state_t new_state);
    bool is_valid_state_transition(oracle_sql_execution_state_t from_state, oracle_sql_execution_state_t to_state);
    
    // 工具方法
    uint64_t get_current_timestamp();
    uint32_t generate_context_id();
    const char* get_sql_type_name(oracle_sql_type_t sql_type);
    const char* get_execution_state_name(oracle_sql_execution_state_t state);
    
    // 数据成员
    std::map<uint64_t, oracle_sql_context_t*> m_sql_contexts;  // SQL上下文映射 (session_id << 32 | cursor_id)
    uint32_t m_next_context_id;             // 下一个上下文ID
    uint32_t m_max_contexts;                // 最大上下文数量
    uint32_t m_context_timeout;             // 上下文超时时间（秒）
    uint32_t m_default_fetch_array_size;    // 默认批量获取大小
    
    // 统计信息
    oracle_sql_statistics_t m_statistics;
};

// SQL执行器工具函数
namespace OracleSqlExecutorUtils
{
    // SQL类型工具
    const char* get_sql_type_description(oracle_sql_type_t sql_type);
    bool is_query_statement(oracle_sql_type_t sql_type);
    bool is_modification_statement(oracle_sql_type_t sql_type);
    bool requires_result_set(oracle_sql_type_t sql_type);
    
    // SQL文本工具
    std::string trim_sql_text(const std::string& sql_text);
    std::string uppercase_sql_keywords(const std::string& sql_text);
    bool contains_bind_variables(const char *sql_text, size_t sql_length);
    
    // 性能工具
    double calculate_execution_efficiency(const oracle_sql_context_t *context);
    uint32_t estimate_result_set_size(const oracle_sql_context_t *context);
    bool should_use_array_fetch(const oracle_sql_context_t *context);
    
    // 安全工具
    bool is_sql_injection_attempt(const char *sql_text, size_t sql_length);
    bool contains_dangerous_keywords(const char *sql_text, size_t sql_length);
    int sanitize_sql_text(const char *sql_text, size_t sql_length, char **sanitized_sql);
}

#endif /* __ORACLE_SQL_EXECUTOR_H__ */
