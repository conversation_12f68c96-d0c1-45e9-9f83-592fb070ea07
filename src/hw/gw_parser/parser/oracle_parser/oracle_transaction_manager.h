/*
 * Oracle事务处理管理器头文件
 * 实现完整的事务状态管理、分布式事务支持和保存点处理
 * 支持Oracle事务的完整生命周期管理
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_TRANSACTION_MANAGER_H__
#define __ORACLE_TRANSACTION_MANAGER_H__

#include <inttypes.h>
#include <map>
#include <vector>
#include <string>
#include "oracle_parser_common.h"

// 事务状态
typedef enum {
    TRANSACTION_STATE_INACTIVE,     // 非活跃状态
    TRANSACTION_STATE_ACTIVE,       // 活跃状态
    TRANSACTION_STATE_PREPARED,     // 已准备状态（分布式事务）
    TRANSACTION_STATE_COMMITTED,    // 已提交状态
    TRANSACTION_STATE_ROLLED_BACK,  // 已回滚状态
    TRANSACTION_STATE_UNKNOWN       // 未知状态
} oracle_transaction_state_t;

// 事务隔离级别
typedef enum {
    ISOLATION_READ_UNCOMMITTED = 1, // 读未提交
    ISOLATION_READ_COMMITTED = 2,   // 读已提交（Oracle默认）
    ISOLATION_REPEATABLE_READ = 4,  // 可重复读
    ISOLATION_SERIALIZABLE = 8      // 串行化
} oracle_isolation_level_t;

// 保存点信息
typedef struct oracle_savepoint
{
    char     name[64];              // 保存点名称
    uint32_t savepoint_id;          // 保存点ID
    uint64_t create_time;           // 创建时间
    uint32_t sequence_number;       // 序列号
    bool     is_active;             // 是否活跃
} oracle_savepoint_t;

// 分布式事务信息
typedef struct oracle_distributed_transaction
{
    char     global_transaction_id[128];    // 全局事务ID
    char     branch_qualifier[64];          // 分支限定符
    uint32_t format_id;                     // 格式ID
    uint8_t  xa_state;                      // XA状态
    bool     is_prepared;                   // 是否已准备
    uint64_t prepare_time;                  // 准备时间
    uint32_t timeout_seconds;               // 超时时间
    char     resource_manager[64];          // 资源管理器名称
} oracle_distributed_transaction_t;

// 事务上下文
typedef struct oracle_transaction_context
{
    uint32_t session_id;                    // 会话ID
    uint32_t transaction_id;                // 事务ID
    oracle_transaction_state_t state;      // 事务状态
    oracle_isolation_level_t isolation_level; // 隔离级别
    
    // 事务属性
    bool     is_readonly;                   // 是否只读
    bool     is_autocommit;                 // 是否自动提交
    bool     is_distributed;               // 是否分布式事务
    uint32_t timeout_seconds;               // 事务超时时间
    
    // 时间信息
    uint64_t start_time;                    // 开始时间
    uint64_t last_activity_time;            // 最后活动时间
    uint64_t commit_time;                   // 提交时间
    uint64_t rollback_time;                 // 回滚时间
    
    // 统计信息
    uint32_t statements_executed;           // 执行的语句数
    uint32_t rows_affected;                 // 影响的行数
    uint64_t total_execution_time;          // 总执行时间
    uint32_t lock_count;                    // 锁数量
    
    // 保存点管理
    std::vector<oracle_savepoint_t> savepoints; // 保存点列表
    uint32_t next_savepoint_id;             // 下一个保存点ID
    
    // 分布式事务信息
    oracle_distributed_transaction_t *distributed_tx; // 分布式事务信息
    
    // 错误信息
    int      last_error_code;               // 最后错误码
    char     last_error_message[512];       // 最后错误消息
    
    // 状态标志
    bool     has_uncommitted_changes;       // 是否有未提交的更改
    bool     is_rollback_only;              // 是否只能回滚
    bool     is_timed_out;                  // 是否超时
} oracle_transaction_context_t;

// 事务统计信息
typedef struct oracle_transaction_statistics
{
    uint64_t total_transactions;            // 总事务数
    uint64_t committed_transactions;        // 已提交事务数
    uint64_t rolled_back_transactions;      // 已回滚事务数
    uint64_t timed_out_transactions;        // 超时事务数
    uint64_t distributed_transactions;      // 分布式事务数
    
    uint64_t total_savepoints;              // 总保存点数
    uint64_t rollback_to_savepoint_count;   // 回滚到保存点次数
    
    uint64_t total_statements;              // 总语句数
    uint64_t total_rows_affected;           // 总影响行数
    uint64_t total_execution_time;          // 总执行时间
    
    double   avg_transaction_duration;      // 平均事务持续时间
    double   avg_statements_per_transaction; // 平均每事务语句数
    uint32_t max_concurrent_transactions;   // 最大并发事务数
    uint32_t current_active_transactions;   // 当前活跃事务数
} oracle_transaction_statistics_t;

// Oracle事务管理器类
class OracleTransactionManager
{
public:
    OracleTransactionManager();
    ~OracleTransactionManager();

    // 事务生命周期管理
    int begin_transaction(uint32_t session_id, uint32_t transaction_id, 
                         oracle_isolation_level_t isolation_level = ISOLATION_READ_COMMITTED);
    int commit_transaction(uint32_t session_id, uint32_t transaction_id);
    int rollback_transaction(uint32_t session_id, uint32_t transaction_id);
    int set_transaction_readonly(uint32_t session_id, uint32_t transaction_id, bool readonly);
    int set_transaction_timeout(uint32_t session_id, uint32_t transaction_id, uint32_t timeout_seconds);
    
    // 保存点管理
    int create_savepoint(uint32_t session_id, uint32_t transaction_id, const char *savepoint_name);
    int rollback_to_savepoint(uint32_t session_id, uint32_t transaction_id, const char *savepoint_name);
    int release_savepoint(uint32_t session_id, uint32_t transaction_id, const char *savepoint_name);
    int list_savepoints(uint32_t session_id, uint32_t transaction_id, 
                       oracle_savepoint_t **savepoints, uint32_t *savepoint_count);
    
    // 分布式事务管理
    int begin_distributed_transaction(uint32_t session_id, const char *global_tx_id, 
                                     const char *branch_qualifier, uint32_t format_id);
    int prepare_distributed_transaction(uint32_t session_id, uint32_t transaction_id);
    int commit_distributed_transaction(uint32_t session_id, uint32_t transaction_id, bool one_phase);
    int rollback_distributed_transaction(uint32_t session_id, uint32_t transaction_id);
    int recover_distributed_transactions(oracle_distributed_transaction_t **transactions, uint32_t *count);
    
    // 事务状态查询
    oracle_transaction_context_t* find_transaction(uint32_t session_id, uint32_t transaction_id);
    oracle_transaction_state_t get_transaction_state(uint32_t session_id, uint32_t transaction_id);
    bool is_transaction_active(uint32_t session_id, uint32_t transaction_id);
    bool has_uncommitted_changes(uint32_t session_id, uint32_t transaction_id);
    
    // 事务属性管理
    int set_isolation_level(uint32_t session_id, uint32_t transaction_id, oracle_isolation_level_t level);
    oracle_isolation_level_t get_isolation_level(uint32_t session_id, uint32_t transaction_id);
    int set_autocommit(uint32_t session_id, bool autocommit);
    bool get_autocommit(uint32_t session_id);
    
    // 事务监控和统计
    void update_transaction_activity(uint32_t session_id, uint32_t transaction_id, 
                                   uint32_t statements_executed, uint32_t rows_affected, uint64_t execution_time);
    void get_transaction_statistics(oracle_transaction_statistics_t *stats);
    void reset_statistics();
    
    // 超时和清理管理
    int cleanup_timed_out_transactions(uint32_t timeout_seconds);
    int cleanup_inactive_transactions(uint32_t inactive_seconds);
    int force_rollback_transaction(uint32_t session_id, uint32_t transaction_id, const char *reason);
    
    // 锁管理
    int acquire_transaction_lock(uint32_t session_id, uint32_t transaction_id, const char *resource);
    int release_transaction_lock(uint32_t session_id, uint32_t transaction_id, const char *resource);
    int release_all_transaction_locks(uint32_t session_id, uint32_t transaction_id);
    
    // 调试和诊断
    void dump_transaction_context(const oracle_transaction_context_t *context);
    void dump_all_transactions();
    void get_transaction_summary(uint32_t *active_count, uint32_t *total_count, 
                               uint64_t *oldest_start_time, uint64_t *total_duration);
    
    // 配置管理
    void set_default_timeout(uint32_t timeout_seconds) { m_default_timeout = timeout_seconds; }
    void set_max_transactions(uint32_t max_transactions) { m_max_transactions = max_transactions; }
    void set_max_savepoints_per_transaction(uint32_t max_savepoints) { m_max_savepoints_per_tx = max_savepoints; }

private:
    // 内部事务管理方法
    oracle_transaction_context_t* create_transaction_context(uint32_t session_id, uint32_t transaction_id);
    int destroy_transaction_context(oracle_transaction_context_t *context);
    int transition_transaction_state(oracle_transaction_context_t *context, oracle_transaction_state_t new_state);
    bool is_valid_state_transition(oracle_transaction_state_t from_state, oracle_transaction_state_t to_state);
    
    // 保存点内部管理
    oracle_savepoint_t* find_savepoint(oracle_transaction_context_t *context, const char *savepoint_name);
    int validate_savepoint_name(const char *savepoint_name);
    int cleanup_savepoints_after(oracle_transaction_context_t *context, uint32_t savepoint_id);
    
    // 分布式事务内部管理
    int create_distributed_transaction_info(oracle_transaction_context_t *context, 
                                           const char *global_tx_id, const char *branch_qualifier, uint32_t format_id);
    int cleanup_distributed_transaction_info(oracle_transaction_context_t *context);
    bool is_valid_xa_state_transition(uint8_t from_state, uint8_t to_state);
    
    // 工具方法
    uint64_t get_current_timestamp();
    uint32_t generate_transaction_id();
    uint32_t generate_savepoint_id();
    const char* get_transaction_state_name(oracle_transaction_state_t state);
    const char* get_isolation_level_name(oracle_isolation_level_t level);
    
    // 数据成员
    std::map<uint64_t, oracle_transaction_context_t*> m_transactions; // 事务映射 (session_id << 32 | transaction_id)
    std::map<uint32_t, bool> m_session_autocommit;     // 会话自动提交设置
    
    uint32_t m_next_transaction_id;         // 下一个事务ID
    uint32_t m_default_timeout;             // 默认超时时间（秒）
    uint32_t m_max_transactions;            // 最大事务数量
    uint32_t m_max_savepoints_per_tx;       // 每个事务最大保存点数
    
    // 统计信息
    oracle_transaction_statistics_t m_statistics;
    
    // 线程安全（如果需要）
    // std::mutex m_transaction_mutex;
};

// 事务管理器工具函数
namespace OracleTransactionUtils
{
    // 事务状态工具
    const char* get_transaction_state_description(oracle_transaction_state_t state);
    const char* get_isolation_level_description(oracle_isolation_level_t level);
    bool is_transaction_state_final(oracle_transaction_state_t state);
    
    // 分布式事务工具
    bool is_valid_global_transaction_id(const char *global_tx_id);
    bool is_valid_branch_qualifier(const char *branch_qualifier);
    int parse_xa_transaction_id(const char *xa_tx_id, char *global_tx_id, char *branch_qualifier, uint32_t *format_id);
    
    // 保存点工具
    bool is_valid_savepoint_name(const char *savepoint_name);
    int generate_unique_savepoint_name(char *savepoint_name, size_t name_size);
    
    // 性能工具
    double calculate_transaction_efficiency(const oracle_transaction_context_t *context);
    uint32_t estimate_transaction_memory_usage(const oracle_transaction_context_t *context);
    bool should_force_commit(const oracle_transaction_context_t *context, uint32_t max_duration_seconds);
    
    // 安全工具
    bool is_transaction_suspicious(const oracle_transaction_context_t *context);
    bool has_potential_deadlock_risk(const oracle_transaction_context_t *context);
    int recommend_isolation_level(const char *workload_type);
}

#endif /* __ORACLE_TRANSACTION_MANAGER_H__ */
