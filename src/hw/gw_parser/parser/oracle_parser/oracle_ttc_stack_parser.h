/*
 * Oracle TTC消息堆叠解析器头文件
 * 实现完整的TTC消息堆叠机制 - Oracle协议的核心特性
 * 基于ojdbc源码和协议文档的完整实现
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_TTC_STACK_PARSER_H__
#define __ORACLE_TTC_STACK_PARSER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include "oracle_parser_common.h"

// TTC堆叠解析结果状态
#define TTC_STACK_SUCCESS           0
#define TTC_STACK_NEED_MORE_DATA    1
#define TTC_STACK_ERROR            -1
#define TTC_STACK_INVALID_DATA     -2
#define TTC_STACK_MEMORY_ERROR     -3
#define TTC_STACK_OVERFLOW         -4

// TTC消息边界标识
#define TTC_MESSAGE_BOUNDARY_MARKER 0xFF

// TTC消息长度编码类型
#define TTC_LENGTH_ENCODING_FIXED   0  // 固定长度
#define TTC_LENGTH_ENCODING_VAR1    1  // 1字节变长
#define TTC_LENGTH_ENCODING_VAR2    2  // 2字节变长
#define TTC_LENGTH_ENCODING_VAR4    3  // 4字节变长

// TTC消息信息结构 - 增强版
typedef struct ttc_message_info
{
    uint8_t  message_type;          // TTC消息类型（1-31）
    uint32_t message_length;        // 消息总长度
    uint32_t header_length;         // 消息头部长度
    uint32_t payload_length;        // 载荷长度
    const char *message_data;       // 消息数据指针
    const char *payload_data;       // 载荷数据指针
    uint32_t message_offset;        // 消息在TNS包中的偏移
    bool is_complete;               // 消息是否完整
    bool has_continuation;          // 是否有后续部分
    uint8_t length_encoding_type;   // 长度编码类型
    uint32_t sequence_number;       // 消息序列号
    
    // 解析状态
    enum {
        TTC_MSG_STATE_HEADER = 0,
        TTC_MSG_STATE_LENGTH,
        TTC_MSG_STATE_PAYLOAD,
        TTC_MSG_STATE_COMPLETE
    } parse_state;
} ttc_message_info_t;

// TTC消息堆叠信息 - 完整实现
typedef struct ttc_stack_info
{
    uint32_t total_length;          // TNS数据包总长度
    uint32_t payload_length;        // TTC载荷总长度
    uint32_t message_count;         // 消息数量
    uint32_t max_message_count;     // 最大消息数量
    ttc_message_info_t *messages;   // 消息数组
    bool is_complete;               // 堆叠是否完整
    bool has_partial_message;       // 是否有部分消息
    uint32_t bytes_consumed;        // 已消费的字节数
    uint32_t parse_errors;          // 解析错误数
    
    // 堆叠解析状态
    enum {
        TTC_STACK_STATE_INIT = 0,
        TTC_STACK_STATE_PARSING,
        TTC_STACK_STATE_COMPLETE,
        TTC_STACK_STATE_ERROR
    } parse_state;
    
    // 错误信息
    char error_message[256];
    uint32_t error_offset;
    
    // 性能统计
    uint64_t parse_start_time;
    uint64_t parse_end_time;
    uint32_t memory_used;
} ttc_stack_info_t;

// TTC消息长度计算器
typedef struct ttc_length_calculator
{
    uint8_t message_type;           // 消息类型
    bool is_variable_length;        // 是否变长
    uint32_t fixed_length;          // 固定长度（如果适用）
    uint32_t min_length;            // 最小长度
    uint32_t max_length;            // 最大长度
    
    // 长度计算函数指针
    uint32_t (*calculate_length)(const char *data, size_t data_len, size_t offset);
} ttc_length_calculator_t;

// TTC消息堆叠解析器类
class OracleTtcStackParser
{
public:
    OracleTtcStackParser();
    ~OracleTtcStackParser();

    // 主要解析接口
    int parse_ttc_stack(const char *tns_data, size_t tns_data_len, 
                       ttc_stack_info_t *stack_info);
    
    // TTC消息边界识别
    int identify_message_boundaries(const char *ttc_data, size_t ttc_data_len,
                                   std::vector<ttc_message_info_t> &messages);
    
    // 单个TTC消息解析
    int parse_single_ttc_message(const char *data, size_t data_len, size_t offset,
                                ttc_message_info_t *msg_info);
    
    // TTC消息长度计算
    uint32_t calculate_ttc_message_length(uint8_t message_type, const char *data, 
                                         size_t data_len, size_t offset);
    
    // 变长编码解析
    uint32_t decode_variable_length(const char *data, size_t data_len, size_t offset,
                                   uint8_t *encoding_type, size_t *bytes_consumed);
    
    // TTC消息验证
    bool validate_ttc_message_structure(const ttc_message_info_t *msg_info);
    bool validate_ttc_stack_integrity(const ttc_stack_info_t *stack_info);
    
    // 消息类型特定的长度计算
    uint32_t calculate_ttifun_length(const char *data, size_t data_len, size_t offset);
    uint32_t calculate_ttirxh_length(const char *data, size_t data_len, size_t offset);
    uint32_t calculate_ttirxd_length(const char *data, size_t data_len, size_t offset);
    uint32_t calculate_ttioer_length(const char *data, size_t data_len, size_t offset);
    uint32_t calculate_ttirpa_length(const char *data, size_t data_len, size_t offset);
    uint32_t calculate_ttista_length(const char *data, size_t data_len, size_t offset);
    uint32_t calculate_ttilobd_length(const char *data, size_t data_len, size_t offset);
    
    // 堆叠模式识别
    int identify_stack_pattern(const ttc_stack_info_t *stack_info, char *pattern_name, size_t name_size);
    bool is_request_response_pattern(const ttc_stack_info_t *stack_info);
    bool is_batch_operation_pattern(const ttc_stack_info_t *stack_info);
    bool is_complex_transaction_pattern(const ttc_stack_info_t *stack_info);
    
    // 内存管理
    int allocate_stack_info(ttc_stack_info_t *stack_info, uint32_t max_messages);
    void free_stack_info(ttc_stack_info_t *stack_info);
    void free_message_info(ttc_message_info_t *msg_info);
    
    // 错误处理
    int handle_parse_error(ttc_stack_info_t *stack_info, int error_code, 
                          const char *error_msg, uint32_t error_offset);
    const char* get_stack_error_message(int error_code);
    
    // 调试和诊断
    void dump_ttc_stack(const ttc_stack_info_t *stack_info);
    void dump_ttc_message(const ttc_message_info_t *msg_info, int indent_level);
    void dump_stack_statistics(const ttc_stack_info_t *stack_info);
    
    // 性能优化
    int optimize_stack_parsing(ttc_stack_info_t *stack_info);
    void update_parse_statistics(const ttc_stack_info_t *stack_info);
    
    // 配置管理
    void set_max_stack_messages(uint32_t max_messages) { m_max_stack_messages = max_messages; }
    void set_max_message_length(uint32_t max_length) { m_max_message_length = max_length; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }
    void set_strict_validation(bool enabled) { m_strict_validation = enabled; }

private:
    // 内部工具方法
    uint8_t read_uint8(const char *data, size_t offset);
    uint16_t read_uint16_be(const char *data, size_t offset);
    uint32_t read_uint32_be(const char *data, size_t offset);
    uint16_t read_uint16_le(const char *data, size_t offset);
    uint32_t read_uint32_le(const char *data, size_t offset);
    
    // TTC消息类型判断
    bool is_fixed_length_message(uint8_t message_type);
    bool is_variable_length_message(uint8_t message_type);
    bool is_request_message(uint8_t message_type);
    bool is_response_message(uint8_t message_type);
    bool is_control_message(uint8_t message_type);
    
    // 长度编码处理
    size_t get_length_encoding_size(uint8_t encoding_type);
    uint32_t decode_length_by_type(const char *data, size_t offset, uint8_t encoding_type);
    
    // 消息边界检测
    bool is_message_boundary(const char *data, size_t data_len, size_t offset);
    int find_next_message_start(const char *data, size_t data_len, size_t start_offset);
    
    // 堆叠解析状态管理
    int initialize_stack_parsing(ttc_stack_info_t *stack_info);
    int advance_parse_state(ttc_stack_info_t *stack_info);
    int finalize_stack_parsing(ttc_stack_info_t *stack_info);
    
    // 错误恢复
    int recover_from_parse_error(ttc_stack_info_t *stack_info, size_t error_offset);
    int skip_corrupted_message(const char *data, size_t data_len, size_t *offset);
    
    // 长度计算器映射
    std::map<uint8_t, ttc_length_calculator_t> m_length_calculators;
    
    // 配置参数
    uint32_t m_max_stack_messages;
    uint32_t m_max_message_length;
    bool m_debug_enabled;
    bool m_strict_validation;
    
    // 统计信息
    uint64_t m_total_stacks_parsed;
    uint64_t m_total_messages_parsed;
    uint64_t m_total_bytes_processed;
    uint64_t m_parse_errors;
    uint64_t m_memory_allocated;
    
    // 性能指标
    uint64_t m_avg_parse_time;
    uint64_t m_max_parse_time;
    uint32_t m_avg_messages_per_stack;
    uint32_t m_max_messages_per_stack;
};

// TTC堆叠解析工具函数
namespace OracleTtcStackUtils
{
    // 消息类型工具
    bool is_ttc_message_type_valid(uint8_t message_type);
    const char* get_ttc_message_type_name(uint8_t message_type);
    uint8_t get_ttc_message_priority(uint8_t message_type);
    
    // 长度计算工具
    uint32_t estimate_message_length(uint8_t message_type, const char *data, size_t data_len);
    bool is_length_reasonable(uint8_t message_type, uint32_t length);
    uint32_t get_max_message_length(uint8_t message_type);
    
    // 堆叠模式工具
    const char* identify_common_stack_pattern(const std::vector<uint8_t> &message_types);
    bool is_valid_message_sequence(const std::vector<uint8_t> &message_types);
    int calculate_stack_complexity(const ttc_stack_info_t *stack_info);
    
    // 性能工具
    size_t estimate_stack_memory_usage(uint32_t message_count, uint32_t avg_message_size);
    double calculate_parse_efficiency(const ttc_stack_info_t *stack_info);
    bool should_optimize_parsing(const ttc_stack_info_t *stack_info);
}

#endif /* __ORACLE_TTC_STACK_PARSER_H__ */
