/*
 * Oracle DATA_DESCRIPTOR包处理器头文件
 * 实现TNS DATA_DESCRIPTOR包类型的完整支持
 * 支持大数据分片处理和重组算法
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_DATA_DESCRIPTOR_H__
#define __ORACLE_DATA_DESCRIPTOR_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include "oracle_parser_common.h"

// DATA_DESCRIPTOR解析结果状态
#define DATA_DESC_SUCCESS           0
#define DATA_DESC_NEED_MORE_DATA    1
#define DATA_DESC_ERROR            -1
#define DATA_DESC_INVALID_DATA     -2
#define DATA_DESC_MEMORY_ERROR     -3
#define DATA_DESC_FRAGMENT_ERROR   -4

// 数据描述符类型
#define DATA_DESC_TYPE_SIMPLE       1  // 简单数据
#define DATA_DESC_TYPE_FRAGMENTED   2  // 分片数据
#define DATA_DESC_TYPE_COMPRESSED   3  // 压缩数据
#define DATA_DESC_TYPE_ENCRYPTED    4  // 加密数据
#define DATA_DESC_TYPE_LOB          5  // LOB数据

// 压缩类型
#define COMPRESSION_NONE            0  // 无压缩
#define COMPRESSION_ZLIB            1  // ZLIB压缩
#define COMPRESSION_LZ4             2  // LZ4压缩
#define COMPRESSION_ORACLE_NATIVE   3  // Oracle原生压缩

// 加密类型
#define ENCRYPTION_NONE             0  // 无加密
#define ENCRYPTION_DES              1  // DES加密
#define ENCRYPTION_3DES             2  // 3DES加密
#define ENCRYPTION_AES128           3  // AES-128加密
#define ENCRYPTION_AES256           4  // AES-256加密

// TNS数据描述符结构
typedef struct tns_data_descriptor
{
    uint16_t descriptor_type;       // 描述符类型
    uint32_t total_length;          // 总数据长度
    uint16_t fragment_count;        // 分片数量
    uint16_t fragment_size;         // 标准分片大小
    uint32_t sequence_number;       // 序列号
    uint8_t  compression_type;      // 压缩类型
    uint8_t  encryption_type;       // 加密类型
    uint32_t checksum;              // 数据校验和
    uint16_t metadata_length;       // 元数据长度
    char     *metadata;             // 元数据内容
    
    // 分片信息
    uint32_t *fragment_offsets;     // 分片偏移数组
    uint16_t *fragment_lengths;     // 分片长度数组
    uint32_t *fragment_checksums;   // 分片校验和数组
    
    // 解析状态
    bool is_complete;               // 描述符是否完整
    uint64_t parse_timestamp;       // 解析时间戳
} tns_data_descriptor_t;

// 数据分片管理器
typedef struct data_fragment_manager
{
    uint32_t session_id;            // 会话ID
    uint32_t sequence_number;       // 序列号
    uint32_t total_fragments;       // 总分片数
    uint32_t received_fragments;    // 已接收分片数
    uint32_t total_data_length;     // 总数据长度
    
    char     **fragment_data;       // 分片数据数组
    size_t   *fragment_sizes;       // 分片大小数组
    bool     *fragment_received;    // 分片接收状态数组
    uint32_t *fragment_checksums;   // 分片校验和数组
    
    char     *assembled_data;       // 重组后的数据
    size_t   assembled_size;        // 重组数据大小
    bool     is_complete;           // 重组是否完成
    
    uint64_t first_fragment_time;   // 第一个分片时间
    uint64_t last_fragment_time;    // 最后一个分片时间
    uint32_t timeout_seconds;       // 超时时间（秒）
    
    // 压缩和加密信息
    uint8_t  compression_type;      // 压缩类型
    uint8_t  encryption_type;       // 加密类型
    uint32_t original_checksum;     // 原始数据校验和
} data_fragment_manager_t;

// Oracle数据描述符解析器类
class OracleDataDescriptor
{
public:
    OracleDataDescriptor();
    ~OracleDataDescriptor();

    // 主要解析接口
    int parse_data_descriptor_packet(const char *tns_data, size_t tns_data_len,
                                    tns_data_descriptor_t *descriptor);
    
    // 数据分片处理
    int create_fragment_manager(uint32_t session_id, uint32_t sequence_number,
                               const tns_data_descriptor_t *descriptor,
                               data_fragment_manager_t **manager);
    
    int add_data_fragment(data_fragment_manager_t *manager, uint32_t fragment_index,
                         const char *fragment_data, size_t fragment_size);
    
    int reassemble_fragmented_data(data_fragment_manager_t *manager);
    
    bool is_reassembly_complete(const data_fragment_manager_t *manager);
    
    // 数据压缩和解压缩
    int decompress_data(const char *compressed_data, size_t compressed_size,
                       uint8_t compression_type, char **decompressed_data, size_t *decompressed_size);
    
    int compress_data(const char *original_data, size_t original_size,
                     uint8_t compression_type, char **compressed_data, size_t *compressed_size);
    
    // 数据加密和解密
    int decrypt_data(const char *encrypted_data, size_t encrypted_size,
                    uint8_t encryption_type, const char *key, size_t key_size,
                    char **decrypted_data, size_t *decrypted_size);
    
    // 校验和计算和验证
    uint32_t calculate_checksum(const char *data, size_t data_size);
    bool verify_checksum(const char *data, size_t data_size, uint32_t expected_checksum);
    
    // 分片管理
    data_fragment_manager_t* find_fragment_manager(uint32_t session_id, uint32_t sequence_number);
    int cleanup_fragment_manager(data_fragment_manager_t *manager);
    int cleanup_expired_managers(uint32_t timeout_seconds);
    
    // 内存管理
    void free_data_descriptor(tns_data_descriptor_t *descriptor);
    void free_fragment_manager(data_fragment_manager_t *manager);
    
    // 统计和监控
    void get_fragment_statistics(uint32_t *active_managers, uint32_t *total_fragments,
                                uint64_t *total_data_size);
    
    // 调试和诊断
    void dump_data_descriptor(const tns_data_descriptor_t *descriptor);
    void dump_fragment_manager(const data_fragment_manager_t *manager);
    
    // 配置管理
    void set_fragment_timeout(uint32_t timeout_seconds) { m_fragment_timeout = timeout_seconds; }
    void set_max_fragment_size(uint32_t max_size) { m_max_fragment_size = max_size; }
    void set_max_managers(uint32_t max_managers) { m_max_managers = max_managers; }

private:
    // 内部解析方法
    int parse_descriptor_header(const char *data, size_t data_len, size_t *offset,
                               tns_data_descriptor_t *descriptor);
    int parse_fragment_info(const char *data, size_t data_len, size_t *offset,
                           tns_data_descriptor_t *descriptor);
    int parse_metadata(const char *data, size_t data_len, size_t *offset,
                      tns_data_descriptor_t *descriptor);
    
    // 分片验证
    bool validate_fragment_index(const data_fragment_manager_t *manager, uint32_t fragment_index);
    bool validate_fragment_size(const data_fragment_manager_t *manager, uint32_t fragment_index, size_t fragment_size);
    bool validate_fragment_checksum(const char *fragment_data, size_t fragment_size, uint32_t expected_checksum);
    
    // 数据重组算法
    int allocate_assembly_buffer(data_fragment_manager_t *manager);
    int copy_fragment_to_buffer(data_fragment_manager_t *manager, uint32_t fragment_index);
    int finalize_assembly(data_fragment_manager_t *manager);
    
    // 压缩算法实现
    int decompress_zlib(const char *compressed_data, size_t compressed_size,
                       char **decompressed_data, size_t *decompressed_size);
    int decompress_lz4(const char *compressed_data, size_t compressed_size,
                      char **decompressed_data, size_t *decompressed_size);
    int decompress_oracle_native(const char *compressed_data, size_t compressed_size,
                                 char **decompressed_data, size_t *decompressed_size);
    
    // 工具方法
    uint32_t generate_manager_id();
    uint64_t get_current_timestamp();
    uint32_t crc32_checksum(const char *data, size_t data_size);
    
    // 数据成员
    std::map<uint64_t, data_fragment_manager_t*> m_fragment_managers; // 分片管理器映射
    uint32_t m_next_manager_id;         // 下一个管理器ID
    uint32_t m_fragment_timeout;        // 分片超时时间（秒）
    uint32_t m_max_fragment_size;       // 最大分片大小
    uint32_t m_max_managers;            // 最大管理器数量
    
    // 统计信息
    uint64_t m_total_descriptors_parsed;    // 总解析描述符数
    uint64_t m_total_fragments_processed;   // 总处理分片数
    uint64_t m_total_data_reassembled;      // 总重组数据量
    uint64_t m_reassembly_errors;           // 重组错误数
    uint64_t m_checksum_errors;             // 校验和错误数
    uint64_t m_timeout_errors;              // 超时错误数
};

// 数据描述符工具函数
namespace OracleDataDescriptorUtils
{
    // 描述符类型工具
    const char* get_descriptor_type_name(uint16_t descriptor_type);
    const char* get_compression_type_name(uint8_t compression_type);
    const char* get_encryption_type_name(uint8_t encryption_type);
    
    // 分片计算工具
    uint32_t calculate_fragment_count(uint32_t total_size, uint32_t fragment_size);
    uint32_t calculate_last_fragment_size(uint32_t total_size, uint32_t fragment_size);
    bool is_fragment_size_optimal(uint32_t fragment_size, uint32_t total_size);
    
    // 性能工具
    double calculate_compression_ratio(size_t original_size, size_t compressed_size);
    uint64_t estimate_reassembly_time(uint32_t fragment_count, uint32_t total_size);
    bool should_use_compression(size_t data_size, uint8_t data_type);
    
    // 安全工具
    bool is_checksum_algorithm_secure(uint32_t checksum_type);
    bool is_encryption_algorithm_secure(uint8_t encryption_type);
    uint32_t recommend_fragment_size(uint32_t total_size, uint32_t network_mtu);
}

#endif /* __ORACLE_DATA_DESCRIPTOR_H__ */
