/*
 * Oracle协议解析器优先级1修复验证测试
 * 验证TTC消息堆叠、TNS包类型、大包格式、认证流程和内存管理的修复
 * <AUTHOR> @date 2025
 */

#include "oracle_ttc_stack_parser.h"
#include "oracle_tns_parser.h"
#include "oracle_auth_handler.h"
#include "oracle_memory_manager.h"
#include "oracle_parser_common.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

// 测试TTC消息堆叠机制
void test_ttc_message_stacking()
{
    printf("=== Testing TTC Message Stacking Mechanism ===\n");
    
    OracleTtcStackParser stack_parser;
    ttc_stack_info_t stack_info;
    
    // 构造包含多个TTC消息的TNS数据包
    const char tns_data_with_stack[] = {
        // TNS头部 (8字节)
        0x00, 0x50,             // 包长度: 80字节
        0x00, 0x00,             // 包校验和
        TNS_PACKET_TYPE_DATA,   // 包类型: DATA
        0x00,                   // 标志位
        0x00, 0x00,             // 头部校验和
        
        // TNS数据标志 (2字节)
        0x00, 0x00,
        
        // TTC消息堆叠 (70字节)
        // 消息1: TTIFUN (函数调用)
        TTIFUN,                 // 消息类型
        0x20,                   // 消息长度: 32字节
        OALL7,                  // 函数码: ALL7
        0x00, 0x01,             // 序列号
        // 函数调用数据 (28字节)
        0x00, 0x00, 0x00, 0x01, // 游标ID
        0x00, 0x10,             // SQL长度: 16
        'S', 'E', 'L', 'E', 'C', 'T', ' ', '*', ' ', 'F', 'R', 'O', 'M', ' ', 'T', '1',
        0x00, 0x00, 0x00, 0x00, // 绑定变量数
        0x00, 0x00, 0x00, 0x00, // 其他参数
        
        // 消息2: TTIRXH (结果集头部)
        TTIRXH,                 // 消息类型
        0x18,                   // 消息长度: 24字节
        0x00, 0x02,             // 列数: 2
        // 列描述符 (20字节)
        0x01, 0x00, 0x16, 0x00, // 列1: VARCHAR2(22)
        0x02, 0x00, 0x04, 0x00, // 列2: NUMBER(4)
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        
        // 消息3: TTIRXD (结果集数据)
        TTIRXD,                 // 消息类型
        0x10,                   // 消息长度: 16字节
        0x00, 0x01,             // 行数: 1
        0x00, 0x00, 0x00, 0x0A, // 数据长度: 10字节
        // 行数据
        'T', 'e', 's', 't', ' ', 'D', 'a', 't', 'a', '1'
    };
    
    printf("Testing TTC message stack parsing...\n");
    int ret = stack_parser.parse_ttc_stack(tns_data_with_stack, sizeof(tns_data_with_stack), &stack_info);
    printf("TTC stack parse result: %d\n", ret);
    
    if (ret == TTC_STACK_SUCCESS) {
        printf("✓ TTC message stacking parsed successfully\n");
        printf("  Total messages: %u\n", stack_info.message_count);
        printf("  Stack pattern: %s\n", 
               OracleTtcStackUtils::identify_common_stack_pattern(
                   std::vector<uint8_t>{TTIFUN, TTIRXH, TTIRXD}));
        
        // 验证每个消息
        for (uint32_t i = 0; i < stack_info.message_count; i++) {
            const ttc_message_info_t *msg = &stack_info.messages[i];
            printf("  Message %u: type=%s, length=%u, complete=%s\n",
                   i, OracleTtcStackUtils::get_ttc_message_type_name(msg->message_type),
                   msg->message_length, msg->is_complete ? "yes" : "no");
        }
        
        stack_parser.free_stack_info(&stack_info);
    } else {
        printf("✗ TTC message stacking parsing failed\n");
    }
    
    printf("\n");
}

// 测试新增的TNS包类型
void test_new_tns_packet_types()
{
    printf("=== Testing New TNS Packet Types ===\n");
    
    OracleTnsParser tns_parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 测试ACK包
    printf("Testing TNS ACK packet...\n");
    const char ack_packet[] = {
        0x00, 0x0C,             // 包长度: 12字节
        0x00, 0x00,             // 包校验和
        TNS_PACKET_TYPE_ACK,    // 包类型: ACK
        0x00,                   // 标志位
        0x00, 0x00,             // 头部校验和
        0x00, 0x01,             // ACK标志
        0x00, 0x05              // ACK序列号
    };
    
    int ret = tns_parser.parse_tns_packet(ack_packet, sizeof(ack_packet), 0, &status, &result);
    printf("ACK packet result: %d\n", ret);
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ TNS ACK packet parsed successfully\n");
    }
    
    // 测试NULL包
    printf("\nTesting TNS NULL packet...\n");
    const char null_packet[] = {
        0x00, 0x08,             // 包长度: 8字节
        0x00, 0x00,             // 包校验和
        TNS_PACKET_TYPE_NULL,   // 包类型: NULL
        0x00,                   // 标志位
        0x00, 0x00              // 头部校验和
    };
    
    ret = tns_parser.parse_tns_packet(null_packet, sizeof(null_packet), 0, &status, &result);
    printf("NULL packet result: %d\n", ret);
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ TNS NULL packet parsed successfully\n");
        printf("  Last activity time updated: %llu\n", status.last_activity_time);
    }
    
    // 测试ABORT包
    printf("\nTesting TNS ABORT packet...\n");
    const char abort_packet[] = {
        0x00, 0x18,             // 包长度: 24字节
        0x00, 0x00,             // 包校验和
        TNS_PACKET_TYPE_ABORT,  // 包类型: ABORT
        0x00,                   // 标志位
        0x00, 0x00,             // 头部校验和
        0x00, 0x0C,             // 中止原因: 12
        'C', 'o', 'n', 'n', 'e', 'c', 't', 'i', 'o', 'n', ' ', 'l', 'o', 's', 't', '\0'
    };
    
    ret = tns_parser.parse_tns_packet(abort_packet, sizeof(abort_packet), 0, &status, &result);
    printf("ABORT packet result: %d\n", ret);
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ TNS ABORT packet parsed successfully\n");
        printf("  Connection status: %d (should be closed)\n", status.conn_stat);
        printf("  Error code: %d\n", status.last_error_code);
    }
    
    // 测试RESEND包
    printf("\nTesting TNS RESEND packet...\n");
    const char resend_packet[] = {
        0x00, 0x10,             // 包长度: 16字节
        0x00, 0x00,             // 包校验和
        TNS_PACKET_TYPE_RESEND, // 包类型: RESEND
        0x00,                   // 标志位
        0x00, 0x00,             // 头部校验和
        0x00, 0x05,             // 开始序列号: 5
        0x00, 0x08,             // 结束序列号: 8
        0x00, 0x01              // 重发原因: 1
    };
    
    ret = tns_parser.parse_tns_packet(resend_packet, sizeof(resend_packet), 0, &status, &result);
    printf("RESEND packet result: %d\n", ret);
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ TNS RESEND packet parsed successfully\n");
        printf("  Resend requests: %u\n", status.resend_requests);
    }
    
    printf("\n");
}

// 测试O3LOGON和O5LOGON认证
void test_oracle_authentication()
{
    printf("=== Testing Oracle Authentication ===\n");
    
    OracleAuthHandler auth_handler;
    oracle_auth_session_t *auth_session;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    status.session_id = 12345;
    
    // 测试O3LOGON认证
    printf("Testing O3LOGON authentication...\n");
    const char o3logon_data[] = {
        0x73,                   // 认证类型: O3LOGON
        0x01,                   // 认证标志
        0x00, 0x04,             // 用户名长度: 4
        'T', 'E', 'S', 'T',     // 用户名: TEST
        0x00, 0x00, 0x00, 0x10, // 加密密码长度: 16
        // 加密密码数据
        0x1A, 0x2B, 0x3C, 0x4D, 0x5E, 0x6F, 0x70, 0x81,
        0x92, 0xA3, 0xB4, 0xC5, 0xD6, 0xE7, 0xF8, 0x09
    };
    
    int ret = auth_handler.create_auth_session(status.session_id, ORACLE_AUTH_TYPE_O3LOGON, &auth_session);
    if (ret == AUTH_PARSE_SUCCESS) {
        ret = auth_handler.parse_o3logon_auth(o3logon_data, sizeof(o3logon_data), auth_session);
        printf("O3LOGON parse result: %d\n", ret);
        
        if (ret == AUTH_PARSE_SUCCESS) {
            printf("✓ O3LOGON authentication parsed successfully\n");
            printf("  Username: %s\n", auth_session->username ? auth_session->username : "NULL");
            printf("  Encrypted password length: %u\n", auth_session->encrypted_password_length);
        }
        
        auth_handler.cleanup_auth_session(auth_session);
    }
    
    // 测试O5LOGON认证
    printf("\nTesting O5LOGON authentication...\n");
    const char o5logon_data[] = {
        0x05,                   // 版本: 5
        0x01,                   // 认证模式: 1
        0x00, 0x00, 0x00, 0x10, // 盐值长度: 16
        // 盐值
        0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
        0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10,
        0x00, 0x00, 0x00, 0x14, // 验证器长度: 20
        // 验证器 (SHA-1哈希)
        0xA9, 0x99, 0x3E, 0x36, 0x47, 0x06, 0x81, 0x6A,
        0xBA, 0x3E, 0x25, 0x71, 0x78, 0x50, 0xC2, 0x6C,
        0x9C, 0xD0, 0xD8, 0x9D
    };
    
    ret = auth_handler.create_auth_session(status.session_id + 1, ORACLE_AUTH_TYPE_O5LOGON, &auth_session);
    if (ret == AUTH_PARSE_SUCCESS) {
        ret = auth_handler.parse_o5logon_auth(o5logon_data, sizeof(o5logon_data), auth_session);
        printf("O5LOGON parse result: %d\n", ret);
        
        if (ret == AUTH_PARSE_SUCCESS) {
            printf("✓ O5LOGON authentication parsed successfully\n");
            printf("  Version: %u\n", auth_session->auth_data.o5logon.version);
            printf("  Auth mode: %u\n", auth_session->auth_data.o5logon.auth_mode);
            printf("  Salt length: %u\n", auth_session->auth_data.o5logon.salt_length);
            printf("  Verifier length: %u\n", auth_session->auth_data.o5logon.verifier_length);
            
            // 测试验证器验证
            ret = auth_handler.validate_o5logon_verifier(&auth_session->auth_data.o5logon);
            printf("  Verifier validation: %s\n", ret == AUTH_PARSE_SUCCESS ? "PASSED" : "FAILED");
        }
        
        auth_handler.cleanup_auth_session(auth_session);
    }
    
    printf("\n");
}

// 测试内存管理
void test_memory_management()
{
    printf("=== Testing Memory Management ===\n");
    
    OracleMemoryManager& mem_mgr = OracleMemoryManager::getInstance();
    
    // 启用调试模式
    mem_mgr.set_debug_enabled(true);
    mem_mgr.set_leak_detection_enabled(true);
    
    printf("Testing memory allocation and deallocation...\n");
    
    // 分配不同类型的内存
    void *ptr1 = ORACLE_MALLOC_TNS(1024);
    void *ptr2 = ORACLE_MALLOC_TTC(512);
    void *ptr3 = ORACLE_MALLOC_AUTH(256);
    void *ptr4 = ORACLE_MALLOC_LOB(2048);
    
    printf("Allocated 4 memory blocks\n");
    
    // 验证指针有效性
    assert(mem_mgr.is_valid_pointer(ptr1));
    assert(mem_mgr.is_valid_pointer(ptr2));
    assert(mem_mgr.is_valid_pointer(ptr3));
    assert(mem_mgr.is_valid_pointer(ptr4));
    
    printf("✓ All pointers are valid\n");
    
    // 检查分配大小
    assert(mem_mgr.get_allocation_size(ptr1) == 1024);
    assert(mem_mgr.get_allocation_size(ptr2) == 512);
    assert(mem_mgr.get_allocation_size(ptr3) == 256);
    assert(mem_mgr.get_allocation_size(ptr4) == 2048);
    
    printf("✓ All allocation sizes are correct\n");
    
    // 释放部分内存
    ORACLE_FREE(ptr1);
    ORACLE_FREE(ptr3);
    
    printf("Freed 2 memory blocks\n");
    
    // 验证已释放的指针无效
    assert(!mem_mgr.is_valid_pointer(ptr1));
    assert(!mem_mgr.is_valid_pointer(ptr3));
    
    printf("✓ Freed pointers are invalid\n");
    
    // 获取内存统计
    memory_statistics_t stats;
    mem_mgr.get_memory_statistics(&stats);
    
    printf("Memory statistics:\n");
    printf("  Current allocations: %llu\n", stats.current_allocations);
    printf("  Current bytes used: %llu\n", stats.current_bytes_used);
    printf("  Total allocations: %llu\n", stats.total_allocations);
    printf("  Total deallocations: %llu\n", stats.total_deallocations);
    
    // 清理剩余内存
    ORACLE_FREE(ptr2);
    ORACLE_FREE(ptr4);
    
    printf("✓ Memory management test completed\n");
    printf("\n");
}

int main()
{
    printf("Oracle Protocol Parser Priority 1 Fixes Verification\n");
    printf("====================================================\n\n");
    
    // 运行所有优先级1修复的验证测试
    test_ttc_message_stacking();
    test_new_tns_packet_types();
    test_oracle_authentication();
    test_memory_management();
    
    // 打印最终内存报告
    OracleMemoryManager::getInstance().print_memory_report();
    
    printf("All Priority 1 fixes verification completed successfully!\n");
    return 0;
}
