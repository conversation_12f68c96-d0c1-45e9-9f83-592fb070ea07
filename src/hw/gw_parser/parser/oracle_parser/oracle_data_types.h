/*
 * Oracle数据类型支持模块头文件
 * 基于ojdbc源码分析实现的Oracle数据类型编码解码功能
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_DATA_TYPES_H__
#define __ORACLE_DATA_TYPES_H__

#include <inttypes.h>
#include <string>
#include <vector>
#include "oracle_parser_common.h"

// 数据类型转换结果状态
#define ORACLE_TYPE_SUCCESS         0
#define ORACLE_TYPE_ERROR          -1
#define ORACLE_TYPE_INVALID_DATA   -2
#define ORACLE_TYPE_BUFFER_TOO_SMALL -3
#define ORACLE_TYPE_UNSUPPORTED    -4

// Oracle NUMBER内部表示
typedef struct oracle_number
{
    bool is_null;               // 是否为NULL
    bool is_zero;               // 是否为零
    bool is_negative;           // 是否为负数
    int exponent;               // 指数
    uint8_t mantissa[21];       // 尾数（最多20位数字+符号）
    uint8_t mantissa_length;    // 尾数长度
} oracle_number_t;

// Oracle DATE内部表示
typedef struct oracle_date
{
    bool is_null;               // 是否为NULL
    int year;                   // 年份
    int month;                  // 月份 (1-12)
    int day;                    // 日期 (1-31)
    int hour;                   // 小时 (0-23)
    int minute;                 // 分钟 (0-59)
    int second;                 // 秒 (0-59)
} oracle_date_t;

// Oracle TIMESTAMP内部表示
typedef struct oracle_timestamp
{
    oracle_date_t date;         // 日期部分
    uint32_t nanosecond;        // 纳秒 (0-999999999)
    int timezone_hour;          // 时区小时偏移
    int timezone_minute;        // 时区分钟偏移
} oracle_timestamp_t;

// Oracle INTERVAL内部表示
typedef struct oracle_interval
{
    bool is_null;               // 是否为NULL
    bool is_negative;           // 是否为负数
    int years;                  // 年
    int months;                 // 月
    int days;                   // 日
    int hours;                  // 小时
    int minutes;                // 分钟
    int seconds;                // 秒
    uint32_t nanoseconds;       // 纳秒
} oracle_interval_t;

// Oracle LOB描述符
typedef struct oracle_lob_descriptor
{
    bool is_null;               // 是否为NULL
    uint32_t lob_id;            // LOB标识符
    uint64_t length;            // LOB长度
    uint32_t chunk_size;        // 块大小
    bool is_temporary;          // 是否为临时LOB
} oracle_lob_descriptor_t;

// Oracle ROWID内部表示
typedef struct oracle_rowid
{
    bool is_null;               // 是否为NULL
    uint32_t object_id;         // 对象ID
    uint32_t file_id;           // 文件ID
    uint32_t block_id;          // 块ID
    uint16_t row_id;            // 行ID
} oracle_rowid_t;

// Oracle数据类型转换器类
class OracleDataTypes
{
public:
    OracleDataTypes();
    ~OracleDataTypes();

    // NUMBER类型处理
    static int decode_number(const uint8_t *data, size_t data_len, oracle_number_t *number);
    static int encode_number(const oracle_number_t *number, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int number_to_string(const oracle_number_t *number, char *str, size_t str_size);
    static int string_to_number(const char *str, oracle_number_t *number);
    static int number_to_double(const oracle_number_t *number, double *value);
    static int double_to_number(double value, oracle_number_t *number);

    // DATE类型处理
    static int decode_date(const uint8_t *data, size_t data_len, oracle_date_t *date);
    static int encode_date(const oracle_date_t *date, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int date_to_string(const oracle_date_t *date, char *str, size_t str_size);
    static int string_to_date(const char *str, oracle_date_t *date);
    static int date_to_timestamp(const oracle_date_t *date, uint64_t *timestamp);
    static int timestamp_to_date(uint64_t timestamp, oracle_date_t *date);

    // TIMESTAMP类型处理
    static int decode_timestamp(const uint8_t *data, size_t data_len, oracle_timestamp_t *timestamp);
    static int encode_timestamp(const oracle_timestamp_t *timestamp, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int timestamp_to_string(const oracle_timestamp_t *timestamp, char *str, size_t str_size);
    static int string_to_timestamp(const char *str, oracle_timestamp_t *timestamp);

    // BINARY_FLOAT/BINARY_DOUBLE类型处理
    static int decode_binary_float(const uint8_t *data, size_t data_len, float *value);
    static int encode_binary_float(float value, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int decode_binary_double(const uint8_t *data, size_t data_len, double *value);
    static int encode_binary_double(double value, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);

    // BOOLEAN类型处理
    static int decode_boolean(const uint8_t *data, size_t data_len, bool *value);
    static int encode_boolean(bool value, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);

    // JSON类型处理
    static int decode_json(const uint8_t *data, size_t data_len, char *json_str, size_t json_str_size);
    static int encode_json(const char *json_str, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);

    // XMLTYPE类型处理
    static int decode_xmltype(const uint8_t *data, size_t data_len, char *xml_str, size_t xml_str_size);
    static int encode_xmltype(const char *xml_str, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);

    // INTERVAL类型处理
    static int decode_interval_ym(const uint8_t *data, size_t data_len, oracle_interval_t *interval);
    static int decode_interval_ds(const uint8_t *data, size_t data_len, oracle_interval_t *interval);
    static int encode_interval_ym(const oracle_interval_t *interval, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int encode_interval_ds(const oracle_interval_t *interval, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int interval_to_string(const oracle_interval_t *interval, char *str, size_t str_size);

    // VARCHAR2/CHAR类型处理
    static int decode_varchar2(const uint8_t *data, size_t data_len, char *str, size_t str_size, size_t *actual_len);
    static int encode_varchar2(const char *str, size_t str_len, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int decode_char(const uint8_t *data, size_t data_len, char *str, size_t str_size, size_t *actual_len);
    static int encode_char(const char *str, size_t str_len, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);

    // RAW类型处理
    static int decode_raw(const uint8_t *data, size_t data_len, uint8_t *raw, size_t raw_size, size_t *actual_len);
    static int encode_raw(const uint8_t *raw, size_t raw_len, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int raw_to_hex_string(const uint8_t *raw, size_t raw_len, char *hex_str, size_t hex_str_size);
    static int hex_string_to_raw(const char *hex_str, uint8_t *raw, size_t raw_size, size_t *raw_len);

    // ROWID类型处理
    static int decode_rowid(const uint8_t *data, size_t data_len, oracle_rowid_t *rowid);
    static int encode_rowid(const oracle_rowid_t *rowid, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int rowid_to_string(const oracle_rowid_t *rowid, char *str, size_t str_size);
    static int string_to_rowid(const char *str, oracle_rowid_t *rowid);

    // LOB类型处理
    static int decode_lob_descriptor(const uint8_t *data, size_t data_len, oracle_lob_descriptor_t *lob_desc);
    static int encode_lob_descriptor(const oracle_lob_descriptor_t *lob_desc, uint8_t *buffer, size_t buffer_size, size_t *encoded_len);
    static int lob_descriptor_to_string(const oracle_lob_descriptor_t *lob_desc, char *str, size_t str_size);

    // 通用类型判断和转换
    static bool is_null_value(const uint8_t *data, size_t data_len, uint8_t type_code);
    static int get_type_max_length(uint8_t type_code);
    static const char* get_type_name(uint8_t type_code);
    static bool is_fixed_length_type(uint8_t type_code);
    static bool is_variable_length_type(uint8_t type_code);

    // 字符集转换
    static int convert_charset(const char *input, size_t input_len, uint8_t from_charset, 
                              char *output, size_t output_size, uint8_t to_charset, size_t *output_len);
    static const char* get_charset_name(uint8_t charset_id);
    static uint8_t get_charset_id(const char *charset_name);

    // 数值计算辅助函数
    static int compare_numbers(const oracle_number_t *num1, const oracle_number_t *num2);
    static int add_numbers(const oracle_number_t *num1, const oracle_number_t *num2, oracle_number_t *result);
    static int subtract_numbers(const oracle_number_t *num1, const oracle_number_t *num2, oracle_number_t *result);
    static int multiply_numbers(const oracle_number_t *num1, const oracle_number_t *num2, oracle_number_t *result);
    static int divide_numbers(const oracle_number_t *num1, const oracle_number_t *num2, oracle_number_t *result);

    // 日期计算辅助函数
    static int compare_dates(const oracle_date_t *date1, const oracle_date_t *date2);
    static int add_days_to_date(const oracle_date_t *date, int days, oracle_date_t *result);
    static int subtract_dates(const oracle_date_t *date1, const oracle_date_t *date2, int *days_diff);
    static bool is_valid_date(const oracle_date_t *date);
    static bool is_leap_year(int year);

    // 调试和诊断
    static void dump_number(const oracle_number_t *number);
    static void dump_date(const oracle_date_t *date);
    static void dump_raw_data(const uint8_t *data, size_t data_len);

private:
    // NUMBER内部处理函数
    static int normalize_number(oracle_number_t *number);
    static int parse_number_exponent(uint8_t exp_byte, bool *is_negative, int *exponent);
    static int parse_number_mantissa(const uint8_t *mantissa_data, size_t len, oracle_number_t *number);
    static int format_number_mantissa(const oracle_number_t *number, uint8_t *buffer, size_t buffer_size);

    // DATE内部处理函数
    static int validate_date_components(int year, int month, int day, int hour, int minute, int second);
    static int get_days_in_month(int year, int month);
    static int date_to_julian_day(const oracle_date_t *date);
    static int julian_day_to_date(int julian_day, oracle_date_t *date);

    // 字符串处理辅助函数
    static void trim_trailing_spaces(char *str);
    static void pad_with_spaces(char *str, size_t target_len);
    static bool is_all_digits(const char *str, size_t len);
    static bool is_valid_hex_string(const char *str, size_t len);

    // 错误处理
    static const char* get_error_message(int error_code);
};

// Oracle数据类型常量定义
namespace OracleTypeConstants
{
    // NUMBER相关常量
    const uint8_t NUMBER_POSITIVE_INFINITY = 0xFF;
    const uint8_t NUMBER_NEGATIVE_INFINITY = 0x00;
    const uint8_t NUMBER_ZERO = 0x80;
    const int NUMBER_MAX_PRECISION = 38;
    const int NUMBER_MAX_SCALE = 127;
    const int NUMBER_MIN_SCALE = -84;

    // DATE相关常量
    const int DATE_MIN_YEAR = -4712;
    const int DATE_MAX_YEAR = 9999;
    const int DATE_EPOCH_YEAR = 1900;
    const uint8_t DATE_CENTURY_OFFSET = 100;
    const uint8_t DATE_YEAR_OFFSET = 100;
    const uint8_t DATE_TIME_OFFSET = 1;

    // 字符集常量
    const uint8_t CHARSET_US7ASCII = 1;
    const uint16_t CHARSET_UTF8 = 871;
    const uint16_t CHARSET_AL32UTF8 = 873;
    const uint16_t CHARSET_ZHS16GBK = 852;

    // LOB相关常量
    const uint32_t LOB_MAX_CHUNK_SIZE = 32767;
    const uint64_t CLOB_MAX_LENGTH = 4294967295ULL;
    const uint64_t BLOB_MAX_LENGTH = 4294967295ULL;
}

#endif /* __ORACLE_DATA_TYPES_H__ */
