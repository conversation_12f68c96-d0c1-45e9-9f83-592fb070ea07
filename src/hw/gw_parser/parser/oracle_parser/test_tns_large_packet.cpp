/*
 * TNS大包格式解析测试程序
 * 验证TNS大包格式解析的正确性
 * <AUTHOR> @date 2025
 */

#include "oracle_tns_parser.h"
#include "oracle_parser_common.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

// 测试TNS标准包格式解析
void test_standard_packet_parsing()
{
    printf("=== Testing Standard TNS Packet Parsing ===\n");
    
    OracleTnsParser parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 构造标准TNS CONNECT包
    const char standard_packet[] = {
        0x00, 0x3A,             // 包长度: 58字节
        0x00, 0x00,             // 包校验和: 0
        0x01,                   // 包类型: CONNECT
        0x00,                   // 标志位: 0
        0x00, 0x00,             // 头部校验和: 0
        // 连接数据 (50字节)
        0x01, 0x38,             // 版本: 312
        0x01, 0x2C,             // 兼容版本: 300
        0x00, 0x00,             // 服务选项
        0x08, 0x00,             // SDU大小: 2048
        0x7F, 0xFF,             // 最大传输单元: 32767
        0x7F, 0xFF,             // NT协议特征
        0x00, 0x00,             // 线路转换
        0x00, 0x00,             // 值转换
        0x00, 0x00,             // 连接标志
        0x00, 0x00, 0x00, 0x00, // 跟踪交叉1
        0x00, 0x00, 0x00, 0x00, // 跟踪交叉2
        0x00, 0x00, 0x00, 0x00, // 跟踪唯一连接ID
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        // 连接描述符
        '(', 'D', 'E', 'S', 'C', 'R', 'I', 'P', 'T', 'I', 'O', 'N', '=', ')',
    };
    
    printf("Testing standard packet (58 bytes)...\n");
    int ret = parser.parse_tns_packet(standard_packet, sizeof(standard_packet), 
                                     0, &status, &result);
    printf("Standard packet parse result: %d\n", ret);
    
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ Standard packet parsed successfully\n");
        printf("  Connection status: %d\n", status.conn_stat);
        printf("  TNS version: %u\n", status.tns_version);
    } else {
        printf("✗ Standard packet parsing failed\n");
    }
    
    printf("\n");
}

// 测试TNS大包格式解析
void test_large_packet_parsing()
{
    printf("=== Testing Large TNS Packet Parsing ===\n");
    
    OracleTnsParser parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 构造大包TNS DATA包 (100KB)
    const size_t large_data_size = 100 * 1024; // 100KB
    const size_t total_packet_size = TNS_LARGE_HEADER_SIZE + large_data_size;
    
    char *large_packet = (char*)malloc(total_packet_size);
    if (!large_packet) {
        printf("✗ Failed to allocate memory for large packet test\n");
        return;
    }
    
    // 构造大包头部
    large_packet[0] = 0x00;     // 大包标识 (高字节)
    large_packet[1] = 0x00;     // 大包标识 (低字节)
    
    // 实际长度 (4字节，网络字节序)
    uint32_t actual_length = htonl(total_packet_size);
    memcpy(large_packet + 2, &actual_length, 4);
    
    large_packet[6] = TNS_PACKET_TYPE_DATA; // 包类型: DATA
    large_packet[7] = 0x00;                 // 标志位: 0
    
    // 填充大量数据 (模拟大结果集)
    for (size_t i = 0; i < large_data_size; i++) {
        large_packet[TNS_LARGE_HEADER_SIZE + i] = (char)(i % 256);
    }
    
    printf("Testing large packet (%zu bytes)...\n", total_packet_size);
    int ret = parser.parse_tns_packet(large_packet, total_packet_size, 
                                     0, &status, &result);
    printf("Large packet parse result: %d\n", ret);
    
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ Large packet parsed successfully\n");
        printf("  Packet size: %zu bytes\n", total_packet_size);
        printf("  Data size: %zu bytes\n", large_data_size);
    } else {
        printf("✗ Large packet parsing failed\n");
    }
    
    free(large_packet);
    printf("\n");
}

// 测试大包格式边界条件
void test_large_packet_edge_cases()
{
    printf("=== Testing Large Packet Edge Cases ===\n");
    
    OracleTnsParser parser;
    tns_header_t header;
    bool is_large_packet;
    
    // 测试1: 不完整的大包头部
    printf("Test 1: Incomplete large packet header...\n");
    const char incomplete_header[] = {0x00, 0x00, 0x00, 0x01}; // 只有4字节
    int ret = parser.parse_tns_header(incomplete_header, sizeof(incomplete_header), 
                                     &header, &is_large_packet);
    printf("Incomplete header result: %d (expected: %d)\n", ret, TNS_PARSE_NEED_MORE_DATA);
    assert(ret == TNS_PARSE_NEED_MORE_DATA);
    printf("✓ Correctly detected incomplete header\n");
    
    // 测试2: 最小大包
    printf("\nTest 2: Minimum large packet...\n");
    const char min_large_packet[] = {
        0x00, 0x00,             // 大包标识
        0x00, 0x00, 0x00, 0x08, // 长度: 8字节 (最小)
        TNS_PACKET_TYPE_DATA,   // 包类型
        0x00                    // 标志位
    };
    ret = parser.parse_tns_header(min_large_packet, sizeof(min_large_packet), 
                                 &header, &is_large_packet);
    printf("Minimum large packet result: %d\n", ret);
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ Minimum large packet parsed successfully\n");
        printf("  Length: %u, Type: %u, Large: %s\n", 
               header.length, header.type, is_large_packet ? "yes" : "no");
        assert(is_large_packet == true);
        assert(header.length == 8);
    }
    
    // 测试3: 超大包 (接近最大限制)
    printf("\nTest 3: Maximum size large packet...\n");
    const char max_large_packet[] = {
        0x00, 0x00,             // 大包标识
        0x7F, 0xFF, 0xFF, 0xFF, // 长度: 2GB-1 (最大)
        TNS_PACKET_TYPE_DATA,   // 包类型
        0x00                    // 标志位
    };
    ret = parser.parse_tns_header(max_large_packet, sizeof(max_large_packet), 
                                 &header, &is_large_packet);
    printf("Maximum large packet result: %d\n", ret);
    if (ret == TNS_PARSE_SUCCESS) {
        printf("✓ Maximum large packet parsed successfully\n");
        printf("  Length: %u, Type: %u, Large: %s\n", 
               header.length, header.type, is_large_packet ? "yes" : "no");
        assert(is_large_packet == true);
        assert(header.length == 0x7FFFFFFF);
    }
    
    // 测试4: 无效的大包长度
    printf("\nTest 4: Invalid large packet length...\n");
    const char invalid_large_packet[] = {
        0x00, 0x00,             // 大包标识
        0x00, 0x00, 0x00, 0x04, // 长度: 4字节 (小于头部大小)
        TNS_PACKET_TYPE_DATA,   // 包类型
        0x00                    // 标志位
    };
    ret = parser.parse_tns_header(invalid_large_packet, sizeof(invalid_large_packet), 
                                 &header, &is_large_packet);
    printf("Invalid large packet result: %d (expected: %d)\n", ret, TNS_PARSE_INVALID_PACKET);
    assert(ret == TNS_PARSE_INVALID_PACKET);
    printf("✓ Correctly rejected invalid large packet length\n");
    
    printf("\n");
}

// 测试标准包和大包的混合场景
void test_mixed_packet_scenarios()
{
    printf("=== Testing Mixed Packet Scenarios ===\n");
    
    OracleTnsParser parser;
    oracle_status_t status;
    oracle_parsed_data_t result;
    
    memset(&status, 0, sizeof(status));
    memset(&result, 0, sizeof(result));
    
    // 场景1: 标准CONNECT包后跟大包DATA包
    printf("Scenario 1: Standard CONNECT + Large DATA...\n");
    
    // 先处理标准CONNECT包
    const char connect_packet[] = {
        0x00, 0x20,             // 包长度: 32字节
        0x00, 0x00,             // 包校验和
        TNS_PACKET_TYPE_CONNECT, // 包类型
        0x00,                   // 标志位
        0x00, 0x00,             // 头部校验和
        // 连接数据 (24字节)
        0x01, 0x38, 0x01, 0x2C, 0x00, 0x00, 0x08, 0x00,
        0x7F, 0xFF, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    };
    
    int ret = parser.parse_tns_packet(connect_packet, sizeof(connect_packet), 
                                     0, &status, &result);
    printf("CONNECT packet result: %d\n", ret);
    
    if (ret == TNS_PARSE_SUCCESS) {
        // 然后处理大包DATA包
        const size_t large_data_size = 1024;
        const size_t total_size = TNS_LARGE_HEADER_SIZE + large_data_size;
        char *large_data_packet = (char*)malloc(total_size);
        
        if (large_data_packet) {
            // 构造大包
            large_data_packet[0] = 0x00;
            large_data_packet[1] = 0x00;
            uint32_t length = htonl(total_size);
            memcpy(large_data_packet + 2, &length, 4);
            large_data_packet[6] = TNS_PACKET_TYPE_DATA;
            large_data_packet[7] = 0x00;
            
            // 填充数据
            memset(large_data_packet + TNS_LARGE_HEADER_SIZE, 0xAA, large_data_size);
            
            ret = parser.parse_tns_packet(large_data_packet, total_size, 
                                         0, &status, &result);
            printf("Large DATA packet result: %d\n", ret);
            
            if (ret == TNS_PARSE_SUCCESS) {
                printf("✓ Mixed packet scenario completed successfully\n");
            }
            
            free(large_data_packet);
        }
    }
    
    printf("\n");
}

int main()
{
    printf("TNS Large Packet Format Parsing Test Suite\n");
    printf("==========================================\n\n");
    
    // 运行所有测试
    test_standard_packet_parsing();
    test_large_packet_parsing();
    test_large_packet_edge_cases();
    test_mixed_packet_scenarios();
    
    printf("All TNS large packet tests completed!\n");
    return 0;
}
