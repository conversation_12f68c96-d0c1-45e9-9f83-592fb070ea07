/*
 * Oracle连接池和会话管理器头文件
 * 实现连接池状态跟踪和会话生命周期管理
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_SESSION_MANAGER_H__
#define __ORACLE_SESSION_MANAGER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <set>
#include <string>
#include <mutex>
#include <thread>
#include "oracle_parser_common.h"

// 会话管理结果状态
#define SESSION_MGR_SUCCESS           0
#define SESSION_MGR_ERROR            -1
#define SESSION_MGR_NOT_FOUND        -2
#define SESSION_MGR_INVALID_STATE    -3
#define SESSION_MGR_RESOURCE_LIMIT   -4
#define SESSION_MGR_TIMEOUT          -5

// 连接状态
#define ORACLE_CONN_STATE_IDLE       0  // 空闲
#define ORACLE_CONN_STATE_ACTIVE     1  // 活跃
#define ORACLE_CONN_STATE_BUSY       2  // 忙碌
#define ORACLE_CONN_STATE_BLOCKED    3  // 阻塞
#define ORACLE_CONN_STATE_CLOSING    4  // 关闭中
#define ORACLE_CONN_STATE_CLOSED     5  // 已关闭
#define ORACLE_CONN_STATE_ERROR      6  // 错误

// 会话类型
#define ORACLE_SESSION_TYPE_USER     1  // 用户会话
#define ORACLE_SESSION_TYPE_SYSTEM   2  // 系统会话
#define ORACLE_SESSION_TYPE_BACKGROUND 3 // 后台会话
#define ORACLE_SESSION_TYPE_SHARED   4  // 共享会话
#define ORACLE_SESSION_TYPE_DEDICATED 5 // 专用会话

// 连接池类型
#define ORACLE_POOL_TYPE_DEDICATED   1  // 专用连接池
#define ORACLE_POOL_TYPE_SHARED      2  // 共享连接池
#define ORACLE_POOL_TYPE_DRCP        3  // 数据库驻留连接池
#define ORACLE_POOL_TYPE_UCP         4  // 通用连接池

// 会话事件类型
#define SESSION_EVENT_CONNECT        1  // 连接
#define SESSION_EVENT_DISCONNECT     2  // 断开
#define SESSION_EVENT_LOGIN          3  // 登录
#define SESSION_EVENT_LOGOUT         4  // 登出
#define SESSION_EVENT_TIMEOUT        5  // 超时
#define SESSION_EVENT_ERROR          6  // 错误
#define SESSION_EVENT_KILL           7  // 终止

// Oracle连接信息
typedef struct oracle_connection_info
{
    uint32_t connection_id;         // 连接ID
    uint8_t  connection_state;      // 连接状态
    uint8_t  session_type;          // 会话类型
    
    // 网络信息
    char     client_ip[64];         // 客户端IP
    uint16_t client_port;           // 客户端端口
    char     server_ip[64];         // 服务器IP
    uint16_t server_port;           // 服务器端口
    char     client_hostname[256];  // 客户端主机名
    
    // 认证信息
    char     username[128];         // 用户名
    char     schema[128];           // 模式
    char     service_name[128];     // 服务名
    char     instance_name[128];    // 实例名
    uint8_t  auth_type;             // 认证类型
    
    // 会话属性
    uint32_t session_id;            // 会话ID
    uint32_t serial_number;         // 序列号
    uint32_t process_id;            // 进程ID
    char     program[256];          // 程序名
    char     module[256];           // 模块名
    char     action[256];           // 动作名
    
    // 时间信息
    uint64_t connect_time;          // 连接时间
    uint64_t login_time;            // 登录时间
    uint64_t last_activity_time;    // 最后活动时间
    uint64_t idle_time;             // 空闲时间
    uint64_t session_timeout;       // 会话超时
    
    // 资源使用情况
    uint64_t logical_reads;         // 逻辑读
    uint64_t physical_reads;        // 物理读
    uint64_t block_gets;            // 块获取
    uint64_t consistent_gets;       // 一致性获取
    uint64_t cpu_time;              // CPU时间
    uint64_t elapsed_time;          // 经过时间
    uint32_t open_cursors;          // 打开游标数
    uint32_t cached_cursors;        // 缓存游标数
    
    // 事务信息
    bool     in_transaction;        // 是否在事务中
    uint64_t transaction_start_time; // 事务开始时间
    uint32_t transaction_id;        // 事务ID
    uint32_t undo_segments;         // 回滚段数
    
    // 锁信息
    uint32_t locks_held;            // 持有锁数
    uint32_t locks_waiting;         // 等待锁数
    char     blocking_session[64];  // 阻塞会话
    char     wait_event[128];       // 等待事件
} oracle_connection_info_t;

// Oracle连接池信息
typedef struct oracle_connection_pool
{
    uint32_t pool_id;               // 连接池ID
    uint8_t  pool_type;             // 连接池类型
    char     pool_name[128];        // 连接池名称
    char     service_name[128];     // 服务名
    
    // 连接池配置
    uint32_t min_connections;       // 最小连接数
    uint32_t max_connections;       // 最大连接数
    uint32_t initial_connections;   // 初始连接数
    uint32_t increment_connections; // 增量连接数
    uint32_t timeout_seconds;       // 超时秒数
    uint32_t max_idle_time;         // 最大空闲时间
    uint32_t validate_connection_timeout; // 连接验证超时
    
    // 连接池状态
    uint32_t active_connections;    // 活跃连接数
    uint32_t idle_connections;      // 空闲连接数
    uint32_t total_connections;     // 总连接数
    uint32_t peak_connections;      // 峰值连接数
    uint32_t failed_connections;    // 失败连接数
    
    // 连接列表
    std::vector<oracle_connection_info_t*> connections;
    std::set<uint32_t> idle_connection_ids;
    std::set<uint32_t> active_connection_ids;
    
    // 统计信息
    uint64_t total_requests;        // 总请求数
    uint64_t successful_requests;   // 成功请求数
    uint64_t failed_requests;       // 失败请求数
    uint64_t connection_reuses;     // 连接重用数
    uint64_t connection_creates;    // 连接创建数
    uint64_t connection_destroys;   // 连接销毁数
    
    // 性能指标
    double   average_wait_time;     // 平均等待时间
    double   average_use_time;      // 平均使用时间
    double   connection_utilization; // 连接利用率
    
    // 时间信息
    uint64_t pool_create_time;      // 连接池创建时间
    uint64_t last_resize_time;      // 最后调整大小时间
} oracle_connection_pool_t;

// 会话事件信息
typedef struct oracle_session_event
{
    uint64_t event_id;              // 事件ID
    uint8_t  event_type;            // 事件类型
    uint32_t session_id;            // 会话ID
    uint32_t connection_id;         // 连接ID
    uint64_t event_time;            // 事件时间
    char     event_description[256]; // 事件描述
    char     event_details[512];    // 事件详情
} oracle_session_event_t;

// Oracle会话管理器类
class OracleSessionManager
{
public:
    OracleSessionManager();
    ~OracleSessionManager();

    // 连接池管理
    int create_connection_pool(const char *pool_name, uint8_t pool_type, 
                              uint32_t min_conn, uint32_t max_conn, oracle_connection_pool_t **pool);
    int destroy_connection_pool(uint32_t pool_id);
    int resize_connection_pool(uint32_t pool_id, uint32_t new_size);
    oracle_connection_pool_t* get_connection_pool(uint32_t pool_id);
    oracle_connection_pool_t* find_connection_pool_by_name(const char *pool_name);

    // 连接管理
    int create_connection(uint32_t pool_id, oracle_connection_info_t **connection);
    int destroy_connection(uint32_t connection_id);
    int get_connection_from_pool(uint32_t pool_id, oracle_connection_info_t **connection);
    int return_connection_to_pool(uint32_t connection_id);
    oracle_connection_info_t* find_connection(uint32_t connection_id);

    // 会话生命周期管理
    int register_session(const oracle_connection_info_t *connection);
    int update_session_activity(uint32_t session_id, uint64_t activity_time);
    int update_session_state(uint32_t session_id, uint8_t new_state);
    int terminate_session(uint32_t session_id, bool force);
    int cleanup_expired_sessions();

    // 会话监控
    int monitor_session_activity(uint32_t session_id, oracle_session_activity_t *activity);
    int detect_idle_sessions(uint32_t idle_threshold, std::vector<uint32_t> &idle_sessions);
    int detect_blocked_sessions(std::vector<oracle_session_block_info_t> &blocked_sessions);
    int analyze_session_performance(uint32_t session_id, oracle_session_performance_t *performance);

    // 连接池优化
    int optimize_pool_size(uint32_t pool_id);
    int balance_pool_connections(uint32_t pool_id);
    int validate_pool_connections(uint32_t pool_id);
    int cleanup_pool_connections(uint32_t pool_id);

    // 事件管理
    int log_session_event(uint32_t session_id, uint8_t event_type, const char *description);
    int get_session_events(uint32_t session_id, std::vector<oracle_session_event_t> &events);
    int cleanup_old_events(uint64_t retention_time);

    // 统计信息
    int get_pool_statistics(uint32_t pool_id, oracle_pool_stats_t *stats);
    int get_session_statistics(oracle_session_stats_t *stats);
    int get_global_statistics(oracle_global_stats_t *stats);
    void reset_statistics();

    // 配置管理
    int set_pool_configuration(uint32_t pool_id, const oracle_pool_config_t *config);
    int get_pool_configuration(uint32_t pool_id, oracle_pool_config_t *config);
    int set_session_timeout(uint32_t timeout_seconds);
    int set_max_idle_time(uint32_t idle_seconds);

    // 健康检查
    int health_check_pool(uint32_t pool_id, oracle_pool_health_t *health);
    int health_check_connection(uint32_t connection_id, oracle_connection_health_t *health);
    int perform_global_health_check(oracle_global_health_t *health);

    // 调试和诊断
    void dump_connection_pool(uint32_t pool_id);
    void dump_connection_info(uint32_t connection_id);
    void dump_session_info(uint32_t session_id);
    void dump_all_pools();

    // 线程安全
    void lock_manager() { m_manager_mutex.lock(); }
    void unlock_manager() { m_manager_mutex.unlock(); }

private:
    // 内部管理方法
    uint32_t generate_pool_id();
    uint32_t generate_connection_id();
    uint64_t generate_event_id();
    
    // 连接池内部管理
    int add_connection_to_pool(oracle_connection_pool_t *pool, oracle_connection_info_t *connection);
    int remove_connection_from_pool(oracle_connection_pool_t *pool, uint32_t connection_id);
    int validate_pool_limits(const oracle_connection_pool_t *pool);
    
    // 会话内部管理
    bool is_session_expired(const oracle_connection_info_t *connection);
    bool is_session_idle_too_long(const oracle_connection_info_t *connection);
    int cleanup_session_resources(oracle_connection_info_t *connection);
    
    // 性能监控内部方法
    void update_pool_statistics(oracle_connection_pool_t *pool);
    void calculate_pool_metrics(oracle_connection_pool_t *pool);
    void analyze_pool_performance(oracle_connection_pool_t *pool);
    
    // 后台任务
    void start_background_tasks();
    void stop_background_tasks();
    void background_cleanup_task();
    void background_monitoring_task();
    void background_optimization_task();

    // 数据存储
    std::map<uint32_t, oracle_connection_pool_t*> m_connection_pools;
    std::map<uint32_t, oracle_connection_info_t*> m_connections;
    std::map<uint32_t, std::vector<oracle_session_event_t>> m_session_events;
    
    // ID生成器
    uint32_t m_next_pool_id;
    uint32_t m_next_connection_id;
    uint64_t m_next_event_id;
    
    // 配置参数
    uint32_t m_default_session_timeout;
    uint32_t m_default_max_idle_time;
    uint32_t m_max_pools;
    uint32_t m_max_connections_per_pool;
    uint64_t m_event_retention_time;
    
    // 线程安全
    std::mutex m_manager_mutex;
    std::mutex m_pools_mutex;
    std::mutex m_connections_mutex;
    std::mutex m_events_mutex;
    
    // 后台任务
    std::thread m_cleanup_thread;
    std::thread m_monitoring_thread;
    std::thread m_optimization_thread;
    bool m_background_tasks_running;
    
    // 统计信息
    uint64_t m_total_pools_created;
    uint64_t m_total_connections_created;
    uint64_t m_total_sessions_created;
    uint64_t m_total_events_logged;
};

// 统计信息结构
typedef struct oracle_pool_stats
{
    uint32_t total_connections;     // 总连接数
    uint32_t active_connections;    // 活跃连接数
    uint32_t idle_connections;      // 空闲连接数
    uint32_t peak_connections;      // 峰值连接数
    uint64_t total_requests;        // 总请求数
    uint64_t successful_requests;   // 成功请求数
    uint64_t failed_requests;       // 失败请求数
    double   success_rate;          // 成功率
    double   average_wait_time;     // 平均等待时间
    double   connection_utilization; // 连接利用率
} oracle_pool_stats_t;

typedef struct oracle_session_stats
{
    uint64_t total_sessions;        // 总会话数
    uint64_t active_sessions;       // 活跃会话数
    uint64_t idle_sessions;         // 空闲会话数
    uint64_t expired_sessions;      // 过期会话数
    uint64_t terminated_sessions;   // 终止会话数
    double   average_session_time;  // 平均会话时间
    double   session_turnover_rate; // 会话周转率
} oracle_session_stats_t;

// 健康检查结构
typedef struct oracle_pool_health
{
    bool     is_healthy;            // 是否健康
    uint8_t  health_score;          // 健康评分(0-100)
    char     health_issues[512];    // 健康问题
    char     recommendations[512];  // 建议
} oracle_pool_health_t;

// 会话管理工具函数命名空间
namespace OracleSessionUtils
{
    // 连接状态判断
    bool is_connection_active(uint8_t connection_state);
    bool is_connection_available(uint8_t connection_state);
    bool can_reuse_connection(const oracle_connection_info_t *connection);
    
    // 会话类型判断
    bool is_user_session(uint8_t session_type);
    bool is_system_session(uint8_t session_type);
    bool requires_dedicated_connection(uint8_t session_type);
    
    // 性能计算
    double calculate_connection_utilization(const oracle_connection_pool_t *pool);
    double calculate_session_efficiency(const oracle_connection_info_t *connection);
    uint32_t estimate_optimal_pool_size(const oracle_connection_pool_t *pool);
    
    // 资源管理
    bool should_create_new_connection(const oracle_connection_pool_t *pool);
    bool should_destroy_idle_connection(const oracle_connection_info_t *connection);
    uint32_t calculate_connection_priority(const oracle_connection_info_t *connection);
}

#endif /* __ORACLE_SESSION_MANAGER_H__ */
