/*
 * Oracle错误处理和恢复机制头文件
 * 实现错误分类处理、连接恢复机制和异常情况处理
 * 提供完整的错误恢复和容错能力
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_ERROR_HANDLER_H__
#define __ORACLE_ERROR_HANDLER_H__

#include <inttypes.h>
#include <map>
#include <vector>
#include <string>
#include <functional>
#include "oracle_parser_common.h"

// 错误分类
typedef enum {
    ORACLE_ERROR_NETWORK,           // 网络错误
    ORACLE_ERROR_PROTOCOL,          // 协议错误
    ORACLE_ERROR_AUTHENTICATION,    // 认证错误
    ORACLE_ERROR_SQL,               // SQL错误
    ORACLE_ERROR_TRANSACTION,       // 事务错误
    ORACLE_ERROR_RESOURCE,          // 资源错误
    ORACLE_ERROR_TIMEOUT,           // 超时错误
    ORACLE_ERROR_MEMORY,            // 内存错误
    ORACLE_ERROR_PARSING,           // 解析错误
    ORACLE_ERROR_CONFIGURATION,     // 配置错误
    ORACLE_ERROR_UNKNOWN            // 未知错误
} oracle_error_category_t;

// 错误严重程度
typedef enum {
    ERROR_SEVERITY_INFO,            // 信息级别
    ERROR_SEVERITY_WARNING,         // 警告级别
    ERROR_SEVERITY_ERROR,           // 错误级别
    ERROR_SEVERITY_CRITICAL,        // 严重级别
    ERROR_SEVERITY_FATAL            // 致命级别
} oracle_error_severity_t;

// 恢复策略
typedef enum {
    RECOVERY_STRATEGY_NONE,         // 无恢复策略
    RECOVERY_STRATEGY_RETRY,        // 重试策略
    RECOVERY_STRATEGY_RECONNECT,    // 重连策略
    RECOVERY_STRATEGY_RESET,        // 重置策略
    RECOVERY_STRATEGY_FAILOVER,     // 故障转移策略
    RECOVERY_STRATEGY_ABORT         // 中止策略
} oracle_recovery_strategy_t;

// 错误信息结构
typedef struct oracle_error_info
{
    int error_code;                         // 错误码
    oracle_error_category_t category;       // 错误分类
    oracle_error_severity_t severity;       // 严重程度
    char error_message[512];                // 错误消息
    char detailed_message[1024];            // 详细错误信息
    char context_info[256];                 // 上下文信息
    
    // 错误位置信息
    const char *source_file;                // 源文件
    int source_line;                        // 源行号
    const char *function_name;              // 函数名
    
    // 时间信息
    uint64_t error_time;                    // 错误发生时间
    uint64_t first_occurrence;              // 首次发生时间
    uint32_t occurrence_count;              // 发生次数
    
    // 恢复信息
    oracle_recovery_strategy_t recovery_strategy; // 恢复策略
    bool is_recoverable;                    // 是否可恢复
    uint32_t max_retry_count;               // 最大重试次数
    uint32_t current_retry_count;           // 当前重试次数
    uint32_t retry_delay_ms;                // 重试延迟（毫秒）
    
    // 关联信息
    uint32_t session_id;                    // 会话ID
    uint32_t transaction_id;                // 事务ID
    uint32_t connection_id;                 // 连接ID
} oracle_error_info_t;

// 错误处理器函数类型
typedef int (*oracle_error_handler_func_t)(const oracle_error_info_t *error_info, oracle_status_t *status);

// 错误处理器描述符
typedef struct oracle_error_handler
{
    int error_code;                         // 错误码（0表示通用处理器）
    oracle_error_category_t category;       // 错误分类
    oracle_error_severity_t min_severity;   // 最小处理严重程度
    oracle_error_handler_func_t handler_func; // 处理函数
    oracle_recovery_strategy_t recovery_strategy; // 恢复策略
    uint32_t max_retry_count;               // 最大重试次数
    uint32_t retry_delay_ms;                // 重试延迟
    const char *description;                // 描述信息
    bool is_enabled;                        // 是否启用
} oracle_error_handler_t;

// 连接恢复上下文
typedef struct oracle_recovery_context
{
    uint32_t connection_id;                 // 连接ID
    uint32_t session_id;                    // 会话ID
    oracle_recovery_strategy_t strategy;    // 恢复策略
    
    // 重试信息
    uint32_t max_retry_attempts;           // 最大重试次数
    uint32_t current_retry_attempt;        // 当前重试次数
    uint32_t retry_delay_ms;               // 重试延迟
    uint64_t last_retry_time;              // 最后重试时间
    
    // 连接信息
    char server_host[256];                 // 服务器主机
    uint16_t server_port;                  // 服务器端口
    char service_name[128];                // 服务名
    char username[64];                     // 用户名
    
    // 状态信息
    bool is_recovering;                    // 是否正在恢复
    bool recovery_successful;              // 恢复是否成功
    uint64_t recovery_start_time;          // 恢复开始时间
    uint64_t recovery_end_time;            // 恢复结束时间
    
    // 错误历史
    std::vector<oracle_error_info_t> error_history; // 错误历史
    uint32_t consecutive_failures;         // 连续失败次数
} oracle_recovery_context_t;

// 错误统计信息
typedef struct oracle_error_statistics
{
    uint64_t total_errors;                 // 总错误数
    uint64_t errors_by_category[ORACLE_ERROR_UNKNOWN + 1]; // 按分类统计
    uint64_t errors_by_severity[ERROR_SEVERITY_FATAL + 1]; // 按严重程度统计
    
    uint64_t total_recoveries_attempted;   // 总恢复尝试次数
    uint64_t successful_recoveries;        // 成功恢复次数
    uint64_t failed_recoveries;            // 失败恢复次数
    
    uint64_t network_timeouts;             // 网络超时次数
    uint64_t connection_failures;          // 连接失败次数
    uint64_t protocol_errors;              // 协议错误次数
    uint64_t authentication_failures;      // 认证失败次数
    
    double   avg_recovery_time;            // 平均恢复时间
    uint32_t max_consecutive_failures;     // 最大连续失败次数
    uint64_t last_error_time;              // 最后错误时间
} oracle_error_statistics_t;

// Oracle错误处理器类
class OracleErrorHandler
{
public:
    OracleErrorHandler();
    ~OracleErrorHandler();

    // 错误处理主接口
    int handle_error(int error_code, oracle_error_category_t category, 
                    const char *error_message, oracle_status_t *status);
    int handle_error_with_context(const oracle_error_info_t *error_info, oracle_status_t *status);
    
    // 错误处理器注册
    int register_error_handler(const oracle_error_handler_t *handler);
    int unregister_error_handler(int error_code, oracle_error_category_t category);
    int register_default_handlers();
    
    // 错误分类和分析
    oracle_error_category_t classify_error(int error_code, const char *error_message);
    oracle_error_severity_t determine_error_severity(int error_code, oracle_error_category_t category);
    oracle_recovery_strategy_t determine_recovery_strategy(const oracle_error_info_t *error_info);
    
    // 连接恢复管理
    int create_recovery_context(uint32_t connection_id, uint32_t session_id, 
                               const char *server_host, uint16_t server_port, 
                               const char *service_name, const char *username);
    int attempt_connection_recovery(oracle_recovery_context_t *recovery_ctx, oracle_status_t *status);
    int attempt_connection_retry(oracle_recovery_context_t *recovery_ctx, oracle_status_t *status);
    int attempt_connection_reestablish(oracle_recovery_context_t *recovery_ctx, oracle_status_t *status);
    int attempt_protocol_reset(oracle_recovery_context_t *recovery_ctx, oracle_status_t *status);
    
    // 错误恢复策略
    int execute_retry_strategy(const oracle_error_info_t *error_info, oracle_status_t *status);
    int execute_reconnect_strategy(const oracle_error_info_t *error_info, oracle_status_t *status);
    int execute_reset_strategy(const oracle_error_info_t *error_info, oracle_status_t *status);
    int execute_failover_strategy(const oracle_error_info_t *error_info, oracle_status_t *status);
    
    // 错误记录和跟踪
    void log_error(const oracle_error_info_t *error_info);
    void track_error_pattern(const oracle_error_info_t *error_info);
    bool is_error_pattern_detected(int error_code, uint32_t time_window_seconds);
    
    // 错误统计和监控
    void get_error_statistics(oracle_error_statistics_t *stats);
    void reset_error_statistics();
    void get_error_summary(uint32_t *total_errors, uint32_t *critical_errors, 
                          uint32_t *recent_errors, uint64_t *last_error_time);
    
    // 超时和清理管理
    int cleanup_expired_recovery_contexts(uint32_t timeout_seconds);
    int force_abort_recovery(uint32_t connection_id, const char *reason);
    
    // 配置管理
    void set_max_retry_attempts(uint32_t max_attempts) { m_max_retry_attempts = max_attempts; }
    void set_default_retry_delay(uint32_t delay_ms) { m_default_retry_delay = delay_ms; }
    void set_recovery_timeout(uint32_t timeout_seconds) { m_recovery_timeout = timeout_seconds; }
    void enable_error_logging(bool enable) { m_error_logging_enabled = enable; }
    
    // 调试和诊断
    void dump_error_info(const oracle_error_info_t *error_info);
    void dump_recovery_context(const oracle_recovery_context_t *recovery_ctx);
    void dump_error_handlers();
    
    // 预定义错误处理器
    static int handle_network_error(const oracle_error_info_t *error_info, oracle_status_t *status);
    static int handle_protocol_error(const oracle_error_info_t *error_info, oracle_status_t *status);
    static int handle_authentication_error(const oracle_error_info_t *error_info, oracle_status_t *status);
    static int handle_sql_error(const oracle_error_info_t *error_info, oracle_status_t *status);
    static int handle_transaction_error(const oracle_error_info_t *error_info, oracle_status_t *status);
    static int handle_timeout_error(const oracle_error_info_t *error_info, oracle_status_t *status);

private:
    // 内部错误处理方法
    oracle_error_handler_t* find_error_handler(int error_code, oracle_error_category_t category);
    int create_error_info(int error_code, oracle_error_category_t category, 
                         const char *error_message, oracle_error_info_t *error_info);
    int update_error_statistics(const oracle_error_info_t *error_info);
    
    // 恢复上下文管理
    oracle_recovery_context_t* find_recovery_context(uint32_t connection_id);
    int cleanup_recovery_context(oracle_recovery_context_t *recovery_ctx);
    bool should_attempt_recovery(const oracle_recovery_context_t *recovery_ctx);
    
    // 错误模式检测
    bool detect_connection_instability(uint32_t connection_id);
    bool detect_authentication_issues(uint32_t session_id);
    bool detect_resource_exhaustion();
    
    // 工具方法
    uint64_t get_current_timestamp();
    const char* get_error_category_name(oracle_error_category_t category);
    const char* get_error_severity_name(oracle_error_severity_t severity);
    const char* get_recovery_strategy_name(oracle_recovery_strategy_t strategy);
    
    // 数据成员
    std::vector<oracle_error_handler_t> m_error_handlers;           // 错误处理器列表
    std::map<uint32_t, oracle_recovery_context_t*> m_recovery_contexts; // 恢复上下文映射
    std::map<int, std::vector<uint64_t>> m_error_timestamps;        // 错误时间戳记录
    
    // 配置参数
    uint32_t m_max_retry_attempts;          // 最大重试次数
    uint32_t m_default_retry_delay;         // 默认重试延迟
    uint32_t m_recovery_timeout;            // 恢复超时时间
    bool     m_error_logging_enabled;       // 是否启用错误日志
    
    // 统计信息
    oracle_error_statistics_t m_statistics;
};

// 错误处理工具函数
namespace OracleErrorUtils
{
    // 错误分类工具
    const char* get_error_category_description(oracle_error_category_t category);
    const char* get_error_severity_description(oracle_error_severity_t severity);
    bool is_error_recoverable(int error_code, oracle_error_category_t category);
    
    // Oracle错误码映射
    oracle_error_category_t map_oracle_error_code(int oracle_error_code);
    oracle_error_severity_t map_oracle_error_severity(int oracle_error_code);
    const char* get_oracle_error_description(int oracle_error_code);
    
    // 网络错误工具
    bool is_network_timeout_error(int error_code);
    bool is_connection_refused_error(int error_code);
    bool is_dns_resolution_error(int error_code);
    
    // 恢复策略工具
    oracle_recovery_strategy_t recommend_recovery_strategy(const oracle_error_info_t *error_info);
    uint32_t calculate_retry_delay(uint32_t attempt_number, uint32_t base_delay_ms);
    bool should_escalate_error(const oracle_error_info_t *error_info);
    
    // 错误模式分析
    bool is_transient_error(int error_code, oracle_error_category_t category);
    bool is_permanent_error(int error_code, oracle_error_category_t category);
    double calculate_error_rate(const std::vector<uint64_t> &error_timestamps, uint32_t time_window_seconds);
}

#endif /* __ORACLE_ERROR_HANDLER_H__ */
