#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include "hbase_parser.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "hbase_stream.h"
#include "hbase_parser_inner.h"

namespace parser{
namespace hbase{
	/**
* 在接收数据时，探测数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
bool CHBaseParser::probe(CSessionMgt * psm, const app_stream * a_app, const struct conn * pcon){
	if(psm == nullptr || a_app == nullptr || pcon == nullptr){
		return false;
	}
	CSession* p_session = psm->find_session(pcon);
	if(p_session == nullptr){
	    p_session = psm->new_session(pcon);
	    if (p_session == NULL)
	    {
	      return false;
	    }
	}


	StreamData *psd;

	if ((psd = p_session->get_stream_data_from_parser(this)) != NULL)
	{
		hbase_stream* psm = psd->p_hbase_stream;
		if (psm){
			if( psm->is_hbase)
			{
				return true;
			}else{
				return false;
			}
		}else{

			return false;
		}
	}


	const char *data;
	int dir = a_app->dir;
	int data_len;
	int offset_out;
	data = p_session->get_data(this, dir, &data_len, &offset_out);

	//printf("data_len=%d\n", data_len);
	if (data == NULL || data_len < 6)
	{
		p_session->discard(this, dir, 0);

		return false;
	}

	bool result = check_is_hbase_protocol(data,data_len,dir);
    p_session->discard(this, dir, 0);

    if(result){
		// 创建 StreamData
		psd = new StreamData();
		if (!p_session->set_parser(this, psd))
		{

		  delete psd;
		  return false;
		}

		psd->p_hbase_stream=new hbase_stream();
		psd->p_hbase_stream->is_hbase = true;
		return true;
    }

    return false;
}

/**
* 在连接关闭时，探测数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
bool CHBaseParser::probe_on_close(CSessionMgt *psm, const app_stream * a_app, const struct conn *pcon){
	return probe(psm,a_app,pcon);
}

/**
* 在连接重置时，探测数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
bool CHBaseParser::probe_on_reset(CSessionMgt *psm, const app_stream * a_app, const struct conn *pcon){
	return probe(psm,a_app, pcon);
}
}
}