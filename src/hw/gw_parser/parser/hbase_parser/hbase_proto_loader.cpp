#include "hbase_proto_loader.h"

#include <stdio.h>
#include <dlfcn.h>
#include <dirent.h>

// #include "hbase_session.h"

const static char* prefix = "libhbaseproto_";
const static char* suffix = ".so";
const static char* path = "/opt/apigw/gwhw/lib/";

hbase_proto_loader::~hbase_proto_loader(){
    for(std::map<std::string,parse_functions>::iterator it = libs.begin();it !=libs.end();++it){
        if(it->second.handle != NULL){
            dlclose(it->second.handle);
        }
    }
}

void hbase_proto_loader::init(){
    struct dirent* p_dirent;
    DIR* p_dir = opendir(path);
    if(p_dir != NULL){
        while((p_dirent=readdir(p_dir))!=NULL){
            std::string file_name = p_dirent->d_name;
            if(file_name.find(prefix) == std::string::npos){
                continue;
            }
            file_name =path+ file_name;
            printf("%s\n", file_name.c_str());
            void* so_handle = dlopen(file_name.c_str(),RTLD_NOW);
            if(so_handle){
                std::string version = p_dirent->d_name;
                version = version.substr(14,version.length()-17);  // libhbaseproto_  14byte  .so 3byte
                if(version.length()>0){
                    parse_functions functions;
                    functions.handle = so_handle;
                    functions.p_parse_cell = (parse_message_func)dlsym(so_handle,"parse_cell_v");
                    functions.p_parse_request_header = (parse_message_func)dlsym(so_handle,"parse_request_header_v");
                    functions.p_parse_request = (parse_message_func)dlsym(so_handle,"parse_request_method_v");
                    functions.p_parse_response_header = (parse_message_func)dlsym(so_handle,"parse_response_header_v");
                    functions.p_parse_response = (parse_message_func)dlsym(so_handle,"parse_response_method_v");
                    functions.p_parse_connect_header = (parse_message_func)dlsym(so_handle,"parse_connect_header_v");
                    if(functions.p_parse_cell == NULL || functions.p_parse_request_header == NULL || functions.p_parse_request == NULL 
                        || functions.p_parse_response_header == NULL || functions.p_parse_response == NULL || functions.p_parse_connect_header == NULL){
                    }else{
                        libs[version] = functions;
                    }
                }
            }else{
                printf("load so %s error:%s\n", file_name.c_str(),dlerror());
            }
        }
        closedir(p_dir);
    }
}

parse_functions hbase_proto_loader::get_parser_func(std::string version){
    if(version.length()<3){
        if(libs.rbegin()!=libs.rend()){
            return libs.rbegin()->second;
        }else{
            return parse_functions();
        }
    }
    
    int fidx=version.find('.');
    if(fidx != std::string::npos){
        int secondidx = version.find('.',fidx+1);
        if(secondidx != std::string::npos){
            version = version.substr(0,secondidx);
        }
        version = version.replace(fidx,fidx+1,"_");
    }
    
    std::map<std::string,parse_functions>::iterator it = libs.find(version);
    if(it!=libs.end()){
        return it->second;
    }
    std::string match = "";
    for(std::map<std::string,parse_functions>::iterator kit = libs.begin();kit!=libs.end();++kit)
    {
        if(kit->first.compare(version)<0 && kit->first.compare(match)>0){
            match = kit->first;
        }
    }
    if(match.length()<1){
        return libs.rbegin()->second;
    }else{
        return libs[match];
    }
}