#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include "hbase_parser.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"
#include "gw_i_upload.h"

#include "hbase_stream.h"
#include "hbase_parser_inner.h"

namespace parser{
namespace hbase{

#define HBASE_EVENT_TYPE_EVENT            1
#define HBASE_EVENT_TYPE_CONTENT          2

static const char msg_event_type[] = "hbase_event";
static const char msg_content_type[] = "hbase_content";

/**
* 在接收数据时，解析数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
int CHBaseParser::parse(CSessionMgt *psm, const app_stream * a_app, const struct conn *pcon){
	if(psm == nullptr || a_app == nullptr || pcon == nullptr){
		return false;
	}
	CSession* p_session = psm->find_session(pcon);
	if(p_session == nullptr){
		return -1;
	}


	StreamData *psd;

	if ((psd = p_session->get_stream_data_from_parser(this)) == NULL)
	{
		return -1;
	}
	const char *data;
	int dir = a_app->dir;
	int data_len;
	int offset_out;
	data = p_session->get_data(this, dir, &data_len, &offset_out);
	int parsed_len = hbase_parse_data(psd->p_hbase_stream,data,data_len,a_app->dir);
	p_session->discard(this, dir, parsed_len);

	if (hbase_parse_complete(psd->p_hbase_stream))
	{		
        cJSON* event_json = cJSON_CreateObject();
        hase_session_to_event_json(pcon,psd->p_hbase_stream,event_json);
        
        char *p_log_str = cJSON_PrintUnformatted(event_json);
        if(p_log_str){
            // printf("%s\n",p_log_str);
            // event_cb_upload_msg(p_log_str, NULL);
            // __sync_fetch_and_add(&g_stats_hbase_parser.cnt_msg_inq_event, 1);
            // free(p_log_str);
            upload_msg(p_log_str,msg_event_type);
        }
        cJSON_Delete(event_json);


        cJSON* content_json = cJSON_CreateObject();
        hase_session_to_content_json(psd->p_hbase_stream,content_json);
        p_log_str = cJSON_PrintUnformatted(content_json);
        if(p_log_str){
            // printf("%s\n",p_log_str);
            // content_cb_upload_msg(p_log_str, NULL);
            // __sync_fetch_and_add(&g_stats_hbase_parser.cnt_msg_inq_content, 1);
            // free(p_log_str);
            upload_msg(p_log_str,msg_content_type);
        }
        cJSON_Delete(content_json);
	}
  return 0;
}

int CHBaseParser::parse_clear(CSessionMgt *psm, const app_stream * a_app, const struct conn *pcon){
    return 0;
}

/**
* 在连接关闭时，解析数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
int CHBaseParser::parse_on_close(CSessionMgt *p_sessionmgt, const app_stream * p_stream, const struct conn *p_conn){
	return parse(p_sessionmgt,p_stream,p_conn);
}

/**
* 在连接重置时，解析数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
int CHBaseParser::parse_on_reset(CSessionMgt *p_sessionmgt, const app_stream * p_stream, const struct conn *p_conn){
	return parse(p_sessionmgt,p_stream,p_conn);
}




void CHBaseParser::upload_msg(const char *msg,const char* type)
{
    if (m_pUpload == NULL)
    {
        GWLOG_INFO(m_comm, "%s upload null\n", HBASE_LOG_PRE);
        return;
    }

    UploadMsg *pum = new UploadMsg;
    memset(pum, 0, sizeof(UploadMsg));

    // hive_upload_user_data_t *p_ud = (hive_upload_user_data_t *)malloc(sizeof(hive_upload_user_data_t));
    // memset(p_ud, 0, sizeof(hive_upload_user_data_t));
    // if (op_id)
    // {
    //     p_ud->op_id = strdup(op_id);
    //     p_ud->op_id_len = strlen(op_id);
    // }
    // p_ud->event_type = evt_type;

    pum->cb = sizeof(UploadMsg);
    pum->destroy_func = free_upload_msg;
    // pum->userdata = p_ud;
    pum->parser = this;
    pum->length = strlen(msg);
    pum->s = msg;
    pum->msgtype = type;
    pum->mem_size = sizeof(UploadMsg) + pum->length;

    m_pUpload->put_msg(pum);
}

void CHBaseParser::free_upload_msg(const struct UploadMsg *p_um)
{
    if (p_um)
    {
        if (p_um->s != NULL)
        {
            cJSON_free((void *)p_um->s);
        }

        // hive_upload_user_data_t *p_ud = (hive_upload_user_data_t *)p_um->userdata;
        // if (p_ud)
        // {
        //     if (p_ud->op_id)
        //     {
        //         free(p_ud->op_id);
        //     }
        //     free(p_ud);
        // }
        delete p_um;
    }

    // __sync_fetch_and_sub(&g_stats_queue_memory_size, length);
}
}
}