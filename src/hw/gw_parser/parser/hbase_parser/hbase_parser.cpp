#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include "hbase_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"


#include "hbase_stream.h"

#include "worker_queue.h"
#include "task_worker.h"


#include "cJSON.h"

namespace parser{
namespace hbase{

CHBaseParser::CHBaseParser(){
  m_quit_signal = 0;
  m_comm = NULL;
  memset(m_name, 0, sizeof(m_name));
  snprintf(m_name, COUNTOF(m_name) - 1, "CHBaseParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);


  m_pUpload = NULL;
  m_conf_upload_name = "";
  m_event_prior = 1;
  m_content_prior = 2;
}

CHBaseParser::~CHBaseParser(void)
{
}


void CHBaseParser::cache_clean() 
{

}

/**
* 获取当前流解析出来的数据。
* @param struct StreamData *
* @param int dir
* @param int *data_len
* @param int *offset_out
*/
const char * CHBaseParser::get_data(const struct StreamData *, int dir, int *data_len, int *offset_out)
{
  *data_len = 0;
  *offset_out = 0;
  return NULL;
}

/**
* 已处理字节数。
* @param struct StreamData *
* @param int dir
* @param int num
*/
bool CHBaseParser::discard(struct StreamData *, int dir, int num)
{
  return false;
}

/**
* 已处理字节数，同时更新数据。
* @param struct StreamData *
* @param int dir
* @param int num
*/
bool CHBaseParser::discard_and_update(struct StreamData *, int dir, int num)
{
  return false;
}

// /**
//  * 删除解析对象中在会话管理中的单边数据。
//  * @param HalfStreamData*
//  */
// virtual void del_session_half_stream(HalfStreamData *);

/**
* @param StreamData*
*/
void CHBaseParser::del_session_stream(StreamData * psd){
	if(psd!=nullptr){
		delete psd->p_hbase_stream;
	}
}

/**
* @param SessionMgtData*
*/
void CHBaseParser::del_session_param(SessionMgtData *){

}

void CHBaseParser::init(){
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  load_conf(NULL);


  m_pUpload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
}

void CHBaseParser::fini(){
  ASSERT(m_comm != NULL);
}

void CHBaseParser::run(){
  ASSERT(m_comm != NULL);
}

/**
* 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
*/
const char * CHBaseParser::get_name(void) const
{
  return m_name;
}

/**
* 获取版本号。
*/
const char *CHBaseParser::get_version(void) const
{
  return  HBASEPARSER_VER;
}

/**
* 设置全局公共类对象实例。
* @param CGwCommon *comm
*/
void CHBaseParser::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
* 加载配置参数（Json字符串，支持动态）。
* @param const char *
*/
bool CHBaseParser::load_conf(const char *)
{
  CGwConfig *pgwc = m_comm->get_gw_config();


  
  m_conf_upload_name = pgwc->read_conf_string("parser", "upload_mode");

  // if (json_string != NULL)
  // {
  //   // TODO 动态加载配置参数
  //   pgwc->load_string(json_string);
  // }

  return true;
}

/**
* 触发退出信号时处理
*/
void CHBaseParser::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
* 等待运行结束
*/
void CHBaseParser::wait_for_stop(void)
{
}

/**
* 设置过滤规则。
* @param CFilterRule*rule
*/
void CHBaseParser::set_url_filter_rule(CFilterRule *rule){}

/**
 *  设置账号过滤规则 
 *  @param CFilterRule *rule
 */
void CHBaseParser::set_accout_filter_rule(CFilterRule *rule){}

void CHBaseParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) {}

// void CHBaseParser::free_worker_queue(CWorkerQueue *p)
// {
//   if (p == NULL)
//   {
//     return;
//   }
//   p->set_quit_signal();
//   p->wait_for_stop();

//   p->delete_queue();

//   p->fini();

//   if (m_comm != NULL)
//   {
//     m_comm->destory_worker_queue(p);
//   }
// }

// void CHBaseParser::free_task_worker(CTaskWorker *p)
// {
//   if (p == NULL)
//   {
//     return;
//   }
//   p->release();
// }

/**
* 增加上层协议解析对象。
* @param CParser *parser
*/
void CHBaseParser::add_upstream(CParser *parser){}

/**
* 清空上层协议解析对象
*/
void CHBaseParser::reset_upstream(void){}

/**
* 推送到上层消息(异步方式, Json序列化数据)
* @param char *s
* @param size_t *length
*/
void CHBaseParser::push_upstream_msg(char *s, size_t length)
{
  GWLOG_TEST(m_comm, "hbase test s=%p length=%u\n", s, length);
}

/**
* 是否使用当前协议解析流数据
* @param struct StreamData*
*/
bool CHBaseParser::is_parsed(const struct StreamData *) const
{
  return false;
}

/**
* 克隆会话流数据到队列中使用(预留)
* @param struct StreamData*
*/
struct StreamData *CHBaseParser::clone_stream_data(const struct StreamData *)
{
  return NULL;
}

/**
 *  获取解析http数量(针对http parser) 
 */
uint64_t CHBaseParser::get_parser_http_cnt()
{
  return 0;
}

/**
 *  获取解析http成功的数量(针对http parser) 
 */
uint64_t CHBaseParser::get_succ_parser_http_cnt()
{
  return 0;
}

/**
 *  获取解析parser的状态数据，以便于进行查看Parser内部状态
 */
void* CHBaseParser::get_parser_status()
{
  return NULL;
}

}
}