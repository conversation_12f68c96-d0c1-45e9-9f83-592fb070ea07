#include "hbase_parser_inner.h"

#include <netinet/in.h>
#include <arpa/inet.h>
#include<sys/time.h>
// #include <stdio.h>

#include <openssl/md5.h>

#include "hbase_stream.h"
#include "hbase_proto_loader.h"

#include <google/protobuf/io/coded_stream.h>

#include "deserializer/parse_message.h"
#include "utils.h"

const static int MAX_MESSAGE_LEN = 64*1024*1024;

namespace parser{
namespace hbase{

cJSON* parse_cell(google::protobuf::io::CodedInputStream &is,hbase_stream& session){
    if(session.functions.p_parse_cell == NULL){
        return NULL;
    }
    return session.functions.p_parse_cell(is,session);
}
cJSON* parse_request_header(google::protobuf::io::CodedInputStream &is,hbase_stream& session){
    if(session.functions.p_parse_request_header == NULL){
        return NULL;
    }
    return session.functions.p_parse_request_header(is,session);
}
cJSON* parse_request_method(google::protobuf::io::CodedInputStream &is,hbase_stream& session){
    if(session.functions.p_parse_request == NULL){
        return NULL;
    }
    return session.functions.p_parse_request(is,session);
}
cJSON* parse_response_header(google::protobuf::io::CodedInputStream &is,hbase_stream& session){
    if(session.functions.p_parse_response_header == NULL){
        return NULL;
    }
    return session.functions.p_parse_response_header(is,session);
}
cJSON* parse_response_method(google::protobuf::io::CodedInputStream &is,hbase_stream& session){
    if(session.functions.p_parse_response == NULL){
        return NULL;
    }
    return session.functions.p_parse_response(is,session);
}
void parse_connect_header(google::protobuf::io::CodedInputStream &is,hbase_stream& session){
    if(session.functions.p_parse_connect_header == NULL){
        session.functions = hbase_proto_loader::instance().get_parser_func("");
        if(session.functions.p_parse_connect_header == NULL){
            return;
        }
    }
    session.functions.p_parse_connect_header(is,session);
    if(session.client_version.length()>0){
        session.functions = hbase_proto_loader::instance().get_parser_func(session.client_version);
    }else{
        //1.0.1及以下版本，无版本信息，统一采用1.0.1版本来解析。但版本信息保留空值。
        session.functions = hbase_proto_loader::instance().get_parser_func("1.0");
    }
}


bool check_is_hbase_protocol(const char *data, int len,int dir){
    CodedInputStream is((const unsigned char*)data,len);
    if(!dir){
        //请求数据

        //连接请求有6个字节的前导内容
        //前四个字节为"HBas",
        //第五个字节为0,
        //第六个字节为认证方式,三个值之一：80(SIMPLE),81(KERBEROS),82(DIGEST)
        //然后是4个字节，表示这次请求的总长度。

        if(len < 6){
            return false;
        }

        if((strncasecmp(data,"HBas",4) != 0) || (data[5]<80 || data[5]>82)){
            return false;
        }

        //前导6个字节，4个字节消息长度，后续跟着ConnectHeader,RequestHeader等消息
        //没法保证这个数据块包含这些消息的完整内容（不完整会解析失败），不使用这些消息的解析来判断协议类型。
    }else{
        //应答数据
        uint32_t msglen =0;
        if(!is.ReadLittleEndian32(&msglen) || ntohl(msglen) > MAX_MESSAGE_LEN){
            //读取应答头长度失败,或无效长度
            return false;
        }
        if(!is.ReadVarint32(&msglen)){
        //数据异常，丢弃当前数据
        return false;
        }
        is.PushLimit(msglen);
        hbase_stream session;
        cJSON* json = parse_response_header(is,session);
        json_autoptr _auto(json);
        if(json == NULL){
            return false;
        }
    }

    return true;
}

int parse_request(hbase_stream &session, const char *data, size_t len){
    if(session.request.call_id > 0){
        if(session.unresponse_request.size()>100){
            std::map<int,hbase_request_info>::iterator it =session.unresponse_request.begin();
            for(;it != session.unresponse_request.end();){
                if(it->first < session.request.call_id - 100){
                    session.unresponse_request.erase(it++);
                }else{
                    it++;
                }
            }
        }
        session.unresponse_request[session.request.call_id] = session.request;
        session.request.reset();
    }
    struct timeval tv;  
    gettimeofday(&tv,NULL);
    session.request.tm = tv.tv_sec;
    session.request.tm = session.request.tm * 1000 + tv.tv_usec/1000;

    CodedInputStream is((const unsigned char*)data,len);
    uint32_t msglen =0;

    //如果连接消息未解析，先解析连接消息。后续请求不包含连接消息
    if(!session.has_read_connect_header){
        is.Skip(6);

        if(is.ExpectAtEnd()){
            return 0;
        }

        if(!is.ReadLittleEndian32(&msglen)){
            //读取请求头长度失败
            return 0;
        }

        msglen=ntohl(msglen);
        if(len < msglen+10){
            //收到的数据没有达到一个完整的消息包
            return 0;
        }

        //从这里开始，收到的数据已经是一个完全的请求了，如果还解析失败，则认为数据错误，忽略这份数据。
        int limit = is.PushLimit(msglen);
        parse_connect_header(is,session);

        is.PopLimit(limit);
        if(is.ExpectAtEnd()){

            return len;
        }
    }else{
        if(!is.ReadLittleEndian32(&msglen)){
            //读取请求头长度失败,等待后续数据
            return 0;
        }

        msglen=ntohl(msglen);

        if(msglen > MAX_MESSAGE_LEN){
            //数据异常，丢弃当前数据
            return len;
        }

        if(len < 4+msglen){
            //收到的数据没有达到一个完整的消息包
            return 0;
        }
    }

    if(!is.ReadVarint32(&msglen)){
        //数据异常，丢弃当前数据
        return len;
    }
    int limit = is.PushLimit(msglen);
    cJSON* request_header = parse_request_header(is,session);
    if(request_header == NULL){
        return len;
    }
    is.PopLimit(limit);
    json_autoptr _request_header(request_header);
    cJSON *root = cJSON_CreateObject();
    json_autoptr auto_root(root);
    cJSON_AddItemToObject(auto_root.ptr,"RequestHeader",_request_header.release());

    //如果仍有数据，解析请求
    if(!is.ExpectAtEnd()){
        cJSON* request = parse_request_method(is,session);
        if(request ){
            json_autoptr _request(request);
            cJSON_AddItemToObject(auto_root.ptr,"Request",_request.release());
        }
    }

    if(!is.ExpectAtEnd()){
        json_autoptr array(cJSON_CreateArray());
        while(!is.ExpectAtEnd()){
            cJSON_AddItemToArray(array.ptr, parse_cell(is,session));
        }
        cJSON_AddItemToObject(auto_root.ptr,"cellblocks", array.release());
    }

    char* p_str = cJSON_PrintUnformatted(auto_root.ptr);
    if(p_str){
        session.request.request_content = std::string(p_str);
        free(p_str);
    }
    // printf("++%s \n",session.request.request_content.c_str());
    //当前消息总长度
    return is.CurrentPosition();
}


int parse_response(hbase_stream& session,const char *data, size_t len){
    CodedInputStream is((const unsigned char*)data,len);
    uint32_t msglen =0;

    if(!is.ReadLittleEndian32(&msglen)){
        //读取请求头长度失败，等待后续数据
        return 0;
    }

    msglen=ntohl(msglen);

    //数据异常，丢弃当前数据
    if(msglen > MAX_MESSAGE_LEN){
        return len;
    }

    if(len < msglen+4){
        //收到的数据没有达到一个完整的消息包，等待后续数据
        return 0;
    }

    //从这里开始，收到的数据已经是一个完全的请求了，如果还解析失败，则认为数据错误，忽略这份数据。
    if(!is.ReadVarint32(&msglen)){
        //数据异常，丢弃当前数据
        return len;
    }
    int limit = is.PushLimit(msglen);
    cJSON* response_header = parse_response_header(is,session);
    if(response_header == NULL){
        return len;
    }
    json_autoptr _response_header(response_header);

    if(session.request.call_id != session.response.call_id){
        if(session.request.call_id>0){
            session.unresponse_request[session.request.call_id] = session.request;
            session.request.reset();
        }
        std::map<int,hbase_request_info>::iterator it =session.unresponse_request.find(session.response.call_id);
        if(it != session.unresponse_request.end()){
            session.request = it->second;
        }else{
            session.response.reset();
            return len;
        }
    }
    is.PopLimit(limit);
    cJSON *root = cJSON_CreateObject();
    json_autoptr auto_root(root);
    cJSON_AddItemToObject(auto_root.ptr,"ResponseHeader",_response_header.release());

    //如果仍有数据，解析请求
    if(!is.ExpectAtEnd()){
        cJSON* response = parse_response_method(is,session);
        if(response ){
            json_autoptr _response(response);
            cJSON_AddItemToObject(auto_root.ptr,"Response", _response.release());
        }
    }

    if(!is.ExpectAtEnd()){
        json_autoptr array(cJSON_CreateArray());
        while(!is.ExpectAtEnd()){
            cJSON_AddItemToArray(array.ptr, parse_cell(is,session));
        }
        cJSON_AddItemToObject(auto_root.ptr,"cellblocks", array.release());
    }
    char* p_str = cJSON_PrintUnformatted(auto_root.ptr);
    if(p_str){
        session.response.response_content = std::string(p_str);
        free(p_str);
    }
    // printf("--%s\n",session.response.response_content.c_str());
    //当前消息总长度
    return is.CurrentPosition();
}


int hbase_parse_data(hbase_stream* p_stream,const char *data, size_t len,bool to_server){
    if(p_stream == NULL){
        return 0;
    }

    if(to_server){
        return parse_request(*p_stream,data,len);
    }else{
        return parse_response(*p_stream,data,len);
    }
}

bool hbase_parse_complete(hbase_stream* p_stream){
    if(p_stream == NULL){
        return false;
    }
    return p_stream->is_complelte(); 
}

void add_net_json(cJSON *p_json_obj, hbase_stream* p_stream, const struct conn *pcon)
{
    if (p_json_obj == NULL || pcon == NULL)
    {
        return;
    }

    char a_src_ip[64] = {0};
    char a_dst_ip[64] = {0};

    if (pcon->client.v == 4)
    {
        strncpy(a_src_ip, int_ntoa(pcon->client.ipv4), 64 - 1);
        strncpy(a_dst_ip, int_ntoa(pcon->server.ipv4), 64 - 1);
    }
    else
    {
        get_ip6addr_str((uint32_t*)pcon->client.ipv6, a_src_ip, COUNTOF(a_src_ip));
        get_ip6addr_str((uint32_t*)pcon->server.ipv6, a_dst_ip, COUNTOF(a_dst_ip));
    }


    unsigned char id[16];
    char id_str[40] = {0};
    std::string uni_id = "";
    uni_id += a_src_ip;
    uni_id += a_dst_ip;
    uni_id += std::to_string(pcon->client.port);
    uni_id += std::to_string(pcon->server.port);
    uni_id += std::to_string(p_stream->request.tm);
    MD5((const unsigned char*)uni_id.c_str(),uni_id.length(),id);
    for(int i=0;i<16;++i){
        sprintf(id_str+(i*2),"%02x",id[i]);
    }
    p_stream->req_md5=std::string(id_str);
    cJSON_AddStringToObject(p_json_obj, "id", id_str);

    cJSON_AddStringToObject(p_json_obj, "ip", a_src_ip);
    cJSON_AddNumberToObject(p_json_obj, "port", pcon->client.port);
    cJSON_AddStringToObject(p_json_obj, "serverIp", a_dst_ip);
    cJSON_AddNumberToObject(p_json_obj, "serverPort", pcon->server.port);
    return;
}

int hase_session_to_event_json(const struct conn *pcon, hbase_stream* p_stream,cJSON *root){
    if(p_stream == NULL){
        return 0;
    }

    add_net_json(root,p_stream,pcon);

    int call_id = p_stream->request.call_id;
    p_stream->to_event_json(root);
    std::string strdata = std::to_string(pcon->client.port);
    cJSON_AddStringToObject(root,"port",strdata.c_str());
    strdata = std::to_string(pcon->server.port);
    cJSON_AddStringToObject(root,"serverPort",strdata.c_str());
    return call_id;
}



int hase_session_to_content_json(hbase_stream* p_stream,cJSON *root){
    if(p_stream == NULL){
        return 0;
    }

    int call_id = p_stream->request.call_id;
    p_stream->to_content_json(root);
    p_stream->reset_info();
    return call_id;
}
    
}
}