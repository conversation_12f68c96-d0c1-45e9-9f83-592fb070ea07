/*
*  HBase协议解析模块
*
*/

#ifndef __PROTO_PARSER_L7_HBASE_HBASE_PARSER_H__
#define __PROTO_PARSER_L7_HBASE_HBASE_PARSER_H__

#include <stdbool.h>
#include <stdlib.h>
#include "utils.h"


#include "cJSON.h"


struct hbase_stream;

namespace parser{
namespace hbase{


int hbase_parse_data(hbase_stream* p_stream,const char *data, size_t len,bool to_server);

bool hbase_parse_complete(hbase_stream* p_stream);

int hase_session_to_event_json(const struct conn *pcon, hbase_stream* p_stream,cJSON *root);
int hase_session_to_content_json(hbase_stream* p_stream,cJSON *root);

bool check_is_hbase_protocol(const char *data, int len,int dir);

}
}


#endif  //__PROTO_PARSER_L7_HBASE_HBASE_PARSER_H__