/*
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 *
 * json2pb is free software; you can redistribute it and/or modify
 * it under the terms of the MIT license. See LICENSE for details.
 * 
 *  参照https://github.com/shramov/json2pb,将protobuf消息转为json
 *  主要改动：将源码里使用的json工具由jansson改为项目使用的cJSON
 */

#ifndef __PROTO_PARSER_L7_HBASE_PB2JSON_H_
#define __PROTO_PARSER_L7_HBASE_PB2JSON_H_

#include "cJSON.h"

//前置声明，占位，避免应用头文件
namespace google{
    namespace protobuf{
        class Message;
    }
}

//json对象的自动释放
struct json_autoptr {
	cJSON * ptr;
	json_autoptr(cJSON *json) : ptr(json) {}
	~json_autoptr() { if (ptr) cJSON_Delete(ptr); }
	cJSON * release() { cJSON *tmp = ptr; ptr = 0; return tmp; }
};

cJSON* pb2json(const google::protobuf::Message &msg);

#endif //__PROTO_PARSER_L7_HBASE_PB2JSON_H_