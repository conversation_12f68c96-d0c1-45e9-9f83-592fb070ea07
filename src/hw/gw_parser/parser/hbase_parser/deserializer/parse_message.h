

#ifndef __PROTO_PARSER_L7_PARSE_MESSAGE_H__
#define __PROTO_PARSER_L7_PARSE_MESSAGE_H__

#include <string>
#include <map>

#include "pb2json.h"

#include <google/protobuf/io/coded_stream.h>



#define __HAS_VERSION_INFO__ 1
#define __HAS_NAMESPACE_INFO__ 1

using namespace google::protobuf::io;

struct hbase_stream;

typedef cJSON* (*p_parse_func)(CodedInputStream &is);

template<class PBMessage>
cJSON* parse_message(CodedInputStream &is){
    uint32_t msglen = 0;
    if(!is.ReadVarint32(&msglen)){
        return NULL;
    }
    int limit = is.PushLimit(msglen);
    PBMessage m;
    if(!m.ParseFromCodedStream(&is)){
        return NULL;
    }
    is.PopLimit(limit);
    return pb2json(m);
}

void regist_parser_func(std::string request_method,p_parse_func request_parser_func,p_parse_func response_parser_func);


#define REGIST_MESSAGE_PARSER(Method,Request,Response) regist_parser_func(#Method,&parse_message<Request>,&parse_message<Response>);



#ifdef __cplusplus
extern "C"
{
#endif


cJSON* parse_cell_v(CodedInputStream &is, hbase_stream& session);
cJSON* parse_request_header_v(CodedInputStream &is, hbase_stream& session);
cJSON* parse_request_method_v(CodedInputStream &is, hbase_stream& session);
cJSON* parse_response_header_v(CodedInputStream &is, hbase_stream& session);
cJSON* parse_response_method_v(CodedInputStream &is, hbase_stream& session);
cJSON* parse_connect_header_v(CodedInputStream &is, hbase_stream& session);

#ifdef __cplusplus
}
#endif

#endif  //__PROTO_PARSER_L7_PARSE_MESSAGE_H__