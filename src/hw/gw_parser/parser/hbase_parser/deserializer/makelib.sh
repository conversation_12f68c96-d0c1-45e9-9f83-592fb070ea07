#!/bin/bash
#
#hbase2.0.0之前使用protobuf 2.5.0对RPC消息进行序列化和反序列化。2.0.0版本开始，使用protobuf 3.1.0版本进行序列化和反序列化。
#hbase不同版本之间，RPC消息内容不完全一致，由proto文件定义。
#以下脚本根据hbase的proto文件，生成RPC消息的序列化和反序列化代码（需要使用正确的protobuf版本），并编译成独立的动态库文件。
#生成的so文件为 ./build/lib/hbaseproto.so
#如果由于版本部匹配不能正确解析hbase RPC消息，需要重新从github上下载对应版本分支的proto文件，重新生成动态库文件，进行替换。

if [[ $# < 1 ]]; then
  exit 0
fi

version=$1
version=${version##*branches/}
version=${version%%/*}
version=${version#*-}
version=${version//\./_}

echo "build version $version"


PROTOCV=2.5.0

HASVERSION=0
HASNAMESPACE=0





if [[ -f ./protobuf${version}/build/lib/libhbaseproto.so ]]; then
  nums=`find . -maxdepth 1 -newer "./protobuf${version}/build/lib/libhbaseproto.so" |grep -E "\.h|\.cpp|\.txt" -c`
  if [[ $nums < 1 ]]; then
    exit 0
  fi
fi



if [[ ! -d protobuf${version} ]]; then
  exit 0
fi

pushd protobuf${version}

  mkdir cpp
  mkdir build
  if [ ! -d ./protobuf ]; then
    for url in $@ 
    do
      svn export $url --force
    done
  fi

  pushd ./protobuf
    for fname in `ls *.proto|grep -v ".${version}.proto"`;
    do
      sed -i '/^import ".*proto"/s/\.proto/'${version}'\.proto/' $fname
      sed -i "/^import '.*proto'/s/\\.proto/${version}\\.proto/" $fname
      sed -i 's/\.protobuf\.generated/\.protobuf'${version}'\.generated/' $fname
      sed -i 's/package hbase\.pb/package hbase'${version}'\.pb/' $fname
      lines=`cat ${fname} |grep "^package" -c`
      if [[ $lines > 0 ]]; then
        sed -i '/^packet/s/hbase/hbase'${version}'/' $fname
      else
        sed -i '1ipackage hbase'${version}'\.pb;' $fname
       fi
      mv -f "$fname" `echo "$fname"|sed "s/\\.proto$/${version}\\.proto/"`
    done
  popd

  case $version in 
    0_*|'1_0')
      HASVERSION=0
      # HASNAMESPACE=1
      ;;
    '1_1'|'1_2')
      HASVERSION=1
      # HASNAMESPACE=1
      ;;
    1_*)
      HASVERSION=1
      # HASNAMESPACE=1
      # PROTOCV=2.5.0
      ;;
    2_*)
      HASVERSION=1
      # HASNAMESPACE=1
      # PROTOCV=3.1.0
      ;;
  esac


  protoc --version


  sed -i 's/ EOF/ MYEOF/g' ./protobuf/Procedure${version}.proto

  pushd protobuf > /dev/null
    protoc  --cpp_out=../cpp *.proto
  popd

  
  \cp -f ../CMakeLists.txt ./
  \cp -f ../parse_message.* ./
  \cp -f ../pb2json.* ./

  sed -i "s/\#define __HAS_VERSION_INFO__.*/\#define __HAS_VERSION_INFO__ ${HASVERSION}/g" parse_message.h
  # sed -i "s/\#define __HAS_NAMESPACE_INFO__.*/\#define __HAS_NAMESPACE_INFO__ ${HASNAMESPACE}/g" ../parse_message.h


  python ../gencode.py ./protobuf


  sed -i 's/\.pb\.h/'${version}'\.pb\.h/g' parse_message.cpp
  sed -i 's/using namespace hbase::pb/using namespace hbase'${version}'::pb/' parse_message.cpp
  sed -i 's/using namespace hbase::pb/using namespace hbase'${version}'::pb/' parse_message_impl.cpp


  pushd ./build > /dev/null
    cmake ../
    make -j4
  popd


  if [ -f ./build/lib/libhbaseproto.so ]; then
    \cp -f ./build/lib/libhbaseproto.so ../libhbaseproto_${version}.so
  fi
popd
