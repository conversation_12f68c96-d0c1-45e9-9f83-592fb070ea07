#include "parse_message.h"

#include <netinet/in.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <sys/time.h>


#include "../../hbase_stream.h"
#include "cpp/Cell.pb.h"
#include "cpp/Client.pb.h"
#include "cpp/RPC.pb.h"


#if __HAS_NAMESPACE_INFO__
using namespace hbase::pb;
#endif


static std::map<std::string,p_parse_func> request_parser;
static std::map<std::string,p_parse_func> response_parser;

void regist_parser_func(std::string request_method,p_parse_func request_parser_func,p_parse_func response_parser_func){
    request_parser[request_method] = request_parser_func;
    response_parser[request_method] = response_parser_func;
}

bool read_int_from_stream(CodedInputStream &is,uint32_t& value){
    if(!is.ReadLittleEndian32(&value)){
        return false;
    }
    value = ntohl(value);
    return true;
}

bool read_short_from_stream(CodedInputStream &is,uint16_t& value){
    if(!is.ReadRaw(&value,sizeof(value))){
        return false;
    }
    value = ntohs(value);
    return true;
}

uint64_t ntohl64(uint64_t value){
    uint64_t result = 0;
    uint32_t low = 0, hight = 0;
    low = value & 0xFFFFFFFF;
    hight = (value >>32) & 0xFFFFFFFF;
    low = ntohl(low);
    hight = ntohl(hight);
    result = low;
    result = (result << 32) | hight;
    return result;
 }

cJSON* parse_cell_v(CodedInputStream &is,hbase_stream& session){
    uint32_t    cell_len = 0, key_len = 0, value_len = 0, qualifier_len = 0;
    uint16_t    row_len = 0, tags_len = 0;
    uint8_t     family_len = 0, type = 0;
    uint64_t    timestamp = 0;
    std::string row = "", family = "", qualifier = "", value = "", tags = "";
    Cell cell;
    if(!read_int_from_stream(is,cell_len)){
        return NULL;
    }
    if(!read_int_from_stream(is,key_len)){
        return NULL;
    }
    if(!read_int_from_stream(is,value_len)){
        return NULL;
    }
    if(!read_short_from_stream(is,row_len)){
        return NULL;
    }
    if(!is.ReadString(&row,row_len)){
        return NULL;
    }
    cell.set_row(row);
    if(!is.ReadRaw(&family_len, sizeof(family_len))){
        return NULL;
    }
    if(!is.ReadString(&family,family_len)){
        return NULL;
    };
    cell.set_family(family);
    qualifier_len = key_len - sizeof(row_len) - row_len - sizeof(family_len) - family_len - sizeof(timestamp) - sizeof(type);
    if(!is.ReadString(&qualifier,qualifier_len)){
        return NULL;
    }
    cell.set_qualifier(qualifier);
    if(!is.ReadRaw(&timestamp,sizeof(timestamp))){
        return NULL;
    }
    cell.set_timestamp(ntohl64(timestamp));
    if(!is.ReadRaw(&type,sizeof(type))){
        return NULL;
    }
    cell.set_cell_type((CellType)type);
    if(!is.ReadString(&value,value_len)){
        return NULL;
    }
    cell.set_value(value);

    if(cell_len - key_len - sizeof(key_len) - value_len - sizeof(value_len) > 0){
        if(!read_short_from_stream(is,tags_len)){
            return NULL;
        }
        if(!is.ReadString(&tags,tags_len)){
            return NULL;
        }
        cell.set_tags(tags);
    }
    return pb2json(cell);
}
cJSON* parse_request_header_v(CodedInputStream &is,hbase_stream& session){
    RequestHeader rh;
    if(!rh.ParseFromCodedStream(&is)){
        return NULL;
    }
    session.request.call_id = rh.call_id();
    session.request.request_method = rh.method_name();
    return pb2json(rh);
}
cJSON* parse_request_method_v(CodedInputStream &is,hbase_stream& session){
    p_parse_func p_func = request_parser[session.request.request_method];
    if(p_func == NULL){
        return NULL;
    }
    return p_func(is);
}
cJSON* parse_response_header_v(CodedInputStream &is,hbase_stream& session){
    ResponseHeader rh;
    if(!rh.ParseFromCodedStream(&is)){
        return NULL;
    }
    session.response.call_id = rh.call_id();
    if(rh.has_exception()){
        session.response.exception_class_name=rh.exception().exception_class_name();
        session.response.stack_trace=rh.exception().stack_trace();
    }
    return pb2json(rh);
}
cJSON* parse_response_method_v(CodedInputStream &is,hbase_stream& session){
    p_parse_func p_func = response_parser[session.request.request_method];
    if(p_func == NULL){
        return NULL;
    }
    return p_func(is);
}

cJSON*  parse_connect_header_v(CodedInputStream &is,hbase_stream& session){
    ConnectionHeader ch;
    if(!ch.ParseFromCodedStream(&is)){
        //数据异常，丢弃当前数据
        return NULL;
    }
    session.has_read_connect_header=true;

    if(ch.has_user_info()){
        session.effective_user = ch.user_info().effective_user();
    }
    if(ch.has_service_name()){
        session.service_name = ch.service_name();
    }
#if __HAS_VERSION_INFO__
    if(ch.has_version_info()){
        session.client_version = ch.version_info().version();
    }
#endif
    return NULL;
}
