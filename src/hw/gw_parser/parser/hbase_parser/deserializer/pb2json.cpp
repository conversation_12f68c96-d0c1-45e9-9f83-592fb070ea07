/*
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 *
 * json2pb is free software; you can redistribute it and/or modify
 * it under the terms of the MIT license. See LICENSE for details.
 */

#include "pb2json.h"

#include <stdio.h>

#include <string>

#include <google/protobuf/message.h>
#include <google/protobuf/descriptor.h>

#include "cJSON.h"

using google::protobuf::Message;
using google::protobuf::MessageFactory;
using google::protobuf::Descriptor;
using google::protobuf::FieldDescriptor;
using google::protobuf::EnumDescriptor;
using google::protobuf::EnumValueDescriptor;
using google::protobuf::Reflection;

inline std::string b64_encode(const std::string &s)
{
	typedef unsigned char u1;
	static const char lookup[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZab<PERSON>lmnopqrstuvwxyz0123456789+/";
	const u1 * data = (const u1 *) s.c_str();
	std::string r;
	r.reserve(s.size() * 4 / 3 + 3);
	for (size_t i = 0; i < s.size(); i += 3) {
		unsigned n = data[i] << 16;
		if (i + 1 < s.size()) n |= data[i + 1] << 8;
		if (i + 2 < s.size()) n |= data[i + 2];

		u1 n0 = (u1)(n >> 18) & 0x3f;
		u1 n1 = (u1)(n >> 12) & 0x3f;
		u1 n2 = (u1)(n >>  6) & 0x3f;
		u1 n3 = (u1)(n      ) & 0x3f;

		r.push_back(lookup[n0]);
		r.push_back(lookup[n1]);
		if (i + 1 < s.size()) r.push_back(lookup[n2]);
		if (i + 2 < s.size()) r.push_back(lookup[n3]);
	}
	for (int i = 0; i < (3 - s.size() % 3) % 3; i++)
		r.push_back('=');
	return r;
}

// static cJSON* _pb2json(const Message& msg);
static cJSON* _field2json(const Message& msg, const FieldDescriptor *field, size_t index){
    const Reflection *ref = msg.GetReflection();
	const bool repeated = field->is_repeated();

    cJSON* json = NULL;

	switch (field->cpp_type()){
#define _CONVERT(type, ctype, fmt, sfunc, afunc)		\
		case FieldDescriptor::type: {			\
			const ctype value = (repeated)?		\
				ref->afunc(msg, field, index):	\
				ref->sfunc(msg, field);		\
			json = fmt(value);			\
			break;					\
		}

		_CONVERT(CPPTYPE_DOUBLE, double, cJSON_CreateNumber, GetDouble, GetRepeatedDouble);
		_CONVERT(CPPTYPE_FLOAT, double, cJSON_CreateNumber, GetFloat, GetRepeatedFloat);
		_CONVERT(CPPTYPE_INT64, int64_t, cJSON_CreateNumber, GetInt64, GetRepeatedInt64);
		_CONVERT(CPPTYPE_UINT64, uint64_t, cJSON_CreateNumber, GetUInt64, GetRepeatedUInt64);
		_CONVERT(CPPTYPE_INT32, int32_t, cJSON_CreateNumber, GetInt32, GetRepeatedInt32);
		_CONVERT(CPPTYPE_UINT32, uint32_t, cJSON_CreateNumber, GetUInt32, GetRepeatedUInt32);
		_CONVERT(CPPTYPE_BOOL, bool, cJSON_CreateBool, GetBool, GetRepeatedBool);
#undef _CONVERT
        case FieldDescriptor::CPPTYPE_STRING: {
			std::string scratch;
			const std::string &value = (repeated)?
				ref->GetRepeatedStringReference(msg, field, index, &scratch):
				ref->GetStringReference(msg, field, &scratch);
			// if (field->type() == FieldDescriptor::TYPE_BYTES){
            //         std::string b64_value = b64_encode(value);
            //         json = cJSON_CreateString2(b64_value.c_str(),b64_value.length());
            //     }
			// else{
				json = cJSON_CreateString2(value.c_str(),value.length());
            // }
			break;
		}
		case FieldDescriptor::CPPTYPE_MESSAGE: {
			const Message& mf = (repeated)?
				ref->GetRepeatedMessage(msg, field, index):
				ref->GetMessage(msg, field);
			json = pb2json(mf);
			break;
		}
		case FieldDescriptor::CPPTYPE_ENUM: {
			const EnumValueDescriptor* ef = (repeated)?
				ref->GetRepeatedEnum(msg, field, index):
				ref->GetEnum(msg, field);

			json = cJSON_CreateNumber(ef->number());
			break;
		}
        default:
            break;
    }
    return json;
}
cJSON* pb2json(const Message& msg){
	const Descriptor *d = msg.GetDescriptor();
	const Reflection *ref = msg.GetReflection();
	if (!d || !ref) return 0;

	cJSON *root = cJSON_CreateObject();
	json_autoptr _auto(root);

	std::vector<const FieldDescriptor *> fields;
	ref->ListFields(msg, &fields);

	for (size_t i = 0; i != fields.size(); i++)
	{
		const FieldDescriptor *field = fields[i];

		cJSON *json = 0;
		if(field->is_repeated()) {
			size_t count = ref->FieldSize(msg, field);
			if (!count) continue;

			json_autoptr array(cJSON_CreateArray());
			for (size_t j = 0; j < count; j++){
				cJSON_AddItemToArray(array.ptr, _field2json(msg, field, j));
            }
			json = array.release();
		} else if (ref->HasField(msg, field)){
			json = _field2json(msg, field, 0);
        }
		else
			continue;

		const std::string &name = (field->is_extension())?field->full_name():field->name();
		cJSON_AddItemToObject(root, name.c_str(), json);
	}
	return _auto.release();
}

// std::string pb2json(const Message &msg){
//     std::string r;

//     cJSON* root = _pb2json(msg);
//     json_autoptr _auto(root);
//     char* p_str = cJSON_PrintUnformatted(root);
//     printf("%s\n",msg.Utf8DebugString().c_str());
//     printf("json:%s\n",p_str);
//     if(p_str != NULL){
//         r = std::string(p_str);
//         free(p_str);
//     }
//     return r;
// }