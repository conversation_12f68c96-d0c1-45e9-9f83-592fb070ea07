#hbase RPC消息序列化反序列化库的编译生成。


cmake_minimum_required(VERSION 2.8)
project(hbaseproto)


SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")
#SET(CMAKE_C_VISIBILITY_PRESET hidden)
#SET(CMAKE_CXX_VISIBILITY_PRESET hidden)
SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++0x")

SET(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g -ggdb")
SET(CMAKE_CXX_FLAGS_RELEASE "$ENV{CXXFLAGS} -O3 -Wall")
SET(CMAKE_BUILD_TYPE Release)


SET(EXECUTABLE_OUTPUT_PATH "${PROJECT_BINARY_DIR}/bin")
SET(LIBRARY_OUTPUT_PATH "${PROJECT_BINARY_DIR}/lib")
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}/../../)
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}/../../../../utils/cjson)
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}/../../../../include)
INCLUDE_DIRECTORIES(/opt/openssl/include/)
LINK_DIRECTORIES(/opt/openssl/lib/)

AUX_SOURCE_DIRECTORY(. DIR_SRCS)
AUX_SOURCE_DIRECTORY(./cpp HBASE_PROTO_SRC)
AUX_SOURCE_DIRECTORY(${PROJECT_SOURCE_DIR}/../../../../utils/cjson CJSON)

add_library(hbaseproto SHARED ${DIR_SRCS} ${HBASE_PROTO_SRC} ${CJSON} ${PROJECT_SOURCE_DIR}/../../hbase_stream.cpp)
target_link_libraries(hbaseproto protobuf ssl crypto)