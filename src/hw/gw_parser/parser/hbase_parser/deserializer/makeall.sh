#!/bin/bash

###为各个版本的hbase生成动态库
###调用格式： ./makelib.sh hbase分支protobuf文件目录的url。目前不支持trunk分支
./makelib.sh https://github.com/apache/hbase/branches/0.98/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/0.98/hbase-rest/src/main/resources/org/apache/hadoop/hbase/rest/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-1.0/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-1.0/hbase-rest/src/main/resources/org/apache/hadoop/hbase/rest/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-1.1/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-1.1/hbase-rest/src/main/resources/org/apache/hadoop/hbase/rest/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-1.2/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-1.2/hbase-rest/src/main/resources/org/apache/hadoop/hbase/rest/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-1.3/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-1.3/hbase-rest/src/main/resources/org/apache/hadoop/hbase/rest/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-1.4/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-1.4/hbase-rest/src/main/resources/org/apache/hadoop/hbase/rest/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-2.0/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-2.0/hbase-rest/src/main/protobuf https://github.com/apache/hbase/branches/branch-2.0/hbase-rsgroup/src/main/protobuf
./makelib.sh https://github.com/apache/hbase/branches/branch-2.1/hbase-protocol/src/main/protobuf https://github.com/apache/hbase/branches/branch-2.1/hbase-rest/src/main/protobuf https://github.com/apache/hbase/branches/branch-2.1/hbase-rsgroup/src/main/protobuf

#下面这两个需要protobuf3.5.1进行编译
# ./makelib.sh https://github.com/apache/hbase/branches/branch-2.0/hbase-protocol-shaded/src/main/protobuf
# ./makelib.sh https://github.com/apache/hbase/branches/branch-2.1/hbase-protocol-shaded/src/main/protobuf