import sys,os,re

includelist = ''
registlist = ''

def getService(line):
    services = line.split(' ')
    return services[1]

def getMethod(line):
    mqr = re.split('[ ()\n]',line)
    methodname=''
    request=''
    response=''
    for s in range(0,len(mqr)):
        if mqr[s] == 'rpc':
            methodname = mqr[s+1]
        elif mqr[s].find('Request')>0:
            request = mqr[s]
        elif mqr[s].find('Response')>0:
            response = mqr[s]
    return methodname,request,response

def parseproto(filename):
    f = open(filename)
    line = f.readline()
    start = False
    global includelist
    global registlist
    while line:
        if (not start) and line.startswith('service'):
            service = getService(line)
            print(service)
            start = True
            includelist+='#include "cpp/'+filename[filename.rfind('/')+1:filename.find('.proto')]+'.pb.h"\n'
            registlist+= '\n\t\t//'+service+'\n'
        elif start and line.find('rpc')>0:
            if line.find(';')<0:
                line += f.readline()
            methodname,request,response=getMethod(line)
            registlist+='\t\tREGIST_MESSAGE_PARSER(%s,%s,%s)\n'%(methodname,request,response)
        line = f.readline()
    f.close()


protodir = sys.argv[1]
target = './parse_message_impl.cpp'
filelist = os.listdir(protodir);
for i in filelist:
    if not i.endswith('.proto'):
        continue
    path = os.path.join(protodir,i)
    if os.path.isfile(path):
        print(path)
        parseproto(path)

wfile=open(target,'w')
wfile.write('#include "parse_message.h"\n\n')
wfile.write(includelist)
wfile.write('\n')
wfile.write('#if __HAS_NAMESPACE_INFO__ \nusing namespace hbase::pb;\n#endif\n')
wfile.write('class parse_function_register{\npublic:\n\tparse_function_register(){\n')
wfile.write(registlist)
wfile.write('\t}\n};\nstatic parse_function_register regist;')
