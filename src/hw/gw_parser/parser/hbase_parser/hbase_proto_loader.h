/**
 * 用于显式加载不同hbase版本反序列化代码的动态库。
 * 获取统一的接口
 */

#ifndef __PROTO_PARSER_L7_HBASE_LIB_LOADER_H_
#define __PROTO_PARSER_L7_HBASE_LIB_LOADER_H_

#include <map>
#include <string>


#include "deserializer/parse_message.h"
#include "hbase_stream.h"

class hbase_proto_loader{
public:
    static hbase_proto_loader& instance(){
        static hbase_proto_loader loader;
        return loader;
    }
    ~hbase_proto_loader();
    void init();
    parse_functions get_parser_func(std::string version);
private:
    hbase_proto_loader(){
        init();
    }
    std::map<std::string,parse_functions> libs;
};

#endif   //__PROTO_PARSER_L7_HBASE_LIB_LOADER_H_