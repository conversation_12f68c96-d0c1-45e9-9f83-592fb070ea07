#include "hbase_stream.h"

#include "utils.h"
#include "cJSON.h"


hbase_stream::hbase_stream():has_read_connect_header(false),effective_user(""),service_name(""),client_version(""){}

bool hbase_stream::is_complelte(){
    if(response.call_id >= 0 && (response.call_id == request.call_id)){
        //忽略定时发送的这个请求
        if(request.request_method.compare("IsMasterRunning")==0){
            reset_info();
            return false;
        }
        return true;
    }
    return false;
}

void hbase_stream::to_event_json(cJSON *root){
    if(root == NULL){
        return;
    }

    // cJSON_AddStringToObject(root,"ip",client_ip.c_str());
    cJSON_AddStringToObject(root, "username", effective_user.c_str());
    cJSON_AddStringToObject(root, "version", client_version.c_str());
    cJSON_AddNumberToObject(root, "timestamp", request.tm);
    cJSON_AddStringToObject(root, "reqContent", request.request_content.c_str());
    cJSON_AddNumberToObject(root, "success", (response.exception_class_name.length()>0)?0:1);
    cJSON_AddStringToObject(root, "operation", request.request_method.c_str());
    cJSON_AddNumberToObject(root, "errCode", 0);
    std::string err_msg=response.exception_class_name + response.stack_trace;
    cJSON_AddStringToObject(root, "errMsg", err_msg.c_str());
    // cJSON_AddStringToObject(root, "serverIp", server_ip.c_str());
    cJSON_AddNumberToObject(root, "resourceType", RESOURCE_TYPE_HBASE);
    // cJSON_AddStringToObject(root,"serverVersion",client_version.c_str());
    cJSON_AddNumberToObject(root, "reqTime", response.tm-request.tm);
}

void hbase_stream::to_content_json(cJSON *root){
    if(root == NULL){
        return;
    }

    cJSON_AddStringToObject(root, "eventId", req_md5.c_str());
    cJSON_AddNumberToObject(root, "timestamp", request.tm);
    cJSON_AddStringToObject(root, "operation", request.request_method.c_str());
    cJSON_AddStringToObject(root, "responseBody", response.response_content.c_str());
    cJSON_AddNumberToObject(root, "resourceType", RESOURCE_TYPE_HBASE);

}