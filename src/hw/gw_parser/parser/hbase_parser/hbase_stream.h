
#ifndef __PARSER_HBASE_HBASESTREAM_H__
#define __PARSER_HBASE_HBASESTREAM_H__

#include <stdint.h>
#include <stdio.h>

#include <string>
#include <map>

namespace google{
    namespace protobuf{
        namespace io{
            class CodedInputStream;
        }
    }
}
struct cJSON;
typedef struct cJSON cJSON;


// namespace parser{
// namespace hbase{


class hbase_stream;
typedef cJSON* (*parse_message_func)(google::protobuf::io::CodedInputStream&,hbase_stream&);
struct hbase_request_info{
public:
    hbase_request_info():call_id(-1), tm(0), request_method(""), request_content(""){}
    void reset(){
        call_id = -1;
        tm = 0;
        request_method = "";
        request_content = "";
    }
    //请求内容
    int call_id;
    uint64_t tm;
    std::string request_method;
    std::string request_content;
};

struct hbase_response_info{
    hbase_response_info():call_id(-1),response_content(""),exception_class_name(""),stack_trace(""),hostname(""),port(""){}
    void reset(){
        call_id = -1;
        response_content = "";
        exception_class_name = "";
        stack_trace = "";
        hostname = "";
        port = "";
    }
    int call_id;
    uint64_t tm;
    //应答内容
    std::string response_content;
    //异常时服务端返回的异常信息
    std::string exception_class_name;
    std::string stack_trace;
    std::string hostname;
    std::string port;
};


typedef void* HANDLE;
struct parse_functions
{
    parse_functions():handle(NULL),p_parse_cell(NULL),p_parse_request_header(NULL),p_parse_request(NULL),p_parse_response_header(NULL),p_parse_response(NULL),p_parse_connect_header(NULL){}

    HANDLE handle;
    parse_message_func p_parse_cell;
    parse_message_func p_parse_request_header;
    parse_message_func p_parse_request;
    parse_message_func p_parse_response_header;
    parse_message_func p_parse_response;
    parse_message_func p_parse_connect_header;
};

struct hbase_stream{
public:
    hbase_stream();

    bool is_hbase;

    bool has_read_connect_header;

    // unsigned short client_port;
    // unsigned short server_port;
    // std::string client_ip;
    // std::string server_ip;
    std::string req_md5;

    //连接信息
    std::string effective_user;
    std::string service_name;

    //标记同一个scan的一系列请求，用于kafka路由，保证同一系列的请求放入同一个存储分片里。
    uint32_t scan_id;


    //连接建立时的客户端信息
    std::string client_version;

    //TODO： 假定hbase是一请求一应答的模式。上一个请求应答之前，下一个请求不会发送。
    //如果请求和应答分为两个序列，即：客户端不必等待上一个请求应答， 即可发送下一个请求。但应答的顺序，严格按照请求的顺序回复。
    //则需要一个队列缓存请求序列，当一个应答到达时，请求序列中应该有对应的请求id，小于该id的请求如果存在，则对应的应答在之前到达或丢失。
    //如果应答序列不保证顺序(后发送的请求由于速度差异有可能先应答)，必须缓存所有未应答的请求，但需要处理应答丢失的请求(如超时机制)
    std::map<int,hbase_request_info> unresponse_request;
    hbase_request_info request;
    hbase_response_info response;

    void reset_info(){
        request.reset();
        response.reset();
    }

    bool is_complelte();

    void to_event_json(cJSON *root);

    void to_content_json(cJSON *root);

    parse_functions functions;
};      //end of class hbase_stream


    
// }   //namespace hbase
// }   //namespace parser
#endif //__PARSER_HBASE_HBASESTREAM_H__