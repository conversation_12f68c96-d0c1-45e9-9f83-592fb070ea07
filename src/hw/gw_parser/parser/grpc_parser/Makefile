ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif

MKFILE_PATH :=$(abspath $(lastword $(MAKEFILE_LIST)))
MKFILE_DIR :=$(patsubst %/, %, $(dir $(MKFILE_PATH)))
MKFILE_DIR_STRIP :=$(strip $(MKFILE_DIR))
ROOT_DIR :=$(MKFILE_DIR_STRIP)/../..
ROOT_PATH :=$(ROOT_DIR)
CORE_PATH :=$(ROOT_DIR)/core/
ifeq ("$(BUILD_ARCH)", "x86")
PROTOBUF_DIR :=$(shell find / -name libprotobuf.a | grep /lib/libprotobuf.a)
endif

CFLAGS          =  -fvisibility=hidden  -fPIC -I. -g
CFLAGS			+= -I$(ROOT_DIR)/include
CFLAGS			+= -I$(ROOT_DIR)/utils/cjson/
CFLAGS			+= -I$(ROOT_DIR)/utils/
CFLAGS			+= -I$(ROOT_DIR)/utils/file_type
CFLAGS			+= -I$(ROOT_DIR)/liblicutils_c_sdk
CFLAGS			+= -I$(ROOT_DIR)/core
CFLAGS			+= -I$(ROOT_DIR)/libaws_api_c++/include

ifeq ("$(BUILD_ARCH)", "x86")
CFLAGS          += -I/usr/libiconv/include
else ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS          += -I/home/<USER>/3rd/iconv/include/
endif

CFLAGS			+= -I$(MKFILE_DIR_STRIP)/../http2_parser/brotli

ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS          += -I/home/<USER>/3rd/libpcap-1.9.1/ -I/home/<USER>/3rd/zlib/include/ -I/home/<USER>/3rd/protobuf/include/
endif

LDFLAGS         = -shared

ifeq ("$(BUILD_ARCH)", "x86")
LDFLAGS        += -lstdc++ -lz -lbrotli -lpthread -L$(MKFILE_DIR_STRIP)/../http2_parser/brotli -L$(ROOT_DIR)/utils/file_type/ -lfile_type
LDFLAGS        += -L/usr/libiconv/lib/
else ifeq ("$(BUILD_ARCH)", "ARM")
LDFLAGS        += -lstdc++ -L/home/<USER>/3rd/zlib/lib/ -lz -L/home/<USER>/3rd/oatpp/lib/ -lbrotli -lpthread -L$(ROOT_DIR)/utils/file_type/ -lfile_type -L/home/<USER>/3rd/protobuf/lib/ -lprotobuf
LDFLAGS        += -L/home/<USER>/3rd/iconv/lib/
endif

include ../../flags.make

O_FILES = grpc_parser.o
O_FILES += hpack_huffman.o hpack_table.o HPACK.o
O_FILES += http2_frame.o http2_frameReassembler.o http2_stream.o HTTP2.o
O_FILES += module_mgt_grpc_parser.o http2_parser_upstream_task_worker.o http2_parser_task_worker.o http2_parser_upstream.o
O_FILES += http2_parser_parser_msg.o
O_FILES += cJSON.o cJSON_Utils.o utils.o cpp_utils.o
O_FILES += ProtobufRawHttpEvent.pb.o

.PHONY: all clean


all: grpc_parser.so

%.o:%.cpp
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cpp_utils.o: ../../core/cpp_utils.cpp ../../include/cpp_utils.h
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

utils.o: ../../core/utils.c ../../include/utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON.o: ../.././utils/cjson/cJSON.c ../.././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON_Utils.o: ../.././utils/cjson/cJSON_Utils.c ../.././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

ifeq ("$(BUILD_ARCH)", "x86")
grpc_parser.so: $(O_FILES)
	$(CC) -o $@ $^ $(PROTOBUF_DIR) $(LDFLAGS) $(LIBS) $(LIB)
else ifeq ("$(BUILD_ARCH)", "ARM")
grpc_parser.so: $(O_FILES)
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)
endif

clean:
	rm -f *.o *~ grpc_parser.so
