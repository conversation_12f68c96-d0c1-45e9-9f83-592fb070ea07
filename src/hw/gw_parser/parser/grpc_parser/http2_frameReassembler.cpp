#include <vector>
#include <cstring>
#include "http2_frameReassembler.h"

using namespace analyzer;
using namespace analyzer::submodule;
using namespace std;

HTTP2_FrameReassembler::HTTP2_FrameReassembler()
{
    this->fragmentedPacket = false;
    this->buffer = nullptr;
    this->bufferLen = 0;
    this->bufferSize = MIN_BUFFER_SIZE;
    this->copyLen = 0;
}

HTTP2_FrameReassembler::~HTTP2_FrameReassembler()
{
    if(this->buffer)
    {
        delete[] this->buffer;
        this->buffer = nullptr;
    }

}

void HTTP2_FrameReassembler::resizeBuffer(uint32_t size)
{
    if(size <= MAX_BUFFER_SIZE)
    {
        if(size > this->bufferSize)
        {
            if(this->buffer)
            {
                delete[] this->buffer;
                this->buffer = new uint8_t[size];
            }
            this->bufferSize = size;
        }
    }

}

void HTTP2_FrameReassembler::allocateBuffer(void)
{
    if(!this->buffer)
    {
        this->buffer = new uint8_t[this->bufferSize];
        this->bufferLen = 0;
    }

}

void HTTP2_FrameReassembler::setBuffer(uint8_t *data,uint32_t len)
{
    this->allocateBuffer();
    memcpy(this->buffer,data,len);
    this->fragmentedPacket = true;
    this->bufferLen = len;
}

void HTTP2_FrameReassembler::appendBuffer(uint8_t* data, uint32_t len)
{
    memcpy(this->buffer+this->bufferLen,data,len);
    this->bufferLen+=len;
}

void HTTP2_FrameReassembler::clearBuffer(void)
{
    this->bufferLen = 0;
    this->fragmentedPacket = false;
}

HTTP2_Frame* HTTP2_FrameReassembler::loadFrame(HTTP2_FrameHeader* fh, uint8_t* payload, uint32_t len)
{
    HTTP2_Frame *frame = nullptr;

    switch(fh->getType())
    {
        case DATA_FRAME: 
            frame = new HTTP2_Data_Frame(fh,payload,len);
            break;
        case HEADERS_FRAME: 
            frame = new HTTP2_Header_Frame(fh, payload, len);
            break;
        case PRIORITY_FRAME: 
            frame = new HTTP2_Priority_Frame(fh, payload, len);
            break;
        case RST_STREAM_FRAME:
            frame = new HTTP2_RstStream_Frame(fh, payload, len);
            break;
        case SETTINGS_FRAME:
            frame = new HTTP2_Settings_Frame(fh, payload, len);
            break;
        case PUSH_PROMISE_FRAME:
            frame = new HTTP2_PushPromise_Frame(fh, payload, len);
            break;
        case PING_FRAME:
            frame = new HTTP2_Ping_Frame(fh, payload, len);
            break;
        case GOAWAY_FRAME:
            frame = new HTTP2_GoAway_Frame(fh, payload, len);
            break;
        case WINDOW_UPDATE_FRAME:
            frame = new HTTP2_WindowUpdate_Frame(fh, payload, len);
            break;
        case CONTINUATION_FRAME:
            frame = new HTTP2_Continuation_Frame(fh, payload, len);
            break;
        default:
            break;
    }

    if(frame && !frame->validate())
    {
        // the frame is invalid
        delete frame;
        frame = nullptr;
    }

    return frame;
}

std::vector<HTTP2_Frame*> HTTP2_FrameReassembler::process(const uint8_t* data, uint32_t len)
{
    std::vector<HTTP2_Frame*> frames;
    if(!data || len == 0)
    {
        return frames;
    }

    uint8_t* cursor =const_cast<uint8_t*>(data);
    uint32_t dataLen = len;

    while(dataLen > 0)
    {
        if(!this->fragmentedPacket)
        {
            // complete data, buffer is empty
            if(dataLen < FRAME_HEADER_LENGTH)
            {
                // too small
                this->setBuffer(cursor,dataLen);
                this->copyLen = 0;
                dataLen = 0;
            }
            else
            {
                HTTP2_FrameHeader *fh = new HTTP2_FrameHeader(cursor);
                uint32_t frameLen = FRAME_HEADER_LENGTH + fh->getLen();
                if(dataLen < frameLen)
                {
                    // fragmented
                    delete fh;
                    this->setBuffer(cursor,dataLen);
                    this->copyLen = frameLen - this->bufferLen; // remain data
                    dataLen = 0;
                }
                else
                {
                    // completed
                    HTTP2_Frame *frame = this->loadFrame(fh,cursor+FRAME_HEADER_LENGTH,fh->getLen());
                    frames.push_back(frame);
                    if(!frame)
                    {
                        // avoid break out and return immediately
                        break;
                    }
                    cursor+=frameLen;
                    dataLen-=frameLen;
                }
            }

        }
        else
        {
            // fragmented data, buffer is not empty
            if((this->bufferLen + dataLen) > this->bufferSize)
            {
                // too big
                frames.push_back(nullptr);
                break;
            }
            else
            {
                uint32_t oldBufferLen = this->bufferLen;
                if(this->copyLen == 0 || dataLen < this->copyLen)
                {
                    // unknown len to copy
                    this->appendBuffer(cursor,dataLen);
                }
                else
                {
                    this->appendBuffer(cursor,this->copyLen);
                }

                if(this->bufferLen < FRAME_HEADER_LENGTH)
                {
                    // still too small
                    dataLen = 0;
                }
                else
                {
                    HTTP2_FrameHeader *fh = new HTTP2_FrameHeader(this->buffer);
                    uint32_t frameLen = FRAME_HEADER_LENGTH + fh->getLen();
                    if(this->bufferLen < frameLen)
                    {
                        // fragmented
                        delete fh;
                        this->copyLen = frameLen - this->bufferLen;
                        dataLen = 0;
                    }
                    else
                    {
                        // completed
                        HTTP2_Frame *frame = this->loadFrame(fh,this->buffer+FRAME_HEADER_LENGTH,fh->getLen());
                        frames.push_back(frame);
                        if(!frame)
                        {
                            break;
                        }
                        if(frameLen <= oldBufferLen)
                        {
                            // twice check
                            frames.push_back(nullptr);
                            break;
                        }
                        cursor += frameLen - oldBufferLen;
                        dataLen -= frameLen - oldBufferLen;
                        this->clearBuffer();
                    }
                }
            }
        }
    }
    return frames;
}


