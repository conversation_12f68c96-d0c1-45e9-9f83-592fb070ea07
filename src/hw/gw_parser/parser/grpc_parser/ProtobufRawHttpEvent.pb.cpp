// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ProtobufRawHttpEvent.proto

#include "ProtobufRawHttpEvent.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {
PROTOBUF_CONSTEXPR ProtobufRawHttpEvent::ProtobufRawHttpEvent(
    ::_pbi::ConstantInitialized)
  : charset_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , req_(nullptr)
  , rsp_(nullptr)
  , meta_(nullptr)
  , net_(nullptr)
  , file_(nullptr)
  , uniqueid_(nullptr)
  , source_(nullptr){}
struct ProtobufRawHttpEventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtobufRawHttpEventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtobufRawHttpEventDefaultTypeInternal() {}
  union {
    ProtobufRawHttpEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtobufRawHttpEventDefaultTypeInternal _ProtobufRawHttpEvent_default_instance_;
PROTOBUF_CONSTEXPR Meta::Meta(
    ::_pbi::ConstantInitialized)
  : tm_(0){}
struct MetaDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MetaDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MetaDefaultTypeInternal() {}
  union {
    Meta _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MetaDefaultTypeInternal _Meta_default_instance_;
PROTOBUF_CONSTEXPR UniqueId::UniqueId(
    ::_pbi::ConstantInitialized)
  : eventid_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}){}
struct UniqueIdDefaultTypeInternal {
  PROTOBUF_CONSTEXPR UniqueIdDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~UniqueIdDefaultTypeInternal() {}
  union {
    UniqueId _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 UniqueIdDefaultTypeInternal _UniqueId_default_instance_;
PROTOBUF_CONSTEXPR HttpRequest_HeaderEntry_DoNotUse::HttpRequest_HeaderEntry_DoNotUse(
    ::_pbi::ConstantInitialized){}
struct HttpRequest_HeaderEntry_DoNotUseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HttpRequest_HeaderEntry_DoNotUseDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~HttpRequest_HeaderEntry_DoNotUseDefaultTypeInternal() {}
  union {
    HttpRequest_HeaderEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HttpRequest_HeaderEntry_DoNotUseDefaultTypeInternal _HttpRequest_HeaderEntry_DoNotUse_default_instance_;
PROTOBUF_CONSTEXPR HttpRequest::HttpRequest(
    ::_pbi::ConstantInitialized)
  : header_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , body_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , remoteaddr_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , url_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , httpversion_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , method_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , errcode_(0){}
struct HttpRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HttpRequestDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~HttpRequestDefaultTypeInternal() {}
  union {
    HttpRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HttpRequestDefaultTypeInternal _HttpRequest_default_instance_;
PROTOBUF_CONSTEXPR HttpResponse_HeaderEntry_DoNotUse::HttpResponse_HeaderEntry_DoNotUse(
    ::_pbi::ConstantInitialized){}
struct HttpResponse_HeaderEntry_DoNotUseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HttpResponse_HeaderEntry_DoNotUseDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~HttpResponse_HeaderEntry_DoNotUseDefaultTypeInternal() {}
  union {
    HttpResponse_HeaderEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HttpResponse_HeaderEntry_DoNotUseDefaultTypeInternal _HttpResponse_HeaderEntry_DoNotUse_default_instance_;
PROTOBUF_CONSTEXPR HttpResponse::HttpResponse(
    ::_pbi::ConstantInitialized)
  : header_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , setcookieslist_()
  , status_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , httpversion_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , body_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , errcode_(0){}
struct HttpResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HttpResponseDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~HttpResponseDefaultTypeInternal() {}
  union {
    HttpResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HttpResponseDefaultTypeInternal _HttpResponse_default_instance_;
PROTOBUF_CONSTEXPR Source::Source(
    ::_pbi::ConstantInitialized)
  : taskid_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , app_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , sourcetype_(0)
{}
struct SourceDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SourceDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SourceDefaultTypeInternal() {}
  union {
    Source _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SourceDefaultTypeInternal _Source_default_instance_;
PROTOBUF_CONSTEXPR RawFileInfo::RawFileInfo(
    ::_pbi::ConstantInitialized)
  : filedirection_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , filename_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , filetype_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , filewarn_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , uploaddir_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , sha256_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , miniobucket_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , rwflag_(0)
  , isincomplete_(0)
  , filelen_(int64_t{0})
  , filetypereliable_(0)
  , uploadflag_(0){}
struct RawFileInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RawFileInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RawFileInfoDefaultTypeInternal() {}
  union {
    RawFileInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RawFileInfoDefaultTypeInternal _RawFileInfo_default_instance_;
PROTOBUF_CONSTEXPR Net::Net(
    ::_pbi::ConstantInitialized)
  : srcip_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , dstip_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , flowsource_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , srcport_(0)
  , dstport_(0)
  , vlanid_(0){}
struct NetDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NetDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~NetDefaultTypeInternal() {}
  union {
    Net _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NetDefaultTypeInternal _Net_default_instance_;
}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com
static ::_pb::Metadata file_level_metadata_ProtobufRawHttpEvent_2eproto[10];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_ProtobufRawHttpEvent_2eproto[1];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_ProtobufRawHttpEvent_2eproto = nullptr;

const uint32_t TableStruct_ProtobufRawHttpEvent_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, req_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, rsp_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, meta_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, net_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, file_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, uniqueid_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, charset_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent, source_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, tm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::UniqueId, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::UniqueId, eventid_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, body_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, remoteaddr_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, url_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, httpversion_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, header_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, method_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpRequest, errcode_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, status_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, httpversion_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, header_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, body_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, setcookieslist_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::HttpResponse, errcode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, sourcetype_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, taskid_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, app_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, filedirection_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, rwflag_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, isincomplete_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, filename_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, filetype_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, filetypereliable_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, filelen_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, filewarn_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, uploadflag_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, uploaddir_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, sha256_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::RawFileInfo, miniobucket_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, srcip_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, srcport_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, dstip_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, dstport_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, flowsource_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, vlanid_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent)},
  { 14, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Meta)},
  { 21, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::UniqueId)},
  { 28, 36, -1, sizeof(::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse)},
  { 38, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::HttpRequest)},
  { 51, 59, -1, sizeof(::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse)},
  { 61, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::HttpResponse)},
  { 73, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Source)},
  { 82, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::RawFileInfo)},
  { 100, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Net)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::com::quanzhi::audit_core::common::model::_ProtobufRawHttpEvent_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Meta_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_UniqueId_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_HttpRequest_HeaderEntry_DoNotUse_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_HttpRequest_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_HttpResponse_HeaderEntry_DoNotUse_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_HttpResponse_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Source_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_RawFileInfo_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Net_default_instance_._instance,
};

const char descriptor_table_protodef_ProtobufRawHttpEvent_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032ProtobufRawHttpEvent.proto\022#com.quanzh"
  "i.audit_core.common.model\"\324\003\n\024ProtobufRa"
  "wHttpEvent\022=\n\003req\030\001 \001(\01320.com.quanzhi.au"
  "dit_core.common.model.HttpRequest\022>\n\003rsp"
  "\030\002 \001(\01321.com.quanzhi.audit_core.common.m"
  "odel.HttpResponse\0227\n\004meta\030\003 \001(\0132).com.qu"
  "anzhi.audit_core.common.model.Meta\0225\n\003ne"
  "t\030\004 \001(\0132(.com.quanzhi.audit_core.common."
  "model.Net\022>\n\004file\030\005 \001(\01320.com.quanzhi.au"
  "dit_core.common.model.RawFileInfo\022\?\n\010uni"
  "queId\030\006 \001(\0132-.com.quanzhi.audit_core.com"
  "mon.model.UniqueId\022\017\n\007charset\030\007 \001(\t\022;\n\006s"
  "ource\030\010 \001(\0132+.com.quanzhi.audit_core.com"
  "mon.model.Source\"\022\n\004Meta\022\n\n\002tm\030\001 \001(\001\"\033\n\010"
  "UniqueId\022\017\n\007eventId\030\001 \001(\t\"\357\001\n\013HttpReques"
  "t\022\014\n\004body\030\001 \001(\014\022\022\n\nremoteAddr\030\002 \001(\t\022\013\n\003u"
  "rl\030\003 \001(\t\022\023\n\013httpVersion\030\004 \001(\t\022L\n\006header\030"
  "\005 \003(\0132<.com.quanzhi.audit_core.common.mo"
  "del.HttpRequest.HeaderEntry\022\016\n\006method\030\006 "
  "\001(\t\022\017\n\007errCode\030\007 \001(\005\032-\n\013HeaderEntry\022\013\n\003k"
  "ey\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\350\001\n\014HttpResp"
  "onse\022\016\n\006status\030\001 \001(\t\022\023\n\013httpVersion\030\002 \001("
  "\t\022M\n\006header\030\003 \003(\0132=.com.quanzhi.audit_co"
  "re.common.model.HttpResponse.HeaderEntry"
  "\022\014\n\004body\030\004 \001(\014\022\026\n\016setCookiesList\030\005 \003(\014\022\017"
  "\n\007errCode\030\006 \001(\005\032-\n\013HeaderEntry\022\013\n\003key\030\001 "
  "\001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"n\n\006Source\022G\n\nsour"
  "ceType\030\001 \001(\01623.com.quanzhi.audit_core.co"
  "mmon.model.SourceTypeEnum\022\016\n\006taskId\030\002 \001("
  "\t\022\013\n\003app\030\003 \001(\t\"\367\001\n\013RawFileInfo\022\025\n\rfileDi"
  "rection\030\001 \001(\t\022\016\n\006rwFlag\030\002 \001(\005\022\024\n\014isIncom"
  "plete\030\003 \001(\005\022\020\n\010fileName\030\004 \001(\t\022\020\n\010fileTyp"
  "e\030\005 \001(\t\022\030\n\020fileTypeReliable\030\006 \001(\005\022\017\n\007fil"
  "eLen\030\007 \001(\003\022\020\n\010fileWarn\030\010 \001(\t\022\022\n\nuploadFl"
  "ag\030\t \001(\005\022\021\n\tuploadDir\030\n \001(\t\022\016\n\006sha256\030\013 "
  "\001(\t\022\023\n\013minioBucket\030\014 \001(\t\"i\n\003Net\022\r\n\005srcIp"
  "\030\001 \001(\t\022\017\n\007srcPort\030\002 \001(\005\022\r\n\005dstIp\030\003 \001(\t\022\017"
  "\n\007dstPort\030\004 \001(\005\022\022\n\nflowSource\030\005 \001(\t\022\016\n\006v"
  "lanId\030\006 \001(\005*\'\n\016SourceTypeEnum\022\013\n\007app_har"
  "\020\000\022\010\n\004flow\020\001B#B!ProtobufRawHttpEvent$$By"
  "JProtobufb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_ProtobufRawHttpEvent_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_ProtobufRawHttpEvent_2eproto = {
    false, false, 1617, descriptor_table_protodef_ProtobufRawHttpEvent_2eproto,
    "ProtobufRawHttpEvent.proto",
    &descriptor_table_ProtobufRawHttpEvent_2eproto_once, nullptr, 0, 10,
    schemas, file_default_instances, TableStruct_ProtobufRawHttpEvent_2eproto::offsets,
    file_level_metadata_ProtobufRawHttpEvent_2eproto, file_level_enum_descriptors_ProtobufRawHttpEvent_2eproto,
    file_level_service_descriptors_ProtobufRawHttpEvent_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_ProtobufRawHttpEvent_2eproto_getter() {
  return &descriptor_table_ProtobufRawHttpEvent_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_ProtobufRawHttpEvent_2eproto(&descriptor_table_ProtobufRawHttpEvent_2eproto);
namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SourceTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ProtobufRawHttpEvent_2eproto);
  return file_level_enum_descriptors_ProtobufRawHttpEvent_2eproto[0];
}
bool SourceTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ProtobufRawHttpEvent::_Internal {
 public:
  static const ::com::quanzhi::audit_core::common::model::HttpRequest& req(const ProtobufRawHttpEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::HttpResponse& rsp(const ProtobufRawHttpEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Meta& meta(const ProtobufRawHttpEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Net& net(const ProtobufRawHttpEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::RawFileInfo& file(const ProtobufRawHttpEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::UniqueId& uniqueid(const ProtobufRawHttpEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Source& source(const ProtobufRawHttpEvent* msg);
};

const ::com::quanzhi::audit_core::common::model::HttpRequest&
ProtobufRawHttpEvent::_Internal::req(const ProtobufRawHttpEvent* msg) {
  return *msg->req_;
}
const ::com::quanzhi::audit_core::common::model::HttpResponse&
ProtobufRawHttpEvent::_Internal::rsp(const ProtobufRawHttpEvent* msg) {
  return *msg->rsp_;
}
const ::com::quanzhi::audit_core::common::model::Meta&
ProtobufRawHttpEvent::_Internal::meta(const ProtobufRawHttpEvent* msg) {
  return *msg->meta_;
}
const ::com::quanzhi::audit_core::common::model::Net&
ProtobufRawHttpEvent::_Internal::net(const ProtobufRawHttpEvent* msg) {
  return *msg->net_;
}
const ::com::quanzhi::audit_core::common::model::RawFileInfo&
ProtobufRawHttpEvent::_Internal::file(const ProtobufRawHttpEvent* msg) {
  return *msg->file_;
}
const ::com::quanzhi::audit_core::common::model::UniqueId&
ProtobufRawHttpEvent::_Internal::uniqueid(const ProtobufRawHttpEvent* msg) {
  return *msg->uniqueid_;
}
const ::com::quanzhi::audit_core::common::model::Source&
ProtobufRawHttpEvent::_Internal::source(const ProtobufRawHttpEvent* msg) {
  return *msg->source_;
}
ProtobufRawHttpEvent::ProtobufRawHttpEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
}
ProtobufRawHttpEvent::ProtobufRawHttpEvent(const ProtobufRawHttpEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  charset_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    charset_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_charset().empty()) {
    charset_.Set(from._internal_charset(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_req()) {
    req_ = new ::com::quanzhi::audit_core::common::model::HttpRequest(*from.req_);
  } else {
    req_ = nullptr;
  }
  if (from._internal_has_rsp()) {
    rsp_ = new ::com::quanzhi::audit_core::common::model::HttpResponse(*from.rsp_);
  } else {
    rsp_ = nullptr;
  }
  if (from._internal_has_meta()) {
    meta_ = new ::com::quanzhi::audit_core::common::model::Meta(*from.meta_);
  } else {
    meta_ = nullptr;
  }
  if (from._internal_has_net()) {
    net_ = new ::com::quanzhi::audit_core::common::model::Net(*from.net_);
  } else {
    net_ = nullptr;
  }
  if (from._internal_has_file()) {
    file_ = new ::com::quanzhi::audit_core::common::model::RawFileInfo(*from.file_);
  } else {
    file_ = nullptr;
  }
  if (from._internal_has_uniqueid()) {
    uniqueid_ = new ::com::quanzhi::audit_core::common::model::UniqueId(*from.uniqueid_);
  } else {
    uniqueid_ = nullptr;
  }
  if (from._internal_has_source()) {
    source_ = new ::com::quanzhi::audit_core::common::model::Source(*from.source_);
  } else {
    source_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
}

inline void ProtobufRawHttpEvent::SharedCtor() {
charset_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  charset_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&req_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&source_) -
    reinterpret_cast<char*>(&req_)) + sizeof(source_));
}

ProtobufRawHttpEvent::~ProtobufRawHttpEvent() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ProtobufRawHttpEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  charset_.Destroy();
  if (this != internal_default_instance()) delete req_;
  if (this != internal_default_instance()) delete rsp_;
  if (this != internal_default_instance()) delete meta_;
  if (this != internal_default_instance()) delete net_;
  if (this != internal_default_instance()) delete file_;
  if (this != internal_default_instance()) delete uniqueid_;
  if (this != internal_default_instance()) delete source_;
}

void ProtobufRawHttpEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ProtobufRawHttpEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  charset_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && req_ != nullptr) {
    delete req_;
  }
  req_ = nullptr;
  if (GetArenaForAllocation() == nullptr && rsp_ != nullptr) {
    delete rsp_;
  }
  rsp_ = nullptr;
  if (GetArenaForAllocation() == nullptr && meta_ != nullptr) {
    delete meta_;
  }
  meta_ = nullptr;
  if (GetArenaForAllocation() == nullptr && net_ != nullptr) {
    delete net_;
  }
  net_ = nullptr;
  if (GetArenaForAllocation() == nullptr && file_ != nullptr) {
    delete file_;
  }
  file_ = nullptr;
  if (GetArenaForAllocation() == nullptr && uniqueid_ != nullptr) {
    delete uniqueid_;
  }
  uniqueid_ = nullptr;
  if (GetArenaForAllocation() == nullptr && source_ != nullptr) {
    delete source_;
  }
  source_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ProtobufRawHttpEvent::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .com.quanzhi.audit_core.common.model.HttpRequest req = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_req(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.HttpResponse rsp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_rsp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Meta meta = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_meta(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Net net = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_net(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.RawFileInfo file = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_file(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_uniqueid(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string charset = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_charset();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset"));
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Source source = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_source(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ProtobufRawHttpEvent::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .com.quanzhi.audit_core.common.model.HttpRequest req = 1;
  if (this->_internal_has_req()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::req(this),
        _Internal::req(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.HttpResponse rsp = 2;
  if (this->_internal_has_rsp()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::rsp(this),
        _Internal::rsp(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.Meta meta = 3;
  if (this->_internal_has_meta()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::meta(this),
        _Internal::meta(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.Net net = 4;
  if (this->_internal_has_net()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::net(this),
        _Internal::net(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.RawFileInfo file = 5;
  if (this->_internal_has_file()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::file(this),
        _Internal::file(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 6;
  if (this->_internal_has_uniqueid()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::uniqueid(this),
        _Internal::uniqueid(this).GetCachedSize(), target, stream);
  }

  // string charset = 7;
  if (!this->_internal_charset().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_charset().data(), static_cast<int>(this->_internal_charset().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_charset(), target);
  }

  // .com.quanzhi.audit_core.common.model.Source source = 8;
  if (this->_internal_has_source()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, _Internal::source(this),
        _Internal::source(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  return target;
}

size_t ProtobufRawHttpEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string charset = 7;
  if (!this->_internal_charset().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_charset());
  }

  // .com.quanzhi.audit_core.common.model.HttpRequest req = 1;
  if (this->_internal_has_req()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *req_);
  }

  // .com.quanzhi.audit_core.common.model.HttpResponse rsp = 2;
  if (this->_internal_has_rsp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rsp_);
  }

  // .com.quanzhi.audit_core.common.model.Meta meta = 3;
  if (this->_internal_has_meta()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *meta_);
  }

  // .com.quanzhi.audit_core.common.model.Net net = 4;
  if (this->_internal_has_net()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *net_);
  }

  // .com.quanzhi.audit_core.common.model.RawFileInfo file = 5;
  if (this->_internal_has_file()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *file_);
  }

  // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 6;
  if (this->_internal_has_uniqueid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *uniqueid_);
  }

  // .com.quanzhi.audit_core.common.model.Source source = 8;
  if (this->_internal_has_source()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *source_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ProtobufRawHttpEvent::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ProtobufRawHttpEvent::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ProtobufRawHttpEvent::GetClassData() const { return &_class_data_; }

void ProtobufRawHttpEvent::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ProtobufRawHttpEvent *>(to)->MergeFrom(
      static_cast<const ProtobufRawHttpEvent &>(from));
}


void ProtobufRawHttpEvent::MergeFrom(const ProtobufRawHttpEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_charset().empty()) {
    _internal_set_charset(from._internal_charset());
  }
  if (from._internal_has_req()) {
    _internal_mutable_req()->::com::quanzhi::audit_core::common::model::HttpRequest::MergeFrom(from._internal_req());
  }
  if (from._internal_has_rsp()) {
    _internal_mutable_rsp()->::com::quanzhi::audit_core::common::model::HttpResponse::MergeFrom(from._internal_rsp());
  }
  if (from._internal_has_meta()) {
    _internal_mutable_meta()->::com::quanzhi::audit_core::common::model::Meta::MergeFrom(from._internal_meta());
  }
  if (from._internal_has_net()) {
    _internal_mutable_net()->::com::quanzhi::audit_core::common::model::Net::MergeFrom(from._internal_net());
  }
  if (from._internal_has_file()) {
    _internal_mutable_file()->::com::quanzhi::audit_core::common::model::RawFileInfo::MergeFrom(from._internal_file());
  }
  if (from._internal_has_uniqueid()) {
    _internal_mutable_uniqueid()->::com::quanzhi::audit_core::common::model::UniqueId::MergeFrom(from._internal_uniqueid());
  }
  if (from._internal_has_source()) {
    _internal_mutable_source()->::com::quanzhi::audit_core::common::model::Source::MergeFrom(from._internal_source());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ProtobufRawHttpEvent::CopyFrom(const ProtobufRawHttpEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProtobufRawHttpEvent::IsInitialized() const {
  return true;
}

void ProtobufRawHttpEvent::InternalSwap(ProtobufRawHttpEvent* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &charset_, lhs_arena,
      &other->charset_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtobufRawHttpEvent, source_)
      + sizeof(ProtobufRawHttpEvent::source_)
      - PROTOBUF_FIELD_OFFSET(ProtobufRawHttpEvent, req_)>(
          reinterpret_cast<char*>(&req_),
          reinterpret_cast<char*>(&other->req_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ProtobufRawHttpEvent::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[0]);
}

// ===================================================================

class Meta::_Internal {
 public:
};

Meta::Meta(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Meta)
}
Meta::Meta(const Meta& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  tm_ = from.tm_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Meta)
}

inline void Meta::SharedCtor() {
tm_ = 0;
}

Meta::~Meta() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Meta)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Meta::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Meta::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Meta::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Meta)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tm_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Meta::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double tm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          tm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Meta::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Meta)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double tm = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_tm = this->_internal_tm();
  uint64_t raw_tm;
  memcpy(&raw_tm, &tmp_tm, sizeof(tmp_tm));
  if (raw_tm != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_tm(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Meta)
  return target;
}

size_t Meta::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Meta)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double tm = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_tm = this->_internal_tm();
  uint64_t raw_tm;
  memcpy(&raw_tm, &tmp_tm, sizeof(tmp_tm));
  if (raw_tm != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Meta::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Meta::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Meta::GetClassData() const { return &_class_data_; }

void Meta::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Meta *>(to)->MergeFrom(
      static_cast<const Meta &>(from));
}


void Meta::MergeFrom(const Meta& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Meta)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_tm = from._internal_tm();
  uint64_t raw_tm;
  memcpy(&raw_tm, &tmp_tm, sizeof(tmp_tm));
  if (raw_tm != 0) {
    _internal_set_tm(from._internal_tm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Meta::CopyFrom(const Meta& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Meta)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Meta::IsInitialized() const {
  return true;
}

void Meta::InternalSwap(Meta* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(tm_, other->tm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Meta::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[1]);
}

// ===================================================================

class UniqueId::_Internal {
 public:
};

UniqueId::UniqueId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.UniqueId)
}
UniqueId::UniqueId(const UniqueId& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  eventid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    eventid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_eventid().empty()) {
    eventid_.Set(from._internal_eventid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.UniqueId)
}

inline void UniqueId::SharedCtor() {
eventid_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  eventid_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

UniqueId::~UniqueId() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.UniqueId)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void UniqueId::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  eventid_.Destroy();
}

void UniqueId::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UniqueId::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.UniqueId)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  eventid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UniqueId::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string eventId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_eventid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.UniqueId.eventId"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UniqueId::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.UniqueId)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string eventId = 1;
  if (!this->_internal_eventid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_eventid().data(), static_cast<int>(this->_internal_eventid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.UniqueId.eventId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_eventid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.UniqueId)
  return target;
}

size_t UniqueId::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.UniqueId)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string eventId = 1;
  if (!this->_internal_eventid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_eventid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UniqueId::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UniqueId::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UniqueId::GetClassData() const { return &_class_data_; }

void UniqueId::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UniqueId *>(to)->MergeFrom(
      static_cast<const UniqueId &>(from));
}


void UniqueId::MergeFrom(const UniqueId& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.UniqueId)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_eventid().empty()) {
    _internal_set_eventid(from._internal_eventid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UniqueId::CopyFrom(const UniqueId& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.UniqueId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UniqueId::IsInitialized() const {
  return true;
}

void UniqueId::InternalSwap(UniqueId* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &eventid_, lhs_arena,
      &other->eventid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata UniqueId::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[2]);
}

// ===================================================================

HttpRequest_HeaderEntry_DoNotUse::HttpRequest_HeaderEntry_DoNotUse() {}
HttpRequest_HeaderEntry_DoNotUse::HttpRequest_HeaderEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void HttpRequest_HeaderEntry_DoNotUse::MergeFrom(const HttpRequest_HeaderEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata HttpRequest_HeaderEntry_DoNotUse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[3]);
}

// ===================================================================

class HttpRequest::_Internal {
 public:
};

HttpRequest::HttpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  header_(arena) {
  SharedCtor();
  if (arena != nullptr && !is_message_owned) {
    arena->OwnCustomDestructor(this, &HttpRequest::ArenaDtor);
  }
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.HttpRequest)
}
HttpRequest::HttpRequest(const HttpRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  header_.MergeFrom(from.header_);
  body_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    body_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_body().empty()) {
    body_.Set(from._internal_body(), 
      GetArenaForAllocation());
  }
  remoteaddr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    remoteaddr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_remoteaddr().empty()) {
    remoteaddr_.Set(from._internal_remoteaddr(), 
      GetArenaForAllocation());
  }
  url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    url_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_url().empty()) {
    url_.Set(from._internal_url(), 
      GetArenaForAllocation());
  }
  httpversion_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    httpversion_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_httpversion().empty()) {
    httpversion_.Set(from._internal_httpversion(), 
      GetArenaForAllocation());
  }
  method_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    method_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_method().empty()) {
    method_.Set(from._internal_method(), 
      GetArenaForAllocation());
  }
  errcode_ = from.errcode_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.HttpRequest)
}

inline void HttpRequest::SharedCtor() {
body_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  body_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
remoteaddr_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  remoteaddr_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
url_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  url_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
httpversion_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  httpversion_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
method_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  method_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
errcode_ = 0;
}

HttpRequest::~HttpRequest() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.HttpRequest)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    ArenaDtor(this);
    return;
  }
  SharedDtor();
}

inline void HttpRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  header_.Destruct();
  body_.Destroy();
  remoteaddr_.Destroy();
  url_.Destroy();
  httpversion_.Destroy();
  method_.Destroy();
}

void HttpRequest::ArenaDtor(void* object) {
  HttpRequest* _this = reinterpret_cast< HttpRequest* >(object);
  _this->header_.Destruct();
}
void HttpRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HttpRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.HttpRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  header_.Clear();
  body_.ClearToEmpty();
  remoteaddr_.ClearToEmpty();
  url_.ClearToEmpty();
  httpversion_.ClearToEmpty();
  method_.ClearToEmpty();
  errcode_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HttpRequest::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes body = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_body();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string remoteAddr = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_remoteaddr();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr"));
        } else
          goto handle_unusual;
        continue;
      // string url = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_url();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.HttpRequest.url"));
        } else
          goto handle_unusual;
        continue;
      // string httpVersion = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_httpversion();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.HttpRequest.httpVersion"));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> header = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&header_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string method = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_method();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.HttpRequest.method"));
        } else
          goto handle_unusual;
        continue;
      // int32 errCode = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          errcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HttpRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.HttpRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes body = 1;
  if (!this->_internal_body().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_body(), target);
  }

  // string remoteAddr = 2;
  if (!this->_internal_remoteaddr().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_remoteaddr().data(), static_cast<int>(this->_internal_remoteaddr().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_remoteaddr(), target);
  }

  // string url = 3;
  if (!this->_internal_url().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_url().data(), static_cast<int>(this->_internal_url().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.HttpRequest.url");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_url(), target);
  }

  // string httpVersion = 4;
  if (!this->_internal_httpversion().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_httpversion().data(), static_cast<int>(this->_internal_httpversion().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.HttpRequest.httpVersion");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_httpversion(), target);
  }

  // map<string, string> header = 5;
  if (!this->_internal_header().empty()) {
    using MapType = ::_pb::Map<std::string, std::string>;
    using WireHelper = HttpRequest_HeaderEntry_DoNotUse::Funcs;
    const auto& map_field = this->_internal_header();
    auto check_utf8 = [](const MapType::value_type& entry) {
      (void)entry;
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
        entry.first.data(), static_cast<int>(entry.first.length()),
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
        "com.quanzhi.audit_core.common.model.HttpRequest.HeaderEntry.key");
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
        entry.second.data(), static_cast<int>(entry.second.length()),
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
        "com.quanzhi.audit_core.common.model.HttpRequest.HeaderEntry.value");
    };

    if (stream->IsSerializationDeterministic() && map_field.size() > 1) {
      for (const auto& entry : ::_pbi::MapSorterPtr<MapType>(map_field)) {
        target = WireHelper::InternalSerialize(5, entry.first, entry.second, target, stream);
        check_utf8(entry);
      }
    } else {
      for (const auto& entry : map_field) {
        target = WireHelper::InternalSerialize(5, entry.first, entry.second, target, stream);
        check_utf8(entry);
      }
    }
  }

  // string method = 6;
  if (!this->_internal_method().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_method().data(), static_cast<int>(this->_internal_method().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.HttpRequest.method");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_method(), target);
  }

  // int32 errCode = 7;
  if (this->_internal_errcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(7, this->_internal_errcode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.HttpRequest)
  return target;
}

size_t HttpRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.HttpRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> header = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_header_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_header().begin();
      it != this->_internal_header().end(); ++it) {
    total_size += HttpRequest_HeaderEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // bytes body = 1;
  if (!this->_internal_body().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_body());
  }

  // string remoteAddr = 2;
  if (!this->_internal_remoteaddr().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_remoteaddr());
  }

  // string url = 3;
  if (!this->_internal_url().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_url());
  }

  // string httpVersion = 4;
  if (!this->_internal_httpversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_httpversion());
  }

  // string method = 6;
  if (!this->_internal_method().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_method());
  }

  // int32 errCode = 7;
  if (this->_internal_errcode() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_errcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HttpRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HttpRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HttpRequest::GetClassData() const { return &_class_data_; }

void HttpRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HttpRequest *>(to)->MergeFrom(
      static_cast<const HttpRequest &>(from));
}


void HttpRequest::MergeFrom(const HttpRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.HttpRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  header_.MergeFrom(from.header_);
  if (!from._internal_body().empty()) {
    _internal_set_body(from._internal_body());
  }
  if (!from._internal_remoteaddr().empty()) {
    _internal_set_remoteaddr(from._internal_remoteaddr());
  }
  if (!from._internal_url().empty()) {
    _internal_set_url(from._internal_url());
  }
  if (!from._internal_httpversion().empty()) {
    _internal_set_httpversion(from._internal_httpversion());
  }
  if (!from._internal_method().empty()) {
    _internal_set_method(from._internal_method());
  }
  if (from._internal_errcode() != 0) {
    _internal_set_errcode(from._internal_errcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HttpRequest::CopyFrom(const HttpRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.HttpRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HttpRequest::IsInitialized() const {
  return true;
}

void HttpRequest::InternalSwap(HttpRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  header_.InternalSwap(&other->header_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &body_, lhs_arena,
      &other->body_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &remoteaddr_, lhs_arena,
      &other->remoteaddr_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &url_, lhs_arena,
      &other->url_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &httpversion_, lhs_arena,
      &other->httpversion_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &method_, lhs_arena,
      &other->method_, rhs_arena
  );
  swap(errcode_, other->errcode_);
}

::PROTOBUF_NAMESPACE_ID::Metadata HttpRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[4]);
}

// ===================================================================

HttpResponse_HeaderEntry_DoNotUse::HttpResponse_HeaderEntry_DoNotUse() {}
HttpResponse_HeaderEntry_DoNotUse::HttpResponse_HeaderEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void HttpResponse_HeaderEntry_DoNotUse::MergeFrom(const HttpResponse_HeaderEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata HttpResponse_HeaderEntry_DoNotUse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[5]);
}

// ===================================================================

class HttpResponse::_Internal {
 public:
};

HttpResponse::HttpResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  header_(arena),
  setcookieslist_(arena) {
  SharedCtor();
  if (arena != nullptr && !is_message_owned) {
    arena->OwnCustomDestructor(this, &HttpResponse::ArenaDtor);
  }
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.HttpResponse)
}
HttpResponse::HttpResponse(const HttpResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      setcookieslist_(from.setcookieslist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  header_.MergeFrom(from.header_);
  status_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status().empty()) {
    status_.Set(from._internal_status(), 
      GetArenaForAllocation());
  }
  httpversion_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    httpversion_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_httpversion().empty()) {
    httpversion_.Set(from._internal_httpversion(), 
      GetArenaForAllocation());
  }
  body_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    body_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_body().empty()) {
    body_.Set(from._internal_body(), 
      GetArenaForAllocation());
  }
  errcode_ = from.errcode_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.HttpResponse)
}

inline void HttpResponse::SharedCtor() {
status_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
httpversion_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  httpversion_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
body_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  body_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
errcode_ = 0;
}

HttpResponse::~HttpResponse() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.HttpResponse)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    ArenaDtor(this);
    return;
  }
  SharedDtor();
}

inline void HttpResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  header_.Destruct();
  status_.Destroy();
  httpversion_.Destroy();
  body_.Destroy();
}

void HttpResponse::ArenaDtor(void* object) {
  HttpResponse* _this = reinterpret_cast< HttpResponse* >(object);
  _this->header_.Destruct();
}
void HttpResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HttpResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.HttpResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  header_.Clear();
  setcookieslist_.Clear();
  status_.ClearToEmpty();
  httpversion_.ClearToEmpty();
  body_.ClearToEmpty();
  errcode_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HttpResponse::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string status = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_status();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.HttpResponse.status"));
        } else
          goto handle_unusual;
        continue;
      // string httpVersion = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_httpversion();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.HttpResponse.httpVersion"));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> header = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&header_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bytes body = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_body();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bytes setCookiesList = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_setcookieslist();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 errCode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          errcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HttpResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.HttpResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string status = 1;
  if (!this->_internal_status().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status().data(), static_cast<int>(this->_internal_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.HttpResponse.status");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_status(), target);
  }

  // string httpVersion = 2;
  if (!this->_internal_httpversion().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_httpversion().data(), static_cast<int>(this->_internal_httpversion().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.HttpResponse.httpVersion");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_httpversion(), target);
  }

  // map<string, string> header = 3;
  if (!this->_internal_header().empty()) {
    using MapType = ::_pb::Map<std::string, std::string>;
    using WireHelper = HttpResponse_HeaderEntry_DoNotUse::Funcs;
    const auto& map_field = this->_internal_header();
    auto check_utf8 = [](const MapType::value_type& entry) {
      (void)entry;
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
        entry.first.data(), static_cast<int>(entry.first.length()),
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
        "com.quanzhi.audit_core.common.model.HttpResponse.HeaderEntry.key");
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
        entry.second.data(), static_cast<int>(entry.second.length()),
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
        "com.quanzhi.audit_core.common.model.HttpResponse.HeaderEntry.value");
    };

    if (stream->IsSerializationDeterministic() && map_field.size() > 1) {
      for (const auto& entry : ::_pbi::MapSorterPtr<MapType>(map_field)) {
        target = WireHelper::InternalSerialize(3, entry.first, entry.second, target, stream);
        check_utf8(entry);
      }
    } else {
      for (const auto& entry : map_field) {
        target = WireHelper::InternalSerialize(3, entry.first, entry.second, target, stream);
        check_utf8(entry);
      }
    }
  }

  // bytes body = 4;
  if (!this->_internal_body().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_body(), target);
  }

  // repeated bytes setCookiesList = 5;
  for (int i = 0, n = this->_internal_setcookieslist_size(); i < n; i++) {
    const auto& s = this->_internal_setcookieslist(i);
    target = stream->WriteBytes(5, s, target);
  }

  // int32 errCode = 6;
  if (this->_internal_errcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_errcode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.HttpResponse)
  return target;
}

size_t HttpResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.HttpResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> header = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_header_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_header().begin();
      it != this->_internal_header().end(); ++it) {
    total_size += HttpResponse_HeaderEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated bytes setCookiesList = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(setcookieslist_.size());
  for (int i = 0, n = setcookieslist_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      setcookieslist_.Get(i));
  }

  // string status = 1;
  if (!this->_internal_status().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status());
  }

  // string httpVersion = 2;
  if (!this->_internal_httpversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_httpversion());
  }

  // bytes body = 4;
  if (!this->_internal_body().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_body());
  }

  // int32 errCode = 6;
  if (this->_internal_errcode() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_errcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HttpResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HttpResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HttpResponse::GetClassData() const { return &_class_data_; }

void HttpResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HttpResponse *>(to)->MergeFrom(
      static_cast<const HttpResponse &>(from));
}


void HttpResponse::MergeFrom(const HttpResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.HttpResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  header_.MergeFrom(from.header_);
  setcookieslist_.MergeFrom(from.setcookieslist_);
  if (!from._internal_status().empty()) {
    _internal_set_status(from._internal_status());
  }
  if (!from._internal_httpversion().empty()) {
    _internal_set_httpversion(from._internal_httpversion());
  }
  if (!from._internal_body().empty()) {
    _internal_set_body(from._internal_body());
  }
  if (from._internal_errcode() != 0) {
    _internal_set_errcode(from._internal_errcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HttpResponse::CopyFrom(const HttpResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.HttpResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HttpResponse::IsInitialized() const {
  return true;
}

void HttpResponse::InternalSwap(HttpResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  header_.InternalSwap(&other->header_);
  setcookieslist_.InternalSwap(&other->setcookieslist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &status_, lhs_arena,
      &other->status_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &httpversion_, lhs_arena,
      &other->httpversion_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &body_, lhs_arena,
      &other->body_, rhs_arena
  );
  swap(errcode_, other->errcode_);
}

::PROTOBUF_NAMESPACE_ID::Metadata HttpResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[6]);
}

// ===================================================================

class Source::_Internal {
 public:
};

Source::Source(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Source)
}
Source::Source(const Source& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  taskid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    taskid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_taskid().empty()) {
    taskid_.Set(from._internal_taskid(), 
      GetArenaForAllocation());
  }
  app_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    app_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_app().empty()) {
    app_.Set(from._internal_app(), 
      GetArenaForAllocation());
  }
  sourcetype_ = from.sourcetype_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Source)
}

inline void Source::SharedCtor() {
taskid_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  taskid_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
app_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  app_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
sourcetype_ = 0;
}

Source::~Source() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Source)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Source::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  taskid_.Destroy();
  app_.Destroy();
}

void Source::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Source::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Source)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  taskid_.ClearToEmpty();
  app_.ClearToEmpty();
  sourcetype_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Source::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_sourcetype(static_cast<::com::quanzhi::audit_core::common::model::SourceTypeEnum>(val));
        } else
          goto handle_unusual;
        continue;
      // string taskId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_taskid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Source.taskId"));
        } else
          goto handle_unusual;
        continue;
      // string app = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_app();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Source.app"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Source::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Source)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
  if (this->_internal_sourcetype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_sourcetype(), target);
  }

  // string taskId = 2;
  if (!this->_internal_taskid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_taskid().data(), static_cast<int>(this->_internal_taskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Source.taskId");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_taskid(), target);
  }

  // string app = 3;
  if (!this->_internal_app().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_app().data(), static_cast<int>(this->_internal_app().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Source.app");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_app(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Source)
  return target;
}

size_t Source::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Source)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string taskId = 2;
  if (!this->_internal_taskid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_taskid());
  }

  // string app = 3;
  if (!this->_internal_app().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_app());
  }

  // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
  if (this->_internal_sourcetype() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_sourcetype());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Source::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Source::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Source::GetClassData() const { return &_class_data_; }

void Source::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Source *>(to)->MergeFrom(
      static_cast<const Source &>(from));
}


void Source::MergeFrom(const Source& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Source)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_taskid().empty()) {
    _internal_set_taskid(from._internal_taskid());
  }
  if (!from._internal_app().empty()) {
    _internal_set_app(from._internal_app());
  }
  if (from._internal_sourcetype() != 0) {
    _internal_set_sourcetype(from._internal_sourcetype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Source::CopyFrom(const Source& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Source)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Source::IsInitialized() const {
  return true;
}

void Source::InternalSwap(Source* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &taskid_, lhs_arena,
      &other->taskid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &app_, lhs_arena,
      &other->app_, rhs_arena
  );
  swap(sourcetype_, other->sourcetype_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Source::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[7]);
}

// ===================================================================

class RawFileInfo::_Internal {
 public:
};

RawFileInfo::RawFileInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.RawFileInfo)
}
RawFileInfo::RawFileInfo(const RawFileInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  filedirection_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    filedirection_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_filedirection().empty()) {
    filedirection_.Set(from._internal_filedirection(), 
      GetArenaForAllocation());
  }
  filename_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    filename_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_filename().empty()) {
    filename_.Set(from._internal_filename(), 
      GetArenaForAllocation());
  }
  filetype_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    filetype_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_filetype().empty()) {
    filetype_.Set(from._internal_filetype(), 
      GetArenaForAllocation());
  }
  filewarn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    filewarn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_filewarn().empty()) {
    filewarn_.Set(from._internal_filewarn(), 
      GetArenaForAllocation());
  }
  uploaddir_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uploaddir_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uploaddir().empty()) {
    uploaddir_.Set(from._internal_uploaddir(), 
      GetArenaForAllocation());
  }
  sha256_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    sha256_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sha256().empty()) {
    sha256_.Set(from._internal_sha256(), 
      GetArenaForAllocation());
  }
  miniobucket_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    miniobucket_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_miniobucket().empty()) {
    miniobucket_.Set(from._internal_miniobucket(), 
      GetArenaForAllocation());
  }
  ::memcpy(&rwflag_, &from.rwflag_,
    static_cast<size_t>(reinterpret_cast<char*>(&uploadflag_) -
    reinterpret_cast<char*>(&rwflag_)) + sizeof(uploadflag_));
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.RawFileInfo)
}

inline void RawFileInfo::SharedCtor() {
filedirection_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  filedirection_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
filename_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  filename_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
filetype_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  filetype_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
filewarn_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  filewarn_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
uploaddir_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uploaddir_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
sha256_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  sha256_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
miniobucket_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  miniobucket_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&rwflag_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&uploadflag_) -
    reinterpret_cast<char*>(&rwflag_)) + sizeof(uploadflag_));
}

RawFileInfo::~RawFileInfo() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.RawFileInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RawFileInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  filedirection_.Destroy();
  filename_.Destroy();
  filetype_.Destroy();
  filewarn_.Destroy();
  uploaddir_.Destroy();
  sha256_.Destroy();
  miniobucket_.Destroy();
}

void RawFileInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RawFileInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.RawFileInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  filedirection_.ClearToEmpty();
  filename_.ClearToEmpty();
  filetype_.ClearToEmpty();
  filewarn_.ClearToEmpty();
  uploaddir_.ClearToEmpty();
  sha256_.ClearToEmpty();
  miniobucket_.ClearToEmpty();
  ::memset(&rwflag_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&uploadflag_) -
      reinterpret_cast<char*>(&rwflag_)) + sizeof(uploadflag_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RawFileInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string fileDirection = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_filedirection();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection"));
        } else
          goto handle_unusual;
        continue;
      // int32 rwFlag = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          rwflag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isIncomplete = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          isincomplete_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string fileName = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_filename();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.fileName"));
        } else
          goto handle_unusual;
        continue;
      // string fileType = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_filetype();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.fileType"));
        } else
          goto handle_unusual;
        continue;
      // int32 fileTypeReliable = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          filetypereliable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 fileLen = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          filelen_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string fileWarn = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_filewarn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn"));
        } else
          goto handle_unusual;
        continue;
      // int32 uploadFlag = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          uploadflag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string uploadDir = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_uploaddir();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir"));
        } else
          goto handle_unusual;
        continue;
      // string sha256 = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_sha256();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.sha256"));
        } else
          goto handle_unusual;
        continue;
      // string minioBucket = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_miniobucket();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.RawFileInfo.minioBucket"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RawFileInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.RawFileInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string fileDirection = 1;
  if (!this->_internal_filedirection().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_filedirection().data(), static_cast<int>(this->_internal_filedirection().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_filedirection(), target);
  }

  // int32 rwFlag = 2;
  if (this->_internal_rwflag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_rwflag(), target);
  }

  // int32 isIncomplete = 3;
  if (this->_internal_isincomplete() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_isincomplete(), target);
  }

  // string fileName = 4;
  if (!this->_internal_filename().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_filename().data(), static_cast<int>(this->_internal_filename().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.fileName");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_filename(), target);
  }

  // string fileType = 5;
  if (!this->_internal_filetype().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_filetype().data(), static_cast<int>(this->_internal_filetype().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.fileType");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_filetype(), target);
  }

  // int32 fileTypeReliable = 6;
  if (this->_internal_filetypereliable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_filetypereliable(), target);
  }

  // int64 fileLen = 7;
  if (this->_internal_filelen() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(7, this->_internal_filelen(), target);
  }

  // string fileWarn = 8;
  if (!this->_internal_filewarn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_filewarn().data(), static_cast<int>(this->_internal_filewarn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_filewarn(), target);
  }

  // int32 uploadFlag = 9;
  if (this->_internal_uploadflag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(9, this->_internal_uploadflag(), target);
  }

  // string uploadDir = 10;
  if (!this->_internal_uploaddir().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uploaddir().data(), static_cast<int>(this->_internal_uploaddir().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_uploaddir(), target);
  }

  // string sha256 = 11;
  if (!this->_internal_sha256().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sha256().data(), static_cast<int>(this->_internal_sha256().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.sha256");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_sha256(), target);
  }

  // string minioBucket = 12;
  if (!this->_internal_miniobucket().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_miniobucket().data(), static_cast<int>(this->_internal_miniobucket().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.RawFileInfo.minioBucket");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_miniobucket(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.RawFileInfo)
  return target;
}

size_t RawFileInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.RawFileInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string fileDirection = 1;
  if (!this->_internal_filedirection().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_filedirection());
  }

  // string fileName = 4;
  if (!this->_internal_filename().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_filename());
  }

  // string fileType = 5;
  if (!this->_internal_filetype().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_filetype());
  }

  // string fileWarn = 8;
  if (!this->_internal_filewarn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_filewarn());
  }

  // string uploadDir = 10;
  if (!this->_internal_uploaddir().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uploaddir());
  }

  // string sha256 = 11;
  if (!this->_internal_sha256().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sha256());
  }

  // string minioBucket = 12;
  if (!this->_internal_miniobucket().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_miniobucket());
  }

  // int32 rwFlag = 2;
  if (this->_internal_rwflag() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_rwflag());
  }

  // int32 isIncomplete = 3;
  if (this->_internal_isincomplete() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_isincomplete());
  }

  // int64 fileLen = 7;
  if (this->_internal_filelen() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_filelen());
  }

  // int32 fileTypeReliable = 6;
  if (this->_internal_filetypereliable() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_filetypereliable());
  }

  // int32 uploadFlag = 9;
  if (this->_internal_uploadflag() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_uploadflag());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RawFileInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RawFileInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RawFileInfo::GetClassData() const { return &_class_data_; }

void RawFileInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RawFileInfo *>(to)->MergeFrom(
      static_cast<const RawFileInfo &>(from));
}


void RawFileInfo::MergeFrom(const RawFileInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.RawFileInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_filedirection().empty()) {
    _internal_set_filedirection(from._internal_filedirection());
  }
  if (!from._internal_filename().empty()) {
    _internal_set_filename(from._internal_filename());
  }
  if (!from._internal_filetype().empty()) {
    _internal_set_filetype(from._internal_filetype());
  }
  if (!from._internal_filewarn().empty()) {
    _internal_set_filewarn(from._internal_filewarn());
  }
  if (!from._internal_uploaddir().empty()) {
    _internal_set_uploaddir(from._internal_uploaddir());
  }
  if (!from._internal_sha256().empty()) {
    _internal_set_sha256(from._internal_sha256());
  }
  if (!from._internal_miniobucket().empty()) {
    _internal_set_miniobucket(from._internal_miniobucket());
  }
  if (from._internal_rwflag() != 0) {
    _internal_set_rwflag(from._internal_rwflag());
  }
  if (from._internal_isincomplete() != 0) {
    _internal_set_isincomplete(from._internal_isincomplete());
  }
  if (from._internal_filelen() != 0) {
    _internal_set_filelen(from._internal_filelen());
  }
  if (from._internal_filetypereliable() != 0) {
    _internal_set_filetypereliable(from._internal_filetypereliable());
  }
  if (from._internal_uploadflag() != 0) {
    _internal_set_uploadflag(from._internal_uploadflag());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RawFileInfo::CopyFrom(const RawFileInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.RawFileInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RawFileInfo::IsInitialized() const {
  return true;
}

void RawFileInfo::InternalSwap(RawFileInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &filedirection_, lhs_arena,
      &other->filedirection_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &filename_, lhs_arena,
      &other->filename_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &filetype_, lhs_arena,
      &other->filetype_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &filewarn_, lhs_arena,
      &other->filewarn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &uploaddir_, lhs_arena,
      &other->uploaddir_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &sha256_, lhs_arena,
      &other->sha256_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &miniobucket_, lhs_arena,
      &other->miniobucket_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RawFileInfo, uploadflag_)
      + sizeof(RawFileInfo::uploadflag_)
      - PROTOBUF_FIELD_OFFSET(RawFileInfo, rwflag_)>(
          reinterpret_cast<char*>(&rwflag_),
          reinterpret_cast<char*>(&other->rwflag_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RawFileInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[8]);
}

// ===================================================================

class Net::_Internal {
 public:
};

Net::Net(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Net)
}
Net::Net(const Net& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  srcip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    srcip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_srcip().empty()) {
    srcip_.Set(from._internal_srcip(), 
      GetArenaForAllocation());
  }
  dstip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    dstip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_dstip().empty()) {
    dstip_.Set(from._internal_dstip(), 
      GetArenaForAllocation());
  }
  flowsource_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    flowsource_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_flowsource().empty()) {
    flowsource_.Set(from._internal_flowsource(), 
      GetArenaForAllocation());
  }
  ::memcpy(&srcport_, &from.srcport_,
    static_cast<size_t>(reinterpret_cast<char*>(&vlanid_) -
    reinterpret_cast<char*>(&srcport_)) + sizeof(vlanid_));
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Net)
}

inline void Net::SharedCtor() {
srcip_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  srcip_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
dstip_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  dstip_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
flowsource_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  flowsource_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&srcport_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&vlanid_) -
    reinterpret_cast<char*>(&srcport_)) + sizeof(vlanid_));
}

Net::~Net() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Net)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Net::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  srcip_.Destroy();
  dstip_.Destroy();
  flowsource_.Destroy();
}

void Net::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Net::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Net)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  srcip_.ClearToEmpty();
  dstip_.ClearToEmpty();
  flowsource_.ClearToEmpty();
  ::memset(&srcport_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&vlanid_) -
      reinterpret_cast<char*>(&srcport_)) + sizeof(vlanid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Net::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string srcIp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_srcip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Net.srcIp"));
        } else
          goto handle_unusual;
        continue;
      // int32 srcPort = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          srcport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string dstIp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_dstip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Net.dstIp"));
        } else
          goto handle_unusual;
        continue;
      // int32 dstPort = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          dstport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string flowSource = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_flowsource();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Net.flowSource"));
        } else
          goto handle_unusual;
        continue;
      // int32 vlanId = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          vlanid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Net::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Net)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string srcIp = 1;
  if (!this->_internal_srcip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_srcip().data(), static_cast<int>(this->_internal_srcip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Net.srcIp");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_srcip(), target);
  }

  // int32 srcPort = 2;
  if (this->_internal_srcport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_srcport(), target);
  }

  // string dstIp = 3;
  if (!this->_internal_dstip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_dstip().data(), static_cast<int>(this->_internal_dstip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Net.dstIp");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_dstip(), target);
  }

  // int32 dstPort = 4;
  if (this->_internal_dstport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_dstport(), target);
  }

  // string flowSource = 5;
  if (!this->_internal_flowsource().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_flowsource().data(), static_cast<int>(this->_internal_flowsource().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Net.flowSource");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_flowsource(), target);
  }

  // int32 vlanId = 6;
  if (this->_internal_vlanid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_vlanid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Net)
  return target;
}

size_t Net::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Net)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string srcIp = 1;
  if (!this->_internal_srcip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_srcip());
  }

  // string dstIp = 3;
  if (!this->_internal_dstip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dstip());
  }

  // string flowSource = 5;
  if (!this->_internal_flowsource().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_flowsource());
  }

  // int32 srcPort = 2;
  if (this->_internal_srcport() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_srcport());
  }

  // int32 dstPort = 4;
  if (this->_internal_dstport() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_dstport());
  }

  // int32 vlanId = 6;
  if (this->_internal_vlanid() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_vlanid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Net::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Net::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Net::GetClassData() const { return &_class_data_; }

void Net::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Net *>(to)->MergeFrom(
      static_cast<const Net &>(from));
}


void Net::MergeFrom(const Net& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Net)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_srcip().empty()) {
    _internal_set_srcip(from._internal_srcip());
  }
  if (!from._internal_dstip().empty()) {
    _internal_set_dstip(from._internal_dstip());
  }
  if (!from._internal_flowsource().empty()) {
    _internal_set_flowsource(from._internal_flowsource());
  }
  if (from._internal_srcport() != 0) {
    _internal_set_srcport(from._internal_srcport());
  }
  if (from._internal_dstport() != 0) {
    _internal_set_dstport(from._internal_dstport());
  }
  if (from._internal_vlanid() != 0) {
    _internal_set_vlanid(from._internal_vlanid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Net::CopyFrom(const Net& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Net)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Net::IsInitialized() const {
  return true;
}

void Net::InternalSwap(Net* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &srcip_, lhs_arena,
      &other->srcip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &dstip_, lhs_arena,
      &other->dstip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &flowsource_, lhs_arena,
      &other->flowsource_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Net, vlanid_)
      + sizeof(Net::vlanid_)
      - PROTOBUF_FIELD_OFFSET(Net, srcport_)>(
          reinterpret_cast<char*>(&srcport_),
          reinterpret_cast<char*>(&other->srcport_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Net::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawHttpEvent_2eproto_getter, &descriptor_table_ProtobufRawHttpEvent_2eproto_once,
      file_level_metadata_ProtobufRawHttpEvent_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Meta*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Meta >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Meta >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::UniqueId*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::UniqueId >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::UniqueId >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::HttpRequest*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::HttpRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::HttpRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::HttpResponse*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::HttpResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::HttpResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Source*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Source >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Source >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::RawFileInfo*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::RawFileInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::RawFileInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Net*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Net >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Net >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
