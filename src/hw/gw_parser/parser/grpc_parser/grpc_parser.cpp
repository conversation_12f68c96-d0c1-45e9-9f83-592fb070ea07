/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <inttypes.h>
#include <arpa/inet.h>
#include <string>

#include "grpc_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "task_worker.h"

#include "gw_stats.h"
#include "display_stats_define.h"
#include "gw_license.h"
#include "gw_i_upload.h"
#include "nacos_listen_conf.h"
#include "ProtobufRawHttpEvent.pb.h"

//////


#define HTTP_PARSER_TOTAL "http2"
#define HTTP_PARSER_REQ_FAILED "request"
#define HTTP_PARSER_RSP_FAILED "response"
#define HTTP_PARSER_BYTES "http2 parser bytes"
#define HTTP_MATCH_TRUNK_FAILED "trunk failed"
#define HTTP_MATCH_TRUNK_TOO_LARGE "trunk too large"
#define HTTP_MATCH_UNZIP_FAILED "unzip failed"
#define HTTP_MATCH_PAYLOAD_TOO_LARGE "payload too large"
#define HTTP_MATCH_PACKET_LOST "packet lost"
#define HTTP_MATCH_SUC_BYTES "suc bytes"



static void clearUploadHttp2Info(analyzer::submodule::upload_http2_info_t &st_upload_http2_info)
{
  memset(&st_upload_http2_info.http2_req_info.remote_addr,0,sizeof(st_upload_http2_info.http2_req_info.remote_addr));
  st_upload_http2_info.http2_req_info.method.clear(); 
  st_upload_http2_info.http2_req_info.full_url.clear();
  st_upload_http2_info.http2_req_info.header.clear();
  st_upload_http2_info.http2_req_info.body.clear();
  st_upload_http2_info.http2_req_info.key.clear();
  st_upload_http2_info.http2_req_info.value.clear();

  memset(&st_upload_http2_info.http2_rsp_info.status_code,0,sizeof(st_upload_http2_info.http2_rsp_info.status_code));
  st_upload_http2_info.http2_rsp_info.header.clear();
  st_upload_http2_info.http2_rsp_info.body.clear();
  st_upload_http2_info.http2_rsp_info.key.clear();
  st_upload_http2_info.http2_rsp_info.value.clear();

  memset(&st_upload_http2_info.http2_meta_info.ts,0,sizeof(st_upload_http2_info.http2_meta_info.ts));

  memset(&st_upload_http2_info.http2_net_info,0,sizeof(st_upload_http2_info.http2_net_info));

  memset(&st_upload_http2_info.a_unique_id,0,sizeof(st_upload_http2_info.a_unique_id));
}

/**
 * CGrpcParser implementation
 */
CGrpcParser::CGrpcParser(void) : m_comm(NULL)
                              , m_quit_signal(0)
                              , m_name{0}
                              , m_stats_http2{0}
                              // , m_stats_url_fwd{0}
                              , m_p_wq{0}
                              , m_p_tw{0}
                              , m_conf_http_parser_queue_max_num(10000)
                              , m_conf_http_parser_queue_memory_max_size_bytes(100 * 1024ULL * 1024ULL)
                              , m_conf_http_gzip_mode(3)
                              , m_conf_run_mode(0) 
                              , m_conf_http_parser_mode(1)
                              , m_conf_http_url_show_size(256)
                              , m_conf_http_request_body_max_size(1 * 1024 * 1024L)
                              , m_conf_http_response_body_max_size(1 * 1024 * 1024L)
                              , m_conf_http_parser_thread_num(2)
                              , m_conf_pcap_timestamp(1)
                              , m_conf_upload_mode(3)
                              , m_conf_http_body_show_size(2048)
                              , m_u32_http_gzip_deep(0)
                              , m_i_insert_body_flag(1)
                              , m_i_insert_original_req_body(0)
                              , m_conf_upload_name("log")
                              , m_conf_http_gzip_thread_num(1)
                              , m_conf_http_gzip_queue_max_num(1000)
                              , m_conf_http_gzip_queue_memory_max_size_bytes(100 * 1024ULL * 1024ULL)
                              , m_conf_http_upstream_thread_num(1)
                              , m_conf_http_upstream_queue_max_num(1000)
                              , m_conf_http_upstream_queue_memory_max_size_bytes(100 * 1024ULL * 1024ULL)
                              , m_conf_multi_queue_forward_enable(0)
                              , m_str_gw_ip("127.0.0.1")
                              , m_i_fast_message(0)
                              , m_conf_upstream(0)
                              , m_urlfilter_rule(NULL)
                              , m_accoutfilter_rule(NULL)
                              , m_upload_client_ipfilter_rule(NULL)
                              , m_upload_server_ipfilter_rule(NULL)
                              , m_conf_http_pipeline_mode(1) 
                              , m_conf_http_header_min_size(64)   
                              , m_conf_http_gzip_parser_thread_num(1)
                              , m_conf_http_gzip_parser_queue_max_num(1000)
                              , m_conf_http_gzip_parser_mode(1)
                              , m_conf_http_gzip_parser_queue_memory_max_size_bytes(1 << 27ULL)
                              , m_p_upload(NULL)
                              , m_http_file_download_flag(0)
                              , m_http_type(6)
                              , m_stream_debug(0)
                              , m_rsp_continue_ignore(1)
                              , m_upload_file(0)
                              , m_conf_http_recv_max_size(0)
                              , m_drop_empty_rsp(0)
                              , m_conf_http_minio_file_thread_num(3)
                              , m_conf_http_minio_file_queue_max_num(200000)
                              , m_conf_http_minio_file_queue_memory_max_size_bytes(1024 * 1024 * 1024ULL)
                              , m_use_new_event_format(0)
                              , m_conf_http_body_md5(0)
                              , m_conf_parser_enable(1)
                              , m_conf_send_empty_http_req_enable(0)
                              , m_conf_send_empty_http_rsp_enable(1)
                              , m_is_drop_file_event(false)
                              , m_upload_protobuf_enable(0)
                              , m_drop_percent(0)
                              , m_drop_enable(1)
{
  analyzer = new analyzer::submodule::HTTP2_Analyzer();
  snprintf(m_name, COUNTOF(m_name) - 1, "CGrpcParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);

  cnt = 0;
  h2c = false;
  kafka_data = NULL;
  clearUploadHttp2Info(st_upload_http2_info);
}

CGrpcParser::~CGrpcParser(void)
{
  delete analyzer;
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    free_worker_queue(m_p_wq[i]);
  }
  for (size_t i = 0; i < COUNTOF(m_p_tw); i++)
  {
    free_task_worker(m_p_tw[i]);
    m_p_tw[i] = NULL;
  }
  
  m_urlfilter_rule = NULL;
  m_accoutfilter_rule = NULL;
}

void CGrpcParser::modify_stats(int enable)
{
  GWLOG_INFO(m_comm, "CGrpcParser modify stats: %d, parser_enable: %d\n", enable, m_conf_parser_enable);
  if (enable > 1) 
    enable = 1;
  if (__sync_bool_compare_and_swap(&m_conf_parser_enable, enable^1, enable)) {
    GWLOG_DEBUG(m_comm, "CGrpcParser modify true\n");
    if (!m_conf_parser_enable)
    {
      cache_clean();
    }
  }
  GWLOG_DEBUG(m_comm, "CGrpcParser after modify stats: %d, parser_enable: %d\n", enable, m_conf_parser_enable);
}

void CGrpcParser::send_empty_http_rsp_enable(int enable)
{
  GWLOG_INFO(m_comm, "CGrpcParser m_conf_send_empty_http_rsp_enable modify stats: %d, m_conf_send_empty_http_rsp_enable: %d\n", enable, m_conf_send_empty_http_rsp_enable);
  if (enable > 1)
    enable = 1;
  if (enable < 0)
    enable = 0;
  if (__sync_bool_compare_and_swap(&m_conf_send_empty_http_rsp_enable, enable^1, enable)) {
    GWLOG_DEBUG(m_comm, "CGrpcParser m_conf_send_empty_http_rsp_enable modify true\n");
    //if (!m_conf_send_empty_http_rsp_enable)
    //{
    //    cache_clean();
    //}
  }
  GWLOG_DEBUG(m_comm, "CGrpcParser after m_conf_send_empty_http_rsp_enable modify stats: %d, m_conf_send_empty_http_rsp_enable: %d\n", enable, m_conf_send_empty_http_rsp_enable);
}

void CGrpcParser::send_empty_http_req_enable(int enable)
{
  GWLOG_INFO(m_comm, "CGrpcParser m_conf_send_empty_http_req_enable modify stats: %d, m_conf_send_empty_http_req_enable: %d\n", enable, m_conf_send_empty_http_req_enable);
  if (enable > 1)
    enable = 1;
  if (enable < 0)
    enable = 0;
  if (__sync_bool_compare_and_swap(&m_conf_send_empty_http_req_enable, enable^1, enable)) {
    GWLOG_DEBUG(m_comm, "CGrpcParser m_conf_send_empty_http_req_enable modify true\n");
    //if (!m_conf_send_empty_http_req_enable)
    //{
    //    cache_clean();
    //}
  }
  GWLOG_DEBUG(m_comm, "CGrpcParser after m_conf_send_empty_http_req_enable modify stats: %d, m_conf_send_empty_http_req_enable: %d\n", enable, m_conf_send_empty_http_req_enable);
}

/*
void CGrpcParser::upload_protobuf_enable(int enable)
{
  GWLOG_INFO(m_comm, "CGrpcParser m_upload_protobuf_enable modify stats: %d, m_upload_protobuf_enable: %d\n", enable, m_upload_protobuf_enable);
  if (enable > 1)
    enable = 1;
  if (enable < 0)
    enable = 0;
  if (__sync_bool_compare_and_swap(&m_upload_protobuf_enable, enable^1, enable)) {
    GWLOG_DEBUG(m_comm, "CGrpcParser m_upload_protobuf_enable modify true\n");
  }
  GWLOG_DEBUG(m_comm, "CGrpcParser after m_upload_protobuf_enable modify stats: %d, m_upload_protobuf_enable: %d\n", enable, m_upload_protobuf_enable);
}
*/

void CGrpcParser::modify_drop_percent(int percent)
{
  GWLOG_INFO(m_comm, "CGrpcParser http_drop_percent modify percent: %d, current: %d\n", percent, m_drop_percent);
  if (percent > 100)
    percent = 100;
  if (percent < 0)
    percent = 0;
  int temp = m_drop_percent;
  if (__sync_bool_compare_and_swap(&m_drop_percent, temp, percent)) {
    GWLOG_DEBUG(m_comm, "CGrpcParser http_drop_percent modify true\n");
  }
  GWLOG_DEBUG(m_comm, "CGrpcParser http_drop_percent modify percent: %d, current: %d\n", percent, m_drop_percent);
}

void CGrpcParser::cache_clean()
{
  for (int i(0); i<HTTPPARSER_WQ_MAX_NUM; i++) 
  {
    if (m_p_wq[i]) 
    {
      m_p_wq[i]->flush_queue();
    }
  }
}

/**
 * 在接收数据时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CGrpcParser::probe(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession* p_session)
{
  GWLOG_DEBUG(m_comm,"This is grpc probe!!!\n");

  const uint8_t *p;
  const char *data;
  int data_len;
  int offset_out;
  enum http_parser_type dir = HTTP_BOTH;
  StreamData *tcp_data = NULL;
  tcp_stream *a_tcp = NULL;
  bool res = false;

  if (p_session == NULL)
  {
    p_session = psm->new_session(pcon);
    if (p_session == NULL)
    {
      return false;
    }
  }

  tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
  //ASSERT(tcp_data != NULL);
  if (tcp_data == NULL) {
    return false;
  }

  a_tcp = tcp_data->a_tcp;
  ASSERT(a_tcp != NULL);

  switch (a_app->dir)
  {
  case STREAM_REQ:
    if (a_tcp->reverse == 0) {
      dir = HTTP_REQUEST;
    } else {
      dir = HTTP_RESPONSE;
    }
    break;

  case STREAM_RSP:
    if (a_tcp->reverse == 0) {
      dir = HTTP_RESPONSE;
    }
    else {
      dir = HTTP_REQUEST;
    }
    break;
  }

  data = p_session->get_data(this, dir, &data_len, &offset_out);
  p = (const uint8_t *)data;

  if (!(p != NULL && data_len > m_conf_http_header_min_size))
  {
    p_session->discard(this, dir, 0);
    return false;
  }


  // h2c http1.1 upgrade http2
  if((my_strstr(data, data_len, "Upgrade: h2c\r\n",strlen("Upgrade: h2c\r\n"))) != NULL)
  {
    res = true;
    //GWLOG_INFO(m_comm,"probe success,this is http2!!:\n");
  }
  else if((my_strstr(data, data_len, "PRI * HTTP/2.0\r\n\r\nSM\r\n\r\n", strlen("PRI * HTTP/2.0\r\n\r\nSM\r\n\r\n"))) != NULL)
  {
    // judge the connection is HTTP2? Magic frame
    res = true;
    //GWLOG_INFO(m_comm,"probe success,this is http2!!:\n");
  }
  else
  {
    // 等待新的数据
    p_session->discard(this, dir, 0);
    res = false;
  }

  return res;
}

/**
 * 在连接关闭时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @param struct conn *
 */
bool CGrpcParser::probe_on_close(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return false;
}


/**
 * 在连接重置时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @param struct conn *
 */
bool CGrpcParser::probe_on_reset(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return false;
}

static void get_addr_str(u_int addr, char *buf, size_t size)
{
  buf[size - 1] = '\0';
  unsigned char *bytes = (unsigned char *)&addr;
  size_t offset = 0;
  int i = 0;
  for (i = 0; i < 4; i++)
  {
    ntos(bytes[i], buf + offset, &offset);
    if (i != 3)
    {
      *(buf + offset++) = '.';
    }
  }
}

static const char *format(analyzer::submodule::upload_http2_info_t st_upload_http2_info,size_t &len,CGwCommon *m_comm,int upload_protobuf_enable)
{
  const char *buffer = NULL;
  if(0 == upload_protobuf_enable)
  {
    // request
    std::string request = "{\"req\":{";
    request = request + "\"method\":" + "\"" + st_upload_http2_info.http2_req_info.method + "\",";
    request = request + "\"http_version\":" + "\"2.0\",";
    request = request + "\"remote_addr\":" + "\"" + st_upload_http2_info.http2_req_info.remote_addr + "\",";
    request = request + "\"url\":" + "\"" + st_upload_http2_info.http2_req_info.full_url + "\",";
    request = request + "\"header\":{";
    request = request + st_upload_http2_info.http2_req_info.header;
    request = request + "},";
    request = request + "\"body\":";
    request = request + "\"" + st_upload_http2_info.http2_req_info.body + "\"\0";
    request = request + "},";

    // response
    std::string response = "\"rsp\":{";
    response = response + "\"status\":" + "\"" + std::to_string(st_upload_http2_info.http2_rsp_info.status_code)+ "\",";
    response = response + "\"http_version\":" + "\"2.0\",";
    response = response + "\"header\":{";
    response = response + st_upload_http2_info.http2_rsp_info.header;
    response = response + "},";
    response = response + "\"body\":";
    response = response + "\"" + st_upload_http2_info.http2_rsp_info.body + "\"\0";
    response = response + "},";

    // meta
    std::string meta = "\"meta\":{";
    meta = meta + "\"tm\":" + std::to_string((int)st_upload_http2_info.http2_meta_info.ts);
    meta = meta + "},";

    // net
    std::string net = "\"net\":{";
    net = net + "\"src_ip\":" + "\"" + st_upload_http2_info.http2_net_info.a_src_ip + "\",";
    net = net + "\"src_port\":" + std::to_string(st_upload_http2_info.http2_net_info.src_port) + ",";
    net = net + "\"dst_ip\":" + "\"" + st_upload_http2_info.http2_net_info.a_dst_ip + "\",";
    net = net + "\"dst_port\":" + std::to_string(st_upload_http2_info.http2_net_info.dst_port);
    net = net + "},";

    // unique_id
    std::string uid = "\"unique_id\":{";
    uid = uid + "\"event_id\":" + "\"" + st_upload_http2_info.a_unique_id + "\"";
    uid = uid + "}}";

    /*
    GWLOG_INFO(m_comm,"req method:%s\n",st_upload_http2_info.http2_req_info.method.c_str());
    GWLOG_INFO(m_comm,"req header:%s\n",st_upload_http2_info.http2_req_info.header.c_str());
    GWLOG_INFO(m_comm,"res header:%s\n",st_upload_http2_info.http2_rsp_info.header.c_str());
    GWLOG_INFO(m_comm,"res body:%s\n",st_upload_http2_info.http2_rsp_info.body.c_str());
    */

    std::string data = request + response + meta + net + uid ;
    len = data.length();

    buffer = (const char *)malloc(sizeof(char) * len);
    strcpy(const_cast<char *>(buffer),data.c_str());
  }
  else if( 1 == upload_protobuf_enable)
  {
    char buf[1024] = {0};
    com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent protobuf_raw_http_event;
    com::quanzhi::audit_core::common::model::HttpRequest* req = protobuf_raw_http_event.mutable_req();
    req->set_method(st_upload_http2_info.http2_req_info.method);
    req->set_httpversion("2.0");
    req->set_remoteaddr(st_upload_http2_info.http2_req_info.remote_addr);
    req->set_errcode(0);
    req->set_url(st_upload_http2_info.http2_req_info.full_url);
    req->set_body(st_upload_http2_info.http2_req_info.body);
    google::protobuf::Map<std::string, std::string>* reqHeader = req->mutable_header();
    for(size_t i=0; i<st_upload_http2_info.http2_req_info.key.size(); ++i)
    {
      (*reqHeader)[st_upload_http2_info.http2_req_info.key[i]] = st_upload_http2_info.http2_req_info.value[i];
    }

    com::quanzhi::audit_core::common::model::HttpResponse* rsp = protobuf_raw_http_event.mutable_rsp();
    snprintf(buf, sizeof(buf), "%d", st_upload_http2_info.http2_rsp_info.status_code);
    rsp->set_status(buf);
    rsp->set_httpversion("2.0");
    rsp->set_errcode(0);
    rsp->set_body(st_upload_http2_info.http2_rsp_info.body);
    rsp->add_setcookieslist("");
    google::protobuf::Map<std::string, std::string>* rspHeader = rsp->mutable_header();
    for(size_t i=0; i<st_upload_http2_info.http2_rsp_info.key.size(); ++i)
    {
      (*rspHeader)[st_upload_http2_info.http2_rsp_info.key[i]] = st_upload_http2_info.http2_rsp_info.value[i];
    }

    com::quanzhi::audit_core::common::model::Meta* meta = protobuf_raw_http_event.mutable_meta();
    meta->set_tm(st_upload_http2_info.http2_meta_info.ts);

    com::quanzhi::audit_core::common::model::Net* net = protobuf_raw_http_event.mutable_net();
    net->set_srcip(st_upload_http2_info.http2_net_info.a_src_ip);
    net->set_srcport(st_upload_http2_info.http2_net_info.src_port);
    net->set_dstip(st_upload_http2_info.http2_net_info.a_dst_ip);
    net->set_dstport(st_upload_http2_info.http2_net_info.dst_port);
    net->set_flowsource("");
    net->set_vlanid(0);

    com::quanzhi::audit_core::common::model::UniqueId* unique_id = protobuf_raw_http_event.mutable_uniqueid();
    unique_id->set_eventid(st_upload_http2_info.a_unique_id);

    com::quanzhi::audit_core::common::model::RawFileInfo* file = protobuf_raw_http_event.mutable_file();
    file->set_filedirection("");
    file->set_rwflag(0);
    file->set_isincomplete(0);
    file->set_filename("");
    file->set_filetype("");
    file->set_filetypereliable(1);
    file->set_filelen(0);
    file->set_filewarn("");
    file->set_uploadflag(0);
    file->set_uploaddir("");
    file->set_sha256("");

    buffer = (const char*)malloc(protobuf_raw_http_event.ByteSizeLong());
    if (NULL == buffer)
    {
        return "";
    }
    protobuf_raw_http_event.SerializeToArray(const_cast<char *>(buffer), protobuf_raw_http_event.ByteSizeLong());
    len = protobuf_raw_http_event.ByteSizeLong();
  }
  return buffer;
}


/**
 * 在接收数据时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CGrpcParser::parse(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon, CSession *p_session)
{
  if (p_session == NULL) {
    return -1;
  }

  StreamData* p_tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
  if (p_tcp_data == NULL) {
    return -1;
  }

  tcp_stream* a_tcp = p_tcp_data->a_tcp;
  if (a_tcp == NULL) {
    return -1;
  }

  const uint8_t *p;
  const char *data;
  int data_len;
  int offset_out;
  enum http_parser_type dir = HTTP_BOTH;
  
  switch (a_app->dir)
  {
  case STREAM_REQ:
    if (a_tcp->reverse == 0) {
      dir = HTTP_REQUEST;
    } else {
      dir = HTTP_RESPONSE;
    }
    break;
  case STREAM_RSP:
    if (a_tcp->reverse == 0) {
      dir = HTTP_RESPONSE;
    }
    else {
      dir = HTTP_REQUEST;
    }
    break;
  }
  data = p_session->get_data(this, dir, &data_len, &offset_out);
  if (data == NULL)
  {
    p_session->discard(this, dir, 0);
    return -1;
  }
  
  //GWLOG_INFO(m_comm,"parse——data_len:%d,read:%d,total%d,source:%u,dest:%u\n",data_len,a_tcp->read,a_tcp->total,a_tcp->addr.source,a_tcp->addr.dest);

  p = (const uint8_t *)data;
  int res = 0;
  if((my_strstr(data, data_len, "Upgrade: h2c\r\n",strlen("Upgrade: h2c\r\n"))) != NULL && (my_strstr(data, data_len, "Host",strlen("Host"))) != NULL)
  {
    // upgrade
    std::string str = data;
    std::map<std::string,std::string> headers;
    size_t end = str.find("\r\n\r\n");

    size_t method_right = str.find(" ");
    std::string method(str.begin(),str.begin()+method_right);

    size_t path_right = str.find(" ",method_right+1);
    std::string path(str.begin()+method_right+1,str.begin()+path_right);

    size_t separator = str.find("\r\n");
    while(separator < end)
    {
      size_t part = str.find(":",separator);
      std::string name(str.begin()+separator+2,str.begin()+part);
      separator = str.find("\r\n",part);
      std::string value(str.begin()+part+2,str.begin()+separator);
      headers[name] = value;
    }

    std::string url = "http://" + headers["Host"] + path;
    std::string header = "";
    size_t count = 1;

    for(auto it : headers)
    {
      header = header + "\"" + it.first + "\"" + ":" + "\"" + it.second + "\"";
      if(count++ < headers.size())
      {
        header = header + ",";
      }
    }
    
    st_upload_http2_info.http2_req_info.method = method;
    st_upload_http2_info.http2_req_info.full_url = url;
    st_upload_http2_info.http2_req_info.header = header;

    h2c = true;

  }
  else if((my_strstr(data, data_len, "Upgrade: h2c\r\n",strlen("Upgrade: h2c\r\n"))) != NULL)
  {
      // skip this data,continue
  }
  else
  {
    res = analyzer->deliverStream(data_len,p,dir == 0 ? true : false,st_upload_http2_info.http2_req_info,st_upload_http2_info.http2_rsp_info,h2c);
  }

  if(st_upload_http2_info.http2_rsp_info.body.length())
  {
    GWLOG_DEBUG(m_comm,"buff_len:%d\n",st_upload_http2_info.http2_rsp_info.body.length());

    // format data
    //const char *s = NULL;
    size_t s_len = 0;

    get_addr_str(pcon->client.ipv4, st_upload_http2_info.http2_req_info.remote_addr, COUNTOF(st_upload_http2_info.http2_req_info.remote_addr));

    // net
    strcpy(st_upload_http2_info.http2_net_info.a_src_ip, st_upload_http2_info.http2_req_info.remote_addr);
    get_addr_str(pcon->server.ipv4, st_upload_http2_info.http2_net_info.a_dst_ip, sizeof(st_upload_http2_info.http2_net_info.a_dst_ip));
    st_upload_http2_info.http2_net_info.src_port = pcon->client.port;
    st_upload_http2_info.http2_net_info.dst_port = pcon->server.port;

    /* 添加unique_id */
    add_event_id(st_upload_http2_info.a_unique_id);

    /* 添加meta信息 */
    if (1 == m_conf_pcap_timestamp)
    {
      // 取自PCAP结构里的时间信息
      st_upload_http2_info.http2_meta_info.ts = p_session->get_ts();
    }
    else
    {
      uint64_t u64_real_ms = m_comm->gw_real_time_ms();
      //st_upload_http2_info.http_meta_info.ts = time(NULL);
      st_upload_http2_info.http2_meta_info.ts = ((double)u64_real_ms / 1000);
    }
    
    __sync_fetch_and_add(&m_stats_http2.cnt_session, 1);
    __sync_fetch_and_add(&m_stats_http2.cnt_session_bytes, st_upload_http2_info.http2_rsp_info.body.length());
    __sync_fetch_and_add(&m_stats_http2.m.cnt_m, 1);
    __sync_fetch_and_add(&m_stats_http2.m.cnt_m_succ, 1);
    __sync_fetch_and_add(&m_stats_http2.rsp_match_req.cnt_m, 1);
    __sync_fetch_and_add(&m_stats_http2.rsp_match_req.cnt_m_succ, 1);
    __sync_fetch_and_add(&m_stats_http2.req_match_rsp.cnt_m, 1);
    __sync_fetch_and_add(&m_stats_http2.req_match_rsp.cnt_m_succ, 1);

    //s = simple_json_encode(st_upload_http2_info, 0, 0, &s_len);

    kafka_data = format(st_upload_http2_info,s_len,m_comm,m_upload_protobuf_enable);

    // upload data
    http2_cb_upload_msg(kafka_data, 0, 0, s_len);

    clearUploadHttp2Info(st_upload_http2_info);
    h2c = false;

  }

  return res;
}


int CGrpcParser::parse_clear(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return 0;
}

/**
  * 在连接关闭时，解析数据流协议。
  * @param CSessionMgt *
  * @param app_stream *
  * @param struct conn *
  */
int CGrpcParser::parse_on_close(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  delete analyzer;
  analyzer = new analyzer::submodule::HTTP2_Analyzer();
  return 0;
}

/**
 * 在连接重置时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @param struct conn *
 */
int CGrpcParser::parse_on_reset(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  delete analyzer;
  analyzer = new analyzer::submodule::HTTP2_Analyzer();
  return 0;
}

/**
 * 获取当前流解析出来的数据。
 * @param struct StreamData *
 * @param int dir
 * @param int *data_len
 * @param int *offset_out
 */
const char *CGrpcParser::get_data(const struct StreamData *, int dir, int *data_len, int *offset_out)
{
  *data_len = 0;
  *offset_out = 0;
  return NULL;
}

/**
 * 已解析处理字节数。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CGrpcParser::discard(struct StreamData *, int dir, int num)
{
  return false;
}

/**
 * 已处理字节数，同时更新数据。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CGrpcParser::discard_and_update(struct StreamData *, int dir, int num)
{
  return false;
}

// /**
//  * 删除解析对象中在会话管理中的单边数据。
//  * @param HalfStreamData*
//  */
// void CGrpcParser::del_session_half_stream(HalfStreamData *)
// {
//   return;
// }

/**
 * @param StreamData*
 */
void CGrpcParser::del_session_stream(StreamData *psd)
{
    return;
}

/**
 * @param SessionMgtData*
 */
void CGrpcParser::del_session_param(SessionMgtData *)
{

}

void CGrpcParser::init()
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  load_conf(NULL);

  if (m_comm->get_verbose())
  {
    print_http2_param();
  }

  /* create http parser work queue */
  /*
  if (!(m_conf_http_parser_mode == 0))
  {
    new_wq_http2_parser_msg();
  }
  */

  /* create upstream parser work queue */
  if (m_conf_upstream)
  {
    new_wq_upstream();
  }

  m_comm->get_nacos_listen_conf()->add_conf_handle_int("http_parser_enable", std::bind(&CGrpcParser::modify_stats, this, std::placeholders::_1));
  m_comm->get_nacos_listen_conf()->add_conf_handle_int("send_empty_http_rsp_enable", std::bind(&CGrpcParser::send_empty_http_rsp_enable, this, std::placeholders::_1));
  m_comm->get_nacos_listen_conf()->add_conf_handle_int("send_empty_http_req_enable", std::bind(&CGrpcParser::send_empty_http_req_enable, this, std::placeholders::_1));
  //m_comm->get_nacos_listen_conf()->add_conf_handle_int("http_upload_protobuf_enable", std::bind(&CGrpcParser::upload_protobuf_enable, this, std::placeholders::_1));
  m_comm->get_nacos_listen_conf()->add_conf_handle_int("http_drop_percent", std::bind(&CGrpcParser::modify_drop_percent, this, std::placeholders::_1));

}

void CGrpcParser::fini()
{
  ASSERT(m_comm != NULL);
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    free_worker_queue(m_p_wq[i]);
    m_p_wq[i] = NULL;
  }
  for (size_t i = 0; i < COUNTOF(m_p_tw); i++)
  {
    free_task_worker(m_p_tw[i]);
    m_p_tw[i] = NULL;
  }
}

void CGrpcParser::run()
{
  ASSERT(m_comm != NULL);
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    if (m_p_wq[i] == NULL)
    {
      continue;
    }
    m_p_wq[i]->run();
  }
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CGrpcParser::get_name(void) const
{
  return m_name;
}

/**
 * 获取版本号。
 */
const char *CGrpcParser::get_version(void) const
{
  return HTTPPARSER_VER;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CGrpcParser::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CGrpcParser::load_conf(const char *json_string)
{
  CGwConfig *pgwc = m_comm->get_gw_config();

  /* 解析http流量队列相关参数 */
  m_conf_http_parser_mode = pgwc->read_conf_int("parser", "http_parser_mode", m_conf_http_parser_mode); /* 是否使用队列，1表示使用 */
  m_conf_http_parser_thread_num = pgwc->read_conf_int("parser", "http_parser_thread_num", m_conf_http_parser_thread_num);
  m_conf_http_parser_queue_max_num = pgwc->read_conf_int("parser", "http_parser_queue_num", m_conf_http_parser_queue_max_num);
  m_conf_http_parser_queue_memory_max_size_bytes = (uint64_t)pgwc->read_conf_int("parser", "queue_http_parser_msg_memory_size", m_conf_http_parser_queue_memory_max_size_bytes / (1 << 20ULL)) * (1 << 20ULL);

  m_conf_upload_name = pgwc->read_conf_string("parser", "upload_mode");
  if (m_conf_upload_name.size() > 0)
  {
    const char *s = m_conf_upload_name.c_str();
    if (0 == strcasecmp("web", s))
    {
      // web
      m_conf_upload_mode = 1;
    }
    else if (0 == strcasecmp("kafka", s))
    {
      // kafka
      m_conf_upload_mode = 2;
    }
    else if (0 == strcasecmp("log", s))
    {
      // log
      m_conf_upload_mode = 3;
    }
    else if (0 == strcasecmp("test", s))
    {
      // test
      m_conf_upload_mode = 4;
    }
    else if (0 == strcasecmp("diy", s))
    {
      // test
      m_conf_upload_mode = 4;
    }
    else
    {
      // no upload
      m_conf_upload_mode = 0;
    }
  }

  m_upload_protobuf_enable = pgwc->read_conf_int("parser", "http_upload_protobuf_enable",0);

  m_p_upload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
  if (m_p_upload == NULL)
  {
    GWLOG_WARN(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
  }
  return true;
}


// void CGrpcParser::load_url_filter_base(void)
// {
//   FILE *fp = fopen(m_str_url_filter_base_path.c_str(), "a+");
// }

/**
 * 触发退出信号时处理
 */
void CGrpcParser::set_quit_signal(void)
{
  m_quit_signal = 1;

  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    if (m_p_wq[i] == NULL)
    {
      continue;
    }
    m_p_wq[i]->set_quit_signal();
  }
}

/**
 * 等待运行结束
 */
void CGrpcParser::wait_for_stop(void)
{
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    if (m_p_wq[i] == NULL)
    {
      continue;
    }
    m_p_wq[i]->wait_for_stop();
  }
}

/**
   * 设置过滤规则。
   * @param CFilterRule *rule
   */
void CGrpcParser::set_url_filter_rule(CFilterRule *rule)
{
  return;
}

/**
 *  设置账号过滤规则 
 *  @param CFilterRule *rule
 */
void CGrpcParser::set_accout_filter_rule(CFilterRule *rule)
{
  return;
}

void CGrpcParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule)
{
  return;
}

/**
 *  获取解析http数量(针对http parser) 
 */
uint64_t CGrpcParser::get_parser_http_cnt()
{
  return m_stats_http2.p.cnt_p;
}

/**
 *  获取解析http成功的数量(针对http parser) 
 */
uint64_t CGrpcParser::get_succ_parser_http_cnt()
{
  return m_stats_http2.p.cnt_p_succ;
}

/**
 *  获取解析parser的状态数据，以便于进行查看Parser内部状态
 */
void* CGrpcParser::get_parser_status()
{
  return (void*)&m_stats_http2;
}

/**
 * 设置解析对象type
 */
void CGrpcParser::set_parser_type(int type)
{
  m_http_type = type;
}

void CGrpcParser::set_tcp_parser(CTcpParser *p) 
{
  m_tcp_parser = p;
}

void CGrpcParser::read_conf_urlbase_for_mon()
{
  return ;
}

void CGrpcParser::read_conf_filetype_for_mon()
{
  return ;
}

void CGrpcParser::get_log_buf(char *log_buf, size_t log_buf_len) const
{
  return ;                                                                                                      
} 

void CGrpcParser::free_worker_queue(CWorkerQueue *p)
{
  if (p == NULL)
  {
    return;
  }
  p->set_quit_signal();
  p->wait_for_stop();

  p->delete_queue();

  p->fini();

  if (m_comm != NULL)
  {
    m_comm->destory_worker_queue(p);
  }
}

void CGrpcParser::free_task_worker(CTaskWorker *p)
{
  if (p == NULL)
  {
    return;
  }
  p->release();
}

uint32_t CGrpcParser::parser_status() const
{
  for (int i = 0; i < HTTPPARSER_WQ_MAX_NUM &&m_p_wq[i]; i++)
  {
    if (m_p_wq[i]->queue_elements_num())
    {
      return 1;
    }
  }

  return 0;
}

void CGrpcParser::print_http2_match_stats(void) const
{ 
  printf("\nstats http match \r\n");
  printf("%-20s %20s\r\n", "item", "count");
}

void CGrpcParser::print_http2_param(void) const
{
    GWLOG_INFO(m_comm, "conf: http2 parser thread num=%d\n", m_conf_http_parser_thread_num);
    GWLOG_INFO(m_comm, "conf: http2 parser queue num=%d\n", m_conf_http_parser_queue_max_num);
    GWLOG_INFO(m_comm, "conf: http2 parser queue memory max size bytes=%llu\n", m_conf_http_parser_queue_memory_max_size_bytes);

    GWLOG_INFO(m_comm, "conf: http2 gzip thread num = %d\n", m_conf_http_gzip_thread_num);
    GWLOG_INFO(m_comm, "conf: http2 gzip queeu num = %d\n", m_conf_http_gzip_queue_max_num);
    GWLOG_INFO(m_comm, "conf: http2 gzip queue memory max size bytes=%llu\n", m_conf_http_gzip_queue_memory_max_size_bytes);

    GWLOG_INFO(m_comm, "conf: http2 upstream thread num = %d\n", m_conf_http_upstream_thread_num);
    GWLOG_INFO(m_comm, "conf: http2 upstream queue num = %d\n", m_conf_http_upstream_queue_max_num);
    GWLOG_INFO(m_comm, "conf: http2 upstream queue memory max size bytes=%llu\n", m_conf_http_upstream_queue_memory_max_size_bytes);

    GWLOG_INFO(m_comm, "conf: http2 gzip parser thread num = %d\n", m_conf_http_gzip_parser_thread_num);
    GWLOG_INFO(m_comm, "conf: http2 gzip parser queue num = %d\n", m_conf_http_gzip_parser_queue_max_num);
    GWLOG_INFO(m_comm, "conf: hhtp2 gzip parser queue memory max size bytes = %llu\n", m_conf_http_gzip_parser_queue_memory_max_size_bytes);

    GWLOG_INFO(m_comm, "conf: http2 gzip mode=%d\n", m_conf_http_gzip_mode);
    GWLOG_INFO(m_comm, "conf: http2 parser mode=%d\n", m_conf_http_parser_mode);
    GWLOG_INFO(m_comm, "conf: http2 upstream mode = %d\n", m_conf_upstream);
    GWLOG_INFO(m_comm, "conf: run mode=%d\n", m_conf_run_mode);
    GWLOG_INFO(m_comm, "conf: http2 url show size=%d\n", m_conf_http_url_show_size);
    GWLOG_INFO(m_comm, "conf: upload name mode = %s\n", m_conf_upload_name.c_str());
    GWLOG_INFO(m_comm, "conf: http2 gzip parser mode = %d\n", m_conf_http_gzip_parser_mode);
    GWLOG_INFO(m_comm, "conf: http2 file download flag = %d\n", m_http_file_download_flag);

    return;
}