#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <strings.h>
#include <memory.h>
#include <unistd.h>
#include <inttypes.h>
#include <arpa/inet.h>

#include "grpc_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "gw_stats.h"

#include "cJSON.h"
#include "simple_json.h"

#include "gw_i_upload.h"

#include "urlfilter_rule.h"
#include "accoutfilter_rule.h"
#include "display_stats_define.h"
#include "get_file_type.h"

#include "pp.h"

// 运行模式状态检查
#define RUNMODE_IS_SHOW_URL(x) (x == 2 || x == 3 || x == 4)
#define RUNMODE_IS_SHOW_BODY(x) (x == 3)
#define RUNMODE_IS_REQ_BODY_FORMAT_STRING(x) (RUNMODE_IS_RESP_BODY_FORMAT_STRING(x) || x == 5 || RUNMODE_IS_RESP_BODY_USE_TEST_DATA(x))
#define RUNMODE_IS_RESP_BODY_USE_TEST_DATA(x) (x == 6)
#define RUNMODE_IS_RESP_BODY_FORMAT_STRING(x) (x >= 1000 || x == 0 || x == 2 || x == 3)

#define REQUEST_CT_FORM "multipart/form-data"
const char *g_p_file_warn = "large";
const char *g_p_file_upload = "upload";
const char *g_p_file_download = "download";

static const char msg_unknown_rule_type[] = "http_unknown_rule";
static const char msg_ruled_type[] = "http_ruled";
static const char msg_drop_file_type[] = "http_drop_file";

thread_local uint64_t g_u64_http_upload_ms = 0;
thread_local uint32_t g_u32_http_upload_index = 0;

void CGrpcParser::http2_cb_upload_msg(const char *s, int unkown_rule, bool analyze, size_t s_len)
{
  //size_t length = strlen(s);
  size_t length  = s_len;
  http2_cb_upstream(s, length);

  if (unlikely(m_p_upload == NULL))
  {
    GWLOG_INFO(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    cJSON_free((void*)s);
    return;
  }

  UploadMsg *pum = new UploadMsg;
  memset(pum, 0, sizeof(UploadMsg));

  pum->cb = sizeof(UploadMsg);
  pum->destroy_func = free_upload_msg;
  pum->parser = this;
  pum->length = length;
  pum->s = s;
  pum->log_for_analyze = analyze;

  switch (unkown_rule)
  {
  case 0:
    pum->msgtype = msg_ruled_type;
    break;
  case 1:
    pum->msgtype = msg_drop_file_type;
    break;
  default:
    pum->msgtype = msg_unknown_rule_type;
  }

  pum->mem_size = sizeof(UploadMsg) + pum->length;

  m_p_upload->put_msg(pum);
}

void CGrpcParser::free_upload_msg(const struct UploadMsg *pum)
{
  CGrpcParser *pThis = (CGrpcParser *)pum->parser;
  ASSERT(pThis != NULL);
  (void)pThis;

  //cJSON_free((void*)pum->s);

  delete pum;
}

/*
void CGrpcParser::free_http2_parser_msg_inner(const http2_parser_msg_t *p)
{
  SAFE_FREE(p->req_msg.header_item);
  SAFE_FREE(p->rsp_msg.header_item);

  BSTR_SAFE_FREE(p->req_msg.header.bstr);
  BSTR_SAFE_FREE(p->req_msg.body.bstr);
  BSTR_SAFE_FREE(p->rsp_msg.header.bstr);
  BSTR_SAFE_FREE(p->rsp_msg.body.bstr);
}
*/


void CGrpcParser::add_event_id(char *p_event_id)
{
    if (NULL == p_event_id)
    {
      return;
    }

    char a_unique_code[64] = {0};
    //char a_unique_encode[128] = {0};
    uint64_t u64_time_val = 0;
    get_ms_timeval(&u64_time_val);

    if (g_u64_http_upload_ms == 0)
    {
        g_u64_http_upload_ms = u64_time_val;
        g_u32_http_upload_index = 1;
    }
    else
    {
        if (u64_time_val == g_u64_http_upload_ms)
        {
            g_u32_http_upload_index ++;
        }
        else
        {
            g_u64_http_upload_ms = u64_time_val;
            g_u32_http_upload_index = 1;
        }
    }

    /* 获取唯一标识ID */
    get_unique_event_id(m_str_gw_ip.c_str(), g_u64_http_upload_ms, g_u32_http_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

    /* 将unique_code进行base64编码 */
    base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

    return;
}
