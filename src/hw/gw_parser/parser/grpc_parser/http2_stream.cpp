#include <string>
#include <memory>
#include <cstring>
#include <iostream>
#include <algorithm>
#include "http2_stream.h"

using namespace analyzer;
using namespace analyzer::submodule;

/********** HTTP2_HalfStream *********/
HTTP2_HalfStream::HTTP2_HalfStream(uint32_t stream_id,Table *table,bool h2c)
{
    this->stream_id = stream_id;
    if(h2c)
    {
        this->state = HTTP2_STREAM_STATE_CLOSED;
    }
    else
    {
        this->state = HTTP2_STREAM_STATE_IDLE; 
    }
        
    this->data_buffer = nullptr;
    this->buffer = nullptr;
    this->data_size = 0;
    this->size = 0;
    this->zip = nullptr;
    this->brotli = nullptr;
    this->brotli_buffer = nullptr;

    this->content_length = 0;
    this->content_encoding_id = DATA_ENCODING_IDENTITY;
    this->end_stream = false;
    this->peer_stream_ended = false;
    this->table = table;
    this->integrality = false;    
}

HTTP2_HalfStream::~HTTP2_HalfStream()
{
    if(this->zip)
    {
        delete zip;
    }
    if(this->brotli)
    {
        BrotliDecoderDestroyInstance(this->brotli);
        this->brotli = nullptr;
    }
    if(this->brotli_buffer) 
    {
        delete this->brotli_buffer;
        this->brotli_buffer = nullptr;
    }
    if(this->data_buffer)
    {
        free(this->data_buffer);
        this->data_buffer = nullptr;
    }
    if(this->buffer)
    {
        free(this->buffer);
        this->buffer = nullptr;
    }
}

void HTTP2_HalfStream::processHeaders(uint8_t* header_block_fragment_ptr, uint32_t len, std::vector<header>& headers)
{
    hpackDecode(headers,header_block_fragment_ptr,table,len);
}

void HTTP2_HalfStream::parseContentEncoding(std::string& s)
{
    DataEncoding encoding = DATA_ENCODING_IDENTITY;

    if(s == "aes128gcm")
    {
        encoding = DATA_ENCODING_AES128GCM;
    }
    else if(s == "compress")
    {
        encoding = DATA_ENCODING_COMPRESS;
    }
    else if(s == "deflate")
    {
        encoding = DATA_ENCODING_DEFLATE;
    }
    else if(s == "pack200-gzip")
    {
        encoding = DATA_ENCODING_PACK200GZIP;
    }
    else if(s == "gzip")
    {
        encoding = DATA_ENCODING_GZIP;
    }
    else if(s == "exi")
    {
        encoding = DATA_ENCODING_EXI;
    }
    else if(s == "br")
    {
        encoding = DATA_ENCODING_BROTLI;
    }
    
    this->content_encoding_id = encoding;
    this->content_encoding = s;
}

void HTTP2_HalfStream::extractField(header h)
{
    if(h.first == "grpc-encoding")
    {
        parseContentEncoding(h.second);
    }
    else if(h.first == "content-type")
    {
        this->content_type = h.second;
    }
    else if(h.first == "content-length")
    {
        this->content_length = std::stoi(h.second);
    }
}

void HTTP2_HalfStream::getBody(int len, const uint8_t* data)
{
    // the data must be complete
    switch (this->content_encoding_id) {
        case DATA_ENCODING_DEFLATE:
        case DATA_ENCODING_GZIP:
        {
            translateZipBody(len, data);

            // check integrality
            uint32_t isize = 0;     // original data size
            memcpy(&isize,data+len-4,sizeof(uint8_t) * 4);
            if(isize == this->data_size)
            {
                this->integrality = true;
            }
            break;
        }
        case DATA_ENCODING_BROTLI:
        {
            this->brotli = BrotliDecoderCreateInstance(0, 0, 0);
            this->brotli_buffer = new uint8_t[BROTLI_BUFFER_SIZE];
            translateBrotliBody(len, data);
            delete this->brotli_buffer;
            this->brotli_buffer = nullptr;
            BrotliDecoderDestroyInstance(this->brotli);
            this->brotli = nullptr;
            break;
        }
        case DATA_ENCODING_AES128GCM:   // AES encrypted with 128 bit Key in Galois/Counter Mode
        case DATA_ENCODING_COMPRESS:
        case DATA_ENCODING_EXI:
        case DATA_ENCODING_PACK200GZIP: // Compressed Jar file (pack200) then gzip'd
        case DATA_ENCODING_IDENTITY:    // No compression... clear text.
        default:
        {
            // check integrality
            /*
            if(len == this->content_length)
            {
                this->integrality = true;
                this->data_buffer = (char *)malloc(sizeof(char) * len);
                memcpy(this->data_buffer,(char *)data,len);
                this->data_size = len;
            }
            else if(this->content_type == "application/json")
            {
                // Note: if response content-type == application/json, it don't have content-length
                this->integrality = true;
                this->data_buffer = (char *)malloc(sizeof(char) * len);
                memcpy(this->data_buffer,(char *)data,len);
                this->data_size = len;
            }
            */
            this->integrality = true;
            this->data_buffer = (char *)malloc(sizeof(char) * len);
            memcpy(this->data_buffer,(char *)data,len);
            this->data_size = len;
            break;
        }
            
    }
}

void HTTP2_HalfStream::translateZipBody(int len, const uint8_t* data)
{
    int zip_status = Z_OK;
    this->zip = new z_stream;
	zip->zalloc = 0;
	zip->zfree = 0;
	zip->opaque = 0;
	zip->next_out = 0;
	zip->avail_out = 0;
	zip->next_in = 0;
	zip->avail_in = 0;

    if ( inflateInit2(zip, MAX_WBITS + 32) != Z_OK )
	{
        // "32" is a gross overload hack that means "check it
	    // for whether it's a gzip file".
        printf("inflate_init_failed\n");
		delete zip;
		zip = nullptr;
	}
	
    if ( ! len || zip_status != Z_OK )
    {
        delete zip;
        return;
    }
		
	static unsigned int unzip_size = 4096;

#ifdef DEBUG
    // c++14
	auto unzipbuf = std::make_unique<Bytef[]>(unzip_size);
#else
    // c++11
    auto unzipbuf = std::unique_ptr<Bytef[]>(new Bytef[unzip_size]);
#endif

	int allow_restart = 1;

	zip->next_in = (Bytef*)data;
	zip->avail_in = len;

	auto orig_next_in = zip->next_in;
	size_t orig_avail_in = zip->avail_in;

	while ( true )
	{
	    zip->next_out = unzipbuf.get();
	    zip->avail_out = unzip_size;
	    zip_status = inflate(zip, Z_SYNC_FLUSH);
	    if ( zip_status == Z_STREAM_END || zip_status == Z_OK )
	    {
	    	allow_restart = 0;
	    	int have = unzip_size - zip->avail_out;
	    	if ( have )
            {
                if(!this->data_buffer)
                {
                    this->data_buffer = (char *)malloc(sizeof(char) * have);
                    memcpy(this->data_buffer,unzipbuf.get(),have);
                }
                else
                {
                    this->data_buffer = (char*)realloc(this->data_buffer,sizeof(char) * (have+this->data_size));
                    memcpy(this->data_buffer+this->data_size,unzipbuf.get(),have);
                }
                this->data_size += have;
            }
	    	if ( zip_status == Z_STREAM_END )
	    	{
	    		inflateEnd(zip);
	    		return;
	    	}
	    	if ( zip->avail_in == 0 )
	    		return;
	    }
	    else if ( allow_restart && zip_status == Z_DATA_ERROR )
	    {
	    	// Some servers seem to not generate zlib headers,
	    	// so this is an attempt to fix and continue anyway.
	    	inflateEnd(zip);
	    	if ( inflateInit2(zip, -MAX_WBITS) != Z_OK )
	    	{
                printf("inflate_init_failed\n");
	    		return;
	    	}
	    	zip->next_in = orig_next_in;
	    	zip->avail_in = orig_avail_in;
	    	allow_restart = 0;
	    	continue;
	    }
	    else
	    {
	    	printf("inflate_failed\n");
	    	return;
	    }
	}
}

void HTTP2_HalfStream::translateBrotliBody(int len, const uint8_t *data)
{
    BrotliDecoderResult result;
    bool repeat;
    size_t bytes_decompressed;
    size_t available_in = len;

    do {
        repeat = false;
        size_t available_out = BROTLI_BUFFER_SIZE;
        uint8_t *next_out = this->brotli_buffer;

        result = BrotliDecoderDecompressStream(
            this->brotli, &available_in, &data,
            &available_out, &next_out, NULL);

        bytes_decompressed = BROTLI_BUFFER_SIZE - available_out;

        if (result == BROTLI_DECODER_RESULT_SUCCESS && available_in > 0) {
            printf( "Unexpected left-over bytes in brotli decompression\n");
        }

        switch(result) {
            case BROTLI_DECODER_RESULT_ERROR:
            {
                BrotliDecoderErrorCode code = BrotliDecoderGetErrorCode(this->brotli);
                const char* error_string = BrotliDecoderErrorString(code);
                printf( "Brotli decoder error: %s\n",error_string);
                break;
            }
            case BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:
                // Set repeat so this sequence continues until all output data
                // is extracted
                repeat = true;
                // Don't break -- let this fall through to the below case(s)
            case BROTLI_DECODER_RESULT_SUCCESS:
            case BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:
            {
                if (bytes_decompressed > 0) 
                {
                    this->data_buffer = (char *)malloc(sizeof(char) * bytes_decompressed);
                    memcpy(this->data_buffer,(char *)this->brotli_buffer,bytes_decompressed);
                    this->data_size += bytes_decompressed;
                    this->integrality = true;
                }
                break;
            }
            default:
                // Unexpected/undocumented result
                printf( "Brotli decoder returned unexpected result\n");
                break;
        }
    } while (repeat);
}

void HTTP2_HalfStream::processData(HTTP2_Data_Frame* data)
{
    uint32_t length;
    const uint8_t* data_msg = data->getData(length);
    length -= 5;    // grpc header has 5 bytes
    if(this->buffer)
    {
        this->buffer = (uint8_t*)realloc(this->buffer,sizeof(uint8_t) * (this->size + length));
        memcpy(this->buffer+this->size,const_cast<uint8_t*>(data_msg + 5),length);
    }
    else
    {
        this->buffer = (uint8_t*)malloc(sizeof(uint8_t) * length);
        memcpy(this->buffer,const_cast<uint8_t*>(data_msg + 5),length);
    }
    this->size += length;

    if(data->isEndStream())
    {
        this->getBody(this->size,(const uint8_t *)this->buffer);
        free(this->buffer);
        this->buffer = nullptr;
        this->size = 0;
    }
    
}

/******** HTTP2_OrigStream *******/
HTTP2_OrigStream::HTTP2_OrigStream(uint32_t stream_id,Table *table,bool h2c):HTTP2_HalfStream(stream_id,table,h2c)
{

}

HTTP2_OrigStream::~HTTP2_OrigStream()
{

}

void HTTP2_OrigStream::handleFrame(HTTP2_Frame* frame)
{
    switch(this->state){
        case HTTP2_STREAM_STATE_IDLE:
            this->Idle_State(frame);
            break;
        case HTTP2_STREAM_STATE_OPEN:
            this->Open_State(frame);
            break;
        case HTTP2_STREAM_STATE_HALF_CLOSED:
            this->Open_State(frame);
            break;
        case HTTP2_STREAM_STATE_CLOSED:
            this->Closed_State(frame);
            break;
        default:
            break;
    }

}

void HTTP2_OrigStream::handlePeerEndStream(void)
{
    this->peer_stream_ended = true;
    if(this->end_stream)
    {
        this->state = HTTP2_STREAM_STATE_CLOSED;
    }
}

void HTTP2_OrigStream::handleEndStream(void)
{
    this->end_stream = true;
    if(this->peer_stream_ended)
    {
        this->state = HTTP2_STREAM_STATE_CLOSED;
    }
}

void HTTP2_OrigStream::handlePushRequested(HTTP2_Frame* frame)
{
    HTTP2_Header_Frame_Base* h = static_cast<HTTP2_Header_Frame_Base*>(frame);
    this->ProcessHeaderBlock(h);

    if(h->isEndHeaders())
    {
        // There is no client body to a push promise
        // only headers
        this->state = HTTP2_STREAM_STATE_HALF_CLOSED;
        this->handleEndStream();
    }
    else
    {
        // expect continuation frame
    }
}

void HTTP2_OrigStream::ProcessHeaderBlock(HTTP2_Header_Frame_Base* h)
{
    uint32_t len = 0;
    uint8_t* ptr = const_cast<uint8_t*>(h->getHeaderBlock(len));
    this->processHeaders(ptr, len,this->headers);
    for(auto it : this->headers)
    {
        if ((it.first)[0] == ':') {
            // Determine if this is one of the Pseudo Headers
            if (it.first == ":authority") {
                std::string token = (it.second).substr((it.second).find("@") + 1, std::string::npos);
                this->request_host = token.substr(0, token.find(":"));
                this->request_authority = it.second;
            }
            else if (it.first == ":method") {
                this->request_method = it.second;
            }
            else if (it.first == ":scheme") {
                this->request_scheme = it.second;
            }
            else if (it.first == ":path") {
                this->request_path = it.second;
            } 
        }
        else
        {
            this->extractField(it);
        }
    }
}

void HTTP2_OrigStream::Idle_State(HTTP2_Frame* frame)
{
    switch(frame->getType())
    {
        case PUSH_PROMISE_FRAME:  // doesn't send
        case HEADERS_FRAME:
        case CONTINUATION_FRAME:
        {
            HTTP2_Header_Frame_Base *h = static_cast<HTTP2_Header_Frame_Base*>(frame);
            this->ProcessHeaderBlock(h);

            if(h->isEndHeaders())
            {
                // get headers —— names & values
                this->state = HTTP2_STREAM_STATE_OPEN;
            }
            else
            {
                //expect continuation frame
            }

            if(h->isEndStream())
            {
                this->state = HTTP2_STREAM_STATE_HALF_CLOSED;
                this->handleEndStream();
            }
            break;
        }
        case DATA_FRAME:
            printf( "Received data frame while in the 'idle' state\n");
            break;
        case SETTINGS_FRAME:
        case GOAWAY_FRAME:
        case PING_FRAME:
        {
            // above frames should in 0 stream
            printf( "Unexpected frame in non-zero stream\n");
            break;
        }
        case WINDOW_UPDATE_FRAME:
        case PRIORITY_FRAME:
            break;
        case RST_STREAM_FRAME:
        {
            printf( "RST_STREAM received for stream in idle state\n");
            this->state = HTTP2_STREAM_STATE_CLOSED;
            break;
        }
        default:
            printf( "Invalid Frame Type:%d\n",frame->getType());
            break;
    }
}

void HTTP2_OrigStream::Open_State(HTTP2_Frame* frame)
{
    switch(frame->getType())
    {
        case PUSH_PROMISE_FRAME:  // doesn't send
        case HEADERS_FRAME:
        case CONTINUATION_FRAME:
        {
            printf( "Received header-like frame while in the 'open' state\n");
            break;
        }
        case DATA_FRAME:
        {
            HTTP2_Data_Frame *data = static_cast<HTTP2_Data_Frame*>(frame);
            this->processData(data);
            if(data->isEndStream())
            {
                if(this->state == HTTP2_STREAM_STATE_OPEN)
                {
                    this->state = HTTP2_STREAM_STATE_HALF_CLOSED;
                }
                else
                {
                    this->state = HTTP2_STREAM_STATE_CLOSED;
                }
                this->handleEndStream();
            }
            break;
        }
        case SETTINGS_FRAME:
        case GOAWAY_FRAME:
        case PING_FRAME:
        {
            // above frames should in 0 stream
            printf( "Unexpected frame in non-zero stream\n");
            break;
        }
        case WINDOW_UPDATE_FRAME:
        case PRIORITY_FRAME:
            break;
        case RST_STREAM_FRAME:
        {
            this->state = HTTP2_STREAM_STATE_CLOSED;
            break;
        }
        default:
            printf( "Invalid Frame Type:%d\n",frame->getType());
            break;
    }
}

void HTTP2_OrigStream::Closed_State(HTTP2_Frame* frame)
{
    // call parent's ~()
}

/******** HTTP2_RecvStream *******/
HTTP2_RecvStream::HTTP2_RecvStream(uint32_t stream_id,Table *table,bool h2c):HTTP2_HalfStream(stream_id,table,h2c)
{

}

HTTP2_RecvStream::~HTTP2_RecvStream()
{

}

void HTTP2_RecvStream::handleFrame(HTTP2_Frame* frame)
{
    switch(this->state){
        case HTTP2_STREAM_STATE_IDLE:
            this->Idle_State(frame);
            break;
        case HTTP2_STREAM_STATE_OPEN:
            this->Open_State(frame);
            break;
        case HTTP2_STREAM_STATE_HALF_CLOSED:
            this->Open_State(frame);
            break;
        case HTTP2_STREAM_STATE_CLOSED:
            this->Closed_State(frame);
            break;
        default:
            break;
    }
}

void HTTP2_RecvStream::handlePeerEndStream(void)
{
    this->peer_stream_ended = true;
    if(this->end_stream)
    {
        this->state = HTTP2_STREAM_STATE_CLOSED;
    }
}

void HTTP2_RecvStream::handleEndStream(void)
{
    this->end_stream = true;
    if(this->peer_stream_ended)
    {
        this->state = HTTP2_STREAM_STATE_CLOSED;
    }
}

void HTTP2_RecvStream::handlePushRequested(HTTP2_Frame* frame)
{
    // error
    printf( "Client sent push promise, unexpected behavior\n");
    this->state = HTTP2_STREAM_STATE_HALF_CLOSED;
    this->handleEndStream();
}

void HTTP2_RecvStream::ProcessHeaderBlock(HTTP2_Header_Frame_Base* h)
{
    uint32_t len = 0;
    uint8_t* ptr = const_cast<uint8_t*>(h->getHeaderBlock(len));
    this->processHeaders(ptr, len, this->headers);
    for(auto it : this->headers)
    {
        if((it.first).at(0) == ':')
        {
            if(it.first == ":status")
            {
                int32_t code = 0;
                try {
                    code = std::stoi(it.second);
                }
                catch (std::invalid_argument&) {
                    printf( "Invalid status code!\n");
                }
                catch (std::out_of_range&) {
                    printf( "Out of range status code!\n");
                }
                if (code < 0 || code > 999) {
                    printf( "Reply code unexpected value\n");
                }
                else
                    this->reply_status = static_cast<uint16_t>(code);
            }
        }
        else
        {
            this->extractField(it);
        }
    }
}

void HTTP2_RecvStream::Idle_State(HTTP2_Frame* frame)
{
    switch(frame->getType())
    {
        case PUSH_PROMISE_FRAME:  
        case HEADERS_FRAME:
        case CONTINUATION_FRAME:
        {
            HTTP2_Header_Frame_Base *h = static_cast<HTTP2_Header_Frame_Base*>(frame);
            this->ProcessHeaderBlock(h);

            if(h->isEndHeaders())
            {
                // get headers —— names & values
                this->state = HTTP2_STREAM_STATE_OPEN;
            }
            else
            {
                //expect continuation frame
            }

            if(h->isEndStream())
            {
                this->state = HTTP2_STREAM_STATE_HALF_CLOSED;
                this->handleEndStream();
            }
            break;
        }
        case DATA_FRAME:
            printf( "Received data frame while in the 'idle' state\n");
            break;
        case SETTINGS_FRAME:
        case GOAWAY_FRAME:
        case PING_FRAME:
        {
            // above frames should in 0 stream
            printf( "Unexpected frame in non-zero stream\n");
            break;
        }
        case WINDOW_UPDATE_FRAME:
        case PRIORITY_FRAME:
            break;
        case RST_STREAM_FRAME:
        {
            printf( "RST_STREAM received for stream in idle state\n");
            this->state = HTTP2_STREAM_STATE_CLOSED;
            break;
        }
        default:
            printf( "Invalid Frame Type:%d\n",frame->getType());
            break;
    }
}

void HTTP2_RecvStream::Open_State(HTTP2_Frame* frame)
{
    switch(frame->getType())
    {
        case PUSH_PROMISE_FRAME:  
        case CONTINUATION_FRAME:
        {
            printf( "Received header-like frame while in the 'open' state\n");
            break;
        }
        case HEADERS_FRAME:
        {
            HTTP2_Header_Frame_Base *h = static_cast<HTTP2_Header_Frame_Base*>(frame);
            this->ProcessHeaderBlock(h);
            
            if(h->isEndStream())
            {
                this->getBody(this->size,(const uint8_t *)this->buffer);
                free(this->buffer);
                this->buffer = nullptr;
                this->size = 0;

                if(this->state == HTTP2_STREAM_STATE_OPEN)
                {
                    this->state = HTTP2_STREAM_STATE_HALF_CLOSED;
                }
                else
                {
                    this->state = HTTP2_STREAM_STATE_CLOSED;
                }
                this->handleEndStream();
            }
            break;
        }
        case DATA_FRAME:
        {
            HTTP2_Data_Frame *data = static_cast<HTTP2_Data_Frame*>(frame);
            this->processData(data);
            break;
        }
        case SETTINGS_FRAME:
        case GOAWAY_FRAME:
        case PING_FRAME:
        {
            // above frames should in 0 stream
            printf( "Unexpected frame in non-zero stream\n");
            break;
        }
        case WINDOW_UPDATE_FRAME:
        case PRIORITY_FRAME:
            break;
        case RST_STREAM_FRAME:
        {
            this->state = HTTP2_STREAM_STATE_CLOSED;
            break;
        }
        default:
            printf( "Invalid Frame Type:%d\n",frame->getType());
            break;
    }
}

void HTTP2_RecvStream::Closed_State(HTTP2_Frame* frame)
{

}

/******** HTTP2_Stream *******/
HTTP2_Stream::HTTP2_Stream(uint32_t stream_id,Table *tables[2],bool h2c)
{
    this->stream_id = stream_id;
    this->stream_end = false;
    this->handling_push = false;
    this->h2c = h2c;

    this->tables[0] = tables[0];
    this->tables[1] = tables[1];

    // is_orig == 0 ,represent this is recv
    this->half_stream[0] = new HTTP2_RecvStream(stream_id,tables[0],false);
    this->half_stream[1] = new HTTP2_OrigStream(stream_id,tables[1],h2c);
    this->stream_reset = false;
    this->stream_resetter = 0;

    printf( "HTTP2 stream create!\n");
}

HTTP2_Stream::~HTTP2_Stream()
{
    delete this->half_stream[0];
    delete this->half_stream[1];

    printf( "HTTP2 stream destory!\n");
}

bool HTTP2_Stream::handleFrame(HTTP2_Frame *frame,bool orig)
{
    if(!this->handling_push)
    {
        // not push request
        if(frame->getType() == PUSH_PROMISE_FRAME)
        {
            this->handling_push = true;
            this->half_stream[!orig]->handlePushRequested(frame);
        }
        else
        {
            this->half_stream[orig]->handleFrame(frame);
        }
    }
    else
    {
        if(frame->getType() != CONTINUATION_FRAME)
        {
            // handle frame
            this->handling_push = false;
            this->half_stream[orig]->handleFrame(frame);
        }
        else
        {
            this->half_stream[!orig]->handlePushRequested(frame);
        }
    }

    if(frame->getType() == RST_STREAM_FRAME)
    {
        this->stream_reset = true;
        this->stream_resetter = orig;
    }

    if(this->half_stream[orig]->isStreamEnded())
    {
        if(h2c)
        {
            this->half_stream[orig]->handlePeerEndStream();
        }
        else
        {
            this->half_stream[!orig]->handlePeerEndStream();
        }
        
    }

    this->stream_end = this->half_stream[orig]->isClosed() && this->half_stream[!orig]->isClosed();

    printf("orig stream state:%d,recv stream state:%d\n",this->half_stream[orig]->isClosed(),this->half_stream[!orig]->isClosed());

    return this->stream_end;
}

static string& replace_all(string& str, const string& old_value, const string& new_value)
{
    while( true ) 
    {
        string::size_type pos(0);
        if( (pos=str.find(old_value))!=string::npos )
        {
            str.replace(pos,old_value.length(),new_value);
        }
        else break;
    }
    return str;
}

void HTTP2_Stream::handleStreamEnd(http2_req_info_t &req,http2_rsp_info_t &res)
{
    // request match response
    if(this->stream_end && this->half_stream[0]->isIntegrality())
    {
        size_t count = 1;
        if(!this->h2c)
        {
            // request
            std::vector<header> req_headers = this->half_stream[1]->getHeaders();
            std::string scheme = "",authority = "",path = "",method = "",req_header = "";
            for(auto it : req_headers)
            {
                if(count ++ < req_headers.size())
                {
                    req_header = req_header + "\"" + it.first + "\"" + ":" + "\"" + it.second + "\",";
                }
                else
                {
                    req_header = req_header + "\"" + it.first + "\"" + ":" + "\"" + it.second + "\"";
                }
                if(it.first == ":scheme")
                {
                    scheme = it.second;
                }
                else if(it.first == ":authority")
                {
                    authority = it.second;
                }
                else if(it.first == ":path")
                {
                    path = it.second;
                }
                else if(it.first == ":method")
                {
                    method = it.second;
                }

                req.key.push_back(it.first);
                req.value.push_back(it.second);
            }
            std::string url = scheme + "://" + authority + path;
            req.method = method;
            req.full_url = url;
            req.header = req_header;
            if(method == "POST")
            {
                std::string body = this->half_stream[1]->getData() + '\0';
                body = replace_all(body,"\n","\\n");
                req.body = body;
            }
            else
            {
                req.body = "";
            }
        }
        

        //response
        std::vector<header> res_headers = this->half_stream[0]->getHeaders();
        std::string status = "",res_header = "";
        count = 1;
        for(auto it : res_headers)
        {
            if(count++ < res_headers.size())
            {
                res_header = res_header + "\"" + it.first + "\"" + ":" + "\"" + it.second + "\",";
            }
            else
            {
                res_header = res_header + "\"" + it.first + "\"" + ":" + "\"" + it.second + "\"";
            }
            if(it.first == ":status")
            {
                status = it.second;
            }
            res.key.push_back(it.first);
            res.value.push_back(it.second);
        }
        res.status_code = std::stoi(status);
        res.header = res_header;
        std::string body = this->half_stream[0]->getData() + '\0';
        body = replace_all(body,"\n","\\n");
        res.body = body;
        
    }
    else
    {
        // continue process frame
    }
}
