syntax="proto3";
package com.quanzhi.audit_core.common.model;
option java_outer_classname = "ProtobufRawHttpEvent$$ByJProtobuf";
//
message ProtobufRawHttpEvent {  
 HttpRequest req=1;
 HttpResponse rsp=2;
 Meta meta=3;
 Net net=4;
 RawFileInfo file=5;
 UniqueId uniqueId=6;
 string charset=7;
 Source source=8;
}
//
message Meta {  
 double tm=1;
}
//
message UniqueId {  
 string eventId=1;
}
//
message HttpRequest {  
 bytes body=1;
 string remoteAddr=2;
 string url=3;
 string httpVersion=4;
 map<string, string> header=5;
 string method=6;
 int32 errCode=7;
}
//
message HttpResponse {  
 string status=1;
 string httpVersion=2;
 map<string, string> header=3;
 bytes body=4;
repeated bytes setCookiesList=5;
 int32 errCode=6;
}
//
message Source {  
 SourceTypeEnum sourceType=1;
 string taskId=2;
 string app=3;
}
enum SourceTypeEnum {  
app_har=0;
flow=1;
}
 //
message RawFileInfo {  
 string fileDirection=1;
 int32 rwFlag=2;
 int32 isIncomplete=3;
 string fileName=4;
 string fileType=5;
 int32 fileTypeReliable=6;
 int64 fileLen=7;
 string fileWarn=8;
 int32 uploadFlag=9;
 string uploadDir=10;
 string sha256=11;
}
//
message Net {  
 string srcIp=1;
 int32 srcPort=2;
 string dstIp=3;
 int32 dstPort=4;
 string flowSource=5;
 int32 vlanId=6;
}
