#ifndef HPACK_H_
#define HPACK_H_

#include <stdint.h>
#include <vector>
#include "hpack_table.h"

int64_t encodeInt(uint8_t* dst, uint32_t I, uint8_t N);
int64_t hpackEncode(uint8_t* buf, const std::vector<header> headers, bool fromsTable, bool from_dTable, bool is_huffman, Table* table, uint32_t dynamic_table_size);
int64_t decodeInt(uint32_t &dst, const uint8_t* buf, uint8_t N);
int64_t hpackDecode(std::vector<header>& headers, const uint8_t* buf, Table* table, uint32_t length);

#endif // HPACK_H_