#include <string.h>
#include <netinet/in.h>
#include<arpa/inet.h>

#include "http2_frame.h"

using namespace analyzer;

static inline uint32_t ntoh24(uint8_t* data)
{
    // Extract as is into a 32-bit integer
    uint32_t num = data[2] << 24 | data[1] << 16 | data[0] << 8;
    return ntohl(num);
}

/******** HTTP2_FrameHeader *********/
HTTP2_FrameHeader::HTTP2_FrameHeader(uint8_t* data)
{
    len = 0;
    type = HTTP2_FRAME_UNDEFINED;
    flags = 0;
    streamId = 0;

    RawFrameHeader* fh = reinterpret_cast<RawFrameHeader*>(data);

    // Parse Frame Length
    this->len = ntoh24(fh->len);
    // Parse Type
    this->type = fh->type;
    // Parse Flags
    this->flags = fh->flags;
    // Parse Stream ID, reverse endianess
    uint8_t* sid = fh->streamId;
    this->streamId = ntohl(*reinterpret_cast<uint32_t*>(sid)) & 0x7FFFFFFF;// mask off Reserved bitfield
}

bool HTTP2_FrameHeader::isEndHeaders(void)
{
    return (this->flags & END_HEADERS_FLAG) != 0;
}

bool HTTP2_FrameHeader::isEndStream(void)
{
    return (this->flags & END_STREAM_FLAG) != 0;
}

bool HTTP2_FrameHeader::isPadded(void)
{
    return (this->flags & PADDED_FLAG) != 0;
}

bool HTTP2_FrameHeader::isPriority(void)
{
    return (this->flags & PRIORITY_FLAG) != 0;
}

bool HTTP2_FrameHeader::isAck(void)
{
    return (this->flags & ACK_FLAG) != 0;
}

/******** HTTP2_Frame **********/

HTTP2_Frame::HTTP2_Frame(HTTP2_FrameHeader* h)
{
    this->header = h;
    this->valid = false;
}

HTTP2_Frame::~HTTP2_Frame(void)
{
    this->header = nullptr;
}

bool HTTP2_Frame::checkPadding(uint8_t* payload, uint32_t len, uint8_t &padLength)
{
    if((len > 0) && (this->header->isPadded())) {
        padLength = payload[0];
        return true;
    }
    return false;
}

/*
** Utility
*/
/**
 * const char* HTTP2_Frame::errorToText(uint32_t error)
 *
 * Description: Convert header decompression error code into
 * ASCII string for display.
 *
 *
 * @param error the error code
 *
 * @return const char*
 */
const std::string HTTP2_Frame::errorToText(uint32_t error)
{
    std::string s = "NO_ERROR";
    switch (error) {
    case  NO_ERROR:
        s = "NO_ERROR";
        break;
    case  PROTOCOL_ERROR:
        s = "PROTOCOL_ERROR";
        break;
    case  INTERNAL_ERROR:
        s = "INTERNAL_ERROR";
        break;
    case  FLOW_CONTROL_ERROR:
        s = "FLOW_CONTROL_ERROR";
        break;
    case  SETTINGS_TIMEOUT:
        s = "SETTINGS_TIMEOUT";
        break;
    case  STREAM_CLOSED:
        s = "STREAM_CLOSED";
        break;
    case  FRAME_SIZE_ERROR:
        s = "FRAME_SIZE_ERROR";
        break;
    case  REFUSED_STREAM:
        s = "REFUSED_STREAM";
        break;
    case  CANCEL:
        s = "CANCEL";
        break;
    case  COMPRESSION_ERROR:
        s = "COMPRESSION_ERROR";
        break;
    case  CONNECT_ERROR:
        s = "CONNECT_ERROR";
        break;
    case  ENHANCE_YOUR_CALM:
        s = "ENHANCE_YOUR_CALM";
        break;
    case  INADEQUATE_SECURITY:
        s = "INADEQUATE_SECURITY";
        break;
    case  HTTP_1_1_REQUIRED:
        s = "HTTP_1_1_REQUIRED";
        break;
    default:
        break;
    }

    return s;

}

/********* HTTP2_DATA_FRAME ********/
HTTP2_Data_Frame::HTTP2_Data_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len)
: HTTP2_Frame(h)
{
    this->dataMsg = nullptr;
    this->dataLength = 0;

    if(this->header->getLen() != len){ //Not provided enough information
        return;
    }

    uint8_t padLength = 0;
    uint8_t* cursor = payload;
    if (this->checkPadding(payload, len, padLength)){
        //Add the padding field itself
        padLength += 1;
        cursor += 1;
    }

    if (padLength > this->header->getLen()){ // Padding too much
        return;
    }

    this->dataLength = this->header->getLen() - padLength;
    this->dataMsg = new uint8_t[this->dataLength];
    if (!this->dataMsg){// allocation error?
        return;
    }

    memcpy(this->dataMsg, cursor, this->dataLength);

    this->valid = true;
}

HTTP2_Data_Frame::~HTTP2_Data_Frame(void)
{
    if(this->dataMsg)
    {
        delete[] this->dataMsg;
        this->dataMsg = nullptr;
    }
}

const uint8_t* HTTP2_Data_Frame::getData(uint32_t& len)
{
    if(this->dataMsg)
    {
        len = this->dataLength;
        return (const uint8_t*)(this->dataMsg);
    }
    else
    {
        len = 0;
        return nullptr;
    }
}

/********* Header Base Class *********/
HTTP2_Header_Frame_Base::HTTP2_Header_Frame_Base(HTTP2_FrameHeader* h):HTTP2_Frame(h)
{
    this->headerBlock = nullptr;
    this->headerBlockLen = 0;
}

HTTP2_Header_Frame_Base::~HTTP2_Header_Frame_Base(void)
{
    if(this->headerBlock)
    {
        delete[] this->headerBlock;
        this->headerBlock = nullptr;
    }
}

const uint8_t* HTTP2_Header_Frame_Base::getHeaderBlock(uint32_t& len)
{
    if(this->headerBlock)
    {
        len = headerBlockLen;
        return (const uint8_t*)(this->headerBlock);
    }
    else
    {
        len = 0;
        return nullptr;
    }
}

/******** HTTP2_Header_Frame ********/
HTTP2_Header_Frame::HTTP2_Header_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Header_Frame_Base(h)
{
    if(this->header->getLen()!=len)
    {
        return;
    }

    uint8_t padLength = 0;
    uint8_t* cursor = payload;
    if(this->checkPadding(payload,len,padLength))
    {
        //Add the padding field itself
        padLength += 1;
        cursor += 1;
    }

    if(this->header->isPriority())
    {
        // Add extra fields to the pad length
        cursor += 5;
        padLength += 5;
    }

    if(padLength>len) // invalid
    {
        return;
    }

    this->headerBlockLen = len - padLength;
    this->headerBlock = new uint8_t[this->headerBlockLen];

    if(!this->headerBlock)
    {
        return;
    }

    memcpy(this->headerBlock,cursor,this->headerBlockLen);
    this->valid = true;
}

HTTP2_Header_Frame::~HTTP2_Header_Frame(void)
{
    // Parent Header_Frame_Base takes care of deleting header block structure
}

/******** HTTP2_Priority_Frame ********/
HTTP2_Priority_Frame::HTTP2_Priority_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Frame(h)
{
    if(this->header->getLen() != len)
    {
        return;
    }

    if(len != 5)   // Priority frames must be 5 bytes
    {
        return;
    }

    this->dependentStream = ntohl(*reinterpret_cast<uint32_t*>(payload));
    this->exclusive =((this->dependentStream & 0x80000000) != 0 );
    this->dependentStream &= 0x7FFFFFFF;
    this->weight = *(payload+4);

    this->valid = true;
}

HTTP2_Priority_Frame::~HTTP2_Priority_Frame(void)
{

}

/******** HTTP2_RstStream_Frame *********/
HTTP2_RstStream_Frame::HTTP2_RstStream_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Frame(h)
{
    if(this->header->getLen() != len)
    {
        return;
    }

    if(len != 4) // RST_STREAM frames must be 4 bytes
    {
        return;
    }

    this->errorCode = ntohl(*reinterpret_cast<uint32_t*>(payload));

    this->valid = true;
}

HTTP2_RstStream_Frame::~HTTP2_RstStream_Frame(void)
{

}

/******** HTTP2_Settings_Frame ******/
HTTP2_Settings_Frame::HTTP2_Settings_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Frame(h)
{
    if(this->header->getLen() != len)
    {
        return;
    }

    if(len % 6 != 0) // settings frames must have a length divisible by 6
    {
        return;
    }

    this->header_table_size_set = false;
    this->enable_push_set = false;
    this->max_concurrent_streams_set = false;
    this->initial_window_size_set = false;
    this->max_frame_size_set = false;
    this->max_header_list_size_set = false;
    this->unrecognized_settings = false;

    uint16_t Identifier;
    uint32_t value;
    uint8_t* cursor = payload;
    uint32_t setlen = len;

    while(setlen)
    {
        // loop fetch settings
        Identifier = ntohs(*reinterpret_cast<uint16_t*>(cursor));
        value = ntohl(*reinterpret_cast<uint32_t*>(cursor+2));
        cursor +=6;
        setlen -= 6;

        switch(Identifier)
        {
            case SETTINGS_HEADER_TABLE_SIZE:
                this->header_table_size_set = true;
                this->header_table_size = value;
                break;
            case SETTINGS_ENABLE_PUSH:
                this->enable_push_set = true;
                this->enable_push = value;
                break;
            case SETTINGS_MAX_CONCURRENT_STREAMS:
                this->max_concurrent_streams_set = true;
                this->max_concurrent_streams = value;
                break;
            case SETTINGS_INITIAL_WINDOW_SIZE:
                this->initial_window_size_set = true;
                this->initial_window_size = value;
                break;
            case SETTINGS_MAX_FRAME_SIZE:
                this->max_frame_size_set = true;
                this->max_frame_size = value;
                break;
            case SETTINGS_MAX_HEADER_LIST_SIZE:
                this->max_header_list_size_set = true;
                this->max_header_list_size = value;
                break;
            default:
                this->unrecognized_settings = true;
                this->unrec_settings.push_back(std::pair<uint16_t, uint32_t>(Identifier, value));
                break;
        }
    }

    this->valid = true;
}

HTTP2_Settings_Frame::~HTTP2_Settings_Frame(void)
{

}

bool HTTP2_Settings_Frame::getHeaderTableSize(uint32_t& size)
{
    if(this->header_table_size_set)
    {
        size = this->header_table_size;
    }
    else
    {
        size = 0;
    }

    return this->header_table_size_set;
}

bool HTTP2_Settings_Frame::getEnablePush(uint32_t& push)
{
    if(this->enable_push_set)
    {
        push = this->enable_push;
    }
    else
    {
        push = 0;
    }

    return this->enable_push_set;
}

bool HTTP2_Settings_Frame::getMaxConcurrentStreams(uint32_t& streams)
{
    if(this->max_concurrent_streams_set)
    {
        streams = this->max_concurrent_streams;
    }
    else
    {
        streams = 0;
    }

    return this->max_concurrent_streams_set;
}

bool HTTP2_Settings_Frame::getInitialWindowSize(uint32_t& size)
{
    if(this->initial_window_size_set)
    {
        size = this->initial_window_size;
    }
    else
    {
        size = 0;
    }

    return this->initial_window_size_set;
}

bool HTTP2_Settings_Frame::getMaxFrameSize(uint32_t& size)
{
    if(this->max_frame_size_set)
    {
        size = this->max_frame_size;
    }
    else
    {
        size = 0;
    }

    return this->max_frame_size_set;
}

bool HTTP2_Settings_Frame::getMaxHeaderListSize(uint32_t& size)
{
    if(this->max_header_list_size_set)
    {
        size = this->max_header_list_size;
    }
    else
    {
        size = 0;
    }

    return this->max_header_list_size_set;
}

/********** HTTP2_PushPromise_Frame *********/
HTTP2_PushPromise_Frame::HTTP2_PushPromise_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Header_Frame_Base(h)
{
    if(this->header->getLen() != len)
    {
        return;
    }

    uint8_t padLength = 0;
    uint8_t* cursor = payload;
    if(this->checkPadding(payload,len,padLength))
    {
        // Add padding field itself
        padLength += 1;
        cursor += 1;
    }

    // Grab promised stream id
    this->promisedStream = ntohl(*reinterpret_cast<uint32_t*>(cursor)) & 0x7FFFFFFF;
    padLength += 4;
    cursor += 4;

    if(padLength > len)
    {
        return;
    }

    this->headerBlockLen = len - padLength;
    this->headerBlock = new uint8_t[this->headerBlockLen];
    if(!this->headerBlock)
    {
        return;
    }

    memcpy(this->headerBlock,cursor,this->headerBlockLen);

    this->valid = true;
    
}

HTTP2_PushPromise_Frame::~HTTP2_PushPromise_Frame(void)
{

}

/********** HTTP2_Ping_Frame *********/
HTTP2_Ping_Frame::HTTP2_Ping_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Frame(h)
{
    if(this->header->getLen() != len)
    {
        return;
    }

    if(len != 8) // Ping frame len must be 8 bytes
    {
        return;
    }

    memcpy(this->data,payload,PING_OPAQUE_DATA_LENGTH);

    this->valid = true;
}

HTTP2_Ping_Frame::~HTTP2_Ping_Frame(void)
{

}

/********** HTTP2_GoAway_Frame *********/
HTTP2_GoAway_Frame::HTTP2_GoAway_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Frame(h)
{
    this->debugData = nullptr;
    this->debugDataLength = 0;

    if(this->header->getLen() != len)
    {
        return;
    }

    if(len < 8) // goaway frame must greater than 8 bytes
    {
        return;
    }

    this->lastStreamId = ntohl(*reinterpret_cast<uint32_t*>(payload)) & 0x7FFFFFFF;
    this->errorCode = ntohl(*reinterpret_cast<uint32_t*>(payload + 4));
    
    if(len > 8)
    {
        this->debugDataLength = len - 8;
        this->debugData = new uint8_t[this->debugDataLength];
        if(!this->debugData)
        {
            return;
        }

        memcpy(this->debugData,payload+8,this->debugDataLength);
    }

    this->valid = true;

}

HTTP2_GoAway_Frame::~HTTP2_GoAway_Frame(void)
{
    if(this->debugData)
    {
        delete[] this->debugData;
        this->debugData = nullptr;
    }
}

const uint8_t* HTTP2_GoAway_Frame::getDebugData(uint32_t& len)
{
    if(this->debugData)
    {
        len = this->debugDataLength;
        return (const uint8_t*)this->debugData;
    }
    else
    {
        len = 0;
        return nullptr;
    }
}

/********** HTTP2_WindowUpdate_Frame *********/
HTTP2_WindowUpdate_Frame::HTTP2_WindowUpdate_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len):HTTP2_Frame(h)
{
    if(this->header->getLen() != len)
    {
        return;
    }

    if(len != 4) // windowUpdate frame must be 4 bytes
    {
        return;
    }

    this->sizeIncrement = ntohl(*reinterpret_cast<uint32_t*>(payload)) & 0x7FFFFFFF;

    this->valid = true;
}

HTTP2_WindowUpdate_Frame::~HTTP2_WindowUpdate_Frame(void)
{

}

/********** HTTP2_Continuation_Frame *********/
HTTP2_Continuation_Frame::HTTP2_Continuation_Frame(HTTP2_FrameHeader* h, uint8_t* payload, uint32_t len)
:HTTP2_Header_Frame(h, payload, len)
{
    // Parent Header_Frame type should take care of everything
}

HTTP2_Continuation_Frame::~HTTP2_Continuation_Frame(void)
{
}