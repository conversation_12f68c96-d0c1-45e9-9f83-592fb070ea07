#ifndef HTTP2_H
#define HTTP2_H

#include <string>
#include <map>
#include <sys/time.h>
#include "http2_frame.h"
#include "http2_frameReassembler.h"
#include "http2_stream.h"

namespace analyzer{ namespace submodule{

static constexpr size_t STREAM_MAX_NUM = 10000;     // max num

static constexpr size_t TIME_OUT = 3;               // 3 seconds    

class HTTP2_Stream;
class HTTP2_HalfStream;

class HTTP2_Analyzer{
public:
    HTTP2_Analyzer();
    virtual ~HTTP2_Analyzer();

    int deliverStream(int len,const uint8_t *data,bool orig,http2_req_info_t &req,http2_rsp_info_t &res,bool h2c);
    //bool isHttp2Connection(int len,const uint8_t *data);
private:
    // stream management
    void initStreams(void);
    void destroyStreams(void);
    HTTP2_Stream* getStream(uint32_t stream_id, bool orig,bool h2c);
    void removeStream(HTTP2_Stream* s);

    // packet fragmentation management
    void initReassemblers(void);
    void destroyReassemblers(void);

    // decompression
    void initTables(void);
    void destoryTables(void);

    // Stream 0 functions
    void handleStream0(HTTP2_Frame* frame);
    void handleSettings(HTTP2_Settings_Frame* frame);
    void handleGoAway(HTTP2_GoAway_Frame* frame);
    void handlePing(HTTP2_Ping_Frame* frame);
    void handleWindowUpdate(HTTP2_WindowUpdate_Frame* frame);

    // check stream state
    bool checkStreams();
    void addStreamTimer();

private:
    Table *tables[2];               // headers dynamic table,both client and server have one
    HTTP2_FrameReassembler *reassemblers;
    std::map<uint32_t,HTTP2_Stream*> streams;

    // connection state
    uint32_t last_stream_id;    // get from goaway frame
    uint32_t last_streams[2];   // record the lastest established stream id ,sequential
         
    uint32_t discard_num;       // discard num if the stream num greater than STREAM_MAX_NUM
};    

} }

#endif //HTTP2_H
