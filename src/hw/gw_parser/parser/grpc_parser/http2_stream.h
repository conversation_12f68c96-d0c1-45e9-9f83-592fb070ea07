#ifndef HTTP2_STREAM_H
#define HTTP2_STREAM_H

#include <zlib.h>
#include <sys/time.h>
#include <vector>
#include "HPACK.h"
#include "decode.h"
#include "http2_frame.h"

namespace analyzer { namespace submodule{

static constexpr size_t BROTLI_BUFFER_SIZE = 102400; // 100KB

enum StreamState {
    // Stream States
    HTTP2_STREAM_STATE_IDLE,
    HTTP2_STREAM_STATE_OPEN,
    HTTP2_STREAM_STATE_HALF_CLOSED,
    HTTP2_STREAM_STATE_CLOSED
};

enum DataEncoding {
    DATA_ENCODING_IDENTITY,
    DATA_ENCODING_AES128GCM,
    DATA_ENCODING_BROTLI,
    DATA_ENCODING_COMPRESS,
    DATA_ENCODING_DEFLATE,
    DATA_ENCODING_EXI,
    DATA_ENCODING_GZIP,
    DATA_ENCODING_PACK200GZIP
};

typedef struct http2_req_info
{
    std::string method;             /* 方法 */
    char remote_addr[64];        /* 源IP */
    std::string full_url; 
    std::string header;             /* 用于存放header */
    std::string body;               /* 用于存放body */
    std::vector<std::string> key;
    std::vector<std::string> value;
}http2_req_info_t;

typedef struct http2_rsp_info
{
    unsigned status_code;
    std::string header;
    std::string body;
    std::vector<std::string> key;
    std::vector<std::string> value;
}http2_rsp_info_t;

typedef struct http2_meta_info
{
    double ts;
}http2_meta_info_t;

typedef struct net_info
{
    unsigned short src_port;
    unsigned short dst_port;
    char a_src_ip[64];
    char a_dst_ip[64];
}net_info_t;

typedef struct upload_http2_info
{
    http2_req_info_t http2_req_info;
    http2_rsp_info_t http2_rsp_info;
    http2_meta_info_t http2_meta_info;
    net_info_t http2_net_info;
    char a_unique_id[128];
}upload_http2_info_t;

/**
 * Class HalfStream 
 * Description：This class represents common/shared functionality
 * between client and server sides including processing of headers
 * and data.
*/
class HTTP2_HalfStream{
public:
    HTTP2_HalfStream(uint32_t stream_id,Table *table,bool h2c);
    virtual ~HTTP2_HalfStream();

    virtual void handleFrame(HTTP2_Frame* frame) = 0;
    virtual void handlePeerEndStream(void) = 0;
    virtual void handlePushRequested(HTTP2_Frame* frame) = 0;

    bool isStreamEnded(void){return this->end_stream;};
    bool isClosed(void){return this->state == HTTP2_STREAM_STATE_CLOSED;};
    size_t getDataSize(void) const {return this->data_size;};
    bool isIntegrality(void){return this->integrality;};

    char *getData(void){return this->data_buffer;};
    std::vector<header>& getHeaders(void){return this->headers;};

protected:
    // decompress headers frame block fragment
    void processHeaders(uint8_t* header_block_fragment_ptr, uint32_t len, std::vector<header> &headers);

    void processData(HTTP2_Data_Frame* data);

    // get data encoding type
    void parseContentEncoding(std::string& s);

    void extractField(header h);

    // decompress data payload
    void getBody(int len, const uint8_t* data);

    // use gzip or deflate to decompress
    void translateZipBody(int len, const uint8_t* data);

    // use br to decompress
    void translateBrotliBody(int len, const uint8_t* data);

protected:
    // stream
    uint32_t stream_id;
    StreamState state;        

    // data management
    char *data_buffer;      // decompress
    size_t data_size;
    uint8_t *buffer;           // compress
    size_t size;
    z_stream* zip;
    BrotliDecoderState* brotli;
    uint8_t* brotli_buffer;

    // headers field
    int content_length;
    DataEncoding content_encoding_id;
    std::string content_encoding;
    std::string content_type;

    bool end_stream;
    bool peer_stream_ended;

    bool integrality;       // check integrality

    Table *table;           // headers dynamic table

    // name & value
    std::vector<header> headers;

};

// originator
class HTTP2_OrigStream : public HTTP2_HalfStream{
public:
    HTTP2_OrigStream(uint32_t stream_id,Table *table,bool h2c);
    ~HTTP2_OrigStream();

    // Perform state processing on a client frame
    void handleFrame(HTTP2_Frame* frame);

    // Process notification of peer stream ending
    void handlePeerEndStream(void);

    // change state
    void handlePushRequested(HTTP2_Frame* frame);
private:
    void Idle_State(HTTP2_Frame* frame);
    void Open_State(HTTP2_Frame* frame);
    void Closed_State(HTTP2_Frame* frame);
    void ProcessHeaderBlock(HTTP2_Header_Frame_Base* h);
    void handleEndStream(void);

    // Pseudo Headers ,start with ":"
    std::string request_method;
    std::string request_authority;
    std::string request_host;
    std::string request_path;
    std::string request_scheme;
};

// receiver
class HTTP2_RecvStream : public HTTP2_HalfStream{
public:
    HTTP2_RecvStream(uint32_t stream_id,Table *table,bool h2c);
    ~HTTP2_RecvStream();

    // Perform state processing on a server frame
    void handleFrame(HTTP2_Frame* frame);

    // Process notification of peer stream ending
    void handlePeerEndStream(void);

    // change state
    void handlePushRequested(HTTP2_Frame* frame);
private:
    void Idle_State(HTTP2_Frame* frame);
    void Open_State(HTTP2_Frame* frame);
    void Closed_State(HTTP2_Frame* frame);
    void ProcessHeaderBlock(HTTP2_Header_Frame_Base* h);
    void handleEndStream(void);

    // Pseudo Headers,,start with ":"
    uint16_t reply_status;
};

class HTTP2_Stream{
public:
    HTTP2_Stream(uint32_t stream_id,Table *tables[2],bool h2c);
    ~HTTP2_Stream();

    uint32_t getStreamId(void){return this->stream_id;};

    bool handleFrame(HTTP2_Frame *frame,bool orig);

    void handleStreamEnd(http2_req_info_t &req,http2_rsp_info_t &res);

    void setBeginTime(time_t time){this->begin_time = time;};

    time_t getBeginTime(){return this->begin_time;};

private:
    uint32_t stream_id;
    bool stream_end;
    bool handling_push;     // push request
    bool stream_reset;      // is reset?
    bool stream_resetter;   // who send Rst_Frame
    bool h2c;

    time_t begin_time;          // no process begin time (s)

    HTTP2_HalfStream *half_stream[2];
    Table *tables[2];
};

} } //namespace analyzer::*

#endif//HTTP2_STREAM_H