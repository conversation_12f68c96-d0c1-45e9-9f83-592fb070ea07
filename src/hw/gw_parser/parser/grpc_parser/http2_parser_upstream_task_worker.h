/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __HTTP2_PARSER_UPSTREAM_TASK_WORKER_H__
#define __HTTP2_PARSER_UPSTREAM_TASK_WORKER_H__

#include "http2_parser_task_worker.h"

typedef struct http2_upstream_data
{
  TaskWorkerData twd;

  size_t length;
  char *s;

} http2_upstream_data_t;

class CTaskWorkerUpstream : public CTaskWorkerGrpc
{
public:
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);
};

#endif // __HTTP2_PARSER_UPSTREAM_TASK_WORKER_H__
