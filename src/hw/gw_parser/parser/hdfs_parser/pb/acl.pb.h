// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: acl.proto

#ifndef PROTOBUF_acl_2eproto__INCLUDED
#define PROTOBUF_acl_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_acl_2eproto();
void protobuf_AssignDesc_acl_2eproto();
void protobuf_ShutdownFile_acl_2eproto();

class AclEntryProto;
class AclStatusProto;
class AclEditLogProto;
class ModifyAclEntriesRequestProto;
class ModifyAclEntriesResponseProto;
class RemoveAclRequestProto;
class RemoveAclResponseProto;
class RemoveAclEntriesRequestProto;
class RemoveAclEntriesResponseProto;
class RemoveDefaultAclRequestProto;
class RemoveDefaultAclResponseProto;
class SetAclRequestProto;
class SetAclResponseProto;
class GetAclStatusRequestProto;
class GetAclStatusResponseProto;

enum AclEntryProto_AclEntryScopeProto {
  AclEntryProto_AclEntryScopeProto_ACCESS = 0,
  AclEntryProto_AclEntryScopeProto_DEFAULT = 1
};
bool AclEntryProto_AclEntryScopeProto_IsValid(int value);
const AclEntryProto_AclEntryScopeProto AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_MIN = AclEntryProto_AclEntryScopeProto_ACCESS;
const AclEntryProto_AclEntryScopeProto AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_MAX = AclEntryProto_AclEntryScopeProto_DEFAULT;
const int AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_ARRAYSIZE = AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* AclEntryProto_AclEntryScopeProto_descriptor();
inline const ::std::string& AclEntryProto_AclEntryScopeProto_Name(AclEntryProto_AclEntryScopeProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    AclEntryProto_AclEntryScopeProto_descriptor(), value);
}
inline bool AclEntryProto_AclEntryScopeProto_Parse(
    const ::std::string& name, AclEntryProto_AclEntryScopeProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<AclEntryProto_AclEntryScopeProto>(
    AclEntryProto_AclEntryScopeProto_descriptor(), name, value);
}
enum AclEntryProto_AclEntryTypeProto {
  AclEntryProto_AclEntryTypeProto_USER = 0,
  AclEntryProto_AclEntryTypeProto_GROUP = 1,
  AclEntryProto_AclEntryTypeProto_MASK = 2,
  AclEntryProto_AclEntryTypeProto_OTHER = 3
};
bool AclEntryProto_AclEntryTypeProto_IsValid(int value);
const AclEntryProto_AclEntryTypeProto AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_MIN = AclEntryProto_AclEntryTypeProto_USER;
const AclEntryProto_AclEntryTypeProto AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_MAX = AclEntryProto_AclEntryTypeProto_OTHER;
const int AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_ARRAYSIZE = AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* AclEntryProto_AclEntryTypeProto_descriptor();
inline const ::std::string& AclEntryProto_AclEntryTypeProto_Name(AclEntryProto_AclEntryTypeProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    AclEntryProto_AclEntryTypeProto_descriptor(), value);
}
inline bool AclEntryProto_AclEntryTypeProto_Parse(
    const ::std::string& name, AclEntryProto_AclEntryTypeProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<AclEntryProto_AclEntryTypeProto>(
    AclEntryProto_AclEntryTypeProto_descriptor(), name, value);
}
enum AclEntryProto_FsActionProto {
  AclEntryProto_FsActionProto_NONE = 0,
  AclEntryProto_FsActionProto_EXECUTE = 1,
  AclEntryProto_FsActionProto_WRITE = 2,
  AclEntryProto_FsActionProto_WRITE_EXECUTE = 3,
  AclEntryProto_FsActionProto_READ = 4,
  AclEntryProto_FsActionProto_READ_EXECUTE = 5,
  AclEntryProto_FsActionProto_READ_WRITE = 6,
  AclEntryProto_FsActionProto_PERM_ALL = 7
};
bool AclEntryProto_FsActionProto_IsValid(int value);
const AclEntryProto_FsActionProto AclEntryProto_FsActionProto_FsActionProto_MIN = AclEntryProto_FsActionProto_NONE;
const AclEntryProto_FsActionProto AclEntryProto_FsActionProto_FsActionProto_MAX = AclEntryProto_FsActionProto_PERM_ALL;
const int AclEntryProto_FsActionProto_FsActionProto_ARRAYSIZE = AclEntryProto_FsActionProto_FsActionProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* AclEntryProto_FsActionProto_descriptor();
inline const ::std::string& AclEntryProto_FsActionProto_Name(AclEntryProto_FsActionProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    AclEntryProto_FsActionProto_descriptor(), value);
}
inline bool AclEntryProto_FsActionProto_Parse(
    const ::std::string& name, AclEntryProto_FsActionProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<AclEntryProto_FsActionProto>(
    AclEntryProto_FsActionProto_descriptor(), name, value);
}
// ===================================================================

class AclEntryProto : public ::google::protobuf::Message {
 public:
  AclEntryProto();
  virtual ~AclEntryProto();

  AclEntryProto(const AclEntryProto& from);

  inline AclEntryProto& operator=(const AclEntryProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AclEntryProto& default_instance();

  void Swap(AclEntryProto* other);

  // implements Message ----------------------------------------------

  AclEntryProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AclEntryProto& from);
  void MergeFrom(const AclEntryProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef AclEntryProto_AclEntryScopeProto AclEntryScopeProto;
  static const AclEntryScopeProto ACCESS = AclEntryProto_AclEntryScopeProto_ACCESS;
  static const AclEntryScopeProto DEFAULT = AclEntryProto_AclEntryScopeProto_DEFAULT;
  static inline bool AclEntryScopeProto_IsValid(int value) {
    return AclEntryProto_AclEntryScopeProto_IsValid(value);
  }
  static const AclEntryScopeProto AclEntryScopeProto_MIN =
    AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_MIN;
  static const AclEntryScopeProto AclEntryScopeProto_MAX =
    AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_MAX;
  static const int AclEntryScopeProto_ARRAYSIZE =
    AclEntryProto_AclEntryScopeProto_AclEntryScopeProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  AclEntryScopeProto_descriptor() {
    return AclEntryProto_AclEntryScopeProto_descriptor();
  }
  static inline const ::std::string& AclEntryScopeProto_Name(AclEntryScopeProto value) {
    return AclEntryProto_AclEntryScopeProto_Name(value);
  }
  static inline bool AclEntryScopeProto_Parse(const ::std::string& name,
      AclEntryScopeProto* value) {
    return AclEntryProto_AclEntryScopeProto_Parse(name, value);
  }

  typedef AclEntryProto_AclEntryTypeProto AclEntryTypeProto;
  static const AclEntryTypeProto USER = AclEntryProto_AclEntryTypeProto_USER;
  static const AclEntryTypeProto GROUP = AclEntryProto_AclEntryTypeProto_GROUP;
  static const AclEntryTypeProto MASK = AclEntryProto_AclEntryTypeProto_MASK;
  static const AclEntryTypeProto OTHER = AclEntryProto_AclEntryTypeProto_OTHER;
  static inline bool AclEntryTypeProto_IsValid(int value) {
    return AclEntryProto_AclEntryTypeProto_IsValid(value);
  }
  static const AclEntryTypeProto AclEntryTypeProto_MIN =
    AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_MIN;
  static const AclEntryTypeProto AclEntryTypeProto_MAX =
    AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_MAX;
  static const int AclEntryTypeProto_ARRAYSIZE =
    AclEntryProto_AclEntryTypeProto_AclEntryTypeProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  AclEntryTypeProto_descriptor() {
    return AclEntryProto_AclEntryTypeProto_descriptor();
  }
  static inline const ::std::string& AclEntryTypeProto_Name(AclEntryTypeProto value) {
    return AclEntryProto_AclEntryTypeProto_Name(value);
  }
  static inline bool AclEntryTypeProto_Parse(const ::std::string& name,
      AclEntryTypeProto* value) {
    return AclEntryProto_AclEntryTypeProto_Parse(name, value);
  }

  typedef AclEntryProto_FsActionProto FsActionProto;
  static const FsActionProto NONE = AclEntryProto_FsActionProto_NONE;
  static const FsActionProto EXECUTE = AclEntryProto_FsActionProto_EXECUTE;
  static const FsActionProto WRITE = AclEntryProto_FsActionProto_WRITE;
  static const FsActionProto WRITE_EXECUTE = AclEntryProto_FsActionProto_WRITE_EXECUTE;
  static const FsActionProto READ = AclEntryProto_FsActionProto_READ;
  static const FsActionProto READ_EXECUTE = AclEntryProto_FsActionProto_READ_EXECUTE;
  static const FsActionProto READ_WRITE = AclEntryProto_FsActionProto_READ_WRITE;
  static const FsActionProto PERM_ALL = AclEntryProto_FsActionProto_PERM_ALL;
  static inline bool FsActionProto_IsValid(int value) {
    return AclEntryProto_FsActionProto_IsValid(value);
  }
  static const FsActionProto FsActionProto_MIN =
    AclEntryProto_FsActionProto_FsActionProto_MIN;
  static const FsActionProto FsActionProto_MAX =
    AclEntryProto_FsActionProto_FsActionProto_MAX;
  static const int FsActionProto_ARRAYSIZE =
    AclEntryProto_FsActionProto_FsActionProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  FsActionProto_descriptor() {
    return AclEntryProto_FsActionProto_descriptor();
  }
  static inline const ::std::string& FsActionProto_Name(FsActionProto value) {
    return AclEntryProto_FsActionProto_Name(value);
  }
  static inline bool FsActionProto_Parse(const ::std::string& name,
      FsActionProto* value) {
    return AclEntryProto_FsActionProto_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.AclEntryProto.AclEntryTypeProto type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::hadoop::hdfs::AclEntryProto_AclEntryTypeProto type() const;
  inline void set_type(::hadoop::hdfs::AclEntryProto_AclEntryTypeProto value);

  // required .hadoop.hdfs.AclEntryProto.AclEntryScopeProto scope = 2;
  inline bool has_scope() const;
  inline void clear_scope();
  static const int kScopeFieldNumber = 2;
  inline ::hadoop::hdfs::AclEntryProto_AclEntryScopeProto scope() const;
  inline void set_scope(::hadoop::hdfs::AclEntryProto_AclEntryScopeProto value);

  // required .hadoop.hdfs.AclEntryProto.FsActionProto permissions = 3;
  inline bool has_permissions() const;
  inline void clear_permissions();
  static const int kPermissionsFieldNumber = 3;
  inline ::hadoop::hdfs::AclEntryProto_FsActionProto permissions() const;
  inline void set_permissions(::hadoop::hdfs::AclEntryProto_FsActionProto value);

  // optional string name = 4;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 4;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.AclEntryProto)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_scope();
  inline void clear_has_scope();
  inline void set_has_permissions();
  inline void clear_has_permissions();
  inline void set_has_name();
  inline void clear_has_name();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int type_;
  int scope_;
  ::std::string* name_;
  int permissions_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static AclEntryProto* default_instance_;
};
// -------------------------------------------------------------------

class AclStatusProto : public ::google::protobuf::Message {
 public:
  AclStatusProto();
  virtual ~AclStatusProto();

  AclStatusProto(const AclStatusProto& from);

  inline AclStatusProto& operator=(const AclStatusProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AclStatusProto& default_instance();

  void Swap(AclStatusProto* other);

  // implements Message ----------------------------------------------

  AclStatusProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AclStatusProto& from);
  void MergeFrom(const AclStatusProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string owner = 1;
  inline bool has_owner() const;
  inline void clear_owner();
  static const int kOwnerFieldNumber = 1;
  inline const ::std::string& owner() const;
  inline void set_owner(const ::std::string& value);
  inline void set_owner(const char* value);
  inline void set_owner(const char* value, size_t size);
  inline ::std::string* mutable_owner();
  inline ::std::string* release_owner();
  inline void set_allocated_owner(::std::string* owner);

  // required string group = 2;
  inline bool has_group() const;
  inline void clear_group();
  static const int kGroupFieldNumber = 2;
  inline const ::std::string& group() const;
  inline void set_group(const ::std::string& value);
  inline void set_group(const char* value);
  inline void set_group(const char* value, size_t size);
  inline ::std::string* mutable_group();
  inline ::std::string* release_group();
  inline void set_allocated_group(::std::string* group);

  // required bool sticky = 3;
  inline bool has_sticky() const;
  inline void clear_sticky();
  static const int kStickyFieldNumber = 3;
  inline bool sticky() const;
  inline void set_sticky(bool value);

  // repeated .hadoop.hdfs.AclEntryProto entries = 4;
  inline int entries_size() const;
  inline void clear_entries();
  static const int kEntriesFieldNumber = 4;
  inline const ::hadoop::hdfs::AclEntryProto& entries(int index) const;
  inline ::hadoop::hdfs::AclEntryProto* mutable_entries(int index);
  inline ::hadoop::hdfs::AclEntryProto* add_entries();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
      entries() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
      mutable_entries();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.AclStatusProto)
 private:
  inline void set_has_owner();
  inline void clear_has_owner();
  inline void set_has_group();
  inline void clear_has_group();
  inline void set_has_sticky();
  inline void clear_has_sticky();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* owner_;
  ::std::string* group_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto > entries_;
  bool sticky_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static AclStatusProto* default_instance_;
};
// -------------------------------------------------------------------

class AclEditLogProto : public ::google::protobuf::Message {
 public:
  AclEditLogProto();
  virtual ~AclEditLogProto();

  AclEditLogProto(const AclEditLogProto& from);

  inline AclEditLogProto& operator=(const AclEditLogProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AclEditLogProto& default_instance();

  void Swap(AclEditLogProto* other);

  // implements Message ----------------------------------------------

  AclEditLogProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AclEditLogProto& from);
  void MergeFrom(const AclEditLogProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // repeated .hadoop.hdfs.AclEntryProto entries = 2;
  inline int entries_size() const;
  inline void clear_entries();
  static const int kEntriesFieldNumber = 2;
  inline const ::hadoop::hdfs::AclEntryProto& entries(int index) const;
  inline ::hadoop::hdfs::AclEntryProto* mutable_entries(int index);
  inline ::hadoop::hdfs::AclEntryProto* add_entries();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
      entries() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
      mutable_entries();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.AclEditLogProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto > entries_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static AclEditLogProto* default_instance_;
};
// -------------------------------------------------------------------

class ModifyAclEntriesRequestProto : public ::google::protobuf::Message {
 public:
  ModifyAclEntriesRequestProto();
  virtual ~ModifyAclEntriesRequestProto();

  ModifyAclEntriesRequestProto(const ModifyAclEntriesRequestProto& from);

  inline ModifyAclEntriesRequestProto& operator=(const ModifyAclEntriesRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ModifyAclEntriesRequestProto& default_instance();

  void Swap(ModifyAclEntriesRequestProto* other);

  // implements Message ----------------------------------------------

  ModifyAclEntriesRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ModifyAclEntriesRequestProto& from);
  void MergeFrom(const ModifyAclEntriesRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  inline int aclspec_size() const;
  inline void clear_aclspec();
  static const int kAclSpecFieldNumber = 2;
  inline const ::hadoop::hdfs::AclEntryProto& aclspec(int index) const;
  inline ::hadoop::hdfs::AclEntryProto* mutable_aclspec(int index);
  inline ::hadoop::hdfs::AclEntryProto* add_aclspec();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
      aclspec() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
      mutable_aclspec();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ModifyAclEntriesRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto > aclspec_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static ModifyAclEntriesRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class ModifyAclEntriesResponseProto : public ::google::protobuf::Message {
 public:
  ModifyAclEntriesResponseProto();
  virtual ~ModifyAclEntriesResponseProto();

  ModifyAclEntriesResponseProto(const ModifyAclEntriesResponseProto& from);

  inline ModifyAclEntriesResponseProto& operator=(const ModifyAclEntriesResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ModifyAclEntriesResponseProto& default_instance();

  void Swap(ModifyAclEntriesResponseProto* other);

  // implements Message ----------------------------------------------

  ModifyAclEntriesResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ModifyAclEntriesResponseProto& from);
  void MergeFrom(const ModifyAclEntriesResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ModifyAclEntriesResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static ModifyAclEntriesResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveAclRequestProto : public ::google::protobuf::Message {
 public:
  RemoveAclRequestProto();
  virtual ~RemoveAclRequestProto();

  RemoveAclRequestProto(const RemoveAclRequestProto& from);

  inline RemoveAclRequestProto& operator=(const RemoveAclRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveAclRequestProto& default_instance();

  void Swap(RemoveAclRequestProto* other);

  // implements Message ----------------------------------------------

  RemoveAclRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveAclRequestProto& from);
  void MergeFrom(const RemoveAclRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveAclRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static RemoveAclRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveAclResponseProto : public ::google::protobuf::Message {
 public:
  RemoveAclResponseProto();
  virtual ~RemoveAclResponseProto();

  RemoveAclResponseProto(const RemoveAclResponseProto& from);

  inline RemoveAclResponseProto& operator=(const RemoveAclResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveAclResponseProto& default_instance();

  void Swap(RemoveAclResponseProto* other);

  // implements Message ----------------------------------------------

  RemoveAclResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveAclResponseProto& from);
  void MergeFrom(const RemoveAclResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveAclResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static RemoveAclResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveAclEntriesRequestProto : public ::google::protobuf::Message {
 public:
  RemoveAclEntriesRequestProto();
  virtual ~RemoveAclEntriesRequestProto();

  RemoveAclEntriesRequestProto(const RemoveAclEntriesRequestProto& from);

  inline RemoveAclEntriesRequestProto& operator=(const RemoveAclEntriesRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveAclEntriesRequestProto& default_instance();

  void Swap(RemoveAclEntriesRequestProto* other);

  // implements Message ----------------------------------------------

  RemoveAclEntriesRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveAclEntriesRequestProto& from);
  void MergeFrom(const RemoveAclEntriesRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  inline int aclspec_size() const;
  inline void clear_aclspec();
  static const int kAclSpecFieldNumber = 2;
  inline const ::hadoop::hdfs::AclEntryProto& aclspec(int index) const;
  inline ::hadoop::hdfs::AclEntryProto* mutable_aclspec(int index);
  inline ::hadoop::hdfs::AclEntryProto* add_aclspec();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
      aclspec() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
      mutable_aclspec();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveAclEntriesRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto > aclspec_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static RemoveAclEntriesRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveAclEntriesResponseProto : public ::google::protobuf::Message {
 public:
  RemoveAclEntriesResponseProto();
  virtual ~RemoveAclEntriesResponseProto();

  RemoveAclEntriesResponseProto(const RemoveAclEntriesResponseProto& from);

  inline RemoveAclEntriesResponseProto& operator=(const RemoveAclEntriesResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveAclEntriesResponseProto& default_instance();

  void Swap(RemoveAclEntriesResponseProto* other);

  // implements Message ----------------------------------------------

  RemoveAclEntriesResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveAclEntriesResponseProto& from);
  void MergeFrom(const RemoveAclEntriesResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveAclEntriesResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static RemoveAclEntriesResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveDefaultAclRequestProto : public ::google::protobuf::Message {
 public:
  RemoveDefaultAclRequestProto();
  virtual ~RemoveDefaultAclRequestProto();

  RemoveDefaultAclRequestProto(const RemoveDefaultAclRequestProto& from);

  inline RemoveDefaultAclRequestProto& operator=(const RemoveDefaultAclRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveDefaultAclRequestProto& default_instance();

  void Swap(RemoveDefaultAclRequestProto* other);

  // implements Message ----------------------------------------------

  RemoveDefaultAclRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveDefaultAclRequestProto& from);
  void MergeFrom(const RemoveDefaultAclRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveDefaultAclRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static RemoveDefaultAclRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveDefaultAclResponseProto : public ::google::protobuf::Message {
 public:
  RemoveDefaultAclResponseProto();
  virtual ~RemoveDefaultAclResponseProto();

  RemoveDefaultAclResponseProto(const RemoveDefaultAclResponseProto& from);

  inline RemoveDefaultAclResponseProto& operator=(const RemoveDefaultAclResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveDefaultAclResponseProto& default_instance();

  void Swap(RemoveDefaultAclResponseProto* other);

  // implements Message ----------------------------------------------

  RemoveDefaultAclResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveDefaultAclResponseProto& from);
  void MergeFrom(const RemoveDefaultAclResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveDefaultAclResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static RemoveDefaultAclResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class SetAclRequestProto : public ::google::protobuf::Message {
 public:
  SetAclRequestProto();
  virtual ~SetAclRequestProto();

  SetAclRequestProto(const SetAclRequestProto& from);

  inline SetAclRequestProto& operator=(const SetAclRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SetAclRequestProto& default_instance();

  void Swap(SetAclRequestProto* other);

  // implements Message ----------------------------------------------

  SetAclRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SetAclRequestProto& from);
  void MergeFrom(const SetAclRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  inline int aclspec_size() const;
  inline void clear_aclspec();
  static const int kAclSpecFieldNumber = 2;
  inline const ::hadoop::hdfs::AclEntryProto& aclspec(int index) const;
  inline ::hadoop::hdfs::AclEntryProto* mutable_aclspec(int index);
  inline ::hadoop::hdfs::AclEntryProto* add_aclspec();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
      aclspec() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
      mutable_aclspec();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SetAclRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto > aclspec_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static SetAclRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class SetAclResponseProto : public ::google::protobuf::Message {
 public:
  SetAclResponseProto();
  virtual ~SetAclResponseProto();

  SetAclResponseProto(const SetAclResponseProto& from);

  inline SetAclResponseProto& operator=(const SetAclResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SetAclResponseProto& default_instance();

  void Swap(SetAclResponseProto* other);

  // implements Message ----------------------------------------------

  SetAclResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SetAclResponseProto& from);
  void MergeFrom(const SetAclResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SetAclResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static SetAclResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetAclStatusRequestProto : public ::google::protobuf::Message {
 public:
  GetAclStatusRequestProto();
  virtual ~GetAclStatusRequestProto();

  GetAclStatusRequestProto(const GetAclStatusRequestProto& from);

  inline GetAclStatusRequestProto& operator=(const GetAclStatusRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetAclStatusRequestProto& default_instance();

  void Swap(GetAclStatusRequestProto* other);

  // implements Message ----------------------------------------------

  GetAclStatusRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetAclStatusRequestProto& from);
  void MergeFrom(const GetAclStatusRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.GetAclStatusRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static GetAclStatusRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetAclStatusResponseProto : public ::google::protobuf::Message {
 public:
  GetAclStatusResponseProto();
  virtual ~GetAclStatusResponseProto();

  GetAclStatusResponseProto(const GetAclStatusResponseProto& from);

  inline GetAclStatusResponseProto& operator=(const GetAclStatusResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetAclStatusResponseProto& default_instance();

  void Swap(GetAclStatusResponseProto* other);

  // implements Message ----------------------------------------------

  GetAclStatusResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetAclStatusResponseProto& from);
  void MergeFrom(const GetAclStatusResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.AclStatusProto result = 1;
  inline bool has_result() const;
  inline void clear_result();
  static const int kResultFieldNumber = 1;
  inline const ::hadoop::hdfs::AclStatusProto& result() const;
  inline ::hadoop::hdfs::AclStatusProto* mutable_result();
  inline ::hadoop::hdfs::AclStatusProto* release_result();
  inline void set_allocated_result(::hadoop::hdfs::AclStatusProto* result);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.GetAclStatusResponseProto)
 private:
  inline void set_has_result();
  inline void clear_has_result();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::AclStatusProto* result_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_acl_2eproto();
  friend void protobuf_AssignDesc_acl_2eproto();
  friend void protobuf_ShutdownFile_acl_2eproto();

  void InitAsDefaultInstance();
  static GetAclStatusResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// AclEntryProto

// required .hadoop.hdfs.AclEntryProto.AclEntryTypeProto type = 1;
inline bool AclEntryProto::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AclEntryProto::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AclEntryProto::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AclEntryProto::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::hadoop::hdfs::AclEntryProto_AclEntryTypeProto AclEntryProto::type() const {
  return static_cast< ::hadoop::hdfs::AclEntryProto_AclEntryTypeProto >(type_);
}
inline void AclEntryProto::set_type(::hadoop::hdfs::AclEntryProto_AclEntryTypeProto value) {
  assert(::hadoop::hdfs::AclEntryProto_AclEntryTypeProto_IsValid(value));
  set_has_type();
  type_ = value;
}

// required .hadoop.hdfs.AclEntryProto.AclEntryScopeProto scope = 2;
inline bool AclEntryProto::has_scope() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void AclEntryProto::set_has_scope() {
  _has_bits_[0] |= 0x00000002u;
}
inline void AclEntryProto::clear_has_scope() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void AclEntryProto::clear_scope() {
  scope_ = 0;
  clear_has_scope();
}
inline ::hadoop::hdfs::AclEntryProto_AclEntryScopeProto AclEntryProto::scope() const {
  return static_cast< ::hadoop::hdfs::AclEntryProto_AclEntryScopeProto >(scope_);
}
inline void AclEntryProto::set_scope(::hadoop::hdfs::AclEntryProto_AclEntryScopeProto value) {
  assert(::hadoop::hdfs::AclEntryProto_AclEntryScopeProto_IsValid(value));
  set_has_scope();
  scope_ = value;
}

// required .hadoop.hdfs.AclEntryProto.FsActionProto permissions = 3;
inline bool AclEntryProto::has_permissions() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void AclEntryProto::set_has_permissions() {
  _has_bits_[0] |= 0x00000004u;
}
inline void AclEntryProto::clear_has_permissions() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void AclEntryProto::clear_permissions() {
  permissions_ = 0;
  clear_has_permissions();
}
inline ::hadoop::hdfs::AclEntryProto_FsActionProto AclEntryProto::permissions() const {
  return static_cast< ::hadoop::hdfs::AclEntryProto_FsActionProto >(permissions_);
}
inline void AclEntryProto::set_permissions(::hadoop::hdfs::AclEntryProto_FsActionProto value) {
  assert(::hadoop::hdfs::AclEntryProto_FsActionProto_IsValid(value));
  set_has_permissions();
  permissions_ = value;
}

// optional string name = 4;
inline bool AclEntryProto::has_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void AclEntryProto::set_has_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void AclEntryProto::clear_has_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void AclEntryProto::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& AclEntryProto::name() const {
  return *name_;
}
inline void AclEntryProto::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void AclEntryProto::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void AclEntryProto::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AclEntryProto::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* AclEntryProto::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AclEntryProto::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// AclStatusProto

// required string owner = 1;
inline bool AclStatusProto::has_owner() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AclStatusProto::set_has_owner() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AclStatusProto::clear_has_owner() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AclStatusProto::clear_owner() {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    owner_->clear();
  }
  clear_has_owner();
}
inline const ::std::string& AclStatusProto::owner() const {
  return *owner_;
}
inline void AclStatusProto::set_owner(const ::std::string& value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void AclStatusProto::set_owner(const char* value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void AclStatusProto::set_owner(const char* value, size_t size) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AclStatusProto::mutable_owner() {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  return owner_;
}
inline ::std::string* AclStatusProto::release_owner() {
  clear_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = owner_;
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AclStatusProto::set_allocated_owner(::std::string* owner) {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    delete owner_;
  }
  if (owner) {
    set_has_owner();
    owner_ = owner;
  } else {
    clear_has_owner();
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string group = 2;
inline bool AclStatusProto::has_group() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void AclStatusProto::set_has_group() {
  _has_bits_[0] |= 0x00000002u;
}
inline void AclStatusProto::clear_has_group() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void AclStatusProto::clear_group() {
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    group_->clear();
  }
  clear_has_group();
}
inline const ::std::string& AclStatusProto::group() const {
  return *group_;
}
inline void AclStatusProto::set_group(const ::std::string& value) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(value);
}
inline void AclStatusProto::set_group(const char* value) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(value);
}
inline void AclStatusProto::set_group(const char* value, size_t size) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AclStatusProto::mutable_group() {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  return group_;
}
inline ::std::string* AclStatusProto::release_group() {
  clear_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = group_;
    group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AclStatusProto::set_allocated_group(::std::string* group) {
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    delete group_;
  }
  if (group) {
    set_has_group();
    group_ = group;
  } else {
    clear_has_group();
    group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required bool sticky = 3;
inline bool AclStatusProto::has_sticky() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void AclStatusProto::set_has_sticky() {
  _has_bits_[0] |= 0x00000004u;
}
inline void AclStatusProto::clear_has_sticky() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void AclStatusProto::clear_sticky() {
  sticky_ = false;
  clear_has_sticky();
}
inline bool AclStatusProto::sticky() const {
  return sticky_;
}
inline void AclStatusProto::set_sticky(bool value) {
  set_has_sticky();
  sticky_ = value;
}

// repeated .hadoop.hdfs.AclEntryProto entries = 4;
inline int AclStatusProto::entries_size() const {
  return entries_.size();
}
inline void AclStatusProto::clear_entries() {
  entries_.Clear();
}
inline const ::hadoop::hdfs::AclEntryProto& AclStatusProto::entries(int index) const {
  return entries_.Get(index);
}
inline ::hadoop::hdfs::AclEntryProto* AclStatusProto::mutable_entries(int index) {
  return entries_.Mutable(index);
}
inline ::hadoop::hdfs::AclEntryProto* AclStatusProto::add_entries() {
  return entries_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
AclStatusProto::entries() const {
  return entries_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
AclStatusProto::mutable_entries() {
  return &entries_;
}

// -------------------------------------------------------------------

// AclEditLogProto

// required string src = 1;
inline bool AclEditLogProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AclEditLogProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AclEditLogProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AclEditLogProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& AclEditLogProto::src() const {
  return *src_;
}
inline void AclEditLogProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void AclEditLogProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void AclEditLogProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AclEditLogProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* AclEditLogProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AclEditLogProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.AclEntryProto entries = 2;
inline int AclEditLogProto::entries_size() const {
  return entries_.size();
}
inline void AclEditLogProto::clear_entries() {
  entries_.Clear();
}
inline const ::hadoop::hdfs::AclEntryProto& AclEditLogProto::entries(int index) const {
  return entries_.Get(index);
}
inline ::hadoop::hdfs::AclEntryProto* AclEditLogProto::mutable_entries(int index) {
  return entries_.Mutable(index);
}
inline ::hadoop::hdfs::AclEntryProto* AclEditLogProto::add_entries() {
  return entries_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
AclEditLogProto::entries() const {
  return entries_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
AclEditLogProto::mutable_entries() {
  return &entries_;
}

// -------------------------------------------------------------------

// ModifyAclEntriesRequestProto

// required string src = 1;
inline bool ModifyAclEntriesRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ModifyAclEntriesRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ModifyAclEntriesRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ModifyAclEntriesRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& ModifyAclEntriesRequestProto::src() const {
  return *src_;
}
inline void ModifyAclEntriesRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void ModifyAclEntriesRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void ModifyAclEntriesRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ModifyAclEntriesRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* ModifyAclEntriesRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ModifyAclEntriesRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
inline int ModifyAclEntriesRequestProto::aclspec_size() const {
  return aclspec_.size();
}
inline void ModifyAclEntriesRequestProto::clear_aclspec() {
  aclspec_.Clear();
}
inline const ::hadoop::hdfs::AclEntryProto& ModifyAclEntriesRequestProto::aclspec(int index) const {
  return aclspec_.Get(index);
}
inline ::hadoop::hdfs::AclEntryProto* ModifyAclEntriesRequestProto::mutable_aclspec(int index) {
  return aclspec_.Mutable(index);
}
inline ::hadoop::hdfs::AclEntryProto* ModifyAclEntriesRequestProto::add_aclspec() {
  return aclspec_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
ModifyAclEntriesRequestProto::aclspec() const {
  return aclspec_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
ModifyAclEntriesRequestProto::mutable_aclspec() {
  return &aclspec_;
}

// -------------------------------------------------------------------

// ModifyAclEntriesResponseProto

// -------------------------------------------------------------------

// RemoveAclRequestProto

// required string src = 1;
inline bool RemoveAclRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RemoveAclRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RemoveAclRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RemoveAclRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& RemoveAclRequestProto::src() const {
  return *src_;
}
inline void RemoveAclRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveAclRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveAclRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RemoveAclRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* RemoveAclRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RemoveAclRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// RemoveAclResponseProto

// -------------------------------------------------------------------

// RemoveAclEntriesRequestProto

// required string src = 1;
inline bool RemoveAclEntriesRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RemoveAclEntriesRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RemoveAclEntriesRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RemoveAclEntriesRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& RemoveAclEntriesRequestProto::src() const {
  return *src_;
}
inline void RemoveAclEntriesRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveAclEntriesRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveAclEntriesRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RemoveAclEntriesRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* RemoveAclEntriesRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RemoveAclEntriesRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
inline int RemoveAclEntriesRequestProto::aclspec_size() const {
  return aclspec_.size();
}
inline void RemoveAclEntriesRequestProto::clear_aclspec() {
  aclspec_.Clear();
}
inline const ::hadoop::hdfs::AclEntryProto& RemoveAclEntriesRequestProto::aclspec(int index) const {
  return aclspec_.Get(index);
}
inline ::hadoop::hdfs::AclEntryProto* RemoveAclEntriesRequestProto::mutable_aclspec(int index) {
  return aclspec_.Mutable(index);
}
inline ::hadoop::hdfs::AclEntryProto* RemoveAclEntriesRequestProto::add_aclspec() {
  return aclspec_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
RemoveAclEntriesRequestProto::aclspec() const {
  return aclspec_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
RemoveAclEntriesRequestProto::mutable_aclspec() {
  return &aclspec_;
}

// -------------------------------------------------------------------

// RemoveAclEntriesResponseProto

// -------------------------------------------------------------------

// RemoveDefaultAclRequestProto

// required string src = 1;
inline bool RemoveDefaultAclRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RemoveDefaultAclRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RemoveDefaultAclRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RemoveDefaultAclRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& RemoveDefaultAclRequestProto::src() const {
  return *src_;
}
inline void RemoveDefaultAclRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveDefaultAclRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveDefaultAclRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RemoveDefaultAclRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* RemoveDefaultAclRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RemoveDefaultAclRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// RemoveDefaultAclResponseProto

// -------------------------------------------------------------------

// SetAclRequestProto

// required string src = 1;
inline bool SetAclRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SetAclRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SetAclRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SetAclRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& SetAclRequestProto::src() const {
  return *src_;
}
inline void SetAclRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void SetAclRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void SetAclRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SetAclRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* SetAclRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SetAclRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
inline int SetAclRequestProto::aclspec_size() const {
  return aclspec_.size();
}
inline void SetAclRequestProto::clear_aclspec() {
  aclspec_.Clear();
}
inline const ::hadoop::hdfs::AclEntryProto& SetAclRequestProto::aclspec(int index) const {
  return aclspec_.Get(index);
}
inline ::hadoop::hdfs::AclEntryProto* SetAclRequestProto::mutable_aclspec(int index) {
  return aclspec_.Mutable(index);
}
inline ::hadoop::hdfs::AclEntryProto* SetAclRequestProto::add_aclspec() {
  return aclspec_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
SetAclRequestProto::aclspec() const {
  return aclspec_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
SetAclRequestProto::mutable_aclspec() {
  return &aclspec_;
}

// -------------------------------------------------------------------

// SetAclResponseProto

// -------------------------------------------------------------------

// GetAclStatusRequestProto

// required string src = 1;
inline bool GetAclStatusRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetAclStatusRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetAclStatusRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetAclStatusRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& GetAclStatusRequestProto::src() const {
  return *src_;
}
inline void GetAclStatusRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void GetAclStatusRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void GetAclStatusRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetAclStatusRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* GetAclStatusRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetAclStatusRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// GetAclStatusResponseProto

// required .hadoop.hdfs.AclStatusProto result = 1;
inline bool GetAclStatusResponseProto::has_result() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetAclStatusResponseProto::set_has_result() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetAclStatusResponseProto::clear_has_result() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetAclStatusResponseProto::clear_result() {
  if (result_ != NULL) result_->::hadoop::hdfs::AclStatusProto::Clear();
  clear_has_result();
}
inline const ::hadoop::hdfs::AclStatusProto& GetAclStatusResponseProto::result() const {
  return result_ != NULL ? *result_ : *default_instance_->result_;
}
inline ::hadoop::hdfs::AclStatusProto* GetAclStatusResponseProto::mutable_result() {
  set_has_result();
  if (result_ == NULL) result_ = new ::hadoop::hdfs::AclStatusProto;
  return result_;
}
inline ::hadoop::hdfs::AclStatusProto* GetAclStatusResponseProto::release_result() {
  clear_has_result();
  ::hadoop::hdfs::AclStatusProto* temp = result_;
  result_ = NULL;
  return temp;
}
inline void GetAclStatusResponseProto::set_allocated_result(::hadoop::hdfs::AclStatusProto* result) {
  delete result_;
  result_ = result;
  if (result) {
    set_has_result();
  } else {
    clear_has_result();
  }
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::AclEntryProto_AclEntryScopeProto>() {
  return ::hadoop::hdfs::AclEntryProto_AclEntryScopeProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::AclEntryProto_AclEntryTypeProto>() {
  return ::hadoop::hdfs::AclEntryProto_AclEntryTypeProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::AclEntryProto_FsActionProto>() {
  return ::hadoop::hdfs::AclEntryProto_FsActionProto_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_acl_2eproto__INCLUDED
