// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: NamenodeProtocol.proto

#ifndef PROTOBUF_NamenodeProtocol_2eproto__INCLUDED
#define PROTOBUF_NamenodeProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {
namespace namenode {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_NamenodeProtocol_2eproto();
void protobuf_AssignDesc_NamenodeProtocol_2eproto();
void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

class GetBlocksRequestProto;
class GetBlocksResponseProto;
class GetBlockKeysRequestProto;
class GetBlockKeysResponseProto;
class GetTransactionIdRequestProto;
class GetTransactionIdResponseProto;
class RollEditLogRequestProto;
class RollEditLogResponseProto;
class GetMostRecentCheckpointTxIdRequestProto;
class GetMostRecentCheckpointTxIdResponseProto;
class ErrorReportRequestProto;
class ErrorReportResponseProto;
class RegisterRequestProto;
class RegisterResponseProto;
class StartCheckpointRequestProto;
class StartCheckpointResponseProto;
class EndCheckpointRequestProto;
class EndCheckpointResponseProto;
class GetEditLogManifestRequestProto;
class GetEditLogManifestResponseProto;

// ===================================================================

class GetBlocksRequestProto : public ::google::protobuf::Message {
 public:
  GetBlocksRequestProto();
  virtual ~GetBlocksRequestProto();

  GetBlocksRequestProto(const GetBlocksRequestProto& from);

  inline GetBlocksRequestProto& operator=(const GetBlocksRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetBlocksRequestProto& default_instance();

  void Swap(GetBlocksRequestProto* other);

  // implements Message ----------------------------------------------

  GetBlocksRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetBlocksRequestProto& from);
  void MergeFrom(const GetBlocksRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.DatanodeIDProto datanode = 1;
  inline bool has_datanode() const;
  inline void clear_datanode();
  static const int kDatanodeFieldNumber = 1;
  inline const ::hadoop::hdfs::DatanodeIDProto& datanode() const;
  inline ::hadoop::hdfs::DatanodeIDProto* mutable_datanode();
  inline ::hadoop::hdfs::DatanodeIDProto* release_datanode();
  inline void set_allocated_datanode(::hadoop::hdfs::DatanodeIDProto* datanode);

  // required uint64 size = 2;
  inline bool has_size() const;
  inline void clear_size();
  static const int kSizeFieldNumber = 2;
  inline ::google::protobuf::uint64 size() const;
  inline void set_size(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetBlocksRequestProto)
 private:
  inline void set_has_datanode();
  inline void clear_has_datanode();
  inline void set_has_size();
  inline void clear_has_size();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::DatanodeIDProto* datanode_;
  ::google::protobuf::uint64 size_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetBlocksRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetBlocksResponseProto : public ::google::protobuf::Message {
 public:
  GetBlocksResponseProto();
  virtual ~GetBlocksResponseProto();

  GetBlocksResponseProto(const GetBlocksResponseProto& from);

  inline GetBlocksResponseProto& operator=(const GetBlocksResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetBlocksResponseProto& default_instance();

  void Swap(GetBlocksResponseProto* other);

  // implements Message ----------------------------------------------

  GetBlocksResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetBlocksResponseProto& from);
  void MergeFrom(const GetBlocksResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BlocksWithLocationsProto blocks = 1;
  inline bool has_blocks() const;
  inline void clear_blocks();
  static const int kBlocksFieldNumber = 1;
  inline const ::hadoop::hdfs::BlocksWithLocationsProto& blocks() const;
  inline ::hadoop::hdfs::BlocksWithLocationsProto* mutable_blocks();
  inline ::hadoop::hdfs::BlocksWithLocationsProto* release_blocks();
  inline void set_allocated_blocks(::hadoop::hdfs::BlocksWithLocationsProto* blocks);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetBlocksResponseProto)
 private:
  inline void set_has_blocks();
  inline void clear_has_blocks();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BlocksWithLocationsProto* blocks_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetBlocksResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetBlockKeysRequestProto : public ::google::protobuf::Message {
 public:
  GetBlockKeysRequestProto();
  virtual ~GetBlockKeysRequestProto();

  GetBlockKeysRequestProto(const GetBlockKeysRequestProto& from);

  inline GetBlockKeysRequestProto& operator=(const GetBlockKeysRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetBlockKeysRequestProto& default_instance();

  void Swap(GetBlockKeysRequestProto* other);

  // implements Message ----------------------------------------------

  GetBlockKeysRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetBlockKeysRequestProto& from);
  void MergeFrom(const GetBlockKeysRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetBlockKeysRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetBlockKeysRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetBlockKeysResponseProto : public ::google::protobuf::Message {
 public:
  GetBlockKeysResponseProto();
  virtual ~GetBlockKeysResponseProto();

  GetBlockKeysResponseProto(const GetBlockKeysResponseProto& from);

  inline GetBlockKeysResponseProto& operator=(const GetBlockKeysResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetBlockKeysResponseProto& default_instance();

  void Swap(GetBlockKeysResponseProto* other);

  // implements Message ----------------------------------------------

  GetBlockKeysResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetBlockKeysResponseProto& from);
  void MergeFrom(const GetBlockKeysResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .hadoop.hdfs.ExportedBlockKeysProto keys = 1;
  inline bool has_keys() const;
  inline void clear_keys();
  static const int kKeysFieldNumber = 1;
  inline const ::hadoop::hdfs::ExportedBlockKeysProto& keys() const;
  inline ::hadoop::hdfs::ExportedBlockKeysProto* mutable_keys();
  inline ::hadoop::hdfs::ExportedBlockKeysProto* release_keys();
  inline void set_allocated_keys(::hadoop::hdfs::ExportedBlockKeysProto* keys);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetBlockKeysResponseProto)
 private:
  inline void set_has_keys();
  inline void clear_has_keys();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ExportedBlockKeysProto* keys_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetBlockKeysResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetTransactionIdRequestProto : public ::google::protobuf::Message {
 public:
  GetTransactionIdRequestProto();
  virtual ~GetTransactionIdRequestProto();

  GetTransactionIdRequestProto(const GetTransactionIdRequestProto& from);

  inline GetTransactionIdRequestProto& operator=(const GetTransactionIdRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetTransactionIdRequestProto& default_instance();

  void Swap(GetTransactionIdRequestProto* other);

  // implements Message ----------------------------------------------

  GetTransactionIdRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetTransactionIdRequestProto& from);
  void MergeFrom(const GetTransactionIdRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetTransactionIdRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetTransactionIdRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetTransactionIdResponseProto : public ::google::protobuf::Message {
 public:
  GetTransactionIdResponseProto();
  virtual ~GetTransactionIdResponseProto();

  GetTransactionIdResponseProto(const GetTransactionIdResponseProto& from);

  inline GetTransactionIdResponseProto& operator=(const GetTransactionIdResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetTransactionIdResponseProto& default_instance();

  void Swap(GetTransactionIdResponseProto* other);

  // implements Message ----------------------------------------------

  GetTransactionIdResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetTransactionIdResponseProto& from);
  void MergeFrom(const GetTransactionIdResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 txId = 1;
  inline bool has_txid() const;
  inline void clear_txid();
  static const int kTxIdFieldNumber = 1;
  inline ::google::protobuf::uint64 txid() const;
  inline void set_txid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetTransactionIdResponseProto)
 private:
  inline void set_has_txid();
  inline void clear_has_txid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 txid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetTransactionIdResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RollEditLogRequestProto : public ::google::protobuf::Message {
 public:
  RollEditLogRequestProto();
  virtual ~RollEditLogRequestProto();

  RollEditLogRequestProto(const RollEditLogRequestProto& from);

  inline RollEditLogRequestProto& operator=(const RollEditLogRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RollEditLogRequestProto& default_instance();

  void Swap(RollEditLogRequestProto* other);

  // implements Message ----------------------------------------------

  RollEditLogRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RollEditLogRequestProto& from);
  void MergeFrom(const RollEditLogRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.RollEditLogRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static RollEditLogRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RollEditLogResponseProto : public ::google::protobuf::Message {
 public:
  RollEditLogResponseProto();
  virtual ~RollEditLogResponseProto();

  RollEditLogResponseProto(const RollEditLogResponseProto& from);

  inline RollEditLogResponseProto& operator=(const RollEditLogResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RollEditLogResponseProto& default_instance();

  void Swap(RollEditLogResponseProto* other);

  // implements Message ----------------------------------------------

  RollEditLogResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RollEditLogResponseProto& from);
  void MergeFrom(const RollEditLogResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
  inline bool has_signature() const;
  inline void clear_signature();
  static const int kSignatureFieldNumber = 1;
  inline const ::hadoop::hdfs::CheckpointSignatureProto& signature() const;
  inline ::hadoop::hdfs::CheckpointSignatureProto* mutable_signature();
  inline ::hadoop::hdfs::CheckpointSignatureProto* release_signature();
  inline void set_allocated_signature(::hadoop::hdfs::CheckpointSignatureProto* signature);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.RollEditLogResponseProto)
 private:
  inline void set_has_signature();
  inline void clear_has_signature();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::CheckpointSignatureProto* signature_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static RollEditLogResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetMostRecentCheckpointTxIdRequestProto : public ::google::protobuf::Message {
 public:
  GetMostRecentCheckpointTxIdRequestProto();
  virtual ~GetMostRecentCheckpointTxIdRequestProto();

  GetMostRecentCheckpointTxIdRequestProto(const GetMostRecentCheckpointTxIdRequestProto& from);

  inline GetMostRecentCheckpointTxIdRequestProto& operator=(const GetMostRecentCheckpointTxIdRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetMostRecentCheckpointTxIdRequestProto& default_instance();

  void Swap(GetMostRecentCheckpointTxIdRequestProto* other);

  // implements Message ----------------------------------------------

  GetMostRecentCheckpointTxIdRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetMostRecentCheckpointTxIdRequestProto& from);
  void MergeFrom(const GetMostRecentCheckpointTxIdRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetMostRecentCheckpointTxIdRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetMostRecentCheckpointTxIdRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetMostRecentCheckpointTxIdResponseProto : public ::google::protobuf::Message {
 public:
  GetMostRecentCheckpointTxIdResponseProto();
  virtual ~GetMostRecentCheckpointTxIdResponseProto();

  GetMostRecentCheckpointTxIdResponseProto(const GetMostRecentCheckpointTxIdResponseProto& from);

  inline GetMostRecentCheckpointTxIdResponseProto& operator=(const GetMostRecentCheckpointTxIdResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetMostRecentCheckpointTxIdResponseProto& default_instance();

  void Swap(GetMostRecentCheckpointTxIdResponseProto* other);

  // implements Message ----------------------------------------------

  GetMostRecentCheckpointTxIdResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetMostRecentCheckpointTxIdResponseProto& from);
  void MergeFrom(const GetMostRecentCheckpointTxIdResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 txId = 1;
  inline bool has_txid() const;
  inline void clear_txid();
  static const int kTxIdFieldNumber = 1;
  inline ::google::protobuf::uint64 txid() const;
  inline void set_txid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetMostRecentCheckpointTxIdResponseProto)
 private:
  inline void set_has_txid();
  inline void clear_has_txid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 txid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetMostRecentCheckpointTxIdResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ErrorReportRequestProto : public ::google::protobuf::Message {
 public:
  ErrorReportRequestProto();
  virtual ~ErrorReportRequestProto();

  ErrorReportRequestProto(const ErrorReportRequestProto& from);

  inline ErrorReportRequestProto& operator=(const ErrorReportRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ErrorReportRequestProto& default_instance();

  void Swap(ErrorReportRequestProto* other);

  // implements Message ----------------------------------------------

  ErrorReportRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ErrorReportRequestProto& from);
  void MergeFrom(const ErrorReportRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  inline bool has_registration() const;
  inline void clear_registration();
  static const int kRegistrationFieldNumber = 1;
  inline const ::hadoop::hdfs::NamenodeRegistrationProto& registration() const;
  inline ::hadoop::hdfs::NamenodeRegistrationProto* mutable_registration();
  inline ::hadoop::hdfs::NamenodeRegistrationProto* release_registration();
  inline void set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration);

  // required uint32 errorCode = 2;
  inline bool has_errorcode() const;
  inline void clear_errorcode();
  static const int kErrorCodeFieldNumber = 2;
  inline ::google::protobuf::uint32 errorcode() const;
  inline void set_errorcode(::google::protobuf::uint32 value);

  // required string msg = 3;
  inline bool has_msg() const;
  inline void clear_msg();
  static const int kMsgFieldNumber = 3;
  inline const ::std::string& msg() const;
  inline void set_msg(const ::std::string& value);
  inline void set_msg(const char* value);
  inline void set_msg(const char* value, size_t size);
  inline ::std::string* mutable_msg();
  inline ::std::string* release_msg();
  inline void set_allocated_msg(::std::string* msg);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.ErrorReportRequestProto)
 private:
  inline void set_has_registration();
  inline void clear_has_registration();
  inline void set_has_errorcode();
  inline void clear_has_errorcode();
  inline void set_has_msg();
  inline void clear_has_msg();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamenodeRegistrationProto* registration_;
  ::std::string* msg_;
  ::google::protobuf::uint32 errorcode_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static ErrorReportRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class ErrorReportResponseProto : public ::google::protobuf::Message {
 public:
  ErrorReportResponseProto();
  virtual ~ErrorReportResponseProto();

  ErrorReportResponseProto(const ErrorReportResponseProto& from);

  inline ErrorReportResponseProto& operator=(const ErrorReportResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ErrorReportResponseProto& default_instance();

  void Swap(ErrorReportResponseProto* other);

  // implements Message ----------------------------------------------

  ErrorReportResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ErrorReportResponseProto& from);
  void MergeFrom(const ErrorReportResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.ErrorReportResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static ErrorReportResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RegisterRequestProto : public ::google::protobuf::Message {
 public:
  RegisterRequestProto();
  virtual ~RegisterRequestProto();

  RegisterRequestProto(const RegisterRequestProto& from);

  inline RegisterRequestProto& operator=(const RegisterRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterRequestProto& default_instance();

  void Swap(RegisterRequestProto* other);

  // implements Message ----------------------------------------------

  RegisterRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RegisterRequestProto& from);
  void MergeFrom(const RegisterRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  inline bool has_registration() const;
  inline void clear_registration();
  static const int kRegistrationFieldNumber = 1;
  inline const ::hadoop::hdfs::NamenodeRegistrationProto& registration() const;
  inline ::hadoop::hdfs::NamenodeRegistrationProto* mutable_registration();
  inline ::hadoop::hdfs::NamenodeRegistrationProto* release_registration();
  inline void set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.RegisterRequestProto)
 private:
  inline void set_has_registration();
  inline void clear_has_registration();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamenodeRegistrationProto* registration_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static RegisterRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RegisterResponseProto : public ::google::protobuf::Message {
 public:
  RegisterResponseProto();
  virtual ~RegisterResponseProto();

  RegisterResponseProto(const RegisterResponseProto& from);

  inline RegisterResponseProto& operator=(const RegisterResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterResponseProto& default_instance();

  void Swap(RegisterResponseProto* other);

  // implements Message ----------------------------------------------

  RegisterResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RegisterResponseProto& from);
  void MergeFrom(const RegisterResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  inline bool has_registration() const;
  inline void clear_registration();
  static const int kRegistrationFieldNumber = 1;
  inline const ::hadoop::hdfs::NamenodeRegistrationProto& registration() const;
  inline ::hadoop::hdfs::NamenodeRegistrationProto* mutable_registration();
  inline ::hadoop::hdfs::NamenodeRegistrationProto* release_registration();
  inline void set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.RegisterResponseProto)
 private:
  inline void set_has_registration();
  inline void clear_has_registration();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamenodeRegistrationProto* registration_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static RegisterResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class StartCheckpointRequestProto : public ::google::protobuf::Message {
 public:
  StartCheckpointRequestProto();
  virtual ~StartCheckpointRequestProto();

  StartCheckpointRequestProto(const StartCheckpointRequestProto& from);

  inline StartCheckpointRequestProto& operator=(const StartCheckpointRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StartCheckpointRequestProto& default_instance();

  void Swap(StartCheckpointRequestProto* other);

  // implements Message ----------------------------------------------

  StartCheckpointRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StartCheckpointRequestProto& from);
  void MergeFrom(const StartCheckpointRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  inline bool has_registration() const;
  inline void clear_registration();
  static const int kRegistrationFieldNumber = 1;
  inline const ::hadoop::hdfs::NamenodeRegistrationProto& registration() const;
  inline ::hadoop::hdfs::NamenodeRegistrationProto* mutable_registration();
  inline ::hadoop::hdfs::NamenodeRegistrationProto* release_registration();
  inline void set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.StartCheckpointRequestProto)
 private:
  inline void set_has_registration();
  inline void clear_has_registration();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamenodeRegistrationProto* registration_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static StartCheckpointRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class StartCheckpointResponseProto : public ::google::protobuf::Message {
 public:
  StartCheckpointResponseProto();
  virtual ~StartCheckpointResponseProto();

  StartCheckpointResponseProto(const StartCheckpointResponseProto& from);

  inline StartCheckpointResponseProto& operator=(const StartCheckpointResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StartCheckpointResponseProto& default_instance();

  void Swap(StartCheckpointResponseProto* other);

  // implements Message ----------------------------------------------

  StartCheckpointResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StartCheckpointResponseProto& from);
  void MergeFrom(const StartCheckpointResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamenodeCommandProto command = 1;
  inline bool has_command() const;
  inline void clear_command();
  static const int kCommandFieldNumber = 1;
  inline const ::hadoop::hdfs::NamenodeCommandProto& command() const;
  inline ::hadoop::hdfs::NamenodeCommandProto* mutable_command();
  inline ::hadoop::hdfs::NamenodeCommandProto* release_command();
  inline void set_allocated_command(::hadoop::hdfs::NamenodeCommandProto* command);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.StartCheckpointResponseProto)
 private:
  inline void set_has_command();
  inline void clear_has_command();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamenodeCommandProto* command_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static StartCheckpointResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class EndCheckpointRequestProto : public ::google::protobuf::Message {
 public:
  EndCheckpointRequestProto();
  virtual ~EndCheckpointRequestProto();

  EndCheckpointRequestProto(const EndCheckpointRequestProto& from);

  inline EndCheckpointRequestProto& operator=(const EndCheckpointRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const EndCheckpointRequestProto& default_instance();

  void Swap(EndCheckpointRequestProto* other);

  // implements Message ----------------------------------------------

  EndCheckpointRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const EndCheckpointRequestProto& from);
  void MergeFrom(const EndCheckpointRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  inline bool has_registration() const;
  inline void clear_registration();
  static const int kRegistrationFieldNumber = 1;
  inline const ::hadoop::hdfs::NamenodeRegistrationProto& registration() const;
  inline ::hadoop::hdfs::NamenodeRegistrationProto* mutable_registration();
  inline ::hadoop::hdfs::NamenodeRegistrationProto* release_registration();
  inline void set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration);

  // required .hadoop.hdfs.CheckpointSignatureProto signature = 2;
  inline bool has_signature() const;
  inline void clear_signature();
  static const int kSignatureFieldNumber = 2;
  inline const ::hadoop::hdfs::CheckpointSignatureProto& signature() const;
  inline ::hadoop::hdfs::CheckpointSignatureProto* mutable_signature();
  inline ::hadoop::hdfs::CheckpointSignatureProto* release_signature();
  inline void set_allocated_signature(::hadoop::hdfs::CheckpointSignatureProto* signature);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.EndCheckpointRequestProto)
 private:
  inline void set_has_registration();
  inline void clear_has_registration();
  inline void set_has_signature();
  inline void clear_has_signature();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamenodeRegistrationProto* registration_;
  ::hadoop::hdfs::CheckpointSignatureProto* signature_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static EndCheckpointRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class EndCheckpointResponseProto : public ::google::protobuf::Message {
 public:
  EndCheckpointResponseProto();
  virtual ~EndCheckpointResponseProto();

  EndCheckpointResponseProto(const EndCheckpointResponseProto& from);

  inline EndCheckpointResponseProto& operator=(const EndCheckpointResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const EndCheckpointResponseProto& default_instance();

  void Swap(EndCheckpointResponseProto* other);

  // implements Message ----------------------------------------------

  EndCheckpointResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const EndCheckpointResponseProto& from);
  void MergeFrom(const EndCheckpointResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.EndCheckpointResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static EndCheckpointResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetEditLogManifestRequestProto : public ::google::protobuf::Message {
 public:
  GetEditLogManifestRequestProto();
  virtual ~GetEditLogManifestRequestProto();

  GetEditLogManifestRequestProto(const GetEditLogManifestRequestProto& from);

  inline GetEditLogManifestRequestProto& operator=(const GetEditLogManifestRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetEditLogManifestRequestProto& default_instance();

  void Swap(GetEditLogManifestRequestProto* other);

  // implements Message ----------------------------------------------

  GetEditLogManifestRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetEditLogManifestRequestProto& from);
  void MergeFrom(const GetEditLogManifestRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 sinceTxId = 1;
  inline bool has_sincetxid() const;
  inline void clear_sincetxid();
  static const int kSinceTxIdFieldNumber = 1;
  inline ::google::protobuf::uint64 sincetxid() const;
  inline void set_sincetxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetEditLogManifestRequestProto)
 private:
  inline void set_has_sincetxid();
  inline void clear_has_sincetxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 sincetxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetEditLogManifestRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetEditLogManifestResponseProto : public ::google::protobuf::Message {
 public:
  GetEditLogManifestResponseProto();
  virtual ~GetEditLogManifestResponseProto();

  GetEditLogManifestResponseProto(const GetEditLogManifestResponseProto& from);

  inline GetEditLogManifestResponseProto& operator=(const GetEditLogManifestResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetEditLogManifestResponseProto& default_instance();

  void Swap(GetEditLogManifestResponseProto* other);

  // implements Message ----------------------------------------------

  GetEditLogManifestResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetEditLogManifestResponseProto& from);
  void MergeFrom(const GetEditLogManifestResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
  inline bool has_manifest() const;
  inline void clear_manifest();
  static const int kManifestFieldNumber = 1;
  inline const ::hadoop::hdfs::RemoteEditLogManifestProto& manifest() const;
  inline ::hadoop::hdfs::RemoteEditLogManifestProto* mutable_manifest();
  inline ::hadoop::hdfs::RemoteEditLogManifestProto* release_manifest();
  inline void set_allocated_manifest(::hadoop::hdfs::RemoteEditLogManifestProto* manifest);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.namenode.GetEditLogManifestResponseProto)
 private:
  inline void set_has_manifest();
  inline void clear_has_manifest();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::RemoteEditLogManifestProto* manifest_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_NamenodeProtocol_2eproto();
  friend void protobuf_AssignDesc_NamenodeProtocol_2eproto();
  friend void protobuf_ShutdownFile_NamenodeProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetEditLogManifestResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// GetBlocksRequestProto

// required .hadoop.hdfs.DatanodeIDProto datanode = 1;
inline bool GetBlocksRequestProto::has_datanode() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetBlocksRequestProto::set_has_datanode() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetBlocksRequestProto::clear_has_datanode() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetBlocksRequestProto::clear_datanode() {
  if (datanode_ != NULL) datanode_->::hadoop::hdfs::DatanodeIDProto::Clear();
  clear_has_datanode();
}
inline const ::hadoop::hdfs::DatanodeIDProto& GetBlocksRequestProto::datanode() const {
  return datanode_ != NULL ? *datanode_ : *default_instance_->datanode_;
}
inline ::hadoop::hdfs::DatanodeIDProto* GetBlocksRequestProto::mutable_datanode() {
  set_has_datanode();
  if (datanode_ == NULL) datanode_ = new ::hadoop::hdfs::DatanodeIDProto;
  return datanode_;
}
inline ::hadoop::hdfs::DatanodeIDProto* GetBlocksRequestProto::release_datanode() {
  clear_has_datanode();
  ::hadoop::hdfs::DatanodeIDProto* temp = datanode_;
  datanode_ = NULL;
  return temp;
}
inline void GetBlocksRequestProto::set_allocated_datanode(::hadoop::hdfs::DatanodeIDProto* datanode) {
  delete datanode_;
  datanode_ = datanode;
  if (datanode) {
    set_has_datanode();
  } else {
    clear_has_datanode();
  }
}

// required uint64 size = 2;
inline bool GetBlocksRequestProto::has_size() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GetBlocksRequestProto::set_has_size() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GetBlocksRequestProto::clear_has_size() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GetBlocksRequestProto::clear_size() {
  size_ = GOOGLE_ULONGLONG(0);
  clear_has_size();
}
inline ::google::protobuf::uint64 GetBlocksRequestProto::size() const {
  return size_;
}
inline void GetBlocksRequestProto::set_size(::google::protobuf::uint64 value) {
  set_has_size();
  size_ = value;
}

// -------------------------------------------------------------------

// GetBlocksResponseProto

// required .hadoop.hdfs.BlocksWithLocationsProto blocks = 1;
inline bool GetBlocksResponseProto::has_blocks() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetBlocksResponseProto::set_has_blocks() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetBlocksResponseProto::clear_has_blocks() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetBlocksResponseProto::clear_blocks() {
  if (blocks_ != NULL) blocks_->::hadoop::hdfs::BlocksWithLocationsProto::Clear();
  clear_has_blocks();
}
inline const ::hadoop::hdfs::BlocksWithLocationsProto& GetBlocksResponseProto::blocks() const {
  return blocks_ != NULL ? *blocks_ : *default_instance_->blocks_;
}
inline ::hadoop::hdfs::BlocksWithLocationsProto* GetBlocksResponseProto::mutable_blocks() {
  set_has_blocks();
  if (blocks_ == NULL) blocks_ = new ::hadoop::hdfs::BlocksWithLocationsProto;
  return blocks_;
}
inline ::hadoop::hdfs::BlocksWithLocationsProto* GetBlocksResponseProto::release_blocks() {
  clear_has_blocks();
  ::hadoop::hdfs::BlocksWithLocationsProto* temp = blocks_;
  blocks_ = NULL;
  return temp;
}
inline void GetBlocksResponseProto::set_allocated_blocks(::hadoop::hdfs::BlocksWithLocationsProto* blocks) {
  delete blocks_;
  blocks_ = blocks;
  if (blocks) {
    set_has_blocks();
  } else {
    clear_has_blocks();
  }
}

// -------------------------------------------------------------------

// GetBlockKeysRequestProto

// -------------------------------------------------------------------

// GetBlockKeysResponseProto

// optional .hadoop.hdfs.ExportedBlockKeysProto keys = 1;
inline bool GetBlockKeysResponseProto::has_keys() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetBlockKeysResponseProto::set_has_keys() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetBlockKeysResponseProto::clear_has_keys() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetBlockKeysResponseProto::clear_keys() {
  if (keys_ != NULL) keys_->::hadoop::hdfs::ExportedBlockKeysProto::Clear();
  clear_has_keys();
}
inline const ::hadoop::hdfs::ExportedBlockKeysProto& GetBlockKeysResponseProto::keys() const {
  return keys_ != NULL ? *keys_ : *default_instance_->keys_;
}
inline ::hadoop::hdfs::ExportedBlockKeysProto* GetBlockKeysResponseProto::mutable_keys() {
  set_has_keys();
  if (keys_ == NULL) keys_ = new ::hadoop::hdfs::ExportedBlockKeysProto;
  return keys_;
}
inline ::hadoop::hdfs::ExportedBlockKeysProto* GetBlockKeysResponseProto::release_keys() {
  clear_has_keys();
  ::hadoop::hdfs::ExportedBlockKeysProto* temp = keys_;
  keys_ = NULL;
  return temp;
}
inline void GetBlockKeysResponseProto::set_allocated_keys(::hadoop::hdfs::ExportedBlockKeysProto* keys) {
  delete keys_;
  keys_ = keys;
  if (keys) {
    set_has_keys();
  } else {
    clear_has_keys();
  }
}

// -------------------------------------------------------------------

// GetTransactionIdRequestProto

// -------------------------------------------------------------------

// GetTransactionIdResponseProto

// required uint64 txId = 1;
inline bool GetTransactionIdResponseProto::has_txid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetTransactionIdResponseProto::set_has_txid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetTransactionIdResponseProto::clear_has_txid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetTransactionIdResponseProto::clear_txid() {
  txid_ = GOOGLE_ULONGLONG(0);
  clear_has_txid();
}
inline ::google::protobuf::uint64 GetTransactionIdResponseProto::txid() const {
  return txid_;
}
inline void GetTransactionIdResponseProto::set_txid(::google::protobuf::uint64 value) {
  set_has_txid();
  txid_ = value;
}

// -------------------------------------------------------------------

// RollEditLogRequestProto

// -------------------------------------------------------------------

// RollEditLogResponseProto

// required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
inline bool RollEditLogResponseProto::has_signature() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RollEditLogResponseProto::set_has_signature() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RollEditLogResponseProto::clear_has_signature() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RollEditLogResponseProto::clear_signature() {
  if (signature_ != NULL) signature_->::hadoop::hdfs::CheckpointSignatureProto::Clear();
  clear_has_signature();
}
inline const ::hadoop::hdfs::CheckpointSignatureProto& RollEditLogResponseProto::signature() const {
  return signature_ != NULL ? *signature_ : *default_instance_->signature_;
}
inline ::hadoop::hdfs::CheckpointSignatureProto* RollEditLogResponseProto::mutable_signature() {
  set_has_signature();
  if (signature_ == NULL) signature_ = new ::hadoop::hdfs::CheckpointSignatureProto;
  return signature_;
}
inline ::hadoop::hdfs::CheckpointSignatureProto* RollEditLogResponseProto::release_signature() {
  clear_has_signature();
  ::hadoop::hdfs::CheckpointSignatureProto* temp = signature_;
  signature_ = NULL;
  return temp;
}
inline void RollEditLogResponseProto::set_allocated_signature(::hadoop::hdfs::CheckpointSignatureProto* signature) {
  delete signature_;
  signature_ = signature;
  if (signature) {
    set_has_signature();
  } else {
    clear_has_signature();
  }
}

// -------------------------------------------------------------------

// GetMostRecentCheckpointTxIdRequestProto

// -------------------------------------------------------------------

// GetMostRecentCheckpointTxIdResponseProto

// required uint64 txId = 1;
inline bool GetMostRecentCheckpointTxIdResponseProto::has_txid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetMostRecentCheckpointTxIdResponseProto::set_has_txid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetMostRecentCheckpointTxIdResponseProto::clear_has_txid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetMostRecentCheckpointTxIdResponseProto::clear_txid() {
  txid_ = GOOGLE_ULONGLONG(0);
  clear_has_txid();
}
inline ::google::protobuf::uint64 GetMostRecentCheckpointTxIdResponseProto::txid() const {
  return txid_;
}
inline void GetMostRecentCheckpointTxIdResponseProto::set_txid(::google::protobuf::uint64 value) {
  set_has_txid();
  txid_ = value;
}

// -------------------------------------------------------------------

// ErrorReportRequestProto

// required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
inline bool ErrorReportRequestProto::has_registration() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ErrorReportRequestProto::set_has_registration() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ErrorReportRequestProto::clear_has_registration() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ErrorReportRequestProto::clear_registration() {
  if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
  clear_has_registration();
}
inline const ::hadoop::hdfs::NamenodeRegistrationProto& ErrorReportRequestProto::registration() const {
  return registration_ != NULL ? *registration_ : *default_instance_->registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* ErrorReportRequestProto::mutable_registration() {
  set_has_registration();
  if (registration_ == NULL) registration_ = new ::hadoop::hdfs::NamenodeRegistrationProto;
  return registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* ErrorReportRequestProto::release_registration() {
  clear_has_registration();
  ::hadoop::hdfs::NamenodeRegistrationProto* temp = registration_;
  registration_ = NULL;
  return temp;
}
inline void ErrorReportRequestProto::set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration) {
  delete registration_;
  registration_ = registration;
  if (registration) {
    set_has_registration();
  } else {
    clear_has_registration();
  }
}

// required uint32 errorCode = 2;
inline bool ErrorReportRequestProto::has_errorcode() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ErrorReportRequestProto::set_has_errorcode() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ErrorReportRequestProto::clear_has_errorcode() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ErrorReportRequestProto::clear_errorcode() {
  errorcode_ = 0u;
  clear_has_errorcode();
}
inline ::google::protobuf::uint32 ErrorReportRequestProto::errorcode() const {
  return errorcode_;
}
inline void ErrorReportRequestProto::set_errorcode(::google::protobuf::uint32 value) {
  set_has_errorcode();
  errorcode_ = value;
}

// required string msg = 3;
inline bool ErrorReportRequestProto::has_msg() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ErrorReportRequestProto::set_has_msg() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ErrorReportRequestProto::clear_has_msg() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ErrorReportRequestProto::clear_msg() {
  if (msg_ != &::google::protobuf::internal::kEmptyString) {
    msg_->clear();
  }
  clear_has_msg();
}
inline const ::std::string& ErrorReportRequestProto::msg() const {
  return *msg_;
}
inline void ErrorReportRequestProto::set_msg(const ::std::string& value) {
  set_has_msg();
  if (msg_ == &::google::protobuf::internal::kEmptyString) {
    msg_ = new ::std::string;
  }
  msg_->assign(value);
}
inline void ErrorReportRequestProto::set_msg(const char* value) {
  set_has_msg();
  if (msg_ == &::google::protobuf::internal::kEmptyString) {
    msg_ = new ::std::string;
  }
  msg_->assign(value);
}
inline void ErrorReportRequestProto::set_msg(const char* value, size_t size) {
  set_has_msg();
  if (msg_ == &::google::protobuf::internal::kEmptyString) {
    msg_ = new ::std::string;
  }
  msg_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ErrorReportRequestProto::mutable_msg() {
  set_has_msg();
  if (msg_ == &::google::protobuf::internal::kEmptyString) {
    msg_ = new ::std::string;
  }
  return msg_;
}
inline ::std::string* ErrorReportRequestProto::release_msg() {
  clear_has_msg();
  if (msg_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = msg_;
    msg_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ErrorReportRequestProto::set_allocated_msg(::std::string* msg) {
  if (msg_ != &::google::protobuf::internal::kEmptyString) {
    delete msg_;
  }
  if (msg) {
    set_has_msg();
    msg_ = msg;
  } else {
    clear_has_msg();
    msg_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ErrorReportResponseProto

// -------------------------------------------------------------------

// RegisterRequestProto

// required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
inline bool RegisterRequestProto::has_registration() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RegisterRequestProto::set_has_registration() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RegisterRequestProto::clear_has_registration() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RegisterRequestProto::clear_registration() {
  if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
  clear_has_registration();
}
inline const ::hadoop::hdfs::NamenodeRegistrationProto& RegisterRequestProto::registration() const {
  return registration_ != NULL ? *registration_ : *default_instance_->registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* RegisterRequestProto::mutable_registration() {
  set_has_registration();
  if (registration_ == NULL) registration_ = new ::hadoop::hdfs::NamenodeRegistrationProto;
  return registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* RegisterRequestProto::release_registration() {
  clear_has_registration();
  ::hadoop::hdfs::NamenodeRegistrationProto* temp = registration_;
  registration_ = NULL;
  return temp;
}
inline void RegisterRequestProto::set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration) {
  delete registration_;
  registration_ = registration;
  if (registration) {
    set_has_registration();
  } else {
    clear_has_registration();
  }
}

// -------------------------------------------------------------------

// RegisterResponseProto

// required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
inline bool RegisterResponseProto::has_registration() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RegisterResponseProto::set_has_registration() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RegisterResponseProto::clear_has_registration() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RegisterResponseProto::clear_registration() {
  if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
  clear_has_registration();
}
inline const ::hadoop::hdfs::NamenodeRegistrationProto& RegisterResponseProto::registration() const {
  return registration_ != NULL ? *registration_ : *default_instance_->registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* RegisterResponseProto::mutable_registration() {
  set_has_registration();
  if (registration_ == NULL) registration_ = new ::hadoop::hdfs::NamenodeRegistrationProto;
  return registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* RegisterResponseProto::release_registration() {
  clear_has_registration();
  ::hadoop::hdfs::NamenodeRegistrationProto* temp = registration_;
  registration_ = NULL;
  return temp;
}
inline void RegisterResponseProto::set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration) {
  delete registration_;
  registration_ = registration;
  if (registration) {
    set_has_registration();
  } else {
    clear_has_registration();
  }
}

// -------------------------------------------------------------------

// StartCheckpointRequestProto

// required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
inline bool StartCheckpointRequestProto::has_registration() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StartCheckpointRequestProto::set_has_registration() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StartCheckpointRequestProto::clear_has_registration() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StartCheckpointRequestProto::clear_registration() {
  if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
  clear_has_registration();
}
inline const ::hadoop::hdfs::NamenodeRegistrationProto& StartCheckpointRequestProto::registration() const {
  return registration_ != NULL ? *registration_ : *default_instance_->registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* StartCheckpointRequestProto::mutable_registration() {
  set_has_registration();
  if (registration_ == NULL) registration_ = new ::hadoop::hdfs::NamenodeRegistrationProto;
  return registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* StartCheckpointRequestProto::release_registration() {
  clear_has_registration();
  ::hadoop::hdfs::NamenodeRegistrationProto* temp = registration_;
  registration_ = NULL;
  return temp;
}
inline void StartCheckpointRequestProto::set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration) {
  delete registration_;
  registration_ = registration;
  if (registration) {
    set_has_registration();
  } else {
    clear_has_registration();
  }
}

// -------------------------------------------------------------------

// StartCheckpointResponseProto

// required .hadoop.hdfs.NamenodeCommandProto command = 1;
inline bool StartCheckpointResponseProto::has_command() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StartCheckpointResponseProto::set_has_command() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StartCheckpointResponseProto::clear_has_command() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StartCheckpointResponseProto::clear_command() {
  if (command_ != NULL) command_->::hadoop::hdfs::NamenodeCommandProto::Clear();
  clear_has_command();
}
inline const ::hadoop::hdfs::NamenodeCommandProto& StartCheckpointResponseProto::command() const {
  return command_ != NULL ? *command_ : *default_instance_->command_;
}
inline ::hadoop::hdfs::NamenodeCommandProto* StartCheckpointResponseProto::mutable_command() {
  set_has_command();
  if (command_ == NULL) command_ = new ::hadoop::hdfs::NamenodeCommandProto;
  return command_;
}
inline ::hadoop::hdfs::NamenodeCommandProto* StartCheckpointResponseProto::release_command() {
  clear_has_command();
  ::hadoop::hdfs::NamenodeCommandProto* temp = command_;
  command_ = NULL;
  return temp;
}
inline void StartCheckpointResponseProto::set_allocated_command(::hadoop::hdfs::NamenodeCommandProto* command) {
  delete command_;
  command_ = command;
  if (command) {
    set_has_command();
  } else {
    clear_has_command();
  }
}

// -------------------------------------------------------------------

// EndCheckpointRequestProto

// required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
inline bool EndCheckpointRequestProto::has_registration() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void EndCheckpointRequestProto::set_has_registration() {
  _has_bits_[0] |= 0x00000001u;
}
inline void EndCheckpointRequestProto::clear_has_registration() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void EndCheckpointRequestProto::clear_registration() {
  if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
  clear_has_registration();
}
inline const ::hadoop::hdfs::NamenodeRegistrationProto& EndCheckpointRequestProto::registration() const {
  return registration_ != NULL ? *registration_ : *default_instance_->registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* EndCheckpointRequestProto::mutable_registration() {
  set_has_registration();
  if (registration_ == NULL) registration_ = new ::hadoop::hdfs::NamenodeRegistrationProto;
  return registration_;
}
inline ::hadoop::hdfs::NamenodeRegistrationProto* EndCheckpointRequestProto::release_registration() {
  clear_has_registration();
  ::hadoop::hdfs::NamenodeRegistrationProto* temp = registration_;
  registration_ = NULL;
  return temp;
}
inline void EndCheckpointRequestProto::set_allocated_registration(::hadoop::hdfs::NamenodeRegistrationProto* registration) {
  delete registration_;
  registration_ = registration;
  if (registration) {
    set_has_registration();
  } else {
    clear_has_registration();
  }
}

// required .hadoop.hdfs.CheckpointSignatureProto signature = 2;
inline bool EndCheckpointRequestProto::has_signature() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void EndCheckpointRequestProto::set_has_signature() {
  _has_bits_[0] |= 0x00000002u;
}
inline void EndCheckpointRequestProto::clear_has_signature() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void EndCheckpointRequestProto::clear_signature() {
  if (signature_ != NULL) signature_->::hadoop::hdfs::CheckpointSignatureProto::Clear();
  clear_has_signature();
}
inline const ::hadoop::hdfs::CheckpointSignatureProto& EndCheckpointRequestProto::signature() const {
  return signature_ != NULL ? *signature_ : *default_instance_->signature_;
}
inline ::hadoop::hdfs::CheckpointSignatureProto* EndCheckpointRequestProto::mutable_signature() {
  set_has_signature();
  if (signature_ == NULL) signature_ = new ::hadoop::hdfs::CheckpointSignatureProto;
  return signature_;
}
inline ::hadoop::hdfs::CheckpointSignatureProto* EndCheckpointRequestProto::release_signature() {
  clear_has_signature();
  ::hadoop::hdfs::CheckpointSignatureProto* temp = signature_;
  signature_ = NULL;
  return temp;
}
inline void EndCheckpointRequestProto::set_allocated_signature(::hadoop::hdfs::CheckpointSignatureProto* signature) {
  delete signature_;
  signature_ = signature;
  if (signature) {
    set_has_signature();
  } else {
    clear_has_signature();
  }
}

// -------------------------------------------------------------------

// EndCheckpointResponseProto

// -------------------------------------------------------------------

// GetEditLogManifestRequestProto

// required uint64 sinceTxId = 1;
inline bool GetEditLogManifestRequestProto::has_sincetxid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetEditLogManifestRequestProto::set_has_sincetxid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetEditLogManifestRequestProto::clear_has_sincetxid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetEditLogManifestRequestProto::clear_sincetxid() {
  sincetxid_ = GOOGLE_ULONGLONG(0);
  clear_has_sincetxid();
}
inline ::google::protobuf::uint64 GetEditLogManifestRequestProto::sincetxid() const {
  return sincetxid_;
}
inline void GetEditLogManifestRequestProto::set_sincetxid(::google::protobuf::uint64 value) {
  set_has_sincetxid();
  sincetxid_ = value;
}

// -------------------------------------------------------------------

// GetEditLogManifestResponseProto

// required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
inline bool GetEditLogManifestResponseProto::has_manifest() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetEditLogManifestResponseProto::set_has_manifest() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetEditLogManifestResponseProto::clear_has_manifest() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetEditLogManifestResponseProto::clear_manifest() {
  if (manifest_ != NULL) manifest_->::hadoop::hdfs::RemoteEditLogManifestProto::Clear();
  clear_has_manifest();
}
inline const ::hadoop::hdfs::RemoteEditLogManifestProto& GetEditLogManifestResponseProto::manifest() const {
  return manifest_ != NULL ? *manifest_ : *default_instance_->manifest_;
}
inline ::hadoop::hdfs::RemoteEditLogManifestProto* GetEditLogManifestResponseProto::mutable_manifest() {
  set_has_manifest();
  if (manifest_ == NULL) manifest_ = new ::hadoop::hdfs::RemoteEditLogManifestProto;
  return manifest_;
}
inline ::hadoop::hdfs::RemoteEditLogManifestProto* GetEditLogManifestResponseProto::release_manifest() {
  clear_has_manifest();
  ::hadoop::hdfs::RemoteEditLogManifestProto* temp = manifest_;
  manifest_ = NULL;
  return temp;
}
inline void GetEditLogManifestResponseProto::set_allocated_manifest(::hadoop::hdfs::RemoteEditLogManifestProto* manifest) {
  delete manifest_;
  manifest_ = manifest;
  if (manifest) {
    set_has_manifest();
  } else {
    clear_has_manifest();
  }
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace namenode
}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_NamenodeProtocol_2eproto__INCLUDED
