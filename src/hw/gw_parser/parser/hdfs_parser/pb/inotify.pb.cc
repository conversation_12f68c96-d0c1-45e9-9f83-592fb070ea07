// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: inotify.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "inotify.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

namespace {

const ::google::protobuf::Descriptor* EventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  EventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CreateEventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CreateEventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CloseEventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CloseEventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* AppendEventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AppendEventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RenameEventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RenameEventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* MetadataUpdateEventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MetadataUpdateEventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* UnlinkEventProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  UnlinkEventProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* EventsListProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  EventsListProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* EventType_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* INodeType_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* MetadataUpdateType_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_inotify_2eproto() {
  protobuf_AddDesc_inotify_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "inotify.proto");
  GOOGLE_CHECK(file != NULL);
  EventProto_descriptor_ = file->message_type(0);
  static const int EventProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventProto, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventProto, contents_),
  };
  EventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      EventProto_descriptor_,
      EventProto::default_instance_,
      EventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(EventProto));
  CreateEventProto_descriptor_ = file->message_type(1);
  static const int CreateEventProto_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, path_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, ctime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, ownername_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, groupname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, perms_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, replication_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, symlinktarget_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, overwrite_),
  };
  CreateEventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CreateEventProto_descriptor_,
      CreateEventProto::default_instance_,
      CreateEventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CreateEventProto));
  CloseEventProto_descriptor_ = file->message_type(2);
  static const int CloseEventProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CloseEventProto, path_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CloseEventProto, filesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CloseEventProto, timestamp_),
  };
  CloseEventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CloseEventProto_descriptor_,
      CloseEventProto::default_instance_,
      CloseEventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CloseEventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CloseEventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CloseEventProto));
  AppendEventProto_descriptor_ = file->message_type(3);
  static const int AppendEventProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AppendEventProto, path_),
  };
  AppendEventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AppendEventProto_descriptor_,
      AppendEventProto::default_instance_,
      AppendEventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AppendEventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AppendEventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AppendEventProto));
  RenameEventProto_descriptor_ = file->message_type(4);
  static const int RenameEventProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenameEventProto, srcpath_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenameEventProto, destpath_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenameEventProto, timestamp_),
  };
  RenameEventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RenameEventProto_descriptor_,
      RenameEventProto::default_instance_,
      RenameEventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenameEventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenameEventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RenameEventProto));
  MetadataUpdateEventProto_descriptor_ = file->message_type(5);
  static const int MetadataUpdateEventProto_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, path_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, mtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, atime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, replication_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, ownername_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, groupname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, perms_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, acls_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, xattrs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, xattrsremoved_),
  };
  MetadataUpdateEventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      MetadataUpdateEventProto_descriptor_,
      MetadataUpdateEventProto::default_instance_,
      MetadataUpdateEventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MetadataUpdateEventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(MetadataUpdateEventProto));
  UnlinkEventProto_descriptor_ = file->message_type(6);
  static const int UnlinkEventProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnlinkEventProto, path_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnlinkEventProto, timestamp_),
  };
  UnlinkEventProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      UnlinkEventProto_descriptor_,
      UnlinkEventProto::default_instance_,
      UnlinkEventProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnlinkEventProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnlinkEventProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(UnlinkEventProto));
  EventsListProto_descriptor_ = file->message_type(7);
  static const int EventsListProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventsListProto, events_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventsListProto, firsttxid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventsListProto, lasttxid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventsListProto, synctxid_),
  };
  EventsListProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      EventsListProto_descriptor_,
      EventsListProto::default_instance_,
      EventsListProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventsListProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EventsListProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(EventsListProto));
  EventType_descriptor_ = file->enum_type(0);
  INodeType_descriptor_ = file->enum_type(1);
  MetadataUpdateType_descriptor_ = file->enum_type(2);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_inotify_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    EventProto_descriptor_, &EventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CreateEventProto_descriptor_, &CreateEventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CloseEventProto_descriptor_, &CloseEventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AppendEventProto_descriptor_, &AppendEventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RenameEventProto_descriptor_, &RenameEventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    MetadataUpdateEventProto_descriptor_, &MetadataUpdateEventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    UnlinkEventProto_descriptor_, &UnlinkEventProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    EventsListProto_descriptor_, &EventsListProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_inotify_2eproto() {
  delete EventProto::default_instance_;
  delete EventProto_reflection_;
  delete CreateEventProto::default_instance_;
  delete CreateEventProto_reflection_;
  delete CloseEventProto::default_instance_;
  delete CloseEventProto_reflection_;
  delete AppendEventProto::default_instance_;
  delete AppendEventProto_reflection_;
  delete RenameEventProto::default_instance_;
  delete RenameEventProto_reflection_;
  delete MetadataUpdateEventProto::default_instance_;
  delete MetadataUpdateEventProto_reflection_;
  delete UnlinkEventProto::default_instance_;
  delete UnlinkEventProto_reflection_;
  delete EventsListProto::default_instance_;
  delete EventsListProto_reflection_;
}

void protobuf_AddDesc_inotify_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::hdfs::protobuf_AddDesc_acl_2eproto();
  ::hadoop::hdfs::protobuf_AddDesc_xattr_2eproto();
  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rinotify.proto\022\013hadoop.hdfs\032\tacl.proto\032"
    "\013xattr.proto\032\nhdfs.proto\"D\n\nEventProto\022$"
    "\n\004type\030\001 \002(\0162\026.hadoop.hdfs.EventType\022\020\n\010"
    "contents\030\002 \002(\014\"\351\001\n\020CreateEventProto\022$\n\004t"
    "ype\030\001 \002(\0162\026.hadoop.hdfs.INodeType\022\014\n\004pat"
    "h\030\002 \002(\t\022\r\n\005ctime\030\003 \002(\003\022\021\n\townerName\030\004 \002("
    "\t\022\021\n\tgroupName\030\005 \002(\t\022-\n\005perms\030\006 \002(\0132\036.ha"
    "doop.hdfs.FsPermissionProto\022\023\n\013replicati"
    "on\030\007 \001(\005\022\025\n\rsymlinkTarget\030\010 \001(\t\022\021\n\toverw"
    "rite\030\t \001(\010\"D\n\017CloseEventProto\022\014\n\004path\030\001 "
    "\002(\t\022\020\n\010fileSize\030\002 \002(\003\022\021\n\ttimestamp\030\003 \002(\003"
    "\" \n\020AppendEventProto\022\014\n\004path\030\001 \002(\t\"H\n\020Re"
    "nameEventProto\022\017\n\007srcPath\030\001 \002(\t\022\020\n\010destP"
    "ath\030\002 \002(\t\022\021\n\ttimestamp\030\003 \002(\003\"\311\002\n\030Metadat"
    "aUpdateEventProto\022\014\n\004path\030\001 \002(\t\022-\n\004type\030"
    "\002 \002(\0162\037.hadoop.hdfs.MetadataUpdateType\022\r"
    "\n\005mtime\030\003 \001(\003\022\r\n\005atime\030\004 \001(\003\022\023\n\013replicat"
    "ion\030\005 \001(\005\022\021\n\townerName\030\006 \001(\t\022\021\n\tgroupNam"
    "e\030\007 \001(\t\022-\n\005perms\030\010 \001(\0132\036.hadoop.hdfs.FsP"
    "ermissionProto\022(\n\004acls\030\t \003(\0132\032.hadoop.hd"
    "fs.AclEntryProto\022\'\n\006xAttrs\030\n \003(\0132\027.hadoo"
    "p.hdfs.XAttrProto\022\025\n\rxAttrsRemoved\030\013 \001(\010"
    "\"3\n\020UnlinkEventProto\022\014\n\004path\030\001 \002(\t\022\021\n\tti"
    "mestamp\030\002 \002(\003\"q\n\017EventsListProto\022\'\n\006even"
    "ts\030\001 \003(\0132\027.hadoop.hdfs.EventProto\022\021\n\tfir"
    "stTxid\030\002 \002(\003\022\020\n\010lastTxid\030\003 \002(\003\022\020\n\010syncTx"
    "id\030\004 \002(\003*x\n\tEventType\022\020\n\014EVENT_CREATE\020\000\022"
    "\017\n\013EVENT_CLOSE\020\001\022\020\n\014EVENT_APPEND\020\002\022\020\n\014EV"
    "ENT_RENAME\020\003\022\022\n\016EVENT_METADATA\020\004\022\020\n\014EVEN"
    "T_UNLINK\020\005*F\n\tINodeType\022\017\n\013I_TYPE_FILE\020\000"
    "\022\024\n\020I_TYPE_DIRECTORY\020\001\022\022\n\016I_TYPE_SYMLINK"
    "\020\002*\230\001\n\022MetadataUpdateType\022\023\n\017META_TYPE_T"
    "IMES\020\000\022\031\n\025META_TYPE_REPLICATION\020\001\022\023\n\017MET"
    "A_TYPE_OWNER\020\002\022\023\n\017META_TYPE_PERMS\020\003\022\022\n\016M"
    "ETA_TYPE_ACLS\020\004\022\024\n\020META_TYPE_XATTRS\020\005B9\n"
    "%org.apache.hadoop.hdfs.protocol.protoB\r"
    "InotifyProtos\240\001\001", 1456);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "inotify.proto", &protobuf_RegisterTypes);
  EventProto::default_instance_ = new EventProto();
  CreateEventProto::default_instance_ = new CreateEventProto();
  CloseEventProto::default_instance_ = new CloseEventProto();
  AppendEventProto::default_instance_ = new AppendEventProto();
  RenameEventProto::default_instance_ = new RenameEventProto();
  MetadataUpdateEventProto::default_instance_ = new MetadataUpdateEventProto();
  UnlinkEventProto::default_instance_ = new UnlinkEventProto();
  EventsListProto::default_instance_ = new EventsListProto();
  EventProto::default_instance_->InitAsDefaultInstance();
  CreateEventProto::default_instance_->InitAsDefaultInstance();
  CloseEventProto::default_instance_->InitAsDefaultInstance();
  AppendEventProto::default_instance_->InitAsDefaultInstance();
  RenameEventProto::default_instance_->InitAsDefaultInstance();
  MetadataUpdateEventProto::default_instance_->InitAsDefaultInstance();
  UnlinkEventProto::default_instance_->InitAsDefaultInstance();
  EventsListProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_inotify_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_inotify_2eproto {
  StaticDescriptorInitializer_inotify_2eproto() {
    protobuf_AddDesc_inotify_2eproto();
  }
} static_descriptor_initializer_inotify_2eproto_;
const ::google::protobuf::EnumDescriptor* EventType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EventType_descriptor_;
}
bool EventType_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* INodeType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeType_descriptor_;
}
bool INodeType_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* MetadataUpdateType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MetadataUpdateType_descriptor_;
}
bool MetadataUpdateType_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}


// ===================================================================

#ifndef _MSC_VER
const int EventProto::kTypeFieldNumber;
const int EventProto::kContentsFieldNumber;
#endif  // !_MSC_VER

EventProto::EventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void EventProto::InitAsDefaultInstance() {
}

EventProto::EventProto(const EventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void EventProto::SharedCtor() {
  _cached_size_ = 0;
  type_ = 0;
  contents_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

EventProto::~EventProto() {
  SharedDtor();
}

void EventProto::SharedDtor() {
  if (contents_ != &::google::protobuf::internal::kEmptyString) {
    delete contents_;
  }
  if (this != default_instance_) {
  }
}

void EventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EventProto_descriptor_;
}

const EventProto& EventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

EventProto* EventProto::default_instance_ = NULL;

EventProto* EventProto::New() const {
  return new EventProto;
}

void EventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    type_ = 0;
    if (has_contents()) {
      if (contents_ != &::google::protobuf::internal::kEmptyString) {
        contents_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool EventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.EventType type = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::EventType_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::EventType >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_contents;
        break;
      }

      // required bytes contents = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_contents:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_contents()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void EventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.EventType type = 1;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // required bytes contents = 2;
  if (has_contents()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->contents(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* EventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.EventType type = 1;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // required bytes contents = 2;
  if (has_contents()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->contents(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int EventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.EventType type = 1;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // required bytes contents = 2;
    if (has_contents()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->contents());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const EventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const EventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void EventProto::MergeFrom(const EventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_contents()) {
      set_contents(from.contents());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void EventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EventProto::CopyFrom(const EventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void EventProto::Swap(EventProto* other) {
  if (other != this) {
    std::swap(type_, other->type_);
    std::swap(contents_, other->contents_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata EventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = EventProto_descriptor_;
  metadata.reflection = EventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CreateEventProto::kTypeFieldNumber;
const int CreateEventProto::kPathFieldNumber;
const int CreateEventProto::kCtimeFieldNumber;
const int CreateEventProto::kOwnerNameFieldNumber;
const int CreateEventProto::kGroupNameFieldNumber;
const int CreateEventProto::kPermsFieldNumber;
const int CreateEventProto::kReplicationFieldNumber;
const int CreateEventProto::kSymlinkTargetFieldNumber;
const int CreateEventProto::kOverwriteFieldNumber;
#endif  // !_MSC_VER

CreateEventProto::CreateEventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CreateEventProto::InitAsDefaultInstance() {
  perms_ = const_cast< ::hadoop::hdfs::FsPermissionProto*>(&::hadoop::hdfs::FsPermissionProto::default_instance());
}

CreateEventProto::CreateEventProto(const CreateEventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CreateEventProto::SharedCtor() {
  _cached_size_ = 0;
  type_ = 0;
  path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ctime_ = GOOGLE_LONGLONG(0);
  ownername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  groupname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  perms_ = NULL;
  replication_ = 0;
  symlinktarget_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  overwrite_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CreateEventProto::~CreateEventProto() {
  SharedDtor();
}

void CreateEventProto::SharedDtor() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (ownername_ != &::google::protobuf::internal::kEmptyString) {
    delete ownername_;
  }
  if (groupname_ != &::google::protobuf::internal::kEmptyString) {
    delete groupname_;
  }
  if (symlinktarget_ != &::google::protobuf::internal::kEmptyString) {
    delete symlinktarget_;
  }
  if (this != default_instance_) {
    delete perms_;
  }
}

void CreateEventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CreateEventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CreateEventProto_descriptor_;
}

const CreateEventProto& CreateEventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

CreateEventProto* CreateEventProto::default_instance_ = NULL;

CreateEventProto* CreateEventProto::New() const {
  return new CreateEventProto;
}

void CreateEventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    type_ = 0;
    if (has_path()) {
      if (path_ != &::google::protobuf::internal::kEmptyString) {
        path_->clear();
      }
    }
    ctime_ = GOOGLE_LONGLONG(0);
    if (has_ownername()) {
      if (ownername_ != &::google::protobuf::internal::kEmptyString) {
        ownername_->clear();
      }
    }
    if (has_groupname()) {
      if (groupname_ != &::google::protobuf::internal::kEmptyString) {
        groupname_->clear();
      }
    }
    if (has_perms()) {
      if (perms_ != NULL) perms_->::hadoop::hdfs::FsPermissionProto::Clear();
    }
    replication_ = 0;
    if (has_symlinktarget()) {
      if (symlinktarget_ != &::google::protobuf::internal::kEmptyString) {
        symlinktarget_->clear();
      }
    }
  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    overwrite_ = false;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CreateEventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.INodeType type = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::INodeType_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::INodeType >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_path;
        break;
      }

      // required string path = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_path:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->path().data(), this->path().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_ctime;
        break;
      }

      // required int64 ctime = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_ctime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ctime_)));
          set_has_ctime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_ownerName;
        break;
      }

      // required string ownerName = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_ownerName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_ownername()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->ownername().data(), this->ownername().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_groupName;
        break;
      }

      // required string groupName = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_groupName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_groupname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->groupname().data(), this->groupname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_perms;
        break;
      }

      // required .hadoop.hdfs.FsPermissionProto perms = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_perms:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_perms()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(56)) goto parse_replication;
        break;
      }

      // optional int32 replication = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_replication:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &replication_)));
          set_has_replication();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(66)) goto parse_symlinkTarget;
        break;
      }

      // optional string symlinkTarget = 8;
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_symlinkTarget:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symlinktarget()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->symlinktarget().data(), this->symlinktarget().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(72)) goto parse_overwrite;
        break;
      }

      // optional bool overwrite = 9;
      case 9: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_overwrite:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &overwrite_)));
          set_has_overwrite();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CreateEventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.INodeType type = 1;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // required string path = 2;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->path(), output);
  }

  // required int64 ctime = 3;
  if (has_ctime()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->ctime(), output);
  }

  // required string ownerName = 4;
  if (has_ownername()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->ownername().data(), this->ownername().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->ownername(), output);
  }

  // required string groupName = 5;
  if (has_groupname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->groupname().data(), this->groupname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->groupname(), output);
  }

  // required .hadoop.hdfs.FsPermissionProto perms = 6;
  if (has_perms()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->perms(), output);
  }

  // optional int32 replication = 7;
  if (has_replication()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->replication(), output);
  }

  // optional string symlinkTarget = 8;
  if (has_symlinktarget()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->symlinktarget().data(), this->symlinktarget().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      8, this->symlinktarget(), output);
  }

  // optional bool overwrite = 9;
  if (has_overwrite()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(9, this->overwrite(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CreateEventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.INodeType type = 1;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // required string path = 2;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->path(), target);
  }

  // required int64 ctime = 3;
  if (has_ctime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->ctime(), target);
  }

  // required string ownerName = 4;
  if (has_ownername()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->ownername().data(), this->ownername().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->ownername(), target);
  }

  // required string groupName = 5;
  if (has_groupname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->groupname().data(), this->groupname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->groupname(), target);
  }

  // required .hadoop.hdfs.FsPermissionProto perms = 6;
  if (has_perms()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        6, this->perms(), target);
  }

  // optional int32 replication = 7;
  if (has_replication()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->replication(), target);
  }

  // optional string symlinkTarget = 8;
  if (has_symlinktarget()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->symlinktarget().data(), this->symlinktarget().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->symlinktarget(), target);
  }

  // optional bool overwrite = 9;
  if (has_overwrite()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(9, this->overwrite(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CreateEventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.INodeType type = 1;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // required string path = 2;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

    // required int64 ctime = 3;
    if (has_ctime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->ctime());
    }

    // required string ownerName = 4;
    if (has_ownername()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->ownername());
    }

    // required string groupName = 5;
    if (has_groupname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->groupname());
    }

    // required .hadoop.hdfs.FsPermissionProto perms = 6;
    if (has_perms()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->perms());
    }

    // optional int32 replication = 7;
    if (has_replication()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->replication());
    }

    // optional string symlinkTarget = 8;
    if (has_symlinktarget()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->symlinktarget());
    }

  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    // optional bool overwrite = 9;
    if (has_overwrite()) {
      total_size += 1 + 1;
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CreateEventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CreateEventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CreateEventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CreateEventProto::MergeFrom(const CreateEventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_path()) {
      set_path(from.path());
    }
    if (from.has_ctime()) {
      set_ctime(from.ctime());
    }
    if (from.has_ownername()) {
      set_ownername(from.ownername());
    }
    if (from.has_groupname()) {
      set_groupname(from.groupname());
    }
    if (from.has_perms()) {
      mutable_perms()->::hadoop::hdfs::FsPermissionProto::MergeFrom(from.perms());
    }
    if (from.has_replication()) {
      set_replication(from.replication());
    }
    if (from.has_symlinktarget()) {
      set_symlinktarget(from.symlinktarget());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_overwrite()) {
      set_overwrite(from.overwrite());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CreateEventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateEventProto::CopyFrom(const CreateEventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateEventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000003f) != 0x0000003f) return false;

  if (has_perms()) {
    if (!this->perms().IsInitialized()) return false;
  }
  return true;
}

void CreateEventProto::Swap(CreateEventProto* other) {
  if (other != this) {
    std::swap(type_, other->type_);
    std::swap(path_, other->path_);
    std::swap(ctime_, other->ctime_);
    std::swap(ownername_, other->ownername_);
    std::swap(groupname_, other->groupname_);
    std::swap(perms_, other->perms_);
    std::swap(replication_, other->replication_);
    std::swap(symlinktarget_, other->symlinktarget_);
    std::swap(overwrite_, other->overwrite_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CreateEventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CreateEventProto_descriptor_;
  metadata.reflection = CreateEventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CloseEventProto::kPathFieldNumber;
const int CloseEventProto::kFileSizeFieldNumber;
const int CloseEventProto::kTimestampFieldNumber;
#endif  // !_MSC_VER

CloseEventProto::CloseEventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CloseEventProto::InitAsDefaultInstance() {
}

CloseEventProto::CloseEventProto(const CloseEventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CloseEventProto::SharedCtor() {
  _cached_size_ = 0;
  path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  filesize_ = GOOGLE_LONGLONG(0);
  timestamp_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CloseEventProto::~CloseEventProto() {
  SharedDtor();
}

void CloseEventProto::SharedDtor() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (this != default_instance_) {
  }
}

void CloseEventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CloseEventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CloseEventProto_descriptor_;
}

const CloseEventProto& CloseEventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

CloseEventProto* CloseEventProto::default_instance_ = NULL;

CloseEventProto* CloseEventProto::New() const {
  return new CloseEventProto;
}

void CloseEventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_path()) {
      if (path_ != &::google::protobuf::internal::kEmptyString) {
        path_->clear();
      }
    }
    filesize_ = GOOGLE_LONGLONG(0);
    timestamp_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CloseEventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string path = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->path().data(), this->path().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_fileSize;
        break;
      }

      // required int64 fileSize = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_fileSize:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &filesize_)));
          set_has_filesize();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_timestamp;
        break;
      }

      // required int64 timestamp = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_timestamp:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &timestamp_)));
          set_has_timestamp();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CloseEventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->path(), output);
  }

  // required int64 fileSize = 2;
  if (has_filesize()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->filesize(), output);
  }

  // required int64 timestamp = 3;
  if (has_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->timestamp(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CloseEventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->path(), target);
  }

  // required int64 fileSize = 2;
  if (has_filesize()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->filesize(), target);
  }

  // required int64 timestamp = 3;
  if (has_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->timestamp(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CloseEventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string path = 1;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

    // required int64 fileSize = 2;
    if (has_filesize()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->filesize());
    }

    // required int64 timestamp = 3;
    if (has_timestamp()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->timestamp());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CloseEventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CloseEventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CloseEventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CloseEventProto::MergeFrom(const CloseEventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_path()) {
      set_path(from.path());
    }
    if (from.has_filesize()) {
      set_filesize(from.filesize());
    }
    if (from.has_timestamp()) {
      set_timestamp(from.timestamp());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CloseEventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CloseEventProto::CopyFrom(const CloseEventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CloseEventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void CloseEventProto::Swap(CloseEventProto* other) {
  if (other != this) {
    std::swap(path_, other->path_);
    std::swap(filesize_, other->filesize_);
    std::swap(timestamp_, other->timestamp_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CloseEventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CloseEventProto_descriptor_;
  metadata.reflection = CloseEventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int AppendEventProto::kPathFieldNumber;
#endif  // !_MSC_VER

AppendEventProto::AppendEventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AppendEventProto::InitAsDefaultInstance() {
}

AppendEventProto::AppendEventProto(const AppendEventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AppendEventProto::SharedCtor() {
  _cached_size_ = 0;
  path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AppendEventProto::~AppendEventProto() {
  SharedDtor();
}

void AppendEventProto::SharedDtor() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (this != default_instance_) {
  }
}

void AppendEventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AppendEventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AppendEventProto_descriptor_;
}

const AppendEventProto& AppendEventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

AppendEventProto* AppendEventProto::default_instance_ = NULL;

AppendEventProto* AppendEventProto::New() const {
  return new AppendEventProto;
}

void AppendEventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_path()) {
      if (path_ != &::google::protobuf::internal::kEmptyString) {
        path_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AppendEventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string path = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->path().data(), this->path().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AppendEventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->path(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AppendEventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->path(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AppendEventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string path = 1;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AppendEventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AppendEventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AppendEventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AppendEventProto::MergeFrom(const AppendEventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_path()) {
      set_path(from.path());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AppendEventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AppendEventProto::CopyFrom(const AppendEventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AppendEventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void AppendEventProto::Swap(AppendEventProto* other) {
  if (other != this) {
    std::swap(path_, other->path_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AppendEventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AppendEventProto_descriptor_;
  metadata.reflection = AppendEventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RenameEventProto::kSrcPathFieldNumber;
const int RenameEventProto::kDestPathFieldNumber;
const int RenameEventProto::kTimestampFieldNumber;
#endif  // !_MSC_VER

RenameEventProto::RenameEventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RenameEventProto::InitAsDefaultInstance() {
}

RenameEventProto::RenameEventProto(const RenameEventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RenameEventProto::SharedCtor() {
  _cached_size_ = 0;
  srcpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  destpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  timestamp_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RenameEventProto::~RenameEventProto() {
  SharedDtor();
}

void RenameEventProto::SharedDtor() {
  if (srcpath_ != &::google::protobuf::internal::kEmptyString) {
    delete srcpath_;
  }
  if (destpath_ != &::google::protobuf::internal::kEmptyString) {
    delete destpath_;
  }
  if (this != default_instance_) {
  }
}

void RenameEventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RenameEventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RenameEventProto_descriptor_;
}

const RenameEventProto& RenameEventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

RenameEventProto* RenameEventProto::default_instance_ = NULL;

RenameEventProto* RenameEventProto::New() const {
  return new RenameEventProto;
}

void RenameEventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_srcpath()) {
      if (srcpath_ != &::google::protobuf::internal::kEmptyString) {
        srcpath_->clear();
      }
    }
    if (has_destpath()) {
      if (destpath_ != &::google::protobuf::internal::kEmptyString) {
        destpath_->clear();
      }
    }
    timestamp_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RenameEventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string srcPath = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_srcpath()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->srcpath().data(), this->srcpath().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_destPath;
        break;
      }

      // required string destPath = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_destPath:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_destpath()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->destpath().data(), this->destpath().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_timestamp;
        break;
      }

      // required int64 timestamp = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_timestamp:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &timestamp_)));
          set_has_timestamp();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RenameEventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string srcPath = 1;
  if (has_srcpath()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->srcpath().data(), this->srcpath().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->srcpath(), output);
  }

  // required string destPath = 2;
  if (has_destpath()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->destpath().data(), this->destpath().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->destpath(), output);
  }

  // required int64 timestamp = 3;
  if (has_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->timestamp(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RenameEventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string srcPath = 1;
  if (has_srcpath()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->srcpath().data(), this->srcpath().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->srcpath(), target);
  }

  // required string destPath = 2;
  if (has_destpath()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->destpath().data(), this->destpath().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->destpath(), target);
  }

  // required int64 timestamp = 3;
  if (has_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->timestamp(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RenameEventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string srcPath = 1;
    if (has_srcpath()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->srcpath());
    }

    // required string destPath = 2;
    if (has_destpath()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->destpath());
    }

    // required int64 timestamp = 3;
    if (has_timestamp()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->timestamp());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RenameEventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RenameEventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RenameEventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RenameEventProto::MergeFrom(const RenameEventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_srcpath()) {
      set_srcpath(from.srcpath());
    }
    if (from.has_destpath()) {
      set_destpath(from.destpath());
    }
    if (from.has_timestamp()) {
      set_timestamp(from.timestamp());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RenameEventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RenameEventProto::CopyFrom(const RenameEventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RenameEventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void RenameEventProto::Swap(RenameEventProto* other) {
  if (other != this) {
    std::swap(srcpath_, other->srcpath_);
    std::swap(destpath_, other->destpath_);
    std::swap(timestamp_, other->timestamp_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RenameEventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RenameEventProto_descriptor_;
  metadata.reflection = RenameEventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int MetadataUpdateEventProto::kPathFieldNumber;
const int MetadataUpdateEventProto::kTypeFieldNumber;
const int MetadataUpdateEventProto::kMtimeFieldNumber;
const int MetadataUpdateEventProto::kAtimeFieldNumber;
const int MetadataUpdateEventProto::kReplicationFieldNumber;
const int MetadataUpdateEventProto::kOwnerNameFieldNumber;
const int MetadataUpdateEventProto::kGroupNameFieldNumber;
const int MetadataUpdateEventProto::kPermsFieldNumber;
const int MetadataUpdateEventProto::kAclsFieldNumber;
const int MetadataUpdateEventProto::kXAttrsFieldNumber;
const int MetadataUpdateEventProto::kXAttrsRemovedFieldNumber;
#endif  // !_MSC_VER

MetadataUpdateEventProto::MetadataUpdateEventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void MetadataUpdateEventProto::InitAsDefaultInstance() {
  perms_ = const_cast< ::hadoop::hdfs::FsPermissionProto*>(&::hadoop::hdfs::FsPermissionProto::default_instance());
}

MetadataUpdateEventProto::MetadataUpdateEventProto(const MetadataUpdateEventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void MetadataUpdateEventProto::SharedCtor() {
  _cached_size_ = 0;
  path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  type_ = 0;
  mtime_ = GOOGLE_LONGLONG(0);
  atime_ = GOOGLE_LONGLONG(0);
  replication_ = 0;
  ownername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  groupname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  perms_ = NULL;
  xattrsremoved_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

MetadataUpdateEventProto::~MetadataUpdateEventProto() {
  SharedDtor();
}

void MetadataUpdateEventProto::SharedDtor() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (ownername_ != &::google::protobuf::internal::kEmptyString) {
    delete ownername_;
  }
  if (groupname_ != &::google::protobuf::internal::kEmptyString) {
    delete groupname_;
  }
  if (this != default_instance_) {
    delete perms_;
  }
}

void MetadataUpdateEventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MetadataUpdateEventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MetadataUpdateEventProto_descriptor_;
}

const MetadataUpdateEventProto& MetadataUpdateEventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

MetadataUpdateEventProto* MetadataUpdateEventProto::default_instance_ = NULL;

MetadataUpdateEventProto* MetadataUpdateEventProto::New() const {
  return new MetadataUpdateEventProto;
}

void MetadataUpdateEventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_path()) {
      if (path_ != &::google::protobuf::internal::kEmptyString) {
        path_->clear();
      }
    }
    type_ = 0;
    mtime_ = GOOGLE_LONGLONG(0);
    atime_ = GOOGLE_LONGLONG(0);
    replication_ = 0;
    if (has_ownername()) {
      if (ownername_ != &::google::protobuf::internal::kEmptyString) {
        ownername_->clear();
      }
    }
    if (has_groupname()) {
      if (groupname_ != &::google::protobuf::internal::kEmptyString) {
        groupname_->clear();
      }
    }
    if (has_perms()) {
      if (perms_ != NULL) perms_->::hadoop::hdfs::FsPermissionProto::Clear();
    }
  }
  if (_has_bits_[10 / 32] & (0xffu << (10 % 32))) {
    xattrsremoved_ = false;
  }
  acls_.Clear();
  xattrs_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool MetadataUpdateEventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string path = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->path().data(), this->path().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_type;
        break;
      }

      // required .hadoop.hdfs.MetadataUpdateType type = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_type:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::MetadataUpdateType_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::MetadataUpdateType >(value));
          } else {
            mutable_unknown_fields()->AddVarint(2, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_mtime;
        break;
      }

      // optional int64 mtime = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_mtime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &mtime_)));
          set_has_mtime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_atime;
        break;
      }

      // optional int64 atime = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_atime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &atime_)));
          set_has_atime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_replication;
        break;
      }

      // optional int32 replication = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_replication:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &replication_)));
          set_has_replication();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_ownerName;
        break;
      }

      // optional string ownerName = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_ownerName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_ownername()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->ownername().data(), this->ownername().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(58)) goto parse_groupName;
        break;
      }

      // optional string groupName = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_groupName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_groupname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->groupname().data(), this->groupname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(66)) goto parse_perms;
        break;
      }

      // optional .hadoop.hdfs.FsPermissionProto perms = 8;
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_perms:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_perms()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(74)) goto parse_acls;
        break;
      }

      // repeated .hadoop.hdfs.AclEntryProto acls = 9;
      case 9: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_acls:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_acls()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(74)) goto parse_acls;
        if (input->ExpectTag(82)) goto parse_xAttrs;
        break;
      }

      // repeated .hadoop.hdfs.XAttrProto xAttrs = 10;
      case 10: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(82)) goto parse_xAttrs;
        if (input->ExpectTag(88)) goto parse_xAttrsRemoved;
        break;
      }

      // optional bool xAttrsRemoved = 11;
      case 11: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_xAttrsRemoved:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &xattrsremoved_)));
          set_has_xattrsremoved();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void MetadataUpdateEventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->path(), output);
  }

  // required .hadoop.hdfs.MetadataUpdateType type = 2;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->type(), output);
  }

  // optional int64 mtime = 3;
  if (has_mtime()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->mtime(), output);
  }

  // optional int64 atime = 4;
  if (has_atime()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->atime(), output);
  }

  // optional int32 replication = 5;
  if (has_replication()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->replication(), output);
  }

  // optional string ownerName = 6;
  if (has_ownername()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->ownername().data(), this->ownername().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      6, this->ownername(), output);
  }

  // optional string groupName = 7;
  if (has_groupname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->groupname().data(), this->groupname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      7, this->groupname(), output);
  }

  // optional .hadoop.hdfs.FsPermissionProto perms = 8;
  if (has_perms()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->perms(), output);
  }

  // repeated .hadoop.hdfs.AclEntryProto acls = 9;
  for (int i = 0; i < this->acls_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->acls(i), output);
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 10;
  for (int i = 0; i < this->xattrs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->xattrs(i), output);
  }

  // optional bool xAttrsRemoved = 11;
  if (has_xattrsremoved()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(11, this->xattrsremoved(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* MetadataUpdateEventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->path(), target);
  }

  // required .hadoop.hdfs.MetadataUpdateType type = 2;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->type(), target);
  }

  // optional int64 mtime = 3;
  if (has_mtime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->mtime(), target);
  }

  // optional int64 atime = 4;
  if (has_atime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->atime(), target);
  }

  // optional int32 replication = 5;
  if (has_replication()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->replication(), target);
  }

  // optional string ownerName = 6;
  if (has_ownername()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->ownername().data(), this->ownername().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->ownername(), target);
  }

  // optional string groupName = 7;
  if (has_groupname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->groupname().data(), this->groupname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->groupname(), target);
  }

  // optional .hadoop.hdfs.FsPermissionProto perms = 8;
  if (has_perms()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        8, this->perms(), target);
  }

  // repeated .hadoop.hdfs.AclEntryProto acls = 9;
  for (int i = 0; i < this->acls_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        9, this->acls(i), target);
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 10;
  for (int i = 0; i < this->xattrs_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        10, this->xattrs(i), target);
  }

  // optional bool xAttrsRemoved = 11;
  if (has_xattrsremoved()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(11, this->xattrsremoved(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int MetadataUpdateEventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string path = 1;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

    // required .hadoop.hdfs.MetadataUpdateType type = 2;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // optional int64 mtime = 3;
    if (has_mtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->mtime());
    }

    // optional int64 atime = 4;
    if (has_atime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->atime());
    }

    // optional int32 replication = 5;
    if (has_replication()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->replication());
    }

    // optional string ownerName = 6;
    if (has_ownername()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->ownername());
    }

    // optional string groupName = 7;
    if (has_groupname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->groupname());
    }

    // optional .hadoop.hdfs.FsPermissionProto perms = 8;
    if (has_perms()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->perms());
    }

  }
  if (_has_bits_[10 / 32] & (0xffu << (10 % 32))) {
    // optional bool xAttrsRemoved = 11;
    if (has_xattrsremoved()) {
      total_size += 1 + 1;
    }

  }
  // repeated .hadoop.hdfs.AclEntryProto acls = 9;
  total_size += 1 * this->acls_size();
  for (int i = 0; i < this->acls_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->acls(i));
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 10;
  total_size += 1 * this->xattrs_size();
  for (int i = 0; i < this->xattrs_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->xattrs(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MetadataUpdateEventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const MetadataUpdateEventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const MetadataUpdateEventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void MetadataUpdateEventProto::MergeFrom(const MetadataUpdateEventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  acls_.MergeFrom(from.acls_);
  xattrs_.MergeFrom(from.xattrs_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_path()) {
      set_path(from.path());
    }
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_mtime()) {
      set_mtime(from.mtime());
    }
    if (from.has_atime()) {
      set_atime(from.atime());
    }
    if (from.has_replication()) {
      set_replication(from.replication());
    }
    if (from.has_ownername()) {
      set_ownername(from.ownername());
    }
    if (from.has_groupname()) {
      set_groupname(from.groupname());
    }
    if (from.has_perms()) {
      mutable_perms()->::hadoop::hdfs::FsPermissionProto::MergeFrom(from.perms());
    }
  }
  if (from._has_bits_[10 / 32] & (0xffu << (10 % 32))) {
    if (from.has_xattrsremoved()) {
      set_xattrsremoved(from.xattrsremoved());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void MetadataUpdateEventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MetadataUpdateEventProto::CopyFrom(const MetadataUpdateEventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MetadataUpdateEventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_perms()) {
    if (!this->perms().IsInitialized()) return false;
  }
  for (int i = 0; i < acls_size(); i++) {
    if (!this->acls(i).IsInitialized()) return false;
  }
  for (int i = 0; i < xattrs_size(); i++) {
    if (!this->xattrs(i).IsInitialized()) return false;
  }
  return true;
}

void MetadataUpdateEventProto::Swap(MetadataUpdateEventProto* other) {
  if (other != this) {
    std::swap(path_, other->path_);
    std::swap(type_, other->type_);
    std::swap(mtime_, other->mtime_);
    std::swap(atime_, other->atime_);
    std::swap(replication_, other->replication_);
    std::swap(ownername_, other->ownername_);
    std::swap(groupname_, other->groupname_);
    std::swap(perms_, other->perms_);
    acls_.Swap(&other->acls_);
    xattrs_.Swap(&other->xattrs_);
    std::swap(xattrsremoved_, other->xattrsremoved_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata MetadataUpdateEventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MetadataUpdateEventProto_descriptor_;
  metadata.reflection = MetadataUpdateEventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int UnlinkEventProto::kPathFieldNumber;
const int UnlinkEventProto::kTimestampFieldNumber;
#endif  // !_MSC_VER

UnlinkEventProto::UnlinkEventProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void UnlinkEventProto::InitAsDefaultInstance() {
}

UnlinkEventProto::UnlinkEventProto(const UnlinkEventProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void UnlinkEventProto::SharedCtor() {
  _cached_size_ = 0;
  path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  timestamp_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

UnlinkEventProto::~UnlinkEventProto() {
  SharedDtor();
}

void UnlinkEventProto::SharedDtor() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (this != default_instance_) {
  }
}

void UnlinkEventProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UnlinkEventProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return UnlinkEventProto_descriptor_;
}

const UnlinkEventProto& UnlinkEventProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

UnlinkEventProto* UnlinkEventProto::default_instance_ = NULL;

UnlinkEventProto* UnlinkEventProto::New() const {
  return new UnlinkEventProto;
}

void UnlinkEventProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_path()) {
      if (path_ != &::google::protobuf::internal::kEmptyString) {
        path_->clear();
      }
    }
    timestamp_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool UnlinkEventProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string path = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->path().data(), this->path().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_timestamp;
        break;
      }

      // required int64 timestamp = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_timestamp:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &timestamp_)));
          set_has_timestamp();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void UnlinkEventProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->path(), output);
  }

  // required int64 timestamp = 2;
  if (has_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->timestamp(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* UnlinkEventProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string path = 1;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->path(), target);
  }

  // required int64 timestamp = 2;
  if (has_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->timestamp(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int UnlinkEventProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string path = 1;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

    // required int64 timestamp = 2;
    if (has_timestamp()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->timestamp());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UnlinkEventProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const UnlinkEventProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const UnlinkEventProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void UnlinkEventProto::MergeFrom(const UnlinkEventProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_path()) {
      set_path(from.path());
    }
    if (from.has_timestamp()) {
      set_timestamp(from.timestamp());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void UnlinkEventProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UnlinkEventProto::CopyFrom(const UnlinkEventProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UnlinkEventProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void UnlinkEventProto::Swap(UnlinkEventProto* other) {
  if (other != this) {
    std::swap(path_, other->path_);
    std::swap(timestamp_, other->timestamp_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata UnlinkEventProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = UnlinkEventProto_descriptor_;
  metadata.reflection = UnlinkEventProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int EventsListProto::kEventsFieldNumber;
const int EventsListProto::kFirstTxidFieldNumber;
const int EventsListProto::kLastTxidFieldNumber;
const int EventsListProto::kSyncTxidFieldNumber;
#endif  // !_MSC_VER

EventsListProto::EventsListProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void EventsListProto::InitAsDefaultInstance() {
}

EventsListProto::EventsListProto(const EventsListProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void EventsListProto::SharedCtor() {
  _cached_size_ = 0;
  firsttxid_ = GOOGLE_LONGLONG(0);
  lasttxid_ = GOOGLE_LONGLONG(0);
  synctxid_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

EventsListProto::~EventsListProto() {
  SharedDtor();
}

void EventsListProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void EventsListProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EventsListProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EventsListProto_descriptor_;
}

const EventsListProto& EventsListProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_inotify_2eproto();
  return *default_instance_;
}

EventsListProto* EventsListProto::default_instance_ = NULL;

EventsListProto* EventsListProto::New() const {
  return new EventsListProto;
}

void EventsListProto::Clear() {
  if (_has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    firsttxid_ = GOOGLE_LONGLONG(0);
    lasttxid_ = GOOGLE_LONGLONG(0);
    synctxid_ = GOOGLE_LONGLONG(0);
  }
  events_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool EventsListProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.hdfs.EventProto events = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_events:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_events()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_events;
        if (input->ExpectTag(16)) goto parse_firstTxid;
        break;
      }

      // required int64 firstTxid = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_firstTxid:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &firsttxid_)));
          set_has_firsttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_lastTxid;
        break;
      }

      // required int64 lastTxid = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lastTxid:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lasttxid_)));
          set_has_lasttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_syncTxid;
        break;
      }

      // required int64 syncTxid = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_syncTxid:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &synctxid_)));
          set_has_synctxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void EventsListProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.hdfs.EventProto events = 1;
  for (int i = 0; i < this->events_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->events(i), output);
  }

  // required int64 firstTxid = 2;
  if (has_firsttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->firsttxid(), output);
  }

  // required int64 lastTxid = 3;
  if (has_lasttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->lasttxid(), output);
  }

  // required int64 syncTxid = 4;
  if (has_synctxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->synctxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* EventsListProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.hdfs.EventProto events = 1;
  for (int i = 0; i < this->events_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->events(i), target);
  }

  // required int64 firstTxid = 2;
  if (has_firsttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->firsttxid(), target);
  }

  // required int64 lastTxid = 3;
  if (has_lasttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->lasttxid(), target);
  }

  // required int64 syncTxid = 4;
  if (has_synctxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->synctxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int EventsListProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    // required int64 firstTxid = 2;
    if (has_firsttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->firsttxid());
    }

    // required int64 lastTxid = 3;
    if (has_lasttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->lasttxid());
    }

    // required int64 syncTxid = 4;
    if (has_synctxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->synctxid());
    }

  }
  // repeated .hadoop.hdfs.EventProto events = 1;
  total_size += 1 * this->events_size();
  for (int i = 0; i < this->events_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->events(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EventsListProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const EventsListProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const EventsListProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void EventsListProto::MergeFrom(const EventsListProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  events_.MergeFrom(from.events_);
  if (from._has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    if (from.has_firsttxid()) {
      set_firsttxid(from.firsttxid());
    }
    if (from.has_lasttxid()) {
      set_lasttxid(from.lasttxid());
    }
    if (from.has_synctxid()) {
      set_synctxid(from.synctxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void EventsListProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EventsListProto::CopyFrom(const EventsListProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EventsListProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000000e) != 0x0000000e) return false;

  for (int i = 0; i < events_size(); i++) {
    if (!this->events(i).IsInitialized()) return false;
  }
  return true;
}

void EventsListProto::Swap(EventsListProto* other) {
  if (other != this) {
    events_.Swap(&other->events_);
    std::swap(firsttxid_, other->firsttxid_);
    std::swap(lasttxid_, other->lasttxid_);
    std::swap(synctxid_, other->synctxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata EventsListProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = EventsListProto_descriptor_;
  metadata.reflection = EventsListProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
