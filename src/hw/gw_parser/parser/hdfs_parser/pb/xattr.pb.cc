// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xattr.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "xattr.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

namespace {

const ::google::protobuf::Descriptor* XAttrProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  XAttrProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* XAttrProto_XAttrNamespaceProto_descriptor_ = NULL;
const ::google::protobuf::Descriptor* XAttrEditLogProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  XAttrEditLogProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* SetXAttrRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SetXAttrRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* SetXAttrResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SetXAttrResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetXAttrsRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetXAttrsRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetXAttrsResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetXAttrsResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListXAttrsRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListXAttrsRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListXAttrsResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListXAttrsResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveXAttrRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveXAttrRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveXAttrResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveXAttrResponseProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* XAttrSetFlagProto_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_xattr_2eproto() {
  protobuf_AddDesc_xattr_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "xattr.proto");
  GOOGLE_CHECK(file != NULL);
  XAttrProto_descriptor_ = file->message_type(0);
  static const int XAttrProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrProto, namespace__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrProto, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrProto, value_),
  };
  XAttrProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      XAttrProto_descriptor_,
      XAttrProto::default_instance_,
      XAttrProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(XAttrProto));
  XAttrProto_XAttrNamespaceProto_descriptor_ = XAttrProto_descriptor_->enum_type(0);
  XAttrEditLogProto_descriptor_ = file->message_type(1);
  static const int XAttrEditLogProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrEditLogProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrEditLogProto, xattrs_),
  };
  XAttrEditLogProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      XAttrEditLogProto_descriptor_,
      XAttrEditLogProto::default_instance_,
      XAttrEditLogProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrEditLogProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(XAttrEditLogProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(XAttrEditLogProto));
  SetXAttrRequestProto_descriptor_ = file->message_type(2);
  static const int SetXAttrRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrRequestProto, xattr_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrRequestProto, flag_),
  };
  SetXAttrRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SetXAttrRequestProto_descriptor_,
      SetXAttrRequestProto::default_instance_,
      SetXAttrRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SetXAttrRequestProto));
  SetXAttrResponseProto_descriptor_ = file->message_type(3);
  static const int SetXAttrResponseProto_offsets_[1] = {
  };
  SetXAttrResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SetXAttrResponseProto_descriptor_,
      SetXAttrResponseProto::default_instance_,
      SetXAttrResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetXAttrResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SetXAttrResponseProto));
  GetXAttrsRequestProto_descriptor_ = file->message_type(4);
  static const int GetXAttrsRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsRequestProto, xattrs_),
  };
  GetXAttrsRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetXAttrsRequestProto_descriptor_,
      GetXAttrsRequestProto::default_instance_,
      GetXAttrsRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetXAttrsRequestProto));
  GetXAttrsResponseProto_descriptor_ = file->message_type(5);
  static const int GetXAttrsResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsResponseProto, xattrs_),
  };
  GetXAttrsResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetXAttrsResponseProto_descriptor_,
      GetXAttrsResponseProto::default_instance_,
      GetXAttrsResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetXAttrsResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetXAttrsResponseProto));
  ListXAttrsRequestProto_descriptor_ = file->message_type(6);
  static const int ListXAttrsRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListXAttrsRequestProto, src_),
  };
  ListXAttrsRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ListXAttrsRequestProto_descriptor_,
      ListXAttrsRequestProto::default_instance_,
      ListXAttrsRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListXAttrsRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListXAttrsRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ListXAttrsRequestProto));
  ListXAttrsResponseProto_descriptor_ = file->message_type(7);
  static const int ListXAttrsResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListXAttrsResponseProto, xattrs_),
  };
  ListXAttrsResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ListXAttrsResponseProto_descriptor_,
      ListXAttrsResponseProto::default_instance_,
      ListXAttrsResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListXAttrsResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListXAttrsResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ListXAttrsResponseProto));
  RemoveXAttrRequestProto_descriptor_ = file->message_type(8);
  static const int RemoveXAttrRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveXAttrRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveXAttrRequestProto, xattr_),
  };
  RemoveXAttrRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveXAttrRequestProto_descriptor_,
      RemoveXAttrRequestProto::default_instance_,
      RemoveXAttrRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveXAttrRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveXAttrRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveXAttrRequestProto));
  RemoveXAttrResponseProto_descriptor_ = file->message_type(9);
  static const int RemoveXAttrResponseProto_offsets_[1] = {
  };
  RemoveXAttrResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveXAttrResponseProto_descriptor_,
      RemoveXAttrResponseProto::default_instance_,
      RemoveXAttrResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveXAttrResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveXAttrResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveXAttrResponseProto));
  XAttrSetFlagProto_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_xattr_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    XAttrProto_descriptor_, &XAttrProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    XAttrEditLogProto_descriptor_, &XAttrEditLogProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SetXAttrRequestProto_descriptor_, &SetXAttrRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SetXAttrResponseProto_descriptor_, &SetXAttrResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetXAttrsRequestProto_descriptor_, &GetXAttrsRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetXAttrsResponseProto_descriptor_, &GetXAttrsResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ListXAttrsRequestProto_descriptor_, &ListXAttrsRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ListXAttrsResponseProto_descriptor_, &ListXAttrsResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveXAttrRequestProto_descriptor_, &RemoveXAttrRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveXAttrResponseProto_descriptor_, &RemoveXAttrResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_xattr_2eproto() {
  delete XAttrProto::default_instance_;
  delete XAttrProto_reflection_;
  delete XAttrEditLogProto::default_instance_;
  delete XAttrEditLogProto_reflection_;
  delete SetXAttrRequestProto::default_instance_;
  delete SetXAttrRequestProto_reflection_;
  delete SetXAttrResponseProto::default_instance_;
  delete SetXAttrResponseProto_reflection_;
  delete GetXAttrsRequestProto::default_instance_;
  delete GetXAttrsRequestProto_reflection_;
  delete GetXAttrsResponseProto::default_instance_;
  delete GetXAttrsResponseProto_reflection_;
  delete ListXAttrsRequestProto::default_instance_;
  delete ListXAttrsRequestProto_reflection_;
  delete ListXAttrsResponseProto::default_instance_;
  delete ListXAttrsResponseProto_reflection_;
  delete RemoveXAttrRequestProto::default_instance_;
  delete RemoveXAttrRequestProto_reflection_;
  delete RemoveXAttrResponseProto::default_instance_;
  delete RemoveXAttrResponseProto_reflection_;
}

void protobuf_AddDesc_xattr_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\013xattr.proto\022\013hadoop.hdfs\"\272\001\n\nXAttrProt"
    "o\022>\n\tnamespace\030\001 \002(\0162+.hadoop.hdfs.XAttr"
    "Proto.XAttrNamespaceProto\022\014\n\004name\030\002 \002(\t\022"
    "\r\n\005value\030\003 \001(\014\"O\n\023XAttrNamespaceProto\022\010\n"
    "\004USER\020\000\022\013\n\007TRUSTED\020\001\022\014\n\010SECURITY\020\002\022\n\n\006SY"
    "STEM\020\003\022\007\n\003RAW\020\004\"I\n\021XAttrEditLogProto\022\013\n\003"
    "src\030\001 \001(\t\022\'\n\006xAttrs\030\002 \003(\0132\027.hadoop.hdfs."
    "XAttrProto\"Y\n\024SetXAttrRequestProto\022\013\n\003sr"
    "c\030\001 \002(\t\022&\n\005xAttr\030\002 \001(\0132\027.hadoop.hdfs.XAt"
    "trProto\022\014\n\004flag\030\003 \001(\r\"\027\n\025SetXAttrRespons"
    "eProto\"M\n\025GetXAttrsRequestProto\022\013\n\003src\030\001"
    " \002(\t\022\'\n\006xAttrs\030\002 \003(\0132\027.hadoop.hdfs.XAttr"
    "Proto\"A\n\026GetXAttrsResponseProto\022\'\n\006xAttr"
    "s\030\001 \003(\0132\027.hadoop.hdfs.XAttrProto\"%\n\026List"
    "XAttrsRequestProto\022\013\n\003src\030\001 \002(\t\"B\n\027ListX"
    "AttrsResponseProto\022\'\n\006xAttrs\030\001 \003(\0132\027.had"
    "oop.hdfs.XAttrProto\"N\n\027RemoveXAttrReques"
    "tProto\022\013\n\003src\030\001 \002(\t\022&\n\005xAttr\030\002 \001(\0132\027.had"
    "oop.hdfs.XAttrProto\"\032\n\030RemoveXAttrRespon"
    "seProto*8\n\021XAttrSetFlagProto\022\020\n\014XATTR_CR"
    "EATE\020\001\022\021\n\rXATTR_REPLACE\020\002B7\n%org.apache."
    "hadoop.hdfs.protocol.protoB\013XAttrProtos\240"
    "\001\001", 882);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "xattr.proto", &protobuf_RegisterTypes);
  XAttrProto::default_instance_ = new XAttrProto();
  XAttrEditLogProto::default_instance_ = new XAttrEditLogProto();
  SetXAttrRequestProto::default_instance_ = new SetXAttrRequestProto();
  SetXAttrResponseProto::default_instance_ = new SetXAttrResponseProto();
  GetXAttrsRequestProto::default_instance_ = new GetXAttrsRequestProto();
  GetXAttrsResponseProto::default_instance_ = new GetXAttrsResponseProto();
  ListXAttrsRequestProto::default_instance_ = new ListXAttrsRequestProto();
  ListXAttrsResponseProto::default_instance_ = new ListXAttrsResponseProto();
  RemoveXAttrRequestProto::default_instance_ = new RemoveXAttrRequestProto();
  RemoveXAttrResponseProto::default_instance_ = new RemoveXAttrResponseProto();
  XAttrProto::default_instance_->InitAsDefaultInstance();
  XAttrEditLogProto::default_instance_->InitAsDefaultInstance();
  SetXAttrRequestProto::default_instance_->InitAsDefaultInstance();
  SetXAttrResponseProto::default_instance_->InitAsDefaultInstance();
  GetXAttrsRequestProto::default_instance_->InitAsDefaultInstance();
  GetXAttrsResponseProto::default_instance_->InitAsDefaultInstance();
  ListXAttrsRequestProto::default_instance_->InitAsDefaultInstance();
  ListXAttrsResponseProto::default_instance_->InitAsDefaultInstance();
  RemoveXAttrRequestProto::default_instance_->InitAsDefaultInstance();
  RemoveXAttrResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_xattr_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_xattr_2eproto {
  StaticDescriptorInitializer_xattr_2eproto() {
    protobuf_AddDesc_xattr_2eproto();
  }
} static_descriptor_initializer_xattr_2eproto_;
const ::google::protobuf::EnumDescriptor* XAttrSetFlagProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return XAttrSetFlagProto_descriptor_;
}
bool XAttrSetFlagProto_IsValid(int value) {
  switch(value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

const ::google::protobuf::EnumDescriptor* XAttrProto_XAttrNamespaceProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return XAttrProto_XAttrNamespaceProto_descriptor_;
}
bool XAttrProto_XAttrNamespaceProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const XAttrProto_XAttrNamespaceProto XAttrProto::USER;
const XAttrProto_XAttrNamespaceProto XAttrProto::TRUSTED;
const XAttrProto_XAttrNamespaceProto XAttrProto::SECURITY;
const XAttrProto_XAttrNamespaceProto XAttrProto::SYSTEM;
const XAttrProto_XAttrNamespaceProto XAttrProto::RAW;
const XAttrProto_XAttrNamespaceProto XAttrProto::XAttrNamespaceProto_MIN;
const XAttrProto_XAttrNamespaceProto XAttrProto::XAttrNamespaceProto_MAX;
const int XAttrProto::XAttrNamespaceProto_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int XAttrProto::kNamespaceFieldNumber;
const int XAttrProto::kNameFieldNumber;
const int XAttrProto::kValueFieldNumber;
#endif  // !_MSC_VER

XAttrProto::XAttrProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void XAttrProto::InitAsDefaultInstance() {
}

XAttrProto::XAttrProto(const XAttrProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void XAttrProto::SharedCtor() {
  _cached_size_ = 0;
  namespace__ = 0;
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

XAttrProto::~XAttrProto() {
  SharedDtor();
}

void XAttrProto::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    delete value_;
  }
  if (this != default_instance_) {
  }
}

void XAttrProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* XAttrProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return XAttrProto_descriptor_;
}

const XAttrProto& XAttrProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

XAttrProto* XAttrProto::default_instance_ = NULL;

XAttrProto* XAttrProto::New() const {
  return new XAttrProto;
}

void XAttrProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    namespace__ = 0;
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
    if (has_value()) {
      if (value_ != &::google::protobuf::internal::kEmptyString) {
        value_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool XAttrProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.XAttrProto.XAttrNamespaceProto namespace = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::XAttrProto_XAttrNamespaceProto_IsValid(value)) {
            set_namespace_(static_cast< ::hadoop::hdfs::XAttrProto_XAttrNamespaceProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_name;
        break;
      }

      // required string name = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->name().data(), this->name().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_value;
        break;
      }

      // optional bytes value = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_value()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void XAttrProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.XAttrProto.XAttrNamespaceProto namespace = 1;
  if (has_namespace_()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->namespace_(), output);
  }

  // required string name = 2;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->name(), output);
  }

  // optional bytes value = 3;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      3, this->value(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* XAttrProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.XAttrProto.XAttrNamespaceProto namespace = 1;
  if (has_namespace_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->namespace_(), target);
  }

  // required string name = 2;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->name(), target);
  }

  // optional bytes value = 3;
  if (has_value()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->value(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int XAttrProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.XAttrProto.XAttrNamespaceProto namespace = 1;
    if (has_namespace_()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->namespace_());
    }

    // required string name = 2;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->name());
    }

    // optional bytes value = 3;
    if (has_value()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->value());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void XAttrProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const XAttrProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const XAttrProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void XAttrProto::MergeFrom(const XAttrProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_namespace_()) {
      set_namespace_(from.namespace_());
    }
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_value()) {
      set_value(from.value());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void XAttrProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void XAttrProto::CopyFrom(const XAttrProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool XAttrProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void XAttrProto::Swap(XAttrProto* other) {
  if (other != this) {
    std::swap(namespace__, other->namespace__);
    std::swap(name_, other->name_);
    std::swap(value_, other->value_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata XAttrProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = XAttrProto_descriptor_;
  metadata.reflection = XAttrProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int XAttrEditLogProto::kSrcFieldNumber;
const int XAttrEditLogProto::kXAttrsFieldNumber;
#endif  // !_MSC_VER

XAttrEditLogProto::XAttrEditLogProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void XAttrEditLogProto::InitAsDefaultInstance() {
}

XAttrEditLogProto::XAttrEditLogProto(const XAttrEditLogProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void XAttrEditLogProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

XAttrEditLogProto::~XAttrEditLogProto() {
  SharedDtor();
}

void XAttrEditLogProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void XAttrEditLogProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* XAttrEditLogProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return XAttrEditLogProto_descriptor_;
}

const XAttrEditLogProto& XAttrEditLogProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

XAttrEditLogProto* XAttrEditLogProto::default_instance_ = NULL;

XAttrEditLogProto* XAttrEditLogProto::New() const {
  return new XAttrEditLogProto;
}

void XAttrEditLogProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  xattrs_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool XAttrEditLogProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_xAttrs;
        break;
      }

      // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_xAttrs;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void XAttrEditLogProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  for (int i = 0; i < this->xattrs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->xattrs(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* XAttrEditLogProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  for (int i = 0; i < this->xattrs_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->xattrs(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int XAttrEditLogProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  total_size += 1 * this->xattrs_size();
  for (int i = 0; i < this->xattrs_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->xattrs(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void XAttrEditLogProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const XAttrEditLogProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const XAttrEditLogProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void XAttrEditLogProto::MergeFrom(const XAttrEditLogProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  xattrs_.MergeFrom(from.xattrs_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void XAttrEditLogProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void XAttrEditLogProto::CopyFrom(const XAttrEditLogProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool XAttrEditLogProto::IsInitialized() const {

  for (int i = 0; i < xattrs_size(); i++) {
    if (!this->xattrs(i).IsInitialized()) return false;
  }
  return true;
}

void XAttrEditLogProto::Swap(XAttrEditLogProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    xattrs_.Swap(&other->xattrs_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata XAttrEditLogProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = XAttrEditLogProto_descriptor_;
  metadata.reflection = XAttrEditLogProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SetXAttrRequestProto::kSrcFieldNumber;
const int SetXAttrRequestProto::kXAttrFieldNumber;
const int SetXAttrRequestProto::kFlagFieldNumber;
#endif  // !_MSC_VER

SetXAttrRequestProto::SetXAttrRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SetXAttrRequestProto::InitAsDefaultInstance() {
  xattr_ = const_cast< ::hadoop::hdfs::XAttrProto*>(&::hadoop::hdfs::XAttrProto::default_instance());
}

SetXAttrRequestProto::SetXAttrRequestProto(const SetXAttrRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SetXAttrRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  xattr_ = NULL;
  flag_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SetXAttrRequestProto::~SetXAttrRequestProto() {
  SharedDtor();
}

void SetXAttrRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
    delete xattr_;
  }
}

void SetXAttrRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SetXAttrRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SetXAttrRequestProto_descriptor_;
}

const SetXAttrRequestProto& SetXAttrRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

SetXAttrRequestProto* SetXAttrRequestProto::default_instance_ = NULL;

SetXAttrRequestProto* SetXAttrRequestProto::New() const {
  return new SetXAttrRequestProto;
}

void SetXAttrRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
    if (has_xattr()) {
      if (xattr_ != NULL) xattr_->::hadoop::hdfs::XAttrProto::Clear();
    }
    flag_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SetXAttrRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_xAttr;
        break;
      }

      // optional .hadoop.hdfs.XAttrProto xAttr = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttr:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_xattr()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_flag;
        break;
      }

      // optional uint32 flag = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_flag:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &flag_)));
          set_has_flag();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SetXAttrRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // optional .hadoop.hdfs.XAttrProto xAttr = 2;
  if (has_xattr()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->xattr(), output);
  }

  // optional uint32 flag = 3;
  if (has_flag()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->flag(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SetXAttrRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // optional .hadoop.hdfs.XAttrProto xAttr = 2;
  if (has_xattr()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->xattr(), target);
  }

  // optional uint32 flag = 3;
  if (has_flag()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->flag(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SetXAttrRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

    // optional .hadoop.hdfs.XAttrProto xAttr = 2;
    if (has_xattr()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->xattr());
    }

    // optional uint32 flag = 3;
    if (has_flag()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->flag());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SetXAttrRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SetXAttrRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SetXAttrRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SetXAttrRequestProto::MergeFrom(const SetXAttrRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
    if (from.has_xattr()) {
      mutable_xattr()->::hadoop::hdfs::XAttrProto::MergeFrom(from.xattr());
    }
    if (from.has_flag()) {
      set_flag(from.flag());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SetXAttrRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SetXAttrRequestProto::CopyFrom(const SetXAttrRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetXAttrRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_xattr()) {
    if (!this->xattr().IsInitialized()) return false;
  }
  return true;
}

void SetXAttrRequestProto::Swap(SetXAttrRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(xattr_, other->xattr_);
    std::swap(flag_, other->flag_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SetXAttrRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SetXAttrRequestProto_descriptor_;
  metadata.reflection = SetXAttrRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

SetXAttrResponseProto::SetXAttrResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SetXAttrResponseProto::InitAsDefaultInstance() {
}

SetXAttrResponseProto::SetXAttrResponseProto(const SetXAttrResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SetXAttrResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SetXAttrResponseProto::~SetXAttrResponseProto() {
  SharedDtor();
}

void SetXAttrResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SetXAttrResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SetXAttrResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SetXAttrResponseProto_descriptor_;
}

const SetXAttrResponseProto& SetXAttrResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

SetXAttrResponseProto* SetXAttrResponseProto::default_instance_ = NULL;

SetXAttrResponseProto* SetXAttrResponseProto::New() const {
  return new SetXAttrResponseProto;
}

void SetXAttrResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SetXAttrResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void SetXAttrResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SetXAttrResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SetXAttrResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SetXAttrResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SetXAttrResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SetXAttrResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SetXAttrResponseProto::MergeFrom(const SetXAttrResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SetXAttrResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SetXAttrResponseProto::CopyFrom(const SetXAttrResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetXAttrResponseProto::IsInitialized() const {

  return true;
}

void SetXAttrResponseProto::Swap(SetXAttrResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SetXAttrResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SetXAttrResponseProto_descriptor_;
  metadata.reflection = SetXAttrResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetXAttrsRequestProto::kSrcFieldNumber;
const int GetXAttrsRequestProto::kXAttrsFieldNumber;
#endif  // !_MSC_VER

GetXAttrsRequestProto::GetXAttrsRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetXAttrsRequestProto::InitAsDefaultInstance() {
}

GetXAttrsRequestProto::GetXAttrsRequestProto(const GetXAttrsRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetXAttrsRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetXAttrsRequestProto::~GetXAttrsRequestProto() {
  SharedDtor();
}

void GetXAttrsRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void GetXAttrsRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetXAttrsRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetXAttrsRequestProto_descriptor_;
}

const GetXAttrsRequestProto& GetXAttrsRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

GetXAttrsRequestProto* GetXAttrsRequestProto::default_instance_ = NULL;

GetXAttrsRequestProto* GetXAttrsRequestProto::New() const {
  return new GetXAttrsRequestProto;
}

void GetXAttrsRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  xattrs_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetXAttrsRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_xAttrs;
        break;
      }

      // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_xAttrs;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetXAttrsRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  for (int i = 0; i < this->xattrs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->xattrs(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetXAttrsRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  for (int i = 0; i < this->xattrs_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->xattrs(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetXAttrsRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  total_size += 1 * this->xattrs_size();
  for (int i = 0; i < this->xattrs_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->xattrs(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetXAttrsRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetXAttrsRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetXAttrsRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetXAttrsRequestProto::MergeFrom(const GetXAttrsRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  xattrs_.MergeFrom(from.xattrs_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetXAttrsRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetXAttrsRequestProto::CopyFrom(const GetXAttrsRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetXAttrsRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < xattrs_size(); i++) {
    if (!this->xattrs(i).IsInitialized()) return false;
  }
  return true;
}

void GetXAttrsRequestProto::Swap(GetXAttrsRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    xattrs_.Swap(&other->xattrs_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetXAttrsRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetXAttrsRequestProto_descriptor_;
  metadata.reflection = GetXAttrsRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetXAttrsResponseProto::kXAttrsFieldNumber;
#endif  // !_MSC_VER

GetXAttrsResponseProto::GetXAttrsResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetXAttrsResponseProto::InitAsDefaultInstance() {
}

GetXAttrsResponseProto::GetXAttrsResponseProto(const GetXAttrsResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetXAttrsResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetXAttrsResponseProto::~GetXAttrsResponseProto() {
  SharedDtor();
}

void GetXAttrsResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetXAttrsResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetXAttrsResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetXAttrsResponseProto_descriptor_;
}

const GetXAttrsResponseProto& GetXAttrsResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

GetXAttrsResponseProto* GetXAttrsResponseProto::default_instance_ = NULL;

GetXAttrsResponseProto* GetXAttrsResponseProto::New() const {
  return new GetXAttrsResponseProto;
}

void GetXAttrsResponseProto::Clear() {
  xattrs_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetXAttrsResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_xAttrs;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetXAttrsResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  for (int i = 0; i < this->xattrs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->xattrs(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetXAttrsResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  for (int i = 0; i < this->xattrs_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->xattrs(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetXAttrsResponseProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  total_size += 1 * this->xattrs_size();
  for (int i = 0; i < this->xattrs_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->xattrs(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetXAttrsResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetXAttrsResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetXAttrsResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetXAttrsResponseProto::MergeFrom(const GetXAttrsResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  xattrs_.MergeFrom(from.xattrs_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetXAttrsResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetXAttrsResponseProto::CopyFrom(const GetXAttrsResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetXAttrsResponseProto::IsInitialized() const {

  for (int i = 0; i < xattrs_size(); i++) {
    if (!this->xattrs(i).IsInitialized()) return false;
  }
  return true;
}

void GetXAttrsResponseProto::Swap(GetXAttrsResponseProto* other) {
  if (other != this) {
    xattrs_.Swap(&other->xattrs_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetXAttrsResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetXAttrsResponseProto_descriptor_;
  metadata.reflection = GetXAttrsResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ListXAttrsRequestProto::kSrcFieldNumber;
#endif  // !_MSC_VER

ListXAttrsRequestProto::ListXAttrsRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ListXAttrsRequestProto::InitAsDefaultInstance() {
}

ListXAttrsRequestProto::ListXAttrsRequestProto(const ListXAttrsRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ListXAttrsRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ListXAttrsRequestProto::~ListXAttrsRequestProto() {
  SharedDtor();
}

void ListXAttrsRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void ListXAttrsRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListXAttrsRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListXAttrsRequestProto_descriptor_;
}

const ListXAttrsRequestProto& ListXAttrsRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

ListXAttrsRequestProto* ListXAttrsRequestProto::default_instance_ = NULL;

ListXAttrsRequestProto* ListXAttrsRequestProto::New() const {
  return new ListXAttrsRequestProto;
}

void ListXAttrsRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ListXAttrsRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ListXAttrsRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ListXAttrsRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ListXAttrsRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListXAttrsRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ListXAttrsRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ListXAttrsRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ListXAttrsRequestProto::MergeFrom(const ListXAttrsRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ListXAttrsRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListXAttrsRequestProto::CopyFrom(const ListXAttrsRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListXAttrsRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void ListXAttrsRequestProto::Swap(ListXAttrsRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ListXAttrsRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListXAttrsRequestProto_descriptor_;
  metadata.reflection = ListXAttrsRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ListXAttrsResponseProto::kXAttrsFieldNumber;
#endif  // !_MSC_VER

ListXAttrsResponseProto::ListXAttrsResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ListXAttrsResponseProto::InitAsDefaultInstance() {
}

ListXAttrsResponseProto::ListXAttrsResponseProto(const ListXAttrsResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ListXAttrsResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ListXAttrsResponseProto::~ListXAttrsResponseProto() {
  SharedDtor();
}

void ListXAttrsResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ListXAttrsResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListXAttrsResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListXAttrsResponseProto_descriptor_;
}

const ListXAttrsResponseProto& ListXAttrsResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

ListXAttrsResponseProto* ListXAttrsResponseProto::default_instance_ = NULL;

ListXAttrsResponseProto* ListXAttrsResponseProto::New() const {
  return new ListXAttrsResponseProto;
}

void ListXAttrsResponseProto::Clear() {
  xattrs_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ListXAttrsResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_xAttrs;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ListXAttrsResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  for (int i = 0; i < this->xattrs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->xattrs(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ListXAttrsResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  for (int i = 0; i < this->xattrs_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->xattrs(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ListXAttrsResponseProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  total_size += 1 * this->xattrs_size();
  for (int i = 0; i < this->xattrs_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->xattrs(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListXAttrsResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ListXAttrsResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ListXAttrsResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ListXAttrsResponseProto::MergeFrom(const ListXAttrsResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  xattrs_.MergeFrom(from.xattrs_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ListXAttrsResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListXAttrsResponseProto::CopyFrom(const ListXAttrsResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListXAttrsResponseProto::IsInitialized() const {

  for (int i = 0; i < xattrs_size(); i++) {
    if (!this->xattrs(i).IsInitialized()) return false;
  }
  return true;
}

void ListXAttrsResponseProto::Swap(ListXAttrsResponseProto* other) {
  if (other != this) {
    xattrs_.Swap(&other->xattrs_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ListXAttrsResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListXAttrsResponseProto_descriptor_;
  metadata.reflection = ListXAttrsResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RemoveXAttrRequestProto::kSrcFieldNumber;
const int RemoveXAttrRequestProto::kXAttrFieldNumber;
#endif  // !_MSC_VER

RemoveXAttrRequestProto::RemoveXAttrRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveXAttrRequestProto::InitAsDefaultInstance() {
  xattr_ = const_cast< ::hadoop::hdfs::XAttrProto*>(&::hadoop::hdfs::XAttrProto::default_instance());
}

RemoveXAttrRequestProto::RemoveXAttrRequestProto(const RemoveXAttrRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveXAttrRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  xattr_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveXAttrRequestProto::~RemoveXAttrRequestProto() {
  SharedDtor();
}

void RemoveXAttrRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
    delete xattr_;
  }
}

void RemoveXAttrRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveXAttrRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveXAttrRequestProto_descriptor_;
}

const RemoveXAttrRequestProto& RemoveXAttrRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

RemoveXAttrRequestProto* RemoveXAttrRequestProto::default_instance_ = NULL;

RemoveXAttrRequestProto* RemoveXAttrRequestProto::New() const {
  return new RemoveXAttrRequestProto;
}

void RemoveXAttrRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
    if (has_xattr()) {
      if (xattr_ != NULL) xattr_->::hadoop::hdfs::XAttrProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveXAttrRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_xAttr;
        break;
      }

      // optional .hadoop.hdfs.XAttrProto xAttr = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttr:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_xattr()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RemoveXAttrRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // optional .hadoop.hdfs.XAttrProto xAttr = 2;
  if (has_xattr()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->xattr(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveXAttrRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // optional .hadoop.hdfs.XAttrProto xAttr = 2;
  if (has_xattr()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->xattr(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveXAttrRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

    // optional .hadoop.hdfs.XAttrProto xAttr = 2;
    if (has_xattr()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->xattr());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveXAttrRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveXAttrRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveXAttrRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveXAttrRequestProto::MergeFrom(const RemoveXAttrRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
    if (from.has_xattr()) {
      mutable_xattr()->::hadoop::hdfs::XAttrProto::MergeFrom(from.xattr());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveXAttrRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveXAttrRequestProto::CopyFrom(const RemoveXAttrRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveXAttrRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_xattr()) {
    if (!this->xattr().IsInitialized()) return false;
  }
  return true;
}

void RemoveXAttrRequestProto::Swap(RemoveXAttrRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(xattr_, other->xattr_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveXAttrRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveXAttrRequestProto_descriptor_;
  metadata.reflection = RemoveXAttrRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RemoveXAttrResponseProto::RemoveXAttrResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveXAttrResponseProto::InitAsDefaultInstance() {
}

RemoveXAttrResponseProto::RemoveXAttrResponseProto(const RemoveXAttrResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveXAttrResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveXAttrResponseProto::~RemoveXAttrResponseProto() {
  SharedDtor();
}

void RemoveXAttrResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RemoveXAttrResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveXAttrResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveXAttrResponseProto_descriptor_;
}

const RemoveXAttrResponseProto& RemoveXAttrResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_xattr_2eproto();
  return *default_instance_;
}

RemoveXAttrResponseProto* RemoveXAttrResponseProto::default_instance_ = NULL;

RemoveXAttrResponseProto* RemoveXAttrResponseProto::New() const {
  return new RemoveXAttrResponseProto;
}

void RemoveXAttrResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveXAttrResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RemoveXAttrResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveXAttrResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveXAttrResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveXAttrResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveXAttrResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveXAttrResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveXAttrResponseProto::MergeFrom(const RemoveXAttrResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveXAttrResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveXAttrResponseProto::CopyFrom(const RemoveXAttrResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveXAttrResponseProto::IsInitialized() const {

  return true;
}

void RemoveXAttrResponseProto::Swap(RemoveXAttrResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveXAttrResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveXAttrResponseProto_descriptor_;
  metadata.reflection = RemoveXAttrResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
