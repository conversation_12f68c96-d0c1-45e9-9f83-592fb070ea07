// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: encryption.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "encryption.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

namespace {

const ::google::protobuf::Descriptor* CreateEncryptionZoneRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CreateEncryptionZoneRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CreateEncryptionZoneResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CreateEncryptionZoneResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListEncryptionZonesRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListEncryptionZonesRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* EncryptionZoneProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  EncryptionZoneProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListEncryptionZonesResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListEncryptionZonesResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetEZForPathRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetEZForPathRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetEZForPathResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetEZForPathResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_encryption_2eproto() {
  protobuf_AddDesc_encryption_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "encryption.proto");
  GOOGLE_CHECK(file != NULL);
  CreateEncryptionZoneRequestProto_descriptor_ = file->message_type(0);
  static const int CreateEncryptionZoneRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEncryptionZoneRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEncryptionZoneRequestProto, keyname_),
  };
  CreateEncryptionZoneRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CreateEncryptionZoneRequestProto_descriptor_,
      CreateEncryptionZoneRequestProto::default_instance_,
      CreateEncryptionZoneRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEncryptionZoneRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEncryptionZoneRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CreateEncryptionZoneRequestProto));
  CreateEncryptionZoneResponseProto_descriptor_ = file->message_type(1);
  static const int CreateEncryptionZoneResponseProto_offsets_[1] = {
  };
  CreateEncryptionZoneResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CreateEncryptionZoneResponseProto_descriptor_,
      CreateEncryptionZoneResponseProto::default_instance_,
      CreateEncryptionZoneResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEncryptionZoneResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CreateEncryptionZoneResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CreateEncryptionZoneResponseProto));
  ListEncryptionZonesRequestProto_descriptor_ = file->message_type(2);
  static const int ListEncryptionZonesRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesRequestProto, id_),
  };
  ListEncryptionZonesRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ListEncryptionZonesRequestProto_descriptor_,
      ListEncryptionZonesRequestProto::default_instance_,
      ListEncryptionZonesRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ListEncryptionZonesRequestProto));
  EncryptionZoneProto_descriptor_ = file->message_type(3);
  static const int EncryptionZoneProto_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, path_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, suite_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, cryptoprotocolversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, keyname_),
  };
  EncryptionZoneProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      EncryptionZoneProto_descriptor_,
      EncryptionZoneProto::default_instance_,
      EncryptionZoneProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EncryptionZoneProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(EncryptionZoneProto));
  ListEncryptionZonesResponseProto_descriptor_ = file->message_type(4);
  static const int ListEncryptionZonesResponseProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesResponseProto, zones_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesResponseProto, hasmore_),
  };
  ListEncryptionZonesResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ListEncryptionZonesResponseProto_descriptor_,
      ListEncryptionZonesResponseProto::default_instance_,
      ListEncryptionZonesResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListEncryptionZonesResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ListEncryptionZonesResponseProto));
  GetEZForPathRequestProto_descriptor_ = file->message_type(5);
  static const int GetEZForPathRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEZForPathRequestProto, src_),
  };
  GetEZForPathRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetEZForPathRequestProto_descriptor_,
      GetEZForPathRequestProto::default_instance_,
      GetEZForPathRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEZForPathRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEZForPathRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetEZForPathRequestProto));
  GetEZForPathResponseProto_descriptor_ = file->message_type(6);
  static const int GetEZForPathResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEZForPathResponseProto, zone_),
  };
  GetEZForPathResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetEZForPathResponseProto_descriptor_,
      GetEZForPathResponseProto::default_instance_,
      GetEZForPathResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEZForPathResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEZForPathResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetEZForPathResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_encryption_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CreateEncryptionZoneRequestProto_descriptor_, &CreateEncryptionZoneRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CreateEncryptionZoneResponseProto_descriptor_, &CreateEncryptionZoneResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ListEncryptionZonesRequestProto_descriptor_, &ListEncryptionZonesRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    EncryptionZoneProto_descriptor_, &EncryptionZoneProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ListEncryptionZonesResponseProto_descriptor_, &ListEncryptionZonesResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetEZForPathRequestProto_descriptor_, &GetEZForPathRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetEZForPathResponseProto_descriptor_, &GetEZForPathResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_encryption_2eproto() {
  delete CreateEncryptionZoneRequestProto::default_instance_;
  delete CreateEncryptionZoneRequestProto_reflection_;
  delete CreateEncryptionZoneResponseProto::default_instance_;
  delete CreateEncryptionZoneResponseProto_reflection_;
  delete ListEncryptionZonesRequestProto::default_instance_;
  delete ListEncryptionZonesRequestProto_reflection_;
  delete EncryptionZoneProto::default_instance_;
  delete EncryptionZoneProto_reflection_;
  delete ListEncryptionZonesResponseProto::default_instance_;
  delete ListEncryptionZonesResponseProto_reflection_;
  delete GetEZForPathRequestProto::default_instance_;
  delete GetEZForPathRequestProto_reflection_;
  delete GetEZForPathResponseProto::default_instance_;
  delete GetEZForPathResponseProto_reflection_;
}

void protobuf_AddDesc_encryption_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020encryption.proto\022\013hadoop.hdfs\032\nhdfs.pr"
    "oto\"@\n CreateEncryptionZoneRequestProto\022"
    "\013\n\003src\030\001 \002(\t\022\017\n\007keyName\030\002 \001(\t\"#\n!CreateE"
    "ncryptionZoneResponseProto\"-\n\037ListEncryp"
    "tionZonesRequestProto\022\n\n\002id\030\001 \002(\003\"\266\001\n\023En"
    "cryptionZoneProto\022\n\n\002id\030\001 \002(\003\022\014\n\004path\030\002 "
    "\002(\t\022,\n\005suite\030\003 \002(\0162\035.hadoop.hdfs.CipherS"
    "uiteProto\022F\n\025cryptoProtocolVersion\030\004 \002(\016"
    "2\'.hadoop.hdfs.CryptoProtocolVersionProt"
    "o\022\017\n\007keyName\030\005 \002(\t\"d\n ListEncryptionZone"
    "sResponseProto\022/\n\005zones\030\001 \003(\0132 .hadoop.h"
    "dfs.EncryptionZoneProto\022\017\n\007hasMore\030\002 \002(\010"
    "\"\'\n\030GetEZForPathRequestProto\022\013\n\003src\030\001 \002("
    "\t\"K\n\031GetEZForPathResponseProto\022.\n\004zone\030\001"
    " \001(\0132 .hadoop.hdfs.EncryptionZoneProtoBA"
    "\n%org.apache.hadoop.hdfs.protocol.protoB"
    "\025EncryptionZonesProtos\240\001\001", 665);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "encryption.proto", &protobuf_RegisterTypes);
  CreateEncryptionZoneRequestProto::default_instance_ = new CreateEncryptionZoneRequestProto();
  CreateEncryptionZoneResponseProto::default_instance_ = new CreateEncryptionZoneResponseProto();
  ListEncryptionZonesRequestProto::default_instance_ = new ListEncryptionZonesRequestProto();
  EncryptionZoneProto::default_instance_ = new EncryptionZoneProto();
  ListEncryptionZonesResponseProto::default_instance_ = new ListEncryptionZonesResponseProto();
  GetEZForPathRequestProto::default_instance_ = new GetEZForPathRequestProto();
  GetEZForPathResponseProto::default_instance_ = new GetEZForPathResponseProto();
  CreateEncryptionZoneRequestProto::default_instance_->InitAsDefaultInstance();
  CreateEncryptionZoneResponseProto::default_instance_->InitAsDefaultInstance();
  ListEncryptionZonesRequestProto::default_instance_->InitAsDefaultInstance();
  EncryptionZoneProto::default_instance_->InitAsDefaultInstance();
  ListEncryptionZonesResponseProto::default_instance_->InitAsDefaultInstance();
  GetEZForPathRequestProto::default_instance_->InitAsDefaultInstance();
  GetEZForPathResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_encryption_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_encryption_2eproto {
  StaticDescriptorInitializer_encryption_2eproto() {
    protobuf_AddDesc_encryption_2eproto();
  }
} static_descriptor_initializer_encryption_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int CreateEncryptionZoneRequestProto::kSrcFieldNumber;
const int CreateEncryptionZoneRequestProto::kKeyNameFieldNumber;
#endif  // !_MSC_VER

CreateEncryptionZoneRequestProto::CreateEncryptionZoneRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CreateEncryptionZoneRequestProto::InitAsDefaultInstance() {
}

CreateEncryptionZoneRequestProto::CreateEncryptionZoneRequestProto(const CreateEncryptionZoneRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CreateEncryptionZoneRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CreateEncryptionZoneRequestProto::~CreateEncryptionZoneRequestProto() {
  SharedDtor();
}

void CreateEncryptionZoneRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    delete keyname_;
  }
  if (this != default_instance_) {
  }
}

void CreateEncryptionZoneRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CreateEncryptionZoneRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CreateEncryptionZoneRequestProto_descriptor_;
}

const CreateEncryptionZoneRequestProto& CreateEncryptionZoneRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

CreateEncryptionZoneRequestProto* CreateEncryptionZoneRequestProto::default_instance_ = NULL;

CreateEncryptionZoneRequestProto* CreateEncryptionZoneRequestProto::New() const {
  return new CreateEncryptionZoneRequestProto;
}

void CreateEncryptionZoneRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
    if (has_keyname()) {
      if (keyname_ != &::google::protobuf::internal::kEmptyString) {
        keyname_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CreateEncryptionZoneRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_keyName;
        break;
      }

      // optional string keyName = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_keyName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_keyname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->keyname().data(), this->keyname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CreateEncryptionZoneRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // optional string keyName = 2;
  if (has_keyname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->keyname().data(), this->keyname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->keyname(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CreateEncryptionZoneRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // optional string keyName = 2;
  if (has_keyname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->keyname().data(), this->keyname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->keyname(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CreateEncryptionZoneRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

    // optional string keyName = 2;
    if (has_keyname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->keyname());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CreateEncryptionZoneRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CreateEncryptionZoneRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CreateEncryptionZoneRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CreateEncryptionZoneRequestProto::MergeFrom(const CreateEncryptionZoneRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
    if (from.has_keyname()) {
      set_keyname(from.keyname());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CreateEncryptionZoneRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateEncryptionZoneRequestProto::CopyFrom(const CreateEncryptionZoneRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateEncryptionZoneRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void CreateEncryptionZoneRequestProto::Swap(CreateEncryptionZoneRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(keyname_, other->keyname_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CreateEncryptionZoneRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CreateEncryptionZoneRequestProto_descriptor_;
  metadata.reflection = CreateEncryptionZoneRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

CreateEncryptionZoneResponseProto::CreateEncryptionZoneResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CreateEncryptionZoneResponseProto::InitAsDefaultInstance() {
}

CreateEncryptionZoneResponseProto::CreateEncryptionZoneResponseProto(const CreateEncryptionZoneResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CreateEncryptionZoneResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CreateEncryptionZoneResponseProto::~CreateEncryptionZoneResponseProto() {
  SharedDtor();
}

void CreateEncryptionZoneResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CreateEncryptionZoneResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CreateEncryptionZoneResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CreateEncryptionZoneResponseProto_descriptor_;
}

const CreateEncryptionZoneResponseProto& CreateEncryptionZoneResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

CreateEncryptionZoneResponseProto* CreateEncryptionZoneResponseProto::default_instance_ = NULL;

CreateEncryptionZoneResponseProto* CreateEncryptionZoneResponseProto::New() const {
  return new CreateEncryptionZoneResponseProto;
}

void CreateEncryptionZoneResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CreateEncryptionZoneResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void CreateEncryptionZoneResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CreateEncryptionZoneResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CreateEncryptionZoneResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CreateEncryptionZoneResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CreateEncryptionZoneResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CreateEncryptionZoneResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CreateEncryptionZoneResponseProto::MergeFrom(const CreateEncryptionZoneResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CreateEncryptionZoneResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateEncryptionZoneResponseProto::CopyFrom(const CreateEncryptionZoneResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateEncryptionZoneResponseProto::IsInitialized() const {

  return true;
}

void CreateEncryptionZoneResponseProto::Swap(CreateEncryptionZoneResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CreateEncryptionZoneResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CreateEncryptionZoneResponseProto_descriptor_;
  metadata.reflection = CreateEncryptionZoneResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ListEncryptionZonesRequestProto::kIdFieldNumber;
#endif  // !_MSC_VER

ListEncryptionZonesRequestProto::ListEncryptionZonesRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ListEncryptionZonesRequestProto::InitAsDefaultInstance() {
}

ListEncryptionZonesRequestProto::ListEncryptionZonesRequestProto(const ListEncryptionZonesRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ListEncryptionZonesRequestProto::SharedCtor() {
  _cached_size_ = 0;
  id_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ListEncryptionZonesRequestProto::~ListEncryptionZonesRequestProto() {
  SharedDtor();
}

void ListEncryptionZonesRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ListEncryptionZonesRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListEncryptionZonesRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListEncryptionZonesRequestProto_descriptor_;
}

const ListEncryptionZonesRequestProto& ListEncryptionZonesRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

ListEncryptionZonesRequestProto* ListEncryptionZonesRequestProto::default_instance_ = NULL;

ListEncryptionZonesRequestProto* ListEncryptionZonesRequestProto::New() const {
  return new ListEncryptionZonesRequestProto;
}

void ListEncryptionZonesRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ListEncryptionZonesRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ListEncryptionZonesRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->id(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ListEncryptionZonesRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->id(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ListEncryptionZonesRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->id());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListEncryptionZonesRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ListEncryptionZonesRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ListEncryptionZonesRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ListEncryptionZonesRequestProto::MergeFrom(const ListEncryptionZonesRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ListEncryptionZonesRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListEncryptionZonesRequestProto::CopyFrom(const ListEncryptionZonesRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListEncryptionZonesRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void ListEncryptionZonesRequestProto::Swap(ListEncryptionZonesRequestProto* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ListEncryptionZonesRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListEncryptionZonesRequestProto_descriptor_;
  metadata.reflection = ListEncryptionZonesRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int EncryptionZoneProto::kIdFieldNumber;
const int EncryptionZoneProto::kPathFieldNumber;
const int EncryptionZoneProto::kSuiteFieldNumber;
const int EncryptionZoneProto::kCryptoProtocolVersionFieldNumber;
const int EncryptionZoneProto::kKeyNameFieldNumber;
#endif  // !_MSC_VER

EncryptionZoneProto::EncryptionZoneProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void EncryptionZoneProto::InitAsDefaultInstance() {
}

EncryptionZoneProto::EncryptionZoneProto(const EncryptionZoneProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void EncryptionZoneProto::SharedCtor() {
  _cached_size_ = 0;
  id_ = GOOGLE_LONGLONG(0);
  path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  suite_ = 1;
  cryptoprotocolversion_ = 1;
  keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

EncryptionZoneProto::~EncryptionZoneProto() {
  SharedDtor();
}

void EncryptionZoneProto::SharedDtor() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    delete keyname_;
  }
  if (this != default_instance_) {
  }
}

void EncryptionZoneProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EncryptionZoneProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EncryptionZoneProto_descriptor_;
}

const EncryptionZoneProto& EncryptionZoneProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

EncryptionZoneProto* EncryptionZoneProto::default_instance_ = NULL;

EncryptionZoneProto* EncryptionZoneProto::New() const {
  return new EncryptionZoneProto;
}

void EncryptionZoneProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = GOOGLE_LONGLONG(0);
    if (has_path()) {
      if (path_ != &::google::protobuf::internal::kEmptyString) {
        path_->clear();
      }
    }
    suite_ = 1;
    cryptoprotocolversion_ = 1;
    if (has_keyname()) {
      if (keyname_ != &::google::protobuf::internal::kEmptyString) {
        keyname_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool EncryptionZoneProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_path;
        break;
      }

      // required string path = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_path:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->path().data(), this->path().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_suite;
        break;
      }

      // required .hadoop.hdfs.CipherSuiteProto suite = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_suite:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::CipherSuiteProto_IsValid(value)) {
            set_suite(static_cast< ::hadoop::hdfs::CipherSuiteProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(3, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_cryptoProtocolVersion;
        break;
      }

      // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_cryptoProtocolVersion:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::CryptoProtocolVersionProto_IsValid(value)) {
            set_cryptoprotocolversion(static_cast< ::hadoop::hdfs::CryptoProtocolVersionProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(4, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_keyName;
        break;
      }

      // required string keyName = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_keyName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_keyname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->keyname().data(), this->keyname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void EncryptionZoneProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->id(), output);
  }

  // required string path = 2;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->path(), output);
  }

  // required .hadoop.hdfs.CipherSuiteProto suite = 3;
  if (has_suite()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->suite(), output);
  }

  // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 4;
  if (has_cryptoprotocolversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->cryptoprotocolversion(), output);
  }

  // required string keyName = 5;
  if (has_keyname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->keyname().data(), this->keyname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->keyname(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* EncryptionZoneProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->id(), target);
  }

  // required string path = 2;
  if (has_path()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->path().data(), this->path().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->path(), target);
  }

  // required .hadoop.hdfs.CipherSuiteProto suite = 3;
  if (has_suite()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->suite(), target);
  }

  // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 4;
  if (has_cryptoprotocolversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->cryptoprotocolversion(), target);
  }

  // required string keyName = 5;
  if (has_keyname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->keyname().data(), this->keyname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->keyname(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int EncryptionZoneProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->id());
    }

    // required string path = 2;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

    // required .hadoop.hdfs.CipherSuiteProto suite = 3;
    if (has_suite()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->suite());
    }

    // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 4;
    if (has_cryptoprotocolversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->cryptoprotocolversion());
    }

    // required string keyName = 5;
    if (has_keyname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->keyname());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EncryptionZoneProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const EncryptionZoneProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const EncryptionZoneProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void EncryptionZoneProto::MergeFrom(const EncryptionZoneProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
    if (from.has_path()) {
      set_path(from.path());
    }
    if (from.has_suite()) {
      set_suite(from.suite());
    }
    if (from.has_cryptoprotocolversion()) {
      set_cryptoprotocolversion(from.cryptoprotocolversion());
    }
    if (from.has_keyname()) {
      set_keyname(from.keyname());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void EncryptionZoneProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EncryptionZoneProto::CopyFrom(const EncryptionZoneProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EncryptionZoneProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000001f) != 0x0000001f) return false;

  return true;
}

void EncryptionZoneProto::Swap(EncryptionZoneProto* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(path_, other->path_);
    std::swap(suite_, other->suite_);
    std::swap(cryptoprotocolversion_, other->cryptoprotocolversion_);
    std::swap(keyname_, other->keyname_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata EncryptionZoneProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = EncryptionZoneProto_descriptor_;
  metadata.reflection = EncryptionZoneProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ListEncryptionZonesResponseProto::kZonesFieldNumber;
const int ListEncryptionZonesResponseProto::kHasMoreFieldNumber;
#endif  // !_MSC_VER

ListEncryptionZonesResponseProto::ListEncryptionZonesResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ListEncryptionZonesResponseProto::InitAsDefaultInstance() {
}

ListEncryptionZonesResponseProto::ListEncryptionZonesResponseProto(const ListEncryptionZonesResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ListEncryptionZonesResponseProto::SharedCtor() {
  _cached_size_ = 0;
  hasmore_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ListEncryptionZonesResponseProto::~ListEncryptionZonesResponseProto() {
  SharedDtor();
}

void ListEncryptionZonesResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ListEncryptionZonesResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListEncryptionZonesResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListEncryptionZonesResponseProto_descriptor_;
}

const ListEncryptionZonesResponseProto& ListEncryptionZonesResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

ListEncryptionZonesResponseProto* ListEncryptionZonesResponseProto::default_instance_ = NULL;

ListEncryptionZonesResponseProto* ListEncryptionZonesResponseProto::New() const {
  return new ListEncryptionZonesResponseProto;
}

void ListEncryptionZonesResponseProto::Clear() {
  if (_has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    hasmore_ = false;
  }
  zones_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ListEncryptionZonesResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.hdfs.EncryptionZoneProto zones = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_zones:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_zones()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_zones;
        if (input->ExpectTag(16)) goto parse_hasMore;
        break;
      }

      // required bool hasMore = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_hasMore:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &hasmore_)));
          set_has_hasmore();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ListEncryptionZonesResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.hdfs.EncryptionZoneProto zones = 1;
  for (int i = 0; i < this->zones_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->zones(i), output);
  }

  // required bool hasMore = 2;
  if (has_hasmore()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->hasmore(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ListEncryptionZonesResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.hdfs.EncryptionZoneProto zones = 1;
  for (int i = 0; i < this->zones_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->zones(i), target);
  }

  // required bool hasMore = 2;
  if (has_hasmore()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->hasmore(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ListEncryptionZonesResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    // required bool hasMore = 2;
    if (has_hasmore()) {
      total_size += 1 + 1;
    }

  }
  // repeated .hadoop.hdfs.EncryptionZoneProto zones = 1;
  total_size += 1 * this->zones_size();
  for (int i = 0; i < this->zones_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->zones(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListEncryptionZonesResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ListEncryptionZonesResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ListEncryptionZonesResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ListEncryptionZonesResponseProto::MergeFrom(const ListEncryptionZonesResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  zones_.MergeFrom(from.zones_);
  if (from._has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    if (from.has_hasmore()) {
      set_hasmore(from.hasmore());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ListEncryptionZonesResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListEncryptionZonesResponseProto::CopyFrom(const ListEncryptionZonesResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListEncryptionZonesResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000002) != 0x00000002) return false;

  for (int i = 0; i < zones_size(); i++) {
    if (!this->zones(i).IsInitialized()) return false;
  }
  return true;
}

void ListEncryptionZonesResponseProto::Swap(ListEncryptionZonesResponseProto* other) {
  if (other != this) {
    zones_.Swap(&other->zones_);
    std::swap(hasmore_, other->hasmore_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ListEncryptionZonesResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListEncryptionZonesResponseProto_descriptor_;
  metadata.reflection = ListEncryptionZonesResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetEZForPathRequestProto::kSrcFieldNumber;
#endif  // !_MSC_VER

GetEZForPathRequestProto::GetEZForPathRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetEZForPathRequestProto::InitAsDefaultInstance() {
}

GetEZForPathRequestProto::GetEZForPathRequestProto(const GetEZForPathRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetEZForPathRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetEZForPathRequestProto::~GetEZForPathRequestProto() {
  SharedDtor();
}

void GetEZForPathRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void GetEZForPathRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetEZForPathRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetEZForPathRequestProto_descriptor_;
}

const GetEZForPathRequestProto& GetEZForPathRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

GetEZForPathRequestProto* GetEZForPathRequestProto::default_instance_ = NULL;

GetEZForPathRequestProto* GetEZForPathRequestProto::New() const {
  return new GetEZForPathRequestProto;
}

void GetEZForPathRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetEZForPathRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetEZForPathRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetEZForPathRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetEZForPathRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetEZForPathRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetEZForPathRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetEZForPathRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetEZForPathRequestProto::MergeFrom(const GetEZForPathRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetEZForPathRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetEZForPathRequestProto::CopyFrom(const GetEZForPathRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetEZForPathRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetEZForPathRequestProto::Swap(GetEZForPathRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetEZForPathRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetEZForPathRequestProto_descriptor_;
  metadata.reflection = GetEZForPathRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetEZForPathResponseProto::kZoneFieldNumber;
#endif  // !_MSC_VER

GetEZForPathResponseProto::GetEZForPathResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetEZForPathResponseProto::InitAsDefaultInstance() {
  zone_ = const_cast< ::hadoop::hdfs::EncryptionZoneProto*>(&::hadoop::hdfs::EncryptionZoneProto::default_instance());
}

GetEZForPathResponseProto::GetEZForPathResponseProto(const GetEZForPathResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetEZForPathResponseProto::SharedCtor() {
  _cached_size_ = 0;
  zone_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetEZForPathResponseProto::~GetEZForPathResponseProto() {
  SharedDtor();
}

void GetEZForPathResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete zone_;
  }
}

void GetEZForPathResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetEZForPathResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetEZForPathResponseProto_descriptor_;
}

const GetEZForPathResponseProto& GetEZForPathResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_encryption_2eproto();
  return *default_instance_;
}

GetEZForPathResponseProto* GetEZForPathResponseProto::default_instance_ = NULL;

GetEZForPathResponseProto* GetEZForPathResponseProto::New() const {
  return new GetEZForPathResponseProto;
}

void GetEZForPathResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_zone()) {
      if (zone_ != NULL) zone_->::hadoop::hdfs::EncryptionZoneProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetEZForPathResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .hadoop.hdfs.EncryptionZoneProto zone = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_zone()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetEZForPathResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional .hadoop.hdfs.EncryptionZoneProto zone = 1;
  if (has_zone()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->zone(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetEZForPathResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional .hadoop.hdfs.EncryptionZoneProto zone = 1;
  if (has_zone()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->zone(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetEZForPathResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional .hadoop.hdfs.EncryptionZoneProto zone = 1;
    if (has_zone()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->zone());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetEZForPathResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetEZForPathResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetEZForPathResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetEZForPathResponseProto::MergeFrom(const GetEZForPathResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_zone()) {
      mutable_zone()->::hadoop::hdfs::EncryptionZoneProto::MergeFrom(from.zone());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetEZForPathResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetEZForPathResponseProto::CopyFrom(const GetEZForPathResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetEZForPathResponseProto::IsInitialized() const {

  if (has_zone()) {
    if (!this->zone().IsInitialized()) return false;
  }
  return true;
}

void GetEZForPathResponseProto::Swap(GetEZForPathResponseProto* other) {
  if (other != this) {
    std::swap(zone_, other->zone_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetEZForPathResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetEZForPathResponseProto_descriptor_;
  metadata.reflection = GetEZForPathResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
