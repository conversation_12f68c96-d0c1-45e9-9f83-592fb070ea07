// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: hdfs.proto

#ifndef PROTOBUF_hdfs_2eproto__INCLUDED
#define PROTOBUF_hdfs_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "Security.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_hdfs_2eproto();
void protobuf_AssignDesc_hdfs_2eproto();
void protobuf_ShutdownFile_hdfs_2eproto();

class ExtendedBlockProto;
class DatanodeIDProto;
class DatanodeLocalInfoProto;
class DatanodeInfosProto;
class DatanodeInfoProto;
class DatanodeStorageProto;
class StorageReportProto;
class ContentSummaryProto;
class CorruptFileBlocksProto;
class FsPermissionProto;
class StorageTypesProto;
class BlockStoragePolicyProto;
class StorageUuidsProto;
class LocatedBlockProto;
class DataEncryptionKeyProto;
class FileEncryptionInfoProto;
class PerFileEncryptionInfoProto;
class ZoneEncryptionInfoProto;
class CipherOptionProto;
class LocatedBlocksProto;
class HdfsFileStatusProto;
class FsServerDefaultsProto;
class DirectoryListingProto;
class SnapshottableDirectoryStatusProto;
class SnapshottableDirectoryListingProto;
class SnapshotDiffReportEntryProto;
class SnapshotDiffReportProto;
class StorageInfoProto;
class NamenodeRegistrationProto;
class CheckpointSignatureProto;
class NamenodeCommandProto;
class CheckpointCommandProto;
class BlockProto;
class BlockWithLocationsProto;
class BlocksWithLocationsProto;
class RemoteEditLogProto;
class RemoteEditLogManifestProto;
class NamespaceInfoProto;
class BlockKeyProto;
class ExportedBlockKeysProto;
class RecoveringBlockProto;
class VersionRequestProto;
class VersionResponseProto;
class SnapshotInfoProto;
class RollingUpgradeStatusProto;

enum DatanodeInfoProto_AdminState {
  DatanodeInfoProto_AdminState_NORMAL = 0,
  DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS = 1,
  DatanodeInfoProto_AdminState_DECOMMISSIONED = 2
};
bool DatanodeInfoProto_AdminState_IsValid(int value);
const DatanodeInfoProto_AdminState DatanodeInfoProto_AdminState_AdminState_MIN = DatanodeInfoProto_AdminState_NORMAL;
const DatanodeInfoProto_AdminState DatanodeInfoProto_AdminState_AdminState_MAX = DatanodeInfoProto_AdminState_DECOMMISSIONED;
const int DatanodeInfoProto_AdminState_AdminState_ARRAYSIZE = DatanodeInfoProto_AdminState_AdminState_MAX + 1;

const ::google::protobuf::EnumDescriptor* DatanodeInfoProto_AdminState_descriptor();
inline const ::std::string& DatanodeInfoProto_AdminState_Name(DatanodeInfoProto_AdminState value) {
  return ::google::protobuf::internal::NameOfEnum(
    DatanodeInfoProto_AdminState_descriptor(), value);
}
inline bool DatanodeInfoProto_AdminState_Parse(
    const ::std::string& name, DatanodeInfoProto_AdminState* value) {
  return ::google::protobuf::internal::ParseNamedEnum<DatanodeInfoProto_AdminState>(
    DatanodeInfoProto_AdminState_descriptor(), name, value);
}
enum DatanodeStorageProto_StorageState {
  DatanodeStorageProto_StorageState_NORMAL = 0,
  DatanodeStorageProto_StorageState_READ_ONLY_SHARED = 1
};
bool DatanodeStorageProto_StorageState_IsValid(int value);
const DatanodeStorageProto_StorageState DatanodeStorageProto_StorageState_StorageState_MIN = DatanodeStorageProto_StorageState_NORMAL;
const DatanodeStorageProto_StorageState DatanodeStorageProto_StorageState_StorageState_MAX = DatanodeStorageProto_StorageState_READ_ONLY_SHARED;
const int DatanodeStorageProto_StorageState_StorageState_ARRAYSIZE = DatanodeStorageProto_StorageState_StorageState_MAX + 1;

const ::google::protobuf::EnumDescriptor* DatanodeStorageProto_StorageState_descriptor();
inline const ::std::string& DatanodeStorageProto_StorageState_Name(DatanodeStorageProto_StorageState value) {
  return ::google::protobuf::internal::NameOfEnum(
    DatanodeStorageProto_StorageState_descriptor(), value);
}
inline bool DatanodeStorageProto_StorageState_Parse(
    const ::std::string& name, DatanodeStorageProto_StorageState* value) {
  return ::google::protobuf::internal::ParseNamedEnum<DatanodeStorageProto_StorageState>(
    DatanodeStorageProto_StorageState_descriptor(), name, value);
}
enum HdfsFileStatusProto_FileType {
  HdfsFileStatusProto_FileType_IS_DIR = 1,
  HdfsFileStatusProto_FileType_IS_FILE = 2,
  HdfsFileStatusProto_FileType_IS_SYMLINK = 3
};
bool HdfsFileStatusProto_FileType_IsValid(int value);
const HdfsFileStatusProto_FileType HdfsFileStatusProto_FileType_FileType_MIN = HdfsFileStatusProto_FileType_IS_DIR;
const HdfsFileStatusProto_FileType HdfsFileStatusProto_FileType_FileType_MAX = HdfsFileStatusProto_FileType_IS_SYMLINK;
const int HdfsFileStatusProto_FileType_FileType_ARRAYSIZE = HdfsFileStatusProto_FileType_FileType_MAX + 1;

const ::google::protobuf::EnumDescriptor* HdfsFileStatusProto_FileType_descriptor();
inline const ::std::string& HdfsFileStatusProto_FileType_Name(HdfsFileStatusProto_FileType value) {
  return ::google::protobuf::internal::NameOfEnum(
    HdfsFileStatusProto_FileType_descriptor(), value);
}
inline bool HdfsFileStatusProto_FileType_Parse(
    const ::std::string& name, HdfsFileStatusProto_FileType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<HdfsFileStatusProto_FileType>(
    HdfsFileStatusProto_FileType_descriptor(), name, value);
}
enum NamenodeRegistrationProto_NamenodeRoleProto {
  NamenodeRegistrationProto_NamenodeRoleProto_NAMENODE = 1,
  NamenodeRegistrationProto_NamenodeRoleProto_BACKUP = 2,
  NamenodeRegistrationProto_NamenodeRoleProto_CHECKPOINT = 3
};
bool NamenodeRegistrationProto_NamenodeRoleProto_IsValid(int value);
const NamenodeRegistrationProto_NamenodeRoleProto NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_MIN = NamenodeRegistrationProto_NamenodeRoleProto_NAMENODE;
const NamenodeRegistrationProto_NamenodeRoleProto NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_MAX = NamenodeRegistrationProto_NamenodeRoleProto_CHECKPOINT;
const int NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_ARRAYSIZE = NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* NamenodeRegistrationProto_NamenodeRoleProto_descriptor();
inline const ::std::string& NamenodeRegistrationProto_NamenodeRoleProto_Name(NamenodeRegistrationProto_NamenodeRoleProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    NamenodeRegistrationProto_NamenodeRoleProto_descriptor(), value);
}
inline bool NamenodeRegistrationProto_NamenodeRoleProto_Parse(
    const ::std::string& name, NamenodeRegistrationProto_NamenodeRoleProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<NamenodeRegistrationProto_NamenodeRoleProto>(
    NamenodeRegistrationProto_NamenodeRoleProto_descriptor(), name, value);
}
enum NamenodeCommandProto_Type {
  NamenodeCommandProto_Type_NamenodeCommand = 0,
  NamenodeCommandProto_Type_CheckPointCommand = 1
};
bool NamenodeCommandProto_Type_IsValid(int value);
const NamenodeCommandProto_Type NamenodeCommandProto_Type_Type_MIN = NamenodeCommandProto_Type_NamenodeCommand;
const NamenodeCommandProto_Type NamenodeCommandProto_Type_Type_MAX = NamenodeCommandProto_Type_CheckPointCommand;
const int NamenodeCommandProto_Type_Type_ARRAYSIZE = NamenodeCommandProto_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* NamenodeCommandProto_Type_descriptor();
inline const ::std::string& NamenodeCommandProto_Type_Name(NamenodeCommandProto_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    NamenodeCommandProto_Type_descriptor(), value);
}
inline bool NamenodeCommandProto_Type_Parse(
    const ::std::string& name, NamenodeCommandProto_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<NamenodeCommandProto_Type>(
    NamenodeCommandProto_Type_descriptor(), name, value);
}
enum StorageTypeProto {
  DISK = 1,
  SSD = 2,
  ARCHIVE = 3,
  RAM_DISK = 4
};
bool StorageTypeProto_IsValid(int value);
const StorageTypeProto StorageTypeProto_MIN = DISK;
const StorageTypeProto StorageTypeProto_MAX = RAM_DISK;
const int StorageTypeProto_ARRAYSIZE = StorageTypeProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* StorageTypeProto_descriptor();
inline const ::std::string& StorageTypeProto_Name(StorageTypeProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    StorageTypeProto_descriptor(), value);
}
inline bool StorageTypeProto_Parse(
    const ::std::string& name, StorageTypeProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<StorageTypeProto>(
    StorageTypeProto_descriptor(), name, value);
}
enum CipherSuiteProto {
  UNKNOWN = 1,
  AES_CTR_NOPADDING = 2
};
bool CipherSuiteProto_IsValid(int value);
const CipherSuiteProto CipherSuiteProto_MIN = UNKNOWN;
const CipherSuiteProto CipherSuiteProto_MAX = AES_CTR_NOPADDING;
const int CipherSuiteProto_ARRAYSIZE = CipherSuiteProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* CipherSuiteProto_descriptor();
inline const ::std::string& CipherSuiteProto_Name(CipherSuiteProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    CipherSuiteProto_descriptor(), value);
}
inline bool CipherSuiteProto_Parse(
    const ::std::string& name, CipherSuiteProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<CipherSuiteProto>(
    CipherSuiteProto_descriptor(), name, value);
}
enum CryptoProtocolVersionProto {
  UNKNOWN_PROTOCOL_VERSION = 1,
  ENCRYPTION_ZONES = 2
};
bool CryptoProtocolVersionProto_IsValid(int value);
const CryptoProtocolVersionProto CryptoProtocolVersionProto_MIN = UNKNOWN_PROTOCOL_VERSION;
const CryptoProtocolVersionProto CryptoProtocolVersionProto_MAX = ENCRYPTION_ZONES;
const int CryptoProtocolVersionProto_ARRAYSIZE = CryptoProtocolVersionProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* CryptoProtocolVersionProto_descriptor();
inline const ::std::string& CryptoProtocolVersionProto_Name(CryptoProtocolVersionProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    CryptoProtocolVersionProto_descriptor(), value);
}
inline bool CryptoProtocolVersionProto_Parse(
    const ::std::string& name, CryptoProtocolVersionProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<CryptoProtocolVersionProto>(
    CryptoProtocolVersionProto_descriptor(), name, value);
}
enum ChecksumTypeProto {
  CHECKSUM_NULL = 0,
  CHECKSUM_CRC32 = 1,
  CHECKSUM_CRC32C = 2
};
bool ChecksumTypeProto_IsValid(int value);
const ChecksumTypeProto ChecksumTypeProto_MIN = CHECKSUM_NULL;
const ChecksumTypeProto ChecksumTypeProto_MAX = CHECKSUM_CRC32C;
const int ChecksumTypeProto_ARRAYSIZE = ChecksumTypeProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* ChecksumTypeProto_descriptor();
inline const ::std::string& ChecksumTypeProto_Name(ChecksumTypeProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    ChecksumTypeProto_descriptor(), value);
}
inline bool ChecksumTypeProto_Parse(
    const ::std::string& name, ChecksumTypeProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ChecksumTypeProto>(
    ChecksumTypeProto_descriptor(), name, value);
}
enum ReplicaStateProto {
  FINALIZED = 0,
  RBW = 1,
  RWR = 2,
  RUR = 3,
  TEMPORARY = 4
};
bool ReplicaStateProto_IsValid(int value);
const ReplicaStateProto ReplicaStateProto_MIN = FINALIZED;
const ReplicaStateProto ReplicaStateProto_MAX = TEMPORARY;
const int ReplicaStateProto_ARRAYSIZE = ReplicaStateProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* ReplicaStateProto_descriptor();
inline const ::std::string& ReplicaStateProto_Name(ReplicaStateProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    ReplicaStateProto_descriptor(), value);
}
inline bool ReplicaStateProto_Parse(
    const ::std::string& name, ReplicaStateProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ReplicaStateProto>(
    ReplicaStateProto_descriptor(), name, value);
}
// ===================================================================

class ExtendedBlockProto : public ::google::protobuf::Message {
 public:
  ExtendedBlockProto();
  virtual ~ExtendedBlockProto();

  ExtendedBlockProto(const ExtendedBlockProto& from);

  inline ExtendedBlockProto& operator=(const ExtendedBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ExtendedBlockProto& default_instance();

  void Swap(ExtendedBlockProto* other);

  // implements Message ----------------------------------------------

  ExtendedBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ExtendedBlockProto& from);
  void MergeFrom(const ExtendedBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string poolId = 1;
  inline bool has_poolid() const;
  inline void clear_poolid();
  static const int kPoolIdFieldNumber = 1;
  inline const ::std::string& poolid() const;
  inline void set_poolid(const ::std::string& value);
  inline void set_poolid(const char* value);
  inline void set_poolid(const char* value, size_t size);
  inline ::std::string* mutable_poolid();
  inline ::std::string* release_poolid();
  inline void set_allocated_poolid(::std::string* poolid);

  // required uint64 blockId = 2;
  inline bool has_blockid() const;
  inline void clear_blockid();
  static const int kBlockIdFieldNumber = 2;
  inline ::google::protobuf::uint64 blockid() const;
  inline void set_blockid(::google::protobuf::uint64 value);

  // required uint64 generationStamp = 3;
  inline bool has_generationstamp() const;
  inline void clear_generationstamp();
  static const int kGenerationStampFieldNumber = 3;
  inline ::google::protobuf::uint64 generationstamp() const;
  inline void set_generationstamp(::google::protobuf::uint64 value);

  // optional uint64 numBytes = 4 [default = 0];
  inline bool has_numbytes() const;
  inline void clear_numbytes();
  static const int kNumBytesFieldNumber = 4;
  inline ::google::protobuf::uint64 numbytes() const;
  inline void set_numbytes(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ExtendedBlockProto)
 private:
  inline void set_has_poolid();
  inline void clear_has_poolid();
  inline void set_has_blockid();
  inline void clear_has_blockid();
  inline void set_has_generationstamp();
  inline void clear_has_generationstamp();
  inline void set_has_numbytes();
  inline void clear_has_numbytes();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* poolid_;
  ::google::protobuf::uint64 blockid_;
  ::google::protobuf::uint64 generationstamp_;
  ::google::protobuf::uint64 numbytes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static ExtendedBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class DatanodeIDProto : public ::google::protobuf::Message {
 public:
  DatanodeIDProto();
  virtual ~DatanodeIDProto();

  DatanodeIDProto(const DatanodeIDProto& from);

  inline DatanodeIDProto& operator=(const DatanodeIDProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DatanodeIDProto& default_instance();

  void Swap(DatanodeIDProto* other);

  // implements Message ----------------------------------------------

  DatanodeIDProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DatanodeIDProto& from);
  void MergeFrom(const DatanodeIDProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string ipAddr = 1;
  inline bool has_ipaddr() const;
  inline void clear_ipaddr();
  static const int kIpAddrFieldNumber = 1;
  inline const ::std::string& ipaddr() const;
  inline void set_ipaddr(const ::std::string& value);
  inline void set_ipaddr(const char* value);
  inline void set_ipaddr(const char* value, size_t size);
  inline ::std::string* mutable_ipaddr();
  inline ::std::string* release_ipaddr();
  inline void set_allocated_ipaddr(::std::string* ipaddr);

  // required string hostName = 2;
  inline bool has_hostname() const;
  inline void clear_hostname();
  static const int kHostNameFieldNumber = 2;
  inline const ::std::string& hostname() const;
  inline void set_hostname(const ::std::string& value);
  inline void set_hostname(const char* value);
  inline void set_hostname(const char* value, size_t size);
  inline ::std::string* mutable_hostname();
  inline ::std::string* release_hostname();
  inline void set_allocated_hostname(::std::string* hostname);

  // required string datanodeUuid = 3;
  inline bool has_datanodeuuid() const;
  inline void clear_datanodeuuid();
  static const int kDatanodeUuidFieldNumber = 3;
  inline const ::std::string& datanodeuuid() const;
  inline void set_datanodeuuid(const ::std::string& value);
  inline void set_datanodeuuid(const char* value);
  inline void set_datanodeuuid(const char* value, size_t size);
  inline ::std::string* mutable_datanodeuuid();
  inline ::std::string* release_datanodeuuid();
  inline void set_allocated_datanodeuuid(::std::string* datanodeuuid);

  // required uint32 xferPort = 4;
  inline bool has_xferport() const;
  inline void clear_xferport();
  static const int kXferPortFieldNumber = 4;
  inline ::google::protobuf::uint32 xferport() const;
  inline void set_xferport(::google::protobuf::uint32 value);

  // required uint32 infoPort = 5;
  inline bool has_infoport() const;
  inline void clear_infoport();
  static const int kInfoPortFieldNumber = 5;
  inline ::google::protobuf::uint32 infoport() const;
  inline void set_infoport(::google::protobuf::uint32 value);

  // required uint32 ipcPort = 6;
  inline bool has_ipcport() const;
  inline void clear_ipcport();
  static const int kIpcPortFieldNumber = 6;
  inline ::google::protobuf::uint32 ipcport() const;
  inline void set_ipcport(::google::protobuf::uint32 value);

  // optional uint32 infoSecurePort = 7 [default = 0];
  inline bool has_infosecureport() const;
  inline void clear_infosecureport();
  static const int kInfoSecurePortFieldNumber = 7;
  inline ::google::protobuf::uint32 infosecureport() const;
  inline void set_infosecureport(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DatanodeIDProto)
 private:
  inline void set_has_ipaddr();
  inline void clear_has_ipaddr();
  inline void set_has_hostname();
  inline void clear_has_hostname();
  inline void set_has_datanodeuuid();
  inline void clear_has_datanodeuuid();
  inline void set_has_xferport();
  inline void clear_has_xferport();
  inline void set_has_infoport();
  inline void clear_has_infoport();
  inline void set_has_ipcport();
  inline void clear_has_ipcport();
  inline void set_has_infosecureport();
  inline void clear_has_infosecureport();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* ipaddr_;
  ::std::string* hostname_;
  ::std::string* datanodeuuid_;
  ::google::protobuf::uint32 xferport_;
  ::google::protobuf::uint32 infoport_;
  ::google::protobuf::uint32 ipcport_;
  ::google::protobuf::uint32 infosecureport_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(7 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DatanodeIDProto* default_instance_;
};
// -------------------------------------------------------------------

class DatanodeLocalInfoProto : public ::google::protobuf::Message {
 public:
  DatanodeLocalInfoProto();
  virtual ~DatanodeLocalInfoProto();

  DatanodeLocalInfoProto(const DatanodeLocalInfoProto& from);

  inline DatanodeLocalInfoProto& operator=(const DatanodeLocalInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DatanodeLocalInfoProto& default_instance();

  void Swap(DatanodeLocalInfoProto* other);

  // implements Message ----------------------------------------------

  DatanodeLocalInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DatanodeLocalInfoProto& from);
  void MergeFrom(const DatanodeLocalInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string softwareVersion = 1;
  inline bool has_softwareversion() const;
  inline void clear_softwareversion();
  static const int kSoftwareVersionFieldNumber = 1;
  inline const ::std::string& softwareversion() const;
  inline void set_softwareversion(const ::std::string& value);
  inline void set_softwareversion(const char* value);
  inline void set_softwareversion(const char* value, size_t size);
  inline ::std::string* mutable_softwareversion();
  inline ::std::string* release_softwareversion();
  inline void set_allocated_softwareversion(::std::string* softwareversion);

  // required string configVersion = 2;
  inline bool has_configversion() const;
  inline void clear_configversion();
  static const int kConfigVersionFieldNumber = 2;
  inline const ::std::string& configversion() const;
  inline void set_configversion(const ::std::string& value);
  inline void set_configversion(const char* value);
  inline void set_configversion(const char* value, size_t size);
  inline ::std::string* mutable_configversion();
  inline ::std::string* release_configversion();
  inline void set_allocated_configversion(::std::string* configversion);

  // required uint64 uptime = 3;
  inline bool has_uptime() const;
  inline void clear_uptime();
  static const int kUptimeFieldNumber = 3;
  inline ::google::protobuf::uint64 uptime() const;
  inline void set_uptime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DatanodeLocalInfoProto)
 private:
  inline void set_has_softwareversion();
  inline void clear_has_softwareversion();
  inline void set_has_configversion();
  inline void clear_has_configversion();
  inline void set_has_uptime();
  inline void clear_has_uptime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* softwareversion_;
  ::std::string* configversion_;
  ::google::protobuf::uint64 uptime_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DatanodeLocalInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class DatanodeInfosProto : public ::google::protobuf::Message {
 public:
  DatanodeInfosProto();
  virtual ~DatanodeInfosProto();

  DatanodeInfosProto(const DatanodeInfosProto& from);

  inline DatanodeInfosProto& operator=(const DatanodeInfosProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DatanodeInfosProto& default_instance();

  void Swap(DatanodeInfosProto* other);

  // implements Message ----------------------------------------------

  DatanodeInfosProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DatanodeInfosProto& from);
  void MergeFrom(const DatanodeInfosProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.DatanodeInfoProto datanodes = 1;
  inline int datanodes_size() const;
  inline void clear_datanodes();
  static const int kDatanodesFieldNumber = 1;
  inline const ::hadoop::hdfs::DatanodeInfoProto& datanodes(int index) const;
  inline ::hadoop::hdfs::DatanodeInfoProto* mutable_datanodes(int index);
  inline ::hadoop::hdfs::DatanodeInfoProto* add_datanodes();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
      datanodes() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
      mutable_datanodes();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DatanodeInfosProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto > datanodes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DatanodeInfosProto* default_instance_;
};
// -------------------------------------------------------------------

class DatanodeInfoProto : public ::google::protobuf::Message {
 public:
  DatanodeInfoProto();
  virtual ~DatanodeInfoProto();

  DatanodeInfoProto(const DatanodeInfoProto& from);

  inline DatanodeInfoProto& operator=(const DatanodeInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DatanodeInfoProto& default_instance();

  void Swap(DatanodeInfoProto* other);

  // implements Message ----------------------------------------------

  DatanodeInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DatanodeInfoProto& from);
  void MergeFrom(const DatanodeInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef DatanodeInfoProto_AdminState AdminState;
  static const AdminState NORMAL = DatanodeInfoProto_AdminState_NORMAL;
  static const AdminState DECOMMISSION_INPROGRESS = DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS;
  static const AdminState DECOMMISSIONED = DatanodeInfoProto_AdminState_DECOMMISSIONED;
  static inline bool AdminState_IsValid(int value) {
    return DatanodeInfoProto_AdminState_IsValid(value);
  }
  static const AdminState AdminState_MIN =
    DatanodeInfoProto_AdminState_AdminState_MIN;
  static const AdminState AdminState_MAX =
    DatanodeInfoProto_AdminState_AdminState_MAX;
  static const int AdminState_ARRAYSIZE =
    DatanodeInfoProto_AdminState_AdminState_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  AdminState_descriptor() {
    return DatanodeInfoProto_AdminState_descriptor();
  }
  static inline const ::std::string& AdminState_Name(AdminState value) {
    return DatanodeInfoProto_AdminState_Name(value);
  }
  static inline bool AdminState_Parse(const ::std::string& name,
      AdminState* value) {
    return DatanodeInfoProto_AdminState_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.DatanodeIDProto id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline const ::hadoop::hdfs::DatanodeIDProto& id() const;
  inline ::hadoop::hdfs::DatanodeIDProto* mutable_id();
  inline ::hadoop::hdfs::DatanodeIDProto* release_id();
  inline void set_allocated_id(::hadoop::hdfs::DatanodeIDProto* id);

  // optional uint64 capacity = 2 [default = 0];
  inline bool has_capacity() const;
  inline void clear_capacity();
  static const int kCapacityFieldNumber = 2;
  inline ::google::protobuf::uint64 capacity() const;
  inline void set_capacity(::google::protobuf::uint64 value);

  // optional uint64 dfsUsed = 3 [default = 0];
  inline bool has_dfsused() const;
  inline void clear_dfsused();
  static const int kDfsUsedFieldNumber = 3;
  inline ::google::protobuf::uint64 dfsused() const;
  inline void set_dfsused(::google::protobuf::uint64 value);

  // optional uint64 remaining = 4 [default = 0];
  inline bool has_remaining() const;
  inline void clear_remaining();
  static const int kRemainingFieldNumber = 4;
  inline ::google::protobuf::uint64 remaining() const;
  inline void set_remaining(::google::protobuf::uint64 value);

  // optional uint64 blockPoolUsed = 5 [default = 0];
  inline bool has_blockpoolused() const;
  inline void clear_blockpoolused();
  static const int kBlockPoolUsedFieldNumber = 5;
  inline ::google::protobuf::uint64 blockpoolused() const;
  inline void set_blockpoolused(::google::protobuf::uint64 value);

  // optional uint64 lastUpdate = 6 [default = 0];
  inline bool has_lastupdate() const;
  inline void clear_lastupdate();
  static const int kLastUpdateFieldNumber = 6;
  inline ::google::protobuf::uint64 lastupdate() const;
  inline void set_lastupdate(::google::protobuf::uint64 value);

  // optional uint32 xceiverCount = 7 [default = 0];
  inline bool has_xceivercount() const;
  inline void clear_xceivercount();
  static const int kXceiverCountFieldNumber = 7;
  inline ::google::protobuf::uint32 xceivercount() const;
  inline void set_xceivercount(::google::protobuf::uint32 value);

  // optional string location = 8;
  inline bool has_location() const;
  inline void clear_location();
  static const int kLocationFieldNumber = 8;
  inline const ::std::string& location() const;
  inline void set_location(const ::std::string& value);
  inline void set_location(const char* value);
  inline void set_location(const char* value, size_t size);
  inline ::std::string* mutable_location();
  inline ::std::string* release_location();
  inline void set_allocated_location(::std::string* location);

  // optional .hadoop.hdfs.DatanodeInfoProto.AdminState adminState = 10 [default = NORMAL];
  inline bool has_adminstate() const;
  inline void clear_adminstate();
  static const int kAdminStateFieldNumber = 10;
  inline ::hadoop::hdfs::DatanodeInfoProto_AdminState adminstate() const;
  inline void set_adminstate(::hadoop::hdfs::DatanodeInfoProto_AdminState value);

  // optional uint64 cacheCapacity = 11 [default = 0];
  inline bool has_cachecapacity() const;
  inline void clear_cachecapacity();
  static const int kCacheCapacityFieldNumber = 11;
  inline ::google::protobuf::uint64 cachecapacity() const;
  inline void set_cachecapacity(::google::protobuf::uint64 value);

  // optional uint64 cacheUsed = 12 [default = 0];
  inline bool has_cacheused() const;
  inline void clear_cacheused();
  static const int kCacheUsedFieldNumber = 12;
  inline ::google::protobuf::uint64 cacheused() const;
  inline void set_cacheused(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DatanodeInfoProto)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_capacity();
  inline void clear_has_capacity();
  inline void set_has_dfsused();
  inline void clear_has_dfsused();
  inline void set_has_remaining();
  inline void clear_has_remaining();
  inline void set_has_blockpoolused();
  inline void clear_has_blockpoolused();
  inline void set_has_lastupdate();
  inline void clear_has_lastupdate();
  inline void set_has_xceivercount();
  inline void clear_has_xceivercount();
  inline void set_has_location();
  inline void clear_has_location();
  inline void set_has_adminstate();
  inline void clear_has_adminstate();
  inline void set_has_cachecapacity();
  inline void clear_has_cachecapacity();
  inline void set_has_cacheused();
  inline void clear_has_cacheused();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::DatanodeIDProto* id_;
  ::google::protobuf::uint64 capacity_;
  ::google::protobuf::uint64 dfsused_;
  ::google::protobuf::uint64 remaining_;
  ::google::protobuf::uint64 blockpoolused_;
  ::google::protobuf::uint64 lastupdate_;
  ::std::string* location_;
  ::google::protobuf::uint32 xceivercount_;
  int adminstate_;
  ::google::protobuf::uint64 cachecapacity_;
  ::google::protobuf::uint64 cacheused_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(11 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DatanodeInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class DatanodeStorageProto : public ::google::protobuf::Message {
 public:
  DatanodeStorageProto();
  virtual ~DatanodeStorageProto();

  DatanodeStorageProto(const DatanodeStorageProto& from);

  inline DatanodeStorageProto& operator=(const DatanodeStorageProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DatanodeStorageProto& default_instance();

  void Swap(DatanodeStorageProto* other);

  // implements Message ----------------------------------------------

  DatanodeStorageProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DatanodeStorageProto& from);
  void MergeFrom(const DatanodeStorageProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef DatanodeStorageProto_StorageState StorageState;
  static const StorageState NORMAL = DatanodeStorageProto_StorageState_NORMAL;
  static const StorageState READ_ONLY_SHARED = DatanodeStorageProto_StorageState_READ_ONLY_SHARED;
  static inline bool StorageState_IsValid(int value) {
    return DatanodeStorageProto_StorageState_IsValid(value);
  }
  static const StorageState StorageState_MIN =
    DatanodeStorageProto_StorageState_StorageState_MIN;
  static const StorageState StorageState_MAX =
    DatanodeStorageProto_StorageState_StorageState_MAX;
  static const int StorageState_ARRAYSIZE =
    DatanodeStorageProto_StorageState_StorageState_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  StorageState_descriptor() {
    return DatanodeStorageProto_StorageState_descriptor();
  }
  static inline const ::std::string& StorageState_Name(StorageState value) {
    return DatanodeStorageProto_StorageState_Name(value);
  }
  static inline bool StorageState_Parse(const ::std::string& name,
      StorageState* value) {
    return DatanodeStorageProto_StorageState_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required string storageUuid = 1;
  inline bool has_storageuuid() const;
  inline void clear_storageuuid();
  static const int kStorageUuidFieldNumber = 1;
  inline const ::std::string& storageuuid() const;
  inline void set_storageuuid(const ::std::string& value);
  inline void set_storageuuid(const char* value);
  inline void set_storageuuid(const char* value, size_t size);
  inline ::std::string* mutable_storageuuid();
  inline ::std::string* release_storageuuid();
  inline void set_allocated_storageuuid(::std::string* storageuuid);

  // optional .hadoop.hdfs.DatanodeStorageProto.StorageState state = 2 [default = NORMAL];
  inline bool has_state() const;
  inline void clear_state();
  static const int kStateFieldNumber = 2;
  inline ::hadoop::hdfs::DatanodeStorageProto_StorageState state() const;
  inline void set_state(::hadoop::hdfs::DatanodeStorageProto_StorageState value);

  // optional .hadoop.hdfs.StorageTypeProto storageType = 3 [default = DISK];
  inline bool has_storagetype() const;
  inline void clear_storagetype();
  static const int kStorageTypeFieldNumber = 3;
  inline ::hadoop::hdfs::StorageTypeProto storagetype() const;
  inline void set_storagetype(::hadoop::hdfs::StorageTypeProto value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DatanodeStorageProto)
 private:
  inline void set_has_storageuuid();
  inline void clear_has_storageuuid();
  inline void set_has_state();
  inline void clear_has_state();
  inline void set_has_storagetype();
  inline void clear_has_storagetype();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* storageuuid_;
  int state_;
  int storagetype_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DatanodeStorageProto* default_instance_;
};
// -------------------------------------------------------------------

class StorageReportProto : public ::google::protobuf::Message {
 public:
  StorageReportProto();
  virtual ~StorageReportProto();

  StorageReportProto(const StorageReportProto& from);

  inline StorageReportProto& operator=(const StorageReportProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StorageReportProto& default_instance();

  void Swap(StorageReportProto* other);

  // implements Message ----------------------------------------------

  StorageReportProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StorageReportProto& from);
  void MergeFrom(const StorageReportProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string storageUuid = 1 [deprecated = true];
  inline bool has_storageuuid() const PROTOBUF_DEPRECATED;
  inline void clear_storageuuid() PROTOBUF_DEPRECATED;
  static const int kStorageUuidFieldNumber = 1;
  inline const ::std::string& storageuuid() const PROTOBUF_DEPRECATED;
  inline void set_storageuuid(const ::std::string& value) PROTOBUF_DEPRECATED;
  inline void set_storageuuid(const char* value) PROTOBUF_DEPRECATED;
  inline void set_storageuuid(const char* value, size_t size) PROTOBUF_DEPRECATED;
  inline ::std::string* mutable_storageuuid() PROTOBUF_DEPRECATED;
  inline ::std::string* release_storageuuid() PROTOBUF_DEPRECATED;
  inline void set_allocated_storageuuid(::std::string* storageuuid) PROTOBUF_DEPRECATED;

  // optional bool failed = 2 [default = false];
  inline bool has_failed() const;
  inline void clear_failed();
  static const int kFailedFieldNumber = 2;
  inline bool failed() const;
  inline void set_failed(bool value);

  // optional uint64 capacity = 3 [default = 0];
  inline bool has_capacity() const;
  inline void clear_capacity();
  static const int kCapacityFieldNumber = 3;
  inline ::google::protobuf::uint64 capacity() const;
  inline void set_capacity(::google::protobuf::uint64 value);

  // optional uint64 dfsUsed = 4 [default = 0];
  inline bool has_dfsused() const;
  inline void clear_dfsused();
  static const int kDfsUsedFieldNumber = 4;
  inline ::google::protobuf::uint64 dfsused() const;
  inline void set_dfsused(::google::protobuf::uint64 value);

  // optional uint64 remaining = 5 [default = 0];
  inline bool has_remaining() const;
  inline void clear_remaining();
  static const int kRemainingFieldNumber = 5;
  inline ::google::protobuf::uint64 remaining() const;
  inline void set_remaining(::google::protobuf::uint64 value);

  // optional uint64 blockPoolUsed = 6 [default = 0];
  inline bool has_blockpoolused() const;
  inline void clear_blockpoolused();
  static const int kBlockPoolUsedFieldNumber = 6;
  inline ::google::protobuf::uint64 blockpoolused() const;
  inline void set_blockpoolused(::google::protobuf::uint64 value);

  // optional .hadoop.hdfs.DatanodeStorageProto storage = 7;
  inline bool has_storage() const;
  inline void clear_storage();
  static const int kStorageFieldNumber = 7;
  inline const ::hadoop::hdfs::DatanodeStorageProto& storage() const;
  inline ::hadoop::hdfs::DatanodeStorageProto* mutable_storage();
  inline ::hadoop::hdfs::DatanodeStorageProto* release_storage();
  inline void set_allocated_storage(::hadoop::hdfs::DatanodeStorageProto* storage);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.StorageReportProto)
 private:
  inline void set_has_storageuuid();
  inline void clear_has_storageuuid();
  inline void set_has_failed();
  inline void clear_has_failed();
  inline void set_has_capacity();
  inline void clear_has_capacity();
  inline void set_has_dfsused();
  inline void clear_has_dfsused();
  inline void set_has_remaining();
  inline void clear_has_remaining();
  inline void set_has_blockpoolused();
  inline void clear_has_blockpoolused();
  inline void set_has_storage();
  inline void clear_has_storage();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* storageuuid_;
  ::google::protobuf::uint64 capacity_;
  ::google::protobuf::uint64 dfsused_;
  ::google::protobuf::uint64 remaining_;
  ::google::protobuf::uint64 blockpoolused_;
  ::hadoop::hdfs::DatanodeStorageProto* storage_;
  bool failed_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(7 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static StorageReportProto* default_instance_;
};
// -------------------------------------------------------------------

class ContentSummaryProto : public ::google::protobuf::Message {
 public:
  ContentSummaryProto();
  virtual ~ContentSummaryProto();

  ContentSummaryProto(const ContentSummaryProto& from);

  inline ContentSummaryProto& operator=(const ContentSummaryProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ContentSummaryProto& default_instance();

  void Swap(ContentSummaryProto* other);

  // implements Message ----------------------------------------------

  ContentSummaryProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ContentSummaryProto& from);
  void MergeFrom(const ContentSummaryProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 length = 1;
  inline bool has_length() const;
  inline void clear_length();
  static const int kLengthFieldNumber = 1;
  inline ::google::protobuf::uint64 length() const;
  inline void set_length(::google::protobuf::uint64 value);

  // required uint64 fileCount = 2;
  inline bool has_filecount() const;
  inline void clear_filecount();
  static const int kFileCountFieldNumber = 2;
  inline ::google::protobuf::uint64 filecount() const;
  inline void set_filecount(::google::protobuf::uint64 value);

  // required uint64 directoryCount = 3;
  inline bool has_directorycount() const;
  inline void clear_directorycount();
  static const int kDirectoryCountFieldNumber = 3;
  inline ::google::protobuf::uint64 directorycount() const;
  inline void set_directorycount(::google::protobuf::uint64 value);

  // required uint64 quota = 4;
  inline bool has_quota() const;
  inline void clear_quota();
  static const int kQuotaFieldNumber = 4;
  inline ::google::protobuf::uint64 quota() const;
  inline void set_quota(::google::protobuf::uint64 value);

  // required uint64 spaceConsumed = 5;
  inline bool has_spaceconsumed() const;
  inline void clear_spaceconsumed();
  static const int kSpaceConsumedFieldNumber = 5;
  inline ::google::protobuf::uint64 spaceconsumed() const;
  inline void set_spaceconsumed(::google::protobuf::uint64 value);

  // required uint64 spaceQuota = 6;
  inline bool has_spacequota() const;
  inline void clear_spacequota();
  static const int kSpaceQuotaFieldNumber = 6;
  inline ::google::protobuf::uint64 spacequota() const;
  inline void set_spacequota(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ContentSummaryProto)
 private:
  inline void set_has_length();
  inline void clear_has_length();
  inline void set_has_filecount();
  inline void clear_has_filecount();
  inline void set_has_directorycount();
  inline void clear_has_directorycount();
  inline void set_has_quota();
  inline void clear_has_quota();
  inline void set_has_spaceconsumed();
  inline void clear_has_spaceconsumed();
  inline void set_has_spacequota();
  inline void clear_has_spacequota();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 length_;
  ::google::protobuf::uint64 filecount_;
  ::google::protobuf::uint64 directorycount_;
  ::google::protobuf::uint64 quota_;
  ::google::protobuf::uint64 spaceconsumed_;
  ::google::protobuf::uint64 spacequota_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static ContentSummaryProto* default_instance_;
};
// -------------------------------------------------------------------

class CorruptFileBlocksProto : public ::google::protobuf::Message {
 public:
  CorruptFileBlocksProto();
  virtual ~CorruptFileBlocksProto();

  CorruptFileBlocksProto(const CorruptFileBlocksProto& from);

  inline CorruptFileBlocksProto& operator=(const CorruptFileBlocksProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CorruptFileBlocksProto& default_instance();

  void Swap(CorruptFileBlocksProto* other);

  // implements Message ----------------------------------------------

  CorruptFileBlocksProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CorruptFileBlocksProto& from);
  void MergeFrom(const CorruptFileBlocksProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string files = 1;
  inline int files_size() const;
  inline void clear_files();
  static const int kFilesFieldNumber = 1;
  inline const ::std::string& files(int index) const;
  inline ::std::string* mutable_files(int index);
  inline void set_files(int index, const ::std::string& value);
  inline void set_files(int index, const char* value);
  inline void set_files(int index, const char* value, size_t size);
  inline ::std::string* add_files();
  inline void add_files(const ::std::string& value);
  inline void add_files(const char* value);
  inline void add_files(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& files() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_files();

  // required string cookie = 2;
  inline bool has_cookie() const;
  inline void clear_cookie();
  static const int kCookieFieldNumber = 2;
  inline const ::std::string& cookie() const;
  inline void set_cookie(const ::std::string& value);
  inline void set_cookie(const char* value);
  inline void set_cookie(const char* value, size_t size);
  inline ::std::string* mutable_cookie();
  inline ::std::string* release_cookie();
  inline void set_allocated_cookie(::std::string* cookie);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CorruptFileBlocksProto)
 private:
  inline void set_has_cookie();
  inline void clear_has_cookie();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::std::string> files_;
  ::std::string* cookie_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static CorruptFileBlocksProto* default_instance_;
};
// -------------------------------------------------------------------

class FsPermissionProto : public ::google::protobuf::Message {
 public:
  FsPermissionProto();
  virtual ~FsPermissionProto();

  FsPermissionProto(const FsPermissionProto& from);

  inline FsPermissionProto& operator=(const FsPermissionProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FsPermissionProto& default_instance();

  void Swap(FsPermissionProto* other);

  // implements Message ----------------------------------------------

  FsPermissionProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FsPermissionProto& from);
  void MergeFrom(const FsPermissionProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 perm = 1;
  inline bool has_perm() const;
  inline void clear_perm();
  static const int kPermFieldNumber = 1;
  inline ::google::protobuf::uint32 perm() const;
  inline void set_perm(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.FsPermissionProto)
 private:
  inline void set_has_perm();
  inline void clear_has_perm();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 perm_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static FsPermissionProto* default_instance_;
};
// -------------------------------------------------------------------

class StorageTypesProto : public ::google::protobuf::Message {
 public:
  StorageTypesProto();
  virtual ~StorageTypesProto();

  StorageTypesProto(const StorageTypesProto& from);

  inline StorageTypesProto& operator=(const StorageTypesProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StorageTypesProto& default_instance();

  void Swap(StorageTypesProto* other);

  // implements Message ----------------------------------------------

  StorageTypesProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StorageTypesProto& from);
  void MergeFrom(const StorageTypesProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.StorageTypeProto storageTypes = 1;
  inline int storagetypes_size() const;
  inline void clear_storagetypes();
  static const int kStorageTypesFieldNumber = 1;
  inline ::hadoop::hdfs::StorageTypeProto storagetypes(int index) const;
  inline void set_storagetypes(int index, ::hadoop::hdfs::StorageTypeProto value);
  inline void add_storagetypes(::hadoop::hdfs::StorageTypeProto value);
  inline const ::google::protobuf::RepeatedField<int>& storagetypes() const;
  inline ::google::protobuf::RepeatedField<int>* mutable_storagetypes();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.StorageTypesProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedField<int> storagetypes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static StorageTypesProto* default_instance_;
};
// -------------------------------------------------------------------

class BlockStoragePolicyProto : public ::google::protobuf::Message {
 public:
  BlockStoragePolicyProto();
  virtual ~BlockStoragePolicyProto();

  BlockStoragePolicyProto(const BlockStoragePolicyProto& from);

  inline BlockStoragePolicyProto& operator=(const BlockStoragePolicyProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BlockStoragePolicyProto& default_instance();

  void Swap(BlockStoragePolicyProto* other);

  // implements Message ----------------------------------------------

  BlockStoragePolicyProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BlockStoragePolicyProto& from);
  void MergeFrom(const BlockStoragePolicyProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 policyId = 1;
  inline bool has_policyid() const;
  inline void clear_policyid();
  static const int kPolicyIdFieldNumber = 1;
  inline ::google::protobuf::uint32 policyid() const;
  inline void set_policyid(::google::protobuf::uint32 value);

  // required string name = 2;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 2;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // required .hadoop.hdfs.StorageTypesProto creationPolicy = 3;
  inline bool has_creationpolicy() const;
  inline void clear_creationpolicy();
  static const int kCreationPolicyFieldNumber = 3;
  inline const ::hadoop::hdfs::StorageTypesProto& creationpolicy() const;
  inline ::hadoop::hdfs::StorageTypesProto* mutable_creationpolicy();
  inline ::hadoop::hdfs::StorageTypesProto* release_creationpolicy();
  inline void set_allocated_creationpolicy(::hadoop::hdfs::StorageTypesProto* creationpolicy);

  // optional .hadoop.hdfs.StorageTypesProto creationFallbackPolicy = 4;
  inline bool has_creationfallbackpolicy() const;
  inline void clear_creationfallbackpolicy();
  static const int kCreationFallbackPolicyFieldNumber = 4;
  inline const ::hadoop::hdfs::StorageTypesProto& creationfallbackpolicy() const;
  inline ::hadoop::hdfs::StorageTypesProto* mutable_creationfallbackpolicy();
  inline ::hadoop::hdfs::StorageTypesProto* release_creationfallbackpolicy();
  inline void set_allocated_creationfallbackpolicy(::hadoop::hdfs::StorageTypesProto* creationfallbackpolicy);

  // optional .hadoop.hdfs.StorageTypesProto replicationFallbackPolicy = 5;
  inline bool has_replicationfallbackpolicy() const;
  inline void clear_replicationfallbackpolicy();
  static const int kReplicationFallbackPolicyFieldNumber = 5;
  inline const ::hadoop::hdfs::StorageTypesProto& replicationfallbackpolicy() const;
  inline ::hadoop::hdfs::StorageTypesProto* mutable_replicationfallbackpolicy();
  inline ::hadoop::hdfs::StorageTypesProto* release_replicationfallbackpolicy();
  inline void set_allocated_replicationfallbackpolicy(::hadoop::hdfs::StorageTypesProto* replicationfallbackpolicy);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BlockStoragePolicyProto)
 private:
  inline void set_has_policyid();
  inline void clear_has_policyid();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_creationpolicy();
  inline void clear_has_creationpolicy();
  inline void set_has_creationfallbackpolicy();
  inline void clear_has_creationfallbackpolicy();
  inline void set_has_replicationfallbackpolicy();
  inline void clear_has_replicationfallbackpolicy();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* name_;
  ::hadoop::hdfs::StorageTypesProto* creationpolicy_;
  ::hadoop::hdfs::StorageTypesProto* creationfallbackpolicy_;
  ::hadoop::hdfs::StorageTypesProto* replicationfallbackpolicy_;
  ::google::protobuf::uint32 policyid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static BlockStoragePolicyProto* default_instance_;
};
// -------------------------------------------------------------------

class StorageUuidsProto : public ::google::protobuf::Message {
 public:
  StorageUuidsProto();
  virtual ~StorageUuidsProto();

  StorageUuidsProto(const StorageUuidsProto& from);

  inline StorageUuidsProto& operator=(const StorageUuidsProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StorageUuidsProto& default_instance();

  void Swap(StorageUuidsProto* other);

  // implements Message ----------------------------------------------

  StorageUuidsProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StorageUuidsProto& from);
  void MergeFrom(const StorageUuidsProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string storageUuids = 1;
  inline int storageuuids_size() const;
  inline void clear_storageuuids();
  static const int kStorageUuidsFieldNumber = 1;
  inline const ::std::string& storageuuids(int index) const;
  inline ::std::string* mutable_storageuuids(int index);
  inline void set_storageuuids(int index, const ::std::string& value);
  inline void set_storageuuids(int index, const char* value);
  inline void set_storageuuids(int index, const char* value, size_t size);
  inline ::std::string* add_storageuuids();
  inline void add_storageuuids(const ::std::string& value);
  inline void add_storageuuids(const char* value);
  inline void add_storageuuids(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& storageuuids() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_storageuuids();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.StorageUuidsProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::std::string> storageuuids_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static StorageUuidsProto* default_instance_;
};
// -------------------------------------------------------------------

class LocatedBlockProto : public ::google::protobuf::Message {
 public:
  LocatedBlockProto();
  virtual ~LocatedBlockProto();

  LocatedBlockProto(const LocatedBlockProto& from);

  inline LocatedBlockProto& operator=(const LocatedBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const LocatedBlockProto& default_instance();

  void Swap(LocatedBlockProto* other);

  // implements Message ----------------------------------------------

  LocatedBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const LocatedBlockProto& from);
  void MergeFrom(const LocatedBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ExtendedBlockProto b = 1;
  inline bool has_b() const;
  inline void clear_b();
  static const int kBFieldNumber = 1;
  inline const ::hadoop::hdfs::ExtendedBlockProto& b() const;
  inline ::hadoop::hdfs::ExtendedBlockProto* mutable_b();
  inline ::hadoop::hdfs::ExtendedBlockProto* release_b();
  inline void set_allocated_b(::hadoop::hdfs::ExtendedBlockProto* b);

  // required uint64 offset = 2;
  inline bool has_offset() const;
  inline void clear_offset();
  static const int kOffsetFieldNumber = 2;
  inline ::google::protobuf::uint64 offset() const;
  inline void set_offset(::google::protobuf::uint64 value);

  // repeated .hadoop.hdfs.DatanodeInfoProto locs = 3;
  inline int locs_size() const;
  inline void clear_locs();
  static const int kLocsFieldNumber = 3;
  inline const ::hadoop::hdfs::DatanodeInfoProto& locs(int index) const;
  inline ::hadoop::hdfs::DatanodeInfoProto* mutable_locs(int index);
  inline ::hadoop::hdfs::DatanodeInfoProto* add_locs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
      locs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
      mutable_locs();

  // required bool corrupt = 4;
  inline bool has_corrupt() const;
  inline void clear_corrupt();
  static const int kCorruptFieldNumber = 4;
  inline bool corrupt() const;
  inline void set_corrupt(bool value);

  // required .hadoop.common.TokenProto blockToken = 5;
  inline bool has_blocktoken() const;
  inline void clear_blocktoken();
  static const int kBlockTokenFieldNumber = 5;
  inline const ::hadoop::common::TokenProto& blocktoken() const;
  inline ::hadoop::common::TokenProto* mutable_blocktoken();
  inline ::hadoop::common::TokenProto* release_blocktoken();
  inline void set_allocated_blocktoken(::hadoop::common::TokenProto* blocktoken);

  // repeated bool isCached = 6 [packed = true];
  inline int iscached_size() const;
  inline void clear_iscached();
  static const int kIsCachedFieldNumber = 6;
  inline bool iscached(int index) const;
  inline void set_iscached(int index, bool value);
  inline void add_iscached(bool value);
  inline const ::google::protobuf::RepeatedField< bool >&
      iscached() const;
  inline ::google::protobuf::RepeatedField< bool >*
      mutable_iscached();

  // repeated .hadoop.hdfs.StorageTypeProto storageTypes = 7;
  inline int storagetypes_size() const;
  inline void clear_storagetypes();
  static const int kStorageTypesFieldNumber = 7;
  inline ::hadoop::hdfs::StorageTypeProto storagetypes(int index) const;
  inline void set_storagetypes(int index, ::hadoop::hdfs::StorageTypeProto value);
  inline void add_storagetypes(::hadoop::hdfs::StorageTypeProto value);
  inline const ::google::protobuf::RepeatedField<int>& storagetypes() const;
  inline ::google::protobuf::RepeatedField<int>* mutable_storagetypes();

  // repeated string storageIDs = 8;
  inline int storageids_size() const;
  inline void clear_storageids();
  static const int kStorageIDsFieldNumber = 8;
  inline const ::std::string& storageids(int index) const;
  inline ::std::string* mutable_storageids(int index);
  inline void set_storageids(int index, const ::std::string& value);
  inline void set_storageids(int index, const char* value);
  inline void set_storageids(int index, const char* value, size_t size);
  inline ::std::string* add_storageids();
  inline void add_storageids(const ::std::string& value);
  inline void add_storageids(const char* value);
  inline void add_storageids(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& storageids() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_storageids();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.LocatedBlockProto)
 private:
  inline void set_has_b();
  inline void clear_has_b();
  inline void set_has_offset();
  inline void clear_has_offset();
  inline void set_has_corrupt();
  inline void clear_has_corrupt();
  inline void set_has_blocktoken();
  inline void clear_has_blocktoken();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ExtendedBlockProto* b_;
  ::google::protobuf::uint64 offset_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto > locs_;
  ::hadoop::common::TokenProto* blocktoken_;
  ::google::protobuf::RepeatedField< bool > iscached_;
  mutable int _iscached_cached_byte_size_;
  ::google::protobuf::RepeatedField<int> storagetypes_;
  ::google::protobuf::RepeatedPtrField< ::std::string> storageids_;
  bool corrupt_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(8 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static LocatedBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class DataEncryptionKeyProto : public ::google::protobuf::Message {
 public:
  DataEncryptionKeyProto();
  virtual ~DataEncryptionKeyProto();

  DataEncryptionKeyProto(const DataEncryptionKeyProto& from);

  inline DataEncryptionKeyProto& operator=(const DataEncryptionKeyProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DataEncryptionKeyProto& default_instance();

  void Swap(DataEncryptionKeyProto* other);

  // implements Message ----------------------------------------------

  DataEncryptionKeyProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DataEncryptionKeyProto& from);
  void MergeFrom(const DataEncryptionKeyProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 keyId = 1;
  inline bool has_keyid() const;
  inline void clear_keyid();
  static const int kKeyIdFieldNumber = 1;
  inline ::google::protobuf::uint32 keyid() const;
  inline void set_keyid(::google::protobuf::uint32 value);

  // required string blockPoolId = 2;
  inline bool has_blockpoolid() const;
  inline void clear_blockpoolid();
  static const int kBlockPoolIdFieldNumber = 2;
  inline const ::std::string& blockpoolid() const;
  inline void set_blockpoolid(const ::std::string& value);
  inline void set_blockpoolid(const char* value);
  inline void set_blockpoolid(const char* value, size_t size);
  inline ::std::string* mutable_blockpoolid();
  inline ::std::string* release_blockpoolid();
  inline void set_allocated_blockpoolid(::std::string* blockpoolid);

  // required bytes nonce = 3;
  inline bool has_nonce() const;
  inline void clear_nonce();
  static const int kNonceFieldNumber = 3;
  inline const ::std::string& nonce() const;
  inline void set_nonce(const ::std::string& value);
  inline void set_nonce(const char* value);
  inline void set_nonce(const void* value, size_t size);
  inline ::std::string* mutable_nonce();
  inline ::std::string* release_nonce();
  inline void set_allocated_nonce(::std::string* nonce);

  // required bytes encryptionKey = 4;
  inline bool has_encryptionkey() const;
  inline void clear_encryptionkey();
  static const int kEncryptionKeyFieldNumber = 4;
  inline const ::std::string& encryptionkey() const;
  inline void set_encryptionkey(const ::std::string& value);
  inline void set_encryptionkey(const char* value);
  inline void set_encryptionkey(const void* value, size_t size);
  inline ::std::string* mutable_encryptionkey();
  inline ::std::string* release_encryptionkey();
  inline void set_allocated_encryptionkey(::std::string* encryptionkey);

  // required uint64 expiryDate = 5;
  inline bool has_expirydate() const;
  inline void clear_expirydate();
  static const int kExpiryDateFieldNumber = 5;
  inline ::google::protobuf::uint64 expirydate() const;
  inline void set_expirydate(::google::protobuf::uint64 value);

  // optional string encryptionAlgorithm = 6;
  inline bool has_encryptionalgorithm() const;
  inline void clear_encryptionalgorithm();
  static const int kEncryptionAlgorithmFieldNumber = 6;
  inline const ::std::string& encryptionalgorithm() const;
  inline void set_encryptionalgorithm(const ::std::string& value);
  inline void set_encryptionalgorithm(const char* value);
  inline void set_encryptionalgorithm(const char* value, size_t size);
  inline ::std::string* mutable_encryptionalgorithm();
  inline ::std::string* release_encryptionalgorithm();
  inline void set_allocated_encryptionalgorithm(::std::string* encryptionalgorithm);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DataEncryptionKeyProto)
 private:
  inline void set_has_keyid();
  inline void clear_has_keyid();
  inline void set_has_blockpoolid();
  inline void clear_has_blockpoolid();
  inline void set_has_nonce();
  inline void clear_has_nonce();
  inline void set_has_encryptionkey();
  inline void clear_has_encryptionkey();
  inline void set_has_expirydate();
  inline void clear_has_expirydate();
  inline void set_has_encryptionalgorithm();
  inline void clear_has_encryptionalgorithm();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* blockpoolid_;
  ::std::string* nonce_;
  ::std::string* encryptionkey_;
  ::google::protobuf::uint64 expirydate_;
  ::std::string* encryptionalgorithm_;
  ::google::protobuf::uint32 keyid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DataEncryptionKeyProto* default_instance_;
};
// -------------------------------------------------------------------

class FileEncryptionInfoProto : public ::google::protobuf::Message {
 public:
  FileEncryptionInfoProto();
  virtual ~FileEncryptionInfoProto();

  FileEncryptionInfoProto(const FileEncryptionInfoProto& from);

  inline FileEncryptionInfoProto& operator=(const FileEncryptionInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FileEncryptionInfoProto& default_instance();

  void Swap(FileEncryptionInfoProto* other);

  // implements Message ----------------------------------------------

  FileEncryptionInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FileEncryptionInfoProto& from);
  void MergeFrom(const FileEncryptionInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.CipherSuiteProto suite = 1;
  inline bool has_suite() const;
  inline void clear_suite();
  static const int kSuiteFieldNumber = 1;
  inline ::hadoop::hdfs::CipherSuiteProto suite() const;
  inline void set_suite(::hadoop::hdfs::CipherSuiteProto value);

  // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 2;
  inline bool has_cryptoprotocolversion() const;
  inline void clear_cryptoprotocolversion();
  static const int kCryptoProtocolVersionFieldNumber = 2;
  inline ::hadoop::hdfs::CryptoProtocolVersionProto cryptoprotocolversion() const;
  inline void set_cryptoprotocolversion(::hadoop::hdfs::CryptoProtocolVersionProto value);

  // required bytes key = 3;
  inline bool has_key() const;
  inline void clear_key();
  static const int kKeyFieldNumber = 3;
  inline const ::std::string& key() const;
  inline void set_key(const ::std::string& value);
  inline void set_key(const char* value);
  inline void set_key(const void* value, size_t size);
  inline ::std::string* mutable_key();
  inline ::std::string* release_key();
  inline void set_allocated_key(::std::string* key);

  // required bytes iv = 4;
  inline bool has_iv() const;
  inline void clear_iv();
  static const int kIvFieldNumber = 4;
  inline const ::std::string& iv() const;
  inline void set_iv(const ::std::string& value);
  inline void set_iv(const char* value);
  inline void set_iv(const void* value, size_t size);
  inline ::std::string* mutable_iv();
  inline ::std::string* release_iv();
  inline void set_allocated_iv(::std::string* iv);

  // required string keyName = 5;
  inline bool has_keyname() const;
  inline void clear_keyname();
  static const int kKeyNameFieldNumber = 5;
  inline const ::std::string& keyname() const;
  inline void set_keyname(const ::std::string& value);
  inline void set_keyname(const char* value);
  inline void set_keyname(const char* value, size_t size);
  inline ::std::string* mutable_keyname();
  inline ::std::string* release_keyname();
  inline void set_allocated_keyname(::std::string* keyname);

  // required string ezKeyVersionName = 6;
  inline bool has_ezkeyversionname() const;
  inline void clear_ezkeyversionname();
  static const int kEzKeyVersionNameFieldNumber = 6;
  inline const ::std::string& ezkeyversionname() const;
  inline void set_ezkeyversionname(const ::std::string& value);
  inline void set_ezkeyversionname(const char* value);
  inline void set_ezkeyversionname(const char* value, size_t size);
  inline ::std::string* mutable_ezkeyversionname();
  inline ::std::string* release_ezkeyversionname();
  inline void set_allocated_ezkeyversionname(::std::string* ezkeyversionname);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.FileEncryptionInfoProto)
 private:
  inline void set_has_suite();
  inline void clear_has_suite();
  inline void set_has_cryptoprotocolversion();
  inline void clear_has_cryptoprotocolversion();
  inline void set_has_key();
  inline void clear_has_key();
  inline void set_has_iv();
  inline void clear_has_iv();
  inline void set_has_keyname();
  inline void clear_has_keyname();
  inline void set_has_ezkeyversionname();
  inline void clear_has_ezkeyversionname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int suite_;
  int cryptoprotocolversion_;
  ::std::string* key_;
  ::std::string* iv_;
  ::std::string* keyname_;
  ::std::string* ezkeyversionname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static FileEncryptionInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class PerFileEncryptionInfoProto : public ::google::protobuf::Message {
 public:
  PerFileEncryptionInfoProto();
  virtual ~PerFileEncryptionInfoProto();

  PerFileEncryptionInfoProto(const PerFileEncryptionInfoProto& from);

  inline PerFileEncryptionInfoProto& operator=(const PerFileEncryptionInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PerFileEncryptionInfoProto& default_instance();

  void Swap(PerFileEncryptionInfoProto* other);

  // implements Message ----------------------------------------------

  PerFileEncryptionInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PerFileEncryptionInfoProto& from);
  void MergeFrom(const PerFileEncryptionInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bytes key = 1;
  inline bool has_key() const;
  inline void clear_key();
  static const int kKeyFieldNumber = 1;
  inline const ::std::string& key() const;
  inline void set_key(const ::std::string& value);
  inline void set_key(const char* value);
  inline void set_key(const void* value, size_t size);
  inline ::std::string* mutable_key();
  inline ::std::string* release_key();
  inline void set_allocated_key(::std::string* key);

  // required bytes iv = 2;
  inline bool has_iv() const;
  inline void clear_iv();
  static const int kIvFieldNumber = 2;
  inline const ::std::string& iv() const;
  inline void set_iv(const ::std::string& value);
  inline void set_iv(const char* value);
  inline void set_iv(const void* value, size_t size);
  inline ::std::string* mutable_iv();
  inline ::std::string* release_iv();
  inline void set_allocated_iv(::std::string* iv);

  // required string ezKeyVersionName = 3;
  inline bool has_ezkeyversionname() const;
  inline void clear_ezkeyversionname();
  static const int kEzKeyVersionNameFieldNumber = 3;
  inline const ::std::string& ezkeyversionname() const;
  inline void set_ezkeyversionname(const ::std::string& value);
  inline void set_ezkeyversionname(const char* value);
  inline void set_ezkeyversionname(const char* value, size_t size);
  inline ::std::string* mutable_ezkeyversionname();
  inline ::std::string* release_ezkeyversionname();
  inline void set_allocated_ezkeyversionname(::std::string* ezkeyversionname);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.PerFileEncryptionInfoProto)
 private:
  inline void set_has_key();
  inline void clear_has_key();
  inline void set_has_iv();
  inline void clear_has_iv();
  inline void set_has_ezkeyversionname();
  inline void clear_has_ezkeyversionname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* key_;
  ::std::string* iv_;
  ::std::string* ezkeyversionname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static PerFileEncryptionInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class ZoneEncryptionInfoProto : public ::google::protobuf::Message {
 public:
  ZoneEncryptionInfoProto();
  virtual ~ZoneEncryptionInfoProto();

  ZoneEncryptionInfoProto(const ZoneEncryptionInfoProto& from);

  inline ZoneEncryptionInfoProto& operator=(const ZoneEncryptionInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ZoneEncryptionInfoProto& default_instance();

  void Swap(ZoneEncryptionInfoProto* other);

  // implements Message ----------------------------------------------

  ZoneEncryptionInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ZoneEncryptionInfoProto& from);
  void MergeFrom(const ZoneEncryptionInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.CipherSuiteProto suite = 1;
  inline bool has_suite() const;
  inline void clear_suite();
  static const int kSuiteFieldNumber = 1;
  inline ::hadoop::hdfs::CipherSuiteProto suite() const;
  inline void set_suite(::hadoop::hdfs::CipherSuiteProto value);

  // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 2;
  inline bool has_cryptoprotocolversion() const;
  inline void clear_cryptoprotocolversion();
  static const int kCryptoProtocolVersionFieldNumber = 2;
  inline ::hadoop::hdfs::CryptoProtocolVersionProto cryptoprotocolversion() const;
  inline void set_cryptoprotocolversion(::hadoop::hdfs::CryptoProtocolVersionProto value);

  // required string keyName = 3;
  inline bool has_keyname() const;
  inline void clear_keyname();
  static const int kKeyNameFieldNumber = 3;
  inline const ::std::string& keyname() const;
  inline void set_keyname(const ::std::string& value);
  inline void set_keyname(const char* value);
  inline void set_keyname(const char* value, size_t size);
  inline ::std::string* mutable_keyname();
  inline ::std::string* release_keyname();
  inline void set_allocated_keyname(::std::string* keyname);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ZoneEncryptionInfoProto)
 private:
  inline void set_has_suite();
  inline void clear_has_suite();
  inline void set_has_cryptoprotocolversion();
  inline void clear_has_cryptoprotocolversion();
  inline void set_has_keyname();
  inline void clear_has_keyname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int suite_;
  int cryptoprotocolversion_;
  ::std::string* keyname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static ZoneEncryptionInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class CipherOptionProto : public ::google::protobuf::Message {
 public:
  CipherOptionProto();
  virtual ~CipherOptionProto();

  CipherOptionProto(const CipherOptionProto& from);

  inline CipherOptionProto& operator=(const CipherOptionProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CipherOptionProto& default_instance();

  void Swap(CipherOptionProto* other);

  // implements Message ----------------------------------------------

  CipherOptionProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CipherOptionProto& from);
  void MergeFrom(const CipherOptionProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.CipherSuiteProto suite = 1;
  inline bool has_suite() const;
  inline void clear_suite();
  static const int kSuiteFieldNumber = 1;
  inline ::hadoop::hdfs::CipherSuiteProto suite() const;
  inline void set_suite(::hadoop::hdfs::CipherSuiteProto value);

  // optional bytes inKey = 2;
  inline bool has_inkey() const;
  inline void clear_inkey();
  static const int kInKeyFieldNumber = 2;
  inline const ::std::string& inkey() const;
  inline void set_inkey(const ::std::string& value);
  inline void set_inkey(const char* value);
  inline void set_inkey(const void* value, size_t size);
  inline ::std::string* mutable_inkey();
  inline ::std::string* release_inkey();
  inline void set_allocated_inkey(::std::string* inkey);

  // optional bytes inIv = 3;
  inline bool has_iniv() const;
  inline void clear_iniv();
  static const int kInIvFieldNumber = 3;
  inline const ::std::string& iniv() const;
  inline void set_iniv(const ::std::string& value);
  inline void set_iniv(const char* value);
  inline void set_iniv(const void* value, size_t size);
  inline ::std::string* mutable_iniv();
  inline ::std::string* release_iniv();
  inline void set_allocated_iniv(::std::string* iniv);

  // optional bytes outKey = 4;
  inline bool has_outkey() const;
  inline void clear_outkey();
  static const int kOutKeyFieldNumber = 4;
  inline const ::std::string& outkey() const;
  inline void set_outkey(const ::std::string& value);
  inline void set_outkey(const char* value);
  inline void set_outkey(const void* value, size_t size);
  inline ::std::string* mutable_outkey();
  inline ::std::string* release_outkey();
  inline void set_allocated_outkey(::std::string* outkey);

  // optional bytes outIv = 5;
  inline bool has_outiv() const;
  inline void clear_outiv();
  static const int kOutIvFieldNumber = 5;
  inline const ::std::string& outiv() const;
  inline void set_outiv(const ::std::string& value);
  inline void set_outiv(const char* value);
  inline void set_outiv(const void* value, size_t size);
  inline ::std::string* mutable_outiv();
  inline ::std::string* release_outiv();
  inline void set_allocated_outiv(::std::string* outiv);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CipherOptionProto)
 private:
  inline void set_has_suite();
  inline void clear_has_suite();
  inline void set_has_inkey();
  inline void clear_has_inkey();
  inline void set_has_iniv();
  inline void clear_has_iniv();
  inline void set_has_outkey();
  inline void clear_has_outkey();
  inline void set_has_outiv();
  inline void clear_has_outiv();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* inkey_;
  ::std::string* iniv_;
  ::std::string* outkey_;
  ::std::string* outiv_;
  int suite_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static CipherOptionProto* default_instance_;
};
// -------------------------------------------------------------------

class LocatedBlocksProto : public ::google::protobuf::Message {
 public:
  LocatedBlocksProto();
  virtual ~LocatedBlocksProto();

  LocatedBlocksProto(const LocatedBlocksProto& from);

  inline LocatedBlocksProto& operator=(const LocatedBlocksProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const LocatedBlocksProto& default_instance();

  void Swap(LocatedBlocksProto* other);

  // implements Message ----------------------------------------------

  LocatedBlocksProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const LocatedBlocksProto& from);
  void MergeFrom(const LocatedBlocksProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 fileLength = 1;
  inline bool has_filelength() const;
  inline void clear_filelength();
  static const int kFileLengthFieldNumber = 1;
  inline ::google::protobuf::uint64 filelength() const;
  inline void set_filelength(::google::protobuf::uint64 value);

  // repeated .hadoop.hdfs.LocatedBlockProto blocks = 2;
  inline int blocks_size() const;
  inline void clear_blocks();
  static const int kBlocksFieldNumber = 2;
  inline const ::hadoop::hdfs::LocatedBlockProto& blocks(int index) const;
  inline ::hadoop::hdfs::LocatedBlockProto* mutable_blocks(int index);
  inline ::hadoop::hdfs::LocatedBlockProto* add_blocks();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::LocatedBlockProto >&
      blocks() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::LocatedBlockProto >*
      mutable_blocks();

  // required bool underConstruction = 3;
  inline bool has_underconstruction() const;
  inline void clear_underconstruction();
  static const int kUnderConstructionFieldNumber = 3;
  inline bool underconstruction() const;
  inline void set_underconstruction(bool value);

  // optional .hadoop.hdfs.LocatedBlockProto lastBlock = 4;
  inline bool has_lastblock() const;
  inline void clear_lastblock();
  static const int kLastBlockFieldNumber = 4;
  inline const ::hadoop::hdfs::LocatedBlockProto& lastblock() const;
  inline ::hadoop::hdfs::LocatedBlockProto* mutable_lastblock();
  inline ::hadoop::hdfs::LocatedBlockProto* release_lastblock();
  inline void set_allocated_lastblock(::hadoop::hdfs::LocatedBlockProto* lastblock);

  // required bool isLastBlockComplete = 5;
  inline bool has_islastblockcomplete() const;
  inline void clear_islastblockcomplete();
  static const int kIsLastBlockCompleteFieldNumber = 5;
  inline bool islastblockcomplete() const;
  inline void set_islastblockcomplete(bool value);

  // optional .hadoop.hdfs.FileEncryptionInfoProto fileEncryptionInfo = 6;
  inline bool has_fileencryptioninfo() const;
  inline void clear_fileencryptioninfo();
  static const int kFileEncryptionInfoFieldNumber = 6;
  inline const ::hadoop::hdfs::FileEncryptionInfoProto& fileencryptioninfo() const;
  inline ::hadoop::hdfs::FileEncryptionInfoProto* mutable_fileencryptioninfo();
  inline ::hadoop::hdfs::FileEncryptionInfoProto* release_fileencryptioninfo();
  inline void set_allocated_fileencryptioninfo(::hadoop::hdfs::FileEncryptionInfoProto* fileencryptioninfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.LocatedBlocksProto)
 private:
  inline void set_has_filelength();
  inline void clear_has_filelength();
  inline void set_has_underconstruction();
  inline void clear_has_underconstruction();
  inline void set_has_lastblock();
  inline void clear_has_lastblock();
  inline void set_has_islastblockcomplete();
  inline void clear_has_islastblockcomplete();
  inline void set_has_fileencryptioninfo();
  inline void clear_has_fileencryptioninfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 filelength_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::LocatedBlockProto > blocks_;
  ::hadoop::hdfs::LocatedBlockProto* lastblock_;
  ::hadoop::hdfs::FileEncryptionInfoProto* fileencryptioninfo_;
  bool underconstruction_;
  bool islastblockcomplete_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static LocatedBlocksProto* default_instance_;
};
// -------------------------------------------------------------------

class HdfsFileStatusProto : public ::google::protobuf::Message {
 public:
  HdfsFileStatusProto();
  virtual ~HdfsFileStatusProto();

  HdfsFileStatusProto(const HdfsFileStatusProto& from);

  inline HdfsFileStatusProto& operator=(const HdfsFileStatusProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const HdfsFileStatusProto& default_instance();

  void Swap(HdfsFileStatusProto* other);

  // implements Message ----------------------------------------------

  HdfsFileStatusProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const HdfsFileStatusProto& from);
  void MergeFrom(const HdfsFileStatusProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef HdfsFileStatusProto_FileType FileType;
  static const FileType IS_DIR = HdfsFileStatusProto_FileType_IS_DIR;
  static const FileType IS_FILE = HdfsFileStatusProto_FileType_IS_FILE;
  static const FileType IS_SYMLINK = HdfsFileStatusProto_FileType_IS_SYMLINK;
  static inline bool FileType_IsValid(int value) {
    return HdfsFileStatusProto_FileType_IsValid(value);
  }
  static const FileType FileType_MIN =
    HdfsFileStatusProto_FileType_FileType_MIN;
  static const FileType FileType_MAX =
    HdfsFileStatusProto_FileType_FileType_MAX;
  static const int FileType_ARRAYSIZE =
    HdfsFileStatusProto_FileType_FileType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  FileType_descriptor() {
    return HdfsFileStatusProto_FileType_descriptor();
  }
  static inline const ::std::string& FileType_Name(FileType value) {
    return HdfsFileStatusProto_FileType_Name(value);
  }
  static inline bool FileType_Parse(const ::std::string& name,
      FileType* value) {
    return HdfsFileStatusProto_FileType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.HdfsFileStatusProto.FileType fileType = 1;
  inline bool has_filetype() const;
  inline void clear_filetype();
  static const int kFileTypeFieldNumber = 1;
  inline ::hadoop::hdfs::HdfsFileStatusProto_FileType filetype() const;
  inline void set_filetype(::hadoop::hdfs::HdfsFileStatusProto_FileType value);

  // required bytes path = 2;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 2;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const void* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // required uint64 length = 3;
  inline bool has_length() const;
  inline void clear_length();
  static const int kLengthFieldNumber = 3;
  inline ::google::protobuf::uint64 length() const;
  inline void set_length(::google::protobuf::uint64 value);

  // required .hadoop.hdfs.FsPermissionProto permission = 4;
  inline bool has_permission() const;
  inline void clear_permission();
  static const int kPermissionFieldNumber = 4;
  inline const ::hadoop::hdfs::FsPermissionProto& permission() const;
  inline ::hadoop::hdfs::FsPermissionProto* mutable_permission();
  inline ::hadoop::hdfs::FsPermissionProto* release_permission();
  inline void set_allocated_permission(::hadoop::hdfs::FsPermissionProto* permission);

  // required string owner = 5;
  inline bool has_owner() const;
  inline void clear_owner();
  static const int kOwnerFieldNumber = 5;
  inline const ::std::string& owner() const;
  inline void set_owner(const ::std::string& value);
  inline void set_owner(const char* value);
  inline void set_owner(const char* value, size_t size);
  inline ::std::string* mutable_owner();
  inline ::std::string* release_owner();
  inline void set_allocated_owner(::std::string* owner);

  // required string group = 6;
  inline bool has_group() const;
  inline void clear_group();
  static const int kGroupFieldNumber = 6;
  inline const ::std::string& group() const;
  inline void set_group(const ::std::string& value);
  inline void set_group(const char* value);
  inline void set_group(const char* value, size_t size);
  inline ::std::string* mutable_group();
  inline ::std::string* release_group();
  inline void set_allocated_group(::std::string* group);

  // required uint64 modification_time = 7;
  inline bool has_modification_time() const;
  inline void clear_modification_time();
  static const int kModificationTimeFieldNumber = 7;
  inline ::google::protobuf::uint64 modification_time() const;
  inline void set_modification_time(::google::protobuf::uint64 value);

  // required uint64 access_time = 8;
  inline bool has_access_time() const;
  inline void clear_access_time();
  static const int kAccessTimeFieldNumber = 8;
  inline ::google::protobuf::uint64 access_time() const;
  inline void set_access_time(::google::protobuf::uint64 value);

  // optional bytes symlink = 9;
  inline bool has_symlink() const;
  inline void clear_symlink();
  static const int kSymlinkFieldNumber = 9;
  inline const ::std::string& symlink() const;
  inline void set_symlink(const ::std::string& value);
  inline void set_symlink(const char* value);
  inline void set_symlink(const void* value, size_t size);
  inline ::std::string* mutable_symlink();
  inline ::std::string* release_symlink();
  inline void set_allocated_symlink(::std::string* symlink);

  // optional uint32 block_replication = 10 [default = 0];
  inline bool has_block_replication() const;
  inline void clear_block_replication();
  static const int kBlockReplicationFieldNumber = 10;
  inline ::google::protobuf::uint32 block_replication() const;
  inline void set_block_replication(::google::protobuf::uint32 value);

  // optional uint64 blocksize = 11 [default = 0];
  inline bool has_blocksize() const;
  inline void clear_blocksize();
  static const int kBlocksizeFieldNumber = 11;
  inline ::google::protobuf::uint64 blocksize() const;
  inline void set_blocksize(::google::protobuf::uint64 value);

  // optional .hadoop.hdfs.LocatedBlocksProto locations = 12;
  inline bool has_locations() const;
  inline void clear_locations();
  static const int kLocationsFieldNumber = 12;
  inline const ::hadoop::hdfs::LocatedBlocksProto& locations() const;
  inline ::hadoop::hdfs::LocatedBlocksProto* mutable_locations();
  inline ::hadoop::hdfs::LocatedBlocksProto* release_locations();
  inline void set_allocated_locations(::hadoop::hdfs::LocatedBlocksProto* locations);

  // optional uint64 fileId = 13 [default = 0];
  inline bool has_fileid() const;
  inline void clear_fileid();
  static const int kFileIdFieldNumber = 13;
  inline ::google::protobuf::uint64 fileid() const;
  inline void set_fileid(::google::protobuf::uint64 value);

  // optional int32 childrenNum = 14 [default = -1];
  inline bool has_childrennum() const;
  inline void clear_childrennum();
  static const int kChildrenNumFieldNumber = 14;
  inline ::google::protobuf::int32 childrennum() const;
  inline void set_childrennum(::google::protobuf::int32 value);

  // optional .hadoop.hdfs.FileEncryptionInfoProto fileEncryptionInfo = 15;
  inline bool has_fileencryptioninfo() const;
  inline void clear_fileencryptioninfo();
  static const int kFileEncryptionInfoFieldNumber = 15;
  inline const ::hadoop::hdfs::FileEncryptionInfoProto& fileencryptioninfo() const;
  inline ::hadoop::hdfs::FileEncryptionInfoProto* mutable_fileencryptioninfo();
  inline ::hadoop::hdfs::FileEncryptionInfoProto* release_fileencryptioninfo();
  inline void set_allocated_fileencryptioninfo(::hadoop::hdfs::FileEncryptionInfoProto* fileencryptioninfo);

  // optional uint32 storagePolicy = 16 [default = 0];
  inline bool has_storagepolicy() const;
  inline void clear_storagepolicy();
  static const int kStoragePolicyFieldNumber = 16;
  inline ::google::protobuf::uint32 storagepolicy() const;
  inline void set_storagepolicy(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.HdfsFileStatusProto)
 private:
  inline void set_has_filetype();
  inline void clear_has_filetype();
  inline void set_has_path();
  inline void clear_has_path();
  inline void set_has_length();
  inline void clear_has_length();
  inline void set_has_permission();
  inline void clear_has_permission();
  inline void set_has_owner();
  inline void clear_has_owner();
  inline void set_has_group();
  inline void clear_has_group();
  inline void set_has_modification_time();
  inline void clear_has_modification_time();
  inline void set_has_access_time();
  inline void clear_has_access_time();
  inline void set_has_symlink();
  inline void clear_has_symlink();
  inline void set_has_block_replication();
  inline void clear_has_block_replication();
  inline void set_has_blocksize();
  inline void clear_has_blocksize();
  inline void set_has_locations();
  inline void clear_has_locations();
  inline void set_has_fileid();
  inline void clear_has_fileid();
  inline void set_has_childrennum();
  inline void clear_has_childrennum();
  inline void set_has_fileencryptioninfo();
  inline void clear_has_fileencryptioninfo();
  inline void set_has_storagepolicy();
  inline void clear_has_storagepolicy();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* path_;
  ::google::protobuf::uint64 length_;
  ::hadoop::hdfs::FsPermissionProto* permission_;
  ::std::string* owner_;
  int filetype_;
  ::google::protobuf::uint32 block_replication_;
  ::std::string* group_;
  ::google::protobuf::uint64 modification_time_;
  ::google::protobuf::uint64 access_time_;
  ::std::string* symlink_;
  ::google::protobuf::uint64 blocksize_;
  ::hadoop::hdfs::LocatedBlocksProto* locations_;
  ::google::protobuf::uint64 fileid_;
  ::hadoop::hdfs::FileEncryptionInfoProto* fileencryptioninfo_;
  ::google::protobuf::int32 childrennum_;
  ::google::protobuf::uint32 storagepolicy_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(16 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static HdfsFileStatusProto* default_instance_;
};
// -------------------------------------------------------------------

class FsServerDefaultsProto : public ::google::protobuf::Message {
 public:
  FsServerDefaultsProto();
  virtual ~FsServerDefaultsProto();

  FsServerDefaultsProto(const FsServerDefaultsProto& from);

  inline FsServerDefaultsProto& operator=(const FsServerDefaultsProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FsServerDefaultsProto& default_instance();

  void Swap(FsServerDefaultsProto* other);

  // implements Message ----------------------------------------------

  FsServerDefaultsProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FsServerDefaultsProto& from);
  void MergeFrom(const FsServerDefaultsProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 blockSize = 1;
  inline bool has_blocksize() const;
  inline void clear_blocksize();
  static const int kBlockSizeFieldNumber = 1;
  inline ::google::protobuf::uint64 blocksize() const;
  inline void set_blocksize(::google::protobuf::uint64 value);

  // required uint32 bytesPerChecksum = 2;
  inline bool has_bytesperchecksum() const;
  inline void clear_bytesperchecksum();
  static const int kBytesPerChecksumFieldNumber = 2;
  inline ::google::protobuf::uint32 bytesperchecksum() const;
  inline void set_bytesperchecksum(::google::protobuf::uint32 value);

  // required uint32 writePacketSize = 3;
  inline bool has_writepacketsize() const;
  inline void clear_writepacketsize();
  static const int kWritePacketSizeFieldNumber = 3;
  inline ::google::protobuf::uint32 writepacketsize() const;
  inline void set_writepacketsize(::google::protobuf::uint32 value);

  // required uint32 replication = 4;
  inline bool has_replication() const;
  inline void clear_replication();
  static const int kReplicationFieldNumber = 4;
  inline ::google::protobuf::uint32 replication() const;
  inline void set_replication(::google::protobuf::uint32 value);

  // required uint32 fileBufferSize = 5;
  inline bool has_filebuffersize() const;
  inline void clear_filebuffersize();
  static const int kFileBufferSizeFieldNumber = 5;
  inline ::google::protobuf::uint32 filebuffersize() const;
  inline void set_filebuffersize(::google::protobuf::uint32 value);

  // optional bool encryptDataTransfer = 6 [default = false];
  inline bool has_encryptdatatransfer() const;
  inline void clear_encryptdatatransfer();
  static const int kEncryptDataTransferFieldNumber = 6;
  inline bool encryptdatatransfer() const;
  inline void set_encryptdatatransfer(bool value);

  // optional uint64 trashInterval = 7 [default = 0];
  inline bool has_trashinterval() const;
  inline void clear_trashinterval();
  static const int kTrashIntervalFieldNumber = 7;
  inline ::google::protobuf::uint64 trashinterval() const;
  inline void set_trashinterval(::google::protobuf::uint64 value);

  // optional .hadoop.hdfs.ChecksumTypeProto checksumType = 8 [default = CHECKSUM_CRC32];
  inline bool has_checksumtype() const;
  inline void clear_checksumtype();
  static const int kChecksumTypeFieldNumber = 8;
  inline ::hadoop::hdfs::ChecksumTypeProto checksumtype() const;
  inline void set_checksumtype(::hadoop::hdfs::ChecksumTypeProto value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.FsServerDefaultsProto)
 private:
  inline void set_has_blocksize();
  inline void clear_has_blocksize();
  inline void set_has_bytesperchecksum();
  inline void clear_has_bytesperchecksum();
  inline void set_has_writepacketsize();
  inline void clear_has_writepacketsize();
  inline void set_has_replication();
  inline void clear_has_replication();
  inline void set_has_filebuffersize();
  inline void clear_has_filebuffersize();
  inline void set_has_encryptdatatransfer();
  inline void clear_has_encryptdatatransfer();
  inline void set_has_trashinterval();
  inline void clear_has_trashinterval();
  inline void set_has_checksumtype();
  inline void clear_has_checksumtype();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 blocksize_;
  ::google::protobuf::uint32 bytesperchecksum_;
  ::google::protobuf::uint32 writepacketsize_;
  ::google::protobuf::uint32 replication_;
  ::google::protobuf::uint32 filebuffersize_;
  ::google::protobuf::uint64 trashinterval_;
  bool encryptdatatransfer_;
  int checksumtype_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(8 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static FsServerDefaultsProto* default_instance_;
};
// -------------------------------------------------------------------

class DirectoryListingProto : public ::google::protobuf::Message {
 public:
  DirectoryListingProto();
  virtual ~DirectoryListingProto();

  DirectoryListingProto(const DirectoryListingProto& from);

  inline DirectoryListingProto& operator=(const DirectoryListingProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DirectoryListingProto& default_instance();

  void Swap(DirectoryListingProto* other);

  // implements Message ----------------------------------------------

  DirectoryListingProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DirectoryListingProto& from);
  void MergeFrom(const DirectoryListingProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.HdfsFileStatusProto partialListing = 1;
  inline int partiallisting_size() const;
  inline void clear_partiallisting();
  static const int kPartialListingFieldNumber = 1;
  inline const ::hadoop::hdfs::HdfsFileStatusProto& partiallisting(int index) const;
  inline ::hadoop::hdfs::HdfsFileStatusProto* mutable_partiallisting(int index);
  inline ::hadoop::hdfs::HdfsFileStatusProto* add_partiallisting();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::HdfsFileStatusProto >&
      partiallisting() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::HdfsFileStatusProto >*
      mutable_partiallisting();

  // required uint32 remainingEntries = 2;
  inline bool has_remainingentries() const;
  inline void clear_remainingentries();
  static const int kRemainingEntriesFieldNumber = 2;
  inline ::google::protobuf::uint32 remainingentries() const;
  inline void set_remainingentries(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DirectoryListingProto)
 private:
  inline void set_has_remainingentries();
  inline void clear_has_remainingentries();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::HdfsFileStatusProto > partiallisting_;
  ::google::protobuf::uint32 remainingentries_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static DirectoryListingProto* default_instance_;
};
// -------------------------------------------------------------------

class SnapshottableDirectoryStatusProto : public ::google::protobuf::Message {
 public:
  SnapshottableDirectoryStatusProto();
  virtual ~SnapshottableDirectoryStatusProto();

  SnapshottableDirectoryStatusProto(const SnapshottableDirectoryStatusProto& from);

  inline SnapshottableDirectoryStatusProto& operator=(const SnapshottableDirectoryStatusProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshottableDirectoryStatusProto& default_instance();

  void Swap(SnapshottableDirectoryStatusProto* other);

  // implements Message ----------------------------------------------

  SnapshottableDirectoryStatusProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshottableDirectoryStatusProto& from);
  void MergeFrom(const SnapshottableDirectoryStatusProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.HdfsFileStatusProto dirStatus = 1;
  inline bool has_dirstatus() const;
  inline void clear_dirstatus();
  static const int kDirStatusFieldNumber = 1;
  inline const ::hadoop::hdfs::HdfsFileStatusProto& dirstatus() const;
  inline ::hadoop::hdfs::HdfsFileStatusProto* mutable_dirstatus();
  inline ::hadoop::hdfs::HdfsFileStatusProto* release_dirstatus();
  inline void set_allocated_dirstatus(::hadoop::hdfs::HdfsFileStatusProto* dirstatus);

  // required uint32 snapshot_quota = 2;
  inline bool has_snapshot_quota() const;
  inline void clear_snapshot_quota();
  static const int kSnapshotQuotaFieldNumber = 2;
  inline ::google::protobuf::uint32 snapshot_quota() const;
  inline void set_snapshot_quota(::google::protobuf::uint32 value);

  // required uint32 snapshot_number = 3;
  inline bool has_snapshot_number() const;
  inline void clear_snapshot_number();
  static const int kSnapshotNumberFieldNumber = 3;
  inline ::google::protobuf::uint32 snapshot_number() const;
  inline void set_snapshot_number(::google::protobuf::uint32 value);

  // required bytes parent_fullpath = 4;
  inline bool has_parent_fullpath() const;
  inline void clear_parent_fullpath();
  static const int kParentFullpathFieldNumber = 4;
  inline const ::std::string& parent_fullpath() const;
  inline void set_parent_fullpath(const ::std::string& value);
  inline void set_parent_fullpath(const char* value);
  inline void set_parent_fullpath(const void* value, size_t size);
  inline ::std::string* mutable_parent_fullpath();
  inline ::std::string* release_parent_fullpath();
  inline void set_allocated_parent_fullpath(::std::string* parent_fullpath);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SnapshottableDirectoryStatusProto)
 private:
  inline void set_has_dirstatus();
  inline void clear_has_dirstatus();
  inline void set_has_snapshot_quota();
  inline void clear_has_snapshot_quota();
  inline void set_has_snapshot_number();
  inline void clear_has_snapshot_number();
  inline void set_has_parent_fullpath();
  inline void clear_has_parent_fullpath();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::HdfsFileStatusProto* dirstatus_;
  ::google::protobuf::uint32 snapshot_quota_;
  ::google::protobuf::uint32 snapshot_number_;
  ::std::string* parent_fullpath_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static SnapshottableDirectoryStatusProto* default_instance_;
};
// -------------------------------------------------------------------

class SnapshottableDirectoryListingProto : public ::google::protobuf::Message {
 public:
  SnapshottableDirectoryListingProto();
  virtual ~SnapshottableDirectoryListingProto();

  SnapshottableDirectoryListingProto(const SnapshottableDirectoryListingProto& from);

  inline SnapshottableDirectoryListingProto& operator=(const SnapshottableDirectoryListingProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshottableDirectoryListingProto& default_instance();

  void Swap(SnapshottableDirectoryListingProto* other);

  // implements Message ----------------------------------------------

  SnapshottableDirectoryListingProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshottableDirectoryListingProto& from);
  void MergeFrom(const SnapshottableDirectoryListingProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.SnapshottableDirectoryStatusProto snapshottableDirListing = 1;
  inline int snapshottabledirlisting_size() const;
  inline void clear_snapshottabledirlisting();
  static const int kSnapshottableDirListingFieldNumber = 1;
  inline const ::hadoop::hdfs::SnapshottableDirectoryStatusProto& snapshottabledirlisting(int index) const;
  inline ::hadoop::hdfs::SnapshottableDirectoryStatusProto* mutable_snapshottabledirlisting(int index);
  inline ::hadoop::hdfs::SnapshottableDirectoryStatusProto* add_snapshottabledirlisting();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshottableDirectoryStatusProto >&
      snapshottabledirlisting() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshottableDirectoryStatusProto >*
      mutable_snapshottabledirlisting();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SnapshottableDirectoryListingProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshottableDirectoryStatusProto > snapshottabledirlisting_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static SnapshottableDirectoryListingProto* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffReportEntryProto : public ::google::protobuf::Message {
 public:
  SnapshotDiffReportEntryProto();
  virtual ~SnapshotDiffReportEntryProto();

  SnapshotDiffReportEntryProto(const SnapshotDiffReportEntryProto& from);

  inline SnapshotDiffReportEntryProto& operator=(const SnapshotDiffReportEntryProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffReportEntryProto& default_instance();

  void Swap(SnapshotDiffReportEntryProto* other);

  // implements Message ----------------------------------------------

  SnapshotDiffReportEntryProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffReportEntryProto& from);
  void MergeFrom(const SnapshotDiffReportEntryProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bytes fullpath = 1;
  inline bool has_fullpath() const;
  inline void clear_fullpath();
  static const int kFullpathFieldNumber = 1;
  inline const ::std::string& fullpath() const;
  inline void set_fullpath(const ::std::string& value);
  inline void set_fullpath(const char* value);
  inline void set_fullpath(const void* value, size_t size);
  inline ::std::string* mutable_fullpath();
  inline ::std::string* release_fullpath();
  inline void set_allocated_fullpath(::std::string* fullpath);

  // required string modificationLabel = 2;
  inline bool has_modificationlabel() const;
  inline void clear_modificationlabel();
  static const int kModificationLabelFieldNumber = 2;
  inline const ::std::string& modificationlabel() const;
  inline void set_modificationlabel(const ::std::string& value);
  inline void set_modificationlabel(const char* value);
  inline void set_modificationlabel(const char* value, size_t size);
  inline ::std::string* mutable_modificationlabel();
  inline ::std::string* release_modificationlabel();
  inline void set_allocated_modificationlabel(::std::string* modificationlabel);

  // optional bytes targetPath = 3;
  inline bool has_targetpath() const;
  inline void clear_targetpath();
  static const int kTargetPathFieldNumber = 3;
  inline const ::std::string& targetpath() const;
  inline void set_targetpath(const ::std::string& value);
  inline void set_targetpath(const char* value);
  inline void set_targetpath(const void* value, size_t size);
  inline ::std::string* mutable_targetpath();
  inline ::std::string* release_targetpath();
  inline void set_allocated_targetpath(::std::string* targetpath);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SnapshotDiffReportEntryProto)
 private:
  inline void set_has_fullpath();
  inline void clear_has_fullpath();
  inline void set_has_modificationlabel();
  inline void clear_has_modificationlabel();
  inline void set_has_targetpath();
  inline void clear_has_targetpath();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* fullpath_;
  ::std::string* modificationlabel_;
  ::std::string* targetpath_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffReportEntryProto* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffReportProto : public ::google::protobuf::Message {
 public:
  SnapshotDiffReportProto();
  virtual ~SnapshotDiffReportProto();

  SnapshotDiffReportProto(const SnapshotDiffReportProto& from);

  inline SnapshotDiffReportProto& operator=(const SnapshotDiffReportProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffReportProto& default_instance();

  void Swap(SnapshotDiffReportProto* other);

  // implements Message ----------------------------------------------

  SnapshotDiffReportProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffReportProto& from);
  void MergeFrom(const SnapshotDiffReportProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string snapshotRoot = 1;
  inline bool has_snapshotroot() const;
  inline void clear_snapshotroot();
  static const int kSnapshotRootFieldNumber = 1;
  inline const ::std::string& snapshotroot() const;
  inline void set_snapshotroot(const ::std::string& value);
  inline void set_snapshotroot(const char* value);
  inline void set_snapshotroot(const char* value, size_t size);
  inline ::std::string* mutable_snapshotroot();
  inline ::std::string* release_snapshotroot();
  inline void set_allocated_snapshotroot(::std::string* snapshotroot);

  // required string fromSnapshot = 2;
  inline bool has_fromsnapshot() const;
  inline void clear_fromsnapshot();
  static const int kFromSnapshotFieldNumber = 2;
  inline const ::std::string& fromsnapshot() const;
  inline void set_fromsnapshot(const ::std::string& value);
  inline void set_fromsnapshot(const char* value);
  inline void set_fromsnapshot(const char* value, size_t size);
  inline ::std::string* mutable_fromsnapshot();
  inline ::std::string* release_fromsnapshot();
  inline void set_allocated_fromsnapshot(::std::string* fromsnapshot);

  // required string toSnapshot = 3;
  inline bool has_tosnapshot() const;
  inline void clear_tosnapshot();
  static const int kToSnapshotFieldNumber = 3;
  inline const ::std::string& tosnapshot() const;
  inline void set_tosnapshot(const ::std::string& value);
  inline void set_tosnapshot(const char* value);
  inline void set_tosnapshot(const char* value, size_t size);
  inline ::std::string* mutable_tosnapshot();
  inline ::std::string* release_tosnapshot();
  inline void set_allocated_tosnapshot(::std::string* tosnapshot);

  // repeated .hadoop.hdfs.SnapshotDiffReportEntryProto diffReportEntries = 4;
  inline int diffreportentries_size() const;
  inline void clear_diffreportentries();
  static const int kDiffReportEntriesFieldNumber = 4;
  inline const ::hadoop::hdfs::SnapshotDiffReportEntryProto& diffreportentries(int index) const;
  inline ::hadoop::hdfs::SnapshotDiffReportEntryProto* mutable_diffreportentries(int index);
  inline ::hadoop::hdfs::SnapshotDiffReportEntryProto* add_diffreportentries();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshotDiffReportEntryProto >&
      diffreportentries() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshotDiffReportEntryProto >*
      mutable_diffreportentries();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SnapshotDiffReportProto)
 private:
  inline void set_has_snapshotroot();
  inline void clear_has_snapshotroot();
  inline void set_has_fromsnapshot();
  inline void clear_has_fromsnapshot();
  inline void set_has_tosnapshot();
  inline void clear_has_tosnapshot();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* snapshotroot_;
  ::std::string* fromsnapshot_;
  ::std::string* tosnapshot_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshotDiffReportEntryProto > diffreportentries_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffReportProto* default_instance_;
};
// -------------------------------------------------------------------

class StorageInfoProto : public ::google::protobuf::Message {
 public:
  StorageInfoProto();
  virtual ~StorageInfoProto();

  StorageInfoProto(const StorageInfoProto& from);

  inline StorageInfoProto& operator=(const StorageInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StorageInfoProto& default_instance();

  void Swap(StorageInfoProto* other);

  // implements Message ----------------------------------------------

  StorageInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StorageInfoProto& from);
  void MergeFrom(const StorageInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 layoutVersion = 1;
  inline bool has_layoutversion() const;
  inline void clear_layoutversion();
  static const int kLayoutVersionFieldNumber = 1;
  inline ::google::protobuf::uint32 layoutversion() const;
  inline void set_layoutversion(::google::protobuf::uint32 value);

  // required uint32 namespceID = 2;
  inline bool has_namespceid() const;
  inline void clear_namespceid();
  static const int kNamespceIDFieldNumber = 2;
  inline ::google::protobuf::uint32 namespceid() const;
  inline void set_namespceid(::google::protobuf::uint32 value);

  // required string clusterID = 3;
  inline bool has_clusterid() const;
  inline void clear_clusterid();
  static const int kClusterIDFieldNumber = 3;
  inline const ::std::string& clusterid() const;
  inline void set_clusterid(const ::std::string& value);
  inline void set_clusterid(const char* value);
  inline void set_clusterid(const char* value, size_t size);
  inline ::std::string* mutable_clusterid();
  inline ::std::string* release_clusterid();
  inline void set_allocated_clusterid(::std::string* clusterid);

  // required uint64 cTime = 4;
  inline bool has_ctime() const;
  inline void clear_ctime();
  static const int kCTimeFieldNumber = 4;
  inline ::google::protobuf::uint64 ctime() const;
  inline void set_ctime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.StorageInfoProto)
 private:
  inline void set_has_layoutversion();
  inline void clear_has_layoutversion();
  inline void set_has_namespceid();
  inline void clear_has_namespceid();
  inline void set_has_clusterid();
  inline void clear_has_clusterid();
  inline void set_has_ctime();
  inline void clear_has_ctime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 layoutversion_;
  ::google::protobuf::uint32 namespceid_;
  ::std::string* clusterid_;
  ::google::protobuf::uint64 ctime_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static StorageInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class NamenodeRegistrationProto : public ::google::protobuf::Message {
 public:
  NamenodeRegistrationProto();
  virtual ~NamenodeRegistrationProto();

  NamenodeRegistrationProto(const NamenodeRegistrationProto& from);

  inline NamenodeRegistrationProto& operator=(const NamenodeRegistrationProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NamenodeRegistrationProto& default_instance();

  void Swap(NamenodeRegistrationProto* other);

  // implements Message ----------------------------------------------

  NamenodeRegistrationProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NamenodeRegistrationProto& from);
  void MergeFrom(const NamenodeRegistrationProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef NamenodeRegistrationProto_NamenodeRoleProto NamenodeRoleProto;
  static const NamenodeRoleProto NAMENODE = NamenodeRegistrationProto_NamenodeRoleProto_NAMENODE;
  static const NamenodeRoleProto BACKUP = NamenodeRegistrationProto_NamenodeRoleProto_BACKUP;
  static const NamenodeRoleProto CHECKPOINT = NamenodeRegistrationProto_NamenodeRoleProto_CHECKPOINT;
  static inline bool NamenodeRoleProto_IsValid(int value) {
    return NamenodeRegistrationProto_NamenodeRoleProto_IsValid(value);
  }
  static const NamenodeRoleProto NamenodeRoleProto_MIN =
    NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_MIN;
  static const NamenodeRoleProto NamenodeRoleProto_MAX =
    NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_MAX;
  static const int NamenodeRoleProto_ARRAYSIZE =
    NamenodeRegistrationProto_NamenodeRoleProto_NamenodeRoleProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NamenodeRoleProto_descriptor() {
    return NamenodeRegistrationProto_NamenodeRoleProto_descriptor();
  }
  static inline const ::std::string& NamenodeRoleProto_Name(NamenodeRoleProto value) {
    return NamenodeRegistrationProto_NamenodeRoleProto_Name(value);
  }
  static inline bool NamenodeRoleProto_Parse(const ::std::string& name,
      NamenodeRoleProto* value) {
    return NamenodeRegistrationProto_NamenodeRoleProto_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required string rpcAddress = 1;
  inline bool has_rpcaddress() const;
  inline void clear_rpcaddress();
  static const int kRpcAddressFieldNumber = 1;
  inline const ::std::string& rpcaddress() const;
  inline void set_rpcaddress(const ::std::string& value);
  inline void set_rpcaddress(const char* value);
  inline void set_rpcaddress(const char* value, size_t size);
  inline ::std::string* mutable_rpcaddress();
  inline ::std::string* release_rpcaddress();
  inline void set_allocated_rpcaddress(::std::string* rpcaddress);

  // required string httpAddress = 2;
  inline bool has_httpaddress() const;
  inline void clear_httpaddress();
  static const int kHttpAddressFieldNumber = 2;
  inline const ::std::string& httpaddress() const;
  inline void set_httpaddress(const ::std::string& value);
  inline void set_httpaddress(const char* value);
  inline void set_httpaddress(const char* value, size_t size);
  inline ::std::string* mutable_httpaddress();
  inline ::std::string* release_httpaddress();
  inline void set_allocated_httpaddress(::std::string* httpaddress);

  // required .hadoop.hdfs.StorageInfoProto storageInfo = 3;
  inline bool has_storageinfo() const;
  inline void clear_storageinfo();
  static const int kStorageInfoFieldNumber = 3;
  inline const ::hadoop::hdfs::StorageInfoProto& storageinfo() const;
  inline ::hadoop::hdfs::StorageInfoProto* mutable_storageinfo();
  inline ::hadoop::hdfs::StorageInfoProto* release_storageinfo();
  inline void set_allocated_storageinfo(::hadoop::hdfs::StorageInfoProto* storageinfo);

  // optional .hadoop.hdfs.NamenodeRegistrationProto.NamenodeRoleProto role = 4 [default = NAMENODE];
  inline bool has_role() const;
  inline void clear_role();
  static const int kRoleFieldNumber = 4;
  inline ::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto role() const;
  inline void set_role(::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.NamenodeRegistrationProto)
 private:
  inline void set_has_rpcaddress();
  inline void clear_has_rpcaddress();
  inline void set_has_httpaddress();
  inline void clear_has_httpaddress();
  inline void set_has_storageinfo();
  inline void clear_has_storageinfo();
  inline void set_has_role();
  inline void clear_has_role();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* rpcaddress_;
  ::std::string* httpaddress_;
  ::hadoop::hdfs::StorageInfoProto* storageinfo_;
  int role_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static NamenodeRegistrationProto* default_instance_;
};
// -------------------------------------------------------------------

class CheckpointSignatureProto : public ::google::protobuf::Message {
 public:
  CheckpointSignatureProto();
  virtual ~CheckpointSignatureProto();

  CheckpointSignatureProto(const CheckpointSignatureProto& from);

  inline CheckpointSignatureProto& operator=(const CheckpointSignatureProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CheckpointSignatureProto& default_instance();

  void Swap(CheckpointSignatureProto* other);

  // implements Message ----------------------------------------------

  CheckpointSignatureProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CheckpointSignatureProto& from);
  void MergeFrom(const CheckpointSignatureProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string blockPoolId = 1;
  inline bool has_blockpoolid() const;
  inline void clear_blockpoolid();
  static const int kBlockPoolIdFieldNumber = 1;
  inline const ::std::string& blockpoolid() const;
  inline void set_blockpoolid(const ::std::string& value);
  inline void set_blockpoolid(const char* value);
  inline void set_blockpoolid(const char* value, size_t size);
  inline ::std::string* mutable_blockpoolid();
  inline ::std::string* release_blockpoolid();
  inline void set_allocated_blockpoolid(::std::string* blockpoolid);

  // required uint64 mostRecentCheckpointTxId = 2;
  inline bool has_mostrecentcheckpointtxid() const;
  inline void clear_mostrecentcheckpointtxid();
  static const int kMostRecentCheckpointTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 mostrecentcheckpointtxid() const;
  inline void set_mostrecentcheckpointtxid(::google::protobuf::uint64 value);

  // required uint64 curSegmentTxId = 3;
  inline bool has_cursegmenttxid() const;
  inline void clear_cursegmenttxid();
  static const int kCurSegmentTxIdFieldNumber = 3;
  inline ::google::protobuf::uint64 cursegmenttxid() const;
  inline void set_cursegmenttxid(::google::protobuf::uint64 value);

  // required .hadoop.hdfs.StorageInfoProto storageInfo = 4;
  inline bool has_storageinfo() const;
  inline void clear_storageinfo();
  static const int kStorageInfoFieldNumber = 4;
  inline const ::hadoop::hdfs::StorageInfoProto& storageinfo() const;
  inline ::hadoop::hdfs::StorageInfoProto* mutable_storageinfo();
  inline ::hadoop::hdfs::StorageInfoProto* release_storageinfo();
  inline void set_allocated_storageinfo(::hadoop::hdfs::StorageInfoProto* storageinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CheckpointSignatureProto)
 private:
  inline void set_has_blockpoolid();
  inline void clear_has_blockpoolid();
  inline void set_has_mostrecentcheckpointtxid();
  inline void clear_has_mostrecentcheckpointtxid();
  inline void set_has_cursegmenttxid();
  inline void clear_has_cursegmenttxid();
  inline void set_has_storageinfo();
  inline void clear_has_storageinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* blockpoolid_;
  ::google::protobuf::uint64 mostrecentcheckpointtxid_;
  ::google::protobuf::uint64 cursegmenttxid_;
  ::hadoop::hdfs::StorageInfoProto* storageinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static CheckpointSignatureProto* default_instance_;
};
// -------------------------------------------------------------------

class NamenodeCommandProto : public ::google::protobuf::Message {
 public:
  NamenodeCommandProto();
  virtual ~NamenodeCommandProto();

  NamenodeCommandProto(const NamenodeCommandProto& from);

  inline NamenodeCommandProto& operator=(const NamenodeCommandProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NamenodeCommandProto& default_instance();

  void Swap(NamenodeCommandProto* other);

  // implements Message ----------------------------------------------

  NamenodeCommandProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NamenodeCommandProto& from);
  void MergeFrom(const NamenodeCommandProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef NamenodeCommandProto_Type Type;
  static const Type NamenodeCommand = NamenodeCommandProto_Type_NamenodeCommand;
  static const Type CheckPointCommand = NamenodeCommandProto_Type_CheckPointCommand;
  static inline bool Type_IsValid(int value) {
    return NamenodeCommandProto_Type_IsValid(value);
  }
  static const Type Type_MIN =
    NamenodeCommandProto_Type_Type_MIN;
  static const Type Type_MAX =
    NamenodeCommandProto_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    NamenodeCommandProto_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return NamenodeCommandProto_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return NamenodeCommandProto_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return NamenodeCommandProto_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required uint32 action = 1;
  inline bool has_action() const;
  inline void clear_action();
  static const int kActionFieldNumber = 1;
  inline ::google::protobuf::uint32 action() const;
  inline void set_action(::google::protobuf::uint32 value);

  // required .hadoop.hdfs.NamenodeCommandProto.Type type = 2;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 2;
  inline ::hadoop::hdfs::NamenodeCommandProto_Type type() const;
  inline void set_type(::hadoop::hdfs::NamenodeCommandProto_Type value);

  // optional .hadoop.hdfs.CheckpointCommandProto checkpointCmd = 3;
  inline bool has_checkpointcmd() const;
  inline void clear_checkpointcmd();
  static const int kCheckpointCmdFieldNumber = 3;
  inline const ::hadoop::hdfs::CheckpointCommandProto& checkpointcmd() const;
  inline ::hadoop::hdfs::CheckpointCommandProto* mutable_checkpointcmd();
  inline ::hadoop::hdfs::CheckpointCommandProto* release_checkpointcmd();
  inline void set_allocated_checkpointcmd(::hadoop::hdfs::CheckpointCommandProto* checkpointcmd);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.NamenodeCommandProto)
 private:
  inline void set_has_action();
  inline void clear_has_action();
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_checkpointcmd();
  inline void clear_has_checkpointcmd();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 action_;
  int type_;
  ::hadoop::hdfs::CheckpointCommandProto* checkpointcmd_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static NamenodeCommandProto* default_instance_;
};
// -------------------------------------------------------------------

class CheckpointCommandProto : public ::google::protobuf::Message {
 public:
  CheckpointCommandProto();
  virtual ~CheckpointCommandProto();

  CheckpointCommandProto(const CheckpointCommandProto& from);

  inline CheckpointCommandProto& operator=(const CheckpointCommandProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CheckpointCommandProto& default_instance();

  void Swap(CheckpointCommandProto* other);

  // implements Message ----------------------------------------------

  CheckpointCommandProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CheckpointCommandProto& from);
  void MergeFrom(const CheckpointCommandProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
  inline bool has_signature() const;
  inline void clear_signature();
  static const int kSignatureFieldNumber = 1;
  inline const ::hadoop::hdfs::CheckpointSignatureProto& signature() const;
  inline ::hadoop::hdfs::CheckpointSignatureProto* mutable_signature();
  inline ::hadoop::hdfs::CheckpointSignatureProto* release_signature();
  inline void set_allocated_signature(::hadoop::hdfs::CheckpointSignatureProto* signature);

  // required bool needToReturnImage = 2;
  inline bool has_needtoreturnimage() const;
  inline void clear_needtoreturnimage();
  static const int kNeedToReturnImageFieldNumber = 2;
  inline bool needtoreturnimage() const;
  inline void set_needtoreturnimage(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CheckpointCommandProto)
 private:
  inline void set_has_signature();
  inline void clear_has_signature();
  inline void set_has_needtoreturnimage();
  inline void clear_has_needtoreturnimage();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::CheckpointSignatureProto* signature_;
  bool needtoreturnimage_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static CheckpointCommandProto* default_instance_;
};
// -------------------------------------------------------------------

class BlockProto : public ::google::protobuf::Message {
 public:
  BlockProto();
  virtual ~BlockProto();

  BlockProto(const BlockProto& from);

  inline BlockProto& operator=(const BlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BlockProto& default_instance();

  void Swap(BlockProto* other);

  // implements Message ----------------------------------------------

  BlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BlockProto& from);
  void MergeFrom(const BlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 blockId = 1;
  inline bool has_blockid() const;
  inline void clear_blockid();
  static const int kBlockIdFieldNumber = 1;
  inline ::google::protobuf::uint64 blockid() const;
  inline void set_blockid(::google::protobuf::uint64 value);

  // required uint64 genStamp = 2;
  inline bool has_genstamp() const;
  inline void clear_genstamp();
  static const int kGenStampFieldNumber = 2;
  inline ::google::protobuf::uint64 genstamp() const;
  inline void set_genstamp(::google::protobuf::uint64 value);

  // optional uint64 numBytes = 3 [default = 0];
  inline bool has_numbytes() const;
  inline void clear_numbytes();
  static const int kNumBytesFieldNumber = 3;
  inline ::google::protobuf::uint64 numbytes() const;
  inline void set_numbytes(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BlockProto)
 private:
  inline void set_has_blockid();
  inline void clear_has_blockid();
  inline void set_has_genstamp();
  inline void clear_has_genstamp();
  inline void set_has_numbytes();
  inline void clear_has_numbytes();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 blockid_;
  ::google::protobuf::uint64 genstamp_;
  ::google::protobuf::uint64 numbytes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static BlockProto* default_instance_;
};
// -------------------------------------------------------------------

class BlockWithLocationsProto : public ::google::protobuf::Message {
 public:
  BlockWithLocationsProto();
  virtual ~BlockWithLocationsProto();

  BlockWithLocationsProto(const BlockWithLocationsProto& from);

  inline BlockWithLocationsProto& operator=(const BlockWithLocationsProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BlockWithLocationsProto& default_instance();

  void Swap(BlockWithLocationsProto* other);

  // implements Message ----------------------------------------------

  BlockWithLocationsProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BlockWithLocationsProto& from);
  void MergeFrom(const BlockWithLocationsProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BlockProto block = 1;
  inline bool has_block() const;
  inline void clear_block();
  static const int kBlockFieldNumber = 1;
  inline const ::hadoop::hdfs::BlockProto& block() const;
  inline ::hadoop::hdfs::BlockProto* mutable_block();
  inline ::hadoop::hdfs::BlockProto* release_block();
  inline void set_allocated_block(::hadoop::hdfs::BlockProto* block);

  // repeated string datanodeUuids = 2;
  inline int datanodeuuids_size() const;
  inline void clear_datanodeuuids();
  static const int kDatanodeUuidsFieldNumber = 2;
  inline const ::std::string& datanodeuuids(int index) const;
  inline ::std::string* mutable_datanodeuuids(int index);
  inline void set_datanodeuuids(int index, const ::std::string& value);
  inline void set_datanodeuuids(int index, const char* value);
  inline void set_datanodeuuids(int index, const char* value, size_t size);
  inline ::std::string* add_datanodeuuids();
  inline void add_datanodeuuids(const ::std::string& value);
  inline void add_datanodeuuids(const char* value);
  inline void add_datanodeuuids(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& datanodeuuids() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_datanodeuuids();

  // repeated string storageUuids = 3;
  inline int storageuuids_size() const;
  inline void clear_storageuuids();
  static const int kStorageUuidsFieldNumber = 3;
  inline const ::std::string& storageuuids(int index) const;
  inline ::std::string* mutable_storageuuids(int index);
  inline void set_storageuuids(int index, const ::std::string& value);
  inline void set_storageuuids(int index, const char* value);
  inline void set_storageuuids(int index, const char* value, size_t size);
  inline ::std::string* add_storageuuids();
  inline void add_storageuuids(const ::std::string& value);
  inline void add_storageuuids(const char* value);
  inline void add_storageuuids(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& storageuuids() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_storageuuids();

  // repeated .hadoop.hdfs.StorageTypeProto storageTypes = 4;
  inline int storagetypes_size() const;
  inline void clear_storagetypes();
  static const int kStorageTypesFieldNumber = 4;
  inline ::hadoop::hdfs::StorageTypeProto storagetypes(int index) const;
  inline void set_storagetypes(int index, ::hadoop::hdfs::StorageTypeProto value);
  inline void add_storagetypes(::hadoop::hdfs::StorageTypeProto value);
  inline const ::google::protobuf::RepeatedField<int>& storagetypes() const;
  inline ::google::protobuf::RepeatedField<int>* mutable_storagetypes();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BlockWithLocationsProto)
 private:
  inline void set_has_block();
  inline void clear_has_block();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BlockProto* block_;
  ::google::protobuf::RepeatedPtrField< ::std::string> datanodeuuids_;
  ::google::protobuf::RepeatedPtrField< ::std::string> storageuuids_;
  ::google::protobuf::RepeatedField<int> storagetypes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static BlockWithLocationsProto* default_instance_;
};
// -------------------------------------------------------------------

class BlocksWithLocationsProto : public ::google::protobuf::Message {
 public:
  BlocksWithLocationsProto();
  virtual ~BlocksWithLocationsProto();

  BlocksWithLocationsProto(const BlocksWithLocationsProto& from);

  inline BlocksWithLocationsProto& operator=(const BlocksWithLocationsProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BlocksWithLocationsProto& default_instance();

  void Swap(BlocksWithLocationsProto* other);

  // implements Message ----------------------------------------------

  BlocksWithLocationsProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BlocksWithLocationsProto& from);
  void MergeFrom(const BlocksWithLocationsProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.BlockWithLocationsProto blocks = 1;
  inline int blocks_size() const;
  inline void clear_blocks();
  static const int kBlocksFieldNumber = 1;
  inline const ::hadoop::hdfs::BlockWithLocationsProto& blocks(int index) const;
  inline ::hadoop::hdfs::BlockWithLocationsProto* mutable_blocks(int index);
  inline ::hadoop::hdfs::BlockWithLocationsProto* add_blocks();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockWithLocationsProto >&
      blocks() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockWithLocationsProto >*
      mutable_blocks();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BlocksWithLocationsProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockWithLocationsProto > blocks_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static BlocksWithLocationsProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoteEditLogProto : public ::google::protobuf::Message {
 public:
  RemoteEditLogProto();
  virtual ~RemoteEditLogProto();

  RemoteEditLogProto(const RemoteEditLogProto& from);

  inline RemoteEditLogProto& operator=(const RemoteEditLogProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoteEditLogProto& default_instance();

  void Swap(RemoteEditLogProto* other);

  // implements Message ----------------------------------------------

  RemoteEditLogProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoteEditLogProto& from);
  void MergeFrom(const RemoteEditLogProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 startTxId = 1;
  inline bool has_starttxid() const;
  inline void clear_starttxid();
  static const int kStartTxIdFieldNumber = 1;
  inline ::google::protobuf::uint64 starttxid() const;
  inline void set_starttxid(::google::protobuf::uint64 value);

  // required uint64 endTxId = 2;
  inline bool has_endtxid() const;
  inline void clear_endtxid();
  static const int kEndTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 endtxid() const;
  inline void set_endtxid(::google::protobuf::uint64 value);

  // optional bool isInProgress = 3 [default = false];
  inline bool has_isinprogress() const;
  inline void clear_isinprogress();
  static const int kIsInProgressFieldNumber = 3;
  inline bool isinprogress() const;
  inline void set_isinprogress(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoteEditLogProto)
 private:
  inline void set_has_starttxid();
  inline void clear_has_starttxid();
  inline void set_has_endtxid();
  inline void clear_has_endtxid();
  inline void set_has_isinprogress();
  inline void clear_has_isinprogress();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 starttxid_;
  ::google::protobuf::uint64 endtxid_;
  bool isinprogress_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static RemoteEditLogProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoteEditLogManifestProto : public ::google::protobuf::Message {
 public:
  RemoteEditLogManifestProto();
  virtual ~RemoteEditLogManifestProto();

  RemoteEditLogManifestProto(const RemoteEditLogManifestProto& from);

  inline RemoteEditLogManifestProto& operator=(const RemoteEditLogManifestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoteEditLogManifestProto& default_instance();

  void Swap(RemoteEditLogManifestProto* other);

  // implements Message ----------------------------------------------

  RemoteEditLogManifestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoteEditLogManifestProto& from);
  void MergeFrom(const RemoteEditLogManifestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.RemoteEditLogProto logs = 1;
  inline int logs_size() const;
  inline void clear_logs();
  static const int kLogsFieldNumber = 1;
  inline const ::hadoop::hdfs::RemoteEditLogProto& logs(int index) const;
  inline ::hadoop::hdfs::RemoteEditLogProto* mutable_logs(int index);
  inline ::hadoop::hdfs::RemoteEditLogProto* add_logs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::RemoteEditLogProto >&
      logs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::RemoteEditLogProto >*
      mutable_logs();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoteEditLogManifestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::RemoteEditLogProto > logs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static RemoteEditLogManifestProto* default_instance_;
};
// -------------------------------------------------------------------

class NamespaceInfoProto : public ::google::protobuf::Message {
 public:
  NamespaceInfoProto();
  virtual ~NamespaceInfoProto();

  NamespaceInfoProto(const NamespaceInfoProto& from);

  inline NamespaceInfoProto& operator=(const NamespaceInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NamespaceInfoProto& default_instance();

  void Swap(NamespaceInfoProto* other);

  // implements Message ----------------------------------------------

  NamespaceInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NamespaceInfoProto& from);
  void MergeFrom(const NamespaceInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string buildVersion = 1;
  inline bool has_buildversion() const;
  inline void clear_buildversion();
  static const int kBuildVersionFieldNumber = 1;
  inline const ::std::string& buildversion() const;
  inline void set_buildversion(const ::std::string& value);
  inline void set_buildversion(const char* value);
  inline void set_buildversion(const char* value, size_t size);
  inline ::std::string* mutable_buildversion();
  inline ::std::string* release_buildversion();
  inline void set_allocated_buildversion(::std::string* buildversion);

  // required uint32 unused = 2;
  inline bool has_unused() const;
  inline void clear_unused();
  static const int kUnusedFieldNumber = 2;
  inline ::google::protobuf::uint32 unused() const;
  inline void set_unused(::google::protobuf::uint32 value);

  // required string blockPoolID = 3;
  inline bool has_blockpoolid() const;
  inline void clear_blockpoolid();
  static const int kBlockPoolIDFieldNumber = 3;
  inline const ::std::string& blockpoolid() const;
  inline void set_blockpoolid(const ::std::string& value);
  inline void set_blockpoolid(const char* value);
  inline void set_blockpoolid(const char* value, size_t size);
  inline ::std::string* mutable_blockpoolid();
  inline ::std::string* release_blockpoolid();
  inline void set_allocated_blockpoolid(::std::string* blockpoolid);

  // required .hadoop.hdfs.StorageInfoProto storageInfo = 4;
  inline bool has_storageinfo() const;
  inline void clear_storageinfo();
  static const int kStorageInfoFieldNumber = 4;
  inline const ::hadoop::hdfs::StorageInfoProto& storageinfo() const;
  inline ::hadoop::hdfs::StorageInfoProto* mutable_storageinfo();
  inline ::hadoop::hdfs::StorageInfoProto* release_storageinfo();
  inline void set_allocated_storageinfo(::hadoop::hdfs::StorageInfoProto* storageinfo);

  // required string softwareVersion = 5;
  inline bool has_softwareversion() const;
  inline void clear_softwareversion();
  static const int kSoftwareVersionFieldNumber = 5;
  inline const ::std::string& softwareversion() const;
  inline void set_softwareversion(const ::std::string& value);
  inline void set_softwareversion(const char* value);
  inline void set_softwareversion(const char* value, size_t size);
  inline ::std::string* mutable_softwareversion();
  inline ::std::string* release_softwareversion();
  inline void set_allocated_softwareversion(::std::string* softwareversion);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.NamespaceInfoProto)
 private:
  inline void set_has_buildversion();
  inline void clear_has_buildversion();
  inline void set_has_unused();
  inline void clear_has_unused();
  inline void set_has_blockpoolid();
  inline void clear_has_blockpoolid();
  inline void set_has_storageinfo();
  inline void clear_has_storageinfo();
  inline void set_has_softwareversion();
  inline void clear_has_softwareversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* buildversion_;
  ::std::string* blockpoolid_;
  ::hadoop::hdfs::StorageInfoProto* storageinfo_;
  ::std::string* softwareversion_;
  ::google::protobuf::uint32 unused_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static NamespaceInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class BlockKeyProto : public ::google::protobuf::Message {
 public:
  BlockKeyProto();
  virtual ~BlockKeyProto();

  BlockKeyProto(const BlockKeyProto& from);

  inline BlockKeyProto& operator=(const BlockKeyProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BlockKeyProto& default_instance();

  void Swap(BlockKeyProto* other);

  // implements Message ----------------------------------------------

  BlockKeyProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BlockKeyProto& from);
  void MergeFrom(const BlockKeyProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 keyId = 1;
  inline bool has_keyid() const;
  inline void clear_keyid();
  static const int kKeyIdFieldNumber = 1;
  inline ::google::protobuf::uint32 keyid() const;
  inline void set_keyid(::google::protobuf::uint32 value);

  // required uint64 expiryDate = 2;
  inline bool has_expirydate() const;
  inline void clear_expirydate();
  static const int kExpiryDateFieldNumber = 2;
  inline ::google::protobuf::uint64 expirydate() const;
  inline void set_expirydate(::google::protobuf::uint64 value);

  // optional bytes keyBytes = 3;
  inline bool has_keybytes() const;
  inline void clear_keybytes();
  static const int kKeyBytesFieldNumber = 3;
  inline const ::std::string& keybytes() const;
  inline void set_keybytes(const ::std::string& value);
  inline void set_keybytes(const char* value);
  inline void set_keybytes(const void* value, size_t size);
  inline ::std::string* mutable_keybytes();
  inline ::std::string* release_keybytes();
  inline void set_allocated_keybytes(::std::string* keybytes);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BlockKeyProto)
 private:
  inline void set_has_keyid();
  inline void clear_has_keyid();
  inline void set_has_expirydate();
  inline void clear_has_expirydate();
  inline void set_has_keybytes();
  inline void clear_has_keybytes();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 expirydate_;
  ::std::string* keybytes_;
  ::google::protobuf::uint32 keyid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static BlockKeyProto* default_instance_;
};
// -------------------------------------------------------------------

class ExportedBlockKeysProto : public ::google::protobuf::Message {
 public:
  ExportedBlockKeysProto();
  virtual ~ExportedBlockKeysProto();

  ExportedBlockKeysProto(const ExportedBlockKeysProto& from);

  inline ExportedBlockKeysProto& operator=(const ExportedBlockKeysProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ExportedBlockKeysProto& default_instance();

  void Swap(ExportedBlockKeysProto* other);

  // implements Message ----------------------------------------------

  ExportedBlockKeysProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ExportedBlockKeysProto& from);
  void MergeFrom(const ExportedBlockKeysProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bool isBlockTokenEnabled = 1;
  inline bool has_isblocktokenenabled() const;
  inline void clear_isblocktokenenabled();
  static const int kIsBlockTokenEnabledFieldNumber = 1;
  inline bool isblocktokenenabled() const;
  inline void set_isblocktokenenabled(bool value);

  // required uint64 keyUpdateInterval = 2;
  inline bool has_keyupdateinterval() const;
  inline void clear_keyupdateinterval();
  static const int kKeyUpdateIntervalFieldNumber = 2;
  inline ::google::protobuf::uint64 keyupdateinterval() const;
  inline void set_keyupdateinterval(::google::protobuf::uint64 value);

  // required uint64 tokenLifeTime = 3;
  inline bool has_tokenlifetime() const;
  inline void clear_tokenlifetime();
  static const int kTokenLifeTimeFieldNumber = 3;
  inline ::google::protobuf::uint64 tokenlifetime() const;
  inline void set_tokenlifetime(::google::protobuf::uint64 value);

  // required .hadoop.hdfs.BlockKeyProto currentKey = 4;
  inline bool has_currentkey() const;
  inline void clear_currentkey();
  static const int kCurrentKeyFieldNumber = 4;
  inline const ::hadoop::hdfs::BlockKeyProto& currentkey() const;
  inline ::hadoop::hdfs::BlockKeyProto* mutable_currentkey();
  inline ::hadoop::hdfs::BlockKeyProto* release_currentkey();
  inline void set_allocated_currentkey(::hadoop::hdfs::BlockKeyProto* currentkey);

  // repeated .hadoop.hdfs.BlockKeyProto allKeys = 5;
  inline int allkeys_size() const;
  inline void clear_allkeys();
  static const int kAllKeysFieldNumber = 5;
  inline const ::hadoop::hdfs::BlockKeyProto& allkeys(int index) const;
  inline ::hadoop::hdfs::BlockKeyProto* mutable_allkeys(int index);
  inline ::hadoop::hdfs::BlockKeyProto* add_allkeys();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockKeyProto >&
      allkeys() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockKeyProto >*
      mutable_allkeys();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ExportedBlockKeysProto)
 private:
  inline void set_has_isblocktokenenabled();
  inline void clear_has_isblocktokenenabled();
  inline void set_has_keyupdateinterval();
  inline void clear_has_keyupdateinterval();
  inline void set_has_tokenlifetime();
  inline void clear_has_tokenlifetime();
  inline void set_has_currentkey();
  inline void clear_has_currentkey();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 keyupdateinterval_;
  ::google::protobuf::uint64 tokenlifetime_;
  ::hadoop::hdfs::BlockKeyProto* currentkey_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockKeyProto > allkeys_;
  bool isblocktokenenabled_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static ExportedBlockKeysProto* default_instance_;
};
// -------------------------------------------------------------------

class RecoveringBlockProto : public ::google::protobuf::Message {
 public:
  RecoveringBlockProto();
  virtual ~RecoveringBlockProto();

  RecoveringBlockProto(const RecoveringBlockProto& from);

  inline RecoveringBlockProto& operator=(const RecoveringBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RecoveringBlockProto& default_instance();

  void Swap(RecoveringBlockProto* other);

  // implements Message ----------------------------------------------

  RecoveringBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RecoveringBlockProto& from);
  void MergeFrom(const RecoveringBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 newGenStamp = 1;
  inline bool has_newgenstamp() const;
  inline void clear_newgenstamp();
  static const int kNewGenStampFieldNumber = 1;
  inline ::google::protobuf::uint64 newgenstamp() const;
  inline void set_newgenstamp(::google::protobuf::uint64 value);

  // required .hadoop.hdfs.LocatedBlockProto block = 2;
  inline bool has_block() const;
  inline void clear_block();
  static const int kBlockFieldNumber = 2;
  inline const ::hadoop::hdfs::LocatedBlockProto& block() const;
  inline ::hadoop::hdfs::LocatedBlockProto* mutable_block();
  inline ::hadoop::hdfs::LocatedBlockProto* release_block();
  inline void set_allocated_block(::hadoop::hdfs::LocatedBlockProto* block);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RecoveringBlockProto)
 private:
  inline void set_has_newgenstamp();
  inline void clear_has_newgenstamp();
  inline void set_has_block();
  inline void clear_has_block();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 newgenstamp_;
  ::hadoop::hdfs::LocatedBlockProto* block_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static RecoveringBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class VersionRequestProto : public ::google::protobuf::Message {
 public:
  VersionRequestProto();
  virtual ~VersionRequestProto();

  VersionRequestProto(const VersionRequestProto& from);

  inline VersionRequestProto& operator=(const VersionRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const VersionRequestProto& default_instance();

  void Swap(VersionRequestProto* other);

  // implements Message ----------------------------------------------

  VersionRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const VersionRequestProto& from);
  void MergeFrom(const VersionRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.VersionRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static VersionRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class VersionResponseProto : public ::google::protobuf::Message {
 public:
  VersionResponseProto();
  virtual ~VersionResponseProto();

  VersionResponseProto(const VersionResponseProto& from);

  inline VersionResponseProto& operator=(const VersionResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const VersionResponseProto& default_instance();

  void Swap(VersionResponseProto* other);

  // implements Message ----------------------------------------------

  VersionResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const VersionResponseProto& from);
  void MergeFrom(const VersionResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.NamespaceInfoProto info = 1;
  inline bool has_info() const;
  inline void clear_info();
  static const int kInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::NamespaceInfoProto& info() const;
  inline ::hadoop::hdfs::NamespaceInfoProto* mutable_info();
  inline ::hadoop::hdfs::NamespaceInfoProto* release_info();
  inline void set_allocated_info(::hadoop::hdfs::NamespaceInfoProto* info);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.VersionResponseProto)
 private:
  inline void set_has_info();
  inline void clear_has_info();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::NamespaceInfoProto* info_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static VersionResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotInfoProto : public ::google::protobuf::Message {
 public:
  SnapshotInfoProto();
  virtual ~SnapshotInfoProto();

  SnapshotInfoProto(const SnapshotInfoProto& from);

  inline SnapshotInfoProto& operator=(const SnapshotInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotInfoProto& default_instance();

  void Swap(SnapshotInfoProto* other);

  // implements Message ----------------------------------------------

  SnapshotInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotInfoProto& from);
  void MergeFrom(const SnapshotInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string snapshotName = 1;
  inline bool has_snapshotname() const;
  inline void clear_snapshotname();
  static const int kSnapshotNameFieldNumber = 1;
  inline const ::std::string& snapshotname() const;
  inline void set_snapshotname(const ::std::string& value);
  inline void set_snapshotname(const char* value);
  inline void set_snapshotname(const char* value, size_t size);
  inline ::std::string* mutable_snapshotname();
  inline ::std::string* release_snapshotname();
  inline void set_allocated_snapshotname(::std::string* snapshotname);

  // required string snapshotRoot = 2;
  inline bool has_snapshotroot() const;
  inline void clear_snapshotroot();
  static const int kSnapshotRootFieldNumber = 2;
  inline const ::std::string& snapshotroot() const;
  inline void set_snapshotroot(const ::std::string& value);
  inline void set_snapshotroot(const char* value);
  inline void set_snapshotroot(const char* value, size_t size);
  inline ::std::string* mutable_snapshotroot();
  inline ::std::string* release_snapshotroot();
  inline void set_allocated_snapshotroot(::std::string* snapshotroot);

  // required .hadoop.hdfs.FsPermissionProto permission = 3;
  inline bool has_permission() const;
  inline void clear_permission();
  static const int kPermissionFieldNumber = 3;
  inline const ::hadoop::hdfs::FsPermissionProto& permission() const;
  inline ::hadoop::hdfs::FsPermissionProto* mutable_permission();
  inline ::hadoop::hdfs::FsPermissionProto* release_permission();
  inline void set_allocated_permission(::hadoop::hdfs::FsPermissionProto* permission);

  // required string owner = 4;
  inline bool has_owner() const;
  inline void clear_owner();
  static const int kOwnerFieldNumber = 4;
  inline const ::std::string& owner() const;
  inline void set_owner(const ::std::string& value);
  inline void set_owner(const char* value);
  inline void set_owner(const char* value, size_t size);
  inline ::std::string* mutable_owner();
  inline ::std::string* release_owner();
  inline void set_allocated_owner(::std::string* owner);

  // required string group = 5;
  inline bool has_group() const;
  inline void clear_group();
  static const int kGroupFieldNumber = 5;
  inline const ::std::string& group() const;
  inline void set_group(const ::std::string& value);
  inline void set_group(const char* value);
  inline void set_group(const char* value, size_t size);
  inline ::std::string* mutable_group();
  inline ::std::string* release_group();
  inline void set_allocated_group(::std::string* group);

  // required string createTime = 6;
  inline bool has_createtime() const;
  inline void clear_createtime();
  static const int kCreateTimeFieldNumber = 6;
  inline const ::std::string& createtime() const;
  inline void set_createtime(const ::std::string& value);
  inline void set_createtime(const char* value);
  inline void set_createtime(const char* value, size_t size);
  inline ::std::string* mutable_createtime();
  inline ::std::string* release_createtime();
  inline void set_allocated_createtime(::std::string* createtime);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SnapshotInfoProto)
 private:
  inline void set_has_snapshotname();
  inline void clear_has_snapshotname();
  inline void set_has_snapshotroot();
  inline void clear_has_snapshotroot();
  inline void set_has_permission();
  inline void clear_has_permission();
  inline void set_has_owner();
  inline void clear_has_owner();
  inline void set_has_group();
  inline void clear_has_group();
  inline void set_has_createtime();
  inline void clear_has_createtime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* snapshotname_;
  ::std::string* snapshotroot_;
  ::hadoop::hdfs::FsPermissionProto* permission_;
  ::std::string* owner_;
  ::std::string* group_;
  ::std::string* createtime_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static SnapshotInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class RollingUpgradeStatusProto : public ::google::protobuf::Message {
 public:
  RollingUpgradeStatusProto();
  virtual ~RollingUpgradeStatusProto();

  RollingUpgradeStatusProto(const RollingUpgradeStatusProto& from);

  inline RollingUpgradeStatusProto& operator=(const RollingUpgradeStatusProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RollingUpgradeStatusProto& default_instance();

  void Swap(RollingUpgradeStatusProto* other);

  // implements Message ----------------------------------------------

  RollingUpgradeStatusProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RollingUpgradeStatusProto& from);
  void MergeFrom(const RollingUpgradeStatusProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string blockPoolId = 1;
  inline bool has_blockpoolid() const;
  inline void clear_blockpoolid();
  static const int kBlockPoolIdFieldNumber = 1;
  inline const ::std::string& blockpoolid() const;
  inline void set_blockpoolid(const ::std::string& value);
  inline void set_blockpoolid(const char* value);
  inline void set_blockpoolid(const char* value, size_t size);
  inline ::std::string* mutable_blockpoolid();
  inline ::std::string* release_blockpoolid();
  inline void set_allocated_blockpoolid(::std::string* blockpoolid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RollingUpgradeStatusProto)
 private:
  inline void set_has_blockpoolid();
  inline void clear_has_blockpoolid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* blockpoolid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_hdfs_2eproto();
  friend void protobuf_AssignDesc_hdfs_2eproto();
  friend void protobuf_ShutdownFile_hdfs_2eproto();

  void InitAsDefaultInstance();
  static RollingUpgradeStatusProto* default_instance_;
};
// ===================================================================


// ===================================================================

// ExtendedBlockProto

// required string poolId = 1;
inline bool ExtendedBlockProto::has_poolid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ExtendedBlockProto::set_has_poolid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ExtendedBlockProto::clear_has_poolid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ExtendedBlockProto::clear_poolid() {
  if (poolid_ != &::google::protobuf::internal::kEmptyString) {
    poolid_->clear();
  }
  clear_has_poolid();
}
inline const ::std::string& ExtendedBlockProto::poolid() const {
  return *poolid_;
}
inline void ExtendedBlockProto::set_poolid(const ::std::string& value) {
  set_has_poolid();
  if (poolid_ == &::google::protobuf::internal::kEmptyString) {
    poolid_ = new ::std::string;
  }
  poolid_->assign(value);
}
inline void ExtendedBlockProto::set_poolid(const char* value) {
  set_has_poolid();
  if (poolid_ == &::google::protobuf::internal::kEmptyString) {
    poolid_ = new ::std::string;
  }
  poolid_->assign(value);
}
inline void ExtendedBlockProto::set_poolid(const char* value, size_t size) {
  set_has_poolid();
  if (poolid_ == &::google::protobuf::internal::kEmptyString) {
    poolid_ = new ::std::string;
  }
  poolid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ExtendedBlockProto::mutable_poolid() {
  set_has_poolid();
  if (poolid_ == &::google::protobuf::internal::kEmptyString) {
    poolid_ = new ::std::string;
  }
  return poolid_;
}
inline ::std::string* ExtendedBlockProto::release_poolid() {
  clear_has_poolid();
  if (poolid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = poolid_;
    poolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ExtendedBlockProto::set_allocated_poolid(::std::string* poolid) {
  if (poolid_ != &::google::protobuf::internal::kEmptyString) {
    delete poolid_;
  }
  if (poolid) {
    set_has_poolid();
    poolid_ = poolid;
  } else {
    clear_has_poolid();
    poolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 blockId = 2;
inline bool ExtendedBlockProto::has_blockid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ExtendedBlockProto::set_has_blockid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ExtendedBlockProto::clear_has_blockid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ExtendedBlockProto::clear_blockid() {
  blockid_ = GOOGLE_ULONGLONG(0);
  clear_has_blockid();
}
inline ::google::protobuf::uint64 ExtendedBlockProto::blockid() const {
  return blockid_;
}
inline void ExtendedBlockProto::set_blockid(::google::protobuf::uint64 value) {
  set_has_blockid();
  blockid_ = value;
}

// required uint64 generationStamp = 3;
inline bool ExtendedBlockProto::has_generationstamp() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ExtendedBlockProto::set_has_generationstamp() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ExtendedBlockProto::clear_has_generationstamp() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ExtendedBlockProto::clear_generationstamp() {
  generationstamp_ = GOOGLE_ULONGLONG(0);
  clear_has_generationstamp();
}
inline ::google::protobuf::uint64 ExtendedBlockProto::generationstamp() const {
  return generationstamp_;
}
inline void ExtendedBlockProto::set_generationstamp(::google::protobuf::uint64 value) {
  set_has_generationstamp();
  generationstamp_ = value;
}

// optional uint64 numBytes = 4 [default = 0];
inline bool ExtendedBlockProto::has_numbytes() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ExtendedBlockProto::set_has_numbytes() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ExtendedBlockProto::clear_has_numbytes() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ExtendedBlockProto::clear_numbytes() {
  numbytes_ = GOOGLE_ULONGLONG(0);
  clear_has_numbytes();
}
inline ::google::protobuf::uint64 ExtendedBlockProto::numbytes() const {
  return numbytes_;
}
inline void ExtendedBlockProto::set_numbytes(::google::protobuf::uint64 value) {
  set_has_numbytes();
  numbytes_ = value;
}

// -------------------------------------------------------------------

// DatanodeIDProto

// required string ipAddr = 1;
inline bool DatanodeIDProto::has_ipaddr() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DatanodeIDProto::set_has_ipaddr() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DatanodeIDProto::clear_has_ipaddr() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DatanodeIDProto::clear_ipaddr() {
  if (ipaddr_ != &::google::protobuf::internal::kEmptyString) {
    ipaddr_->clear();
  }
  clear_has_ipaddr();
}
inline const ::std::string& DatanodeIDProto::ipaddr() const {
  return *ipaddr_;
}
inline void DatanodeIDProto::set_ipaddr(const ::std::string& value) {
  set_has_ipaddr();
  if (ipaddr_ == &::google::protobuf::internal::kEmptyString) {
    ipaddr_ = new ::std::string;
  }
  ipaddr_->assign(value);
}
inline void DatanodeIDProto::set_ipaddr(const char* value) {
  set_has_ipaddr();
  if (ipaddr_ == &::google::protobuf::internal::kEmptyString) {
    ipaddr_ = new ::std::string;
  }
  ipaddr_->assign(value);
}
inline void DatanodeIDProto::set_ipaddr(const char* value, size_t size) {
  set_has_ipaddr();
  if (ipaddr_ == &::google::protobuf::internal::kEmptyString) {
    ipaddr_ = new ::std::string;
  }
  ipaddr_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeIDProto::mutable_ipaddr() {
  set_has_ipaddr();
  if (ipaddr_ == &::google::protobuf::internal::kEmptyString) {
    ipaddr_ = new ::std::string;
  }
  return ipaddr_;
}
inline ::std::string* DatanodeIDProto::release_ipaddr() {
  clear_has_ipaddr();
  if (ipaddr_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = ipaddr_;
    ipaddr_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeIDProto::set_allocated_ipaddr(::std::string* ipaddr) {
  if (ipaddr_ != &::google::protobuf::internal::kEmptyString) {
    delete ipaddr_;
  }
  if (ipaddr) {
    set_has_ipaddr();
    ipaddr_ = ipaddr;
  } else {
    clear_has_ipaddr();
    ipaddr_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string hostName = 2;
inline bool DatanodeIDProto::has_hostname() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DatanodeIDProto::set_has_hostname() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DatanodeIDProto::clear_has_hostname() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DatanodeIDProto::clear_hostname() {
  if (hostname_ != &::google::protobuf::internal::kEmptyString) {
    hostname_->clear();
  }
  clear_has_hostname();
}
inline const ::std::string& DatanodeIDProto::hostname() const {
  return *hostname_;
}
inline void DatanodeIDProto::set_hostname(const ::std::string& value) {
  set_has_hostname();
  if (hostname_ == &::google::protobuf::internal::kEmptyString) {
    hostname_ = new ::std::string;
  }
  hostname_->assign(value);
}
inline void DatanodeIDProto::set_hostname(const char* value) {
  set_has_hostname();
  if (hostname_ == &::google::protobuf::internal::kEmptyString) {
    hostname_ = new ::std::string;
  }
  hostname_->assign(value);
}
inline void DatanodeIDProto::set_hostname(const char* value, size_t size) {
  set_has_hostname();
  if (hostname_ == &::google::protobuf::internal::kEmptyString) {
    hostname_ = new ::std::string;
  }
  hostname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeIDProto::mutable_hostname() {
  set_has_hostname();
  if (hostname_ == &::google::protobuf::internal::kEmptyString) {
    hostname_ = new ::std::string;
  }
  return hostname_;
}
inline ::std::string* DatanodeIDProto::release_hostname() {
  clear_has_hostname();
  if (hostname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = hostname_;
    hostname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeIDProto::set_allocated_hostname(::std::string* hostname) {
  if (hostname_ != &::google::protobuf::internal::kEmptyString) {
    delete hostname_;
  }
  if (hostname) {
    set_has_hostname();
    hostname_ = hostname;
  } else {
    clear_has_hostname();
    hostname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string datanodeUuid = 3;
inline bool DatanodeIDProto::has_datanodeuuid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DatanodeIDProto::set_has_datanodeuuid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DatanodeIDProto::clear_has_datanodeuuid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DatanodeIDProto::clear_datanodeuuid() {
  if (datanodeuuid_ != &::google::protobuf::internal::kEmptyString) {
    datanodeuuid_->clear();
  }
  clear_has_datanodeuuid();
}
inline const ::std::string& DatanodeIDProto::datanodeuuid() const {
  return *datanodeuuid_;
}
inline void DatanodeIDProto::set_datanodeuuid(const ::std::string& value) {
  set_has_datanodeuuid();
  if (datanodeuuid_ == &::google::protobuf::internal::kEmptyString) {
    datanodeuuid_ = new ::std::string;
  }
  datanodeuuid_->assign(value);
}
inline void DatanodeIDProto::set_datanodeuuid(const char* value) {
  set_has_datanodeuuid();
  if (datanodeuuid_ == &::google::protobuf::internal::kEmptyString) {
    datanodeuuid_ = new ::std::string;
  }
  datanodeuuid_->assign(value);
}
inline void DatanodeIDProto::set_datanodeuuid(const char* value, size_t size) {
  set_has_datanodeuuid();
  if (datanodeuuid_ == &::google::protobuf::internal::kEmptyString) {
    datanodeuuid_ = new ::std::string;
  }
  datanodeuuid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeIDProto::mutable_datanodeuuid() {
  set_has_datanodeuuid();
  if (datanodeuuid_ == &::google::protobuf::internal::kEmptyString) {
    datanodeuuid_ = new ::std::string;
  }
  return datanodeuuid_;
}
inline ::std::string* DatanodeIDProto::release_datanodeuuid() {
  clear_has_datanodeuuid();
  if (datanodeuuid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = datanodeuuid_;
    datanodeuuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeIDProto::set_allocated_datanodeuuid(::std::string* datanodeuuid) {
  if (datanodeuuid_ != &::google::protobuf::internal::kEmptyString) {
    delete datanodeuuid_;
  }
  if (datanodeuuid) {
    set_has_datanodeuuid();
    datanodeuuid_ = datanodeuuid;
  } else {
    clear_has_datanodeuuid();
    datanodeuuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint32 xferPort = 4;
inline bool DatanodeIDProto::has_xferport() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void DatanodeIDProto::set_has_xferport() {
  _has_bits_[0] |= 0x00000008u;
}
inline void DatanodeIDProto::clear_has_xferport() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void DatanodeIDProto::clear_xferport() {
  xferport_ = 0u;
  clear_has_xferport();
}
inline ::google::protobuf::uint32 DatanodeIDProto::xferport() const {
  return xferport_;
}
inline void DatanodeIDProto::set_xferport(::google::protobuf::uint32 value) {
  set_has_xferport();
  xferport_ = value;
}

// required uint32 infoPort = 5;
inline bool DatanodeIDProto::has_infoport() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void DatanodeIDProto::set_has_infoport() {
  _has_bits_[0] |= 0x00000010u;
}
inline void DatanodeIDProto::clear_has_infoport() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void DatanodeIDProto::clear_infoport() {
  infoport_ = 0u;
  clear_has_infoport();
}
inline ::google::protobuf::uint32 DatanodeIDProto::infoport() const {
  return infoport_;
}
inline void DatanodeIDProto::set_infoport(::google::protobuf::uint32 value) {
  set_has_infoport();
  infoport_ = value;
}

// required uint32 ipcPort = 6;
inline bool DatanodeIDProto::has_ipcport() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void DatanodeIDProto::set_has_ipcport() {
  _has_bits_[0] |= 0x00000020u;
}
inline void DatanodeIDProto::clear_has_ipcport() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void DatanodeIDProto::clear_ipcport() {
  ipcport_ = 0u;
  clear_has_ipcport();
}
inline ::google::protobuf::uint32 DatanodeIDProto::ipcport() const {
  return ipcport_;
}
inline void DatanodeIDProto::set_ipcport(::google::protobuf::uint32 value) {
  set_has_ipcport();
  ipcport_ = value;
}

// optional uint32 infoSecurePort = 7 [default = 0];
inline bool DatanodeIDProto::has_infosecureport() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void DatanodeIDProto::set_has_infosecureport() {
  _has_bits_[0] |= 0x00000040u;
}
inline void DatanodeIDProto::clear_has_infosecureport() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void DatanodeIDProto::clear_infosecureport() {
  infosecureport_ = 0u;
  clear_has_infosecureport();
}
inline ::google::protobuf::uint32 DatanodeIDProto::infosecureport() const {
  return infosecureport_;
}
inline void DatanodeIDProto::set_infosecureport(::google::protobuf::uint32 value) {
  set_has_infosecureport();
  infosecureport_ = value;
}

// -------------------------------------------------------------------

// DatanodeLocalInfoProto

// required string softwareVersion = 1;
inline bool DatanodeLocalInfoProto::has_softwareversion() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DatanodeLocalInfoProto::set_has_softwareversion() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DatanodeLocalInfoProto::clear_has_softwareversion() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DatanodeLocalInfoProto::clear_softwareversion() {
  if (softwareversion_ != &::google::protobuf::internal::kEmptyString) {
    softwareversion_->clear();
  }
  clear_has_softwareversion();
}
inline const ::std::string& DatanodeLocalInfoProto::softwareversion() const {
  return *softwareversion_;
}
inline void DatanodeLocalInfoProto::set_softwareversion(const ::std::string& value) {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  softwareversion_->assign(value);
}
inline void DatanodeLocalInfoProto::set_softwareversion(const char* value) {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  softwareversion_->assign(value);
}
inline void DatanodeLocalInfoProto::set_softwareversion(const char* value, size_t size) {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  softwareversion_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeLocalInfoProto::mutable_softwareversion() {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  return softwareversion_;
}
inline ::std::string* DatanodeLocalInfoProto::release_softwareversion() {
  clear_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = softwareversion_;
    softwareversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeLocalInfoProto::set_allocated_softwareversion(::std::string* softwareversion) {
  if (softwareversion_ != &::google::protobuf::internal::kEmptyString) {
    delete softwareversion_;
  }
  if (softwareversion) {
    set_has_softwareversion();
    softwareversion_ = softwareversion;
  } else {
    clear_has_softwareversion();
    softwareversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string configVersion = 2;
inline bool DatanodeLocalInfoProto::has_configversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DatanodeLocalInfoProto::set_has_configversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DatanodeLocalInfoProto::clear_has_configversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DatanodeLocalInfoProto::clear_configversion() {
  if (configversion_ != &::google::protobuf::internal::kEmptyString) {
    configversion_->clear();
  }
  clear_has_configversion();
}
inline const ::std::string& DatanodeLocalInfoProto::configversion() const {
  return *configversion_;
}
inline void DatanodeLocalInfoProto::set_configversion(const ::std::string& value) {
  set_has_configversion();
  if (configversion_ == &::google::protobuf::internal::kEmptyString) {
    configversion_ = new ::std::string;
  }
  configversion_->assign(value);
}
inline void DatanodeLocalInfoProto::set_configversion(const char* value) {
  set_has_configversion();
  if (configversion_ == &::google::protobuf::internal::kEmptyString) {
    configversion_ = new ::std::string;
  }
  configversion_->assign(value);
}
inline void DatanodeLocalInfoProto::set_configversion(const char* value, size_t size) {
  set_has_configversion();
  if (configversion_ == &::google::protobuf::internal::kEmptyString) {
    configversion_ = new ::std::string;
  }
  configversion_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeLocalInfoProto::mutable_configversion() {
  set_has_configversion();
  if (configversion_ == &::google::protobuf::internal::kEmptyString) {
    configversion_ = new ::std::string;
  }
  return configversion_;
}
inline ::std::string* DatanodeLocalInfoProto::release_configversion() {
  clear_has_configversion();
  if (configversion_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = configversion_;
    configversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeLocalInfoProto::set_allocated_configversion(::std::string* configversion) {
  if (configversion_ != &::google::protobuf::internal::kEmptyString) {
    delete configversion_;
  }
  if (configversion) {
    set_has_configversion();
    configversion_ = configversion;
  } else {
    clear_has_configversion();
    configversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 uptime = 3;
inline bool DatanodeLocalInfoProto::has_uptime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DatanodeLocalInfoProto::set_has_uptime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DatanodeLocalInfoProto::clear_has_uptime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DatanodeLocalInfoProto::clear_uptime() {
  uptime_ = GOOGLE_ULONGLONG(0);
  clear_has_uptime();
}
inline ::google::protobuf::uint64 DatanodeLocalInfoProto::uptime() const {
  return uptime_;
}
inline void DatanodeLocalInfoProto::set_uptime(::google::protobuf::uint64 value) {
  set_has_uptime();
  uptime_ = value;
}

// -------------------------------------------------------------------

// DatanodeInfosProto

// repeated .hadoop.hdfs.DatanodeInfoProto datanodes = 1;
inline int DatanodeInfosProto::datanodes_size() const {
  return datanodes_.size();
}
inline void DatanodeInfosProto::clear_datanodes() {
  datanodes_.Clear();
}
inline const ::hadoop::hdfs::DatanodeInfoProto& DatanodeInfosProto::datanodes(int index) const {
  return datanodes_.Get(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* DatanodeInfosProto::mutable_datanodes(int index) {
  return datanodes_.Mutable(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* DatanodeInfosProto::add_datanodes() {
  return datanodes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
DatanodeInfosProto::datanodes() const {
  return datanodes_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
DatanodeInfosProto::mutable_datanodes() {
  return &datanodes_;
}

// -------------------------------------------------------------------

// DatanodeInfoProto

// required .hadoop.hdfs.DatanodeIDProto id = 1;
inline bool DatanodeInfoProto::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DatanodeInfoProto::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DatanodeInfoProto::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DatanodeInfoProto::clear_id() {
  if (id_ != NULL) id_->::hadoop::hdfs::DatanodeIDProto::Clear();
  clear_has_id();
}
inline const ::hadoop::hdfs::DatanodeIDProto& DatanodeInfoProto::id() const {
  return id_ != NULL ? *id_ : *default_instance_->id_;
}
inline ::hadoop::hdfs::DatanodeIDProto* DatanodeInfoProto::mutable_id() {
  set_has_id();
  if (id_ == NULL) id_ = new ::hadoop::hdfs::DatanodeIDProto;
  return id_;
}
inline ::hadoop::hdfs::DatanodeIDProto* DatanodeInfoProto::release_id() {
  clear_has_id();
  ::hadoop::hdfs::DatanodeIDProto* temp = id_;
  id_ = NULL;
  return temp;
}
inline void DatanodeInfoProto::set_allocated_id(::hadoop::hdfs::DatanodeIDProto* id) {
  delete id_;
  id_ = id;
  if (id) {
    set_has_id();
  } else {
    clear_has_id();
  }
}

// optional uint64 capacity = 2 [default = 0];
inline bool DatanodeInfoProto::has_capacity() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DatanodeInfoProto::set_has_capacity() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DatanodeInfoProto::clear_has_capacity() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DatanodeInfoProto::clear_capacity() {
  capacity_ = GOOGLE_ULONGLONG(0);
  clear_has_capacity();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::capacity() const {
  return capacity_;
}
inline void DatanodeInfoProto::set_capacity(::google::protobuf::uint64 value) {
  set_has_capacity();
  capacity_ = value;
}

// optional uint64 dfsUsed = 3 [default = 0];
inline bool DatanodeInfoProto::has_dfsused() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DatanodeInfoProto::set_has_dfsused() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DatanodeInfoProto::clear_has_dfsused() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DatanodeInfoProto::clear_dfsused() {
  dfsused_ = GOOGLE_ULONGLONG(0);
  clear_has_dfsused();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::dfsused() const {
  return dfsused_;
}
inline void DatanodeInfoProto::set_dfsused(::google::protobuf::uint64 value) {
  set_has_dfsused();
  dfsused_ = value;
}

// optional uint64 remaining = 4 [default = 0];
inline bool DatanodeInfoProto::has_remaining() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void DatanodeInfoProto::set_has_remaining() {
  _has_bits_[0] |= 0x00000008u;
}
inline void DatanodeInfoProto::clear_has_remaining() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void DatanodeInfoProto::clear_remaining() {
  remaining_ = GOOGLE_ULONGLONG(0);
  clear_has_remaining();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::remaining() const {
  return remaining_;
}
inline void DatanodeInfoProto::set_remaining(::google::protobuf::uint64 value) {
  set_has_remaining();
  remaining_ = value;
}

// optional uint64 blockPoolUsed = 5 [default = 0];
inline bool DatanodeInfoProto::has_blockpoolused() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void DatanodeInfoProto::set_has_blockpoolused() {
  _has_bits_[0] |= 0x00000010u;
}
inline void DatanodeInfoProto::clear_has_blockpoolused() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void DatanodeInfoProto::clear_blockpoolused() {
  blockpoolused_ = GOOGLE_ULONGLONG(0);
  clear_has_blockpoolused();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::blockpoolused() const {
  return blockpoolused_;
}
inline void DatanodeInfoProto::set_blockpoolused(::google::protobuf::uint64 value) {
  set_has_blockpoolused();
  blockpoolused_ = value;
}

// optional uint64 lastUpdate = 6 [default = 0];
inline bool DatanodeInfoProto::has_lastupdate() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void DatanodeInfoProto::set_has_lastupdate() {
  _has_bits_[0] |= 0x00000020u;
}
inline void DatanodeInfoProto::clear_has_lastupdate() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void DatanodeInfoProto::clear_lastupdate() {
  lastupdate_ = GOOGLE_ULONGLONG(0);
  clear_has_lastupdate();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::lastupdate() const {
  return lastupdate_;
}
inline void DatanodeInfoProto::set_lastupdate(::google::protobuf::uint64 value) {
  set_has_lastupdate();
  lastupdate_ = value;
}

// optional uint32 xceiverCount = 7 [default = 0];
inline bool DatanodeInfoProto::has_xceivercount() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void DatanodeInfoProto::set_has_xceivercount() {
  _has_bits_[0] |= 0x00000040u;
}
inline void DatanodeInfoProto::clear_has_xceivercount() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void DatanodeInfoProto::clear_xceivercount() {
  xceivercount_ = 0u;
  clear_has_xceivercount();
}
inline ::google::protobuf::uint32 DatanodeInfoProto::xceivercount() const {
  return xceivercount_;
}
inline void DatanodeInfoProto::set_xceivercount(::google::protobuf::uint32 value) {
  set_has_xceivercount();
  xceivercount_ = value;
}

// optional string location = 8;
inline bool DatanodeInfoProto::has_location() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void DatanodeInfoProto::set_has_location() {
  _has_bits_[0] |= 0x00000080u;
}
inline void DatanodeInfoProto::clear_has_location() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void DatanodeInfoProto::clear_location() {
  if (location_ != &::google::protobuf::internal::kEmptyString) {
    location_->clear();
  }
  clear_has_location();
}
inline const ::std::string& DatanodeInfoProto::location() const {
  return *location_;
}
inline void DatanodeInfoProto::set_location(const ::std::string& value) {
  set_has_location();
  if (location_ == &::google::protobuf::internal::kEmptyString) {
    location_ = new ::std::string;
  }
  location_->assign(value);
}
inline void DatanodeInfoProto::set_location(const char* value) {
  set_has_location();
  if (location_ == &::google::protobuf::internal::kEmptyString) {
    location_ = new ::std::string;
  }
  location_->assign(value);
}
inline void DatanodeInfoProto::set_location(const char* value, size_t size) {
  set_has_location();
  if (location_ == &::google::protobuf::internal::kEmptyString) {
    location_ = new ::std::string;
  }
  location_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeInfoProto::mutable_location() {
  set_has_location();
  if (location_ == &::google::protobuf::internal::kEmptyString) {
    location_ = new ::std::string;
  }
  return location_;
}
inline ::std::string* DatanodeInfoProto::release_location() {
  clear_has_location();
  if (location_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = location_;
    location_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeInfoProto::set_allocated_location(::std::string* location) {
  if (location_ != &::google::protobuf::internal::kEmptyString) {
    delete location_;
  }
  if (location) {
    set_has_location();
    location_ = location;
  } else {
    clear_has_location();
    location_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.DatanodeInfoProto.AdminState adminState = 10 [default = NORMAL];
inline bool DatanodeInfoProto::has_adminstate() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void DatanodeInfoProto::set_has_adminstate() {
  _has_bits_[0] |= 0x00000100u;
}
inline void DatanodeInfoProto::clear_has_adminstate() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void DatanodeInfoProto::clear_adminstate() {
  adminstate_ = 0;
  clear_has_adminstate();
}
inline ::hadoop::hdfs::DatanodeInfoProto_AdminState DatanodeInfoProto::adminstate() const {
  return static_cast< ::hadoop::hdfs::DatanodeInfoProto_AdminState >(adminstate_);
}
inline void DatanodeInfoProto::set_adminstate(::hadoop::hdfs::DatanodeInfoProto_AdminState value) {
  assert(::hadoop::hdfs::DatanodeInfoProto_AdminState_IsValid(value));
  set_has_adminstate();
  adminstate_ = value;
}

// optional uint64 cacheCapacity = 11 [default = 0];
inline bool DatanodeInfoProto::has_cachecapacity() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void DatanodeInfoProto::set_has_cachecapacity() {
  _has_bits_[0] |= 0x00000200u;
}
inline void DatanodeInfoProto::clear_has_cachecapacity() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void DatanodeInfoProto::clear_cachecapacity() {
  cachecapacity_ = GOOGLE_ULONGLONG(0);
  clear_has_cachecapacity();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::cachecapacity() const {
  return cachecapacity_;
}
inline void DatanodeInfoProto::set_cachecapacity(::google::protobuf::uint64 value) {
  set_has_cachecapacity();
  cachecapacity_ = value;
}

// optional uint64 cacheUsed = 12 [default = 0];
inline bool DatanodeInfoProto::has_cacheused() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void DatanodeInfoProto::set_has_cacheused() {
  _has_bits_[0] |= 0x00000400u;
}
inline void DatanodeInfoProto::clear_has_cacheused() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void DatanodeInfoProto::clear_cacheused() {
  cacheused_ = GOOGLE_ULONGLONG(0);
  clear_has_cacheused();
}
inline ::google::protobuf::uint64 DatanodeInfoProto::cacheused() const {
  return cacheused_;
}
inline void DatanodeInfoProto::set_cacheused(::google::protobuf::uint64 value) {
  set_has_cacheused();
  cacheused_ = value;
}

// -------------------------------------------------------------------

// DatanodeStorageProto

// required string storageUuid = 1;
inline bool DatanodeStorageProto::has_storageuuid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DatanodeStorageProto::set_has_storageuuid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DatanodeStorageProto::clear_has_storageuuid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DatanodeStorageProto::clear_storageuuid() {
  if (storageuuid_ != &::google::protobuf::internal::kEmptyString) {
    storageuuid_->clear();
  }
  clear_has_storageuuid();
}
inline const ::std::string& DatanodeStorageProto::storageuuid() const {
  return *storageuuid_;
}
inline void DatanodeStorageProto::set_storageuuid(const ::std::string& value) {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  storageuuid_->assign(value);
}
inline void DatanodeStorageProto::set_storageuuid(const char* value) {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  storageuuid_->assign(value);
}
inline void DatanodeStorageProto::set_storageuuid(const char* value, size_t size) {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  storageuuid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DatanodeStorageProto::mutable_storageuuid() {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  return storageuuid_;
}
inline ::std::string* DatanodeStorageProto::release_storageuuid() {
  clear_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = storageuuid_;
    storageuuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DatanodeStorageProto::set_allocated_storageuuid(::std::string* storageuuid) {
  if (storageuuid_ != &::google::protobuf::internal::kEmptyString) {
    delete storageuuid_;
  }
  if (storageuuid) {
    set_has_storageuuid();
    storageuuid_ = storageuuid;
  } else {
    clear_has_storageuuid();
    storageuuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.DatanodeStorageProto.StorageState state = 2 [default = NORMAL];
inline bool DatanodeStorageProto::has_state() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DatanodeStorageProto::set_has_state() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DatanodeStorageProto::clear_has_state() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DatanodeStorageProto::clear_state() {
  state_ = 0;
  clear_has_state();
}
inline ::hadoop::hdfs::DatanodeStorageProto_StorageState DatanodeStorageProto::state() const {
  return static_cast< ::hadoop::hdfs::DatanodeStorageProto_StorageState >(state_);
}
inline void DatanodeStorageProto::set_state(::hadoop::hdfs::DatanodeStorageProto_StorageState value) {
  assert(::hadoop::hdfs::DatanodeStorageProto_StorageState_IsValid(value));
  set_has_state();
  state_ = value;
}

// optional .hadoop.hdfs.StorageTypeProto storageType = 3 [default = DISK];
inline bool DatanodeStorageProto::has_storagetype() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DatanodeStorageProto::set_has_storagetype() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DatanodeStorageProto::clear_has_storagetype() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DatanodeStorageProto::clear_storagetype() {
  storagetype_ = 1;
  clear_has_storagetype();
}
inline ::hadoop::hdfs::StorageTypeProto DatanodeStorageProto::storagetype() const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(storagetype_);
}
inline void DatanodeStorageProto::set_storagetype(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  set_has_storagetype();
  storagetype_ = value;
}

// -------------------------------------------------------------------

// StorageReportProto

// required string storageUuid = 1 [deprecated = true];
inline bool StorageReportProto::has_storageuuid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StorageReportProto::set_has_storageuuid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StorageReportProto::clear_has_storageuuid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StorageReportProto::clear_storageuuid() {
  if (storageuuid_ != &::google::protobuf::internal::kEmptyString) {
    storageuuid_->clear();
  }
  clear_has_storageuuid();
}
inline const ::std::string& StorageReportProto::storageuuid() const {
  return *storageuuid_;
}
inline void StorageReportProto::set_storageuuid(const ::std::string& value) {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  storageuuid_->assign(value);
}
inline void StorageReportProto::set_storageuuid(const char* value) {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  storageuuid_->assign(value);
}
inline void StorageReportProto::set_storageuuid(const char* value, size_t size) {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  storageuuid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* StorageReportProto::mutable_storageuuid() {
  set_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    storageuuid_ = new ::std::string;
  }
  return storageuuid_;
}
inline ::std::string* StorageReportProto::release_storageuuid() {
  clear_has_storageuuid();
  if (storageuuid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = storageuuid_;
    storageuuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void StorageReportProto::set_allocated_storageuuid(::std::string* storageuuid) {
  if (storageuuid_ != &::google::protobuf::internal::kEmptyString) {
    delete storageuuid_;
  }
  if (storageuuid) {
    set_has_storageuuid();
    storageuuid_ = storageuuid;
  } else {
    clear_has_storageuuid();
    storageuuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bool failed = 2 [default = false];
inline bool StorageReportProto::has_failed() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void StorageReportProto::set_has_failed() {
  _has_bits_[0] |= 0x00000002u;
}
inline void StorageReportProto::clear_has_failed() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void StorageReportProto::clear_failed() {
  failed_ = false;
  clear_has_failed();
}
inline bool StorageReportProto::failed() const {
  return failed_;
}
inline void StorageReportProto::set_failed(bool value) {
  set_has_failed();
  failed_ = value;
}

// optional uint64 capacity = 3 [default = 0];
inline bool StorageReportProto::has_capacity() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void StorageReportProto::set_has_capacity() {
  _has_bits_[0] |= 0x00000004u;
}
inline void StorageReportProto::clear_has_capacity() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void StorageReportProto::clear_capacity() {
  capacity_ = GOOGLE_ULONGLONG(0);
  clear_has_capacity();
}
inline ::google::protobuf::uint64 StorageReportProto::capacity() const {
  return capacity_;
}
inline void StorageReportProto::set_capacity(::google::protobuf::uint64 value) {
  set_has_capacity();
  capacity_ = value;
}

// optional uint64 dfsUsed = 4 [default = 0];
inline bool StorageReportProto::has_dfsused() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void StorageReportProto::set_has_dfsused() {
  _has_bits_[0] |= 0x00000008u;
}
inline void StorageReportProto::clear_has_dfsused() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void StorageReportProto::clear_dfsused() {
  dfsused_ = GOOGLE_ULONGLONG(0);
  clear_has_dfsused();
}
inline ::google::protobuf::uint64 StorageReportProto::dfsused() const {
  return dfsused_;
}
inline void StorageReportProto::set_dfsused(::google::protobuf::uint64 value) {
  set_has_dfsused();
  dfsused_ = value;
}

// optional uint64 remaining = 5 [default = 0];
inline bool StorageReportProto::has_remaining() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void StorageReportProto::set_has_remaining() {
  _has_bits_[0] |= 0x00000010u;
}
inline void StorageReportProto::clear_has_remaining() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void StorageReportProto::clear_remaining() {
  remaining_ = GOOGLE_ULONGLONG(0);
  clear_has_remaining();
}
inline ::google::protobuf::uint64 StorageReportProto::remaining() const {
  return remaining_;
}
inline void StorageReportProto::set_remaining(::google::protobuf::uint64 value) {
  set_has_remaining();
  remaining_ = value;
}

// optional uint64 blockPoolUsed = 6 [default = 0];
inline bool StorageReportProto::has_blockpoolused() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void StorageReportProto::set_has_blockpoolused() {
  _has_bits_[0] |= 0x00000020u;
}
inline void StorageReportProto::clear_has_blockpoolused() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void StorageReportProto::clear_blockpoolused() {
  blockpoolused_ = GOOGLE_ULONGLONG(0);
  clear_has_blockpoolused();
}
inline ::google::protobuf::uint64 StorageReportProto::blockpoolused() const {
  return blockpoolused_;
}
inline void StorageReportProto::set_blockpoolused(::google::protobuf::uint64 value) {
  set_has_blockpoolused();
  blockpoolused_ = value;
}

// optional .hadoop.hdfs.DatanodeStorageProto storage = 7;
inline bool StorageReportProto::has_storage() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void StorageReportProto::set_has_storage() {
  _has_bits_[0] |= 0x00000040u;
}
inline void StorageReportProto::clear_has_storage() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void StorageReportProto::clear_storage() {
  if (storage_ != NULL) storage_->::hadoop::hdfs::DatanodeStorageProto::Clear();
  clear_has_storage();
}
inline const ::hadoop::hdfs::DatanodeStorageProto& StorageReportProto::storage() const {
  return storage_ != NULL ? *storage_ : *default_instance_->storage_;
}
inline ::hadoop::hdfs::DatanodeStorageProto* StorageReportProto::mutable_storage() {
  set_has_storage();
  if (storage_ == NULL) storage_ = new ::hadoop::hdfs::DatanodeStorageProto;
  return storage_;
}
inline ::hadoop::hdfs::DatanodeStorageProto* StorageReportProto::release_storage() {
  clear_has_storage();
  ::hadoop::hdfs::DatanodeStorageProto* temp = storage_;
  storage_ = NULL;
  return temp;
}
inline void StorageReportProto::set_allocated_storage(::hadoop::hdfs::DatanodeStorageProto* storage) {
  delete storage_;
  storage_ = storage;
  if (storage) {
    set_has_storage();
  } else {
    clear_has_storage();
  }
}

// -------------------------------------------------------------------

// ContentSummaryProto

// required uint64 length = 1;
inline bool ContentSummaryProto::has_length() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ContentSummaryProto::set_has_length() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ContentSummaryProto::clear_has_length() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ContentSummaryProto::clear_length() {
  length_ = GOOGLE_ULONGLONG(0);
  clear_has_length();
}
inline ::google::protobuf::uint64 ContentSummaryProto::length() const {
  return length_;
}
inline void ContentSummaryProto::set_length(::google::protobuf::uint64 value) {
  set_has_length();
  length_ = value;
}

// required uint64 fileCount = 2;
inline bool ContentSummaryProto::has_filecount() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ContentSummaryProto::set_has_filecount() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ContentSummaryProto::clear_has_filecount() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ContentSummaryProto::clear_filecount() {
  filecount_ = GOOGLE_ULONGLONG(0);
  clear_has_filecount();
}
inline ::google::protobuf::uint64 ContentSummaryProto::filecount() const {
  return filecount_;
}
inline void ContentSummaryProto::set_filecount(::google::protobuf::uint64 value) {
  set_has_filecount();
  filecount_ = value;
}

// required uint64 directoryCount = 3;
inline bool ContentSummaryProto::has_directorycount() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ContentSummaryProto::set_has_directorycount() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ContentSummaryProto::clear_has_directorycount() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ContentSummaryProto::clear_directorycount() {
  directorycount_ = GOOGLE_ULONGLONG(0);
  clear_has_directorycount();
}
inline ::google::protobuf::uint64 ContentSummaryProto::directorycount() const {
  return directorycount_;
}
inline void ContentSummaryProto::set_directorycount(::google::protobuf::uint64 value) {
  set_has_directorycount();
  directorycount_ = value;
}

// required uint64 quota = 4;
inline bool ContentSummaryProto::has_quota() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ContentSummaryProto::set_has_quota() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ContentSummaryProto::clear_has_quota() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ContentSummaryProto::clear_quota() {
  quota_ = GOOGLE_ULONGLONG(0);
  clear_has_quota();
}
inline ::google::protobuf::uint64 ContentSummaryProto::quota() const {
  return quota_;
}
inline void ContentSummaryProto::set_quota(::google::protobuf::uint64 value) {
  set_has_quota();
  quota_ = value;
}

// required uint64 spaceConsumed = 5;
inline bool ContentSummaryProto::has_spaceconsumed() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ContentSummaryProto::set_has_spaceconsumed() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ContentSummaryProto::clear_has_spaceconsumed() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ContentSummaryProto::clear_spaceconsumed() {
  spaceconsumed_ = GOOGLE_ULONGLONG(0);
  clear_has_spaceconsumed();
}
inline ::google::protobuf::uint64 ContentSummaryProto::spaceconsumed() const {
  return spaceconsumed_;
}
inline void ContentSummaryProto::set_spaceconsumed(::google::protobuf::uint64 value) {
  set_has_spaceconsumed();
  spaceconsumed_ = value;
}

// required uint64 spaceQuota = 6;
inline bool ContentSummaryProto::has_spacequota() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ContentSummaryProto::set_has_spacequota() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ContentSummaryProto::clear_has_spacequota() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ContentSummaryProto::clear_spacequota() {
  spacequota_ = GOOGLE_ULONGLONG(0);
  clear_has_spacequota();
}
inline ::google::protobuf::uint64 ContentSummaryProto::spacequota() const {
  return spacequota_;
}
inline void ContentSummaryProto::set_spacequota(::google::protobuf::uint64 value) {
  set_has_spacequota();
  spacequota_ = value;
}

// -------------------------------------------------------------------

// CorruptFileBlocksProto

// repeated string files = 1;
inline int CorruptFileBlocksProto::files_size() const {
  return files_.size();
}
inline void CorruptFileBlocksProto::clear_files() {
  files_.Clear();
}
inline const ::std::string& CorruptFileBlocksProto::files(int index) const {
  return files_.Get(index);
}
inline ::std::string* CorruptFileBlocksProto::mutable_files(int index) {
  return files_.Mutable(index);
}
inline void CorruptFileBlocksProto::set_files(int index, const ::std::string& value) {
  files_.Mutable(index)->assign(value);
}
inline void CorruptFileBlocksProto::set_files(int index, const char* value) {
  files_.Mutable(index)->assign(value);
}
inline void CorruptFileBlocksProto::set_files(int index, const char* value, size_t size) {
  files_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CorruptFileBlocksProto::add_files() {
  return files_.Add();
}
inline void CorruptFileBlocksProto::add_files(const ::std::string& value) {
  files_.Add()->assign(value);
}
inline void CorruptFileBlocksProto::add_files(const char* value) {
  files_.Add()->assign(value);
}
inline void CorruptFileBlocksProto::add_files(const char* value, size_t size) {
  files_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CorruptFileBlocksProto::files() const {
  return files_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CorruptFileBlocksProto::mutable_files() {
  return &files_;
}

// required string cookie = 2;
inline bool CorruptFileBlocksProto::has_cookie() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CorruptFileBlocksProto::set_has_cookie() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CorruptFileBlocksProto::clear_has_cookie() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CorruptFileBlocksProto::clear_cookie() {
  if (cookie_ != &::google::protobuf::internal::kEmptyString) {
    cookie_->clear();
  }
  clear_has_cookie();
}
inline const ::std::string& CorruptFileBlocksProto::cookie() const {
  return *cookie_;
}
inline void CorruptFileBlocksProto::set_cookie(const ::std::string& value) {
  set_has_cookie();
  if (cookie_ == &::google::protobuf::internal::kEmptyString) {
    cookie_ = new ::std::string;
  }
  cookie_->assign(value);
}
inline void CorruptFileBlocksProto::set_cookie(const char* value) {
  set_has_cookie();
  if (cookie_ == &::google::protobuf::internal::kEmptyString) {
    cookie_ = new ::std::string;
  }
  cookie_->assign(value);
}
inline void CorruptFileBlocksProto::set_cookie(const char* value, size_t size) {
  set_has_cookie();
  if (cookie_ == &::google::protobuf::internal::kEmptyString) {
    cookie_ = new ::std::string;
  }
  cookie_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CorruptFileBlocksProto::mutable_cookie() {
  set_has_cookie();
  if (cookie_ == &::google::protobuf::internal::kEmptyString) {
    cookie_ = new ::std::string;
  }
  return cookie_;
}
inline ::std::string* CorruptFileBlocksProto::release_cookie() {
  clear_has_cookie();
  if (cookie_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = cookie_;
    cookie_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CorruptFileBlocksProto::set_allocated_cookie(::std::string* cookie) {
  if (cookie_ != &::google::protobuf::internal::kEmptyString) {
    delete cookie_;
  }
  if (cookie) {
    set_has_cookie();
    cookie_ = cookie;
  } else {
    clear_has_cookie();
    cookie_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// FsPermissionProto

// required uint32 perm = 1;
inline bool FsPermissionProto::has_perm() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FsPermissionProto::set_has_perm() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FsPermissionProto::clear_has_perm() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FsPermissionProto::clear_perm() {
  perm_ = 0u;
  clear_has_perm();
}
inline ::google::protobuf::uint32 FsPermissionProto::perm() const {
  return perm_;
}
inline void FsPermissionProto::set_perm(::google::protobuf::uint32 value) {
  set_has_perm();
  perm_ = value;
}

// -------------------------------------------------------------------

// StorageTypesProto

// repeated .hadoop.hdfs.StorageTypeProto storageTypes = 1;
inline int StorageTypesProto::storagetypes_size() const {
  return storagetypes_.size();
}
inline void StorageTypesProto::clear_storagetypes() {
  storagetypes_.Clear();
}
inline ::hadoop::hdfs::StorageTypeProto StorageTypesProto::storagetypes(int index) const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(storagetypes_.Get(index));
}
inline void StorageTypesProto::set_storagetypes(int index, ::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  storagetypes_.Set(index, value);
}
inline void StorageTypesProto::add_storagetypes(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  storagetypes_.Add(value);
}
inline const ::google::protobuf::RepeatedField<int>&
StorageTypesProto::storagetypes() const {
  return storagetypes_;
}
inline ::google::protobuf::RepeatedField<int>*
StorageTypesProto::mutable_storagetypes() {
  return &storagetypes_;
}

// -------------------------------------------------------------------

// BlockStoragePolicyProto

// required uint32 policyId = 1;
inline bool BlockStoragePolicyProto::has_policyid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BlockStoragePolicyProto::set_has_policyid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BlockStoragePolicyProto::clear_has_policyid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BlockStoragePolicyProto::clear_policyid() {
  policyid_ = 0u;
  clear_has_policyid();
}
inline ::google::protobuf::uint32 BlockStoragePolicyProto::policyid() const {
  return policyid_;
}
inline void BlockStoragePolicyProto::set_policyid(::google::protobuf::uint32 value) {
  set_has_policyid();
  policyid_ = value;
}

// required string name = 2;
inline bool BlockStoragePolicyProto::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void BlockStoragePolicyProto::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void BlockStoragePolicyProto::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void BlockStoragePolicyProto::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& BlockStoragePolicyProto::name() const {
  return *name_;
}
inline void BlockStoragePolicyProto::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void BlockStoragePolicyProto::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void BlockStoragePolicyProto::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* BlockStoragePolicyProto::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* BlockStoragePolicyProto::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void BlockStoragePolicyProto::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.StorageTypesProto creationPolicy = 3;
inline bool BlockStoragePolicyProto::has_creationpolicy() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void BlockStoragePolicyProto::set_has_creationpolicy() {
  _has_bits_[0] |= 0x00000004u;
}
inline void BlockStoragePolicyProto::clear_has_creationpolicy() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void BlockStoragePolicyProto::clear_creationpolicy() {
  if (creationpolicy_ != NULL) creationpolicy_->::hadoop::hdfs::StorageTypesProto::Clear();
  clear_has_creationpolicy();
}
inline const ::hadoop::hdfs::StorageTypesProto& BlockStoragePolicyProto::creationpolicy() const {
  return creationpolicy_ != NULL ? *creationpolicy_ : *default_instance_->creationpolicy_;
}
inline ::hadoop::hdfs::StorageTypesProto* BlockStoragePolicyProto::mutable_creationpolicy() {
  set_has_creationpolicy();
  if (creationpolicy_ == NULL) creationpolicy_ = new ::hadoop::hdfs::StorageTypesProto;
  return creationpolicy_;
}
inline ::hadoop::hdfs::StorageTypesProto* BlockStoragePolicyProto::release_creationpolicy() {
  clear_has_creationpolicy();
  ::hadoop::hdfs::StorageTypesProto* temp = creationpolicy_;
  creationpolicy_ = NULL;
  return temp;
}
inline void BlockStoragePolicyProto::set_allocated_creationpolicy(::hadoop::hdfs::StorageTypesProto* creationpolicy) {
  delete creationpolicy_;
  creationpolicy_ = creationpolicy;
  if (creationpolicy) {
    set_has_creationpolicy();
  } else {
    clear_has_creationpolicy();
  }
}

// optional .hadoop.hdfs.StorageTypesProto creationFallbackPolicy = 4;
inline bool BlockStoragePolicyProto::has_creationfallbackpolicy() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void BlockStoragePolicyProto::set_has_creationfallbackpolicy() {
  _has_bits_[0] |= 0x00000008u;
}
inline void BlockStoragePolicyProto::clear_has_creationfallbackpolicy() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void BlockStoragePolicyProto::clear_creationfallbackpolicy() {
  if (creationfallbackpolicy_ != NULL) creationfallbackpolicy_->::hadoop::hdfs::StorageTypesProto::Clear();
  clear_has_creationfallbackpolicy();
}
inline const ::hadoop::hdfs::StorageTypesProto& BlockStoragePolicyProto::creationfallbackpolicy() const {
  return creationfallbackpolicy_ != NULL ? *creationfallbackpolicy_ : *default_instance_->creationfallbackpolicy_;
}
inline ::hadoop::hdfs::StorageTypesProto* BlockStoragePolicyProto::mutable_creationfallbackpolicy() {
  set_has_creationfallbackpolicy();
  if (creationfallbackpolicy_ == NULL) creationfallbackpolicy_ = new ::hadoop::hdfs::StorageTypesProto;
  return creationfallbackpolicy_;
}
inline ::hadoop::hdfs::StorageTypesProto* BlockStoragePolicyProto::release_creationfallbackpolicy() {
  clear_has_creationfallbackpolicy();
  ::hadoop::hdfs::StorageTypesProto* temp = creationfallbackpolicy_;
  creationfallbackpolicy_ = NULL;
  return temp;
}
inline void BlockStoragePolicyProto::set_allocated_creationfallbackpolicy(::hadoop::hdfs::StorageTypesProto* creationfallbackpolicy) {
  delete creationfallbackpolicy_;
  creationfallbackpolicy_ = creationfallbackpolicy;
  if (creationfallbackpolicy) {
    set_has_creationfallbackpolicy();
  } else {
    clear_has_creationfallbackpolicy();
  }
}

// optional .hadoop.hdfs.StorageTypesProto replicationFallbackPolicy = 5;
inline bool BlockStoragePolicyProto::has_replicationfallbackpolicy() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void BlockStoragePolicyProto::set_has_replicationfallbackpolicy() {
  _has_bits_[0] |= 0x00000010u;
}
inline void BlockStoragePolicyProto::clear_has_replicationfallbackpolicy() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void BlockStoragePolicyProto::clear_replicationfallbackpolicy() {
  if (replicationfallbackpolicy_ != NULL) replicationfallbackpolicy_->::hadoop::hdfs::StorageTypesProto::Clear();
  clear_has_replicationfallbackpolicy();
}
inline const ::hadoop::hdfs::StorageTypesProto& BlockStoragePolicyProto::replicationfallbackpolicy() const {
  return replicationfallbackpolicy_ != NULL ? *replicationfallbackpolicy_ : *default_instance_->replicationfallbackpolicy_;
}
inline ::hadoop::hdfs::StorageTypesProto* BlockStoragePolicyProto::mutable_replicationfallbackpolicy() {
  set_has_replicationfallbackpolicy();
  if (replicationfallbackpolicy_ == NULL) replicationfallbackpolicy_ = new ::hadoop::hdfs::StorageTypesProto;
  return replicationfallbackpolicy_;
}
inline ::hadoop::hdfs::StorageTypesProto* BlockStoragePolicyProto::release_replicationfallbackpolicy() {
  clear_has_replicationfallbackpolicy();
  ::hadoop::hdfs::StorageTypesProto* temp = replicationfallbackpolicy_;
  replicationfallbackpolicy_ = NULL;
  return temp;
}
inline void BlockStoragePolicyProto::set_allocated_replicationfallbackpolicy(::hadoop::hdfs::StorageTypesProto* replicationfallbackpolicy) {
  delete replicationfallbackpolicy_;
  replicationfallbackpolicy_ = replicationfallbackpolicy;
  if (replicationfallbackpolicy) {
    set_has_replicationfallbackpolicy();
  } else {
    clear_has_replicationfallbackpolicy();
  }
}

// -------------------------------------------------------------------

// StorageUuidsProto

// repeated string storageUuids = 1;
inline int StorageUuidsProto::storageuuids_size() const {
  return storageuuids_.size();
}
inline void StorageUuidsProto::clear_storageuuids() {
  storageuuids_.Clear();
}
inline const ::std::string& StorageUuidsProto::storageuuids(int index) const {
  return storageuuids_.Get(index);
}
inline ::std::string* StorageUuidsProto::mutable_storageuuids(int index) {
  return storageuuids_.Mutable(index);
}
inline void StorageUuidsProto::set_storageuuids(int index, const ::std::string& value) {
  storageuuids_.Mutable(index)->assign(value);
}
inline void StorageUuidsProto::set_storageuuids(int index, const char* value) {
  storageuuids_.Mutable(index)->assign(value);
}
inline void StorageUuidsProto::set_storageuuids(int index, const char* value, size_t size) {
  storageuuids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* StorageUuidsProto::add_storageuuids() {
  return storageuuids_.Add();
}
inline void StorageUuidsProto::add_storageuuids(const ::std::string& value) {
  storageuuids_.Add()->assign(value);
}
inline void StorageUuidsProto::add_storageuuids(const char* value) {
  storageuuids_.Add()->assign(value);
}
inline void StorageUuidsProto::add_storageuuids(const char* value, size_t size) {
  storageuuids_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
StorageUuidsProto::storageuuids() const {
  return storageuuids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
StorageUuidsProto::mutable_storageuuids() {
  return &storageuuids_;
}

// -------------------------------------------------------------------

// LocatedBlockProto

// required .hadoop.hdfs.ExtendedBlockProto b = 1;
inline bool LocatedBlockProto::has_b() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void LocatedBlockProto::set_has_b() {
  _has_bits_[0] |= 0x00000001u;
}
inline void LocatedBlockProto::clear_has_b() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void LocatedBlockProto::clear_b() {
  if (b_ != NULL) b_->::hadoop::hdfs::ExtendedBlockProto::Clear();
  clear_has_b();
}
inline const ::hadoop::hdfs::ExtendedBlockProto& LocatedBlockProto::b() const {
  return b_ != NULL ? *b_ : *default_instance_->b_;
}
inline ::hadoop::hdfs::ExtendedBlockProto* LocatedBlockProto::mutable_b() {
  set_has_b();
  if (b_ == NULL) b_ = new ::hadoop::hdfs::ExtendedBlockProto;
  return b_;
}
inline ::hadoop::hdfs::ExtendedBlockProto* LocatedBlockProto::release_b() {
  clear_has_b();
  ::hadoop::hdfs::ExtendedBlockProto* temp = b_;
  b_ = NULL;
  return temp;
}
inline void LocatedBlockProto::set_allocated_b(::hadoop::hdfs::ExtendedBlockProto* b) {
  delete b_;
  b_ = b;
  if (b) {
    set_has_b();
  } else {
    clear_has_b();
  }
}

// required uint64 offset = 2;
inline bool LocatedBlockProto::has_offset() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void LocatedBlockProto::set_has_offset() {
  _has_bits_[0] |= 0x00000002u;
}
inline void LocatedBlockProto::clear_has_offset() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void LocatedBlockProto::clear_offset() {
  offset_ = GOOGLE_ULONGLONG(0);
  clear_has_offset();
}
inline ::google::protobuf::uint64 LocatedBlockProto::offset() const {
  return offset_;
}
inline void LocatedBlockProto::set_offset(::google::protobuf::uint64 value) {
  set_has_offset();
  offset_ = value;
}

// repeated .hadoop.hdfs.DatanodeInfoProto locs = 3;
inline int LocatedBlockProto::locs_size() const {
  return locs_.size();
}
inline void LocatedBlockProto::clear_locs() {
  locs_.Clear();
}
inline const ::hadoop::hdfs::DatanodeInfoProto& LocatedBlockProto::locs(int index) const {
  return locs_.Get(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* LocatedBlockProto::mutable_locs(int index) {
  return locs_.Mutable(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* LocatedBlockProto::add_locs() {
  return locs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
LocatedBlockProto::locs() const {
  return locs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
LocatedBlockProto::mutable_locs() {
  return &locs_;
}

// required bool corrupt = 4;
inline bool LocatedBlockProto::has_corrupt() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void LocatedBlockProto::set_has_corrupt() {
  _has_bits_[0] |= 0x00000008u;
}
inline void LocatedBlockProto::clear_has_corrupt() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void LocatedBlockProto::clear_corrupt() {
  corrupt_ = false;
  clear_has_corrupt();
}
inline bool LocatedBlockProto::corrupt() const {
  return corrupt_;
}
inline void LocatedBlockProto::set_corrupt(bool value) {
  set_has_corrupt();
  corrupt_ = value;
}

// required .hadoop.common.TokenProto blockToken = 5;
inline bool LocatedBlockProto::has_blocktoken() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void LocatedBlockProto::set_has_blocktoken() {
  _has_bits_[0] |= 0x00000010u;
}
inline void LocatedBlockProto::clear_has_blocktoken() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void LocatedBlockProto::clear_blocktoken() {
  if (blocktoken_ != NULL) blocktoken_->::hadoop::common::TokenProto::Clear();
  clear_has_blocktoken();
}
inline const ::hadoop::common::TokenProto& LocatedBlockProto::blocktoken() const {
  return blocktoken_ != NULL ? *blocktoken_ : *default_instance_->blocktoken_;
}
inline ::hadoop::common::TokenProto* LocatedBlockProto::mutable_blocktoken() {
  set_has_blocktoken();
  if (blocktoken_ == NULL) blocktoken_ = new ::hadoop::common::TokenProto;
  return blocktoken_;
}
inline ::hadoop::common::TokenProto* LocatedBlockProto::release_blocktoken() {
  clear_has_blocktoken();
  ::hadoop::common::TokenProto* temp = blocktoken_;
  blocktoken_ = NULL;
  return temp;
}
inline void LocatedBlockProto::set_allocated_blocktoken(::hadoop::common::TokenProto* blocktoken) {
  delete blocktoken_;
  blocktoken_ = blocktoken;
  if (blocktoken) {
    set_has_blocktoken();
  } else {
    clear_has_blocktoken();
  }
}

// repeated bool isCached = 6 [packed = true];
inline int LocatedBlockProto::iscached_size() const {
  return iscached_.size();
}
inline void LocatedBlockProto::clear_iscached() {
  iscached_.Clear();
}
inline bool LocatedBlockProto::iscached(int index) const {
  return iscached_.Get(index);
}
inline void LocatedBlockProto::set_iscached(int index, bool value) {
  iscached_.Set(index, value);
}
inline void LocatedBlockProto::add_iscached(bool value) {
  iscached_.Add(value);
}
inline const ::google::protobuf::RepeatedField< bool >&
LocatedBlockProto::iscached() const {
  return iscached_;
}
inline ::google::protobuf::RepeatedField< bool >*
LocatedBlockProto::mutable_iscached() {
  return &iscached_;
}

// repeated .hadoop.hdfs.StorageTypeProto storageTypes = 7;
inline int LocatedBlockProto::storagetypes_size() const {
  return storagetypes_.size();
}
inline void LocatedBlockProto::clear_storagetypes() {
  storagetypes_.Clear();
}
inline ::hadoop::hdfs::StorageTypeProto LocatedBlockProto::storagetypes(int index) const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(storagetypes_.Get(index));
}
inline void LocatedBlockProto::set_storagetypes(int index, ::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  storagetypes_.Set(index, value);
}
inline void LocatedBlockProto::add_storagetypes(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  storagetypes_.Add(value);
}
inline const ::google::protobuf::RepeatedField<int>&
LocatedBlockProto::storagetypes() const {
  return storagetypes_;
}
inline ::google::protobuf::RepeatedField<int>*
LocatedBlockProto::mutable_storagetypes() {
  return &storagetypes_;
}

// repeated string storageIDs = 8;
inline int LocatedBlockProto::storageids_size() const {
  return storageids_.size();
}
inline void LocatedBlockProto::clear_storageids() {
  storageids_.Clear();
}
inline const ::std::string& LocatedBlockProto::storageids(int index) const {
  return storageids_.Get(index);
}
inline ::std::string* LocatedBlockProto::mutable_storageids(int index) {
  return storageids_.Mutable(index);
}
inline void LocatedBlockProto::set_storageids(int index, const ::std::string& value) {
  storageids_.Mutable(index)->assign(value);
}
inline void LocatedBlockProto::set_storageids(int index, const char* value) {
  storageids_.Mutable(index)->assign(value);
}
inline void LocatedBlockProto::set_storageids(int index, const char* value, size_t size) {
  storageids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* LocatedBlockProto::add_storageids() {
  return storageids_.Add();
}
inline void LocatedBlockProto::add_storageids(const ::std::string& value) {
  storageids_.Add()->assign(value);
}
inline void LocatedBlockProto::add_storageids(const char* value) {
  storageids_.Add()->assign(value);
}
inline void LocatedBlockProto::add_storageids(const char* value, size_t size) {
  storageids_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
LocatedBlockProto::storageids() const {
  return storageids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
LocatedBlockProto::mutable_storageids() {
  return &storageids_;
}

// -------------------------------------------------------------------

// DataEncryptionKeyProto

// required uint32 keyId = 1;
inline bool DataEncryptionKeyProto::has_keyid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DataEncryptionKeyProto::set_has_keyid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DataEncryptionKeyProto::clear_has_keyid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DataEncryptionKeyProto::clear_keyid() {
  keyid_ = 0u;
  clear_has_keyid();
}
inline ::google::protobuf::uint32 DataEncryptionKeyProto::keyid() const {
  return keyid_;
}
inline void DataEncryptionKeyProto::set_keyid(::google::protobuf::uint32 value) {
  set_has_keyid();
  keyid_ = value;
}

// required string blockPoolId = 2;
inline bool DataEncryptionKeyProto::has_blockpoolid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DataEncryptionKeyProto::set_has_blockpoolid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DataEncryptionKeyProto::clear_has_blockpoolid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DataEncryptionKeyProto::clear_blockpoolid() {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    blockpoolid_->clear();
  }
  clear_has_blockpoolid();
}
inline const ::std::string& DataEncryptionKeyProto::blockpoolid() const {
  return *blockpoolid_;
}
inline void DataEncryptionKeyProto::set_blockpoolid(const ::std::string& value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void DataEncryptionKeyProto::set_blockpoolid(const char* value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void DataEncryptionKeyProto::set_blockpoolid(const char* value, size_t size) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DataEncryptionKeyProto::mutable_blockpoolid() {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  return blockpoolid_;
}
inline ::std::string* DataEncryptionKeyProto::release_blockpoolid() {
  clear_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = blockpoolid_;
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DataEncryptionKeyProto::set_allocated_blockpoolid(::std::string* blockpoolid) {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    delete blockpoolid_;
  }
  if (blockpoolid) {
    set_has_blockpoolid();
    blockpoolid_ = blockpoolid;
  } else {
    clear_has_blockpoolid();
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required bytes nonce = 3;
inline bool DataEncryptionKeyProto::has_nonce() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DataEncryptionKeyProto::set_has_nonce() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DataEncryptionKeyProto::clear_has_nonce() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DataEncryptionKeyProto::clear_nonce() {
  if (nonce_ != &::google::protobuf::internal::kEmptyString) {
    nonce_->clear();
  }
  clear_has_nonce();
}
inline const ::std::string& DataEncryptionKeyProto::nonce() const {
  return *nonce_;
}
inline void DataEncryptionKeyProto::set_nonce(const ::std::string& value) {
  set_has_nonce();
  if (nonce_ == &::google::protobuf::internal::kEmptyString) {
    nonce_ = new ::std::string;
  }
  nonce_->assign(value);
}
inline void DataEncryptionKeyProto::set_nonce(const char* value) {
  set_has_nonce();
  if (nonce_ == &::google::protobuf::internal::kEmptyString) {
    nonce_ = new ::std::string;
  }
  nonce_->assign(value);
}
inline void DataEncryptionKeyProto::set_nonce(const void* value, size_t size) {
  set_has_nonce();
  if (nonce_ == &::google::protobuf::internal::kEmptyString) {
    nonce_ = new ::std::string;
  }
  nonce_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DataEncryptionKeyProto::mutable_nonce() {
  set_has_nonce();
  if (nonce_ == &::google::protobuf::internal::kEmptyString) {
    nonce_ = new ::std::string;
  }
  return nonce_;
}
inline ::std::string* DataEncryptionKeyProto::release_nonce() {
  clear_has_nonce();
  if (nonce_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = nonce_;
    nonce_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DataEncryptionKeyProto::set_allocated_nonce(::std::string* nonce) {
  if (nonce_ != &::google::protobuf::internal::kEmptyString) {
    delete nonce_;
  }
  if (nonce) {
    set_has_nonce();
    nonce_ = nonce;
  } else {
    clear_has_nonce();
    nonce_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required bytes encryptionKey = 4;
inline bool DataEncryptionKeyProto::has_encryptionkey() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void DataEncryptionKeyProto::set_has_encryptionkey() {
  _has_bits_[0] |= 0x00000008u;
}
inline void DataEncryptionKeyProto::clear_has_encryptionkey() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void DataEncryptionKeyProto::clear_encryptionkey() {
  if (encryptionkey_ != &::google::protobuf::internal::kEmptyString) {
    encryptionkey_->clear();
  }
  clear_has_encryptionkey();
}
inline const ::std::string& DataEncryptionKeyProto::encryptionkey() const {
  return *encryptionkey_;
}
inline void DataEncryptionKeyProto::set_encryptionkey(const ::std::string& value) {
  set_has_encryptionkey();
  if (encryptionkey_ == &::google::protobuf::internal::kEmptyString) {
    encryptionkey_ = new ::std::string;
  }
  encryptionkey_->assign(value);
}
inline void DataEncryptionKeyProto::set_encryptionkey(const char* value) {
  set_has_encryptionkey();
  if (encryptionkey_ == &::google::protobuf::internal::kEmptyString) {
    encryptionkey_ = new ::std::string;
  }
  encryptionkey_->assign(value);
}
inline void DataEncryptionKeyProto::set_encryptionkey(const void* value, size_t size) {
  set_has_encryptionkey();
  if (encryptionkey_ == &::google::protobuf::internal::kEmptyString) {
    encryptionkey_ = new ::std::string;
  }
  encryptionkey_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DataEncryptionKeyProto::mutable_encryptionkey() {
  set_has_encryptionkey();
  if (encryptionkey_ == &::google::protobuf::internal::kEmptyString) {
    encryptionkey_ = new ::std::string;
  }
  return encryptionkey_;
}
inline ::std::string* DataEncryptionKeyProto::release_encryptionkey() {
  clear_has_encryptionkey();
  if (encryptionkey_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = encryptionkey_;
    encryptionkey_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DataEncryptionKeyProto::set_allocated_encryptionkey(::std::string* encryptionkey) {
  if (encryptionkey_ != &::google::protobuf::internal::kEmptyString) {
    delete encryptionkey_;
  }
  if (encryptionkey) {
    set_has_encryptionkey();
    encryptionkey_ = encryptionkey;
  } else {
    clear_has_encryptionkey();
    encryptionkey_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 expiryDate = 5;
inline bool DataEncryptionKeyProto::has_expirydate() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void DataEncryptionKeyProto::set_has_expirydate() {
  _has_bits_[0] |= 0x00000010u;
}
inline void DataEncryptionKeyProto::clear_has_expirydate() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void DataEncryptionKeyProto::clear_expirydate() {
  expirydate_ = GOOGLE_ULONGLONG(0);
  clear_has_expirydate();
}
inline ::google::protobuf::uint64 DataEncryptionKeyProto::expirydate() const {
  return expirydate_;
}
inline void DataEncryptionKeyProto::set_expirydate(::google::protobuf::uint64 value) {
  set_has_expirydate();
  expirydate_ = value;
}

// optional string encryptionAlgorithm = 6;
inline bool DataEncryptionKeyProto::has_encryptionalgorithm() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void DataEncryptionKeyProto::set_has_encryptionalgorithm() {
  _has_bits_[0] |= 0x00000020u;
}
inline void DataEncryptionKeyProto::clear_has_encryptionalgorithm() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void DataEncryptionKeyProto::clear_encryptionalgorithm() {
  if (encryptionalgorithm_ != &::google::protobuf::internal::kEmptyString) {
    encryptionalgorithm_->clear();
  }
  clear_has_encryptionalgorithm();
}
inline const ::std::string& DataEncryptionKeyProto::encryptionalgorithm() const {
  return *encryptionalgorithm_;
}
inline void DataEncryptionKeyProto::set_encryptionalgorithm(const ::std::string& value) {
  set_has_encryptionalgorithm();
  if (encryptionalgorithm_ == &::google::protobuf::internal::kEmptyString) {
    encryptionalgorithm_ = new ::std::string;
  }
  encryptionalgorithm_->assign(value);
}
inline void DataEncryptionKeyProto::set_encryptionalgorithm(const char* value) {
  set_has_encryptionalgorithm();
  if (encryptionalgorithm_ == &::google::protobuf::internal::kEmptyString) {
    encryptionalgorithm_ = new ::std::string;
  }
  encryptionalgorithm_->assign(value);
}
inline void DataEncryptionKeyProto::set_encryptionalgorithm(const char* value, size_t size) {
  set_has_encryptionalgorithm();
  if (encryptionalgorithm_ == &::google::protobuf::internal::kEmptyString) {
    encryptionalgorithm_ = new ::std::string;
  }
  encryptionalgorithm_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DataEncryptionKeyProto::mutable_encryptionalgorithm() {
  set_has_encryptionalgorithm();
  if (encryptionalgorithm_ == &::google::protobuf::internal::kEmptyString) {
    encryptionalgorithm_ = new ::std::string;
  }
  return encryptionalgorithm_;
}
inline ::std::string* DataEncryptionKeyProto::release_encryptionalgorithm() {
  clear_has_encryptionalgorithm();
  if (encryptionalgorithm_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = encryptionalgorithm_;
    encryptionalgorithm_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DataEncryptionKeyProto::set_allocated_encryptionalgorithm(::std::string* encryptionalgorithm) {
  if (encryptionalgorithm_ != &::google::protobuf::internal::kEmptyString) {
    delete encryptionalgorithm_;
  }
  if (encryptionalgorithm) {
    set_has_encryptionalgorithm();
    encryptionalgorithm_ = encryptionalgorithm;
  } else {
    clear_has_encryptionalgorithm();
    encryptionalgorithm_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// FileEncryptionInfoProto

// required .hadoop.hdfs.CipherSuiteProto suite = 1;
inline bool FileEncryptionInfoProto::has_suite() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FileEncryptionInfoProto::set_has_suite() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FileEncryptionInfoProto::clear_has_suite() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FileEncryptionInfoProto::clear_suite() {
  suite_ = 1;
  clear_has_suite();
}
inline ::hadoop::hdfs::CipherSuiteProto FileEncryptionInfoProto::suite() const {
  return static_cast< ::hadoop::hdfs::CipherSuiteProto >(suite_);
}
inline void FileEncryptionInfoProto::set_suite(::hadoop::hdfs::CipherSuiteProto value) {
  assert(::hadoop::hdfs::CipherSuiteProto_IsValid(value));
  set_has_suite();
  suite_ = value;
}

// required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 2;
inline bool FileEncryptionInfoProto::has_cryptoprotocolversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FileEncryptionInfoProto::set_has_cryptoprotocolversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FileEncryptionInfoProto::clear_has_cryptoprotocolversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FileEncryptionInfoProto::clear_cryptoprotocolversion() {
  cryptoprotocolversion_ = 1;
  clear_has_cryptoprotocolversion();
}
inline ::hadoop::hdfs::CryptoProtocolVersionProto FileEncryptionInfoProto::cryptoprotocolversion() const {
  return static_cast< ::hadoop::hdfs::CryptoProtocolVersionProto >(cryptoprotocolversion_);
}
inline void FileEncryptionInfoProto::set_cryptoprotocolversion(::hadoop::hdfs::CryptoProtocolVersionProto value) {
  assert(::hadoop::hdfs::CryptoProtocolVersionProto_IsValid(value));
  set_has_cryptoprotocolversion();
  cryptoprotocolversion_ = value;
}

// required bytes key = 3;
inline bool FileEncryptionInfoProto::has_key() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FileEncryptionInfoProto::set_has_key() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FileEncryptionInfoProto::clear_has_key() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FileEncryptionInfoProto::clear_key() {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    key_->clear();
  }
  clear_has_key();
}
inline const ::std::string& FileEncryptionInfoProto::key() const {
  return *key_;
}
inline void FileEncryptionInfoProto::set_key(const ::std::string& value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void FileEncryptionInfoProto::set_key(const char* value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void FileEncryptionInfoProto::set_key(const void* value, size_t size) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FileEncryptionInfoProto::mutable_key() {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  return key_;
}
inline ::std::string* FileEncryptionInfoProto::release_key() {
  clear_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = key_;
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FileEncryptionInfoProto::set_allocated_key(::std::string* key) {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    delete key_;
  }
  if (key) {
    set_has_key();
    key_ = key;
  } else {
    clear_has_key();
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required bytes iv = 4;
inline bool FileEncryptionInfoProto::has_iv() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void FileEncryptionInfoProto::set_has_iv() {
  _has_bits_[0] |= 0x00000008u;
}
inline void FileEncryptionInfoProto::clear_has_iv() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void FileEncryptionInfoProto::clear_iv() {
  if (iv_ != &::google::protobuf::internal::kEmptyString) {
    iv_->clear();
  }
  clear_has_iv();
}
inline const ::std::string& FileEncryptionInfoProto::iv() const {
  return *iv_;
}
inline void FileEncryptionInfoProto::set_iv(const ::std::string& value) {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  iv_->assign(value);
}
inline void FileEncryptionInfoProto::set_iv(const char* value) {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  iv_->assign(value);
}
inline void FileEncryptionInfoProto::set_iv(const void* value, size_t size) {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  iv_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FileEncryptionInfoProto::mutable_iv() {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  return iv_;
}
inline ::std::string* FileEncryptionInfoProto::release_iv() {
  clear_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = iv_;
    iv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FileEncryptionInfoProto::set_allocated_iv(::std::string* iv) {
  if (iv_ != &::google::protobuf::internal::kEmptyString) {
    delete iv_;
  }
  if (iv) {
    set_has_iv();
    iv_ = iv;
  } else {
    clear_has_iv();
    iv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string keyName = 5;
inline bool FileEncryptionInfoProto::has_keyname() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void FileEncryptionInfoProto::set_has_keyname() {
  _has_bits_[0] |= 0x00000010u;
}
inline void FileEncryptionInfoProto::clear_has_keyname() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void FileEncryptionInfoProto::clear_keyname() {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    keyname_->clear();
  }
  clear_has_keyname();
}
inline const ::std::string& FileEncryptionInfoProto::keyname() const {
  return *keyname_;
}
inline void FileEncryptionInfoProto::set_keyname(const ::std::string& value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void FileEncryptionInfoProto::set_keyname(const char* value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void FileEncryptionInfoProto::set_keyname(const char* value, size_t size) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FileEncryptionInfoProto::mutable_keyname() {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  return keyname_;
}
inline ::std::string* FileEncryptionInfoProto::release_keyname() {
  clear_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = keyname_;
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FileEncryptionInfoProto::set_allocated_keyname(::std::string* keyname) {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    delete keyname_;
  }
  if (keyname) {
    set_has_keyname();
    keyname_ = keyname;
  } else {
    clear_has_keyname();
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string ezKeyVersionName = 6;
inline bool FileEncryptionInfoProto::has_ezkeyversionname() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void FileEncryptionInfoProto::set_has_ezkeyversionname() {
  _has_bits_[0] |= 0x00000020u;
}
inline void FileEncryptionInfoProto::clear_has_ezkeyversionname() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void FileEncryptionInfoProto::clear_ezkeyversionname() {
  if (ezkeyversionname_ != &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_->clear();
  }
  clear_has_ezkeyversionname();
}
inline const ::std::string& FileEncryptionInfoProto::ezkeyversionname() const {
  return *ezkeyversionname_;
}
inline void FileEncryptionInfoProto::set_ezkeyversionname(const ::std::string& value) {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  ezkeyversionname_->assign(value);
}
inline void FileEncryptionInfoProto::set_ezkeyversionname(const char* value) {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  ezkeyversionname_->assign(value);
}
inline void FileEncryptionInfoProto::set_ezkeyversionname(const char* value, size_t size) {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  ezkeyversionname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FileEncryptionInfoProto::mutable_ezkeyversionname() {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  return ezkeyversionname_;
}
inline ::std::string* FileEncryptionInfoProto::release_ezkeyversionname() {
  clear_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = ezkeyversionname_;
    ezkeyversionname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FileEncryptionInfoProto::set_allocated_ezkeyversionname(::std::string* ezkeyversionname) {
  if (ezkeyversionname_ != &::google::protobuf::internal::kEmptyString) {
    delete ezkeyversionname_;
  }
  if (ezkeyversionname) {
    set_has_ezkeyversionname();
    ezkeyversionname_ = ezkeyversionname;
  } else {
    clear_has_ezkeyversionname();
    ezkeyversionname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// PerFileEncryptionInfoProto

// required bytes key = 1;
inline bool PerFileEncryptionInfoProto::has_key() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PerFileEncryptionInfoProto::set_has_key() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PerFileEncryptionInfoProto::clear_has_key() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PerFileEncryptionInfoProto::clear_key() {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    key_->clear();
  }
  clear_has_key();
}
inline const ::std::string& PerFileEncryptionInfoProto::key() const {
  return *key_;
}
inline void PerFileEncryptionInfoProto::set_key(const ::std::string& value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void PerFileEncryptionInfoProto::set_key(const char* value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void PerFileEncryptionInfoProto::set_key(const void* value, size_t size) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* PerFileEncryptionInfoProto::mutable_key() {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  return key_;
}
inline ::std::string* PerFileEncryptionInfoProto::release_key() {
  clear_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = key_;
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void PerFileEncryptionInfoProto::set_allocated_key(::std::string* key) {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    delete key_;
  }
  if (key) {
    set_has_key();
    key_ = key;
  } else {
    clear_has_key();
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required bytes iv = 2;
inline bool PerFileEncryptionInfoProto::has_iv() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void PerFileEncryptionInfoProto::set_has_iv() {
  _has_bits_[0] |= 0x00000002u;
}
inline void PerFileEncryptionInfoProto::clear_has_iv() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void PerFileEncryptionInfoProto::clear_iv() {
  if (iv_ != &::google::protobuf::internal::kEmptyString) {
    iv_->clear();
  }
  clear_has_iv();
}
inline const ::std::string& PerFileEncryptionInfoProto::iv() const {
  return *iv_;
}
inline void PerFileEncryptionInfoProto::set_iv(const ::std::string& value) {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  iv_->assign(value);
}
inline void PerFileEncryptionInfoProto::set_iv(const char* value) {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  iv_->assign(value);
}
inline void PerFileEncryptionInfoProto::set_iv(const void* value, size_t size) {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  iv_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* PerFileEncryptionInfoProto::mutable_iv() {
  set_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    iv_ = new ::std::string;
  }
  return iv_;
}
inline ::std::string* PerFileEncryptionInfoProto::release_iv() {
  clear_has_iv();
  if (iv_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = iv_;
    iv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void PerFileEncryptionInfoProto::set_allocated_iv(::std::string* iv) {
  if (iv_ != &::google::protobuf::internal::kEmptyString) {
    delete iv_;
  }
  if (iv) {
    set_has_iv();
    iv_ = iv;
  } else {
    clear_has_iv();
    iv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string ezKeyVersionName = 3;
inline bool PerFileEncryptionInfoProto::has_ezkeyversionname() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void PerFileEncryptionInfoProto::set_has_ezkeyversionname() {
  _has_bits_[0] |= 0x00000004u;
}
inline void PerFileEncryptionInfoProto::clear_has_ezkeyversionname() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void PerFileEncryptionInfoProto::clear_ezkeyversionname() {
  if (ezkeyversionname_ != &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_->clear();
  }
  clear_has_ezkeyversionname();
}
inline const ::std::string& PerFileEncryptionInfoProto::ezkeyversionname() const {
  return *ezkeyversionname_;
}
inline void PerFileEncryptionInfoProto::set_ezkeyversionname(const ::std::string& value) {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  ezkeyversionname_->assign(value);
}
inline void PerFileEncryptionInfoProto::set_ezkeyversionname(const char* value) {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  ezkeyversionname_->assign(value);
}
inline void PerFileEncryptionInfoProto::set_ezkeyversionname(const char* value, size_t size) {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  ezkeyversionname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* PerFileEncryptionInfoProto::mutable_ezkeyversionname() {
  set_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    ezkeyversionname_ = new ::std::string;
  }
  return ezkeyversionname_;
}
inline ::std::string* PerFileEncryptionInfoProto::release_ezkeyversionname() {
  clear_has_ezkeyversionname();
  if (ezkeyversionname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = ezkeyversionname_;
    ezkeyversionname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void PerFileEncryptionInfoProto::set_allocated_ezkeyversionname(::std::string* ezkeyversionname) {
  if (ezkeyversionname_ != &::google::protobuf::internal::kEmptyString) {
    delete ezkeyversionname_;
  }
  if (ezkeyversionname) {
    set_has_ezkeyversionname();
    ezkeyversionname_ = ezkeyversionname;
  } else {
    clear_has_ezkeyversionname();
    ezkeyversionname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ZoneEncryptionInfoProto

// required .hadoop.hdfs.CipherSuiteProto suite = 1;
inline bool ZoneEncryptionInfoProto::has_suite() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ZoneEncryptionInfoProto::set_has_suite() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ZoneEncryptionInfoProto::clear_has_suite() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ZoneEncryptionInfoProto::clear_suite() {
  suite_ = 1;
  clear_has_suite();
}
inline ::hadoop::hdfs::CipherSuiteProto ZoneEncryptionInfoProto::suite() const {
  return static_cast< ::hadoop::hdfs::CipherSuiteProto >(suite_);
}
inline void ZoneEncryptionInfoProto::set_suite(::hadoop::hdfs::CipherSuiteProto value) {
  assert(::hadoop::hdfs::CipherSuiteProto_IsValid(value));
  set_has_suite();
  suite_ = value;
}

// required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 2;
inline bool ZoneEncryptionInfoProto::has_cryptoprotocolversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ZoneEncryptionInfoProto::set_has_cryptoprotocolversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ZoneEncryptionInfoProto::clear_has_cryptoprotocolversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ZoneEncryptionInfoProto::clear_cryptoprotocolversion() {
  cryptoprotocolversion_ = 1;
  clear_has_cryptoprotocolversion();
}
inline ::hadoop::hdfs::CryptoProtocolVersionProto ZoneEncryptionInfoProto::cryptoprotocolversion() const {
  return static_cast< ::hadoop::hdfs::CryptoProtocolVersionProto >(cryptoprotocolversion_);
}
inline void ZoneEncryptionInfoProto::set_cryptoprotocolversion(::hadoop::hdfs::CryptoProtocolVersionProto value) {
  assert(::hadoop::hdfs::CryptoProtocolVersionProto_IsValid(value));
  set_has_cryptoprotocolversion();
  cryptoprotocolversion_ = value;
}

// required string keyName = 3;
inline bool ZoneEncryptionInfoProto::has_keyname() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ZoneEncryptionInfoProto::set_has_keyname() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ZoneEncryptionInfoProto::clear_has_keyname() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ZoneEncryptionInfoProto::clear_keyname() {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    keyname_->clear();
  }
  clear_has_keyname();
}
inline const ::std::string& ZoneEncryptionInfoProto::keyname() const {
  return *keyname_;
}
inline void ZoneEncryptionInfoProto::set_keyname(const ::std::string& value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void ZoneEncryptionInfoProto::set_keyname(const char* value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void ZoneEncryptionInfoProto::set_keyname(const char* value, size_t size) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ZoneEncryptionInfoProto::mutable_keyname() {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  return keyname_;
}
inline ::std::string* ZoneEncryptionInfoProto::release_keyname() {
  clear_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = keyname_;
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ZoneEncryptionInfoProto::set_allocated_keyname(::std::string* keyname) {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    delete keyname_;
  }
  if (keyname) {
    set_has_keyname();
    keyname_ = keyname;
  } else {
    clear_has_keyname();
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// CipherOptionProto

// required .hadoop.hdfs.CipherSuiteProto suite = 1;
inline bool CipherOptionProto::has_suite() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CipherOptionProto::set_has_suite() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CipherOptionProto::clear_has_suite() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CipherOptionProto::clear_suite() {
  suite_ = 1;
  clear_has_suite();
}
inline ::hadoop::hdfs::CipherSuiteProto CipherOptionProto::suite() const {
  return static_cast< ::hadoop::hdfs::CipherSuiteProto >(suite_);
}
inline void CipherOptionProto::set_suite(::hadoop::hdfs::CipherSuiteProto value) {
  assert(::hadoop::hdfs::CipherSuiteProto_IsValid(value));
  set_has_suite();
  suite_ = value;
}

// optional bytes inKey = 2;
inline bool CipherOptionProto::has_inkey() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CipherOptionProto::set_has_inkey() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CipherOptionProto::clear_has_inkey() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CipherOptionProto::clear_inkey() {
  if (inkey_ != &::google::protobuf::internal::kEmptyString) {
    inkey_->clear();
  }
  clear_has_inkey();
}
inline const ::std::string& CipherOptionProto::inkey() const {
  return *inkey_;
}
inline void CipherOptionProto::set_inkey(const ::std::string& value) {
  set_has_inkey();
  if (inkey_ == &::google::protobuf::internal::kEmptyString) {
    inkey_ = new ::std::string;
  }
  inkey_->assign(value);
}
inline void CipherOptionProto::set_inkey(const char* value) {
  set_has_inkey();
  if (inkey_ == &::google::protobuf::internal::kEmptyString) {
    inkey_ = new ::std::string;
  }
  inkey_->assign(value);
}
inline void CipherOptionProto::set_inkey(const void* value, size_t size) {
  set_has_inkey();
  if (inkey_ == &::google::protobuf::internal::kEmptyString) {
    inkey_ = new ::std::string;
  }
  inkey_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CipherOptionProto::mutable_inkey() {
  set_has_inkey();
  if (inkey_ == &::google::protobuf::internal::kEmptyString) {
    inkey_ = new ::std::string;
  }
  return inkey_;
}
inline ::std::string* CipherOptionProto::release_inkey() {
  clear_has_inkey();
  if (inkey_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = inkey_;
    inkey_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CipherOptionProto::set_allocated_inkey(::std::string* inkey) {
  if (inkey_ != &::google::protobuf::internal::kEmptyString) {
    delete inkey_;
  }
  if (inkey) {
    set_has_inkey();
    inkey_ = inkey;
  } else {
    clear_has_inkey();
    inkey_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bytes inIv = 3;
inline bool CipherOptionProto::has_iniv() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void CipherOptionProto::set_has_iniv() {
  _has_bits_[0] |= 0x00000004u;
}
inline void CipherOptionProto::clear_has_iniv() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void CipherOptionProto::clear_iniv() {
  if (iniv_ != &::google::protobuf::internal::kEmptyString) {
    iniv_->clear();
  }
  clear_has_iniv();
}
inline const ::std::string& CipherOptionProto::iniv() const {
  return *iniv_;
}
inline void CipherOptionProto::set_iniv(const ::std::string& value) {
  set_has_iniv();
  if (iniv_ == &::google::protobuf::internal::kEmptyString) {
    iniv_ = new ::std::string;
  }
  iniv_->assign(value);
}
inline void CipherOptionProto::set_iniv(const char* value) {
  set_has_iniv();
  if (iniv_ == &::google::protobuf::internal::kEmptyString) {
    iniv_ = new ::std::string;
  }
  iniv_->assign(value);
}
inline void CipherOptionProto::set_iniv(const void* value, size_t size) {
  set_has_iniv();
  if (iniv_ == &::google::protobuf::internal::kEmptyString) {
    iniv_ = new ::std::string;
  }
  iniv_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CipherOptionProto::mutable_iniv() {
  set_has_iniv();
  if (iniv_ == &::google::protobuf::internal::kEmptyString) {
    iniv_ = new ::std::string;
  }
  return iniv_;
}
inline ::std::string* CipherOptionProto::release_iniv() {
  clear_has_iniv();
  if (iniv_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = iniv_;
    iniv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CipherOptionProto::set_allocated_iniv(::std::string* iniv) {
  if (iniv_ != &::google::protobuf::internal::kEmptyString) {
    delete iniv_;
  }
  if (iniv) {
    set_has_iniv();
    iniv_ = iniv;
  } else {
    clear_has_iniv();
    iniv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bytes outKey = 4;
inline bool CipherOptionProto::has_outkey() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void CipherOptionProto::set_has_outkey() {
  _has_bits_[0] |= 0x00000008u;
}
inline void CipherOptionProto::clear_has_outkey() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void CipherOptionProto::clear_outkey() {
  if (outkey_ != &::google::protobuf::internal::kEmptyString) {
    outkey_->clear();
  }
  clear_has_outkey();
}
inline const ::std::string& CipherOptionProto::outkey() const {
  return *outkey_;
}
inline void CipherOptionProto::set_outkey(const ::std::string& value) {
  set_has_outkey();
  if (outkey_ == &::google::protobuf::internal::kEmptyString) {
    outkey_ = new ::std::string;
  }
  outkey_->assign(value);
}
inline void CipherOptionProto::set_outkey(const char* value) {
  set_has_outkey();
  if (outkey_ == &::google::protobuf::internal::kEmptyString) {
    outkey_ = new ::std::string;
  }
  outkey_->assign(value);
}
inline void CipherOptionProto::set_outkey(const void* value, size_t size) {
  set_has_outkey();
  if (outkey_ == &::google::protobuf::internal::kEmptyString) {
    outkey_ = new ::std::string;
  }
  outkey_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CipherOptionProto::mutable_outkey() {
  set_has_outkey();
  if (outkey_ == &::google::protobuf::internal::kEmptyString) {
    outkey_ = new ::std::string;
  }
  return outkey_;
}
inline ::std::string* CipherOptionProto::release_outkey() {
  clear_has_outkey();
  if (outkey_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = outkey_;
    outkey_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CipherOptionProto::set_allocated_outkey(::std::string* outkey) {
  if (outkey_ != &::google::protobuf::internal::kEmptyString) {
    delete outkey_;
  }
  if (outkey) {
    set_has_outkey();
    outkey_ = outkey;
  } else {
    clear_has_outkey();
    outkey_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bytes outIv = 5;
inline bool CipherOptionProto::has_outiv() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void CipherOptionProto::set_has_outiv() {
  _has_bits_[0] |= 0x00000010u;
}
inline void CipherOptionProto::clear_has_outiv() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void CipherOptionProto::clear_outiv() {
  if (outiv_ != &::google::protobuf::internal::kEmptyString) {
    outiv_->clear();
  }
  clear_has_outiv();
}
inline const ::std::string& CipherOptionProto::outiv() const {
  return *outiv_;
}
inline void CipherOptionProto::set_outiv(const ::std::string& value) {
  set_has_outiv();
  if (outiv_ == &::google::protobuf::internal::kEmptyString) {
    outiv_ = new ::std::string;
  }
  outiv_->assign(value);
}
inline void CipherOptionProto::set_outiv(const char* value) {
  set_has_outiv();
  if (outiv_ == &::google::protobuf::internal::kEmptyString) {
    outiv_ = new ::std::string;
  }
  outiv_->assign(value);
}
inline void CipherOptionProto::set_outiv(const void* value, size_t size) {
  set_has_outiv();
  if (outiv_ == &::google::protobuf::internal::kEmptyString) {
    outiv_ = new ::std::string;
  }
  outiv_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CipherOptionProto::mutable_outiv() {
  set_has_outiv();
  if (outiv_ == &::google::protobuf::internal::kEmptyString) {
    outiv_ = new ::std::string;
  }
  return outiv_;
}
inline ::std::string* CipherOptionProto::release_outiv() {
  clear_has_outiv();
  if (outiv_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = outiv_;
    outiv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CipherOptionProto::set_allocated_outiv(::std::string* outiv) {
  if (outiv_ != &::google::protobuf::internal::kEmptyString) {
    delete outiv_;
  }
  if (outiv) {
    set_has_outiv();
    outiv_ = outiv;
  } else {
    clear_has_outiv();
    outiv_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// LocatedBlocksProto

// required uint64 fileLength = 1;
inline bool LocatedBlocksProto::has_filelength() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void LocatedBlocksProto::set_has_filelength() {
  _has_bits_[0] |= 0x00000001u;
}
inline void LocatedBlocksProto::clear_has_filelength() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void LocatedBlocksProto::clear_filelength() {
  filelength_ = GOOGLE_ULONGLONG(0);
  clear_has_filelength();
}
inline ::google::protobuf::uint64 LocatedBlocksProto::filelength() const {
  return filelength_;
}
inline void LocatedBlocksProto::set_filelength(::google::protobuf::uint64 value) {
  set_has_filelength();
  filelength_ = value;
}

// repeated .hadoop.hdfs.LocatedBlockProto blocks = 2;
inline int LocatedBlocksProto::blocks_size() const {
  return blocks_.size();
}
inline void LocatedBlocksProto::clear_blocks() {
  blocks_.Clear();
}
inline const ::hadoop::hdfs::LocatedBlockProto& LocatedBlocksProto::blocks(int index) const {
  return blocks_.Get(index);
}
inline ::hadoop::hdfs::LocatedBlockProto* LocatedBlocksProto::mutable_blocks(int index) {
  return blocks_.Mutable(index);
}
inline ::hadoop::hdfs::LocatedBlockProto* LocatedBlocksProto::add_blocks() {
  return blocks_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::LocatedBlockProto >&
LocatedBlocksProto::blocks() const {
  return blocks_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::LocatedBlockProto >*
LocatedBlocksProto::mutable_blocks() {
  return &blocks_;
}

// required bool underConstruction = 3;
inline bool LocatedBlocksProto::has_underconstruction() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void LocatedBlocksProto::set_has_underconstruction() {
  _has_bits_[0] |= 0x00000004u;
}
inline void LocatedBlocksProto::clear_has_underconstruction() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void LocatedBlocksProto::clear_underconstruction() {
  underconstruction_ = false;
  clear_has_underconstruction();
}
inline bool LocatedBlocksProto::underconstruction() const {
  return underconstruction_;
}
inline void LocatedBlocksProto::set_underconstruction(bool value) {
  set_has_underconstruction();
  underconstruction_ = value;
}

// optional .hadoop.hdfs.LocatedBlockProto lastBlock = 4;
inline bool LocatedBlocksProto::has_lastblock() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void LocatedBlocksProto::set_has_lastblock() {
  _has_bits_[0] |= 0x00000008u;
}
inline void LocatedBlocksProto::clear_has_lastblock() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void LocatedBlocksProto::clear_lastblock() {
  if (lastblock_ != NULL) lastblock_->::hadoop::hdfs::LocatedBlockProto::Clear();
  clear_has_lastblock();
}
inline const ::hadoop::hdfs::LocatedBlockProto& LocatedBlocksProto::lastblock() const {
  return lastblock_ != NULL ? *lastblock_ : *default_instance_->lastblock_;
}
inline ::hadoop::hdfs::LocatedBlockProto* LocatedBlocksProto::mutable_lastblock() {
  set_has_lastblock();
  if (lastblock_ == NULL) lastblock_ = new ::hadoop::hdfs::LocatedBlockProto;
  return lastblock_;
}
inline ::hadoop::hdfs::LocatedBlockProto* LocatedBlocksProto::release_lastblock() {
  clear_has_lastblock();
  ::hadoop::hdfs::LocatedBlockProto* temp = lastblock_;
  lastblock_ = NULL;
  return temp;
}
inline void LocatedBlocksProto::set_allocated_lastblock(::hadoop::hdfs::LocatedBlockProto* lastblock) {
  delete lastblock_;
  lastblock_ = lastblock;
  if (lastblock) {
    set_has_lastblock();
  } else {
    clear_has_lastblock();
  }
}

// required bool isLastBlockComplete = 5;
inline bool LocatedBlocksProto::has_islastblockcomplete() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void LocatedBlocksProto::set_has_islastblockcomplete() {
  _has_bits_[0] |= 0x00000010u;
}
inline void LocatedBlocksProto::clear_has_islastblockcomplete() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void LocatedBlocksProto::clear_islastblockcomplete() {
  islastblockcomplete_ = false;
  clear_has_islastblockcomplete();
}
inline bool LocatedBlocksProto::islastblockcomplete() const {
  return islastblockcomplete_;
}
inline void LocatedBlocksProto::set_islastblockcomplete(bool value) {
  set_has_islastblockcomplete();
  islastblockcomplete_ = value;
}

// optional .hadoop.hdfs.FileEncryptionInfoProto fileEncryptionInfo = 6;
inline bool LocatedBlocksProto::has_fileencryptioninfo() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void LocatedBlocksProto::set_has_fileencryptioninfo() {
  _has_bits_[0] |= 0x00000020u;
}
inline void LocatedBlocksProto::clear_has_fileencryptioninfo() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void LocatedBlocksProto::clear_fileencryptioninfo() {
  if (fileencryptioninfo_ != NULL) fileencryptioninfo_->::hadoop::hdfs::FileEncryptionInfoProto::Clear();
  clear_has_fileencryptioninfo();
}
inline const ::hadoop::hdfs::FileEncryptionInfoProto& LocatedBlocksProto::fileencryptioninfo() const {
  return fileencryptioninfo_ != NULL ? *fileencryptioninfo_ : *default_instance_->fileencryptioninfo_;
}
inline ::hadoop::hdfs::FileEncryptionInfoProto* LocatedBlocksProto::mutable_fileencryptioninfo() {
  set_has_fileencryptioninfo();
  if (fileencryptioninfo_ == NULL) fileencryptioninfo_ = new ::hadoop::hdfs::FileEncryptionInfoProto;
  return fileencryptioninfo_;
}
inline ::hadoop::hdfs::FileEncryptionInfoProto* LocatedBlocksProto::release_fileencryptioninfo() {
  clear_has_fileencryptioninfo();
  ::hadoop::hdfs::FileEncryptionInfoProto* temp = fileencryptioninfo_;
  fileencryptioninfo_ = NULL;
  return temp;
}
inline void LocatedBlocksProto::set_allocated_fileencryptioninfo(::hadoop::hdfs::FileEncryptionInfoProto* fileencryptioninfo) {
  delete fileencryptioninfo_;
  fileencryptioninfo_ = fileencryptioninfo;
  if (fileencryptioninfo) {
    set_has_fileencryptioninfo();
  } else {
    clear_has_fileencryptioninfo();
  }
}

// -------------------------------------------------------------------

// HdfsFileStatusProto

// required .hadoop.hdfs.HdfsFileStatusProto.FileType fileType = 1;
inline bool HdfsFileStatusProto::has_filetype() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void HdfsFileStatusProto::set_has_filetype() {
  _has_bits_[0] |= 0x00000001u;
}
inline void HdfsFileStatusProto::clear_has_filetype() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void HdfsFileStatusProto::clear_filetype() {
  filetype_ = 1;
  clear_has_filetype();
}
inline ::hadoop::hdfs::HdfsFileStatusProto_FileType HdfsFileStatusProto::filetype() const {
  return static_cast< ::hadoop::hdfs::HdfsFileStatusProto_FileType >(filetype_);
}
inline void HdfsFileStatusProto::set_filetype(::hadoop::hdfs::HdfsFileStatusProto_FileType value) {
  assert(::hadoop::hdfs::HdfsFileStatusProto_FileType_IsValid(value));
  set_has_filetype();
  filetype_ = value;
}

// required bytes path = 2;
inline bool HdfsFileStatusProto::has_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void HdfsFileStatusProto::set_has_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void HdfsFileStatusProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void HdfsFileStatusProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& HdfsFileStatusProto::path() const {
  return *path_;
}
inline void HdfsFileStatusProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void HdfsFileStatusProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void HdfsFileStatusProto::set_path(const void* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* HdfsFileStatusProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* HdfsFileStatusProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void HdfsFileStatusProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 length = 3;
inline bool HdfsFileStatusProto::has_length() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void HdfsFileStatusProto::set_has_length() {
  _has_bits_[0] |= 0x00000004u;
}
inline void HdfsFileStatusProto::clear_has_length() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void HdfsFileStatusProto::clear_length() {
  length_ = GOOGLE_ULONGLONG(0);
  clear_has_length();
}
inline ::google::protobuf::uint64 HdfsFileStatusProto::length() const {
  return length_;
}
inline void HdfsFileStatusProto::set_length(::google::protobuf::uint64 value) {
  set_has_length();
  length_ = value;
}

// required .hadoop.hdfs.FsPermissionProto permission = 4;
inline bool HdfsFileStatusProto::has_permission() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void HdfsFileStatusProto::set_has_permission() {
  _has_bits_[0] |= 0x00000008u;
}
inline void HdfsFileStatusProto::clear_has_permission() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void HdfsFileStatusProto::clear_permission() {
  if (permission_ != NULL) permission_->::hadoop::hdfs::FsPermissionProto::Clear();
  clear_has_permission();
}
inline const ::hadoop::hdfs::FsPermissionProto& HdfsFileStatusProto::permission() const {
  return permission_ != NULL ? *permission_ : *default_instance_->permission_;
}
inline ::hadoop::hdfs::FsPermissionProto* HdfsFileStatusProto::mutable_permission() {
  set_has_permission();
  if (permission_ == NULL) permission_ = new ::hadoop::hdfs::FsPermissionProto;
  return permission_;
}
inline ::hadoop::hdfs::FsPermissionProto* HdfsFileStatusProto::release_permission() {
  clear_has_permission();
  ::hadoop::hdfs::FsPermissionProto* temp = permission_;
  permission_ = NULL;
  return temp;
}
inline void HdfsFileStatusProto::set_allocated_permission(::hadoop::hdfs::FsPermissionProto* permission) {
  delete permission_;
  permission_ = permission;
  if (permission) {
    set_has_permission();
  } else {
    clear_has_permission();
  }
}

// required string owner = 5;
inline bool HdfsFileStatusProto::has_owner() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void HdfsFileStatusProto::set_has_owner() {
  _has_bits_[0] |= 0x00000010u;
}
inline void HdfsFileStatusProto::clear_has_owner() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void HdfsFileStatusProto::clear_owner() {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    owner_->clear();
  }
  clear_has_owner();
}
inline const ::std::string& HdfsFileStatusProto::owner() const {
  return *owner_;
}
inline void HdfsFileStatusProto::set_owner(const ::std::string& value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void HdfsFileStatusProto::set_owner(const char* value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void HdfsFileStatusProto::set_owner(const char* value, size_t size) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* HdfsFileStatusProto::mutable_owner() {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  return owner_;
}
inline ::std::string* HdfsFileStatusProto::release_owner() {
  clear_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = owner_;
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void HdfsFileStatusProto::set_allocated_owner(::std::string* owner) {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    delete owner_;
  }
  if (owner) {
    set_has_owner();
    owner_ = owner;
  } else {
    clear_has_owner();
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string group = 6;
inline bool HdfsFileStatusProto::has_group() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void HdfsFileStatusProto::set_has_group() {
  _has_bits_[0] |= 0x00000020u;
}
inline void HdfsFileStatusProto::clear_has_group() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void HdfsFileStatusProto::clear_group() {
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    group_->clear();
  }
  clear_has_group();
}
inline const ::std::string& HdfsFileStatusProto::group() const {
  return *group_;
}
inline void HdfsFileStatusProto::set_group(const ::std::string& value) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(value);
}
inline void HdfsFileStatusProto::set_group(const char* value) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(value);
}
inline void HdfsFileStatusProto::set_group(const char* value, size_t size) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* HdfsFileStatusProto::mutable_group() {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  return group_;
}
inline ::std::string* HdfsFileStatusProto::release_group() {
  clear_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = group_;
    group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void HdfsFileStatusProto::set_allocated_group(::std::string* group) {
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    delete group_;
  }
  if (group) {
    set_has_group();
    group_ = group;
  } else {
    clear_has_group();
    group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 modification_time = 7;
inline bool HdfsFileStatusProto::has_modification_time() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void HdfsFileStatusProto::set_has_modification_time() {
  _has_bits_[0] |= 0x00000040u;
}
inline void HdfsFileStatusProto::clear_has_modification_time() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void HdfsFileStatusProto::clear_modification_time() {
  modification_time_ = GOOGLE_ULONGLONG(0);
  clear_has_modification_time();
}
inline ::google::protobuf::uint64 HdfsFileStatusProto::modification_time() const {
  return modification_time_;
}
inline void HdfsFileStatusProto::set_modification_time(::google::protobuf::uint64 value) {
  set_has_modification_time();
  modification_time_ = value;
}

// required uint64 access_time = 8;
inline bool HdfsFileStatusProto::has_access_time() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void HdfsFileStatusProto::set_has_access_time() {
  _has_bits_[0] |= 0x00000080u;
}
inline void HdfsFileStatusProto::clear_has_access_time() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void HdfsFileStatusProto::clear_access_time() {
  access_time_ = GOOGLE_ULONGLONG(0);
  clear_has_access_time();
}
inline ::google::protobuf::uint64 HdfsFileStatusProto::access_time() const {
  return access_time_;
}
inline void HdfsFileStatusProto::set_access_time(::google::protobuf::uint64 value) {
  set_has_access_time();
  access_time_ = value;
}

// optional bytes symlink = 9;
inline bool HdfsFileStatusProto::has_symlink() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void HdfsFileStatusProto::set_has_symlink() {
  _has_bits_[0] |= 0x00000100u;
}
inline void HdfsFileStatusProto::clear_has_symlink() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void HdfsFileStatusProto::clear_symlink() {
  if (symlink_ != &::google::protobuf::internal::kEmptyString) {
    symlink_->clear();
  }
  clear_has_symlink();
}
inline const ::std::string& HdfsFileStatusProto::symlink() const {
  return *symlink_;
}
inline void HdfsFileStatusProto::set_symlink(const ::std::string& value) {
  set_has_symlink();
  if (symlink_ == &::google::protobuf::internal::kEmptyString) {
    symlink_ = new ::std::string;
  }
  symlink_->assign(value);
}
inline void HdfsFileStatusProto::set_symlink(const char* value) {
  set_has_symlink();
  if (symlink_ == &::google::protobuf::internal::kEmptyString) {
    symlink_ = new ::std::string;
  }
  symlink_->assign(value);
}
inline void HdfsFileStatusProto::set_symlink(const void* value, size_t size) {
  set_has_symlink();
  if (symlink_ == &::google::protobuf::internal::kEmptyString) {
    symlink_ = new ::std::string;
  }
  symlink_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* HdfsFileStatusProto::mutable_symlink() {
  set_has_symlink();
  if (symlink_ == &::google::protobuf::internal::kEmptyString) {
    symlink_ = new ::std::string;
  }
  return symlink_;
}
inline ::std::string* HdfsFileStatusProto::release_symlink() {
  clear_has_symlink();
  if (symlink_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = symlink_;
    symlink_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void HdfsFileStatusProto::set_allocated_symlink(::std::string* symlink) {
  if (symlink_ != &::google::protobuf::internal::kEmptyString) {
    delete symlink_;
  }
  if (symlink) {
    set_has_symlink();
    symlink_ = symlink;
  } else {
    clear_has_symlink();
    symlink_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint32 block_replication = 10 [default = 0];
inline bool HdfsFileStatusProto::has_block_replication() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void HdfsFileStatusProto::set_has_block_replication() {
  _has_bits_[0] |= 0x00000200u;
}
inline void HdfsFileStatusProto::clear_has_block_replication() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void HdfsFileStatusProto::clear_block_replication() {
  block_replication_ = 0u;
  clear_has_block_replication();
}
inline ::google::protobuf::uint32 HdfsFileStatusProto::block_replication() const {
  return block_replication_;
}
inline void HdfsFileStatusProto::set_block_replication(::google::protobuf::uint32 value) {
  set_has_block_replication();
  block_replication_ = value;
}

// optional uint64 blocksize = 11 [default = 0];
inline bool HdfsFileStatusProto::has_blocksize() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void HdfsFileStatusProto::set_has_blocksize() {
  _has_bits_[0] |= 0x00000400u;
}
inline void HdfsFileStatusProto::clear_has_blocksize() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void HdfsFileStatusProto::clear_blocksize() {
  blocksize_ = GOOGLE_ULONGLONG(0);
  clear_has_blocksize();
}
inline ::google::protobuf::uint64 HdfsFileStatusProto::blocksize() const {
  return blocksize_;
}
inline void HdfsFileStatusProto::set_blocksize(::google::protobuf::uint64 value) {
  set_has_blocksize();
  blocksize_ = value;
}

// optional .hadoop.hdfs.LocatedBlocksProto locations = 12;
inline bool HdfsFileStatusProto::has_locations() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void HdfsFileStatusProto::set_has_locations() {
  _has_bits_[0] |= 0x00000800u;
}
inline void HdfsFileStatusProto::clear_has_locations() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void HdfsFileStatusProto::clear_locations() {
  if (locations_ != NULL) locations_->::hadoop::hdfs::LocatedBlocksProto::Clear();
  clear_has_locations();
}
inline const ::hadoop::hdfs::LocatedBlocksProto& HdfsFileStatusProto::locations() const {
  return locations_ != NULL ? *locations_ : *default_instance_->locations_;
}
inline ::hadoop::hdfs::LocatedBlocksProto* HdfsFileStatusProto::mutable_locations() {
  set_has_locations();
  if (locations_ == NULL) locations_ = new ::hadoop::hdfs::LocatedBlocksProto;
  return locations_;
}
inline ::hadoop::hdfs::LocatedBlocksProto* HdfsFileStatusProto::release_locations() {
  clear_has_locations();
  ::hadoop::hdfs::LocatedBlocksProto* temp = locations_;
  locations_ = NULL;
  return temp;
}
inline void HdfsFileStatusProto::set_allocated_locations(::hadoop::hdfs::LocatedBlocksProto* locations) {
  delete locations_;
  locations_ = locations;
  if (locations) {
    set_has_locations();
  } else {
    clear_has_locations();
  }
}

// optional uint64 fileId = 13 [default = 0];
inline bool HdfsFileStatusProto::has_fileid() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void HdfsFileStatusProto::set_has_fileid() {
  _has_bits_[0] |= 0x00001000u;
}
inline void HdfsFileStatusProto::clear_has_fileid() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void HdfsFileStatusProto::clear_fileid() {
  fileid_ = GOOGLE_ULONGLONG(0);
  clear_has_fileid();
}
inline ::google::protobuf::uint64 HdfsFileStatusProto::fileid() const {
  return fileid_;
}
inline void HdfsFileStatusProto::set_fileid(::google::protobuf::uint64 value) {
  set_has_fileid();
  fileid_ = value;
}

// optional int32 childrenNum = 14 [default = -1];
inline bool HdfsFileStatusProto::has_childrennum() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void HdfsFileStatusProto::set_has_childrennum() {
  _has_bits_[0] |= 0x00002000u;
}
inline void HdfsFileStatusProto::clear_has_childrennum() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void HdfsFileStatusProto::clear_childrennum() {
  childrennum_ = -1;
  clear_has_childrennum();
}
inline ::google::protobuf::int32 HdfsFileStatusProto::childrennum() const {
  return childrennum_;
}
inline void HdfsFileStatusProto::set_childrennum(::google::protobuf::int32 value) {
  set_has_childrennum();
  childrennum_ = value;
}

// optional .hadoop.hdfs.FileEncryptionInfoProto fileEncryptionInfo = 15;
inline bool HdfsFileStatusProto::has_fileencryptioninfo() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void HdfsFileStatusProto::set_has_fileencryptioninfo() {
  _has_bits_[0] |= 0x00004000u;
}
inline void HdfsFileStatusProto::clear_has_fileencryptioninfo() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void HdfsFileStatusProto::clear_fileencryptioninfo() {
  if (fileencryptioninfo_ != NULL) fileencryptioninfo_->::hadoop::hdfs::FileEncryptionInfoProto::Clear();
  clear_has_fileencryptioninfo();
}
inline const ::hadoop::hdfs::FileEncryptionInfoProto& HdfsFileStatusProto::fileencryptioninfo() const {
  return fileencryptioninfo_ != NULL ? *fileencryptioninfo_ : *default_instance_->fileencryptioninfo_;
}
inline ::hadoop::hdfs::FileEncryptionInfoProto* HdfsFileStatusProto::mutable_fileencryptioninfo() {
  set_has_fileencryptioninfo();
  if (fileencryptioninfo_ == NULL) fileencryptioninfo_ = new ::hadoop::hdfs::FileEncryptionInfoProto;
  return fileencryptioninfo_;
}
inline ::hadoop::hdfs::FileEncryptionInfoProto* HdfsFileStatusProto::release_fileencryptioninfo() {
  clear_has_fileencryptioninfo();
  ::hadoop::hdfs::FileEncryptionInfoProto* temp = fileencryptioninfo_;
  fileencryptioninfo_ = NULL;
  return temp;
}
inline void HdfsFileStatusProto::set_allocated_fileencryptioninfo(::hadoop::hdfs::FileEncryptionInfoProto* fileencryptioninfo) {
  delete fileencryptioninfo_;
  fileencryptioninfo_ = fileencryptioninfo;
  if (fileencryptioninfo) {
    set_has_fileencryptioninfo();
  } else {
    clear_has_fileencryptioninfo();
  }
}

// optional uint32 storagePolicy = 16 [default = 0];
inline bool HdfsFileStatusProto::has_storagepolicy() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void HdfsFileStatusProto::set_has_storagepolicy() {
  _has_bits_[0] |= 0x00008000u;
}
inline void HdfsFileStatusProto::clear_has_storagepolicy() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void HdfsFileStatusProto::clear_storagepolicy() {
  storagepolicy_ = 0u;
  clear_has_storagepolicy();
}
inline ::google::protobuf::uint32 HdfsFileStatusProto::storagepolicy() const {
  return storagepolicy_;
}
inline void HdfsFileStatusProto::set_storagepolicy(::google::protobuf::uint32 value) {
  set_has_storagepolicy();
  storagepolicy_ = value;
}

// -------------------------------------------------------------------

// FsServerDefaultsProto

// required uint64 blockSize = 1;
inline bool FsServerDefaultsProto::has_blocksize() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FsServerDefaultsProto::set_has_blocksize() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FsServerDefaultsProto::clear_has_blocksize() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FsServerDefaultsProto::clear_blocksize() {
  blocksize_ = GOOGLE_ULONGLONG(0);
  clear_has_blocksize();
}
inline ::google::protobuf::uint64 FsServerDefaultsProto::blocksize() const {
  return blocksize_;
}
inline void FsServerDefaultsProto::set_blocksize(::google::protobuf::uint64 value) {
  set_has_blocksize();
  blocksize_ = value;
}

// required uint32 bytesPerChecksum = 2;
inline bool FsServerDefaultsProto::has_bytesperchecksum() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FsServerDefaultsProto::set_has_bytesperchecksum() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FsServerDefaultsProto::clear_has_bytesperchecksum() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FsServerDefaultsProto::clear_bytesperchecksum() {
  bytesperchecksum_ = 0u;
  clear_has_bytesperchecksum();
}
inline ::google::protobuf::uint32 FsServerDefaultsProto::bytesperchecksum() const {
  return bytesperchecksum_;
}
inline void FsServerDefaultsProto::set_bytesperchecksum(::google::protobuf::uint32 value) {
  set_has_bytesperchecksum();
  bytesperchecksum_ = value;
}

// required uint32 writePacketSize = 3;
inline bool FsServerDefaultsProto::has_writepacketsize() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FsServerDefaultsProto::set_has_writepacketsize() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FsServerDefaultsProto::clear_has_writepacketsize() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FsServerDefaultsProto::clear_writepacketsize() {
  writepacketsize_ = 0u;
  clear_has_writepacketsize();
}
inline ::google::protobuf::uint32 FsServerDefaultsProto::writepacketsize() const {
  return writepacketsize_;
}
inline void FsServerDefaultsProto::set_writepacketsize(::google::protobuf::uint32 value) {
  set_has_writepacketsize();
  writepacketsize_ = value;
}

// required uint32 replication = 4;
inline bool FsServerDefaultsProto::has_replication() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void FsServerDefaultsProto::set_has_replication() {
  _has_bits_[0] |= 0x00000008u;
}
inline void FsServerDefaultsProto::clear_has_replication() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void FsServerDefaultsProto::clear_replication() {
  replication_ = 0u;
  clear_has_replication();
}
inline ::google::protobuf::uint32 FsServerDefaultsProto::replication() const {
  return replication_;
}
inline void FsServerDefaultsProto::set_replication(::google::protobuf::uint32 value) {
  set_has_replication();
  replication_ = value;
}

// required uint32 fileBufferSize = 5;
inline bool FsServerDefaultsProto::has_filebuffersize() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void FsServerDefaultsProto::set_has_filebuffersize() {
  _has_bits_[0] |= 0x00000010u;
}
inline void FsServerDefaultsProto::clear_has_filebuffersize() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void FsServerDefaultsProto::clear_filebuffersize() {
  filebuffersize_ = 0u;
  clear_has_filebuffersize();
}
inline ::google::protobuf::uint32 FsServerDefaultsProto::filebuffersize() const {
  return filebuffersize_;
}
inline void FsServerDefaultsProto::set_filebuffersize(::google::protobuf::uint32 value) {
  set_has_filebuffersize();
  filebuffersize_ = value;
}

// optional bool encryptDataTransfer = 6 [default = false];
inline bool FsServerDefaultsProto::has_encryptdatatransfer() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void FsServerDefaultsProto::set_has_encryptdatatransfer() {
  _has_bits_[0] |= 0x00000020u;
}
inline void FsServerDefaultsProto::clear_has_encryptdatatransfer() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void FsServerDefaultsProto::clear_encryptdatatransfer() {
  encryptdatatransfer_ = false;
  clear_has_encryptdatatransfer();
}
inline bool FsServerDefaultsProto::encryptdatatransfer() const {
  return encryptdatatransfer_;
}
inline void FsServerDefaultsProto::set_encryptdatatransfer(bool value) {
  set_has_encryptdatatransfer();
  encryptdatatransfer_ = value;
}

// optional uint64 trashInterval = 7 [default = 0];
inline bool FsServerDefaultsProto::has_trashinterval() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void FsServerDefaultsProto::set_has_trashinterval() {
  _has_bits_[0] |= 0x00000040u;
}
inline void FsServerDefaultsProto::clear_has_trashinterval() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void FsServerDefaultsProto::clear_trashinterval() {
  trashinterval_ = GOOGLE_ULONGLONG(0);
  clear_has_trashinterval();
}
inline ::google::protobuf::uint64 FsServerDefaultsProto::trashinterval() const {
  return trashinterval_;
}
inline void FsServerDefaultsProto::set_trashinterval(::google::protobuf::uint64 value) {
  set_has_trashinterval();
  trashinterval_ = value;
}

// optional .hadoop.hdfs.ChecksumTypeProto checksumType = 8 [default = CHECKSUM_CRC32];
inline bool FsServerDefaultsProto::has_checksumtype() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void FsServerDefaultsProto::set_has_checksumtype() {
  _has_bits_[0] |= 0x00000080u;
}
inline void FsServerDefaultsProto::clear_has_checksumtype() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void FsServerDefaultsProto::clear_checksumtype() {
  checksumtype_ = 1;
  clear_has_checksumtype();
}
inline ::hadoop::hdfs::ChecksumTypeProto FsServerDefaultsProto::checksumtype() const {
  return static_cast< ::hadoop::hdfs::ChecksumTypeProto >(checksumtype_);
}
inline void FsServerDefaultsProto::set_checksumtype(::hadoop::hdfs::ChecksumTypeProto value) {
  assert(::hadoop::hdfs::ChecksumTypeProto_IsValid(value));
  set_has_checksumtype();
  checksumtype_ = value;
}

// -------------------------------------------------------------------

// DirectoryListingProto

// repeated .hadoop.hdfs.HdfsFileStatusProto partialListing = 1;
inline int DirectoryListingProto::partiallisting_size() const {
  return partiallisting_.size();
}
inline void DirectoryListingProto::clear_partiallisting() {
  partiallisting_.Clear();
}
inline const ::hadoop::hdfs::HdfsFileStatusProto& DirectoryListingProto::partiallisting(int index) const {
  return partiallisting_.Get(index);
}
inline ::hadoop::hdfs::HdfsFileStatusProto* DirectoryListingProto::mutable_partiallisting(int index) {
  return partiallisting_.Mutable(index);
}
inline ::hadoop::hdfs::HdfsFileStatusProto* DirectoryListingProto::add_partiallisting() {
  return partiallisting_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::HdfsFileStatusProto >&
DirectoryListingProto::partiallisting() const {
  return partiallisting_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::HdfsFileStatusProto >*
DirectoryListingProto::mutable_partiallisting() {
  return &partiallisting_;
}

// required uint32 remainingEntries = 2;
inline bool DirectoryListingProto::has_remainingentries() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DirectoryListingProto::set_has_remainingentries() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DirectoryListingProto::clear_has_remainingentries() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DirectoryListingProto::clear_remainingentries() {
  remainingentries_ = 0u;
  clear_has_remainingentries();
}
inline ::google::protobuf::uint32 DirectoryListingProto::remainingentries() const {
  return remainingentries_;
}
inline void DirectoryListingProto::set_remainingentries(::google::protobuf::uint32 value) {
  set_has_remainingentries();
  remainingentries_ = value;
}

// -------------------------------------------------------------------

// SnapshottableDirectoryStatusProto

// required .hadoop.hdfs.HdfsFileStatusProto dirStatus = 1;
inline bool SnapshottableDirectoryStatusProto::has_dirstatus() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshottableDirectoryStatusProto::set_has_dirstatus() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshottableDirectoryStatusProto::clear_has_dirstatus() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshottableDirectoryStatusProto::clear_dirstatus() {
  if (dirstatus_ != NULL) dirstatus_->::hadoop::hdfs::HdfsFileStatusProto::Clear();
  clear_has_dirstatus();
}
inline const ::hadoop::hdfs::HdfsFileStatusProto& SnapshottableDirectoryStatusProto::dirstatus() const {
  return dirstatus_ != NULL ? *dirstatus_ : *default_instance_->dirstatus_;
}
inline ::hadoop::hdfs::HdfsFileStatusProto* SnapshottableDirectoryStatusProto::mutable_dirstatus() {
  set_has_dirstatus();
  if (dirstatus_ == NULL) dirstatus_ = new ::hadoop::hdfs::HdfsFileStatusProto;
  return dirstatus_;
}
inline ::hadoop::hdfs::HdfsFileStatusProto* SnapshottableDirectoryStatusProto::release_dirstatus() {
  clear_has_dirstatus();
  ::hadoop::hdfs::HdfsFileStatusProto* temp = dirstatus_;
  dirstatus_ = NULL;
  return temp;
}
inline void SnapshottableDirectoryStatusProto::set_allocated_dirstatus(::hadoop::hdfs::HdfsFileStatusProto* dirstatus) {
  delete dirstatus_;
  dirstatus_ = dirstatus;
  if (dirstatus) {
    set_has_dirstatus();
  } else {
    clear_has_dirstatus();
  }
}

// required uint32 snapshot_quota = 2;
inline bool SnapshottableDirectoryStatusProto::has_snapshot_quota() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshottableDirectoryStatusProto::set_has_snapshot_quota() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshottableDirectoryStatusProto::clear_has_snapshot_quota() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshottableDirectoryStatusProto::clear_snapshot_quota() {
  snapshot_quota_ = 0u;
  clear_has_snapshot_quota();
}
inline ::google::protobuf::uint32 SnapshottableDirectoryStatusProto::snapshot_quota() const {
  return snapshot_quota_;
}
inline void SnapshottableDirectoryStatusProto::set_snapshot_quota(::google::protobuf::uint32 value) {
  set_has_snapshot_quota();
  snapshot_quota_ = value;
}

// required uint32 snapshot_number = 3;
inline bool SnapshottableDirectoryStatusProto::has_snapshot_number() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshottableDirectoryStatusProto::set_has_snapshot_number() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshottableDirectoryStatusProto::clear_has_snapshot_number() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshottableDirectoryStatusProto::clear_snapshot_number() {
  snapshot_number_ = 0u;
  clear_has_snapshot_number();
}
inline ::google::protobuf::uint32 SnapshottableDirectoryStatusProto::snapshot_number() const {
  return snapshot_number_;
}
inline void SnapshottableDirectoryStatusProto::set_snapshot_number(::google::protobuf::uint32 value) {
  set_has_snapshot_number();
  snapshot_number_ = value;
}

// required bytes parent_fullpath = 4;
inline bool SnapshottableDirectoryStatusProto::has_parent_fullpath() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void SnapshottableDirectoryStatusProto::set_has_parent_fullpath() {
  _has_bits_[0] |= 0x00000008u;
}
inline void SnapshottableDirectoryStatusProto::clear_has_parent_fullpath() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void SnapshottableDirectoryStatusProto::clear_parent_fullpath() {
  if (parent_fullpath_ != &::google::protobuf::internal::kEmptyString) {
    parent_fullpath_->clear();
  }
  clear_has_parent_fullpath();
}
inline const ::std::string& SnapshottableDirectoryStatusProto::parent_fullpath() const {
  return *parent_fullpath_;
}
inline void SnapshottableDirectoryStatusProto::set_parent_fullpath(const ::std::string& value) {
  set_has_parent_fullpath();
  if (parent_fullpath_ == &::google::protobuf::internal::kEmptyString) {
    parent_fullpath_ = new ::std::string;
  }
  parent_fullpath_->assign(value);
}
inline void SnapshottableDirectoryStatusProto::set_parent_fullpath(const char* value) {
  set_has_parent_fullpath();
  if (parent_fullpath_ == &::google::protobuf::internal::kEmptyString) {
    parent_fullpath_ = new ::std::string;
  }
  parent_fullpath_->assign(value);
}
inline void SnapshottableDirectoryStatusProto::set_parent_fullpath(const void* value, size_t size) {
  set_has_parent_fullpath();
  if (parent_fullpath_ == &::google::protobuf::internal::kEmptyString) {
    parent_fullpath_ = new ::std::string;
  }
  parent_fullpath_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshottableDirectoryStatusProto::mutable_parent_fullpath() {
  set_has_parent_fullpath();
  if (parent_fullpath_ == &::google::protobuf::internal::kEmptyString) {
    parent_fullpath_ = new ::std::string;
  }
  return parent_fullpath_;
}
inline ::std::string* SnapshottableDirectoryStatusProto::release_parent_fullpath() {
  clear_has_parent_fullpath();
  if (parent_fullpath_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = parent_fullpath_;
    parent_fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshottableDirectoryStatusProto::set_allocated_parent_fullpath(::std::string* parent_fullpath) {
  if (parent_fullpath_ != &::google::protobuf::internal::kEmptyString) {
    delete parent_fullpath_;
  }
  if (parent_fullpath) {
    set_has_parent_fullpath();
    parent_fullpath_ = parent_fullpath;
  } else {
    clear_has_parent_fullpath();
    parent_fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// SnapshottableDirectoryListingProto

// repeated .hadoop.hdfs.SnapshottableDirectoryStatusProto snapshottableDirListing = 1;
inline int SnapshottableDirectoryListingProto::snapshottabledirlisting_size() const {
  return snapshottabledirlisting_.size();
}
inline void SnapshottableDirectoryListingProto::clear_snapshottabledirlisting() {
  snapshottabledirlisting_.Clear();
}
inline const ::hadoop::hdfs::SnapshottableDirectoryStatusProto& SnapshottableDirectoryListingProto::snapshottabledirlisting(int index) const {
  return snapshottabledirlisting_.Get(index);
}
inline ::hadoop::hdfs::SnapshottableDirectoryStatusProto* SnapshottableDirectoryListingProto::mutable_snapshottabledirlisting(int index) {
  return snapshottabledirlisting_.Mutable(index);
}
inline ::hadoop::hdfs::SnapshottableDirectoryStatusProto* SnapshottableDirectoryListingProto::add_snapshottabledirlisting() {
  return snapshottabledirlisting_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshottableDirectoryStatusProto >&
SnapshottableDirectoryListingProto::snapshottabledirlisting() const {
  return snapshottabledirlisting_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshottableDirectoryStatusProto >*
SnapshottableDirectoryListingProto::mutable_snapshottabledirlisting() {
  return &snapshottabledirlisting_;
}

// -------------------------------------------------------------------

// SnapshotDiffReportEntryProto

// required bytes fullpath = 1;
inline bool SnapshotDiffReportEntryProto::has_fullpath() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotDiffReportEntryProto::set_has_fullpath() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotDiffReportEntryProto::clear_has_fullpath() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotDiffReportEntryProto::clear_fullpath() {
  if (fullpath_ != &::google::protobuf::internal::kEmptyString) {
    fullpath_->clear();
  }
  clear_has_fullpath();
}
inline const ::std::string& SnapshotDiffReportEntryProto::fullpath() const {
  return *fullpath_;
}
inline void SnapshotDiffReportEntryProto::set_fullpath(const ::std::string& value) {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  fullpath_->assign(value);
}
inline void SnapshotDiffReportEntryProto::set_fullpath(const char* value) {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  fullpath_->assign(value);
}
inline void SnapshotDiffReportEntryProto::set_fullpath(const void* value, size_t size) {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  fullpath_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffReportEntryProto::mutable_fullpath() {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  return fullpath_;
}
inline ::std::string* SnapshotDiffReportEntryProto::release_fullpath() {
  clear_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fullpath_;
    fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffReportEntryProto::set_allocated_fullpath(::std::string* fullpath) {
  if (fullpath_ != &::google::protobuf::internal::kEmptyString) {
    delete fullpath_;
  }
  if (fullpath) {
    set_has_fullpath();
    fullpath_ = fullpath;
  } else {
    clear_has_fullpath();
    fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string modificationLabel = 2;
inline bool SnapshotDiffReportEntryProto::has_modificationlabel() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotDiffReportEntryProto::set_has_modificationlabel() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotDiffReportEntryProto::clear_has_modificationlabel() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotDiffReportEntryProto::clear_modificationlabel() {
  if (modificationlabel_ != &::google::protobuf::internal::kEmptyString) {
    modificationlabel_->clear();
  }
  clear_has_modificationlabel();
}
inline const ::std::string& SnapshotDiffReportEntryProto::modificationlabel() const {
  return *modificationlabel_;
}
inline void SnapshotDiffReportEntryProto::set_modificationlabel(const ::std::string& value) {
  set_has_modificationlabel();
  if (modificationlabel_ == &::google::protobuf::internal::kEmptyString) {
    modificationlabel_ = new ::std::string;
  }
  modificationlabel_->assign(value);
}
inline void SnapshotDiffReportEntryProto::set_modificationlabel(const char* value) {
  set_has_modificationlabel();
  if (modificationlabel_ == &::google::protobuf::internal::kEmptyString) {
    modificationlabel_ = new ::std::string;
  }
  modificationlabel_->assign(value);
}
inline void SnapshotDiffReportEntryProto::set_modificationlabel(const char* value, size_t size) {
  set_has_modificationlabel();
  if (modificationlabel_ == &::google::protobuf::internal::kEmptyString) {
    modificationlabel_ = new ::std::string;
  }
  modificationlabel_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffReportEntryProto::mutable_modificationlabel() {
  set_has_modificationlabel();
  if (modificationlabel_ == &::google::protobuf::internal::kEmptyString) {
    modificationlabel_ = new ::std::string;
  }
  return modificationlabel_;
}
inline ::std::string* SnapshotDiffReportEntryProto::release_modificationlabel() {
  clear_has_modificationlabel();
  if (modificationlabel_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = modificationlabel_;
    modificationlabel_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffReportEntryProto::set_allocated_modificationlabel(::std::string* modificationlabel) {
  if (modificationlabel_ != &::google::protobuf::internal::kEmptyString) {
    delete modificationlabel_;
  }
  if (modificationlabel) {
    set_has_modificationlabel();
    modificationlabel_ = modificationlabel;
  } else {
    clear_has_modificationlabel();
    modificationlabel_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bytes targetPath = 3;
inline bool SnapshotDiffReportEntryProto::has_targetpath() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotDiffReportEntryProto::set_has_targetpath() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotDiffReportEntryProto::clear_has_targetpath() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotDiffReportEntryProto::clear_targetpath() {
  if (targetpath_ != &::google::protobuf::internal::kEmptyString) {
    targetpath_->clear();
  }
  clear_has_targetpath();
}
inline const ::std::string& SnapshotDiffReportEntryProto::targetpath() const {
  return *targetpath_;
}
inline void SnapshotDiffReportEntryProto::set_targetpath(const ::std::string& value) {
  set_has_targetpath();
  if (targetpath_ == &::google::protobuf::internal::kEmptyString) {
    targetpath_ = new ::std::string;
  }
  targetpath_->assign(value);
}
inline void SnapshotDiffReportEntryProto::set_targetpath(const char* value) {
  set_has_targetpath();
  if (targetpath_ == &::google::protobuf::internal::kEmptyString) {
    targetpath_ = new ::std::string;
  }
  targetpath_->assign(value);
}
inline void SnapshotDiffReportEntryProto::set_targetpath(const void* value, size_t size) {
  set_has_targetpath();
  if (targetpath_ == &::google::protobuf::internal::kEmptyString) {
    targetpath_ = new ::std::string;
  }
  targetpath_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffReportEntryProto::mutable_targetpath() {
  set_has_targetpath();
  if (targetpath_ == &::google::protobuf::internal::kEmptyString) {
    targetpath_ = new ::std::string;
  }
  return targetpath_;
}
inline ::std::string* SnapshotDiffReportEntryProto::release_targetpath() {
  clear_has_targetpath();
  if (targetpath_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = targetpath_;
    targetpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffReportEntryProto::set_allocated_targetpath(::std::string* targetpath) {
  if (targetpath_ != &::google::protobuf::internal::kEmptyString) {
    delete targetpath_;
  }
  if (targetpath) {
    set_has_targetpath();
    targetpath_ = targetpath;
  } else {
    clear_has_targetpath();
    targetpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// SnapshotDiffReportProto

// required string snapshotRoot = 1;
inline bool SnapshotDiffReportProto::has_snapshotroot() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotDiffReportProto::set_has_snapshotroot() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotDiffReportProto::clear_has_snapshotroot() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotDiffReportProto::clear_snapshotroot() {
  if (snapshotroot_ != &::google::protobuf::internal::kEmptyString) {
    snapshotroot_->clear();
  }
  clear_has_snapshotroot();
}
inline const ::std::string& SnapshotDiffReportProto::snapshotroot() const {
  return *snapshotroot_;
}
inline void SnapshotDiffReportProto::set_snapshotroot(const ::std::string& value) {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  snapshotroot_->assign(value);
}
inline void SnapshotDiffReportProto::set_snapshotroot(const char* value) {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  snapshotroot_->assign(value);
}
inline void SnapshotDiffReportProto::set_snapshotroot(const char* value, size_t size) {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  snapshotroot_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffReportProto::mutable_snapshotroot() {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  return snapshotroot_;
}
inline ::std::string* SnapshotDiffReportProto::release_snapshotroot() {
  clear_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = snapshotroot_;
    snapshotroot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffReportProto::set_allocated_snapshotroot(::std::string* snapshotroot) {
  if (snapshotroot_ != &::google::protobuf::internal::kEmptyString) {
    delete snapshotroot_;
  }
  if (snapshotroot) {
    set_has_snapshotroot();
    snapshotroot_ = snapshotroot;
  } else {
    clear_has_snapshotroot();
    snapshotroot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string fromSnapshot = 2;
inline bool SnapshotDiffReportProto::has_fromsnapshot() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotDiffReportProto::set_has_fromsnapshot() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotDiffReportProto::clear_has_fromsnapshot() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotDiffReportProto::clear_fromsnapshot() {
  if (fromsnapshot_ != &::google::protobuf::internal::kEmptyString) {
    fromsnapshot_->clear();
  }
  clear_has_fromsnapshot();
}
inline const ::std::string& SnapshotDiffReportProto::fromsnapshot() const {
  return *fromsnapshot_;
}
inline void SnapshotDiffReportProto::set_fromsnapshot(const ::std::string& value) {
  set_has_fromsnapshot();
  if (fromsnapshot_ == &::google::protobuf::internal::kEmptyString) {
    fromsnapshot_ = new ::std::string;
  }
  fromsnapshot_->assign(value);
}
inline void SnapshotDiffReportProto::set_fromsnapshot(const char* value) {
  set_has_fromsnapshot();
  if (fromsnapshot_ == &::google::protobuf::internal::kEmptyString) {
    fromsnapshot_ = new ::std::string;
  }
  fromsnapshot_->assign(value);
}
inline void SnapshotDiffReportProto::set_fromsnapshot(const char* value, size_t size) {
  set_has_fromsnapshot();
  if (fromsnapshot_ == &::google::protobuf::internal::kEmptyString) {
    fromsnapshot_ = new ::std::string;
  }
  fromsnapshot_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffReportProto::mutable_fromsnapshot() {
  set_has_fromsnapshot();
  if (fromsnapshot_ == &::google::protobuf::internal::kEmptyString) {
    fromsnapshot_ = new ::std::string;
  }
  return fromsnapshot_;
}
inline ::std::string* SnapshotDiffReportProto::release_fromsnapshot() {
  clear_has_fromsnapshot();
  if (fromsnapshot_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fromsnapshot_;
    fromsnapshot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffReportProto::set_allocated_fromsnapshot(::std::string* fromsnapshot) {
  if (fromsnapshot_ != &::google::protobuf::internal::kEmptyString) {
    delete fromsnapshot_;
  }
  if (fromsnapshot) {
    set_has_fromsnapshot();
    fromsnapshot_ = fromsnapshot;
  } else {
    clear_has_fromsnapshot();
    fromsnapshot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string toSnapshot = 3;
inline bool SnapshotDiffReportProto::has_tosnapshot() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotDiffReportProto::set_has_tosnapshot() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotDiffReportProto::clear_has_tosnapshot() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotDiffReportProto::clear_tosnapshot() {
  if (tosnapshot_ != &::google::protobuf::internal::kEmptyString) {
    tosnapshot_->clear();
  }
  clear_has_tosnapshot();
}
inline const ::std::string& SnapshotDiffReportProto::tosnapshot() const {
  return *tosnapshot_;
}
inline void SnapshotDiffReportProto::set_tosnapshot(const ::std::string& value) {
  set_has_tosnapshot();
  if (tosnapshot_ == &::google::protobuf::internal::kEmptyString) {
    tosnapshot_ = new ::std::string;
  }
  tosnapshot_->assign(value);
}
inline void SnapshotDiffReportProto::set_tosnapshot(const char* value) {
  set_has_tosnapshot();
  if (tosnapshot_ == &::google::protobuf::internal::kEmptyString) {
    tosnapshot_ = new ::std::string;
  }
  tosnapshot_->assign(value);
}
inline void SnapshotDiffReportProto::set_tosnapshot(const char* value, size_t size) {
  set_has_tosnapshot();
  if (tosnapshot_ == &::google::protobuf::internal::kEmptyString) {
    tosnapshot_ = new ::std::string;
  }
  tosnapshot_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffReportProto::mutable_tosnapshot() {
  set_has_tosnapshot();
  if (tosnapshot_ == &::google::protobuf::internal::kEmptyString) {
    tosnapshot_ = new ::std::string;
  }
  return tosnapshot_;
}
inline ::std::string* SnapshotDiffReportProto::release_tosnapshot() {
  clear_has_tosnapshot();
  if (tosnapshot_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = tosnapshot_;
    tosnapshot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffReportProto::set_allocated_tosnapshot(::std::string* tosnapshot) {
  if (tosnapshot_ != &::google::protobuf::internal::kEmptyString) {
    delete tosnapshot_;
  }
  if (tosnapshot) {
    set_has_tosnapshot();
    tosnapshot_ = tosnapshot;
  } else {
    clear_has_tosnapshot();
    tosnapshot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.SnapshotDiffReportEntryProto diffReportEntries = 4;
inline int SnapshotDiffReportProto::diffreportentries_size() const {
  return diffreportentries_.size();
}
inline void SnapshotDiffReportProto::clear_diffreportentries() {
  diffreportentries_.Clear();
}
inline const ::hadoop::hdfs::SnapshotDiffReportEntryProto& SnapshotDiffReportProto::diffreportentries(int index) const {
  return diffreportentries_.Get(index);
}
inline ::hadoop::hdfs::SnapshotDiffReportEntryProto* SnapshotDiffReportProto::mutable_diffreportentries(int index) {
  return diffreportentries_.Mutable(index);
}
inline ::hadoop::hdfs::SnapshotDiffReportEntryProto* SnapshotDiffReportProto::add_diffreportentries() {
  return diffreportentries_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshotDiffReportEntryProto >&
SnapshotDiffReportProto::diffreportentries() const {
  return diffreportentries_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::SnapshotDiffReportEntryProto >*
SnapshotDiffReportProto::mutable_diffreportentries() {
  return &diffreportentries_;
}

// -------------------------------------------------------------------

// StorageInfoProto

// required uint32 layoutVersion = 1;
inline bool StorageInfoProto::has_layoutversion() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StorageInfoProto::set_has_layoutversion() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StorageInfoProto::clear_has_layoutversion() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StorageInfoProto::clear_layoutversion() {
  layoutversion_ = 0u;
  clear_has_layoutversion();
}
inline ::google::protobuf::uint32 StorageInfoProto::layoutversion() const {
  return layoutversion_;
}
inline void StorageInfoProto::set_layoutversion(::google::protobuf::uint32 value) {
  set_has_layoutversion();
  layoutversion_ = value;
}

// required uint32 namespceID = 2;
inline bool StorageInfoProto::has_namespceid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void StorageInfoProto::set_has_namespceid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void StorageInfoProto::clear_has_namespceid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void StorageInfoProto::clear_namespceid() {
  namespceid_ = 0u;
  clear_has_namespceid();
}
inline ::google::protobuf::uint32 StorageInfoProto::namespceid() const {
  return namespceid_;
}
inline void StorageInfoProto::set_namespceid(::google::protobuf::uint32 value) {
  set_has_namespceid();
  namespceid_ = value;
}

// required string clusterID = 3;
inline bool StorageInfoProto::has_clusterid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void StorageInfoProto::set_has_clusterid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void StorageInfoProto::clear_has_clusterid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void StorageInfoProto::clear_clusterid() {
  if (clusterid_ != &::google::protobuf::internal::kEmptyString) {
    clusterid_->clear();
  }
  clear_has_clusterid();
}
inline const ::std::string& StorageInfoProto::clusterid() const {
  return *clusterid_;
}
inline void StorageInfoProto::set_clusterid(const ::std::string& value) {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  clusterid_->assign(value);
}
inline void StorageInfoProto::set_clusterid(const char* value) {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  clusterid_->assign(value);
}
inline void StorageInfoProto::set_clusterid(const char* value, size_t size) {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  clusterid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* StorageInfoProto::mutable_clusterid() {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  return clusterid_;
}
inline ::std::string* StorageInfoProto::release_clusterid() {
  clear_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clusterid_;
    clusterid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void StorageInfoProto::set_allocated_clusterid(::std::string* clusterid) {
  if (clusterid_ != &::google::protobuf::internal::kEmptyString) {
    delete clusterid_;
  }
  if (clusterid) {
    set_has_clusterid();
    clusterid_ = clusterid;
  } else {
    clear_has_clusterid();
    clusterid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 cTime = 4;
inline bool StorageInfoProto::has_ctime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void StorageInfoProto::set_has_ctime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void StorageInfoProto::clear_has_ctime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void StorageInfoProto::clear_ctime() {
  ctime_ = GOOGLE_ULONGLONG(0);
  clear_has_ctime();
}
inline ::google::protobuf::uint64 StorageInfoProto::ctime() const {
  return ctime_;
}
inline void StorageInfoProto::set_ctime(::google::protobuf::uint64 value) {
  set_has_ctime();
  ctime_ = value;
}

// -------------------------------------------------------------------

// NamenodeRegistrationProto

// required string rpcAddress = 1;
inline bool NamenodeRegistrationProto::has_rpcaddress() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NamenodeRegistrationProto::set_has_rpcaddress() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NamenodeRegistrationProto::clear_has_rpcaddress() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NamenodeRegistrationProto::clear_rpcaddress() {
  if (rpcaddress_ != &::google::protobuf::internal::kEmptyString) {
    rpcaddress_->clear();
  }
  clear_has_rpcaddress();
}
inline const ::std::string& NamenodeRegistrationProto::rpcaddress() const {
  return *rpcaddress_;
}
inline void NamenodeRegistrationProto::set_rpcaddress(const ::std::string& value) {
  set_has_rpcaddress();
  if (rpcaddress_ == &::google::protobuf::internal::kEmptyString) {
    rpcaddress_ = new ::std::string;
  }
  rpcaddress_->assign(value);
}
inline void NamenodeRegistrationProto::set_rpcaddress(const char* value) {
  set_has_rpcaddress();
  if (rpcaddress_ == &::google::protobuf::internal::kEmptyString) {
    rpcaddress_ = new ::std::string;
  }
  rpcaddress_->assign(value);
}
inline void NamenodeRegistrationProto::set_rpcaddress(const char* value, size_t size) {
  set_has_rpcaddress();
  if (rpcaddress_ == &::google::protobuf::internal::kEmptyString) {
    rpcaddress_ = new ::std::string;
  }
  rpcaddress_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* NamenodeRegistrationProto::mutable_rpcaddress() {
  set_has_rpcaddress();
  if (rpcaddress_ == &::google::protobuf::internal::kEmptyString) {
    rpcaddress_ = new ::std::string;
  }
  return rpcaddress_;
}
inline ::std::string* NamenodeRegistrationProto::release_rpcaddress() {
  clear_has_rpcaddress();
  if (rpcaddress_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = rpcaddress_;
    rpcaddress_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void NamenodeRegistrationProto::set_allocated_rpcaddress(::std::string* rpcaddress) {
  if (rpcaddress_ != &::google::protobuf::internal::kEmptyString) {
    delete rpcaddress_;
  }
  if (rpcaddress) {
    set_has_rpcaddress();
    rpcaddress_ = rpcaddress;
  } else {
    clear_has_rpcaddress();
    rpcaddress_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string httpAddress = 2;
inline bool NamenodeRegistrationProto::has_httpaddress() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NamenodeRegistrationProto::set_has_httpaddress() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NamenodeRegistrationProto::clear_has_httpaddress() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NamenodeRegistrationProto::clear_httpaddress() {
  if (httpaddress_ != &::google::protobuf::internal::kEmptyString) {
    httpaddress_->clear();
  }
  clear_has_httpaddress();
}
inline const ::std::string& NamenodeRegistrationProto::httpaddress() const {
  return *httpaddress_;
}
inline void NamenodeRegistrationProto::set_httpaddress(const ::std::string& value) {
  set_has_httpaddress();
  if (httpaddress_ == &::google::protobuf::internal::kEmptyString) {
    httpaddress_ = new ::std::string;
  }
  httpaddress_->assign(value);
}
inline void NamenodeRegistrationProto::set_httpaddress(const char* value) {
  set_has_httpaddress();
  if (httpaddress_ == &::google::protobuf::internal::kEmptyString) {
    httpaddress_ = new ::std::string;
  }
  httpaddress_->assign(value);
}
inline void NamenodeRegistrationProto::set_httpaddress(const char* value, size_t size) {
  set_has_httpaddress();
  if (httpaddress_ == &::google::protobuf::internal::kEmptyString) {
    httpaddress_ = new ::std::string;
  }
  httpaddress_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* NamenodeRegistrationProto::mutable_httpaddress() {
  set_has_httpaddress();
  if (httpaddress_ == &::google::protobuf::internal::kEmptyString) {
    httpaddress_ = new ::std::string;
  }
  return httpaddress_;
}
inline ::std::string* NamenodeRegistrationProto::release_httpaddress() {
  clear_has_httpaddress();
  if (httpaddress_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = httpaddress_;
    httpaddress_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void NamenodeRegistrationProto::set_allocated_httpaddress(::std::string* httpaddress) {
  if (httpaddress_ != &::google::protobuf::internal::kEmptyString) {
    delete httpaddress_;
  }
  if (httpaddress) {
    set_has_httpaddress();
    httpaddress_ = httpaddress;
  } else {
    clear_has_httpaddress();
    httpaddress_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.StorageInfoProto storageInfo = 3;
inline bool NamenodeRegistrationProto::has_storageinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NamenodeRegistrationProto::set_has_storageinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NamenodeRegistrationProto::clear_has_storageinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NamenodeRegistrationProto::clear_storageinfo() {
  if (storageinfo_ != NULL) storageinfo_->::hadoop::hdfs::StorageInfoProto::Clear();
  clear_has_storageinfo();
}
inline const ::hadoop::hdfs::StorageInfoProto& NamenodeRegistrationProto::storageinfo() const {
  return storageinfo_ != NULL ? *storageinfo_ : *default_instance_->storageinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* NamenodeRegistrationProto::mutable_storageinfo() {
  set_has_storageinfo();
  if (storageinfo_ == NULL) storageinfo_ = new ::hadoop::hdfs::StorageInfoProto;
  return storageinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* NamenodeRegistrationProto::release_storageinfo() {
  clear_has_storageinfo();
  ::hadoop::hdfs::StorageInfoProto* temp = storageinfo_;
  storageinfo_ = NULL;
  return temp;
}
inline void NamenodeRegistrationProto::set_allocated_storageinfo(::hadoop::hdfs::StorageInfoProto* storageinfo) {
  delete storageinfo_;
  storageinfo_ = storageinfo;
  if (storageinfo) {
    set_has_storageinfo();
  } else {
    clear_has_storageinfo();
  }
}

// optional .hadoop.hdfs.NamenodeRegistrationProto.NamenodeRoleProto role = 4 [default = NAMENODE];
inline bool NamenodeRegistrationProto::has_role() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void NamenodeRegistrationProto::set_has_role() {
  _has_bits_[0] |= 0x00000008u;
}
inline void NamenodeRegistrationProto::clear_has_role() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void NamenodeRegistrationProto::clear_role() {
  role_ = 1;
  clear_has_role();
}
inline ::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto NamenodeRegistrationProto::role() const {
  return static_cast< ::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto >(role_);
}
inline void NamenodeRegistrationProto::set_role(::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto value) {
  assert(::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto_IsValid(value));
  set_has_role();
  role_ = value;
}

// -------------------------------------------------------------------

// CheckpointSignatureProto

// required string blockPoolId = 1;
inline bool CheckpointSignatureProto::has_blockpoolid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CheckpointSignatureProto::set_has_blockpoolid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CheckpointSignatureProto::clear_has_blockpoolid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CheckpointSignatureProto::clear_blockpoolid() {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    blockpoolid_->clear();
  }
  clear_has_blockpoolid();
}
inline const ::std::string& CheckpointSignatureProto::blockpoolid() const {
  return *blockpoolid_;
}
inline void CheckpointSignatureProto::set_blockpoolid(const ::std::string& value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void CheckpointSignatureProto::set_blockpoolid(const char* value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void CheckpointSignatureProto::set_blockpoolid(const char* value, size_t size) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CheckpointSignatureProto::mutable_blockpoolid() {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  return blockpoolid_;
}
inline ::std::string* CheckpointSignatureProto::release_blockpoolid() {
  clear_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = blockpoolid_;
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CheckpointSignatureProto::set_allocated_blockpoolid(::std::string* blockpoolid) {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    delete blockpoolid_;
  }
  if (blockpoolid) {
    set_has_blockpoolid();
    blockpoolid_ = blockpoolid;
  } else {
    clear_has_blockpoolid();
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 mostRecentCheckpointTxId = 2;
inline bool CheckpointSignatureProto::has_mostrecentcheckpointtxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CheckpointSignatureProto::set_has_mostrecentcheckpointtxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CheckpointSignatureProto::clear_has_mostrecentcheckpointtxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CheckpointSignatureProto::clear_mostrecentcheckpointtxid() {
  mostrecentcheckpointtxid_ = GOOGLE_ULONGLONG(0);
  clear_has_mostrecentcheckpointtxid();
}
inline ::google::protobuf::uint64 CheckpointSignatureProto::mostrecentcheckpointtxid() const {
  return mostrecentcheckpointtxid_;
}
inline void CheckpointSignatureProto::set_mostrecentcheckpointtxid(::google::protobuf::uint64 value) {
  set_has_mostrecentcheckpointtxid();
  mostrecentcheckpointtxid_ = value;
}

// required uint64 curSegmentTxId = 3;
inline bool CheckpointSignatureProto::has_cursegmenttxid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void CheckpointSignatureProto::set_has_cursegmenttxid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void CheckpointSignatureProto::clear_has_cursegmenttxid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void CheckpointSignatureProto::clear_cursegmenttxid() {
  cursegmenttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_cursegmenttxid();
}
inline ::google::protobuf::uint64 CheckpointSignatureProto::cursegmenttxid() const {
  return cursegmenttxid_;
}
inline void CheckpointSignatureProto::set_cursegmenttxid(::google::protobuf::uint64 value) {
  set_has_cursegmenttxid();
  cursegmenttxid_ = value;
}

// required .hadoop.hdfs.StorageInfoProto storageInfo = 4;
inline bool CheckpointSignatureProto::has_storageinfo() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void CheckpointSignatureProto::set_has_storageinfo() {
  _has_bits_[0] |= 0x00000008u;
}
inline void CheckpointSignatureProto::clear_has_storageinfo() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void CheckpointSignatureProto::clear_storageinfo() {
  if (storageinfo_ != NULL) storageinfo_->::hadoop::hdfs::StorageInfoProto::Clear();
  clear_has_storageinfo();
}
inline const ::hadoop::hdfs::StorageInfoProto& CheckpointSignatureProto::storageinfo() const {
  return storageinfo_ != NULL ? *storageinfo_ : *default_instance_->storageinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* CheckpointSignatureProto::mutable_storageinfo() {
  set_has_storageinfo();
  if (storageinfo_ == NULL) storageinfo_ = new ::hadoop::hdfs::StorageInfoProto;
  return storageinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* CheckpointSignatureProto::release_storageinfo() {
  clear_has_storageinfo();
  ::hadoop::hdfs::StorageInfoProto* temp = storageinfo_;
  storageinfo_ = NULL;
  return temp;
}
inline void CheckpointSignatureProto::set_allocated_storageinfo(::hadoop::hdfs::StorageInfoProto* storageinfo) {
  delete storageinfo_;
  storageinfo_ = storageinfo;
  if (storageinfo) {
    set_has_storageinfo();
  } else {
    clear_has_storageinfo();
  }
}

// -------------------------------------------------------------------

// NamenodeCommandProto

// required uint32 action = 1;
inline bool NamenodeCommandProto::has_action() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NamenodeCommandProto::set_has_action() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NamenodeCommandProto::clear_has_action() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NamenodeCommandProto::clear_action() {
  action_ = 0u;
  clear_has_action();
}
inline ::google::protobuf::uint32 NamenodeCommandProto::action() const {
  return action_;
}
inline void NamenodeCommandProto::set_action(::google::protobuf::uint32 value) {
  set_has_action();
  action_ = value;
}

// required .hadoop.hdfs.NamenodeCommandProto.Type type = 2;
inline bool NamenodeCommandProto::has_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NamenodeCommandProto::set_has_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NamenodeCommandProto::clear_has_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NamenodeCommandProto::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::hadoop::hdfs::NamenodeCommandProto_Type NamenodeCommandProto::type() const {
  return static_cast< ::hadoop::hdfs::NamenodeCommandProto_Type >(type_);
}
inline void NamenodeCommandProto::set_type(::hadoop::hdfs::NamenodeCommandProto_Type value) {
  assert(::hadoop::hdfs::NamenodeCommandProto_Type_IsValid(value));
  set_has_type();
  type_ = value;
}

// optional .hadoop.hdfs.CheckpointCommandProto checkpointCmd = 3;
inline bool NamenodeCommandProto::has_checkpointcmd() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NamenodeCommandProto::set_has_checkpointcmd() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NamenodeCommandProto::clear_has_checkpointcmd() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NamenodeCommandProto::clear_checkpointcmd() {
  if (checkpointcmd_ != NULL) checkpointcmd_->::hadoop::hdfs::CheckpointCommandProto::Clear();
  clear_has_checkpointcmd();
}
inline const ::hadoop::hdfs::CheckpointCommandProto& NamenodeCommandProto::checkpointcmd() const {
  return checkpointcmd_ != NULL ? *checkpointcmd_ : *default_instance_->checkpointcmd_;
}
inline ::hadoop::hdfs::CheckpointCommandProto* NamenodeCommandProto::mutable_checkpointcmd() {
  set_has_checkpointcmd();
  if (checkpointcmd_ == NULL) checkpointcmd_ = new ::hadoop::hdfs::CheckpointCommandProto;
  return checkpointcmd_;
}
inline ::hadoop::hdfs::CheckpointCommandProto* NamenodeCommandProto::release_checkpointcmd() {
  clear_has_checkpointcmd();
  ::hadoop::hdfs::CheckpointCommandProto* temp = checkpointcmd_;
  checkpointcmd_ = NULL;
  return temp;
}
inline void NamenodeCommandProto::set_allocated_checkpointcmd(::hadoop::hdfs::CheckpointCommandProto* checkpointcmd) {
  delete checkpointcmd_;
  checkpointcmd_ = checkpointcmd;
  if (checkpointcmd) {
    set_has_checkpointcmd();
  } else {
    clear_has_checkpointcmd();
  }
}

// -------------------------------------------------------------------

// CheckpointCommandProto

// required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
inline bool CheckpointCommandProto::has_signature() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CheckpointCommandProto::set_has_signature() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CheckpointCommandProto::clear_has_signature() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CheckpointCommandProto::clear_signature() {
  if (signature_ != NULL) signature_->::hadoop::hdfs::CheckpointSignatureProto::Clear();
  clear_has_signature();
}
inline const ::hadoop::hdfs::CheckpointSignatureProto& CheckpointCommandProto::signature() const {
  return signature_ != NULL ? *signature_ : *default_instance_->signature_;
}
inline ::hadoop::hdfs::CheckpointSignatureProto* CheckpointCommandProto::mutable_signature() {
  set_has_signature();
  if (signature_ == NULL) signature_ = new ::hadoop::hdfs::CheckpointSignatureProto;
  return signature_;
}
inline ::hadoop::hdfs::CheckpointSignatureProto* CheckpointCommandProto::release_signature() {
  clear_has_signature();
  ::hadoop::hdfs::CheckpointSignatureProto* temp = signature_;
  signature_ = NULL;
  return temp;
}
inline void CheckpointCommandProto::set_allocated_signature(::hadoop::hdfs::CheckpointSignatureProto* signature) {
  delete signature_;
  signature_ = signature;
  if (signature) {
    set_has_signature();
  } else {
    clear_has_signature();
  }
}

// required bool needToReturnImage = 2;
inline bool CheckpointCommandProto::has_needtoreturnimage() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CheckpointCommandProto::set_has_needtoreturnimage() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CheckpointCommandProto::clear_has_needtoreturnimage() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CheckpointCommandProto::clear_needtoreturnimage() {
  needtoreturnimage_ = false;
  clear_has_needtoreturnimage();
}
inline bool CheckpointCommandProto::needtoreturnimage() const {
  return needtoreturnimage_;
}
inline void CheckpointCommandProto::set_needtoreturnimage(bool value) {
  set_has_needtoreturnimage();
  needtoreturnimage_ = value;
}

// -------------------------------------------------------------------

// BlockProto

// required uint64 blockId = 1;
inline bool BlockProto::has_blockid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BlockProto::set_has_blockid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BlockProto::clear_has_blockid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BlockProto::clear_blockid() {
  blockid_ = GOOGLE_ULONGLONG(0);
  clear_has_blockid();
}
inline ::google::protobuf::uint64 BlockProto::blockid() const {
  return blockid_;
}
inline void BlockProto::set_blockid(::google::protobuf::uint64 value) {
  set_has_blockid();
  blockid_ = value;
}

// required uint64 genStamp = 2;
inline bool BlockProto::has_genstamp() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void BlockProto::set_has_genstamp() {
  _has_bits_[0] |= 0x00000002u;
}
inline void BlockProto::clear_has_genstamp() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void BlockProto::clear_genstamp() {
  genstamp_ = GOOGLE_ULONGLONG(0);
  clear_has_genstamp();
}
inline ::google::protobuf::uint64 BlockProto::genstamp() const {
  return genstamp_;
}
inline void BlockProto::set_genstamp(::google::protobuf::uint64 value) {
  set_has_genstamp();
  genstamp_ = value;
}

// optional uint64 numBytes = 3 [default = 0];
inline bool BlockProto::has_numbytes() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void BlockProto::set_has_numbytes() {
  _has_bits_[0] |= 0x00000004u;
}
inline void BlockProto::clear_has_numbytes() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void BlockProto::clear_numbytes() {
  numbytes_ = GOOGLE_ULONGLONG(0);
  clear_has_numbytes();
}
inline ::google::protobuf::uint64 BlockProto::numbytes() const {
  return numbytes_;
}
inline void BlockProto::set_numbytes(::google::protobuf::uint64 value) {
  set_has_numbytes();
  numbytes_ = value;
}

// -------------------------------------------------------------------

// BlockWithLocationsProto

// required .hadoop.hdfs.BlockProto block = 1;
inline bool BlockWithLocationsProto::has_block() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BlockWithLocationsProto::set_has_block() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BlockWithLocationsProto::clear_has_block() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BlockWithLocationsProto::clear_block() {
  if (block_ != NULL) block_->::hadoop::hdfs::BlockProto::Clear();
  clear_has_block();
}
inline const ::hadoop::hdfs::BlockProto& BlockWithLocationsProto::block() const {
  return block_ != NULL ? *block_ : *default_instance_->block_;
}
inline ::hadoop::hdfs::BlockProto* BlockWithLocationsProto::mutable_block() {
  set_has_block();
  if (block_ == NULL) block_ = new ::hadoop::hdfs::BlockProto;
  return block_;
}
inline ::hadoop::hdfs::BlockProto* BlockWithLocationsProto::release_block() {
  clear_has_block();
  ::hadoop::hdfs::BlockProto* temp = block_;
  block_ = NULL;
  return temp;
}
inline void BlockWithLocationsProto::set_allocated_block(::hadoop::hdfs::BlockProto* block) {
  delete block_;
  block_ = block;
  if (block) {
    set_has_block();
  } else {
    clear_has_block();
  }
}

// repeated string datanodeUuids = 2;
inline int BlockWithLocationsProto::datanodeuuids_size() const {
  return datanodeuuids_.size();
}
inline void BlockWithLocationsProto::clear_datanodeuuids() {
  datanodeuuids_.Clear();
}
inline const ::std::string& BlockWithLocationsProto::datanodeuuids(int index) const {
  return datanodeuuids_.Get(index);
}
inline ::std::string* BlockWithLocationsProto::mutable_datanodeuuids(int index) {
  return datanodeuuids_.Mutable(index);
}
inline void BlockWithLocationsProto::set_datanodeuuids(int index, const ::std::string& value) {
  datanodeuuids_.Mutable(index)->assign(value);
}
inline void BlockWithLocationsProto::set_datanodeuuids(int index, const char* value) {
  datanodeuuids_.Mutable(index)->assign(value);
}
inline void BlockWithLocationsProto::set_datanodeuuids(int index, const char* value, size_t size) {
  datanodeuuids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* BlockWithLocationsProto::add_datanodeuuids() {
  return datanodeuuids_.Add();
}
inline void BlockWithLocationsProto::add_datanodeuuids(const ::std::string& value) {
  datanodeuuids_.Add()->assign(value);
}
inline void BlockWithLocationsProto::add_datanodeuuids(const char* value) {
  datanodeuuids_.Add()->assign(value);
}
inline void BlockWithLocationsProto::add_datanodeuuids(const char* value, size_t size) {
  datanodeuuids_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BlockWithLocationsProto::datanodeuuids() const {
  return datanodeuuids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BlockWithLocationsProto::mutable_datanodeuuids() {
  return &datanodeuuids_;
}

// repeated string storageUuids = 3;
inline int BlockWithLocationsProto::storageuuids_size() const {
  return storageuuids_.size();
}
inline void BlockWithLocationsProto::clear_storageuuids() {
  storageuuids_.Clear();
}
inline const ::std::string& BlockWithLocationsProto::storageuuids(int index) const {
  return storageuuids_.Get(index);
}
inline ::std::string* BlockWithLocationsProto::mutable_storageuuids(int index) {
  return storageuuids_.Mutable(index);
}
inline void BlockWithLocationsProto::set_storageuuids(int index, const ::std::string& value) {
  storageuuids_.Mutable(index)->assign(value);
}
inline void BlockWithLocationsProto::set_storageuuids(int index, const char* value) {
  storageuuids_.Mutable(index)->assign(value);
}
inline void BlockWithLocationsProto::set_storageuuids(int index, const char* value, size_t size) {
  storageuuids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* BlockWithLocationsProto::add_storageuuids() {
  return storageuuids_.Add();
}
inline void BlockWithLocationsProto::add_storageuuids(const ::std::string& value) {
  storageuuids_.Add()->assign(value);
}
inline void BlockWithLocationsProto::add_storageuuids(const char* value) {
  storageuuids_.Add()->assign(value);
}
inline void BlockWithLocationsProto::add_storageuuids(const char* value, size_t size) {
  storageuuids_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BlockWithLocationsProto::storageuuids() const {
  return storageuuids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BlockWithLocationsProto::mutable_storageuuids() {
  return &storageuuids_;
}

// repeated .hadoop.hdfs.StorageTypeProto storageTypes = 4;
inline int BlockWithLocationsProto::storagetypes_size() const {
  return storagetypes_.size();
}
inline void BlockWithLocationsProto::clear_storagetypes() {
  storagetypes_.Clear();
}
inline ::hadoop::hdfs::StorageTypeProto BlockWithLocationsProto::storagetypes(int index) const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(storagetypes_.Get(index));
}
inline void BlockWithLocationsProto::set_storagetypes(int index, ::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  storagetypes_.Set(index, value);
}
inline void BlockWithLocationsProto::add_storagetypes(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  storagetypes_.Add(value);
}
inline const ::google::protobuf::RepeatedField<int>&
BlockWithLocationsProto::storagetypes() const {
  return storagetypes_;
}
inline ::google::protobuf::RepeatedField<int>*
BlockWithLocationsProto::mutable_storagetypes() {
  return &storagetypes_;
}

// -------------------------------------------------------------------

// BlocksWithLocationsProto

// repeated .hadoop.hdfs.BlockWithLocationsProto blocks = 1;
inline int BlocksWithLocationsProto::blocks_size() const {
  return blocks_.size();
}
inline void BlocksWithLocationsProto::clear_blocks() {
  blocks_.Clear();
}
inline const ::hadoop::hdfs::BlockWithLocationsProto& BlocksWithLocationsProto::blocks(int index) const {
  return blocks_.Get(index);
}
inline ::hadoop::hdfs::BlockWithLocationsProto* BlocksWithLocationsProto::mutable_blocks(int index) {
  return blocks_.Mutable(index);
}
inline ::hadoop::hdfs::BlockWithLocationsProto* BlocksWithLocationsProto::add_blocks() {
  return blocks_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockWithLocationsProto >&
BlocksWithLocationsProto::blocks() const {
  return blocks_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockWithLocationsProto >*
BlocksWithLocationsProto::mutable_blocks() {
  return &blocks_;
}

// -------------------------------------------------------------------

// RemoteEditLogProto

// required uint64 startTxId = 1;
inline bool RemoteEditLogProto::has_starttxid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RemoteEditLogProto::set_has_starttxid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RemoteEditLogProto::clear_has_starttxid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RemoteEditLogProto::clear_starttxid() {
  starttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_starttxid();
}
inline ::google::protobuf::uint64 RemoteEditLogProto::starttxid() const {
  return starttxid_;
}
inline void RemoteEditLogProto::set_starttxid(::google::protobuf::uint64 value) {
  set_has_starttxid();
  starttxid_ = value;
}

// required uint64 endTxId = 2;
inline bool RemoteEditLogProto::has_endtxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RemoteEditLogProto::set_has_endtxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RemoteEditLogProto::clear_has_endtxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RemoteEditLogProto::clear_endtxid() {
  endtxid_ = GOOGLE_ULONGLONG(0);
  clear_has_endtxid();
}
inline ::google::protobuf::uint64 RemoteEditLogProto::endtxid() const {
  return endtxid_;
}
inline void RemoteEditLogProto::set_endtxid(::google::protobuf::uint64 value) {
  set_has_endtxid();
  endtxid_ = value;
}

// optional bool isInProgress = 3 [default = false];
inline bool RemoteEditLogProto::has_isinprogress() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RemoteEditLogProto::set_has_isinprogress() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RemoteEditLogProto::clear_has_isinprogress() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RemoteEditLogProto::clear_isinprogress() {
  isinprogress_ = false;
  clear_has_isinprogress();
}
inline bool RemoteEditLogProto::isinprogress() const {
  return isinprogress_;
}
inline void RemoteEditLogProto::set_isinprogress(bool value) {
  set_has_isinprogress();
  isinprogress_ = value;
}

// -------------------------------------------------------------------

// RemoteEditLogManifestProto

// repeated .hadoop.hdfs.RemoteEditLogProto logs = 1;
inline int RemoteEditLogManifestProto::logs_size() const {
  return logs_.size();
}
inline void RemoteEditLogManifestProto::clear_logs() {
  logs_.Clear();
}
inline const ::hadoop::hdfs::RemoteEditLogProto& RemoteEditLogManifestProto::logs(int index) const {
  return logs_.Get(index);
}
inline ::hadoop::hdfs::RemoteEditLogProto* RemoteEditLogManifestProto::mutable_logs(int index) {
  return logs_.Mutable(index);
}
inline ::hadoop::hdfs::RemoteEditLogProto* RemoteEditLogManifestProto::add_logs() {
  return logs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::RemoteEditLogProto >&
RemoteEditLogManifestProto::logs() const {
  return logs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::RemoteEditLogProto >*
RemoteEditLogManifestProto::mutable_logs() {
  return &logs_;
}

// -------------------------------------------------------------------

// NamespaceInfoProto

// required string buildVersion = 1;
inline bool NamespaceInfoProto::has_buildversion() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NamespaceInfoProto::set_has_buildversion() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NamespaceInfoProto::clear_has_buildversion() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NamespaceInfoProto::clear_buildversion() {
  if (buildversion_ != &::google::protobuf::internal::kEmptyString) {
    buildversion_->clear();
  }
  clear_has_buildversion();
}
inline const ::std::string& NamespaceInfoProto::buildversion() const {
  return *buildversion_;
}
inline void NamespaceInfoProto::set_buildversion(const ::std::string& value) {
  set_has_buildversion();
  if (buildversion_ == &::google::protobuf::internal::kEmptyString) {
    buildversion_ = new ::std::string;
  }
  buildversion_->assign(value);
}
inline void NamespaceInfoProto::set_buildversion(const char* value) {
  set_has_buildversion();
  if (buildversion_ == &::google::protobuf::internal::kEmptyString) {
    buildversion_ = new ::std::string;
  }
  buildversion_->assign(value);
}
inline void NamespaceInfoProto::set_buildversion(const char* value, size_t size) {
  set_has_buildversion();
  if (buildversion_ == &::google::protobuf::internal::kEmptyString) {
    buildversion_ = new ::std::string;
  }
  buildversion_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* NamespaceInfoProto::mutable_buildversion() {
  set_has_buildversion();
  if (buildversion_ == &::google::protobuf::internal::kEmptyString) {
    buildversion_ = new ::std::string;
  }
  return buildversion_;
}
inline ::std::string* NamespaceInfoProto::release_buildversion() {
  clear_has_buildversion();
  if (buildversion_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = buildversion_;
    buildversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void NamespaceInfoProto::set_allocated_buildversion(::std::string* buildversion) {
  if (buildversion_ != &::google::protobuf::internal::kEmptyString) {
    delete buildversion_;
  }
  if (buildversion) {
    set_has_buildversion();
    buildversion_ = buildversion;
  } else {
    clear_has_buildversion();
    buildversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint32 unused = 2;
inline bool NamespaceInfoProto::has_unused() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NamespaceInfoProto::set_has_unused() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NamespaceInfoProto::clear_has_unused() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NamespaceInfoProto::clear_unused() {
  unused_ = 0u;
  clear_has_unused();
}
inline ::google::protobuf::uint32 NamespaceInfoProto::unused() const {
  return unused_;
}
inline void NamespaceInfoProto::set_unused(::google::protobuf::uint32 value) {
  set_has_unused();
  unused_ = value;
}

// required string blockPoolID = 3;
inline bool NamespaceInfoProto::has_blockpoolid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NamespaceInfoProto::set_has_blockpoolid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NamespaceInfoProto::clear_has_blockpoolid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NamespaceInfoProto::clear_blockpoolid() {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    blockpoolid_->clear();
  }
  clear_has_blockpoolid();
}
inline const ::std::string& NamespaceInfoProto::blockpoolid() const {
  return *blockpoolid_;
}
inline void NamespaceInfoProto::set_blockpoolid(const ::std::string& value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void NamespaceInfoProto::set_blockpoolid(const char* value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void NamespaceInfoProto::set_blockpoolid(const char* value, size_t size) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* NamespaceInfoProto::mutable_blockpoolid() {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  return blockpoolid_;
}
inline ::std::string* NamespaceInfoProto::release_blockpoolid() {
  clear_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = blockpoolid_;
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void NamespaceInfoProto::set_allocated_blockpoolid(::std::string* blockpoolid) {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    delete blockpoolid_;
  }
  if (blockpoolid) {
    set_has_blockpoolid();
    blockpoolid_ = blockpoolid;
  } else {
    clear_has_blockpoolid();
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.StorageInfoProto storageInfo = 4;
inline bool NamespaceInfoProto::has_storageinfo() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void NamespaceInfoProto::set_has_storageinfo() {
  _has_bits_[0] |= 0x00000008u;
}
inline void NamespaceInfoProto::clear_has_storageinfo() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void NamespaceInfoProto::clear_storageinfo() {
  if (storageinfo_ != NULL) storageinfo_->::hadoop::hdfs::StorageInfoProto::Clear();
  clear_has_storageinfo();
}
inline const ::hadoop::hdfs::StorageInfoProto& NamespaceInfoProto::storageinfo() const {
  return storageinfo_ != NULL ? *storageinfo_ : *default_instance_->storageinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* NamespaceInfoProto::mutable_storageinfo() {
  set_has_storageinfo();
  if (storageinfo_ == NULL) storageinfo_ = new ::hadoop::hdfs::StorageInfoProto;
  return storageinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* NamespaceInfoProto::release_storageinfo() {
  clear_has_storageinfo();
  ::hadoop::hdfs::StorageInfoProto* temp = storageinfo_;
  storageinfo_ = NULL;
  return temp;
}
inline void NamespaceInfoProto::set_allocated_storageinfo(::hadoop::hdfs::StorageInfoProto* storageinfo) {
  delete storageinfo_;
  storageinfo_ = storageinfo;
  if (storageinfo) {
    set_has_storageinfo();
  } else {
    clear_has_storageinfo();
  }
}

// required string softwareVersion = 5;
inline bool NamespaceInfoProto::has_softwareversion() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void NamespaceInfoProto::set_has_softwareversion() {
  _has_bits_[0] |= 0x00000010u;
}
inline void NamespaceInfoProto::clear_has_softwareversion() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void NamespaceInfoProto::clear_softwareversion() {
  if (softwareversion_ != &::google::protobuf::internal::kEmptyString) {
    softwareversion_->clear();
  }
  clear_has_softwareversion();
}
inline const ::std::string& NamespaceInfoProto::softwareversion() const {
  return *softwareversion_;
}
inline void NamespaceInfoProto::set_softwareversion(const ::std::string& value) {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  softwareversion_->assign(value);
}
inline void NamespaceInfoProto::set_softwareversion(const char* value) {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  softwareversion_->assign(value);
}
inline void NamespaceInfoProto::set_softwareversion(const char* value, size_t size) {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  softwareversion_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* NamespaceInfoProto::mutable_softwareversion() {
  set_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    softwareversion_ = new ::std::string;
  }
  return softwareversion_;
}
inline ::std::string* NamespaceInfoProto::release_softwareversion() {
  clear_has_softwareversion();
  if (softwareversion_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = softwareversion_;
    softwareversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void NamespaceInfoProto::set_allocated_softwareversion(::std::string* softwareversion) {
  if (softwareversion_ != &::google::protobuf::internal::kEmptyString) {
    delete softwareversion_;
  }
  if (softwareversion) {
    set_has_softwareversion();
    softwareversion_ = softwareversion;
  } else {
    clear_has_softwareversion();
    softwareversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// BlockKeyProto

// required uint32 keyId = 1;
inline bool BlockKeyProto::has_keyid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BlockKeyProto::set_has_keyid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BlockKeyProto::clear_has_keyid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BlockKeyProto::clear_keyid() {
  keyid_ = 0u;
  clear_has_keyid();
}
inline ::google::protobuf::uint32 BlockKeyProto::keyid() const {
  return keyid_;
}
inline void BlockKeyProto::set_keyid(::google::protobuf::uint32 value) {
  set_has_keyid();
  keyid_ = value;
}

// required uint64 expiryDate = 2;
inline bool BlockKeyProto::has_expirydate() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void BlockKeyProto::set_has_expirydate() {
  _has_bits_[0] |= 0x00000002u;
}
inline void BlockKeyProto::clear_has_expirydate() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void BlockKeyProto::clear_expirydate() {
  expirydate_ = GOOGLE_ULONGLONG(0);
  clear_has_expirydate();
}
inline ::google::protobuf::uint64 BlockKeyProto::expirydate() const {
  return expirydate_;
}
inline void BlockKeyProto::set_expirydate(::google::protobuf::uint64 value) {
  set_has_expirydate();
  expirydate_ = value;
}

// optional bytes keyBytes = 3;
inline bool BlockKeyProto::has_keybytes() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void BlockKeyProto::set_has_keybytes() {
  _has_bits_[0] |= 0x00000004u;
}
inline void BlockKeyProto::clear_has_keybytes() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void BlockKeyProto::clear_keybytes() {
  if (keybytes_ != &::google::protobuf::internal::kEmptyString) {
    keybytes_->clear();
  }
  clear_has_keybytes();
}
inline const ::std::string& BlockKeyProto::keybytes() const {
  return *keybytes_;
}
inline void BlockKeyProto::set_keybytes(const ::std::string& value) {
  set_has_keybytes();
  if (keybytes_ == &::google::protobuf::internal::kEmptyString) {
    keybytes_ = new ::std::string;
  }
  keybytes_->assign(value);
}
inline void BlockKeyProto::set_keybytes(const char* value) {
  set_has_keybytes();
  if (keybytes_ == &::google::protobuf::internal::kEmptyString) {
    keybytes_ = new ::std::string;
  }
  keybytes_->assign(value);
}
inline void BlockKeyProto::set_keybytes(const void* value, size_t size) {
  set_has_keybytes();
  if (keybytes_ == &::google::protobuf::internal::kEmptyString) {
    keybytes_ = new ::std::string;
  }
  keybytes_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* BlockKeyProto::mutable_keybytes() {
  set_has_keybytes();
  if (keybytes_ == &::google::protobuf::internal::kEmptyString) {
    keybytes_ = new ::std::string;
  }
  return keybytes_;
}
inline ::std::string* BlockKeyProto::release_keybytes() {
  clear_has_keybytes();
  if (keybytes_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = keybytes_;
    keybytes_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void BlockKeyProto::set_allocated_keybytes(::std::string* keybytes) {
  if (keybytes_ != &::google::protobuf::internal::kEmptyString) {
    delete keybytes_;
  }
  if (keybytes) {
    set_has_keybytes();
    keybytes_ = keybytes;
  } else {
    clear_has_keybytes();
    keybytes_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ExportedBlockKeysProto

// required bool isBlockTokenEnabled = 1;
inline bool ExportedBlockKeysProto::has_isblocktokenenabled() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ExportedBlockKeysProto::set_has_isblocktokenenabled() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ExportedBlockKeysProto::clear_has_isblocktokenenabled() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ExportedBlockKeysProto::clear_isblocktokenenabled() {
  isblocktokenenabled_ = false;
  clear_has_isblocktokenenabled();
}
inline bool ExportedBlockKeysProto::isblocktokenenabled() const {
  return isblocktokenenabled_;
}
inline void ExportedBlockKeysProto::set_isblocktokenenabled(bool value) {
  set_has_isblocktokenenabled();
  isblocktokenenabled_ = value;
}

// required uint64 keyUpdateInterval = 2;
inline bool ExportedBlockKeysProto::has_keyupdateinterval() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ExportedBlockKeysProto::set_has_keyupdateinterval() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ExportedBlockKeysProto::clear_has_keyupdateinterval() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ExportedBlockKeysProto::clear_keyupdateinterval() {
  keyupdateinterval_ = GOOGLE_ULONGLONG(0);
  clear_has_keyupdateinterval();
}
inline ::google::protobuf::uint64 ExportedBlockKeysProto::keyupdateinterval() const {
  return keyupdateinterval_;
}
inline void ExportedBlockKeysProto::set_keyupdateinterval(::google::protobuf::uint64 value) {
  set_has_keyupdateinterval();
  keyupdateinterval_ = value;
}

// required uint64 tokenLifeTime = 3;
inline bool ExportedBlockKeysProto::has_tokenlifetime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ExportedBlockKeysProto::set_has_tokenlifetime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ExportedBlockKeysProto::clear_has_tokenlifetime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ExportedBlockKeysProto::clear_tokenlifetime() {
  tokenlifetime_ = GOOGLE_ULONGLONG(0);
  clear_has_tokenlifetime();
}
inline ::google::protobuf::uint64 ExportedBlockKeysProto::tokenlifetime() const {
  return tokenlifetime_;
}
inline void ExportedBlockKeysProto::set_tokenlifetime(::google::protobuf::uint64 value) {
  set_has_tokenlifetime();
  tokenlifetime_ = value;
}

// required .hadoop.hdfs.BlockKeyProto currentKey = 4;
inline bool ExportedBlockKeysProto::has_currentkey() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ExportedBlockKeysProto::set_has_currentkey() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ExportedBlockKeysProto::clear_has_currentkey() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ExportedBlockKeysProto::clear_currentkey() {
  if (currentkey_ != NULL) currentkey_->::hadoop::hdfs::BlockKeyProto::Clear();
  clear_has_currentkey();
}
inline const ::hadoop::hdfs::BlockKeyProto& ExportedBlockKeysProto::currentkey() const {
  return currentkey_ != NULL ? *currentkey_ : *default_instance_->currentkey_;
}
inline ::hadoop::hdfs::BlockKeyProto* ExportedBlockKeysProto::mutable_currentkey() {
  set_has_currentkey();
  if (currentkey_ == NULL) currentkey_ = new ::hadoop::hdfs::BlockKeyProto;
  return currentkey_;
}
inline ::hadoop::hdfs::BlockKeyProto* ExportedBlockKeysProto::release_currentkey() {
  clear_has_currentkey();
  ::hadoop::hdfs::BlockKeyProto* temp = currentkey_;
  currentkey_ = NULL;
  return temp;
}
inline void ExportedBlockKeysProto::set_allocated_currentkey(::hadoop::hdfs::BlockKeyProto* currentkey) {
  delete currentkey_;
  currentkey_ = currentkey;
  if (currentkey) {
    set_has_currentkey();
  } else {
    clear_has_currentkey();
  }
}

// repeated .hadoop.hdfs.BlockKeyProto allKeys = 5;
inline int ExportedBlockKeysProto::allkeys_size() const {
  return allkeys_.size();
}
inline void ExportedBlockKeysProto::clear_allkeys() {
  allkeys_.Clear();
}
inline const ::hadoop::hdfs::BlockKeyProto& ExportedBlockKeysProto::allkeys(int index) const {
  return allkeys_.Get(index);
}
inline ::hadoop::hdfs::BlockKeyProto* ExportedBlockKeysProto::mutable_allkeys(int index) {
  return allkeys_.Mutable(index);
}
inline ::hadoop::hdfs::BlockKeyProto* ExportedBlockKeysProto::add_allkeys() {
  return allkeys_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockKeyProto >&
ExportedBlockKeysProto::allkeys() const {
  return allkeys_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockKeyProto >*
ExportedBlockKeysProto::mutable_allkeys() {
  return &allkeys_;
}

// -------------------------------------------------------------------

// RecoveringBlockProto

// required uint64 newGenStamp = 1;
inline bool RecoveringBlockProto::has_newgenstamp() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RecoveringBlockProto::set_has_newgenstamp() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RecoveringBlockProto::clear_has_newgenstamp() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RecoveringBlockProto::clear_newgenstamp() {
  newgenstamp_ = GOOGLE_ULONGLONG(0);
  clear_has_newgenstamp();
}
inline ::google::protobuf::uint64 RecoveringBlockProto::newgenstamp() const {
  return newgenstamp_;
}
inline void RecoveringBlockProto::set_newgenstamp(::google::protobuf::uint64 value) {
  set_has_newgenstamp();
  newgenstamp_ = value;
}

// required .hadoop.hdfs.LocatedBlockProto block = 2;
inline bool RecoveringBlockProto::has_block() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RecoveringBlockProto::set_has_block() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RecoveringBlockProto::clear_has_block() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RecoveringBlockProto::clear_block() {
  if (block_ != NULL) block_->::hadoop::hdfs::LocatedBlockProto::Clear();
  clear_has_block();
}
inline const ::hadoop::hdfs::LocatedBlockProto& RecoveringBlockProto::block() const {
  return block_ != NULL ? *block_ : *default_instance_->block_;
}
inline ::hadoop::hdfs::LocatedBlockProto* RecoveringBlockProto::mutable_block() {
  set_has_block();
  if (block_ == NULL) block_ = new ::hadoop::hdfs::LocatedBlockProto;
  return block_;
}
inline ::hadoop::hdfs::LocatedBlockProto* RecoveringBlockProto::release_block() {
  clear_has_block();
  ::hadoop::hdfs::LocatedBlockProto* temp = block_;
  block_ = NULL;
  return temp;
}
inline void RecoveringBlockProto::set_allocated_block(::hadoop::hdfs::LocatedBlockProto* block) {
  delete block_;
  block_ = block;
  if (block) {
    set_has_block();
  } else {
    clear_has_block();
  }
}

// -------------------------------------------------------------------

// VersionRequestProto

// -------------------------------------------------------------------

// VersionResponseProto

// required .hadoop.hdfs.NamespaceInfoProto info = 1;
inline bool VersionResponseProto::has_info() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void VersionResponseProto::set_has_info() {
  _has_bits_[0] |= 0x00000001u;
}
inline void VersionResponseProto::clear_has_info() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void VersionResponseProto::clear_info() {
  if (info_ != NULL) info_->::hadoop::hdfs::NamespaceInfoProto::Clear();
  clear_has_info();
}
inline const ::hadoop::hdfs::NamespaceInfoProto& VersionResponseProto::info() const {
  return info_ != NULL ? *info_ : *default_instance_->info_;
}
inline ::hadoop::hdfs::NamespaceInfoProto* VersionResponseProto::mutable_info() {
  set_has_info();
  if (info_ == NULL) info_ = new ::hadoop::hdfs::NamespaceInfoProto;
  return info_;
}
inline ::hadoop::hdfs::NamespaceInfoProto* VersionResponseProto::release_info() {
  clear_has_info();
  ::hadoop::hdfs::NamespaceInfoProto* temp = info_;
  info_ = NULL;
  return temp;
}
inline void VersionResponseProto::set_allocated_info(::hadoop::hdfs::NamespaceInfoProto* info) {
  delete info_;
  info_ = info;
  if (info) {
    set_has_info();
  } else {
    clear_has_info();
  }
}

// -------------------------------------------------------------------

// SnapshotInfoProto

// required string snapshotName = 1;
inline bool SnapshotInfoProto::has_snapshotname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotInfoProto::set_has_snapshotname() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotInfoProto::clear_has_snapshotname() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotInfoProto::clear_snapshotname() {
  if (snapshotname_ != &::google::protobuf::internal::kEmptyString) {
    snapshotname_->clear();
  }
  clear_has_snapshotname();
}
inline const ::std::string& SnapshotInfoProto::snapshotname() const {
  return *snapshotname_;
}
inline void SnapshotInfoProto::set_snapshotname(const ::std::string& value) {
  set_has_snapshotname();
  if (snapshotname_ == &::google::protobuf::internal::kEmptyString) {
    snapshotname_ = new ::std::string;
  }
  snapshotname_->assign(value);
}
inline void SnapshotInfoProto::set_snapshotname(const char* value) {
  set_has_snapshotname();
  if (snapshotname_ == &::google::protobuf::internal::kEmptyString) {
    snapshotname_ = new ::std::string;
  }
  snapshotname_->assign(value);
}
inline void SnapshotInfoProto::set_snapshotname(const char* value, size_t size) {
  set_has_snapshotname();
  if (snapshotname_ == &::google::protobuf::internal::kEmptyString) {
    snapshotname_ = new ::std::string;
  }
  snapshotname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotInfoProto::mutable_snapshotname() {
  set_has_snapshotname();
  if (snapshotname_ == &::google::protobuf::internal::kEmptyString) {
    snapshotname_ = new ::std::string;
  }
  return snapshotname_;
}
inline ::std::string* SnapshotInfoProto::release_snapshotname() {
  clear_has_snapshotname();
  if (snapshotname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = snapshotname_;
    snapshotname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotInfoProto::set_allocated_snapshotname(::std::string* snapshotname) {
  if (snapshotname_ != &::google::protobuf::internal::kEmptyString) {
    delete snapshotname_;
  }
  if (snapshotname) {
    set_has_snapshotname();
    snapshotname_ = snapshotname;
  } else {
    clear_has_snapshotname();
    snapshotname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string snapshotRoot = 2;
inline bool SnapshotInfoProto::has_snapshotroot() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotInfoProto::set_has_snapshotroot() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotInfoProto::clear_has_snapshotroot() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotInfoProto::clear_snapshotroot() {
  if (snapshotroot_ != &::google::protobuf::internal::kEmptyString) {
    snapshotroot_->clear();
  }
  clear_has_snapshotroot();
}
inline const ::std::string& SnapshotInfoProto::snapshotroot() const {
  return *snapshotroot_;
}
inline void SnapshotInfoProto::set_snapshotroot(const ::std::string& value) {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  snapshotroot_->assign(value);
}
inline void SnapshotInfoProto::set_snapshotroot(const char* value) {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  snapshotroot_->assign(value);
}
inline void SnapshotInfoProto::set_snapshotroot(const char* value, size_t size) {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  snapshotroot_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotInfoProto::mutable_snapshotroot() {
  set_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    snapshotroot_ = new ::std::string;
  }
  return snapshotroot_;
}
inline ::std::string* SnapshotInfoProto::release_snapshotroot() {
  clear_has_snapshotroot();
  if (snapshotroot_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = snapshotroot_;
    snapshotroot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotInfoProto::set_allocated_snapshotroot(::std::string* snapshotroot) {
  if (snapshotroot_ != &::google::protobuf::internal::kEmptyString) {
    delete snapshotroot_;
  }
  if (snapshotroot) {
    set_has_snapshotroot();
    snapshotroot_ = snapshotroot;
  } else {
    clear_has_snapshotroot();
    snapshotroot_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.FsPermissionProto permission = 3;
inline bool SnapshotInfoProto::has_permission() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotInfoProto::set_has_permission() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotInfoProto::clear_has_permission() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotInfoProto::clear_permission() {
  if (permission_ != NULL) permission_->::hadoop::hdfs::FsPermissionProto::Clear();
  clear_has_permission();
}
inline const ::hadoop::hdfs::FsPermissionProto& SnapshotInfoProto::permission() const {
  return permission_ != NULL ? *permission_ : *default_instance_->permission_;
}
inline ::hadoop::hdfs::FsPermissionProto* SnapshotInfoProto::mutable_permission() {
  set_has_permission();
  if (permission_ == NULL) permission_ = new ::hadoop::hdfs::FsPermissionProto;
  return permission_;
}
inline ::hadoop::hdfs::FsPermissionProto* SnapshotInfoProto::release_permission() {
  clear_has_permission();
  ::hadoop::hdfs::FsPermissionProto* temp = permission_;
  permission_ = NULL;
  return temp;
}
inline void SnapshotInfoProto::set_allocated_permission(::hadoop::hdfs::FsPermissionProto* permission) {
  delete permission_;
  permission_ = permission;
  if (permission) {
    set_has_permission();
  } else {
    clear_has_permission();
  }
}

// required string owner = 4;
inline bool SnapshotInfoProto::has_owner() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void SnapshotInfoProto::set_has_owner() {
  _has_bits_[0] |= 0x00000008u;
}
inline void SnapshotInfoProto::clear_has_owner() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void SnapshotInfoProto::clear_owner() {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    owner_->clear();
  }
  clear_has_owner();
}
inline const ::std::string& SnapshotInfoProto::owner() const {
  return *owner_;
}
inline void SnapshotInfoProto::set_owner(const ::std::string& value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void SnapshotInfoProto::set_owner(const char* value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void SnapshotInfoProto::set_owner(const char* value, size_t size) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotInfoProto::mutable_owner() {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  return owner_;
}
inline ::std::string* SnapshotInfoProto::release_owner() {
  clear_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = owner_;
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotInfoProto::set_allocated_owner(::std::string* owner) {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    delete owner_;
  }
  if (owner) {
    set_has_owner();
    owner_ = owner;
  } else {
    clear_has_owner();
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string group = 5;
inline bool SnapshotInfoProto::has_group() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void SnapshotInfoProto::set_has_group() {
  _has_bits_[0] |= 0x00000010u;
}
inline void SnapshotInfoProto::clear_has_group() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void SnapshotInfoProto::clear_group() {
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    group_->clear();
  }
  clear_has_group();
}
inline const ::std::string& SnapshotInfoProto::group() const {
  return *group_;
}
inline void SnapshotInfoProto::set_group(const ::std::string& value) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(value);
}
inline void SnapshotInfoProto::set_group(const char* value) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(value);
}
inline void SnapshotInfoProto::set_group(const char* value, size_t size) {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  group_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotInfoProto::mutable_group() {
  set_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    group_ = new ::std::string;
  }
  return group_;
}
inline ::std::string* SnapshotInfoProto::release_group() {
  clear_has_group();
  if (group_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = group_;
    group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotInfoProto::set_allocated_group(::std::string* group) {
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    delete group_;
  }
  if (group) {
    set_has_group();
    group_ = group;
  } else {
    clear_has_group();
    group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string createTime = 6;
inline bool SnapshotInfoProto::has_createtime() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void SnapshotInfoProto::set_has_createtime() {
  _has_bits_[0] |= 0x00000020u;
}
inline void SnapshotInfoProto::clear_has_createtime() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void SnapshotInfoProto::clear_createtime() {
  if (createtime_ != &::google::protobuf::internal::kEmptyString) {
    createtime_->clear();
  }
  clear_has_createtime();
}
inline const ::std::string& SnapshotInfoProto::createtime() const {
  return *createtime_;
}
inline void SnapshotInfoProto::set_createtime(const ::std::string& value) {
  set_has_createtime();
  if (createtime_ == &::google::protobuf::internal::kEmptyString) {
    createtime_ = new ::std::string;
  }
  createtime_->assign(value);
}
inline void SnapshotInfoProto::set_createtime(const char* value) {
  set_has_createtime();
  if (createtime_ == &::google::protobuf::internal::kEmptyString) {
    createtime_ = new ::std::string;
  }
  createtime_->assign(value);
}
inline void SnapshotInfoProto::set_createtime(const char* value, size_t size) {
  set_has_createtime();
  if (createtime_ == &::google::protobuf::internal::kEmptyString) {
    createtime_ = new ::std::string;
  }
  createtime_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotInfoProto::mutable_createtime() {
  set_has_createtime();
  if (createtime_ == &::google::protobuf::internal::kEmptyString) {
    createtime_ = new ::std::string;
  }
  return createtime_;
}
inline ::std::string* SnapshotInfoProto::release_createtime() {
  clear_has_createtime();
  if (createtime_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = createtime_;
    createtime_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotInfoProto::set_allocated_createtime(::std::string* createtime) {
  if (createtime_ != &::google::protobuf::internal::kEmptyString) {
    delete createtime_;
  }
  if (createtime) {
    set_has_createtime();
    createtime_ = createtime;
  } else {
    clear_has_createtime();
    createtime_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// RollingUpgradeStatusProto

// required string blockPoolId = 1;
inline bool RollingUpgradeStatusProto::has_blockpoolid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RollingUpgradeStatusProto::set_has_blockpoolid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RollingUpgradeStatusProto::clear_has_blockpoolid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RollingUpgradeStatusProto::clear_blockpoolid() {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    blockpoolid_->clear();
  }
  clear_has_blockpoolid();
}
inline const ::std::string& RollingUpgradeStatusProto::blockpoolid() const {
  return *blockpoolid_;
}
inline void RollingUpgradeStatusProto::set_blockpoolid(const ::std::string& value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void RollingUpgradeStatusProto::set_blockpoolid(const char* value) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(value);
}
inline void RollingUpgradeStatusProto::set_blockpoolid(const char* value, size_t size) {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  blockpoolid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RollingUpgradeStatusProto::mutable_blockpoolid() {
  set_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    blockpoolid_ = new ::std::string;
  }
  return blockpoolid_;
}
inline ::std::string* RollingUpgradeStatusProto::release_blockpoolid() {
  clear_has_blockpoolid();
  if (blockpoolid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = blockpoolid_;
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RollingUpgradeStatusProto::set_allocated_blockpoolid(::std::string* blockpoolid) {
  if (blockpoolid_ != &::google::protobuf::internal::kEmptyString) {
    delete blockpoolid_;
  }
  if (blockpoolid) {
    set_has_blockpoolid();
    blockpoolid_ = blockpoolid;
  } else {
    clear_has_blockpoolid();
    blockpoolid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::DatanodeInfoProto_AdminState>() {
  return ::hadoop::hdfs::DatanodeInfoProto_AdminState_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::DatanodeStorageProto_StorageState>() {
  return ::hadoop::hdfs::DatanodeStorageProto_StorageState_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::HdfsFileStatusProto_FileType>() {
  return ::hadoop::hdfs::HdfsFileStatusProto_FileType_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto>() {
  return ::hadoop::hdfs::NamenodeRegistrationProto_NamenodeRoleProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::NamenodeCommandProto_Type>() {
  return ::hadoop::hdfs::NamenodeCommandProto_Type_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::StorageTypeProto>() {
  return ::hadoop::hdfs::StorageTypeProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::CipherSuiteProto>() {
  return ::hadoop::hdfs::CipherSuiteProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::CryptoProtocolVersionProto>() {
  return ::hadoop::hdfs::CryptoProtocolVersionProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::ChecksumTypeProto>() {
  return ::hadoop::hdfs::ChecksumTypeProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::ReplicaStateProto>() {
  return ::hadoop::hdfs::ReplicaStateProto_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_hdfs_2eproto__INCLUDED
