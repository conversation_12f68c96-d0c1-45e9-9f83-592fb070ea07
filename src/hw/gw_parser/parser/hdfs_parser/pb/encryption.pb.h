// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: encryption.proto

#ifndef PROTOBUF_encryption_2eproto__INCLUDED
#define PROTOBUF_encryption_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_encryption_2eproto();
void protobuf_AssignDesc_encryption_2eproto();
void protobuf_ShutdownFile_encryption_2eproto();

class CreateEncryptionZoneRequestProto;
class CreateEncryptionZoneResponseProto;
class ListEncryptionZonesRequestProto;
class EncryptionZoneProto;
class ListEncryptionZonesResponseProto;
class GetEZForPathRequestProto;
class GetEZForPathResponseProto;

// ===================================================================

class CreateEncryptionZoneRequestProto : public ::google::protobuf::Message {
 public:
  CreateEncryptionZoneRequestProto();
  virtual ~CreateEncryptionZoneRequestProto();

  CreateEncryptionZoneRequestProto(const CreateEncryptionZoneRequestProto& from);

  inline CreateEncryptionZoneRequestProto& operator=(const CreateEncryptionZoneRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateEncryptionZoneRequestProto& default_instance();

  void Swap(CreateEncryptionZoneRequestProto* other);

  // implements Message ----------------------------------------------

  CreateEncryptionZoneRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CreateEncryptionZoneRequestProto& from);
  void MergeFrom(const CreateEncryptionZoneRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // optional string keyName = 2;
  inline bool has_keyname() const;
  inline void clear_keyname();
  static const int kKeyNameFieldNumber = 2;
  inline const ::std::string& keyname() const;
  inline void set_keyname(const ::std::string& value);
  inline void set_keyname(const char* value);
  inline void set_keyname(const char* value, size_t size);
  inline ::std::string* mutable_keyname();
  inline ::std::string* release_keyname();
  inline void set_allocated_keyname(::std::string* keyname);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CreateEncryptionZoneRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();
  inline void set_has_keyname();
  inline void clear_has_keyname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::std::string* keyname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static CreateEncryptionZoneRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class CreateEncryptionZoneResponseProto : public ::google::protobuf::Message {
 public:
  CreateEncryptionZoneResponseProto();
  virtual ~CreateEncryptionZoneResponseProto();

  CreateEncryptionZoneResponseProto(const CreateEncryptionZoneResponseProto& from);

  inline CreateEncryptionZoneResponseProto& operator=(const CreateEncryptionZoneResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateEncryptionZoneResponseProto& default_instance();

  void Swap(CreateEncryptionZoneResponseProto* other);

  // implements Message ----------------------------------------------

  CreateEncryptionZoneResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CreateEncryptionZoneResponseProto& from);
  void MergeFrom(const CreateEncryptionZoneResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CreateEncryptionZoneResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static CreateEncryptionZoneResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ListEncryptionZonesRequestProto : public ::google::protobuf::Message {
 public:
  ListEncryptionZonesRequestProto();
  virtual ~ListEncryptionZonesRequestProto();

  ListEncryptionZonesRequestProto(const ListEncryptionZonesRequestProto& from);

  inline ListEncryptionZonesRequestProto& operator=(const ListEncryptionZonesRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListEncryptionZonesRequestProto& default_instance();

  void Swap(ListEncryptionZonesRequestProto* other);

  // implements Message ----------------------------------------------

  ListEncryptionZonesRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListEncryptionZonesRequestProto& from);
  void MergeFrom(const ListEncryptionZonesRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::int64 id() const;
  inline void set_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ListEncryptionZonesRequestProto)
 private:
  inline void set_has_id();
  inline void clear_has_id();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 id_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static ListEncryptionZonesRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class EncryptionZoneProto : public ::google::protobuf::Message {
 public:
  EncryptionZoneProto();
  virtual ~EncryptionZoneProto();

  EncryptionZoneProto(const EncryptionZoneProto& from);

  inline EncryptionZoneProto& operator=(const EncryptionZoneProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const EncryptionZoneProto& default_instance();

  void Swap(EncryptionZoneProto* other);

  // implements Message ----------------------------------------------

  EncryptionZoneProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const EncryptionZoneProto& from);
  void MergeFrom(const EncryptionZoneProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::int64 id() const;
  inline void set_id(::google::protobuf::int64 value);

  // required string path = 2;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 2;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const char* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // required .hadoop.hdfs.CipherSuiteProto suite = 3;
  inline bool has_suite() const;
  inline void clear_suite();
  static const int kSuiteFieldNumber = 3;
  inline ::hadoop::hdfs::CipherSuiteProto suite() const;
  inline void set_suite(::hadoop::hdfs::CipherSuiteProto value);

  // required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 4;
  inline bool has_cryptoprotocolversion() const;
  inline void clear_cryptoprotocolversion();
  static const int kCryptoProtocolVersionFieldNumber = 4;
  inline ::hadoop::hdfs::CryptoProtocolVersionProto cryptoprotocolversion() const;
  inline void set_cryptoprotocolversion(::hadoop::hdfs::CryptoProtocolVersionProto value);

  // required string keyName = 5;
  inline bool has_keyname() const;
  inline void clear_keyname();
  static const int kKeyNameFieldNumber = 5;
  inline const ::std::string& keyname() const;
  inline void set_keyname(const ::std::string& value);
  inline void set_keyname(const char* value);
  inline void set_keyname(const char* value, size_t size);
  inline ::std::string* mutable_keyname();
  inline ::std::string* release_keyname();
  inline void set_allocated_keyname(::std::string* keyname);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.EncryptionZoneProto)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_path();
  inline void clear_has_path();
  inline void set_has_suite();
  inline void clear_has_suite();
  inline void set_has_cryptoprotocolversion();
  inline void clear_has_cryptoprotocolversion();
  inline void set_has_keyname();
  inline void clear_has_keyname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 id_;
  ::std::string* path_;
  int suite_;
  int cryptoprotocolversion_;
  ::std::string* keyname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static EncryptionZoneProto* default_instance_;
};
// -------------------------------------------------------------------

class ListEncryptionZonesResponseProto : public ::google::protobuf::Message {
 public:
  ListEncryptionZonesResponseProto();
  virtual ~ListEncryptionZonesResponseProto();

  ListEncryptionZonesResponseProto(const ListEncryptionZonesResponseProto& from);

  inline ListEncryptionZonesResponseProto& operator=(const ListEncryptionZonesResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListEncryptionZonesResponseProto& default_instance();

  void Swap(ListEncryptionZonesResponseProto* other);

  // implements Message ----------------------------------------------

  ListEncryptionZonesResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListEncryptionZonesResponseProto& from);
  void MergeFrom(const ListEncryptionZonesResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.EncryptionZoneProto zones = 1;
  inline int zones_size() const;
  inline void clear_zones();
  static const int kZonesFieldNumber = 1;
  inline const ::hadoop::hdfs::EncryptionZoneProto& zones(int index) const;
  inline ::hadoop::hdfs::EncryptionZoneProto* mutable_zones(int index);
  inline ::hadoop::hdfs::EncryptionZoneProto* add_zones();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EncryptionZoneProto >&
      zones() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EncryptionZoneProto >*
      mutable_zones();

  // required bool hasMore = 2;
  inline bool has_hasmore() const;
  inline void clear_hasmore();
  static const int kHasMoreFieldNumber = 2;
  inline bool hasmore() const;
  inline void set_hasmore(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ListEncryptionZonesResponseProto)
 private:
  inline void set_has_hasmore();
  inline void clear_has_hasmore();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EncryptionZoneProto > zones_;
  bool hasmore_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static ListEncryptionZonesResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetEZForPathRequestProto : public ::google::protobuf::Message {
 public:
  GetEZForPathRequestProto();
  virtual ~GetEZForPathRequestProto();

  GetEZForPathRequestProto(const GetEZForPathRequestProto& from);

  inline GetEZForPathRequestProto& operator=(const GetEZForPathRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetEZForPathRequestProto& default_instance();

  void Swap(GetEZForPathRequestProto* other);

  // implements Message ----------------------------------------------

  GetEZForPathRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetEZForPathRequestProto& from);
  void MergeFrom(const GetEZForPathRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.GetEZForPathRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static GetEZForPathRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetEZForPathResponseProto : public ::google::protobuf::Message {
 public:
  GetEZForPathResponseProto();
  virtual ~GetEZForPathResponseProto();

  GetEZForPathResponseProto(const GetEZForPathResponseProto& from);

  inline GetEZForPathResponseProto& operator=(const GetEZForPathResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetEZForPathResponseProto& default_instance();

  void Swap(GetEZForPathResponseProto* other);

  // implements Message ----------------------------------------------

  GetEZForPathResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetEZForPathResponseProto& from);
  void MergeFrom(const GetEZForPathResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .hadoop.hdfs.EncryptionZoneProto zone = 1;
  inline bool has_zone() const;
  inline void clear_zone();
  static const int kZoneFieldNumber = 1;
  inline const ::hadoop::hdfs::EncryptionZoneProto& zone() const;
  inline ::hadoop::hdfs::EncryptionZoneProto* mutable_zone();
  inline ::hadoop::hdfs::EncryptionZoneProto* release_zone();
  inline void set_allocated_zone(::hadoop::hdfs::EncryptionZoneProto* zone);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.GetEZForPathResponseProto)
 private:
  inline void set_has_zone();
  inline void clear_has_zone();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::EncryptionZoneProto* zone_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_encryption_2eproto();
  friend void protobuf_AssignDesc_encryption_2eproto();
  friend void protobuf_ShutdownFile_encryption_2eproto();

  void InitAsDefaultInstance();
  static GetEZForPathResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// CreateEncryptionZoneRequestProto

// required string src = 1;
inline bool CreateEncryptionZoneRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CreateEncryptionZoneRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CreateEncryptionZoneRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CreateEncryptionZoneRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& CreateEncryptionZoneRequestProto::src() const {
  return *src_;
}
inline void CreateEncryptionZoneRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void CreateEncryptionZoneRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void CreateEncryptionZoneRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CreateEncryptionZoneRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* CreateEncryptionZoneRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CreateEncryptionZoneRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string keyName = 2;
inline bool CreateEncryptionZoneRequestProto::has_keyname() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CreateEncryptionZoneRequestProto::set_has_keyname() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CreateEncryptionZoneRequestProto::clear_has_keyname() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CreateEncryptionZoneRequestProto::clear_keyname() {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    keyname_->clear();
  }
  clear_has_keyname();
}
inline const ::std::string& CreateEncryptionZoneRequestProto::keyname() const {
  return *keyname_;
}
inline void CreateEncryptionZoneRequestProto::set_keyname(const ::std::string& value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void CreateEncryptionZoneRequestProto::set_keyname(const char* value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void CreateEncryptionZoneRequestProto::set_keyname(const char* value, size_t size) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CreateEncryptionZoneRequestProto::mutable_keyname() {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  return keyname_;
}
inline ::std::string* CreateEncryptionZoneRequestProto::release_keyname() {
  clear_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = keyname_;
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CreateEncryptionZoneRequestProto::set_allocated_keyname(::std::string* keyname) {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    delete keyname_;
  }
  if (keyname) {
    set_has_keyname();
    keyname_ = keyname;
  } else {
    clear_has_keyname();
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// CreateEncryptionZoneResponseProto

// -------------------------------------------------------------------

// ListEncryptionZonesRequestProto

// required int64 id = 1;
inline bool ListEncryptionZonesRequestProto::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ListEncryptionZonesRequestProto::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ListEncryptionZonesRequestProto::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ListEncryptionZonesRequestProto::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::int64 ListEncryptionZonesRequestProto::id() const {
  return id_;
}
inline void ListEncryptionZonesRequestProto::set_id(::google::protobuf::int64 value) {
  set_has_id();
  id_ = value;
}

// -------------------------------------------------------------------

// EncryptionZoneProto

// required int64 id = 1;
inline bool EncryptionZoneProto::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void EncryptionZoneProto::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void EncryptionZoneProto::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void EncryptionZoneProto::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::int64 EncryptionZoneProto::id() const {
  return id_;
}
inline void EncryptionZoneProto::set_id(::google::protobuf::int64 value) {
  set_has_id();
  id_ = value;
}

// required string path = 2;
inline bool EncryptionZoneProto::has_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void EncryptionZoneProto::set_has_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void EncryptionZoneProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void EncryptionZoneProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& EncryptionZoneProto::path() const {
  return *path_;
}
inline void EncryptionZoneProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void EncryptionZoneProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void EncryptionZoneProto::set_path(const char* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* EncryptionZoneProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* EncryptionZoneProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void EncryptionZoneProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.CipherSuiteProto suite = 3;
inline bool EncryptionZoneProto::has_suite() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void EncryptionZoneProto::set_has_suite() {
  _has_bits_[0] |= 0x00000004u;
}
inline void EncryptionZoneProto::clear_has_suite() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void EncryptionZoneProto::clear_suite() {
  suite_ = 1;
  clear_has_suite();
}
inline ::hadoop::hdfs::CipherSuiteProto EncryptionZoneProto::suite() const {
  return static_cast< ::hadoop::hdfs::CipherSuiteProto >(suite_);
}
inline void EncryptionZoneProto::set_suite(::hadoop::hdfs::CipherSuiteProto value) {
  assert(::hadoop::hdfs::CipherSuiteProto_IsValid(value));
  set_has_suite();
  suite_ = value;
}

// required .hadoop.hdfs.CryptoProtocolVersionProto cryptoProtocolVersion = 4;
inline bool EncryptionZoneProto::has_cryptoprotocolversion() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void EncryptionZoneProto::set_has_cryptoprotocolversion() {
  _has_bits_[0] |= 0x00000008u;
}
inline void EncryptionZoneProto::clear_has_cryptoprotocolversion() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void EncryptionZoneProto::clear_cryptoprotocolversion() {
  cryptoprotocolversion_ = 1;
  clear_has_cryptoprotocolversion();
}
inline ::hadoop::hdfs::CryptoProtocolVersionProto EncryptionZoneProto::cryptoprotocolversion() const {
  return static_cast< ::hadoop::hdfs::CryptoProtocolVersionProto >(cryptoprotocolversion_);
}
inline void EncryptionZoneProto::set_cryptoprotocolversion(::hadoop::hdfs::CryptoProtocolVersionProto value) {
  assert(::hadoop::hdfs::CryptoProtocolVersionProto_IsValid(value));
  set_has_cryptoprotocolversion();
  cryptoprotocolversion_ = value;
}

// required string keyName = 5;
inline bool EncryptionZoneProto::has_keyname() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void EncryptionZoneProto::set_has_keyname() {
  _has_bits_[0] |= 0x00000010u;
}
inline void EncryptionZoneProto::clear_has_keyname() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void EncryptionZoneProto::clear_keyname() {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    keyname_->clear();
  }
  clear_has_keyname();
}
inline const ::std::string& EncryptionZoneProto::keyname() const {
  return *keyname_;
}
inline void EncryptionZoneProto::set_keyname(const ::std::string& value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void EncryptionZoneProto::set_keyname(const char* value) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(value);
}
inline void EncryptionZoneProto::set_keyname(const char* value, size_t size) {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  keyname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* EncryptionZoneProto::mutable_keyname() {
  set_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    keyname_ = new ::std::string;
  }
  return keyname_;
}
inline ::std::string* EncryptionZoneProto::release_keyname() {
  clear_has_keyname();
  if (keyname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = keyname_;
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void EncryptionZoneProto::set_allocated_keyname(::std::string* keyname) {
  if (keyname_ != &::google::protobuf::internal::kEmptyString) {
    delete keyname_;
  }
  if (keyname) {
    set_has_keyname();
    keyname_ = keyname;
  } else {
    clear_has_keyname();
    keyname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ListEncryptionZonesResponseProto

// repeated .hadoop.hdfs.EncryptionZoneProto zones = 1;
inline int ListEncryptionZonesResponseProto::zones_size() const {
  return zones_.size();
}
inline void ListEncryptionZonesResponseProto::clear_zones() {
  zones_.Clear();
}
inline const ::hadoop::hdfs::EncryptionZoneProto& ListEncryptionZonesResponseProto::zones(int index) const {
  return zones_.Get(index);
}
inline ::hadoop::hdfs::EncryptionZoneProto* ListEncryptionZonesResponseProto::mutable_zones(int index) {
  return zones_.Mutable(index);
}
inline ::hadoop::hdfs::EncryptionZoneProto* ListEncryptionZonesResponseProto::add_zones() {
  return zones_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EncryptionZoneProto >&
ListEncryptionZonesResponseProto::zones() const {
  return zones_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EncryptionZoneProto >*
ListEncryptionZonesResponseProto::mutable_zones() {
  return &zones_;
}

// required bool hasMore = 2;
inline bool ListEncryptionZonesResponseProto::has_hasmore() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ListEncryptionZonesResponseProto::set_has_hasmore() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ListEncryptionZonesResponseProto::clear_has_hasmore() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ListEncryptionZonesResponseProto::clear_hasmore() {
  hasmore_ = false;
  clear_has_hasmore();
}
inline bool ListEncryptionZonesResponseProto::hasmore() const {
  return hasmore_;
}
inline void ListEncryptionZonesResponseProto::set_hasmore(bool value) {
  set_has_hasmore();
  hasmore_ = value;
}

// -------------------------------------------------------------------

// GetEZForPathRequestProto

// required string src = 1;
inline bool GetEZForPathRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetEZForPathRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetEZForPathRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetEZForPathRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& GetEZForPathRequestProto::src() const {
  return *src_;
}
inline void GetEZForPathRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void GetEZForPathRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void GetEZForPathRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetEZForPathRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* GetEZForPathRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetEZForPathRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// GetEZForPathResponseProto

// optional .hadoop.hdfs.EncryptionZoneProto zone = 1;
inline bool GetEZForPathResponseProto::has_zone() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetEZForPathResponseProto::set_has_zone() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetEZForPathResponseProto::clear_has_zone() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetEZForPathResponseProto::clear_zone() {
  if (zone_ != NULL) zone_->::hadoop::hdfs::EncryptionZoneProto::Clear();
  clear_has_zone();
}
inline const ::hadoop::hdfs::EncryptionZoneProto& GetEZForPathResponseProto::zone() const {
  return zone_ != NULL ? *zone_ : *default_instance_->zone_;
}
inline ::hadoop::hdfs::EncryptionZoneProto* GetEZForPathResponseProto::mutable_zone() {
  set_has_zone();
  if (zone_ == NULL) zone_ = new ::hadoop::hdfs::EncryptionZoneProto;
  return zone_;
}
inline ::hadoop::hdfs::EncryptionZoneProto* GetEZForPathResponseProto::release_zone() {
  clear_has_zone();
  ::hadoop::hdfs::EncryptionZoneProto* temp = zone_;
  zone_ = NULL;
  return temp;
}
inline void GetEZForPathResponseProto::set_allocated_zone(::hadoop::hdfs::EncryptionZoneProto* zone) {
  delete zone_;
  zone_ = zone;
  if (zone) {
    set_has_zone();
  } else {
    clear_has_zone();
  }
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_encryption_2eproto__INCLUDED
