// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: inotify.proto

#ifndef PROTOBUF_inotify_2eproto__INCLUDED
#define PROTOBUF_inotify_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "acl.pb.h"
#include "xattr.pb.h"
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_inotify_2eproto();
void protobuf_AssignDesc_inotify_2eproto();
void protobuf_ShutdownFile_inotify_2eproto();

class EventProto;
class CreateEventProto;
class CloseEventProto;
class AppendEventProto;
class RenameEventProto;
class MetadataUpdateEventProto;
class UnlinkEventProto;
class EventsListProto;

enum EventType {
  EVENT_CREATE = 0,
  EVENT_CLOSE = 1,
  EVENT_APPEND = 2,
  EVENT_RENAME = 3,
  EVENT_METADATA = 4,
  EVENT_UNLINK = 5
};
bool EventType_IsValid(int value);
const EventType EventType_MIN = EVENT_CREATE;
const EventType EventType_MAX = EVENT_UNLINK;
const int EventType_ARRAYSIZE = EventType_MAX + 1;

const ::google::protobuf::EnumDescriptor* EventType_descriptor();
inline const ::std::string& EventType_Name(EventType value) {
  return ::google::protobuf::internal::NameOfEnum(
    EventType_descriptor(), value);
}
inline bool EventType_Parse(
    const ::std::string& name, EventType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<EventType>(
    EventType_descriptor(), name, value);
}
enum INodeType {
  I_TYPE_FILE = 0,
  I_TYPE_DIRECTORY = 1,
  I_TYPE_SYMLINK = 2
};
bool INodeType_IsValid(int value);
const INodeType INodeType_MIN = I_TYPE_FILE;
const INodeType INodeType_MAX = I_TYPE_SYMLINK;
const int INodeType_ARRAYSIZE = INodeType_MAX + 1;

const ::google::protobuf::EnumDescriptor* INodeType_descriptor();
inline const ::std::string& INodeType_Name(INodeType value) {
  return ::google::protobuf::internal::NameOfEnum(
    INodeType_descriptor(), value);
}
inline bool INodeType_Parse(
    const ::std::string& name, INodeType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<INodeType>(
    INodeType_descriptor(), name, value);
}
enum MetadataUpdateType {
  META_TYPE_TIMES = 0,
  META_TYPE_REPLICATION = 1,
  META_TYPE_OWNER = 2,
  META_TYPE_PERMS = 3,
  META_TYPE_ACLS = 4,
  META_TYPE_XATTRS = 5
};
bool MetadataUpdateType_IsValid(int value);
const MetadataUpdateType MetadataUpdateType_MIN = META_TYPE_TIMES;
const MetadataUpdateType MetadataUpdateType_MAX = META_TYPE_XATTRS;
const int MetadataUpdateType_ARRAYSIZE = MetadataUpdateType_MAX + 1;

const ::google::protobuf::EnumDescriptor* MetadataUpdateType_descriptor();
inline const ::std::string& MetadataUpdateType_Name(MetadataUpdateType value) {
  return ::google::protobuf::internal::NameOfEnum(
    MetadataUpdateType_descriptor(), value);
}
inline bool MetadataUpdateType_Parse(
    const ::std::string& name, MetadataUpdateType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<MetadataUpdateType>(
    MetadataUpdateType_descriptor(), name, value);
}
// ===================================================================

class EventProto : public ::google::protobuf::Message {
 public:
  EventProto();
  virtual ~EventProto();

  EventProto(const EventProto& from);

  inline EventProto& operator=(const EventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const EventProto& default_instance();

  void Swap(EventProto* other);

  // implements Message ----------------------------------------------

  EventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const EventProto& from);
  void MergeFrom(const EventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.EventType type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::hadoop::hdfs::EventType type() const;
  inline void set_type(::hadoop::hdfs::EventType value);

  // required bytes contents = 2;
  inline bool has_contents() const;
  inline void clear_contents();
  static const int kContentsFieldNumber = 2;
  inline const ::std::string& contents() const;
  inline void set_contents(const ::std::string& value);
  inline void set_contents(const char* value);
  inline void set_contents(const void* value, size_t size);
  inline ::std::string* mutable_contents();
  inline ::std::string* release_contents();
  inline void set_allocated_contents(::std::string* contents);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.EventProto)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_contents();
  inline void clear_has_contents();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* contents_;
  int type_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static EventProto* default_instance_;
};
// -------------------------------------------------------------------

class CreateEventProto : public ::google::protobuf::Message {
 public:
  CreateEventProto();
  virtual ~CreateEventProto();

  CreateEventProto(const CreateEventProto& from);

  inline CreateEventProto& operator=(const CreateEventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateEventProto& default_instance();

  void Swap(CreateEventProto* other);

  // implements Message ----------------------------------------------

  CreateEventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CreateEventProto& from);
  void MergeFrom(const CreateEventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.INodeType type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::hadoop::hdfs::INodeType type() const;
  inline void set_type(::hadoop::hdfs::INodeType value);

  // required string path = 2;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 2;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const char* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // required int64 ctime = 3;
  inline bool has_ctime() const;
  inline void clear_ctime();
  static const int kCtimeFieldNumber = 3;
  inline ::google::protobuf::int64 ctime() const;
  inline void set_ctime(::google::protobuf::int64 value);

  // required string ownerName = 4;
  inline bool has_ownername() const;
  inline void clear_ownername();
  static const int kOwnerNameFieldNumber = 4;
  inline const ::std::string& ownername() const;
  inline void set_ownername(const ::std::string& value);
  inline void set_ownername(const char* value);
  inline void set_ownername(const char* value, size_t size);
  inline ::std::string* mutable_ownername();
  inline ::std::string* release_ownername();
  inline void set_allocated_ownername(::std::string* ownername);

  // required string groupName = 5;
  inline bool has_groupname() const;
  inline void clear_groupname();
  static const int kGroupNameFieldNumber = 5;
  inline const ::std::string& groupname() const;
  inline void set_groupname(const ::std::string& value);
  inline void set_groupname(const char* value);
  inline void set_groupname(const char* value, size_t size);
  inline ::std::string* mutable_groupname();
  inline ::std::string* release_groupname();
  inline void set_allocated_groupname(::std::string* groupname);

  // required .hadoop.hdfs.FsPermissionProto perms = 6;
  inline bool has_perms() const;
  inline void clear_perms();
  static const int kPermsFieldNumber = 6;
  inline const ::hadoop::hdfs::FsPermissionProto& perms() const;
  inline ::hadoop::hdfs::FsPermissionProto* mutable_perms();
  inline ::hadoop::hdfs::FsPermissionProto* release_perms();
  inline void set_allocated_perms(::hadoop::hdfs::FsPermissionProto* perms);

  // optional int32 replication = 7;
  inline bool has_replication() const;
  inline void clear_replication();
  static const int kReplicationFieldNumber = 7;
  inline ::google::protobuf::int32 replication() const;
  inline void set_replication(::google::protobuf::int32 value);

  // optional string symlinkTarget = 8;
  inline bool has_symlinktarget() const;
  inline void clear_symlinktarget();
  static const int kSymlinkTargetFieldNumber = 8;
  inline const ::std::string& symlinktarget() const;
  inline void set_symlinktarget(const ::std::string& value);
  inline void set_symlinktarget(const char* value);
  inline void set_symlinktarget(const char* value, size_t size);
  inline ::std::string* mutable_symlinktarget();
  inline ::std::string* release_symlinktarget();
  inline void set_allocated_symlinktarget(::std::string* symlinktarget);

  // optional bool overwrite = 9;
  inline bool has_overwrite() const;
  inline void clear_overwrite();
  static const int kOverwriteFieldNumber = 9;
  inline bool overwrite() const;
  inline void set_overwrite(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CreateEventProto)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_path();
  inline void clear_has_path();
  inline void set_has_ctime();
  inline void clear_has_ctime();
  inline void set_has_ownername();
  inline void clear_has_ownername();
  inline void set_has_groupname();
  inline void clear_has_groupname();
  inline void set_has_perms();
  inline void clear_has_perms();
  inline void set_has_replication();
  inline void clear_has_replication();
  inline void set_has_symlinktarget();
  inline void clear_has_symlinktarget();
  inline void set_has_overwrite();
  inline void clear_has_overwrite();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* path_;
  ::google::protobuf::int64 ctime_;
  ::std::string* ownername_;
  int type_;
  ::google::protobuf::int32 replication_;
  ::std::string* groupname_;
  ::hadoop::hdfs::FsPermissionProto* perms_;
  ::std::string* symlinktarget_;
  bool overwrite_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(9 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static CreateEventProto* default_instance_;
};
// -------------------------------------------------------------------

class CloseEventProto : public ::google::protobuf::Message {
 public:
  CloseEventProto();
  virtual ~CloseEventProto();

  CloseEventProto(const CloseEventProto& from);

  inline CloseEventProto& operator=(const CloseEventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CloseEventProto& default_instance();

  void Swap(CloseEventProto* other);

  // implements Message ----------------------------------------------

  CloseEventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CloseEventProto& from);
  void MergeFrom(const CloseEventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string path = 1;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 1;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const char* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // required int64 fileSize = 2;
  inline bool has_filesize() const;
  inline void clear_filesize();
  static const int kFileSizeFieldNumber = 2;
  inline ::google::protobuf::int64 filesize() const;
  inline void set_filesize(::google::protobuf::int64 value);

  // required int64 timestamp = 3;
  inline bool has_timestamp() const;
  inline void clear_timestamp();
  static const int kTimestampFieldNumber = 3;
  inline ::google::protobuf::int64 timestamp() const;
  inline void set_timestamp(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CloseEventProto)
 private:
  inline void set_has_path();
  inline void clear_has_path();
  inline void set_has_filesize();
  inline void clear_has_filesize();
  inline void set_has_timestamp();
  inline void clear_has_timestamp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* path_;
  ::google::protobuf::int64 filesize_;
  ::google::protobuf::int64 timestamp_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static CloseEventProto* default_instance_;
};
// -------------------------------------------------------------------

class AppendEventProto : public ::google::protobuf::Message {
 public:
  AppendEventProto();
  virtual ~AppendEventProto();

  AppendEventProto(const AppendEventProto& from);

  inline AppendEventProto& operator=(const AppendEventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AppendEventProto& default_instance();

  void Swap(AppendEventProto* other);

  // implements Message ----------------------------------------------

  AppendEventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AppendEventProto& from);
  void MergeFrom(const AppendEventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string path = 1;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 1;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const char* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.AppendEventProto)
 private:
  inline void set_has_path();
  inline void clear_has_path();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* path_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static AppendEventProto* default_instance_;
};
// -------------------------------------------------------------------

class RenameEventProto : public ::google::protobuf::Message {
 public:
  RenameEventProto();
  virtual ~RenameEventProto();

  RenameEventProto(const RenameEventProto& from);

  inline RenameEventProto& operator=(const RenameEventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RenameEventProto& default_instance();

  void Swap(RenameEventProto* other);

  // implements Message ----------------------------------------------

  RenameEventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RenameEventProto& from);
  void MergeFrom(const RenameEventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string srcPath = 1;
  inline bool has_srcpath() const;
  inline void clear_srcpath();
  static const int kSrcPathFieldNumber = 1;
  inline const ::std::string& srcpath() const;
  inline void set_srcpath(const ::std::string& value);
  inline void set_srcpath(const char* value);
  inline void set_srcpath(const char* value, size_t size);
  inline ::std::string* mutable_srcpath();
  inline ::std::string* release_srcpath();
  inline void set_allocated_srcpath(::std::string* srcpath);

  // required string destPath = 2;
  inline bool has_destpath() const;
  inline void clear_destpath();
  static const int kDestPathFieldNumber = 2;
  inline const ::std::string& destpath() const;
  inline void set_destpath(const ::std::string& value);
  inline void set_destpath(const char* value);
  inline void set_destpath(const char* value, size_t size);
  inline ::std::string* mutable_destpath();
  inline ::std::string* release_destpath();
  inline void set_allocated_destpath(::std::string* destpath);

  // required int64 timestamp = 3;
  inline bool has_timestamp() const;
  inline void clear_timestamp();
  static const int kTimestampFieldNumber = 3;
  inline ::google::protobuf::int64 timestamp() const;
  inline void set_timestamp(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RenameEventProto)
 private:
  inline void set_has_srcpath();
  inline void clear_has_srcpath();
  inline void set_has_destpath();
  inline void clear_has_destpath();
  inline void set_has_timestamp();
  inline void clear_has_timestamp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* srcpath_;
  ::std::string* destpath_;
  ::google::protobuf::int64 timestamp_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static RenameEventProto* default_instance_;
};
// -------------------------------------------------------------------

class MetadataUpdateEventProto : public ::google::protobuf::Message {
 public:
  MetadataUpdateEventProto();
  virtual ~MetadataUpdateEventProto();

  MetadataUpdateEventProto(const MetadataUpdateEventProto& from);

  inline MetadataUpdateEventProto& operator=(const MetadataUpdateEventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MetadataUpdateEventProto& default_instance();

  void Swap(MetadataUpdateEventProto* other);

  // implements Message ----------------------------------------------

  MetadataUpdateEventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MetadataUpdateEventProto& from);
  void MergeFrom(const MetadataUpdateEventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string path = 1;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 1;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const char* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // required .hadoop.hdfs.MetadataUpdateType type = 2;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 2;
  inline ::hadoop::hdfs::MetadataUpdateType type() const;
  inline void set_type(::hadoop::hdfs::MetadataUpdateType value);

  // optional int64 mtime = 3;
  inline bool has_mtime() const;
  inline void clear_mtime();
  static const int kMtimeFieldNumber = 3;
  inline ::google::protobuf::int64 mtime() const;
  inline void set_mtime(::google::protobuf::int64 value);

  // optional int64 atime = 4;
  inline bool has_atime() const;
  inline void clear_atime();
  static const int kAtimeFieldNumber = 4;
  inline ::google::protobuf::int64 atime() const;
  inline void set_atime(::google::protobuf::int64 value);

  // optional int32 replication = 5;
  inline bool has_replication() const;
  inline void clear_replication();
  static const int kReplicationFieldNumber = 5;
  inline ::google::protobuf::int32 replication() const;
  inline void set_replication(::google::protobuf::int32 value);

  // optional string ownerName = 6;
  inline bool has_ownername() const;
  inline void clear_ownername();
  static const int kOwnerNameFieldNumber = 6;
  inline const ::std::string& ownername() const;
  inline void set_ownername(const ::std::string& value);
  inline void set_ownername(const char* value);
  inline void set_ownername(const char* value, size_t size);
  inline ::std::string* mutable_ownername();
  inline ::std::string* release_ownername();
  inline void set_allocated_ownername(::std::string* ownername);

  // optional string groupName = 7;
  inline bool has_groupname() const;
  inline void clear_groupname();
  static const int kGroupNameFieldNumber = 7;
  inline const ::std::string& groupname() const;
  inline void set_groupname(const ::std::string& value);
  inline void set_groupname(const char* value);
  inline void set_groupname(const char* value, size_t size);
  inline ::std::string* mutable_groupname();
  inline ::std::string* release_groupname();
  inline void set_allocated_groupname(::std::string* groupname);

  // optional .hadoop.hdfs.FsPermissionProto perms = 8;
  inline bool has_perms() const;
  inline void clear_perms();
  static const int kPermsFieldNumber = 8;
  inline const ::hadoop::hdfs::FsPermissionProto& perms() const;
  inline ::hadoop::hdfs::FsPermissionProto* mutable_perms();
  inline ::hadoop::hdfs::FsPermissionProto* release_perms();
  inline void set_allocated_perms(::hadoop::hdfs::FsPermissionProto* perms);

  // repeated .hadoop.hdfs.AclEntryProto acls = 9;
  inline int acls_size() const;
  inline void clear_acls();
  static const int kAclsFieldNumber = 9;
  inline const ::hadoop::hdfs::AclEntryProto& acls(int index) const;
  inline ::hadoop::hdfs::AclEntryProto* mutable_acls(int index);
  inline ::hadoop::hdfs::AclEntryProto* add_acls();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
      acls() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
      mutable_acls();

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 10;
  inline int xattrs_size() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 10;
  inline const ::hadoop::hdfs::XAttrProto& xattrs(int index) const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattrs(int index);
  inline ::hadoop::hdfs::XAttrProto* add_xattrs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
      xattrs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
      mutable_xattrs();

  // optional bool xAttrsRemoved = 11;
  inline bool has_xattrsremoved() const;
  inline void clear_xattrsremoved();
  static const int kXAttrsRemovedFieldNumber = 11;
  inline bool xattrsremoved() const;
  inline void set_xattrsremoved(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.MetadataUpdateEventProto)
 private:
  inline void set_has_path();
  inline void clear_has_path();
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_mtime();
  inline void clear_has_mtime();
  inline void set_has_atime();
  inline void clear_has_atime();
  inline void set_has_replication();
  inline void clear_has_replication();
  inline void set_has_ownername();
  inline void clear_has_ownername();
  inline void set_has_groupname();
  inline void clear_has_groupname();
  inline void set_has_perms();
  inline void clear_has_perms();
  inline void set_has_xattrsremoved();
  inline void clear_has_xattrsremoved();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* path_;
  ::google::protobuf::int64 mtime_;
  int type_;
  ::google::protobuf::int32 replication_;
  ::google::protobuf::int64 atime_;
  ::std::string* ownername_;
  ::std::string* groupname_;
  ::hadoop::hdfs::FsPermissionProto* perms_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto > acls_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto > xattrs_;
  bool xattrsremoved_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(11 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static MetadataUpdateEventProto* default_instance_;
};
// -------------------------------------------------------------------

class UnlinkEventProto : public ::google::protobuf::Message {
 public:
  UnlinkEventProto();
  virtual ~UnlinkEventProto();

  UnlinkEventProto(const UnlinkEventProto& from);

  inline UnlinkEventProto& operator=(const UnlinkEventProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UnlinkEventProto& default_instance();

  void Swap(UnlinkEventProto* other);

  // implements Message ----------------------------------------------

  UnlinkEventProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UnlinkEventProto& from);
  void MergeFrom(const UnlinkEventProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string path = 1;
  inline bool has_path() const;
  inline void clear_path();
  static const int kPathFieldNumber = 1;
  inline const ::std::string& path() const;
  inline void set_path(const ::std::string& value);
  inline void set_path(const char* value);
  inline void set_path(const char* value, size_t size);
  inline ::std::string* mutable_path();
  inline ::std::string* release_path();
  inline void set_allocated_path(::std::string* path);

  // required int64 timestamp = 2;
  inline bool has_timestamp() const;
  inline void clear_timestamp();
  static const int kTimestampFieldNumber = 2;
  inline ::google::protobuf::int64 timestamp() const;
  inline void set_timestamp(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.UnlinkEventProto)
 private:
  inline void set_has_path();
  inline void clear_has_path();
  inline void set_has_timestamp();
  inline void clear_has_timestamp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* path_;
  ::google::protobuf::int64 timestamp_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static UnlinkEventProto* default_instance_;
};
// -------------------------------------------------------------------

class EventsListProto : public ::google::protobuf::Message {
 public:
  EventsListProto();
  virtual ~EventsListProto();

  EventsListProto(const EventsListProto& from);

  inline EventsListProto& operator=(const EventsListProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const EventsListProto& default_instance();

  void Swap(EventsListProto* other);

  // implements Message ----------------------------------------------

  EventsListProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const EventsListProto& from);
  void MergeFrom(const EventsListProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.EventProto events = 1;
  inline int events_size() const;
  inline void clear_events();
  static const int kEventsFieldNumber = 1;
  inline const ::hadoop::hdfs::EventProto& events(int index) const;
  inline ::hadoop::hdfs::EventProto* mutable_events(int index);
  inline ::hadoop::hdfs::EventProto* add_events();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EventProto >&
      events() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EventProto >*
      mutable_events();

  // required int64 firstTxid = 2;
  inline bool has_firsttxid() const;
  inline void clear_firsttxid();
  static const int kFirstTxidFieldNumber = 2;
  inline ::google::protobuf::int64 firsttxid() const;
  inline void set_firsttxid(::google::protobuf::int64 value);

  // required int64 lastTxid = 3;
  inline bool has_lasttxid() const;
  inline void clear_lasttxid();
  static const int kLastTxidFieldNumber = 3;
  inline ::google::protobuf::int64 lasttxid() const;
  inline void set_lasttxid(::google::protobuf::int64 value);

  // required int64 syncTxid = 4;
  inline bool has_synctxid() const;
  inline void clear_synctxid();
  static const int kSyncTxidFieldNumber = 4;
  inline ::google::protobuf::int64 synctxid() const;
  inline void set_synctxid(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.EventsListProto)
 private:
  inline void set_has_firsttxid();
  inline void clear_has_firsttxid();
  inline void set_has_lasttxid();
  inline void clear_has_lasttxid();
  inline void set_has_synctxid();
  inline void clear_has_synctxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EventProto > events_;
  ::google::protobuf::int64 firsttxid_;
  ::google::protobuf::int64 lasttxid_;
  ::google::protobuf::int64 synctxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_inotify_2eproto();
  friend void protobuf_AssignDesc_inotify_2eproto();
  friend void protobuf_ShutdownFile_inotify_2eproto();

  void InitAsDefaultInstance();
  static EventsListProto* default_instance_;
};
// ===================================================================


// ===================================================================

// EventProto

// required .hadoop.hdfs.EventType type = 1;
inline bool EventProto::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void EventProto::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void EventProto::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void EventProto::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::hadoop::hdfs::EventType EventProto::type() const {
  return static_cast< ::hadoop::hdfs::EventType >(type_);
}
inline void EventProto::set_type(::hadoop::hdfs::EventType value) {
  assert(::hadoop::hdfs::EventType_IsValid(value));
  set_has_type();
  type_ = value;
}

// required bytes contents = 2;
inline bool EventProto::has_contents() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void EventProto::set_has_contents() {
  _has_bits_[0] |= 0x00000002u;
}
inline void EventProto::clear_has_contents() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void EventProto::clear_contents() {
  if (contents_ != &::google::protobuf::internal::kEmptyString) {
    contents_->clear();
  }
  clear_has_contents();
}
inline const ::std::string& EventProto::contents() const {
  return *contents_;
}
inline void EventProto::set_contents(const ::std::string& value) {
  set_has_contents();
  if (contents_ == &::google::protobuf::internal::kEmptyString) {
    contents_ = new ::std::string;
  }
  contents_->assign(value);
}
inline void EventProto::set_contents(const char* value) {
  set_has_contents();
  if (contents_ == &::google::protobuf::internal::kEmptyString) {
    contents_ = new ::std::string;
  }
  contents_->assign(value);
}
inline void EventProto::set_contents(const void* value, size_t size) {
  set_has_contents();
  if (contents_ == &::google::protobuf::internal::kEmptyString) {
    contents_ = new ::std::string;
  }
  contents_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* EventProto::mutable_contents() {
  set_has_contents();
  if (contents_ == &::google::protobuf::internal::kEmptyString) {
    contents_ = new ::std::string;
  }
  return contents_;
}
inline ::std::string* EventProto::release_contents() {
  clear_has_contents();
  if (contents_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = contents_;
    contents_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void EventProto::set_allocated_contents(::std::string* contents) {
  if (contents_ != &::google::protobuf::internal::kEmptyString) {
    delete contents_;
  }
  if (contents) {
    set_has_contents();
    contents_ = contents;
  } else {
    clear_has_contents();
    contents_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// CreateEventProto

// required .hadoop.hdfs.INodeType type = 1;
inline bool CreateEventProto::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CreateEventProto::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CreateEventProto::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CreateEventProto::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::hadoop::hdfs::INodeType CreateEventProto::type() const {
  return static_cast< ::hadoop::hdfs::INodeType >(type_);
}
inline void CreateEventProto::set_type(::hadoop::hdfs::INodeType value) {
  assert(::hadoop::hdfs::INodeType_IsValid(value));
  set_has_type();
  type_ = value;
}

// required string path = 2;
inline bool CreateEventProto::has_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CreateEventProto::set_has_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CreateEventProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CreateEventProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& CreateEventProto::path() const {
  return *path_;
}
inline void CreateEventProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void CreateEventProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void CreateEventProto::set_path(const char* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CreateEventProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* CreateEventProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CreateEventProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required int64 ctime = 3;
inline bool CreateEventProto::has_ctime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void CreateEventProto::set_has_ctime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void CreateEventProto::clear_has_ctime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void CreateEventProto::clear_ctime() {
  ctime_ = GOOGLE_LONGLONG(0);
  clear_has_ctime();
}
inline ::google::protobuf::int64 CreateEventProto::ctime() const {
  return ctime_;
}
inline void CreateEventProto::set_ctime(::google::protobuf::int64 value) {
  set_has_ctime();
  ctime_ = value;
}

// required string ownerName = 4;
inline bool CreateEventProto::has_ownername() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void CreateEventProto::set_has_ownername() {
  _has_bits_[0] |= 0x00000008u;
}
inline void CreateEventProto::clear_has_ownername() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void CreateEventProto::clear_ownername() {
  if (ownername_ != &::google::protobuf::internal::kEmptyString) {
    ownername_->clear();
  }
  clear_has_ownername();
}
inline const ::std::string& CreateEventProto::ownername() const {
  return *ownername_;
}
inline void CreateEventProto::set_ownername(const ::std::string& value) {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  ownername_->assign(value);
}
inline void CreateEventProto::set_ownername(const char* value) {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  ownername_->assign(value);
}
inline void CreateEventProto::set_ownername(const char* value, size_t size) {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  ownername_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CreateEventProto::mutable_ownername() {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  return ownername_;
}
inline ::std::string* CreateEventProto::release_ownername() {
  clear_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = ownername_;
    ownername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CreateEventProto::set_allocated_ownername(::std::string* ownername) {
  if (ownername_ != &::google::protobuf::internal::kEmptyString) {
    delete ownername_;
  }
  if (ownername) {
    set_has_ownername();
    ownername_ = ownername;
  } else {
    clear_has_ownername();
    ownername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string groupName = 5;
inline bool CreateEventProto::has_groupname() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void CreateEventProto::set_has_groupname() {
  _has_bits_[0] |= 0x00000010u;
}
inline void CreateEventProto::clear_has_groupname() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void CreateEventProto::clear_groupname() {
  if (groupname_ != &::google::protobuf::internal::kEmptyString) {
    groupname_->clear();
  }
  clear_has_groupname();
}
inline const ::std::string& CreateEventProto::groupname() const {
  return *groupname_;
}
inline void CreateEventProto::set_groupname(const ::std::string& value) {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  groupname_->assign(value);
}
inline void CreateEventProto::set_groupname(const char* value) {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  groupname_->assign(value);
}
inline void CreateEventProto::set_groupname(const char* value, size_t size) {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  groupname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CreateEventProto::mutable_groupname() {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  return groupname_;
}
inline ::std::string* CreateEventProto::release_groupname() {
  clear_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = groupname_;
    groupname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CreateEventProto::set_allocated_groupname(::std::string* groupname) {
  if (groupname_ != &::google::protobuf::internal::kEmptyString) {
    delete groupname_;
  }
  if (groupname) {
    set_has_groupname();
    groupname_ = groupname;
  } else {
    clear_has_groupname();
    groupname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.FsPermissionProto perms = 6;
inline bool CreateEventProto::has_perms() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void CreateEventProto::set_has_perms() {
  _has_bits_[0] |= 0x00000020u;
}
inline void CreateEventProto::clear_has_perms() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void CreateEventProto::clear_perms() {
  if (perms_ != NULL) perms_->::hadoop::hdfs::FsPermissionProto::Clear();
  clear_has_perms();
}
inline const ::hadoop::hdfs::FsPermissionProto& CreateEventProto::perms() const {
  return perms_ != NULL ? *perms_ : *default_instance_->perms_;
}
inline ::hadoop::hdfs::FsPermissionProto* CreateEventProto::mutable_perms() {
  set_has_perms();
  if (perms_ == NULL) perms_ = new ::hadoop::hdfs::FsPermissionProto;
  return perms_;
}
inline ::hadoop::hdfs::FsPermissionProto* CreateEventProto::release_perms() {
  clear_has_perms();
  ::hadoop::hdfs::FsPermissionProto* temp = perms_;
  perms_ = NULL;
  return temp;
}
inline void CreateEventProto::set_allocated_perms(::hadoop::hdfs::FsPermissionProto* perms) {
  delete perms_;
  perms_ = perms;
  if (perms) {
    set_has_perms();
  } else {
    clear_has_perms();
  }
}

// optional int32 replication = 7;
inline bool CreateEventProto::has_replication() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void CreateEventProto::set_has_replication() {
  _has_bits_[0] |= 0x00000040u;
}
inline void CreateEventProto::clear_has_replication() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void CreateEventProto::clear_replication() {
  replication_ = 0;
  clear_has_replication();
}
inline ::google::protobuf::int32 CreateEventProto::replication() const {
  return replication_;
}
inline void CreateEventProto::set_replication(::google::protobuf::int32 value) {
  set_has_replication();
  replication_ = value;
}

// optional string symlinkTarget = 8;
inline bool CreateEventProto::has_symlinktarget() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void CreateEventProto::set_has_symlinktarget() {
  _has_bits_[0] |= 0x00000080u;
}
inline void CreateEventProto::clear_has_symlinktarget() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void CreateEventProto::clear_symlinktarget() {
  if (symlinktarget_ != &::google::protobuf::internal::kEmptyString) {
    symlinktarget_->clear();
  }
  clear_has_symlinktarget();
}
inline const ::std::string& CreateEventProto::symlinktarget() const {
  return *symlinktarget_;
}
inline void CreateEventProto::set_symlinktarget(const ::std::string& value) {
  set_has_symlinktarget();
  if (symlinktarget_ == &::google::protobuf::internal::kEmptyString) {
    symlinktarget_ = new ::std::string;
  }
  symlinktarget_->assign(value);
}
inline void CreateEventProto::set_symlinktarget(const char* value) {
  set_has_symlinktarget();
  if (symlinktarget_ == &::google::protobuf::internal::kEmptyString) {
    symlinktarget_ = new ::std::string;
  }
  symlinktarget_->assign(value);
}
inline void CreateEventProto::set_symlinktarget(const char* value, size_t size) {
  set_has_symlinktarget();
  if (symlinktarget_ == &::google::protobuf::internal::kEmptyString) {
    symlinktarget_ = new ::std::string;
  }
  symlinktarget_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CreateEventProto::mutable_symlinktarget() {
  set_has_symlinktarget();
  if (symlinktarget_ == &::google::protobuf::internal::kEmptyString) {
    symlinktarget_ = new ::std::string;
  }
  return symlinktarget_;
}
inline ::std::string* CreateEventProto::release_symlinktarget() {
  clear_has_symlinktarget();
  if (symlinktarget_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = symlinktarget_;
    symlinktarget_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CreateEventProto::set_allocated_symlinktarget(::std::string* symlinktarget) {
  if (symlinktarget_ != &::google::protobuf::internal::kEmptyString) {
    delete symlinktarget_;
  }
  if (symlinktarget) {
    set_has_symlinktarget();
    symlinktarget_ = symlinktarget;
  } else {
    clear_has_symlinktarget();
    symlinktarget_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bool overwrite = 9;
inline bool CreateEventProto::has_overwrite() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void CreateEventProto::set_has_overwrite() {
  _has_bits_[0] |= 0x00000100u;
}
inline void CreateEventProto::clear_has_overwrite() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void CreateEventProto::clear_overwrite() {
  overwrite_ = false;
  clear_has_overwrite();
}
inline bool CreateEventProto::overwrite() const {
  return overwrite_;
}
inline void CreateEventProto::set_overwrite(bool value) {
  set_has_overwrite();
  overwrite_ = value;
}

// -------------------------------------------------------------------

// CloseEventProto

// required string path = 1;
inline bool CloseEventProto::has_path() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CloseEventProto::set_has_path() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CloseEventProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CloseEventProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& CloseEventProto::path() const {
  return *path_;
}
inline void CloseEventProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void CloseEventProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void CloseEventProto::set_path(const char* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* CloseEventProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* CloseEventProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void CloseEventProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required int64 fileSize = 2;
inline bool CloseEventProto::has_filesize() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CloseEventProto::set_has_filesize() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CloseEventProto::clear_has_filesize() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CloseEventProto::clear_filesize() {
  filesize_ = GOOGLE_LONGLONG(0);
  clear_has_filesize();
}
inline ::google::protobuf::int64 CloseEventProto::filesize() const {
  return filesize_;
}
inline void CloseEventProto::set_filesize(::google::protobuf::int64 value) {
  set_has_filesize();
  filesize_ = value;
}

// required int64 timestamp = 3;
inline bool CloseEventProto::has_timestamp() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void CloseEventProto::set_has_timestamp() {
  _has_bits_[0] |= 0x00000004u;
}
inline void CloseEventProto::clear_has_timestamp() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void CloseEventProto::clear_timestamp() {
  timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_timestamp();
}
inline ::google::protobuf::int64 CloseEventProto::timestamp() const {
  return timestamp_;
}
inline void CloseEventProto::set_timestamp(::google::protobuf::int64 value) {
  set_has_timestamp();
  timestamp_ = value;
}

// -------------------------------------------------------------------

// AppendEventProto

// required string path = 1;
inline bool AppendEventProto::has_path() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AppendEventProto::set_has_path() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AppendEventProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AppendEventProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& AppendEventProto::path() const {
  return *path_;
}
inline void AppendEventProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void AppendEventProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void AppendEventProto::set_path(const char* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AppendEventProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* AppendEventProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AppendEventProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// RenameEventProto

// required string srcPath = 1;
inline bool RenameEventProto::has_srcpath() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RenameEventProto::set_has_srcpath() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RenameEventProto::clear_has_srcpath() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RenameEventProto::clear_srcpath() {
  if (srcpath_ != &::google::protobuf::internal::kEmptyString) {
    srcpath_->clear();
  }
  clear_has_srcpath();
}
inline const ::std::string& RenameEventProto::srcpath() const {
  return *srcpath_;
}
inline void RenameEventProto::set_srcpath(const ::std::string& value) {
  set_has_srcpath();
  if (srcpath_ == &::google::protobuf::internal::kEmptyString) {
    srcpath_ = new ::std::string;
  }
  srcpath_->assign(value);
}
inline void RenameEventProto::set_srcpath(const char* value) {
  set_has_srcpath();
  if (srcpath_ == &::google::protobuf::internal::kEmptyString) {
    srcpath_ = new ::std::string;
  }
  srcpath_->assign(value);
}
inline void RenameEventProto::set_srcpath(const char* value, size_t size) {
  set_has_srcpath();
  if (srcpath_ == &::google::protobuf::internal::kEmptyString) {
    srcpath_ = new ::std::string;
  }
  srcpath_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RenameEventProto::mutable_srcpath() {
  set_has_srcpath();
  if (srcpath_ == &::google::protobuf::internal::kEmptyString) {
    srcpath_ = new ::std::string;
  }
  return srcpath_;
}
inline ::std::string* RenameEventProto::release_srcpath() {
  clear_has_srcpath();
  if (srcpath_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = srcpath_;
    srcpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RenameEventProto::set_allocated_srcpath(::std::string* srcpath) {
  if (srcpath_ != &::google::protobuf::internal::kEmptyString) {
    delete srcpath_;
  }
  if (srcpath) {
    set_has_srcpath();
    srcpath_ = srcpath;
  } else {
    clear_has_srcpath();
    srcpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string destPath = 2;
inline bool RenameEventProto::has_destpath() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RenameEventProto::set_has_destpath() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RenameEventProto::clear_has_destpath() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RenameEventProto::clear_destpath() {
  if (destpath_ != &::google::protobuf::internal::kEmptyString) {
    destpath_->clear();
  }
  clear_has_destpath();
}
inline const ::std::string& RenameEventProto::destpath() const {
  return *destpath_;
}
inline void RenameEventProto::set_destpath(const ::std::string& value) {
  set_has_destpath();
  if (destpath_ == &::google::protobuf::internal::kEmptyString) {
    destpath_ = new ::std::string;
  }
  destpath_->assign(value);
}
inline void RenameEventProto::set_destpath(const char* value) {
  set_has_destpath();
  if (destpath_ == &::google::protobuf::internal::kEmptyString) {
    destpath_ = new ::std::string;
  }
  destpath_->assign(value);
}
inline void RenameEventProto::set_destpath(const char* value, size_t size) {
  set_has_destpath();
  if (destpath_ == &::google::protobuf::internal::kEmptyString) {
    destpath_ = new ::std::string;
  }
  destpath_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RenameEventProto::mutable_destpath() {
  set_has_destpath();
  if (destpath_ == &::google::protobuf::internal::kEmptyString) {
    destpath_ = new ::std::string;
  }
  return destpath_;
}
inline ::std::string* RenameEventProto::release_destpath() {
  clear_has_destpath();
  if (destpath_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = destpath_;
    destpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RenameEventProto::set_allocated_destpath(::std::string* destpath) {
  if (destpath_ != &::google::protobuf::internal::kEmptyString) {
    delete destpath_;
  }
  if (destpath) {
    set_has_destpath();
    destpath_ = destpath;
  } else {
    clear_has_destpath();
    destpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required int64 timestamp = 3;
inline bool RenameEventProto::has_timestamp() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RenameEventProto::set_has_timestamp() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RenameEventProto::clear_has_timestamp() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RenameEventProto::clear_timestamp() {
  timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_timestamp();
}
inline ::google::protobuf::int64 RenameEventProto::timestamp() const {
  return timestamp_;
}
inline void RenameEventProto::set_timestamp(::google::protobuf::int64 value) {
  set_has_timestamp();
  timestamp_ = value;
}

// -------------------------------------------------------------------

// MetadataUpdateEventProto

// required string path = 1;
inline bool MetadataUpdateEventProto::has_path() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void MetadataUpdateEventProto::set_has_path() {
  _has_bits_[0] |= 0x00000001u;
}
inline void MetadataUpdateEventProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void MetadataUpdateEventProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& MetadataUpdateEventProto::path() const {
  return *path_;
}
inline void MetadataUpdateEventProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void MetadataUpdateEventProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void MetadataUpdateEventProto::set_path(const char* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* MetadataUpdateEventProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* MetadataUpdateEventProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void MetadataUpdateEventProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.MetadataUpdateType type = 2;
inline bool MetadataUpdateEventProto::has_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void MetadataUpdateEventProto::set_has_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void MetadataUpdateEventProto::clear_has_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void MetadataUpdateEventProto::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::hadoop::hdfs::MetadataUpdateType MetadataUpdateEventProto::type() const {
  return static_cast< ::hadoop::hdfs::MetadataUpdateType >(type_);
}
inline void MetadataUpdateEventProto::set_type(::hadoop::hdfs::MetadataUpdateType value) {
  assert(::hadoop::hdfs::MetadataUpdateType_IsValid(value));
  set_has_type();
  type_ = value;
}

// optional int64 mtime = 3;
inline bool MetadataUpdateEventProto::has_mtime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void MetadataUpdateEventProto::set_has_mtime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void MetadataUpdateEventProto::clear_has_mtime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void MetadataUpdateEventProto::clear_mtime() {
  mtime_ = GOOGLE_LONGLONG(0);
  clear_has_mtime();
}
inline ::google::protobuf::int64 MetadataUpdateEventProto::mtime() const {
  return mtime_;
}
inline void MetadataUpdateEventProto::set_mtime(::google::protobuf::int64 value) {
  set_has_mtime();
  mtime_ = value;
}

// optional int64 atime = 4;
inline bool MetadataUpdateEventProto::has_atime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void MetadataUpdateEventProto::set_has_atime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void MetadataUpdateEventProto::clear_has_atime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void MetadataUpdateEventProto::clear_atime() {
  atime_ = GOOGLE_LONGLONG(0);
  clear_has_atime();
}
inline ::google::protobuf::int64 MetadataUpdateEventProto::atime() const {
  return atime_;
}
inline void MetadataUpdateEventProto::set_atime(::google::protobuf::int64 value) {
  set_has_atime();
  atime_ = value;
}

// optional int32 replication = 5;
inline bool MetadataUpdateEventProto::has_replication() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void MetadataUpdateEventProto::set_has_replication() {
  _has_bits_[0] |= 0x00000010u;
}
inline void MetadataUpdateEventProto::clear_has_replication() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void MetadataUpdateEventProto::clear_replication() {
  replication_ = 0;
  clear_has_replication();
}
inline ::google::protobuf::int32 MetadataUpdateEventProto::replication() const {
  return replication_;
}
inline void MetadataUpdateEventProto::set_replication(::google::protobuf::int32 value) {
  set_has_replication();
  replication_ = value;
}

// optional string ownerName = 6;
inline bool MetadataUpdateEventProto::has_ownername() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void MetadataUpdateEventProto::set_has_ownername() {
  _has_bits_[0] |= 0x00000020u;
}
inline void MetadataUpdateEventProto::clear_has_ownername() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void MetadataUpdateEventProto::clear_ownername() {
  if (ownername_ != &::google::protobuf::internal::kEmptyString) {
    ownername_->clear();
  }
  clear_has_ownername();
}
inline const ::std::string& MetadataUpdateEventProto::ownername() const {
  return *ownername_;
}
inline void MetadataUpdateEventProto::set_ownername(const ::std::string& value) {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  ownername_->assign(value);
}
inline void MetadataUpdateEventProto::set_ownername(const char* value) {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  ownername_->assign(value);
}
inline void MetadataUpdateEventProto::set_ownername(const char* value, size_t size) {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  ownername_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* MetadataUpdateEventProto::mutable_ownername() {
  set_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    ownername_ = new ::std::string;
  }
  return ownername_;
}
inline ::std::string* MetadataUpdateEventProto::release_ownername() {
  clear_has_ownername();
  if (ownername_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = ownername_;
    ownername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void MetadataUpdateEventProto::set_allocated_ownername(::std::string* ownername) {
  if (ownername_ != &::google::protobuf::internal::kEmptyString) {
    delete ownername_;
  }
  if (ownername) {
    set_has_ownername();
    ownername_ = ownername;
  } else {
    clear_has_ownername();
    ownername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string groupName = 7;
inline bool MetadataUpdateEventProto::has_groupname() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void MetadataUpdateEventProto::set_has_groupname() {
  _has_bits_[0] |= 0x00000040u;
}
inline void MetadataUpdateEventProto::clear_has_groupname() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void MetadataUpdateEventProto::clear_groupname() {
  if (groupname_ != &::google::protobuf::internal::kEmptyString) {
    groupname_->clear();
  }
  clear_has_groupname();
}
inline const ::std::string& MetadataUpdateEventProto::groupname() const {
  return *groupname_;
}
inline void MetadataUpdateEventProto::set_groupname(const ::std::string& value) {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  groupname_->assign(value);
}
inline void MetadataUpdateEventProto::set_groupname(const char* value) {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  groupname_->assign(value);
}
inline void MetadataUpdateEventProto::set_groupname(const char* value, size_t size) {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  groupname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* MetadataUpdateEventProto::mutable_groupname() {
  set_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    groupname_ = new ::std::string;
  }
  return groupname_;
}
inline ::std::string* MetadataUpdateEventProto::release_groupname() {
  clear_has_groupname();
  if (groupname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = groupname_;
    groupname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void MetadataUpdateEventProto::set_allocated_groupname(::std::string* groupname) {
  if (groupname_ != &::google::protobuf::internal::kEmptyString) {
    delete groupname_;
  }
  if (groupname) {
    set_has_groupname();
    groupname_ = groupname;
  } else {
    clear_has_groupname();
    groupname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.FsPermissionProto perms = 8;
inline bool MetadataUpdateEventProto::has_perms() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void MetadataUpdateEventProto::set_has_perms() {
  _has_bits_[0] |= 0x00000080u;
}
inline void MetadataUpdateEventProto::clear_has_perms() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void MetadataUpdateEventProto::clear_perms() {
  if (perms_ != NULL) perms_->::hadoop::hdfs::FsPermissionProto::Clear();
  clear_has_perms();
}
inline const ::hadoop::hdfs::FsPermissionProto& MetadataUpdateEventProto::perms() const {
  return perms_ != NULL ? *perms_ : *default_instance_->perms_;
}
inline ::hadoop::hdfs::FsPermissionProto* MetadataUpdateEventProto::mutable_perms() {
  set_has_perms();
  if (perms_ == NULL) perms_ = new ::hadoop::hdfs::FsPermissionProto;
  return perms_;
}
inline ::hadoop::hdfs::FsPermissionProto* MetadataUpdateEventProto::release_perms() {
  clear_has_perms();
  ::hadoop::hdfs::FsPermissionProto* temp = perms_;
  perms_ = NULL;
  return temp;
}
inline void MetadataUpdateEventProto::set_allocated_perms(::hadoop::hdfs::FsPermissionProto* perms) {
  delete perms_;
  perms_ = perms;
  if (perms) {
    set_has_perms();
  } else {
    clear_has_perms();
  }
}

// repeated .hadoop.hdfs.AclEntryProto acls = 9;
inline int MetadataUpdateEventProto::acls_size() const {
  return acls_.size();
}
inline void MetadataUpdateEventProto::clear_acls() {
  acls_.Clear();
}
inline const ::hadoop::hdfs::AclEntryProto& MetadataUpdateEventProto::acls(int index) const {
  return acls_.Get(index);
}
inline ::hadoop::hdfs::AclEntryProto* MetadataUpdateEventProto::mutable_acls(int index) {
  return acls_.Mutable(index);
}
inline ::hadoop::hdfs::AclEntryProto* MetadataUpdateEventProto::add_acls() {
  return acls_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >&
MetadataUpdateEventProto::acls() const {
  return acls_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::AclEntryProto >*
MetadataUpdateEventProto::mutable_acls() {
  return &acls_;
}

// repeated .hadoop.hdfs.XAttrProto xAttrs = 10;
inline int MetadataUpdateEventProto::xattrs_size() const {
  return xattrs_.size();
}
inline void MetadataUpdateEventProto::clear_xattrs() {
  xattrs_.Clear();
}
inline const ::hadoop::hdfs::XAttrProto& MetadataUpdateEventProto::xattrs(int index) const {
  return xattrs_.Get(index);
}
inline ::hadoop::hdfs::XAttrProto* MetadataUpdateEventProto::mutable_xattrs(int index) {
  return xattrs_.Mutable(index);
}
inline ::hadoop::hdfs::XAttrProto* MetadataUpdateEventProto::add_xattrs() {
  return xattrs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
MetadataUpdateEventProto::xattrs() const {
  return xattrs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
MetadataUpdateEventProto::mutable_xattrs() {
  return &xattrs_;
}

// optional bool xAttrsRemoved = 11;
inline bool MetadataUpdateEventProto::has_xattrsremoved() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void MetadataUpdateEventProto::set_has_xattrsremoved() {
  _has_bits_[0] |= 0x00000400u;
}
inline void MetadataUpdateEventProto::clear_has_xattrsremoved() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void MetadataUpdateEventProto::clear_xattrsremoved() {
  xattrsremoved_ = false;
  clear_has_xattrsremoved();
}
inline bool MetadataUpdateEventProto::xattrsremoved() const {
  return xattrsremoved_;
}
inline void MetadataUpdateEventProto::set_xattrsremoved(bool value) {
  set_has_xattrsremoved();
  xattrsremoved_ = value;
}

// -------------------------------------------------------------------

// UnlinkEventProto

// required string path = 1;
inline bool UnlinkEventProto::has_path() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void UnlinkEventProto::set_has_path() {
  _has_bits_[0] |= 0x00000001u;
}
inline void UnlinkEventProto::clear_has_path() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void UnlinkEventProto::clear_path() {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    path_->clear();
  }
  clear_has_path();
}
inline const ::std::string& UnlinkEventProto::path() const {
  return *path_;
}
inline void UnlinkEventProto::set_path(const ::std::string& value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void UnlinkEventProto::set_path(const char* value) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(value);
}
inline void UnlinkEventProto::set_path(const char* value, size_t size) {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  path_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* UnlinkEventProto::mutable_path() {
  set_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    path_ = new ::std::string;
  }
  return path_;
}
inline ::std::string* UnlinkEventProto::release_path() {
  clear_has_path();
  if (path_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = path_;
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void UnlinkEventProto::set_allocated_path(::std::string* path) {
  if (path_ != &::google::protobuf::internal::kEmptyString) {
    delete path_;
  }
  if (path) {
    set_has_path();
    path_ = path;
  } else {
    clear_has_path();
    path_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required int64 timestamp = 2;
inline bool UnlinkEventProto::has_timestamp() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void UnlinkEventProto::set_has_timestamp() {
  _has_bits_[0] |= 0x00000002u;
}
inline void UnlinkEventProto::clear_has_timestamp() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void UnlinkEventProto::clear_timestamp() {
  timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_timestamp();
}
inline ::google::protobuf::int64 UnlinkEventProto::timestamp() const {
  return timestamp_;
}
inline void UnlinkEventProto::set_timestamp(::google::protobuf::int64 value) {
  set_has_timestamp();
  timestamp_ = value;
}

// -------------------------------------------------------------------

// EventsListProto

// repeated .hadoop.hdfs.EventProto events = 1;
inline int EventsListProto::events_size() const {
  return events_.size();
}
inline void EventsListProto::clear_events() {
  events_.Clear();
}
inline const ::hadoop::hdfs::EventProto& EventsListProto::events(int index) const {
  return events_.Get(index);
}
inline ::hadoop::hdfs::EventProto* EventsListProto::mutable_events(int index) {
  return events_.Mutable(index);
}
inline ::hadoop::hdfs::EventProto* EventsListProto::add_events() {
  return events_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EventProto >&
EventsListProto::events() const {
  return events_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::EventProto >*
EventsListProto::mutable_events() {
  return &events_;
}

// required int64 firstTxid = 2;
inline bool EventsListProto::has_firsttxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void EventsListProto::set_has_firsttxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void EventsListProto::clear_has_firsttxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void EventsListProto::clear_firsttxid() {
  firsttxid_ = GOOGLE_LONGLONG(0);
  clear_has_firsttxid();
}
inline ::google::protobuf::int64 EventsListProto::firsttxid() const {
  return firsttxid_;
}
inline void EventsListProto::set_firsttxid(::google::protobuf::int64 value) {
  set_has_firsttxid();
  firsttxid_ = value;
}

// required int64 lastTxid = 3;
inline bool EventsListProto::has_lasttxid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void EventsListProto::set_has_lasttxid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void EventsListProto::clear_has_lasttxid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void EventsListProto::clear_lasttxid() {
  lasttxid_ = GOOGLE_LONGLONG(0);
  clear_has_lasttxid();
}
inline ::google::protobuf::int64 EventsListProto::lasttxid() const {
  return lasttxid_;
}
inline void EventsListProto::set_lasttxid(::google::protobuf::int64 value) {
  set_has_lasttxid();
  lasttxid_ = value;
}

// required int64 syncTxid = 4;
inline bool EventsListProto::has_synctxid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void EventsListProto::set_has_synctxid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void EventsListProto::clear_has_synctxid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void EventsListProto::clear_synctxid() {
  synctxid_ = GOOGLE_LONGLONG(0);
  clear_has_synctxid();
}
inline ::google::protobuf::int64 EventsListProto::synctxid() const {
  return synctxid_;
}
inline void EventsListProto::set_synctxid(::google::protobuf::int64 value) {
  set_has_synctxid();
  synctxid_ = value;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::EventType>() {
  return ::hadoop::hdfs::EventType_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::INodeType>() {
  return ::hadoop::hdfs::INodeType_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::MetadataUpdateType>() {
  return ::hadoop::hdfs::MetadataUpdateType_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_inotify_2eproto__INCLUDED
