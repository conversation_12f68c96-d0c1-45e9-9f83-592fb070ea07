// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: datatransfer.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "datatransfer.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

namespace {

const ::google::protobuf::Descriptor* DataTransferEncryptorMessageProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DataTransferEncryptorMessageProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor_ = NULL;
const ::google::protobuf::Descriptor* BaseHeaderProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BaseHeaderProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DataTransferTraceInfoProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DataTransferTraceInfoProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ClientOperationHeaderProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ClientOperationHeaderProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CachingStrategyProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CachingStrategyProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpReadBlockProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpReadBlockProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ChecksumProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ChecksumProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpWriteBlockProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpWriteBlockProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* OpWriteBlockProto_BlockConstructionStage_descriptor_ = NULL;
const ::google::protobuf::Descriptor* OpTransferBlockProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpTransferBlockProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpReplaceBlockProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpReplaceBlockProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpCopyBlockProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpCopyBlockProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpBlockChecksumProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpBlockChecksumProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ShortCircuitShmIdProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ShortCircuitShmIdProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ShortCircuitShmSlotProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ShortCircuitShmSlotProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpRequestShortCircuitAccessProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpRequestShortCircuitAccessProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ReleaseShortCircuitAccessRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ReleaseShortCircuitAccessRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ReleaseShortCircuitAccessResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ReleaseShortCircuitAccessResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ShortCircuitShmRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ShortCircuitShmRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ShortCircuitShmResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ShortCircuitShmResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PacketHeaderProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PacketHeaderProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PipelineAckProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PipelineAckProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ReadOpChecksumInfoProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ReadOpChecksumInfoProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* BlockOpResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BlockOpResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ClientReadStatusProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ClientReadStatusProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DNTransferAckProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DNTransferAckProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* OpBlockChecksumResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OpBlockChecksumResponseProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* Status_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_datatransfer_2eproto() {
  protobuf_AddDesc_datatransfer_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "datatransfer.proto");
  GOOGLE_CHECK(file != NULL);
  DataTransferEncryptorMessageProto_descriptor_ = file->message_type(0);
  static const int DataTransferEncryptorMessageProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferEncryptorMessageProto, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferEncryptorMessageProto, payload_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferEncryptorMessageProto, message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferEncryptorMessageProto, cipheroption_),
  };
  DataTransferEncryptorMessageProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DataTransferEncryptorMessageProto_descriptor_,
      DataTransferEncryptorMessageProto::default_instance_,
      DataTransferEncryptorMessageProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferEncryptorMessageProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferEncryptorMessageProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DataTransferEncryptorMessageProto));
  DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor_ = DataTransferEncryptorMessageProto_descriptor_->enum_type(0);
  BaseHeaderProto_descriptor_ = file->message_type(1);
  static const int BaseHeaderProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BaseHeaderProto, block_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BaseHeaderProto, token_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BaseHeaderProto, traceinfo_),
  };
  BaseHeaderProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      BaseHeaderProto_descriptor_,
      BaseHeaderProto::default_instance_,
      BaseHeaderProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BaseHeaderProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BaseHeaderProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(BaseHeaderProto));
  DataTransferTraceInfoProto_descriptor_ = file->message_type(2);
  static const int DataTransferTraceInfoProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferTraceInfoProto, traceid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferTraceInfoProto, parentid_),
  };
  DataTransferTraceInfoProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DataTransferTraceInfoProto_descriptor_,
      DataTransferTraceInfoProto::default_instance_,
      DataTransferTraceInfoProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferTraceInfoProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DataTransferTraceInfoProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DataTransferTraceInfoProto));
  ClientOperationHeaderProto_descriptor_ = file->message_type(3);
  static const int ClientOperationHeaderProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientOperationHeaderProto, baseheader_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientOperationHeaderProto, clientname_),
  };
  ClientOperationHeaderProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ClientOperationHeaderProto_descriptor_,
      ClientOperationHeaderProto::default_instance_,
      ClientOperationHeaderProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientOperationHeaderProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientOperationHeaderProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ClientOperationHeaderProto));
  CachingStrategyProto_descriptor_ = file->message_type(4);
  static const int CachingStrategyProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CachingStrategyProto, dropbehind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CachingStrategyProto, readahead_),
  };
  CachingStrategyProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CachingStrategyProto_descriptor_,
      CachingStrategyProto::default_instance_,
      CachingStrategyProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CachingStrategyProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CachingStrategyProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CachingStrategyProto));
  OpReadBlockProto_descriptor_ = file->message_type(5);
  static const int OpReadBlockProto_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, header_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, offset_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, len_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, sendchecksums_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, cachingstrategy_),
  };
  OpReadBlockProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpReadBlockProto_descriptor_,
      OpReadBlockProto::default_instance_,
      OpReadBlockProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReadBlockProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpReadBlockProto));
  ChecksumProto_descriptor_ = file->message_type(6);
  static const int ChecksumProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ChecksumProto, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ChecksumProto, bytesperchecksum_),
  };
  ChecksumProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ChecksumProto_descriptor_,
      ChecksumProto::default_instance_,
      ChecksumProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ChecksumProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ChecksumProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ChecksumProto));
  OpWriteBlockProto_descriptor_ = file->message_type(7);
  static const int OpWriteBlockProto_offsets_[13] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, header_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, targets_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, source_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, stage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, pipelinesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, minbytesrcvd_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, maxbytesrcvd_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, latestgenerationstamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, requestedchecksum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, cachingstrategy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, storagetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, targetstoragetypes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, allowlazypersist_),
  };
  OpWriteBlockProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpWriteBlockProto_descriptor_,
      OpWriteBlockProto::default_instance_,
      OpWriteBlockProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpWriteBlockProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpWriteBlockProto));
  OpWriteBlockProto_BlockConstructionStage_descriptor_ = OpWriteBlockProto_descriptor_->enum_type(0);
  OpTransferBlockProto_descriptor_ = file->message_type(8);
  static const int OpTransferBlockProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpTransferBlockProto, header_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpTransferBlockProto, targets_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpTransferBlockProto, targetstoragetypes_),
  };
  OpTransferBlockProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpTransferBlockProto_descriptor_,
      OpTransferBlockProto::default_instance_,
      OpTransferBlockProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpTransferBlockProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpTransferBlockProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpTransferBlockProto));
  OpReplaceBlockProto_descriptor_ = file->message_type(9);
  static const int OpReplaceBlockProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReplaceBlockProto, header_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReplaceBlockProto, delhint_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReplaceBlockProto, source_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReplaceBlockProto, storagetype_),
  };
  OpReplaceBlockProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpReplaceBlockProto_descriptor_,
      OpReplaceBlockProto::default_instance_,
      OpReplaceBlockProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReplaceBlockProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpReplaceBlockProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpReplaceBlockProto));
  OpCopyBlockProto_descriptor_ = file->message_type(10);
  static const int OpCopyBlockProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpCopyBlockProto, header_),
  };
  OpCopyBlockProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpCopyBlockProto_descriptor_,
      OpCopyBlockProto::default_instance_,
      OpCopyBlockProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpCopyBlockProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpCopyBlockProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpCopyBlockProto));
  OpBlockChecksumProto_descriptor_ = file->message_type(11);
  static const int OpBlockChecksumProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumProto, header_),
  };
  OpBlockChecksumProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpBlockChecksumProto_descriptor_,
      OpBlockChecksumProto::default_instance_,
      OpBlockChecksumProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpBlockChecksumProto));
  ShortCircuitShmIdProto_descriptor_ = file->message_type(12);
  static const int ShortCircuitShmIdProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmIdProto, hi_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmIdProto, lo_),
  };
  ShortCircuitShmIdProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ShortCircuitShmIdProto_descriptor_,
      ShortCircuitShmIdProto::default_instance_,
      ShortCircuitShmIdProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmIdProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmIdProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ShortCircuitShmIdProto));
  ShortCircuitShmSlotProto_descriptor_ = file->message_type(13);
  static const int ShortCircuitShmSlotProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmSlotProto, shmid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmSlotProto, slotidx_),
  };
  ShortCircuitShmSlotProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ShortCircuitShmSlotProto_descriptor_,
      ShortCircuitShmSlotProto::default_instance_,
      ShortCircuitShmSlotProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmSlotProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmSlotProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ShortCircuitShmSlotProto));
  OpRequestShortCircuitAccessProto_descriptor_ = file->message_type(14);
  static const int OpRequestShortCircuitAccessProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpRequestShortCircuitAccessProto, header_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpRequestShortCircuitAccessProto, maxversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpRequestShortCircuitAccessProto, slotid_),
  };
  OpRequestShortCircuitAccessProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpRequestShortCircuitAccessProto_descriptor_,
      OpRequestShortCircuitAccessProto::default_instance_,
      OpRequestShortCircuitAccessProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpRequestShortCircuitAccessProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpRequestShortCircuitAccessProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpRequestShortCircuitAccessProto));
  ReleaseShortCircuitAccessRequestProto_descriptor_ = file->message_type(15);
  static const int ReleaseShortCircuitAccessRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessRequestProto, slotid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessRequestProto, traceinfo_),
  };
  ReleaseShortCircuitAccessRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ReleaseShortCircuitAccessRequestProto_descriptor_,
      ReleaseShortCircuitAccessRequestProto::default_instance_,
      ReleaseShortCircuitAccessRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ReleaseShortCircuitAccessRequestProto));
  ReleaseShortCircuitAccessResponseProto_descriptor_ = file->message_type(16);
  static const int ReleaseShortCircuitAccessResponseProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessResponseProto, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessResponseProto, error_),
  };
  ReleaseShortCircuitAccessResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ReleaseShortCircuitAccessResponseProto_descriptor_,
      ReleaseShortCircuitAccessResponseProto::default_instance_,
      ReleaseShortCircuitAccessResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReleaseShortCircuitAccessResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ReleaseShortCircuitAccessResponseProto));
  ShortCircuitShmRequestProto_descriptor_ = file->message_type(17);
  static const int ShortCircuitShmRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmRequestProto, clientname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmRequestProto, traceinfo_),
  };
  ShortCircuitShmRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ShortCircuitShmRequestProto_descriptor_,
      ShortCircuitShmRequestProto::default_instance_,
      ShortCircuitShmRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ShortCircuitShmRequestProto));
  ShortCircuitShmResponseProto_descriptor_ = file->message_type(18);
  static const int ShortCircuitShmResponseProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmResponseProto, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmResponseProto, error_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmResponseProto, id_),
  };
  ShortCircuitShmResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ShortCircuitShmResponseProto_descriptor_,
      ShortCircuitShmResponseProto::default_instance_,
      ShortCircuitShmResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShortCircuitShmResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ShortCircuitShmResponseProto));
  PacketHeaderProto_descriptor_ = file->message_type(19);
  static const int PacketHeaderProto_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, offsetinblock_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, seqno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, lastpacketinblock_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, datalen_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, syncblock_),
  };
  PacketHeaderProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PacketHeaderProto_descriptor_,
      PacketHeaderProto::default_instance_,
      PacketHeaderProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PacketHeaderProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PacketHeaderProto));
  PipelineAckProto_descriptor_ = file->message_type(20);
  static const int PipelineAckProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PipelineAckProto, seqno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PipelineAckProto, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PipelineAckProto, downstreamacktimenanos_),
  };
  PipelineAckProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PipelineAckProto_descriptor_,
      PipelineAckProto::default_instance_,
      PipelineAckProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PipelineAckProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PipelineAckProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PipelineAckProto));
  ReadOpChecksumInfoProto_descriptor_ = file->message_type(21);
  static const int ReadOpChecksumInfoProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReadOpChecksumInfoProto, checksum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReadOpChecksumInfoProto, chunkoffset_),
  };
  ReadOpChecksumInfoProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ReadOpChecksumInfoProto_descriptor_,
      ReadOpChecksumInfoProto::default_instance_,
      ReadOpChecksumInfoProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReadOpChecksumInfoProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ReadOpChecksumInfoProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ReadOpChecksumInfoProto));
  BlockOpResponseProto_descriptor_ = file->message_type(22);
  static const int BlockOpResponseProto_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, firstbadlink_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, checksumresponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, readopchecksuminfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, shortcircuitaccessversion_),
  };
  BlockOpResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      BlockOpResponseProto_descriptor_,
      BlockOpResponseProto::default_instance_,
      BlockOpResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BlockOpResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(BlockOpResponseProto));
  ClientReadStatusProto_descriptor_ = file->message_type(23);
  static const int ClientReadStatusProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientReadStatusProto, status_),
  };
  ClientReadStatusProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ClientReadStatusProto_descriptor_,
      ClientReadStatusProto::default_instance_,
      ClientReadStatusProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientReadStatusProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClientReadStatusProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ClientReadStatusProto));
  DNTransferAckProto_descriptor_ = file->message_type(24);
  static const int DNTransferAckProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DNTransferAckProto, status_),
  };
  DNTransferAckProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DNTransferAckProto_descriptor_,
      DNTransferAckProto::default_instance_,
      DNTransferAckProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DNTransferAckProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DNTransferAckProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DNTransferAckProto));
  OpBlockChecksumResponseProto_descriptor_ = file->message_type(25);
  static const int OpBlockChecksumResponseProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumResponseProto, bytespercrc_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumResponseProto, crcperblock_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumResponseProto, md5_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumResponseProto, crctype_),
  };
  OpBlockChecksumResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      OpBlockChecksumResponseProto_descriptor_,
      OpBlockChecksumResponseProto::default_instance_,
      OpBlockChecksumResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OpBlockChecksumResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(OpBlockChecksumResponseProto));
  Status_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_datatransfer_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DataTransferEncryptorMessageProto_descriptor_, &DataTransferEncryptorMessageProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    BaseHeaderProto_descriptor_, &BaseHeaderProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DataTransferTraceInfoProto_descriptor_, &DataTransferTraceInfoProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ClientOperationHeaderProto_descriptor_, &ClientOperationHeaderProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CachingStrategyProto_descriptor_, &CachingStrategyProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpReadBlockProto_descriptor_, &OpReadBlockProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ChecksumProto_descriptor_, &ChecksumProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpWriteBlockProto_descriptor_, &OpWriteBlockProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpTransferBlockProto_descriptor_, &OpTransferBlockProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpReplaceBlockProto_descriptor_, &OpReplaceBlockProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpCopyBlockProto_descriptor_, &OpCopyBlockProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpBlockChecksumProto_descriptor_, &OpBlockChecksumProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ShortCircuitShmIdProto_descriptor_, &ShortCircuitShmIdProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ShortCircuitShmSlotProto_descriptor_, &ShortCircuitShmSlotProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpRequestShortCircuitAccessProto_descriptor_, &OpRequestShortCircuitAccessProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ReleaseShortCircuitAccessRequestProto_descriptor_, &ReleaseShortCircuitAccessRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ReleaseShortCircuitAccessResponseProto_descriptor_, &ReleaseShortCircuitAccessResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ShortCircuitShmRequestProto_descriptor_, &ShortCircuitShmRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ShortCircuitShmResponseProto_descriptor_, &ShortCircuitShmResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PacketHeaderProto_descriptor_, &PacketHeaderProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PipelineAckProto_descriptor_, &PipelineAckProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ReadOpChecksumInfoProto_descriptor_, &ReadOpChecksumInfoProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    BlockOpResponseProto_descriptor_, &BlockOpResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ClientReadStatusProto_descriptor_, &ClientReadStatusProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DNTransferAckProto_descriptor_, &DNTransferAckProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    OpBlockChecksumResponseProto_descriptor_, &OpBlockChecksumResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_datatransfer_2eproto() {
  delete DataTransferEncryptorMessageProto::default_instance_;
  delete DataTransferEncryptorMessageProto_reflection_;
  delete BaseHeaderProto::default_instance_;
  delete BaseHeaderProto_reflection_;
  delete DataTransferTraceInfoProto::default_instance_;
  delete DataTransferTraceInfoProto_reflection_;
  delete ClientOperationHeaderProto::default_instance_;
  delete ClientOperationHeaderProto_reflection_;
  delete CachingStrategyProto::default_instance_;
  delete CachingStrategyProto_reflection_;
  delete OpReadBlockProto::default_instance_;
  delete OpReadBlockProto_reflection_;
  delete ChecksumProto::default_instance_;
  delete ChecksumProto_reflection_;
  delete OpWriteBlockProto::default_instance_;
  delete OpWriteBlockProto_reflection_;
  delete OpTransferBlockProto::default_instance_;
  delete OpTransferBlockProto_reflection_;
  delete OpReplaceBlockProto::default_instance_;
  delete OpReplaceBlockProto_reflection_;
  delete OpCopyBlockProto::default_instance_;
  delete OpCopyBlockProto_reflection_;
  delete OpBlockChecksumProto::default_instance_;
  delete OpBlockChecksumProto_reflection_;
  delete ShortCircuitShmIdProto::default_instance_;
  delete ShortCircuitShmIdProto_reflection_;
  delete ShortCircuitShmSlotProto::default_instance_;
  delete ShortCircuitShmSlotProto_reflection_;
  delete OpRequestShortCircuitAccessProto::default_instance_;
  delete OpRequestShortCircuitAccessProto_reflection_;
  delete ReleaseShortCircuitAccessRequestProto::default_instance_;
  delete ReleaseShortCircuitAccessRequestProto_reflection_;
  delete ReleaseShortCircuitAccessResponseProto::default_instance_;
  delete ReleaseShortCircuitAccessResponseProto_reflection_;
  delete ShortCircuitShmRequestProto::default_instance_;
  delete ShortCircuitShmRequestProto_reflection_;
  delete ShortCircuitShmResponseProto::default_instance_;
  delete ShortCircuitShmResponseProto_reflection_;
  delete PacketHeaderProto::default_instance_;
  delete PacketHeaderProto_reflection_;
  delete PipelineAckProto::default_instance_;
  delete PipelineAckProto_reflection_;
  delete ReadOpChecksumInfoProto::default_instance_;
  delete ReadOpChecksumInfoProto_reflection_;
  delete BlockOpResponseProto::default_instance_;
  delete BlockOpResponseProto_reflection_;
  delete ClientReadStatusProto::default_instance_;
  delete ClientReadStatusProto_reflection_;
  delete DNTransferAckProto::default_instance_;
  delete DNTransferAckProto_reflection_;
  delete OpBlockChecksumResponseProto::default_instance_;
  delete OpBlockChecksumResponseProto_reflection_;
}

void protobuf_AddDesc_datatransfer_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::common::protobuf_AddDesc_Security_2eproto();
  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\022datatransfer.proto\022\013hadoop.hdfs\032\016Secur"
    "ity.proto\032\nhdfs.proto\"\245\002\n!DataTransferEn"
    "cryptorMessageProto\022Z\n\006status\030\001 \002(\0162J.ha"
    "doop.hdfs.DataTransferEncryptorMessagePr"
    "oto.DataTransferEncryptorStatus\022\017\n\007paylo"
    "ad\030\002 \001(\014\022\017\n\007message\030\003 \001(\t\0224\n\014cipherOptio"
    "n\030\004 \003(\0132\036.hadoop.hdfs.CipherOptionProto\""
    "L\n\033DataTransferEncryptorStatus\022\013\n\007SUCCES"
    "S\020\000\022\025\n\021ERROR_UNKNOWN_KEY\020\001\022\t\n\005ERROR\020\002\"\247\001"
    "\n\017BaseHeaderProto\022.\n\005block\030\001 \002(\0132\037.hadoo"
    "p.hdfs.ExtendedBlockProto\022(\n\005token\030\002 \001(\013"
    "2\031.hadoop.common.TokenProto\022:\n\ttraceInfo"
    "\030\003 \001(\0132\'.hadoop.hdfs.DataTransferTraceIn"
    "foProto\"\?\n\032DataTransferTraceInfoProto\022\017\n"
    "\007traceId\030\001 \002(\004\022\020\n\010parentId\030\002 \002(\004\"b\n\032Clie"
    "ntOperationHeaderProto\0220\n\nbaseHeader\030\001 \002"
    "(\0132\034.hadoop.hdfs.BaseHeaderProto\022\022\n\nclie"
    "ntName\030\002 \002(\t\"=\n\024CachingStrategyProto\022\022\n\n"
    "dropBehind\030\001 \001(\010\022\021\n\treadahead\030\002 \001(\003\"\301\001\n\020"
    "OpReadBlockProto\0227\n\006header\030\001 \002(\0132\'.hadoo"
    "p.hdfs.ClientOperationHeaderProto\022\016\n\006off"
    "set\030\002 \002(\004\022\013\n\003len\030\003 \002(\004\022\033\n\rsendChecksums\030"
    "\004 \001(\010:\004true\022:\n\017cachingStrategy\030\005 \001(\0132!.h"
    "adoop.hdfs.CachingStrategyProto\"W\n\rCheck"
    "sumProto\022,\n\004type\030\001 \002(\0162\036.hadoop.hdfs.Che"
    "cksumTypeProto\022\030\n\020bytesPerChecksum\030\002 \002(\r"
    "\"\350\006\n\021OpWriteBlockProto\0227\n\006header\030\001 \002(\0132\'"
    ".hadoop.hdfs.ClientOperationHeaderProto\022"
    "/\n\007targets\030\002 \003(\0132\036.hadoop.hdfs.DatanodeI"
    "nfoProto\022.\n\006source\030\003 \001(\0132\036.hadoop.hdfs.D"
    "atanodeInfoProto\022D\n\005stage\030\004 \002(\01625.hadoop"
    ".hdfs.OpWriteBlockProto.BlockConstructio"
    "nStage\022\024\n\014pipelineSize\030\005 \002(\r\022\024\n\014minBytes"
    "Rcvd\030\006 \002(\004\022\024\n\014maxBytesRcvd\030\007 \002(\004\022\035\n\025late"
    "stGenerationStamp\030\010 \002(\004\0225\n\021requestedChec"
    "ksum\030\t \002(\0132\032.hadoop.hdfs.ChecksumProto\022:"
    "\n\017cachingStrategy\030\n \001(\0132!.hadoop.hdfs.Ca"
    "chingStrategyProto\0228\n\013storageType\030\013 \001(\0162"
    "\035.hadoop.hdfs.StorageTypeProto:\004DISK\0229\n\022"
    "targetStorageTypes\030\014 \003(\0162\035.hadoop.hdfs.S"
    "torageTypeProto\022\037\n\020allowLazyPersist\030\r \001("
    "\010:\005false\"\210\002\n\026BlockConstructionStage\022\031\n\025P"
    "IPELINE_SETUP_APPEND\020\000\022\"\n\036PIPELINE_SETUP"
    "_APPEND_RECOVERY\020\001\022\022\n\016DATA_STREAMING\020\002\022%"
    "\n!PIPELINE_SETUP_STREAMING_RECOVERY\020\003\022\022\n"
    "\016PIPELINE_CLOSE\020\004\022\033\n\027PIPELINE_CLOSE_RECO"
    "VERY\020\005\022\031\n\025PIPELINE_SETUP_CREATE\020\006\022\020\n\014TRA"
    "NSFER_RBW\020\007\022\026\n\022TRANSFER_FINALIZED\020\010\"\273\001\n\024"
    "OpTransferBlockProto\0227\n\006header\030\001 \002(\0132\'.h"
    "adoop.hdfs.ClientOperationHeaderProto\022/\n"
    "\007targets\030\002 \003(\0132\036.hadoop.hdfs.DatanodeInf"
    "oProto\0229\n\022targetStorageTypes\030\003 \003(\0162\035.had"
    "oop.hdfs.StorageTypeProto\"\276\001\n\023OpReplaceB"
    "lockProto\022,\n\006header\030\001 \002(\0132\034.hadoop.hdfs."
    "BaseHeaderProto\022\017\n\007delHint\030\002 \002(\t\022.\n\006sour"
    "ce\030\003 \002(\0132\036.hadoop.hdfs.DatanodeInfoProto"
    "\0228\n\013storageType\030\004 \001(\0162\035.hadoop.hdfs.Stor"
    "ageTypeProto:\004DISK\"@\n\020OpCopyBlockProto\022,"
    "\n\006header\030\001 \002(\0132\034.hadoop.hdfs.BaseHeaderP"
    "roto\"D\n\024OpBlockChecksumProto\022,\n\006header\030\001"
    " \002(\0132\034.hadoop.hdfs.BaseHeaderProto\"0\n\026Sh"
    "ortCircuitShmIdProto\022\n\n\002hi\030\001 \002(\003\022\n\n\002lo\030\002"
    " \002(\003\"_\n\030ShortCircuitShmSlotProto\0222\n\005shmI"
    "d\030\001 \002(\0132#.hadoop.hdfs.ShortCircuitShmIdP"
    "roto\022\017\n\007slotIdx\030\002 \002(\005\"\233\001\n OpRequestShort"
    "CircuitAccessProto\022,\n\006header\030\001 \002(\0132\034.had"
    "oop.hdfs.BaseHeaderProto\022\022\n\nmaxVersion\030\002"
    " \002(\r\0225\n\006slotId\030\003 \001(\0132%.hadoop.hdfs.Short"
    "CircuitShmSlotProto\"\232\001\n%ReleaseShortCirc"
    "uitAccessRequestProto\0225\n\006slotId\030\001 \002(\0132%."
    "hadoop.hdfs.ShortCircuitShmSlotProto\022:\n\t"
    "traceInfo\030\002 \001(\0132\'.hadoop.hdfs.DataTransf"
    "erTraceInfoProto\"\\\n&ReleaseShortCircuitA"
    "ccessResponseProto\022#\n\006status\030\001 \002(\0162\023.had"
    "oop.hdfs.Status\022\r\n\005error\030\002 \001(\t\"m\n\033ShortC"
    "ircuitShmRequestProto\022\022\n\nclientName\030\001 \002("
    "\t\022:\n\ttraceInfo\030\002 \001(\0132\'.hadoop.hdfs.DataT"
    "ransferTraceInfoProto\"\203\001\n\034ShortCircuitSh"
    "mResponseProto\022#\n\006status\030\001 \002(\0162\023.hadoop."
    "hdfs.Status\022\r\n\005error\030\002 \001(\t\022/\n\002id\030\003 \001(\0132#"
    ".hadoop.hdfs.ShortCircuitShmIdProto\"\177\n\021P"
    "acketHeaderProto\022\025\n\roffsetInBlock\030\001 \002(\020\022"
    "\r\n\005seqno\030\002 \002(\020\022\031\n\021lastPacketInBlock\030\003 \002("
    "\010\022\017\n\007dataLen\030\004 \002(\017\022\030\n\tsyncBlock\030\005 \001(\010:\005f"
    "alse\"i\n\020PipelineAckProto\022\r\n\005seqno\030\001 \002(\022\022"
    "#\n\006status\030\002 \003(\0162\023.hadoop.hdfs.Status\022!\n\026"
    "downstreamAckTimeNanos\030\003 \001(\004:\0010\"\\\n\027ReadO"
    "pChecksumInfoProto\022,\n\010checksum\030\001 \002(\0132\032.h"
    "adoop.hdfs.ChecksumProto\022\023\n\013chunkOffset\030"
    "\002 \002(\004\"\214\002\n\024BlockOpResponseProto\022#\n\006status"
    "\030\001 \002(\0162\023.hadoop.hdfs.Status\022\024\n\014firstBadL"
    "ink\030\002 \001(\t\022C\n\020checksumResponse\030\003 \001(\0132).ha"
    "doop.hdfs.OpBlockChecksumResponseProto\022@"
    "\n\022readOpChecksumInfo\030\004 \001(\0132$.hadoop.hdfs"
    ".ReadOpChecksumInfoProto\022\017\n\007message\030\005 \001("
    "\t\022!\n\031shortCircuitAccessVersion\030\006 \001(\r\"<\n\025"
    "ClientReadStatusProto\022#\n\006status\030\001 \002(\0162\023."
    "hadoop.hdfs.Status\"9\n\022DNTransferAckProto"
    "\022#\n\006status\030\001 \002(\0162\023.hadoop.hdfs.Status\"\206\001"
    "\n\034OpBlockChecksumResponseProto\022\023\n\013bytesP"
    "erCrc\030\001 \002(\r\022\023\n\013crcPerBlock\030\002 \002(\004\022\013\n\003md5\030"
    "\003 \002(\014\022/\n\007crcType\030\004 \001(\0162\036.hadoop.hdfs.Che"
    "cksumTypeProto*\364\001\n\006Status\022\013\n\007SUCCESS\020\000\022\t"
    "\n\005ERROR\020\001\022\022\n\016ERROR_CHECKSUM\020\002\022\021\n\rERROR_I"
    "NVALID\020\003\022\020\n\014ERROR_EXISTS\020\004\022\026\n\022ERROR_ACCE"
    "SS_TOKEN\020\005\022\017\n\013CHECKSUM_OK\020\006\022\025\n\021ERROR_UNS"
    "UPPORTED\020\007\022\017\n\013OOB_RESTART\020\010\022\021\n\rOOB_RESER"
    "VED1\020\t\022\021\n\rOOB_RESERVED2\020\n\022\021\n\rOOB_RESERVE"
    "D3\020\013\022\017\n\013IN_PROGRESS\020\014B>\n%org.apache.hado"
    "op.hdfs.protocol.protoB\022DataTransferProt"
    "os\240\001\001", 4405);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "datatransfer.proto", &protobuf_RegisterTypes);
  DataTransferEncryptorMessageProto::default_instance_ = new DataTransferEncryptorMessageProto();
  BaseHeaderProto::default_instance_ = new BaseHeaderProto();
  DataTransferTraceInfoProto::default_instance_ = new DataTransferTraceInfoProto();
  ClientOperationHeaderProto::default_instance_ = new ClientOperationHeaderProto();
  CachingStrategyProto::default_instance_ = new CachingStrategyProto();
  OpReadBlockProto::default_instance_ = new OpReadBlockProto();
  ChecksumProto::default_instance_ = new ChecksumProto();
  OpWriteBlockProto::default_instance_ = new OpWriteBlockProto();
  OpTransferBlockProto::default_instance_ = new OpTransferBlockProto();
  OpReplaceBlockProto::default_instance_ = new OpReplaceBlockProto();
  OpCopyBlockProto::default_instance_ = new OpCopyBlockProto();
  OpBlockChecksumProto::default_instance_ = new OpBlockChecksumProto();
  ShortCircuitShmIdProto::default_instance_ = new ShortCircuitShmIdProto();
  ShortCircuitShmSlotProto::default_instance_ = new ShortCircuitShmSlotProto();
  OpRequestShortCircuitAccessProto::default_instance_ = new OpRequestShortCircuitAccessProto();
  ReleaseShortCircuitAccessRequestProto::default_instance_ = new ReleaseShortCircuitAccessRequestProto();
  ReleaseShortCircuitAccessResponseProto::default_instance_ = new ReleaseShortCircuitAccessResponseProto();
  ShortCircuitShmRequestProto::default_instance_ = new ShortCircuitShmRequestProto();
  ShortCircuitShmResponseProto::default_instance_ = new ShortCircuitShmResponseProto();
  PacketHeaderProto::default_instance_ = new PacketHeaderProto();
  PipelineAckProto::default_instance_ = new PipelineAckProto();
  ReadOpChecksumInfoProto::default_instance_ = new ReadOpChecksumInfoProto();
  BlockOpResponseProto::default_instance_ = new BlockOpResponseProto();
  ClientReadStatusProto::default_instance_ = new ClientReadStatusProto();
  DNTransferAckProto::default_instance_ = new DNTransferAckProto();
  OpBlockChecksumResponseProto::default_instance_ = new OpBlockChecksumResponseProto();
  DataTransferEncryptorMessageProto::default_instance_->InitAsDefaultInstance();
  BaseHeaderProto::default_instance_->InitAsDefaultInstance();
  DataTransferTraceInfoProto::default_instance_->InitAsDefaultInstance();
  ClientOperationHeaderProto::default_instance_->InitAsDefaultInstance();
  CachingStrategyProto::default_instance_->InitAsDefaultInstance();
  OpReadBlockProto::default_instance_->InitAsDefaultInstance();
  ChecksumProto::default_instance_->InitAsDefaultInstance();
  OpWriteBlockProto::default_instance_->InitAsDefaultInstance();
  OpTransferBlockProto::default_instance_->InitAsDefaultInstance();
  OpReplaceBlockProto::default_instance_->InitAsDefaultInstance();
  OpCopyBlockProto::default_instance_->InitAsDefaultInstance();
  OpBlockChecksumProto::default_instance_->InitAsDefaultInstance();
  ShortCircuitShmIdProto::default_instance_->InitAsDefaultInstance();
  ShortCircuitShmSlotProto::default_instance_->InitAsDefaultInstance();
  OpRequestShortCircuitAccessProto::default_instance_->InitAsDefaultInstance();
  ReleaseShortCircuitAccessRequestProto::default_instance_->InitAsDefaultInstance();
  ReleaseShortCircuitAccessResponseProto::default_instance_->InitAsDefaultInstance();
  ShortCircuitShmRequestProto::default_instance_->InitAsDefaultInstance();
  ShortCircuitShmResponseProto::default_instance_->InitAsDefaultInstance();
  PacketHeaderProto::default_instance_->InitAsDefaultInstance();
  PipelineAckProto::default_instance_->InitAsDefaultInstance();
  ReadOpChecksumInfoProto::default_instance_->InitAsDefaultInstance();
  BlockOpResponseProto::default_instance_->InitAsDefaultInstance();
  ClientReadStatusProto::default_instance_->InitAsDefaultInstance();
  DNTransferAckProto::default_instance_->InitAsDefaultInstance();
  OpBlockChecksumResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_datatransfer_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_datatransfer_2eproto {
  StaticDescriptorInitializer_datatransfer_2eproto() {
    protobuf_AddDesc_datatransfer_2eproto();
  }
} static_descriptor_initializer_datatransfer_2eproto_;
const ::google::protobuf::EnumDescriptor* Status_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Status_descriptor_;
}
bool Status_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
      return true;
    default:
      return false;
  }
}


// ===================================================================

const ::google::protobuf::EnumDescriptor* DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor_;
}
bool DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto::SUCCESS;
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto::ERROR_UNKNOWN_KEY;
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto::ERROR;
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto::DataTransferEncryptorStatus_MIN;
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto::DataTransferEncryptorStatus_MAX;
const int DataTransferEncryptorMessageProto::DataTransferEncryptorStatus_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int DataTransferEncryptorMessageProto::kStatusFieldNumber;
const int DataTransferEncryptorMessageProto::kPayloadFieldNumber;
const int DataTransferEncryptorMessageProto::kMessageFieldNumber;
const int DataTransferEncryptorMessageProto::kCipherOptionFieldNumber;
#endif  // !_MSC_VER

DataTransferEncryptorMessageProto::DataTransferEncryptorMessageProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DataTransferEncryptorMessageProto::InitAsDefaultInstance() {
}

DataTransferEncryptorMessageProto::DataTransferEncryptorMessageProto(const DataTransferEncryptorMessageProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DataTransferEncryptorMessageProto::SharedCtor() {
  _cached_size_ = 0;
  status_ = 0;
  payload_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  message_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DataTransferEncryptorMessageProto::~DataTransferEncryptorMessageProto() {
  SharedDtor();
}

void DataTransferEncryptorMessageProto::SharedDtor() {
  if (payload_ != &::google::protobuf::internal::kEmptyString) {
    delete payload_;
  }
  if (message_ != &::google::protobuf::internal::kEmptyString) {
    delete message_;
  }
  if (this != default_instance_) {
  }
}

void DataTransferEncryptorMessageProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DataTransferEncryptorMessageProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DataTransferEncryptorMessageProto_descriptor_;
}

const DataTransferEncryptorMessageProto& DataTransferEncryptorMessageProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

DataTransferEncryptorMessageProto* DataTransferEncryptorMessageProto::default_instance_ = NULL;

DataTransferEncryptorMessageProto* DataTransferEncryptorMessageProto::New() const {
  return new DataTransferEncryptorMessageProto;
}

void DataTransferEncryptorMessageProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    status_ = 0;
    if (has_payload()) {
      if (payload_ != &::google::protobuf::internal::kEmptyString) {
        payload_->clear();
      }
    }
    if (has_message()) {
      if (message_ != &::google::protobuf::internal::kEmptyString) {
        message_->clear();
      }
    }
  }
  cipheroption_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DataTransferEncryptorMessageProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.DataTransferEncryptorMessageProto.DataTransferEncryptorStatus status = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_IsValid(value)) {
            set_status(static_cast< ::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_payload;
        break;
      }

      // optional bytes payload = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_payload:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_payload()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_message;
        break;
      }

      // optional string message = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_message()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->message().data(), this->message().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_cipherOption;
        break;
      }

      // repeated .hadoop.hdfs.CipherOptionProto cipherOption = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_cipherOption:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_cipheroption()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_cipherOption;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DataTransferEncryptorMessageProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.DataTransferEncryptorMessageProto.DataTransferEncryptorStatus status = 1;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  // optional bytes payload = 2;
  if (has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->payload(), output);
  }

  // optional string message = 3;
  if (has_message()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->message().data(), this->message().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->message(), output);
  }

  // repeated .hadoop.hdfs.CipherOptionProto cipherOption = 4;
  for (int i = 0; i < this->cipheroption_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->cipheroption(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DataTransferEncryptorMessageProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.DataTransferEncryptorMessageProto.DataTransferEncryptorStatus status = 1;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  // optional bytes payload = 2;
  if (has_payload()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->payload(), target);
  }

  // optional string message = 3;
  if (has_message()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->message().data(), this->message().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->message(), target);
  }

  // repeated .hadoop.hdfs.CipherOptionProto cipherOption = 4;
  for (int i = 0; i < this->cipheroption_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->cipheroption(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DataTransferEncryptorMessageProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.DataTransferEncryptorMessageProto.DataTransferEncryptorStatus status = 1;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

    // optional bytes payload = 2;
    if (has_payload()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->payload());
    }

    // optional string message = 3;
    if (has_message()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->message());
    }

  }
  // repeated .hadoop.hdfs.CipherOptionProto cipherOption = 4;
  total_size += 1 * this->cipheroption_size();
  for (int i = 0; i < this->cipheroption_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->cipheroption(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DataTransferEncryptorMessageProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DataTransferEncryptorMessageProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DataTransferEncryptorMessageProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DataTransferEncryptorMessageProto::MergeFrom(const DataTransferEncryptorMessageProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  cipheroption_.MergeFrom(from.cipheroption_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_status()) {
      set_status(from.status());
    }
    if (from.has_payload()) {
      set_payload(from.payload());
    }
    if (from.has_message()) {
      set_message(from.message());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DataTransferEncryptorMessageProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DataTransferEncryptorMessageProto::CopyFrom(const DataTransferEncryptorMessageProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataTransferEncryptorMessageProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < cipheroption_size(); i++) {
    if (!this->cipheroption(i).IsInitialized()) return false;
  }
  return true;
}

void DataTransferEncryptorMessageProto::Swap(DataTransferEncryptorMessageProto* other) {
  if (other != this) {
    std::swap(status_, other->status_);
    std::swap(payload_, other->payload_);
    std::swap(message_, other->message_);
    cipheroption_.Swap(&other->cipheroption_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DataTransferEncryptorMessageProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DataTransferEncryptorMessageProto_descriptor_;
  metadata.reflection = DataTransferEncryptorMessageProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int BaseHeaderProto::kBlockFieldNumber;
const int BaseHeaderProto::kTokenFieldNumber;
const int BaseHeaderProto::kTraceInfoFieldNumber;
#endif  // !_MSC_VER

BaseHeaderProto::BaseHeaderProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void BaseHeaderProto::InitAsDefaultInstance() {
  block_ = const_cast< ::hadoop::hdfs::ExtendedBlockProto*>(&::hadoop::hdfs::ExtendedBlockProto::default_instance());
  token_ = const_cast< ::hadoop::common::TokenProto*>(&::hadoop::common::TokenProto::default_instance());
  traceinfo_ = const_cast< ::hadoop::hdfs::DataTransferTraceInfoProto*>(&::hadoop::hdfs::DataTransferTraceInfoProto::default_instance());
}

BaseHeaderProto::BaseHeaderProto(const BaseHeaderProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void BaseHeaderProto::SharedCtor() {
  _cached_size_ = 0;
  block_ = NULL;
  token_ = NULL;
  traceinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

BaseHeaderProto::~BaseHeaderProto() {
  SharedDtor();
}

void BaseHeaderProto::SharedDtor() {
  if (this != default_instance_) {
    delete block_;
    delete token_;
    delete traceinfo_;
  }
}

void BaseHeaderProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BaseHeaderProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BaseHeaderProto_descriptor_;
}

const BaseHeaderProto& BaseHeaderProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

BaseHeaderProto* BaseHeaderProto::default_instance_ = NULL;

BaseHeaderProto* BaseHeaderProto::New() const {
  return new BaseHeaderProto;
}

void BaseHeaderProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_block()) {
      if (block_ != NULL) block_->::hadoop::hdfs::ExtendedBlockProto::Clear();
    }
    if (has_token()) {
      if (token_ != NULL) token_->::hadoop::common::TokenProto::Clear();
    }
    if (has_traceinfo()) {
      if (traceinfo_ != NULL) traceinfo_->::hadoop::hdfs::DataTransferTraceInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool BaseHeaderProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ExtendedBlockProto block = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_block()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_token;
        break;
      }

      // optional .hadoop.common.TokenProto token = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_token:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_token()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_traceInfo;
        break;
      }

      // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_traceInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_traceinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void BaseHeaderProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ExtendedBlockProto block = 1;
  if (has_block()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->block(), output);
  }

  // optional .hadoop.common.TokenProto token = 2;
  if (has_token()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->token(), output);
  }

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 3;
  if (has_traceinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->traceinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* BaseHeaderProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ExtendedBlockProto block = 1;
  if (has_block()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->block(), target);
  }

  // optional .hadoop.common.TokenProto token = 2;
  if (has_token()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->token(), target);
  }

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 3;
  if (has_traceinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->traceinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int BaseHeaderProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ExtendedBlockProto block = 1;
    if (has_block()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->block());
    }

    // optional .hadoop.common.TokenProto token = 2;
    if (has_token()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->token());
    }

    // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 3;
    if (has_traceinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->traceinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BaseHeaderProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const BaseHeaderProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const BaseHeaderProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void BaseHeaderProto::MergeFrom(const BaseHeaderProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_block()) {
      mutable_block()->::hadoop::hdfs::ExtendedBlockProto::MergeFrom(from.block());
    }
    if (from.has_token()) {
      mutable_token()->::hadoop::common::TokenProto::MergeFrom(from.token());
    }
    if (from.has_traceinfo()) {
      mutable_traceinfo()->::hadoop::hdfs::DataTransferTraceInfoProto::MergeFrom(from.traceinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void BaseHeaderProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BaseHeaderProto::CopyFrom(const BaseHeaderProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BaseHeaderProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_block()) {
    if (!this->block().IsInitialized()) return false;
  }
  if (has_token()) {
    if (!this->token().IsInitialized()) return false;
  }
  if (has_traceinfo()) {
    if (!this->traceinfo().IsInitialized()) return false;
  }
  return true;
}

void BaseHeaderProto::Swap(BaseHeaderProto* other) {
  if (other != this) {
    std::swap(block_, other->block_);
    std::swap(token_, other->token_);
    std::swap(traceinfo_, other->traceinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata BaseHeaderProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BaseHeaderProto_descriptor_;
  metadata.reflection = BaseHeaderProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DataTransferTraceInfoProto::kTraceIdFieldNumber;
const int DataTransferTraceInfoProto::kParentIdFieldNumber;
#endif  // !_MSC_VER

DataTransferTraceInfoProto::DataTransferTraceInfoProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DataTransferTraceInfoProto::InitAsDefaultInstance() {
}

DataTransferTraceInfoProto::DataTransferTraceInfoProto(const DataTransferTraceInfoProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DataTransferTraceInfoProto::SharedCtor() {
  _cached_size_ = 0;
  traceid_ = GOOGLE_ULONGLONG(0);
  parentid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DataTransferTraceInfoProto::~DataTransferTraceInfoProto() {
  SharedDtor();
}

void DataTransferTraceInfoProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DataTransferTraceInfoProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DataTransferTraceInfoProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DataTransferTraceInfoProto_descriptor_;
}

const DataTransferTraceInfoProto& DataTransferTraceInfoProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

DataTransferTraceInfoProto* DataTransferTraceInfoProto::default_instance_ = NULL;

DataTransferTraceInfoProto* DataTransferTraceInfoProto::New() const {
  return new DataTransferTraceInfoProto;
}

void DataTransferTraceInfoProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    traceid_ = GOOGLE_ULONGLONG(0);
    parentid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DataTransferTraceInfoProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 traceId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &traceid_)));
          set_has_traceid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_parentId;
        break;
      }

      // required uint64 parentId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_parentId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &parentid_)));
          set_has_parentid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DataTransferTraceInfoProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 traceId = 1;
  if (has_traceid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->traceid(), output);
  }

  // required uint64 parentId = 2;
  if (has_parentid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->parentid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DataTransferTraceInfoProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 traceId = 1;
  if (has_traceid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->traceid(), target);
  }

  // required uint64 parentId = 2;
  if (has_parentid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->parentid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DataTransferTraceInfoProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 traceId = 1;
    if (has_traceid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->traceid());
    }

    // required uint64 parentId = 2;
    if (has_parentid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->parentid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DataTransferTraceInfoProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DataTransferTraceInfoProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DataTransferTraceInfoProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DataTransferTraceInfoProto::MergeFrom(const DataTransferTraceInfoProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_traceid()) {
      set_traceid(from.traceid());
    }
    if (from.has_parentid()) {
      set_parentid(from.parentid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DataTransferTraceInfoProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DataTransferTraceInfoProto::CopyFrom(const DataTransferTraceInfoProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataTransferTraceInfoProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void DataTransferTraceInfoProto::Swap(DataTransferTraceInfoProto* other) {
  if (other != this) {
    std::swap(traceid_, other->traceid_);
    std::swap(parentid_, other->parentid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DataTransferTraceInfoProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DataTransferTraceInfoProto_descriptor_;
  metadata.reflection = DataTransferTraceInfoProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ClientOperationHeaderProto::kBaseHeaderFieldNumber;
const int ClientOperationHeaderProto::kClientNameFieldNumber;
#endif  // !_MSC_VER

ClientOperationHeaderProto::ClientOperationHeaderProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ClientOperationHeaderProto::InitAsDefaultInstance() {
  baseheader_ = const_cast< ::hadoop::hdfs::BaseHeaderProto*>(&::hadoop::hdfs::BaseHeaderProto::default_instance());
}

ClientOperationHeaderProto::ClientOperationHeaderProto(const ClientOperationHeaderProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ClientOperationHeaderProto::SharedCtor() {
  _cached_size_ = 0;
  baseheader_ = NULL;
  clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ClientOperationHeaderProto::~ClientOperationHeaderProto() {
  SharedDtor();
}

void ClientOperationHeaderProto::SharedDtor() {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    delete clientname_;
  }
  if (this != default_instance_) {
    delete baseheader_;
  }
}

void ClientOperationHeaderProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientOperationHeaderProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ClientOperationHeaderProto_descriptor_;
}

const ClientOperationHeaderProto& ClientOperationHeaderProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ClientOperationHeaderProto* ClientOperationHeaderProto::default_instance_ = NULL;

ClientOperationHeaderProto* ClientOperationHeaderProto::New() const {
  return new ClientOperationHeaderProto;
}

void ClientOperationHeaderProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_baseheader()) {
      if (baseheader_ != NULL) baseheader_->::hadoop::hdfs::BaseHeaderProto::Clear();
    }
    if (has_clientname()) {
      if (clientname_ != &::google::protobuf::internal::kEmptyString) {
        clientname_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ClientOperationHeaderProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.BaseHeaderProto baseHeader = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_baseheader()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_clientName;
        break;
      }

      // required string clientName = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_clientName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_clientname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->clientname().data(), this->clientname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ClientOperationHeaderProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.BaseHeaderProto baseHeader = 1;
  if (has_baseheader()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->baseheader(), output);
  }

  // required string clientName = 2;
  if (has_clientname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientname().data(), this->clientname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->clientname(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ClientOperationHeaderProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.BaseHeaderProto baseHeader = 1;
  if (has_baseheader()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->baseheader(), target);
  }

  // required string clientName = 2;
  if (has_clientname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientname().data(), this->clientname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->clientname(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ClientOperationHeaderProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.BaseHeaderProto baseHeader = 1;
    if (has_baseheader()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->baseheader());
    }

    // required string clientName = 2;
    if (has_clientname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->clientname());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientOperationHeaderProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ClientOperationHeaderProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ClientOperationHeaderProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ClientOperationHeaderProto::MergeFrom(const ClientOperationHeaderProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_baseheader()) {
      mutable_baseheader()->::hadoop::hdfs::BaseHeaderProto::MergeFrom(from.baseheader());
    }
    if (from.has_clientname()) {
      set_clientname(from.clientname());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ClientOperationHeaderProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientOperationHeaderProto::CopyFrom(const ClientOperationHeaderProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientOperationHeaderProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_baseheader()) {
    if (!this->baseheader().IsInitialized()) return false;
  }
  return true;
}

void ClientOperationHeaderProto::Swap(ClientOperationHeaderProto* other) {
  if (other != this) {
    std::swap(baseheader_, other->baseheader_);
    std::swap(clientname_, other->clientname_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ClientOperationHeaderProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ClientOperationHeaderProto_descriptor_;
  metadata.reflection = ClientOperationHeaderProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CachingStrategyProto::kDropBehindFieldNumber;
const int CachingStrategyProto::kReadaheadFieldNumber;
#endif  // !_MSC_VER

CachingStrategyProto::CachingStrategyProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CachingStrategyProto::InitAsDefaultInstance() {
}

CachingStrategyProto::CachingStrategyProto(const CachingStrategyProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CachingStrategyProto::SharedCtor() {
  _cached_size_ = 0;
  dropbehind_ = false;
  readahead_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CachingStrategyProto::~CachingStrategyProto() {
  SharedDtor();
}

void CachingStrategyProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CachingStrategyProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CachingStrategyProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CachingStrategyProto_descriptor_;
}

const CachingStrategyProto& CachingStrategyProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

CachingStrategyProto* CachingStrategyProto::default_instance_ = NULL;

CachingStrategyProto* CachingStrategyProto::New() const {
  return new CachingStrategyProto;
}

void CachingStrategyProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    dropbehind_ = false;
    readahead_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CachingStrategyProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool dropBehind = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &dropbehind_)));
          set_has_dropbehind();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_readahead;
        break;
      }

      // optional int64 readahead = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_readahead:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &readahead_)));
          set_has_readahead();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CachingStrategyProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional bool dropBehind = 1;
  if (has_dropbehind()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->dropbehind(), output);
  }

  // optional int64 readahead = 2;
  if (has_readahead()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->readahead(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CachingStrategyProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional bool dropBehind = 1;
  if (has_dropbehind()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->dropbehind(), target);
  }

  // optional int64 readahead = 2;
  if (has_readahead()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->readahead(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CachingStrategyProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional bool dropBehind = 1;
    if (has_dropbehind()) {
      total_size += 1 + 1;
    }

    // optional int64 readahead = 2;
    if (has_readahead()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->readahead());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CachingStrategyProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CachingStrategyProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CachingStrategyProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CachingStrategyProto::MergeFrom(const CachingStrategyProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_dropbehind()) {
      set_dropbehind(from.dropbehind());
    }
    if (from.has_readahead()) {
      set_readahead(from.readahead());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CachingStrategyProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CachingStrategyProto::CopyFrom(const CachingStrategyProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CachingStrategyProto::IsInitialized() const {

  return true;
}

void CachingStrategyProto::Swap(CachingStrategyProto* other) {
  if (other != this) {
    std::swap(dropbehind_, other->dropbehind_);
    std::swap(readahead_, other->readahead_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CachingStrategyProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CachingStrategyProto_descriptor_;
  metadata.reflection = CachingStrategyProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpReadBlockProto::kHeaderFieldNumber;
const int OpReadBlockProto::kOffsetFieldNumber;
const int OpReadBlockProto::kLenFieldNumber;
const int OpReadBlockProto::kSendChecksumsFieldNumber;
const int OpReadBlockProto::kCachingStrategyFieldNumber;
#endif  // !_MSC_VER

OpReadBlockProto::OpReadBlockProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpReadBlockProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::ClientOperationHeaderProto*>(&::hadoop::hdfs::ClientOperationHeaderProto::default_instance());
  cachingstrategy_ = const_cast< ::hadoop::hdfs::CachingStrategyProto*>(&::hadoop::hdfs::CachingStrategyProto::default_instance());
}

OpReadBlockProto::OpReadBlockProto(const OpReadBlockProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpReadBlockProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  offset_ = GOOGLE_ULONGLONG(0);
  len_ = GOOGLE_ULONGLONG(0);
  sendchecksums_ = true;
  cachingstrategy_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpReadBlockProto::~OpReadBlockProto() {
  SharedDtor();
}

void OpReadBlockProto::SharedDtor() {
  if (this != default_instance_) {
    delete header_;
    delete cachingstrategy_;
  }
}

void OpReadBlockProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpReadBlockProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpReadBlockProto_descriptor_;
}

const OpReadBlockProto& OpReadBlockProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpReadBlockProto* OpReadBlockProto::default_instance_ = NULL;

OpReadBlockProto* OpReadBlockProto::New() const {
  return new OpReadBlockProto;
}

void OpReadBlockProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::ClientOperationHeaderProto::Clear();
    }
    offset_ = GOOGLE_ULONGLONG(0);
    len_ = GOOGLE_ULONGLONG(0);
    sendchecksums_ = true;
    if (has_cachingstrategy()) {
      if (cachingstrategy_ != NULL) cachingstrategy_->::hadoop::hdfs::CachingStrategyProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpReadBlockProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_offset;
        break;
      }

      // required uint64 offset = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_offset:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &offset_)));
          set_has_offset();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_len;
        break;
      }

      // required uint64 len = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_len:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &len_)));
          set_has_len();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_sendChecksums;
        break;
      }

      // optional bool sendChecksums = 4 [default = true];
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_sendChecksums:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &sendchecksums_)));
          set_has_sendchecksums();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_cachingStrategy;
        break;
      }

      // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_cachingStrategy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_cachingstrategy()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpReadBlockProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  // required uint64 offset = 2;
  if (has_offset()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->offset(), output);
  }

  // required uint64 len = 3;
  if (has_len()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->len(), output);
  }

  // optional bool sendChecksums = 4 [default = true];
  if (has_sendchecksums()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->sendchecksums(), output);
  }

  // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 5;
  if (has_cachingstrategy()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->cachingstrategy(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpReadBlockProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  // required uint64 offset = 2;
  if (has_offset()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->offset(), target);
  }

  // required uint64 len = 3;
  if (has_len()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->len(), target);
  }

  // optional bool sendChecksums = 4 [default = true];
  if (has_sendchecksums()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->sendchecksums(), target);
  }

  // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 5;
  if (has_cachingstrategy()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        5, this->cachingstrategy(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpReadBlockProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

    // required uint64 offset = 2;
    if (has_offset()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->offset());
    }

    // required uint64 len = 3;
    if (has_len()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->len());
    }

    // optional bool sendChecksums = 4 [default = true];
    if (has_sendchecksums()) {
      total_size += 1 + 1;
    }

    // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 5;
    if (has_cachingstrategy()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->cachingstrategy());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpReadBlockProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpReadBlockProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpReadBlockProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpReadBlockProto::MergeFrom(const OpReadBlockProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::ClientOperationHeaderProto::MergeFrom(from.header());
    }
    if (from.has_offset()) {
      set_offset(from.offset());
    }
    if (from.has_len()) {
      set_len(from.len());
    }
    if (from.has_sendchecksums()) {
      set_sendchecksums(from.sendchecksums());
    }
    if (from.has_cachingstrategy()) {
      mutable_cachingstrategy()->::hadoop::hdfs::CachingStrategyProto::MergeFrom(from.cachingstrategy());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpReadBlockProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpReadBlockProto::CopyFrom(const OpReadBlockProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpReadBlockProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  return true;
}

void OpReadBlockProto::Swap(OpReadBlockProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    std::swap(offset_, other->offset_);
    std::swap(len_, other->len_);
    std::swap(sendchecksums_, other->sendchecksums_);
    std::swap(cachingstrategy_, other->cachingstrategy_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpReadBlockProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpReadBlockProto_descriptor_;
  metadata.reflection = OpReadBlockProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ChecksumProto::kTypeFieldNumber;
const int ChecksumProto::kBytesPerChecksumFieldNumber;
#endif  // !_MSC_VER

ChecksumProto::ChecksumProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ChecksumProto::InitAsDefaultInstance() {
}

ChecksumProto::ChecksumProto(const ChecksumProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ChecksumProto::SharedCtor() {
  _cached_size_ = 0;
  type_ = 0;
  bytesperchecksum_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ChecksumProto::~ChecksumProto() {
  SharedDtor();
}

void ChecksumProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ChecksumProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ChecksumProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ChecksumProto_descriptor_;
}

const ChecksumProto& ChecksumProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ChecksumProto* ChecksumProto::default_instance_ = NULL;

ChecksumProto* ChecksumProto::New() const {
  return new ChecksumProto;
}

void ChecksumProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    type_ = 0;
    bytesperchecksum_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ChecksumProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ChecksumTypeProto type = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::ChecksumTypeProto_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::ChecksumTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_bytesPerChecksum;
        break;
      }

      // required uint32 bytesPerChecksum = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_bytesPerChecksum:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &bytesperchecksum_)));
          set_has_bytesperchecksum();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ChecksumProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ChecksumTypeProto type = 1;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // required uint32 bytesPerChecksum = 2;
  if (has_bytesperchecksum()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->bytesperchecksum(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ChecksumProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ChecksumTypeProto type = 1;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // required uint32 bytesPerChecksum = 2;
  if (has_bytesperchecksum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->bytesperchecksum(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ChecksumProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ChecksumTypeProto type = 1;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // required uint32 bytesPerChecksum = 2;
    if (has_bytesperchecksum()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->bytesperchecksum());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ChecksumProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ChecksumProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ChecksumProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ChecksumProto::MergeFrom(const ChecksumProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_bytesperchecksum()) {
      set_bytesperchecksum(from.bytesperchecksum());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ChecksumProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ChecksumProto::CopyFrom(const ChecksumProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChecksumProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void ChecksumProto::Swap(ChecksumProto* other) {
  if (other != this) {
    std::swap(type_, other->type_);
    std::swap(bytesperchecksum_, other->bytesperchecksum_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ChecksumProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ChecksumProto_descriptor_;
  metadata.reflection = ChecksumProto_reflection_;
  return metadata;
}


// ===================================================================

const ::google::protobuf::EnumDescriptor* OpWriteBlockProto_BlockConstructionStage_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpWriteBlockProto_BlockConstructionStage_descriptor_;
}
bool OpWriteBlockProto_BlockConstructionStage_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::PIPELINE_SETUP_APPEND;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::PIPELINE_SETUP_APPEND_RECOVERY;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::DATA_STREAMING;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::PIPELINE_SETUP_STREAMING_RECOVERY;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::PIPELINE_CLOSE;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::PIPELINE_CLOSE_RECOVERY;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::PIPELINE_SETUP_CREATE;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::TRANSFER_RBW;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::TRANSFER_FINALIZED;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::BlockConstructionStage_MIN;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::BlockConstructionStage_MAX;
const int OpWriteBlockProto::BlockConstructionStage_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int OpWriteBlockProto::kHeaderFieldNumber;
const int OpWriteBlockProto::kTargetsFieldNumber;
const int OpWriteBlockProto::kSourceFieldNumber;
const int OpWriteBlockProto::kStageFieldNumber;
const int OpWriteBlockProto::kPipelineSizeFieldNumber;
const int OpWriteBlockProto::kMinBytesRcvdFieldNumber;
const int OpWriteBlockProto::kMaxBytesRcvdFieldNumber;
const int OpWriteBlockProto::kLatestGenerationStampFieldNumber;
const int OpWriteBlockProto::kRequestedChecksumFieldNumber;
const int OpWriteBlockProto::kCachingStrategyFieldNumber;
const int OpWriteBlockProto::kStorageTypeFieldNumber;
const int OpWriteBlockProto::kTargetStorageTypesFieldNumber;
const int OpWriteBlockProto::kAllowLazyPersistFieldNumber;
#endif  // !_MSC_VER

OpWriteBlockProto::OpWriteBlockProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpWriteBlockProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::ClientOperationHeaderProto*>(&::hadoop::hdfs::ClientOperationHeaderProto::default_instance());
  source_ = const_cast< ::hadoop::hdfs::DatanodeInfoProto*>(&::hadoop::hdfs::DatanodeInfoProto::default_instance());
  requestedchecksum_ = const_cast< ::hadoop::hdfs::ChecksumProto*>(&::hadoop::hdfs::ChecksumProto::default_instance());
  cachingstrategy_ = const_cast< ::hadoop::hdfs::CachingStrategyProto*>(&::hadoop::hdfs::CachingStrategyProto::default_instance());
}

OpWriteBlockProto::OpWriteBlockProto(const OpWriteBlockProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpWriteBlockProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  source_ = NULL;
  stage_ = 0;
  pipelinesize_ = 0u;
  minbytesrcvd_ = GOOGLE_ULONGLONG(0);
  maxbytesrcvd_ = GOOGLE_ULONGLONG(0);
  latestgenerationstamp_ = GOOGLE_ULONGLONG(0);
  requestedchecksum_ = NULL;
  cachingstrategy_ = NULL;
  storagetype_ = 1;
  allowlazypersist_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpWriteBlockProto::~OpWriteBlockProto() {
  SharedDtor();
}

void OpWriteBlockProto::SharedDtor() {
  if (this != default_instance_) {
    delete header_;
    delete source_;
    delete requestedchecksum_;
    delete cachingstrategy_;
  }
}

void OpWriteBlockProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpWriteBlockProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpWriteBlockProto_descriptor_;
}

const OpWriteBlockProto& OpWriteBlockProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpWriteBlockProto* OpWriteBlockProto::default_instance_ = NULL;

OpWriteBlockProto* OpWriteBlockProto::New() const {
  return new OpWriteBlockProto;
}

void OpWriteBlockProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::ClientOperationHeaderProto::Clear();
    }
    if (has_source()) {
      if (source_ != NULL) source_->::hadoop::hdfs::DatanodeInfoProto::Clear();
    }
    stage_ = 0;
    pipelinesize_ = 0u;
    minbytesrcvd_ = GOOGLE_ULONGLONG(0);
    maxbytesrcvd_ = GOOGLE_ULONGLONG(0);
    latestgenerationstamp_ = GOOGLE_ULONGLONG(0);
  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (has_requestedchecksum()) {
      if (requestedchecksum_ != NULL) requestedchecksum_->::hadoop::hdfs::ChecksumProto::Clear();
    }
    if (has_cachingstrategy()) {
      if (cachingstrategy_ != NULL) cachingstrategy_->::hadoop::hdfs::CachingStrategyProto::Clear();
    }
    storagetype_ = 1;
    allowlazypersist_ = false;
  }
  targets_.Clear();
  targetstoragetypes_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpWriteBlockProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_targets;
        break;
      }

      // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_targets:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_targets()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_targets;
        if (input->ExpectTag(26)) goto parse_source;
        break;
      }

      // optional .hadoop.hdfs.DatanodeInfoProto source = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_source:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_source()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_stage;
        break;
      }

      // required .hadoop.hdfs.OpWriteBlockProto.BlockConstructionStage stage = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_stage:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage_IsValid(value)) {
            set_stage(static_cast< ::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage >(value));
          } else {
            mutable_unknown_fields()->AddVarint(4, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_pipelineSize;
        break;
      }

      // required uint32 pipelineSize = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_pipelineSize:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &pipelinesize_)));
          set_has_pipelinesize();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(48)) goto parse_minBytesRcvd;
        break;
      }

      // required uint64 minBytesRcvd = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_minBytesRcvd:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &minbytesrcvd_)));
          set_has_minbytesrcvd();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(56)) goto parse_maxBytesRcvd;
        break;
      }

      // required uint64 maxBytesRcvd = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_maxBytesRcvd:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &maxbytesrcvd_)));
          set_has_maxbytesrcvd();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(64)) goto parse_latestGenerationStamp;
        break;
      }

      // required uint64 latestGenerationStamp = 8;
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_latestGenerationStamp:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &latestgenerationstamp_)));
          set_has_latestgenerationstamp();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(74)) goto parse_requestedChecksum;
        break;
      }

      // required .hadoop.hdfs.ChecksumProto requestedChecksum = 9;
      case 9: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_requestedChecksum:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_requestedchecksum()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(82)) goto parse_cachingStrategy;
        break;
      }

      // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 10;
      case 10: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_cachingStrategy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_cachingstrategy()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(88)) goto parse_storageType;
        break;
      }

      // optional .hadoop.hdfs.StorageTypeProto storageType = 11 [default = DISK];
      case 11: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_storageType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::StorageTypeProto_IsValid(value)) {
            set_storagetype(static_cast< ::hadoop::hdfs::StorageTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(11, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(96)) goto parse_targetStorageTypes;
        break;
      }

      // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 12;
      case 12: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_targetStorageTypes:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::StorageTypeProto_IsValid(value)) {
            add_targetstoragetypes(static_cast< ::hadoop::hdfs::StorageTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(12, value);
          }
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_LENGTH_DELIMITED) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedEnumNoInline(
                 input,
                 &::hadoop::hdfs::StorageTypeProto_IsValid,
                 this->mutable_targetstoragetypes())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(96)) goto parse_targetStorageTypes;
        if (input->ExpectTag(104)) goto parse_allowLazyPersist;
        break;
      }

      // optional bool allowLazyPersist = 13 [default = false];
      case 13: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_allowLazyPersist:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &allowlazypersist_)));
          set_has_allowlazypersist();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpWriteBlockProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  for (int i = 0; i < this->targets_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->targets(i), output);
  }

  // optional .hadoop.hdfs.DatanodeInfoProto source = 3;
  if (has_source()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->source(), output);
  }

  // required .hadoop.hdfs.OpWriteBlockProto.BlockConstructionStage stage = 4;
  if (has_stage()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->stage(), output);
  }

  // required uint32 pipelineSize = 5;
  if (has_pipelinesize()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(5, this->pipelinesize(), output);
  }

  // required uint64 minBytesRcvd = 6;
  if (has_minbytesrcvd()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(6, this->minbytesrcvd(), output);
  }

  // required uint64 maxBytesRcvd = 7;
  if (has_maxbytesrcvd()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(7, this->maxbytesrcvd(), output);
  }

  // required uint64 latestGenerationStamp = 8;
  if (has_latestgenerationstamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(8, this->latestgenerationstamp(), output);
  }

  // required .hadoop.hdfs.ChecksumProto requestedChecksum = 9;
  if (has_requestedchecksum()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->requestedchecksum(), output);
  }

  // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 10;
  if (has_cachingstrategy()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->cachingstrategy(), output);
  }

  // optional .hadoop.hdfs.StorageTypeProto storageType = 11 [default = DISK];
  if (has_storagetype()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      11, this->storagetype(), output);
  }

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 12;
  for (int i = 0; i < this->targetstoragetypes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      12, this->targetstoragetypes(i), output);
  }

  // optional bool allowLazyPersist = 13 [default = false];
  if (has_allowlazypersist()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->allowlazypersist(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpWriteBlockProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  for (int i = 0; i < this->targets_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->targets(i), target);
  }

  // optional .hadoop.hdfs.DatanodeInfoProto source = 3;
  if (has_source()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->source(), target);
  }

  // required .hadoop.hdfs.OpWriteBlockProto.BlockConstructionStage stage = 4;
  if (has_stage()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->stage(), target);
  }

  // required uint32 pipelineSize = 5;
  if (has_pipelinesize()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(5, this->pipelinesize(), target);
  }

  // required uint64 minBytesRcvd = 6;
  if (has_minbytesrcvd()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(6, this->minbytesrcvd(), target);
  }

  // required uint64 maxBytesRcvd = 7;
  if (has_maxbytesrcvd()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(7, this->maxbytesrcvd(), target);
  }

  // required uint64 latestGenerationStamp = 8;
  if (has_latestgenerationstamp()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(8, this->latestgenerationstamp(), target);
  }

  // required .hadoop.hdfs.ChecksumProto requestedChecksum = 9;
  if (has_requestedchecksum()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        9, this->requestedchecksum(), target);
  }

  // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 10;
  if (has_cachingstrategy()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        10, this->cachingstrategy(), target);
  }

  // optional .hadoop.hdfs.StorageTypeProto storageType = 11 [default = DISK];
  if (has_storagetype()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      11, this->storagetype(), target);
  }

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 12;
  for (int i = 0; i < this->targetstoragetypes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      12, this->targetstoragetypes(i), target);
  }

  // optional bool allowLazyPersist = 13 [default = false];
  if (has_allowlazypersist()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(13, this->allowlazypersist(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpWriteBlockProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

    // optional .hadoop.hdfs.DatanodeInfoProto source = 3;
    if (has_source()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->source());
    }

    // required .hadoop.hdfs.OpWriteBlockProto.BlockConstructionStage stage = 4;
    if (has_stage()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->stage());
    }

    // required uint32 pipelineSize = 5;
    if (has_pipelinesize()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->pipelinesize());
    }

    // required uint64 minBytesRcvd = 6;
    if (has_minbytesrcvd()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->minbytesrcvd());
    }

    // required uint64 maxBytesRcvd = 7;
    if (has_maxbytesrcvd()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->maxbytesrcvd());
    }

    // required uint64 latestGenerationStamp = 8;
    if (has_latestgenerationstamp()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->latestgenerationstamp());
    }

  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    // required .hadoop.hdfs.ChecksumProto requestedChecksum = 9;
    if (has_requestedchecksum()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->requestedchecksum());
    }

    // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 10;
    if (has_cachingstrategy()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->cachingstrategy());
    }

    // optional .hadoop.hdfs.StorageTypeProto storageType = 11 [default = DISK];
    if (has_storagetype()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->storagetype());
    }

    // optional bool allowLazyPersist = 13 [default = false];
    if (has_allowlazypersist()) {
      total_size += 1 + 1;
    }

  }
  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  total_size += 1 * this->targets_size();
  for (int i = 0; i < this->targets_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->targets(i));
  }

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 12;
  {
    int data_size = 0;
    for (int i = 0; i < this->targetstoragetypes_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->targetstoragetypes(i));
    }
    total_size += 1 * this->targetstoragetypes_size() + data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpWriteBlockProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpWriteBlockProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpWriteBlockProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpWriteBlockProto::MergeFrom(const OpWriteBlockProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  targets_.MergeFrom(from.targets_);
  targetstoragetypes_.MergeFrom(from.targetstoragetypes_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::ClientOperationHeaderProto::MergeFrom(from.header());
    }
    if (from.has_source()) {
      mutable_source()->::hadoop::hdfs::DatanodeInfoProto::MergeFrom(from.source());
    }
    if (from.has_stage()) {
      set_stage(from.stage());
    }
    if (from.has_pipelinesize()) {
      set_pipelinesize(from.pipelinesize());
    }
    if (from.has_minbytesrcvd()) {
      set_minbytesrcvd(from.minbytesrcvd());
    }
    if (from.has_maxbytesrcvd()) {
      set_maxbytesrcvd(from.maxbytesrcvd());
    }
    if (from.has_latestgenerationstamp()) {
      set_latestgenerationstamp(from.latestgenerationstamp());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_requestedchecksum()) {
      mutable_requestedchecksum()->::hadoop::hdfs::ChecksumProto::MergeFrom(from.requestedchecksum());
    }
    if (from.has_cachingstrategy()) {
      mutable_cachingstrategy()->::hadoop::hdfs::CachingStrategyProto::MergeFrom(from.cachingstrategy());
    }
    if (from.has_storagetype()) {
      set_storagetype(from.storagetype());
    }
    if (from.has_allowlazypersist()) {
      set_allowlazypersist(from.allowlazypersist());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpWriteBlockProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpWriteBlockProto::CopyFrom(const OpWriteBlockProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpWriteBlockProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x000001f9) != 0x000001f9) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  for (int i = 0; i < targets_size(); i++) {
    if (!this->targets(i).IsInitialized()) return false;
  }
  if (has_source()) {
    if (!this->source().IsInitialized()) return false;
  }
  if (has_requestedchecksum()) {
    if (!this->requestedchecksum().IsInitialized()) return false;
  }
  return true;
}

void OpWriteBlockProto::Swap(OpWriteBlockProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    targets_.Swap(&other->targets_);
    std::swap(source_, other->source_);
    std::swap(stage_, other->stage_);
    std::swap(pipelinesize_, other->pipelinesize_);
    std::swap(minbytesrcvd_, other->minbytesrcvd_);
    std::swap(maxbytesrcvd_, other->maxbytesrcvd_);
    std::swap(latestgenerationstamp_, other->latestgenerationstamp_);
    std::swap(requestedchecksum_, other->requestedchecksum_);
    std::swap(cachingstrategy_, other->cachingstrategy_);
    std::swap(storagetype_, other->storagetype_);
    targetstoragetypes_.Swap(&other->targetstoragetypes_);
    std::swap(allowlazypersist_, other->allowlazypersist_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpWriteBlockProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpWriteBlockProto_descriptor_;
  metadata.reflection = OpWriteBlockProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpTransferBlockProto::kHeaderFieldNumber;
const int OpTransferBlockProto::kTargetsFieldNumber;
const int OpTransferBlockProto::kTargetStorageTypesFieldNumber;
#endif  // !_MSC_VER

OpTransferBlockProto::OpTransferBlockProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpTransferBlockProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::ClientOperationHeaderProto*>(&::hadoop::hdfs::ClientOperationHeaderProto::default_instance());
}

OpTransferBlockProto::OpTransferBlockProto(const OpTransferBlockProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpTransferBlockProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpTransferBlockProto::~OpTransferBlockProto() {
  SharedDtor();
}

void OpTransferBlockProto::SharedDtor() {
  if (this != default_instance_) {
    delete header_;
  }
}

void OpTransferBlockProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpTransferBlockProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpTransferBlockProto_descriptor_;
}

const OpTransferBlockProto& OpTransferBlockProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpTransferBlockProto* OpTransferBlockProto::default_instance_ = NULL;

OpTransferBlockProto* OpTransferBlockProto::New() const {
  return new OpTransferBlockProto;
}

void OpTransferBlockProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::ClientOperationHeaderProto::Clear();
    }
  }
  targets_.Clear();
  targetstoragetypes_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpTransferBlockProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_targets;
        break;
      }

      // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_targets:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_targets()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_targets;
        if (input->ExpectTag(24)) goto parse_targetStorageTypes;
        break;
      }

      // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_targetStorageTypes:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::StorageTypeProto_IsValid(value)) {
            add_targetstoragetypes(static_cast< ::hadoop::hdfs::StorageTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(3, value);
          }
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_LENGTH_DELIMITED) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedEnumNoInline(
                 input,
                 &::hadoop::hdfs::StorageTypeProto_IsValid,
                 this->mutable_targetstoragetypes())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_targetStorageTypes;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpTransferBlockProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  for (int i = 0; i < this->targets_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->targets(i), output);
  }

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 3;
  for (int i = 0; i < this->targetstoragetypes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->targetstoragetypes(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpTransferBlockProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  for (int i = 0; i < this->targets_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->targets(i), target);
  }

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 3;
  for (int i = 0; i < this->targetstoragetypes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->targetstoragetypes(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpTransferBlockProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

  }
  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  total_size += 1 * this->targets_size();
  for (int i = 0; i < this->targets_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->targets(i));
  }

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 3;
  {
    int data_size = 0;
    for (int i = 0; i < this->targetstoragetypes_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->targetstoragetypes(i));
    }
    total_size += 1 * this->targetstoragetypes_size() + data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpTransferBlockProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpTransferBlockProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpTransferBlockProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpTransferBlockProto::MergeFrom(const OpTransferBlockProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  targets_.MergeFrom(from.targets_);
  targetstoragetypes_.MergeFrom(from.targetstoragetypes_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::ClientOperationHeaderProto::MergeFrom(from.header());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpTransferBlockProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpTransferBlockProto::CopyFrom(const OpTransferBlockProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpTransferBlockProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  for (int i = 0; i < targets_size(); i++) {
    if (!this->targets(i).IsInitialized()) return false;
  }
  return true;
}

void OpTransferBlockProto::Swap(OpTransferBlockProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    targets_.Swap(&other->targets_);
    targetstoragetypes_.Swap(&other->targetstoragetypes_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpTransferBlockProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpTransferBlockProto_descriptor_;
  metadata.reflection = OpTransferBlockProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpReplaceBlockProto::kHeaderFieldNumber;
const int OpReplaceBlockProto::kDelHintFieldNumber;
const int OpReplaceBlockProto::kSourceFieldNumber;
const int OpReplaceBlockProto::kStorageTypeFieldNumber;
#endif  // !_MSC_VER

OpReplaceBlockProto::OpReplaceBlockProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpReplaceBlockProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::BaseHeaderProto*>(&::hadoop::hdfs::BaseHeaderProto::default_instance());
  source_ = const_cast< ::hadoop::hdfs::DatanodeInfoProto*>(&::hadoop::hdfs::DatanodeInfoProto::default_instance());
}

OpReplaceBlockProto::OpReplaceBlockProto(const OpReplaceBlockProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpReplaceBlockProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  delhint_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  source_ = NULL;
  storagetype_ = 1;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpReplaceBlockProto::~OpReplaceBlockProto() {
  SharedDtor();
}

void OpReplaceBlockProto::SharedDtor() {
  if (delhint_ != &::google::protobuf::internal::kEmptyString) {
    delete delhint_;
  }
  if (this != default_instance_) {
    delete header_;
    delete source_;
  }
}

void OpReplaceBlockProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpReplaceBlockProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpReplaceBlockProto_descriptor_;
}

const OpReplaceBlockProto& OpReplaceBlockProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpReplaceBlockProto* OpReplaceBlockProto::default_instance_ = NULL;

OpReplaceBlockProto* OpReplaceBlockProto::New() const {
  return new OpReplaceBlockProto;
}

void OpReplaceBlockProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
    }
    if (has_delhint()) {
      if (delhint_ != &::google::protobuf::internal::kEmptyString) {
        delhint_->clear();
      }
    }
    if (has_source()) {
      if (source_ != NULL) source_->::hadoop::hdfs::DatanodeInfoProto::Clear();
    }
    storagetype_ = 1;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpReplaceBlockProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.BaseHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_delHint;
        break;
      }

      // required string delHint = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_delHint:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_delhint()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->delhint().data(), this->delhint().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_source;
        break;
      }

      // required .hadoop.hdfs.DatanodeInfoProto source = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_source:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_source()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_storageType;
        break;
      }

      // optional .hadoop.hdfs.StorageTypeProto storageType = 4 [default = DISK];
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_storageType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::StorageTypeProto_IsValid(value)) {
            set_storagetype(static_cast< ::hadoop::hdfs::StorageTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(4, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpReplaceBlockProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  // required string delHint = 2;
  if (has_delhint()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->delhint().data(), this->delhint().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->delhint(), output);
  }

  // required .hadoop.hdfs.DatanodeInfoProto source = 3;
  if (has_source()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->source(), output);
  }

  // optional .hadoop.hdfs.StorageTypeProto storageType = 4 [default = DISK];
  if (has_storagetype()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->storagetype(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpReplaceBlockProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  // required string delHint = 2;
  if (has_delhint()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->delhint().data(), this->delhint().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->delhint(), target);
  }

  // required .hadoop.hdfs.DatanodeInfoProto source = 3;
  if (has_source()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->source(), target);
  }

  // optional .hadoop.hdfs.StorageTypeProto storageType = 4 [default = DISK];
  if (has_storagetype()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->storagetype(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpReplaceBlockProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.BaseHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

    // required string delHint = 2;
    if (has_delhint()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->delhint());
    }

    // required .hadoop.hdfs.DatanodeInfoProto source = 3;
    if (has_source()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->source());
    }

    // optional .hadoop.hdfs.StorageTypeProto storageType = 4 [default = DISK];
    if (has_storagetype()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->storagetype());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpReplaceBlockProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpReplaceBlockProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpReplaceBlockProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpReplaceBlockProto::MergeFrom(const OpReplaceBlockProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::BaseHeaderProto::MergeFrom(from.header());
    }
    if (from.has_delhint()) {
      set_delhint(from.delhint());
    }
    if (from.has_source()) {
      mutable_source()->::hadoop::hdfs::DatanodeInfoProto::MergeFrom(from.source());
    }
    if (from.has_storagetype()) {
      set_storagetype(from.storagetype());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpReplaceBlockProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpReplaceBlockProto::CopyFrom(const OpReplaceBlockProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpReplaceBlockProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  if (has_source()) {
    if (!this->source().IsInitialized()) return false;
  }
  return true;
}

void OpReplaceBlockProto::Swap(OpReplaceBlockProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    std::swap(delhint_, other->delhint_);
    std::swap(source_, other->source_);
    std::swap(storagetype_, other->storagetype_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpReplaceBlockProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpReplaceBlockProto_descriptor_;
  metadata.reflection = OpReplaceBlockProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpCopyBlockProto::kHeaderFieldNumber;
#endif  // !_MSC_VER

OpCopyBlockProto::OpCopyBlockProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpCopyBlockProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::BaseHeaderProto*>(&::hadoop::hdfs::BaseHeaderProto::default_instance());
}

OpCopyBlockProto::OpCopyBlockProto(const OpCopyBlockProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpCopyBlockProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpCopyBlockProto::~OpCopyBlockProto() {
  SharedDtor();
}

void OpCopyBlockProto::SharedDtor() {
  if (this != default_instance_) {
    delete header_;
  }
}

void OpCopyBlockProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpCopyBlockProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpCopyBlockProto_descriptor_;
}

const OpCopyBlockProto& OpCopyBlockProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpCopyBlockProto* OpCopyBlockProto::default_instance_ = NULL;

OpCopyBlockProto* OpCopyBlockProto::New() const {
  return new OpCopyBlockProto;
}

void OpCopyBlockProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpCopyBlockProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.BaseHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpCopyBlockProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpCopyBlockProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpCopyBlockProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.BaseHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpCopyBlockProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpCopyBlockProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpCopyBlockProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpCopyBlockProto::MergeFrom(const OpCopyBlockProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::BaseHeaderProto::MergeFrom(from.header());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpCopyBlockProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpCopyBlockProto::CopyFrom(const OpCopyBlockProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpCopyBlockProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  return true;
}

void OpCopyBlockProto::Swap(OpCopyBlockProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpCopyBlockProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpCopyBlockProto_descriptor_;
  metadata.reflection = OpCopyBlockProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpBlockChecksumProto::kHeaderFieldNumber;
#endif  // !_MSC_VER

OpBlockChecksumProto::OpBlockChecksumProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpBlockChecksumProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::BaseHeaderProto*>(&::hadoop::hdfs::BaseHeaderProto::default_instance());
}

OpBlockChecksumProto::OpBlockChecksumProto(const OpBlockChecksumProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpBlockChecksumProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpBlockChecksumProto::~OpBlockChecksumProto() {
  SharedDtor();
}

void OpBlockChecksumProto::SharedDtor() {
  if (this != default_instance_) {
    delete header_;
  }
}

void OpBlockChecksumProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpBlockChecksumProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpBlockChecksumProto_descriptor_;
}

const OpBlockChecksumProto& OpBlockChecksumProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpBlockChecksumProto* OpBlockChecksumProto::default_instance_ = NULL;

OpBlockChecksumProto* OpBlockChecksumProto::New() const {
  return new OpBlockChecksumProto;
}

void OpBlockChecksumProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpBlockChecksumProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.BaseHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpBlockChecksumProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpBlockChecksumProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpBlockChecksumProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.BaseHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpBlockChecksumProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpBlockChecksumProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpBlockChecksumProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpBlockChecksumProto::MergeFrom(const OpBlockChecksumProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::BaseHeaderProto::MergeFrom(from.header());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpBlockChecksumProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpBlockChecksumProto::CopyFrom(const OpBlockChecksumProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpBlockChecksumProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  return true;
}

void OpBlockChecksumProto::Swap(OpBlockChecksumProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpBlockChecksumProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpBlockChecksumProto_descriptor_;
  metadata.reflection = OpBlockChecksumProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ShortCircuitShmIdProto::kHiFieldNumber;
const int ShortCircuitShmIdProto::kLoFieldNumber;
#endif  // !_MSC_VER

ShortCircuitShmIdProto::ShortCircuitShmIdProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ShortCircuitShmIdProto::InitAsDefaultInstance() {
}

ShortCircuitShmIdProto::ShortCircuitShmIdProto(const ShortCircuitShmIdProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ShortCircuitShmIdProto::SharedCtor() {
  _cached_size_ = 0;
  hi_ = GOOGLE_LONGLONG(0);
  lo_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ShortCircuitShmIdProto::~ShortCircuitShmIdProto() {
  SharedDtor();
}

void ShortCircuitShmIdProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ShortCircuitShmIdProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ShortCircuitShmIdProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ShortCircuitShmIdProto_descriptor_;
}

const ShortCircuitShmIdProto& ShortCircuitShmIdProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ShortCircuitShmIdProto* ShortCircuitShmIdProto::default_instance_ = NULL;

ShortCircuitShmIdProto* ShortCircuitShmIdProto::New() const {
  return new ShortCircuitShmIdProto;
}

void ShortCircuitShmIdProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    hi_ = GOOGLE_LONGLONG(0);
    lo_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ShortCircuitShmIdProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 hi = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &hi_)));
          set_has_hi();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_lo;
        break;
      }

      // required int64 lo = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lo:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lo_)));
          set_has_lo();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ShortCircuitShmIdProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 hi = 1;
  if (has_hi()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->hi(), output);
  }

  // required int64 lo = 2;
  if (has_lo()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->lo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ShortCircuitShmIdProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 hi = 1;
  if (has_hi()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->hi(), target);
  }

  // required int64 lo = 2;
  if (has_lo()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->lo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ShortCircuitShmIdProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 hi = 1;
    if (has_hi()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->hi());
    }

    // required int64 lo = 2;
    if (has_lo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->lo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ShortCircuitShmIdProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ShortCircuitShmIdProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ShortCircuitShmIdProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ShortCircuitShmIdProto::MergeFrom(const ShortCircuitShmIdProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_hi()) {
      set_hi(from.hi());
    }
    if (from.has_lo()) {
      set_lo(from.lo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ShortCircuitShmIdProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ShortCircuitShmIdProto::CopyFrom(const ShortCircuitShmIdProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ShortCircuitShmIdProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void ShortCircuitShmIdProto::Swap(ShortCircuitShmIdProto* other) {
  if (other != this) {
    std::swap(hi_, other->hi_);
    std::swap(lo_, other->lo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ShortCircuitShmIdProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ShortCircuitShmIdProto_descriptor_;
  metadata.reflection = ShortCircuitShmIdProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ShortCircuitShmSlotProto::kShmIdFieldNumber;
const int ShortCircuitShmSlotProto::kSlotIdxFieldNumber;
#endif  // !_MSC_VER

ShortCircuitShmSlotProto::ShortCircuitShmSlotProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ShortCircuitShmSlotProto::InitAsDefaultInstance() {
  shmid_ = const_cast< ::hadoop::hdfs::ShortCircuitShmIdProto*>(&::hadoop::hdfs::ShortCircuitShmIdProto::default_instance());
}

ShortCircuitShmSlotProto::ShortCircuitShmSlotProto(const ShortCircuitShmSlotProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ShortCircuitShmSlotProto::SharedCtor() {
  _cached_size_ = 0;
  shmid_ = NULL;
  slotidx_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ShortCircuitShmSlotProto::~ShortCircuitShmSlotProto() {
  SharedDtor();
}

void ShortCircuitShmSlotProto::SharedDtor() {
  if (this != default_instance_) {
    delete shmid_;
  }
}

void ShortCircuitShmSlotProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ShortCircuitShmSlotProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ShortCircuitShmSlotProto_descriptor_;
}

const ShortCircuitShmSlotProto& ShortCircuitShmSlotProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ShortCircuitShmSlotProto* ShortCircuitShmSlotProto::default_instance_ = NULL;

ShortCircuitShmSlotProto* ShortCircuitShmSlotProto::New() const {
  return new ShortCircuitShmSlotProto;
}

void ShortCircuitShmSlotProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_shmid()) {
      if (shmid_ != NULL) shmid_->::hadoop::hdfs::ShortCircuitShmIdProto::Clear();
    }
    slotidx_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ShortCircuitShmSlotProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ShortCircuitShmIdProto shmId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_shmid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_slotIdx;
        break;
      }

      // required int32 slotIdx = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_slotIdx:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &slotidx_)));
          set_has_slotidx();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ShortCircuitShmSlotProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ShortCircuitShmIdProto shmId = 1;
  if (has_shmid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->shmid(), output);
  }

  // required int32 slotIdx = 2;
  if (has_slotidx()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->slotidx(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ShortCircuitShmSlotProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ShortCircuitShmIdProto shmId = 1;
  if (has_shmid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->shmid(), target);
  }

  // required int32 slotIdx = 2;
  if (has_slotidx()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->slotidx(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ShortCircuitShmSlotProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ShortCircuitShmIdProto shmId = 1;
    if (has_shmid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->shmid());
    }

    // required int32 slotIdx = 2;
    if (has_slotidx()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->slotidx());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ShortCircuitShmSlotProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ShortCircuitShmSlotProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ShortCircuitShmSlotProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ShortCircuitShmSlotProto::MergeFrom(const ShortCircuitShmSlotProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_shmid()) {
      mutable_shmid()->::hadoop::hdfs::ShortCircuitShmIdProto::MergeFrom(from.shmid());
    }
    if (from.has_slotidx()) {
      set_slotidx(from.slotidx());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ShortCircuitShmSlotProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ShortCircuitShmSlotProto::CopyFrom(const ShortCircuitShmSlotProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ShortCircuitShmSlotProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_shmid()) {
    if (!this->shmid().IsInitialized()) return false;
  }
  return true;
}

void ShortCircuitShmSlotProto::Swap(ShortCircuitShmSlotProto* other) {
  if (other != this) {
    std::swap(shmid_, other->shmid_);
    std::swap(slotidx_, other->slotidx_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ShortCircuitShmSlotProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ShortCircuitShmSlotProto_descriptor_;
  metadata.reflection = ShortCircuitShmSlotProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpRequestShortCircuitAccessProto::kHeaderFieldNumber;
const int OpRequestShortCircuitAccessProto::kMaxVersionFieldNumber;
const int OpRequestShortCircuitAccessProto::kSlotIdFieldNumber;
#endif  // !_MSC_VER

OpRequestShortCircuitAccessProto::OpRequestShortCircuitAccessProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpRequestShortCircuitAccessProto::InitAsDefaultInstance() {
  header_ = const_cast< ::hadoop::hdfs::BaseHeaderProto*>(&::hadoop::hdfs::BaseHeaderProto::default_instance());
  slotid_ = const_cast< ::hadoop::hdfs::ShortCircuitShmSlotProto*>(&::hadoop::hdfs::ShortCircuitShmSlotProto::default_instance());
}

OpRequestShortCircuitAccessProto::OpRequestShortCircuitAccessProto(const OpRequestShortCircuitAccessProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpRequestShortCircuitAccessProto::SharedCtor() {
  _cached_size_ = 0;
  header_ = NULL;
  maxversion_ = 0u;
  slotid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpRequestShortCircuitAccessProto::~OpRequestShortCircuitAccessProto() {
  SharedDtor();
}

void OpRequestShortCircuitAccessProto::SharedDtor() {
  if (this != default_instance_) {
    delete header_;
    delete slotid_;
  }
}

void OpRequestShortCircuitAccessProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpRequestShortCircuitAccessProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpRequestShortCircuitAccessProto_descriptor_;
}

const OpRequestShortCircuitAccessProto& OpRequestShortCircuitAccessProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpRequestShortCircuitAccessProto* OpRequestShortCircuitAccessProto::default_instance_ = NULL;

OpRequestShortCircuitAccessProto* OpRequestShortCircuitAccessProto::New() const {
  return new OpRequestShortCircuitAccessProto;
}

void OpRequestShortCircuitAccessProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_header()) {
      if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
    }
    maxversion_ = 0u;
    if (has_slotid()) {
      if (slotid_ != NULL) slotid_->::hadoop::hdfs::ShortCircuitShmSlotProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpRequestShortCircuitAccessProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.BaseHeaderProto header = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_header()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_maxVersion;
        break;
      }

      // required uint32 maxVersion = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_maxVersion:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &maxversion_)));
          set_has_maxversion();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_slotId;
        break;
      }

      // optional .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_slotId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_slotid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpRequestShortCircuitAccessProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->header(), output);
  }

  // required uint32 maxVersion = 2;
  if (has_maxversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->maxversion(), output);
  }

  // optional .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 3;
  if (has_slotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->slotid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpRequestShortCircuitAccessProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  if (has_header()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->header(), target);
  }

  // required uint32 maxVersion = 2;
  if (has_maxversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->maxversion(), target);
  }

  // optional .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 3;
  if (has_slotid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->slotid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpRequestShortCircuitAccessProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.BaseHeaderProto header = 1;
    if (has_header()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->header());
    }

    // required uint32 maxVersion = 2;
    if (has_maxversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->maxversion());
    }

    // optional .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 3;
    if (has_slotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->slotid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpRequestShortCircuitAccessProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpRequestShortCircuitAccessProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpRequestShortCircuitAccessProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpRequestShortCircuitAccessProto::MergeFrom(const OpRequestShortCircuitAccessProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_header()) {
      mutable_header()->::hadoop::hdfs::BaseHeaderProto::MergeFrom(from.header());
    }
    if (from.has_maxversion()) {
      set_maxversion(from.maxversion());
    }
    if (from.has_slotid()) {
      mutable_slotid()->::hadoop::hdfs::ShortCircuitShmSlotProto::MergeFrom(from.slotid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpRequestShortCircuitAccessProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpRequestShortCircuitAccessProto::CopyFrom(const OpRequestShortCircuitAccessProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpRequestShortCircuitAccessProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_header()) {
    if (!this->header().IsInitialized()) return false;
  }
  if (has_slotid()) {
    if (!this->slotid().IsInitialized()) return false;
  }
  return true;
}

void OpRequestShortCircuitAccessProto::Swap(OpRequestShortCircuitAccessProto* other) {
  if (other != this) {
    std::swap(header_, other->header_);
    std::swap(maxversion_, other->maxversion_);
    std::swap(slotid_, other->slotid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpRequestShortCircuitAccessProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpRequestShortCircuitAccessProto_descriptor_;
  metadata.reflection = OpRequestShortCircuitAccessProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ReleaseShortCircuitAccessRequestProto::kSlotIdFieldNumber;
const int ReleaseShortCircuitAccessRequestProto::kTraceInfoFieldNumber;
#endif  // !_MSC_VER

ReleaseShortCircuitAccessRequestProto::ReleaseShortCircuitAccessRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ReleaseShortCircuitAccessRequestProto::InitAsDefaultInstance() {
  slotid_ = const_cast< ::hadoop::hdfs::ShortCircuitShmSlotProto*>(&::hadoop::hdfs::ShortCircuitShmSlotProto::default_instance());
  traceinfo_ = const_cast< ::hadoop::hdfs::DataTransferTraceInfoProto*>(&::hadoop::hdfs::DataTransferTraceInfoProto::default_instance());
}

ReleaseShortCircuitAccessRequestProto::ReleaseShortCircuitAccessRequestProto(const ReleaseShortCircuitAccessRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ReleaseShortCircuitAccessRequestProto::SharedCtor() {
  _cached_size_ = 0;
  slotid_ = NULL;
  traceinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ReleaseShortCircuitAccessRequestProto::~ReleaseShortCircuitAccessRequestProto() {
  SharedDtor();
}

void ReleaseShortCircuitAccessRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete slotid_;
    delete traceinfo_;
  }
}

void ReleaseShortCircuitAccessRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ReleaseShortCircuitAccessRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ReleaseShortCircuitAccessRequestProto_descriptor_;
}

const ReleaseShortCircuitAccessRequestProto& ReleaseShortCircuitAccessRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ReleaseShortCircuitAccessRequestProto* ReleaseShortCircuitAccessRequestProto::default_instance_ = NULL;

ReleaseShortCircuitAccessRequestProto* ReleaseShortCircuitAccessRequestProto::New() const {
  return new ReleaseShortCircuitAccessRequestProto;
}

void ReleaseShortCircuitAccessRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_slotid()) {
      if (slotid_ != NULL) slotid_->::hadoop::hdfs::ShortCircuitShmSlotProto::Clear();
    }
    if (has_traceinfo()) {
      if (traceinfo_ != NULL) traceinfo_->::hadoop::hdfs::DataTransferTraceInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ReleaseShortCircuitAccessRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_slotid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_traceInfo;
        break;
      }

      // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_traceInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_traceinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ReleaseShortCircuitAccessRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 1;
  if (has_slotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->slotid(), output);
  }

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
  if (has_traceinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->traceinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ReleaseShortCircuitAccessRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 1;
  if (has_slotid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->slotid(), target);
  }

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
  if (has_traceinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->traceinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ReleaseShortCircuitAccessRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 1;
    if (has_slotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->slotid());
    }

    // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
    if (has_traceinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->traceinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ReleaseShortCircuitAccessRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ReleaseShortCircuitAccessRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ReleaseShortCircuitAccessRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ReleaseShortCircuitAccessRequestProto::MergeFrom(const ReleaseShortCircuitAccessRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_slotid()) {
      mutable_slotid()->::hadoop::hdfs::ShortCircuitShmSlotProto::MergeFrom(from.slotid());
    }
    if (from.has_traceinfo()) {
      mutable_traceinfo()->::hadoop::hdfs::DataTransferTraceInfoProto::MergeFrom(from.traceinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ReleaseShortCircuitAccessRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReleaseShortCircuitAccessRequestProto::CopyFrom(const ReleaseShortCircuitAccessRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReleaseShortCircuitAccessRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_slotid()) {
    if (!this->slotid().IsInitialized()) return false;
  }
  if (has_traceinfo()) {
    if (!this->traceinfo().IsInitialized()) return false;
  }
  return true;
}

void ReleaseShortCircuitAccessRequestProto::Swap(ReleaseShortCircuitAccessRequestProto* other) {
  if (other != this) {
    std::swap(slotid_, other->slotid_);
    std::swap(traceinfo_, other->traceinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ReleaseShortCircuitAccessRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ReleaseShortCircuitAccessRequestProto_descriptor_;
  metadata.reflection = ReleaseShortCircuitAccessRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ReleaseShortCircuitAccessResponseProto::kStatusFieldNumber;
const int ReleaseShortCircuitAccessResponseProto::kErrorFieldNumber;
#endif  // !_MSC_VER

ReleaseShortCircuitAccessResponseProto::ReleaseShortCircuitAccessResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ReleaseShortCircuitAccessResponseProto::InitAsDefaultInstance() {
}

ReleaseShortCircuitAccessResponseProto::ReleaseShortCircuitAccessResponseProto(const ReleaseShortCircuitAccessResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ReleaseShortCircuitAccessResponseProto::SharedCtor() {
  _cached_size_ = 0;
  status_ = 0;
  error_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ReleaseShortCircuitAccessResponseProto::~ReleaseShortCircuitAccessResponseProto() {
  SharedDtor();
}

void ReleaseShortCircuitAccessResponseProto::SharedDtor() {
  if (error_ != &::google::protobuf::internal::kEmptyString) {
    delete error_;
  }
  if (this != default_instance_) {
  }
}

void ReleaseShortCircuitAccessResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ReleaseShortCircuitAccessResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ReleaseShortCircuitAccessResponseProto_descriptor_;
}

const ReleaseShortCircuitAccessResponseProto& ReleaseShortCircuitAccessResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ReleaseShortCircuitAccessResponseProto* ReleaseShortCircuitAccessResponseProto::default_instance_ = NULL;

ReleaseShortCircuitAccessResponseProto* ReleaseShortCircuitAccessResponseProto::New() const {
  return new ReleaseShortCircuitAccessResponseProto;
}

void ReleaseShortCircuitAccessResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    status_ = 0;
    if (has_error()) {
      if (error_ != &::google::protobuf::internal::kEmptyString) {
        error_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ReleaseShortCircuitAccessResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.Status status = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::Status_IsValid(value)) {
            set_status(static_cast< ::hadoop::hdfs::Status >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_error;
        break;
      }

      // optional string error = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_error:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_error()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->error().data(), this->error().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ReleaseShortCircuitAccessResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  // optional string error = 2;
  if (has_error()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->error().data(), this->error().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->error(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ReleaseShortCircuitAccessResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  // optional string error = 2;
  if (has_error()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->error().data(), this->error().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->error(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ReleaseShortCircuitAccessResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.Status status = 1;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

    // optional string error = 2;
    if (has_error()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->error());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ReleaseShortCircuitAccessResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ReleaseShortCircuitAccessResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ReleaseShortCircuitAccessResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ReleaseShortCircuitAccessResponseProto::MergeFrom(const ReleaseShortCircuitAccessResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_status()) {
      set_status(from.status());
    }
    if (from.has_error()) {
      set_error(from.error());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ReleaseShortCircuitAccessResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReleaseShortCircuitAccessResponseProto::CopyFrom(const ReleaseShortCircuitAccessResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReleaseShortCircuitAccessResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void ReleaseShortCircuitAccessResponseProto::Swap(ReleaseShortCircuitAccessResponseProto* other) {
  if (other != this) {
    std::swap(status_, other->status_);
    std::swap(error_, other->error_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ReleaseShortCircuitAccessResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ReleaseShortCircuitAccessResponseProto_descriptor_;
  metadata.reflection = ReleaseShortCircuitAccessResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ShortCircuitShmRequestProto::kClientNameFieldNumber;
const int ShortCircuitShmRequestProto::kTraceInfoFieldNumber;
#endif  // !_MSC_VER

ShortCircuitShmRequestProto::ShortCircuitShmRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ShortCircuitShmRequestProto::InitAsDefaultInstance() {
  traceinfo_ = const_cast< ::hadoop::hdfs::DataTransferTraceInfoProto*>(&::hadoop::hdfs::DataTransferTraceInfoProto::default_instance());
}

ShortCircuitShmRequestProto::ShortCircuitShmRequestProto(const ShortCircuitShmRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ShortCircuitShmRequestProto::SharedCtor() {
  _cached_size_ = 0;
  clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  traceinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ShortCircuitShmRequestProto::~ShortCircuitShmRequestProto() {
  SharedDtor();
}

void ShortCircuitShmRequestProto::SharedDtor() {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    delete clientname_;
  }
  if (this != default_instance_) {
    delete traceinfo_;
  }
}

void ShortCircuitShmRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ShortCircuitShmRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ShortCircuitShmRequestProto_descriptor_;
}

const ShortCircuitShmRequestProto& ShortCircuitShmRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ShortCircuitShmRequestProto* ShortCircuitShmRequestProto::default_instance_ = NULL;

ShortCircuitShmRequestProto* ShortCircuitShmRequestProto::New() const {
  return new ShortCircuitShmRequestProto;
}

void ShortCircuitShmRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_clientname()) {
      if (clientname_ != &::google::protobuf::internal::kEmptyString) {
        clientname_->clear();
      }
    }
    if (has_traceinfo()) {
      if (traceinfo_ != NULL) traceinfo_->::hadoop::hdfs::DataTransferTraceInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ShortCircuitShmRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string clientName = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_clientname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->clientname().data(), this->clientname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_traceInfo;
        break;
      }

      // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_traceInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_traceinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ShortCircuitShmRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string clientName = 1;
  if (has_clientname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientname().data(), this->clientname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->clientname(), output);
  }

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
  if (has_traceinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->traceinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ShortCircuitShmRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string clientName = 1;
  if (has_clientname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientname().data(), this->clientname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->clientname(), target);
  }

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
  if (has_traceinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->traceinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ShortCircuitShmRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string clientName = 1;
    if (has_clientname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->clientname());
    }

    // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
    if (has_traceinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->traceinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ShortCircuitShmRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ShortCircuitShmRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ShortCircuitShmRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ShortCircuitShmRequestProto::MergeFrom(const ShortCircuitShmRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_clientname()) {
      set_clientname(from.clientname());
    }
    if (from.has_traceinfo()) {
      mutable_traceinfo()->::hadoop::hdfs::DataTransferTraceInfoProto::MergeFrom(from.traceinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ShortCircuitShmRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ShortCircuitShmRequestProto::CopyFrom(const ShortCircuitShmRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ShortCircuitShmRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_traceinfo()) {
    if (!this->traceinfo().IsInitialized()) return false;
  }
  return true;
}

void ShortCircuitShmRequestProto::Swap(ShortCircuitShmRequestProto* other) {
  if (other != this) {
    std::swap(clientname_, other->clientname_);
    std::swap(traceinfo_, other->traceinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ShortCircuitShmRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ShortCircuitShmRequestProto_descriptor_;
  metadata.reflection = ShortCircuitShmRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ShortCircuitShmResponseProto::kStatusFieldNumber;
const int ShortCircuitShmResponseProto::kErrorFieldNumber;
const int ShortCircuitShmResponseProto::kIdFieldNumber;
#endif  // !_MSC_VER

ShortCircuitShmResponseProto::ShortCircuitShmResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ShortCircuitShmResponseProto::InitAsDefaultInstance() {
  id_ = const_cast< ::hadoop::hdfs::ShortCircuitShmIdProto*>(&::hadoop::hdfs::ShortCircuitShmIdProto::default_instance());
}

ShortCircuitShmResponseProto::ShortCircuitShmResponseProto(const ShortCircuitShmResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ShortCircuitShmResponseProto::SharedCtor() {
  _cached_size_ = 0;
  status_ = 0;
  error_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  id_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ShortCircuitShmResponseProto::~ShortCircuitShmResponseProto() {
  SharedDtor();
}

void ShortCircuitShmResponseProto::SharedDtor() {
  if (error_ != &::google::protobuf::internal::kEmptyString) {
    delete error_;
  }
  if (this != default_instance_) {
    delete id_;
  }
}

void ShortCircuitShmResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ShortCircuitShmResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ShortCircuitShmResponseProto_descriptor_;
}

const ShortCircuitShmResponseProto& ShortCircuitShmResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ShortCircuitShmResponseProto* ShortCircuitShmResponseProto::default_instance_ = NULL;

ShortCircuitShmResponseProto* ShortCircuitShmResponseProto::New() const {
  return new ShortCircuitShmResponseProto;
}

void ShortCircuitShmResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    status_ = 0;
    if (has_error()) {
      if (error_ != &::google::protobuf::internal::kEmptyString) {
        error_->clear();
      }
    }
    if (has_id()) {
      if (id_ != NULL) id_->::hadoop::hdfs::ShortCircuitShmIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ShortCircuitShmResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.Status status = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::Status_IsValid(value)) {
            set_status(static_cast< ::hadoop::hdfs::Status >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_error;
        break;
      }

      // optional string error = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_error:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_error()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->error().data(), this->error().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_id;
        break;
      }

      // optional .hadoop.hdfs.ShortCircuitShmIdProto id = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_id:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_id()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ShortCircuitShmResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  // optional string error = 2;
  if (has_error()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->error().data(), this->error().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->error(), output);
  }

  // optional .hadoop.hdfs.ShortCircuitShmIdProto id = 3;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->id(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ShortCircuitShmResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  // optional string error = 2;
  if (has_error()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->error().data(), this->error().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->error(), target);
  }

  // optional .hadoop.hdfs.ShortCircuitShmIdProto id = 3;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->id(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ShortCircuitShmResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.Status status = 1;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

    // optional string error = 2;
    if (has_error()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->error());
    }

    // optional .hadoop.hdfs.ShortCircuitShmIdProto id = 3;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->id());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ShortCircuitShmResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ShortCircuitShmResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ShortCircuitShmResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ShortCircuitShmResponseProto::MergeFrom(const ShortCircuitShmResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_status()) {
      set_status(from.status());
    }
    if (from.has_error()) {
      set_error(from.error());
    }
    if (from.has_id()) {
      mutable_id()->::hadoop::hdfs::ShortCircuitShmIdProto::MergeFrom(from.id());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ShortCircuitShmResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ShortCircuitShmResponseProto::CopyFrom(const ShortCircuitShmResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ShortCircuitShmResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_id()) {
    if (!this->id().IsInitialized()) return false;
  }
  return true;
}

void ShortCircuitShmResponseProto::Swap(ShortCircuitShmResponseProto* other) {
  if (other != this) {
    std::swap(status_, other->status_);
    std::swap(error_, other->error_);
    std::swap(id_, other->id_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ShortCircuitShmResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ShortCircuitShmResponseProto_descriptor_;
  metadata.reflection = ShortCircuitShmResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int PacketHeaderProto::kOffsetInBlockFieldNumber;
const int PacketHeaderProto::kSeqnoFieldNumber;
const int PacketHeaderProto::kLastPacketInBlockFieldNumber;
const int PacketHeaderProto::kDataLenFieldNumber;
const int PacketHeaderProto::kSyncBlockFieldNumber;
#endif  // !_MSC_VER

PacketHeaderProto::PacketHeaderProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PacketHeaderProto::InitAsDefaultInstance() {
}

PacketHeaderProto::PacketHeaderProto(const PacketHeaderProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PacketHeaderProto::SharedCtor() {
  _cached_size_ = 0;
  offsetinblock_ = GOOGLE_LONGLONG(0);
  seqno_ = GOOGLE_LONGLONG(0);
  lastpacketinblock_ = false;
  datalen_ = 0;
  syncblock_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PacketHeaderProto::~PacketHeaderProto() {
  SharedDtor();
}

void PacketHeaderProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void PacketHeaderProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PacketHeaderProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PacketHeaderProto_descriptor_;
}

const PacketHeaderProto& PacketHeaderProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

PacketHeaderProto* PacketHeaderProto::default_instance_ = NULL;

PacketHeaderProto* PacketHeaderProto::New() const {
  return new PacketHeaderProto;
}

void PacketHeaderProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    offsetinblock_ = GOOGLE_LONGLONG(0);
    seqno_ = GOOGLE_LONGLONG(0);
    lastpacketinblock_ = false;
    datalen_ = 0;
    syncblock_ = false;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PacketHeaderProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required sfixed64 offsetInBlock = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED64) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &offsetinblock_)));
          set_has_offsetinblock();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(17)) goto parse_seqno;
        break;
      }

      // required sfixed64 seqno = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED64) {
         parse_seqno:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &seqno_)));
          set_has_seqno();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_lastPacketInBlock;
        break;
      }

      // required bool lastPacketInBlock = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lastPacketInBlock:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &lastpacketinblock_)));
          set_has_lastpacketinblock();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(37)) goto parse_dataLen;
        break;
      }

      // required sfixed32 dataLen = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED32) {
         parse_dataLen:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, &datalen_)));
          set_has_datalen();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_syncBlock;
        break;
      }

      // optional bool syncBlock = 5 [default = false];
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_syncBlock:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &syncblock_)));
          set_has_syncblock();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void PacketHeaderProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required sfixed64 offsetInBlock = 1;
  if (has_offsetinblock()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(1, this->offsetinblock(), output);
  }

  // required sfixed64 seqno = 2;
  if (has_seqno()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(2, this->seqno(), output);
  }

  // required bool lastPacketInBlock = 3;
  if (has_lastpacketinblock()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->lastpacketinblock(), output);
  }

  // required sfixed32 dataLen = 4;
  if (has_datalen()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(4, this->datalen(), output);
  }

  // optional bool syncBlock = 5 [default = false];
  if (has_syncblock()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->syncblock(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PacketHeaderProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required sfixed64 offsetInBlock = 1;
  if (has_offsetinblock()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed64ToArray(1, this->offsetinblock(), target);
  }

  // required sfixed64 seqno = 2;
  if (has_seqno()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed64ToArray(2, this->seqno(), target);
  }

  // required bool lastPacketInBlock = 3;
  if (has_lastpacketinblock()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->lastpacketinblock(), target);
  }

  // required sfixed32 dataLen = 4;
  if (has_datalen()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed32ToArray(4, this->datalen(), target);
  }

  // optional bool syncBlock = 5 [default = false];
  if (has_syncblock()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->syncblock(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PacketHeaderProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required sfixed64 offsetInBlock = 1;
    if (has_offsetinblock()) {
      total_size += 1 + 8;
    }

    // required sfixed64 seqno = 2;
    if (has_seqno()) {
      total_size += 1 + 8;
    }

    // required bool lastPacketInBlock = 3;
    if (has_lastpacketinblock()) {
      total_size += 1 + 1;
    }

    // required sfixed32 dataLen = 4;
    if (has_datalen()) {
      total_size += 1 + 4;
    }

    // optional bool syncBlock = 5 [default = false];
    if (has_syncblock()) {
      total_size += 1 + 1;
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PacketHeaderProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PacketHeaderProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PacketHeaderProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PacketHeaderProto::MergeFrom(const PacketHeaderProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_offsetinblock()) {
      set_offsetinblock(from.offsetinblock());
    }
    if (from.has_seqno()) {
      set_seqno(from.seqno());
    }
    if (from.has_lastpacketinblock()) {
      set_lastpacketinblock(from.lastpacketinblock());
    }
    if (from.has_datalen()) {
      set_datalen(from.datalen());
    }
    if (from.has_syncblock()) {
      set_syncblock(from.syncblock());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PacketHeaderProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PacketHeaderProto::CopyFrom(const PacketHeaderProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PacketHeaderProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000000f) != 0x0000000f) return false;

  return true;
}

void PacketHeaderProto::Swap(PacketHeaderProto* other) {
  if (other != this) {
    std::swap(offsetinblock_, other->offsetinblock_);
    std::swap(seqno_, other->seqno_);
    std::swap(lastpacketinblock_, other->lastpacketinblock_);
    std::swap(datalen_, other->datalen_);
    std::swap(syncblock_, other->syncblock_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PacketHeaderProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PacketHeaderProto_descriptor_;
  metadata.reflection = PacketHeaderProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int PipelineAckProto::kSeqnoFieldNumber;
const int PipelineAckProto::kStatusFieldNumber;
const int PipelineAckProto::kDownstreamAckTimeNanosFieldNumber;
#endif  // !_MSC_VER

PipelineAckProto::PipelineAckProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PipelineAckProto::InitAsDefaultInstance() {
}

PipelineAckProto::PipelineAckProto(const PipelineAckProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PipelineAckProto::SharedCtor() {
  _cached_size_ = 0;
  seqno_ = GOOGLE_LONGLONG(0);
  downstreamacktimenanos_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PipelineAckProto::~PipelineAckProto() {
  SharedDtor();
}

void PipelineAckProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void PipelineAckProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PipelineAckProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PipelineAckProto_descriptor_;
}

const PipelineAckProto& PipelineAckProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

PipelineAckProto* PipelineAckProto::default_instance_ = NULL;

PipelineAckProto* PipelineAckProto::New() const {
  return new PipelineAckProto;
}

void PipelineAckProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    seqno_ = GOOGLE_LONGLONG(0);
    downstreamacktimenanos_ = GOOGLE_ULONGLONG(0);
  }
  status_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PipelineAckProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required sint64 seqno = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &seqno_)));
          set_has_seqno();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_status;
        break;
      }

      // repeated .hadoop.hdfs.Status status = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_status:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::Status_IsValid(value)) {
            add_status(static_cast< ::hadoop::hdfs::Status >(value));
          } else {
            mutable_unknown_fields()->AddVarint(2, value);
          }
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_LENGTH_DELIMITED) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedEnumNoInline(
                 input,
                 &::hadoop::hdfs::Status_IsValid,
                 this->mutable_status())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_status;
        if (input->ExpectTag(24)) goto parse_downstreamAckTimeNanos;
        break;
      }

      // optional uint64 downstreamAckTimeNanos = 3 [default = 0];
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_downstreamAckTimeNanos:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &downstreamacktimenanos_)));
          set_has_downstreamacktimenanos();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void PipelineAckProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required sint64 seqno = 1;
  if (has_seqno()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(1, this->seqno(), output);
  }

  // repeated .hadoop.hdfs.Status status = 2;
  for (int i = 0; i < this->status_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->status(i), output);
  }

  // optional uint64 downstreamAckTimeNanos = 3 [default = 0];
  if (has_downstreamacktimenanos()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->downstreamacktimenanos(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PipelineAckProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required sint64 seqno = 1;
  if (has_seqno()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt64ToArray(1, this->seqno(), target);
  }

  // repeated .hadoop.hdfs.Status status = 2;
  for (int i = 0; i < this->status_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->status(i), target);
  }

  // optional uint64 downstreamAckTimeNanos = 3 [default = 0];
  if (has_downstreamacktimenanos()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->downstreamacktimenanos(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PipelineAckProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required sint64 seqno = 1;
    if (has_seqno()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt64Size(
          this->seqno());
    }

    // optional uint64 downstreamAckTimeNanos = 3 [default = 0];
    if (has_downstreamacktimenanos()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->downstreamacktimenanos());
    }

  }
  // repeated .hadoop.hdfs.Status status = 2;
  {
    int data_size = 0;
    for (int i = 0; i < this->status_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->status(i));
    }
    total_size += 1 * this->status_size() + data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PipelineAckProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PipelineAckProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PipelineAckProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PipelineAckProto::MergeFrom(const PipelineAckProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  status_.MergeFrom(from.status_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_seqno()) {
      set_seqno(from.seqno());
    }
    if (from.has_downstreamacktimenanos()) {
      set_downstreamacktimenanos(from.downstreamacktimenanos());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PipelineAckProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PipelineAckProto::CopyFrom(const PipelineAckProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PipelineAckProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void PipelineAckProto::Swap(PipelineAckProto* other) {
  if (other != this) {
    std::swap(seqno_, other->seqno_);
    status_.Swap(&other->status_);
    std::swap(downstreamacktimenanos_, other->downstreamacktimenanos_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PipelineAckProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PipelineAckProto_descriptor_;
  metadata.reflection = PipelineAckProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ReadOpChecksumInfoProto::kChecksumFieldNumber;
const int ReadOpChecksumInfoProto::kChunkOffsetFieldNumber;
#endif  // !_MSC_VER

ReadOpChecksumInfoProto::ReadOpChecksumInfoProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ReadOpChecksumInfoProto::InitAsDefaultInstance() {
  checksum_ = const_cast< ::hadoop::hdfs::ChecksumProto*>(&::hadoop::hdfs::ChecksumProto::default_instance());
}

ReadOpChecksumInfoProto::ReadOpChecksumInfoProto(const ReadOpChecksumInfoProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ReadOpChecksumInfoProto::SharedCtor() {
  _cached_size_ = 0;
  checksum_ = NULL;
  chunkoffset_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ReadOpChecksumInfoProto::~ReadOpChecksumInfoProto() {
  SharedDtor();
}

void ReadOpChecksumInfoProto::SharedDtor() {
  if (this != default_instance_) {
    delete checksum_;
  }
}

void ReadOpChecksumInfoProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ReadOpChecksumInfoProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ReadOpChecksumInfoProto_descriptor_;
}

const ReadOpChecksumInfoProto& ReadOpChecksumInfoProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ReadOpChecksumInfoProto* ReadOpChecksumInfoProto::default_instance_ = NULL;

ReadOpChecksumInfoProto* ReadOpChecksumInfoProto::New() const {
  return new ReadOpChecksumInfoProto;
}

void ReadOpChecksumInfoProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_checksum()) {
      if (checksum_ != NULL) checksum_->::hadoop::hdfs::ChecksumProto::Clear();
    }
    chunkoffset_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ReadOpChecksumInfoProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.ChecksumProto checksum = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_checksum()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_chunkOffset;
        break;
      }

      // required uint64 chunkOffset = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_chunkOffset:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &chunkoffset_)));
          set_has_chunkoffset();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ReadOpChecksumInfoProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.ChecksumProto checksum = 1;
  if (has_checksum()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->checksum(), output);
  }

  // required uint64 chunkOffset = 2;
  if (has_chunkoffset()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->chunkoffset(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ReadOpChecksumInfoProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.ChecksumProto checksum = 1;
  if (has_checksum()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->checksum(), target);
  }

  // required uint64 chunkOffset = 2;
  if (has_chunkoffset()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->chunkoffset(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ReadOpChecksumInfoProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.ChecksumProto checksum = 1;
    if (has_checksum()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->checksum());
    }

    // required uint64 chunkOffset = 2;
    if (has_chunkoffset()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->chunkoffset());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ReadOpChecksumInfoProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ReadOpChecksumInfoProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ReadOpChecksumInfoProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ReadOpChecksumInfoProto::MergeFrom(const ReadOpChecksumInfoProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_checksum()) {
      mutable_checksum()->::hadoop::hdfs::ChecksumProto::MergeFrom(from.checksum());
    }
    if (from.has_chunkoffset()) {
      set_chunkoffset(from.chunkoffset());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ReadOpChecksumInfoProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReadOpChecksumInfoProto::CopyFrom(const ReadOpChecksumInfoProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReadOpChecksumInfoProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_checksum()) {
    if (!this->checksum().IsInitialized()) return false;
  }
  return true;
}

void ReadOpChecksumInfoProto::Swap(ReadOpChecksumInfoProto* other) {
  if (other != this) {
    std::swap(checksum_, other->checksum_);
    std::swap(chunkoffset_, other->chunkoffset_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ReadOpChecksumInfoProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ReadOpChecksumInfoProto_descriptor_;
  metadata.reflection = ReadOpChecksumInfoProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int BlockOpResponseProto::kStatusFieldNumber;
const int BlockOpResponseProto::kFirstBadLinkFieldNumber;
const int BlockOpResponseProto::kChecksumResponseFieldNumber;
const int BlockOpResponseProto::kReadOpChecksumInfoFieldNumber;
const int BlockOpResponseProto::kMessageFieldNumber;
const int BlockOpResponseProto::kShortCircuitAccessVersionFieldNumber;
#endif  // !_MSC_VER

BlockOpResponseProto::BlockOpResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void BlockOpResponseProto::InitAsDefaultInstance() {
  checksumresponse_ = const_cast< ::hadoop::hdfs::OpBlockChecksumResponseProto*>(&::hadoop::hdfs::OpBlockChecksumResponseProto::default_instance());
  readopchecksuminfo_ = const_cast< ::hadoop::hdfs::ReadOpChecksumInfoProto*>(&::hadoop::hdfs::ReadOpChecksumInfoProto::default_instance());
}

BlockOpResponseProto::BlockOpResponseProto(const BlockOpResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void BlockOpResponseProto::SharedCtor() {
  _cached_size_ = 0;
  status_ = 0;
  firstbadlink_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  checksumresponse_ = NULL;
  readopchecksuminfo_ = NULL;
  message_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  shortcircuitaccessversion_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

BlockOpResponseProto::~BlockOpResponseProto() {
  SharedDtor();
}

void BlockOpResponseProto::SharedDtor() {
  if (firstbadlink_ != &::google::protobuf::internal::kEmptyString) {
    delete firstbadlink_;
  }
  if (message_ != &::google::protobuf::internal::kEmptyString) {
    delete message_;
  }
  if (this != default_instance_) {
    delete checksumresponse_;
    delete readopchecksuminfo_;
  }
}

void BlockOpResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BlockOpResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BlockOpResponseProto_descriptor_;
}

const BlockOpResponseProto& BlockOpResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

BlockOpResponseProto* BlockOpResponseProto::default_instance_ = NULL;

BlockOpResponseProto* BlockOpResponseProto::New() const {
  return new BlockOpResponseProto;
}

void BlockOpResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    status_ = 0;
    if (has_firstbadlink()) {
      if (firstbadlink_ != &::google::protobuf::internal::kEmptyString) {
        firstbadlink_->clear();
      }
    }
    if (has_checksumresponse()) {
      if (checksumresponse_ != NULL) checksumresponse_->::hadoop::hdfs::OpBlockChecksumResponseProto::Clear();
    }
    if (has_readopchecksuminfo()) {
      if (readopchecksuminfo_ != NULL) readopchecksuminfo_->::hadoop::hdfs::ReadOpChecksumInfoProto::Clear();
    }
    if (has_message()) {
      if (message_ != &::google::protobuf::internal::kEmptyString) {
        message_->clear();
      }
    }
    shortcircuitaccessversion_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool BlockOpResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.Status status = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::Status_IsValid(value)) {
            set_status(static_cast< ::hadoop::hdfs::Status >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_firstBadLink;
        break;
      }

      // optional string firstBadLink = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_firstBadLink:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_firstbadlink()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->firstbadlink().data(), this->firstbadlink().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_checksumResponse;
        break;
      }

      // optional .hadoop.hdfs.OpBlockChecksumResponseProto checksumResponse = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_checksumResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_checksumresponse()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_readOpChecksumInfo;
        break;
      }

      // optional .hadoop.hdfs.ReadOpChecksumInfoProto readOpChecksumInfo = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_readOpChecksumInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_readopchecksuminfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_message;
        break;
      }

      // optional string message = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_message()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->message().data(), this->message().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(48)) goto parse_shortCircuitAccessVersion;
        break;
      }

      // optional uint32 shortCircuitAccessVersion = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_shortCircuitAccessVersion:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &shortcircuitaccessversion_)));
          set_has_shortcircuitaccessversion();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void BlockOpResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  // optional string firstBadLink = 2;
  if (has_firstbadlink()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->firstbadlink().data(), this->firstbadlink().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->firstbadlink(), output);
  }

  // optional .hadoop.hdfs.OpBlockChecksumResponseProto checksumResponse = 3;
  if (has_checksumresponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->checksumresponse(), output);
  }

  // optional .hadoop.hdfs.ReadOpChecksumInfoProto readOpChecksumInfo = 4;
  if (has_readopchecksuminfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->readopchecksuminfo(), output);
  }

  // optional string message = 5;
  if (has_message()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->message().data(), this->message().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->message(), output);
  }

  // optional uint32 shortCircuitAccessVersion = 6;
  if (has_shortcircuitaccessversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(6, this->shortcircuitaccessversion(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* BlockOpResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  // optional string firstBadLink = 2;
  if (has_firstbadlink()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->firstbadlink().data(), this->firstbadlink().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->firstbadlink(), target);
  }

  // optional .hadoop.hdfs.OpBlockChecksumResponseProto checksumResponse = 3;
  if (has_checksumresponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->checksumresponse(), target);
  }

  // optional .hadoop.hdfs.ReadOpChecksumInfoProto readOpChecksumInfo = 4;
  if (has_readopchecksuminfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->readopchecksuminfo(), target);
  }

  // optional string message = 5;
  if (has_message()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->message().data(), this->message().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->message(), target);
  }

  // optional uint32 shortCircuitAccessVersion = 6;
  if (has_shortcircuitaccessversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(6, this->shortcircuitaccessversion(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int BlockOpResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.Status status = 1;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

    // optional string firstBadLink = 2;
    if (has_firstbadlink()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->firstbadlink());
    }

    // optional .hadoop.hdfs.OpBlockChecksumResponseProto checksumResponse = 3;
    if (has_checksumresponse()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->checksumresponse());
    }

    // optional .hadoop.hdfs.ReadOpChecksumInfoProto readOpChecksumInfo = 4;
    if (has_readopchecksuminfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->readopchecksuminfo());
    }

    // optional string message = 5;
    if (has_message()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->message());
    }

    // optional uint32 shortCircuitAccessVersion = 6;
    if (has_shortcircuitaccessversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->shortcircuitaccessversion());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BlockOpResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const BlockOpResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const BlockOpResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void BlockOpResponseProto::MergeFrom(const BlockOpResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_status()) {
      set_status(from.status());
    }
    if (from.has_firstbadlink()) {
      set_firstbadlink(from.firstbadlink());
    }
    if (from.has_checksumresponse()) {
      mutable_checksumresponse()->::hadoop::hdfs::OpBlockChecksumResponseProto::MergeFrom(from.checksumresponse());
    }
    if (from.has_readopchecksuminfo()) {
      mutable_readopchecksuminfo()->::hadoop::hdfs::ReadOpChecksumInfoProto::MergeFrom(from.readopchecksuminfo());
    }
    if (from.has_message()) {
      set_message(from.message());
    }
    if (from.has_shortcircuitaccessversion()) {
      set_shortcircuitaccessversion(from.shortcircuitaccessversion());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void BlockOpResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BlockOpResponseProto::CopyFrom(const BlockOpResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BlockOpResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_checksumresponse()) {
    if (!this->checksumresponse().IsInitialized()) return false;
  }
  if (has_readopchecksuminfo()) {
    if (!this->readopchecksuminfo().IsInitialized()) return false;
  }
  return true;
}

void BlockOpResponseProto::Swap(BlockOpResponseProto* other) {
  if (other != this) {
    std::swap(status_, other->status_);
    std::swap(firstbadlink_, other->firstbadlink_);
    std::swap(checksumresponse_, other->checksumresponse_);
    std::swap(readopchecksuminfo_, other->readopchecksuminfo_);
    std::swap(message_, other->message_);
    std::swap(shortcircuitaccessversion_, other->shortcircuitaccessversion_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata BlockOpResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BlockOpResponseProto_descriptor_;
  metadata.reflection = BlockOpResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ClientReadStatusProto::kStatusFieldNumber;
#endif  // !_MSC_VER

ClientReadStatusProto::ClientReadStatusProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ClientReadStatusProto::InitAsDefaultInstance() {
}

ClientReadStatusProto::ClientReadStatusProto(const ClientReadStatusProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ClientReadStatusProto::SharedCtor() {
  _cached_size_ = 0;
  status_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ClientReadStatusProto::~ClientReadStatusProto() {
  SharedDtor();
}

void ClientReadStatusProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ClientReadStatusProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientReadStatusProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ClientReadStatusProto_descriptor_;
}

const ClientReadStatusProto& ClientReadStatusProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

ClientReadStatusProto* ClientReadStatusProto::default_instance_ = NULL;

ClientReadStatusProto* ClientReadStatusProto::New() const {
  return new ClientReadStatusProto;
}

void ClientReadStatusProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    status_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ClientReadStatusProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.Status status = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::Status_IsValid(value)) {
            set_status(static_cast< ::hadoop::hdfs::Status >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ClientReadStatusProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ClientReadStatusProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ClientReadStatusProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.Status status = 1;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientReadStatusProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ClientReadStatusProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ClientReadStatusProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ClientReadStatusProto::MergeFrom(const ClientReadStatusProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_status()) {
      set_status(from.status());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ClientReadStatusProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientReadStatusProto::CopyFrom(const ClientReadStatusProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientReadStatusProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void ClientReadStatusProto::Swap(ClientReadStatusProto* other) {
  if (other != this) {
    std::swap(status_, other->status_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ClientReadStatusProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ClientReadStatusProto_descriptor_;
  metadata.reflection = ClientReadStatusProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DNTransferAckProto::kStatusFieldNumber;
#endif  // !_MSC_VER

DNTransferAckProto::DNTransferAckProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DNTransferAckProto::InitAsDefaultInstance() {
}

DNTransferAckProto::DNTransferAckProto(const DNTransferAckProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DNTransferAckProto::SharedCtor() {
  _cached_size_ = 0;
  status_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DNTransferAckProto::~DNTransferAckProto() {
  SharedDtor();
}

void DNTransferAckProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DNTransferAckProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DNTransferAckProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DNTransferAckProto_descriptor_;
}

const DNTransferAckProto& DNTransferAckProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

DNTransferAckProto* DNTransferAckProto::default_instance_ = NULL;

DNTransferAckProto* DNTransferAckProto::New() const {
  return new DNTransferAckProto;
}

void DNTransferAckProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    status_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DNTransferAckProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.Status status = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::Status_IsValid(value)) {
            set_status(static_cast< ::hadoop::hdfs::Status >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DNTransferAckProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DNTransferAckProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.Status status = 1;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DNTransferAckProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.Status status = 1;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DNTransferAckProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DNTransferAckProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DNTransferAckProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DNTransferAckProto::MergeFrom(const DNTransferAckProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_status()) {
      set_status(from.status());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DNTransferAckProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DNTransferAckProto::CopyFrom(const DNTransferAckProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DNTransferAckProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void DNTransferAckProto::Swap(DNTransferAckProto* other) {
  if (other != this) {
    std::swap(status_, other->status_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DNTransferAckProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DNTransferAckProto_descriptor_;
  metadata.reflection = DNTransferAckProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int OpBlockChecksumResponseProto::kBytesPerCrcFieldNumber;
const int OpBlockChecksumResponseProto::kCrcPerBlockFieldNumber;
const int OpBlockChecksumResponseProto::kMd5FieldNumber;
const int OpBlockChecksumResponseProto::kCrcTypeFieldNumber;
#endif  // !_MSC_VER

OpBlockChecksumResponseProto::OpBlockChecksumResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void OpBlockChecksumResponseProto::InitAsDefaultInstance() {
}

OpBlockChecksumResponseProto::OpBlockChecksumResponseProto(const OpBlockChecksumResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void OpBlockChecksumResponseProto::SharedCtor() {
  _cached_size_ = 0;
  bytespercrc_ = 0u;
  crcperblock_ = GOOGLE_ULONGLONG(0);
  md5_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  crctype_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

OpBlockChecksumResponseProto::~OpBlockChecksumResponseProto() {
  SharedDtor();
}

void OpBlockChecksumResponseProto::SharedDtor() {
  if (md5_ != &::google::protobuf::internal::kEmptyString) {
    delete md5_;
  }
  if (this != default_instance_) {
  }
}

void OpBlockChecksumResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OpBlockChecksumResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OpBlockChecksumResponseProto_descriptor_;
}

const OpBlockChecksumResponseProto& OpBlockChecksumResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_datatransfer_2eproto();
  return *default_instance_;
}

OpBlockChecksumResponseProto* OpBlockChecksumResponseProto::default_instance_ = NULL;

OpBlockChecksumResponseProto* OpBlockChecksumResponseProto::New() const {
  return new OpBlockChecksumResponseProto;
}

void OpBlockChecksumResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    bytespercrc_ = 0u;
    crcperblock_ = GOOGLE_ULONGLONG(0);
    if (has_md5()) {
      if (md5_ != &::google::protobuf::internal::kEmptyString) {
        md5_->clear();
      }
    }
    crctype_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool OpBlockChecksumResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint32 bytesPerCrc = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &bytespercrc_)));
          set_has_bytespercrc();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_crcPerBlock;
        break;
      }

      // required uint64 crcPerBlock = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_crcPerBlock:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &crcperblock_)));
          set_has_crcperblock();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_md5;
        break;
      }

      // required bytes md5 = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_md5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_md5()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_crcType;
        break;
      }

      // optional .hadoop.hdfs.ChecksumTypeProto crcType = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_crcType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::ChecksumTypeProto_IsValid(value)) {
            set_crctype(static_cast< ::hadoop::hdfs::ChecksumTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(4, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void OpBlockChecksumResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint32 bytesPerCrc = 1;
  if (has_bytespercrc()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->bytespercrc(), output);
  }

  // required uint64 crcPerBlock = 2;
  if (has_crcperblock()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->crcperblock(), output);
  }

  // required bytes md5 = 3;
  if (has_md5()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      3, this->md5(), output);
  }

  // optional .hadoop.hdfs.ChecksumTypeProto crcType = 4;
  if (has_crctype()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->crctype(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* OpBlockChecksumResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint32 bytesPerCrc = 1;
  if (has_bytespercrc()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->bytespercrc(), target);
  }

  // required uint64 crcPerBlock = 2;
  if (has_crcperblock()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->crcperblock(), target);
  }

  // required bytes md5 = 3;
  if (has_md5()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->md5(), target);
  }

  // optional .hadoop.hdfs.ChecksumTypeProto crcType = 4;
  if (has_crctype()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->crctype(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int OpBlockChecksumResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint32 bytesPerCrc = 1;
    if (has_bytespercrc()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->bytespercrc());
    }

    // required uint64 crcPerBlock = 2;
    if (has_crcperblock()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->crcperblock());
    }

    // required bytes md5 = 3;
    if (has_md5()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->md5());
    }

    // optional .hadoop.hdfs.ChecksumTypeProto crcType = 4;
    if (has_crctype()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->crctype());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OpBlockChecksumResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const OpBlockChecksumResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const OpBlockChecksumResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void OpBlockChecksumResponseProto::MergeFrom(const OpBlockChecksumResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_bytespercrc()) {
      set_bytespercrc(from.bytespercrc());
    }
    if (from.has_crcperblock()) {
      set_crcperblock(from.crcperblock());
    }
    if (from.has_md5()) {
      set_md5(from.md5());
    }
    if (from.has_crctype()) {
      set_crctype(from.crctype());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void OpBlockChecksumResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpBlockChecksumResponseProto::CopyFrom(const OpBlockChecksumResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpBlockChecksumResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void OpBlockChecksumResponseProto::Swap(OpBlockChecksumResponseProto* other) {
  if (other != this) {
    std::swap(bytespercrc_, other->bytespercrc_);
    std::swap(crcperblock_, other->crcperblock_);
    std::swap(md5_, other->md5_);
    std::swap(crctype_, other->crctype_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata OpBlockChecksumResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OpBlockChecksumResponseProto_descriptor_;
  metadata.reflection = OpBlockChecksumResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
