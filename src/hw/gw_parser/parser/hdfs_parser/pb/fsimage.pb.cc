// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: fsimage.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "fsimage.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {
namespace fsimage {

namespace {

const ::google::protobuf::Descriptor* FileSummary_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FileSummary_reflection_ = NULL;
const ::google::protobuf::Descriptor* FileSummary_Section_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FileSummary_Section_reflection_ = NULL;
const ::google::protobuf::Descriptor* NameSystemSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NameSystemSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_FileUnderConstructionFeature_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_FileUnderConstructionFeature_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_AclFeatureProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_AclFeatureProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_XAttrCompactProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_XAttrCompactProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_XAttrFeatureProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_XAttrFeatureProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_INodeFile_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_INodeFile_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_INodeDirectory_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_INodeDirectory_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_INodeSymlink_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_INodeSymlink_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeSection_INode_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeSection_INode_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* INodeSection_INode_Type_descriptor_ = NULL;
const ::google::protobuf::Descriptor* FilesUnderConstructionSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FilesUnderConstructionSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* FilesUnderConstructionSection_FileUnderConstructionEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FilesUnderConstructionSection_FileUnderConstructionEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeDirectorySection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeDirectorySection_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeDirectorySection_DirEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeDirectorySection_DirEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeReferenceSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeReferenceSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* INodeReferenceSection_INodeReference_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  INodeReferenceSection_INodeReference_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotSection_Snapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotSection_Snapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotDiffSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotDiffSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotDiffSection_CreatedListEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotDiffSection_CreatedListEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotDiffSection_DirectoryDiff_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotDiffSection_DirectoryDiff_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotDiffSection_FileDiff_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotDiffSection_FileDiff_reflection_ = NULL;
const ::google::protobuf::Descriptor* SnapshotDiffSection_DiffEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SnapshotDiffSection_DiffEntry_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* SnapshotDiffSection_DiffEntry_Type_descriptor_ = NULL;
const ::google::protobuf::Descriptor* StringTableSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StringTableSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* StringTableSection_Entry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StringTableSection_Entry_reflection_ = NULL;
const ::google::protobuf::Descriptor* SecretManagerSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SecretManagerSection_reflection_ = NULL;
const ::google::protobuf::Descriptor* SecretManagerSection_DelegationKey_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SecretManagerSection_DelegationKey_reflection_ = NULL;
const ::google::protobuf::Descriptor* SecretManagerSection_PersistToken_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SecretManagerSection_PersistToken_reflection_ = NULL;
const ::google::protobuf::Descriptor* CacheManagerSection_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CacheManagerSection_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_fsimage_2eproto() {
  protobuf_AddDesc_fsimage_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "fsimage.proto");
  GOOGLE_CHECK(file != NULL);
  FileSummary_descriptor_ = file->message_type(0);
  static const int FileSummary_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary, ondiskversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary, layoutversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary, codec_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary, sections_),
  };
  FileSummary_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FileSummary_descriptor_,
      FileSummary::default_instance_,
      FileSummary_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FileSummary));
  FileSummary_Section_descriptor_ = FileSummary_descriptor_->nested_type(0);
  static const int FileSummary_Section_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary_Section, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary_Section, length_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary_Section, offset_),
  };
  FileSummary_Section_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FileSummary_Section_descriptor_,
      FileSummary_Section::default_instance_,
      FileSummary_Section_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary_Section, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FileSummary_Section, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FileSummary_Section));
  NameSystemSection_descriptor_ = file->message_type(1);
  static const int NameSystemSection_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, namespaceid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, genstampv1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, genstampv2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, genstampv1limit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, lastallocatedblockid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, transactionid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, rollingupgradestarttime_),
  };
  NameSystemSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      NameSystemSection_descriptor_,
      NameSystemSection::default_instance_,
      NameSystemSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NameSystemSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(NameSystemSection));
  INodeSection_descriptor_ = file->message_type(2);
  static const int INodeSection_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection, lastinodeid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection, numinodes_),
  };
  INodeSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_descriptor_,
      INodeSection::default_instance_,
      INodeSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection));
  INodeSection_FileUnderConstructionFeature_descriptor_ = INodeSection_descriptor_->nested_type(0);
  static const int INodeSection_FileUnderConstructionFeature_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_FileUnderConstructionFeature, clientname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_FileUnderConstructionFeature, clientmachine_),
  };
  INodeSection_FileUnderConstructionFeature_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_FileUnderConstructionFeature_descriptor_,
      INodeSection_FileUnderConstructionFeature::default_instance_,
      INodeSection_FileUnderConstructionFeature_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_FileUnderConstructionFeature, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_FileUnderConstructionFeature, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_FileUnderConstructionFeature));
  INodeSection_AclFeatureProto_descriptor_ = INodeSection_descriptor_->nested_type(1);
  static const int INodeSection_AclFeatureProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_AclFeatureProto, entries_),
  };
  INodeSection_AclFeatureProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_AclFeatureProto_descriptor_,
      INodeSection_AclFeatureProto::default_instance_,
      INodeSection_AclFeatureProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_AclFeatureProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_AclFeatureProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_AclFeatureProto));
  INodeSection_XAttrCompactProto_descriptor_ = INodeSection_descriptor_->nested_type(2);
  static const int INodeSection_XAttrCompactProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrCompactProto, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrCompactProto, value_),
  };
  INodeSection_XAttrCompactProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_XAttrCompactProto_descriptor_,
      INodeSection_XAttrCompactProto::default_instance_,
      INodeSection_XAttrCompactProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrCompactProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrCompactProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_XAttrCompactProto));
  INodeSection_XAttrFeatureProto_descriptor_ = INodeSection_descriptor_->nested_type(3);
  static const int INodeSection_XAttrFeatureProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrFeatureProto, xattrs_),
  };
  INodeSection_XAttrFeatureProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_XAttrFeatureProto_descriptor_,
      INodeSection_XAttrFeatureProto::default_instance_,
      INodeSection_XAttrFeatureProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrFeatureProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_XAttrFeatureProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_XAttrFeatureProto));
  INodeSection_INodeFile_descriptor_ = INodeSection_descriptor_->nested_type(4);
  static const int INodeSection_INodeFile_offsets_[10] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, replication_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, modificationtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, accesstime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, preferredblocksize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, permission_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, blocks_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, fileuc_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, acl_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, xattrs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, storagepolicyid_),
  };
  INodeSection_INodeFile_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_INodeFile_descriptor_,
      INodeSection_INodeFile::default_instance_,
      INodeSection_INodeFile_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeFile, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_INodeFile));
  INodeSection_INodeDirectory_descriptor_ = INodeSection_descriptor_->nested_type(5);
  static const int INodeSection_INodeDirectory_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, modificationtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, nsquota_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, dsquota_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, permission_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, acl_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, xattrs_),
  };
  INodeSection_INodeDirectory_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_INodeDirectory_descriptor_,
      INodeSection_INodeDirectory::default_instance_,
      INodeSection_INodeDirectory_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeDirectory, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_INodeDirectory));
  INodeSection_INodeSymlink_descriptor_ = INodeSection_descriptor_->nested_type(6);
  static const int INodeSection_INodeSymlink_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeSymlink, permission_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeSymlink, target_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeSymlink, modificationtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeSymlink, accesstime_),
  };
  INodeSection_INodeSymlink_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_INodeSymlink_descriptor_,
      INodeSection_INodeSymlink::default_instance_,
      INodeSection_INodeSymlink_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeSymlink, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INodeSymlink, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_INodeSymlink));
  INodeSection_INode_descriptor_ = INodeSection_descriptor_->nested_type(7);
  static const int INodeSection_INode_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, file_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, directory_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, symlink_),
  };
  INodeSection_INode_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeSection_INode_descriptor_,
      INodeSection_INode::default_instance_,
      INodeSection_INode_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeSection_INode, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeSection_INode));
  INodeSection_INode_Type_descriptor_ = INodeSection_INode_descriptor_->enum_type(0);
  FilesUnderConstructionSection_descriptor_ = file->message_type(3);
  static const int FilesUnderConstructionSection_offsets_[1] = {
  };
  FilesUnderConstructionSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FilesUnderConstructionSection_descriptor_,
      FilesUnderConstructionSection::default_instance_,
      FilesUnderConstructionSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FilesUnderConstructionSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FilesUnderConstructionSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FilesUnderConstructionSection));
  FilesUnderConstructionSection_FileUnderConstructionEntry_descriptor_ = FilesUnderConstructionSection_descriptor_->nested_type(0);
  static const int FilesUnderConstructionSection_FileUnderConstructionEntry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FilesUnderConstructionSection_FileUnderConstructionEntry, inodeid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FilesUnderConstructionSection_FileUnderConstructionEntry, fullpath_),
  };
  FilesUnderConstructionSection_FileUnderConstructionEntry_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FilesUnderConstructionSection_FileUnderConstructionEntry_descriptor_,
      FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance_,
      FilesUnderConstructionSection_FileUnderConstructionEntry_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FilesUnderConstructionSection_FileUnderConstructionEntry, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FilesUnderConstructionSection_FileUnderConstructionEntry, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FilesUnderConstructionSection_FileUnderConstructionEntry));
  INodeDirectorySection_descriptor_ = file->message_type(4);
  static const int INodeDirectorySection_offsets_[1] = {
  };
  INodeDirectorySection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeDirectorySection_descriptor_,
      INodeDirectorySection::default_instance_,
      INodeDirectorySection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeDirectorySection));
  INodeDirectorySection_DirEntry_descriptor_ = INodeDirectorySection_descriptor_->nested_type(0);
  static const int INodeDirectorySection_DirEntry_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection_DirEntry, parent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection_DirEntry, children_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection_DirEntry, refchildren_),
  };
  INodeDirectorySection_DirEntry_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeDirectorySection_DirEntry_descriptor_,
      INodeDirectorySection_DirEntry::default_instance_,
      INodeDirectorySection_DirEntry_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection_DirEntry, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeDirectorySection_DirEntry, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeDirectorySection_DirEntry));
  INodeReferenceSection_descriptor_ = file->message_type(5);
  static const int INodeReferenceSection_offsets_[1] = {
  };
  INodeReferenceSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeReferenceSection_descriptor_,
      INodeReferenceSection::default_instance_,
      INodeReferenceSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeReferenceSection));
  INodeReferenceSection_INodeReference_descriptor_ = INodeReferenceSection_descriptor_->nested_type(0);
  static const int INodeReferenceSection_INodeReference_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection_INodeReference, referredid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection_INodeReference, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection_INodeReference, dstsnapshotid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection_INodeReference, lastsnapshotid_),
  };
  INodeReferenceSection_INodeReference_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      INodeReferenceSection_INodeReference_descriptor_,
      INodeReferenceSection_INodeReference::default_instance_,
      INodeReferenceSection_INodeReference_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection_INodeReference, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(INodeReferenceSection_INodeReference, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(INodeReferenceSection_INodeReference));
  SnapshotSection_descriptor_ = file->message_type(6);
  static const int SnapshotSection_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection, snapshotcounter_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection, snapshottabledir_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection, numsnapshots_),
  };
  SnapshotSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotSection_descriptor_,
      SnapshotSection::default_instance_,
      SnapshotSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotSection));
  SnapshotSection_Snapshot_descriptor_ = SnapshotSection_descriptor_->nested_type(0);
  static const int SnapshotSection_Snapshot_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection_Snapshot, snapshotid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection_Snapshot, root_),
  };
  SnapshotSection_Snapshot_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotSection_Snapshot_descriptor_,
      SnapshotSection_Snapshot::default_instance_,
      SnapshotSection_Snapshot_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection_Snapshot, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotSection_Snapshot, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotSection_Snapshot));
  SnapshotDiffSection_descriptor_ = file->message_type(7);
  static const int SnapshotDiffSection_offsets_[1] = {
  };
  SnapshotDiffSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotDiffSection_descriptor_,
      SnapshotDiffSection::default_instance_,
      SnapshotDiffSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotDiffSection));
  SnapshotDiffSection_CreatedListEntry_descriptor_ = SnapshotDiffSection_descriptor_->nested_type(0);
  static const int SnapshotDiffSection_CreatedListEntry_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_CreatedListEntry, name_),
  };
  SnapshotDiffSection_CreatedListEntry_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotDiffSection_CreatedListEntry_descriptor_,
      SnapshotDiffSection_CreatedListEntry::default_instance_,
      SnapshotDiffSection_CreatedListEntry_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_CreatedListEntry, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_CreatedListEntry, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotDiffSection_CreatedListEntry));
  SnapshotDiffSection_DirectoryDiff_descriptor_ = SnapshotDiffSection_descriptor_->nested_type(1);
  static const int SnapshotDiffSection_DirectoryDiff_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, snapshotid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, childrensize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, issnapshotroot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, snapshotcopy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, createdlistsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, deletedinode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, deletedinoderef_),
  };
  SnapshotDiffSection_DirectoryDiff_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotDiffSection_DirectoryDiff_descriptor_,
      SnapshotDiffSection_DirectoryDiff::default_instance_,
      SnapshotDiffSection_DirectoryDiff_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DirectoryDiff, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotDiffSection_DirectoryDiff));
  SnapshotDiffSection_FileDiff_descriptor_ = SnapshotDiffSection_descriptor_->nested_type(2);
  static const int SnapshotDiffSection_FileDiff_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_FileDiff, snapshotid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_FileDiff, filesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_FileDiff, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_FileDiff, snapshotcopy_),
  };
  SnapshotDiffSection_FileDiff_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotDiffSection_FileDiff_descriptor_,
      SnapshotDiffSection_FileDiff::default_instance_,
      SnapshotDiffSection_FileDiff_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_FileDiff, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_FileDiff, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotDiffSection_FileDiff));
  SnapshotDiffSection_DiffEntry_descriptor_ = SnapshotDiffSection_descriptor_->nested_type(3);
  static const int SnapshotDiffSection_DiffEntry_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DiffEntry, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DiffEntry, inodeid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DiffEntry, numofdiff_),
  };
  SnapshotDiffSection_DiffEntry_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SnapshotDiffSection_DiffEntry_descriptor_,
      SnapshotDiffSection_DiffEntry::default_instance_,
      SnapshotDiffSection_DiffEntry_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DiffEntry, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SnapshotDiffSection_DiffEntry, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SnapshotDiffSection_DiffEntry));
  SnapshotDiffSection_DiffEntry_Type_descriptor_ = SnapshotDiffSection_DiffEntry_descriptor_->enum_type(0);
  StringTableSection_descriptor_ = file->message_type(8);
  static const int StringTableSection_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection, numentry_),
  };
  StringTableSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      StringTableSection_descriptor_,
      StringTableSection::default_instance_,
      StringTableSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(StringTableSection));
  StringTableSection_Entry_descriptor_ = StringTableSection_descriptor_->nested_type(0);
  static const int StringTableSection_Entry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection_Entry, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection_Entry, str_),
  };
  StringTableSection_Entry_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      StringTableSection_Entry_descriptor_,
      StringTableSection_Entry::default_instance_,
      StringTableSection_Entry_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection_Entry, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringTableSection_Entry, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(StringTableSection_Entry));
  SecretManagerSection_descriptor_ = file->message_type(9);
  static const int SecretManagerSection_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection, currentid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection, tokensequencenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection, numkeys_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection, numtokens_),
  };
  SecretManagerSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SecretManagerSection_descriptor_,
      SecretManagerSection::default_instance_,
      SecretManagerSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SecretManagerSection));
  SecretManagerSection_DelegationKey_descriptor_ = SecretManagerSection_descriptor_->nested_type(0);
  static const int SecretManagerSection_DelegationKey_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_DelegationKey, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_DelegationKey, expirydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_DelegationKey, key_),
  };
  SecretManagerSection_DelegationKey_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SecretManagerSection_DelegationKey_descriptor_,
      SecretManagerSection_DelegationKey::default_instance_,
      SecretManagerSection_DelegationKey_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_DelegationKey, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_DelegationKey, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SecretManagerSection_DelegationKey));
  SecretManagerSection_PersistToken_descriptor_ = SecretManagerSection_descriptor_->nested_type(1);
  static const int SecretManagerSection_PersistToken_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, version_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, owner_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, renewer_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, realuser_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, issuedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, maxdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, sequencenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, masterkeyid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, expirydate_),
  };
  SecretManagerSection_PersistToken_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SecretManagerSection_PersistToken_descriptor_,
      SecretManagerSection_PersistToken::default_instance_,
      SecretManagerSection_PersistToken_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecretManagerSection_PersistToken, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SecretManagerSection_PersistToken));
  CacheManagerSection_descriptor_ = file->message_type(10);
  static const int CacheManagerSection_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CacheManagerSection, nextdirectiveid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CacheManagerSection, numpools_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CacheManagerSection, numdirectives_),
  };
  CacheManagerSection_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CacheManagerSection_descriptor_,
      CacheManagerSection::default_instance_,
      CacheManagerSection_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CacheManagerSection, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CacheManagerSection, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CacheManagerSection));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_fsimage_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FileSummary_descriptor_, &FileSummary::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FileSummary_Section_descriptor_, &FileSummary_Section::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    NameSystemSection_descriptor_, &NameSystemSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_descriptor_, &INodeSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_FileUnderConstructionFeature_descriptor_, &INodeSection_FileUnderConstructionFeature::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_AclFeatureProto_descriptor_, &INodeSection_AclFeatureProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_XAttrCompactProto_descriptor_, &INodeSection_XAttrCompactProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_XAttrFeatureProto_descriptor_, &INodeSection_XAttrFeatureProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_INodeFile_descriptor_, &INodeSection_INodeFile::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_INodeDirectory_descriptor_, &INodeSection_INodeDirectory::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_INodeSymlink_descriptor_, &INodeSection_INodeSymlink::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeSection_INode_descriptor_, &INodeSection_INode::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FilesUnderConstructionSection_descriptor_, &FilesUnderConstructionSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FilesUnderConstructionSection_FileUnderConstructionEntry_descriptor_, &FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeDirectorySection_descriptor_, &INodeDirectorySection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeDirectorySection_DirEntry_descriptor_, &INodeDirectorySection_DirEntry::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeReferenceSection_descriptor_, &INodeReferenceSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    INodeReferenceSection_INodeReference_descriptor_, &INodeReferenceSection_INodeReference::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotSection_descriptor_, &SnapshotSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotSection_Snapshot_descriptor_, &SnapshotSection_Snapshot::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotDiffSection_descriptor_, &SnapshotDiffSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotDiffSection_CreatedListEntry_descriptor_, &SnapshotDiffSection_CreatedListEntry::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotDiffSection_DirectoryDiff_descriptor_, &SnapshotDiffSection_DirectoryDiff::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotDiffSection_FileDiff_descriptor_, &SnapshotDiffSection_FileDiff::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SnapshotDiffSection_DiffEntry_descriptor_, &SnapshotDiffSection_DiffEntry::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    StringTableSection_descriptor_, &StringTableSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    StringTableSection_Entry_descriptor_, &StringTableSection_Entry::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SecretManagerSection_descriptor_, &SecretManagerSection::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SecretManagerSection_DelegationKey_descriptor_, &SecretManagerSection_DelegationKey::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SecretManagerSection_PersistToken_descriptor_, &SecretManagerSection_PersistToken::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CacheManagerSection_descriptor_, &CacheManagerSection::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_fsimage_2eproto() {
  delete FileSummary::default_instance_;
  delete FileSummary_reflection_;
  delete FileSummary_Section::default_instance_;
  delete FileSummary_Section_reflection_;
  delete NameSystemSection::default_instance_;
  delete NameSystemSection_reflection_;
  delete INodeSection::default_instance_;
  delete INodeSection_reflection_;
  delete INodeSection_FileUnderConstructionFeature::default_instance_;
  delete INodeSection_FileUnderConstructionFeature_reflection_;
  delete INodeSection_AclFeatureProto::default_instance_;
  delete INodeSection_AclFeatureProto_reflection_;
  delete INodeSection_XAttrCompactProto::default_instance_;
  delete INodeSection_XAttrCompactProto_reflection_;
  delete INodeSection_XAttrFeatureProto::default_instance_;
  delete INodeSection_XAttrFeatureProto_reflection_;
  delete INodeSection_INodeFile::default_instance_;
  delete INodeSection_INodeFile_reflection_;
  delete INodeSection_INodeDirectory::default_instance_;
  delete INodeSection_INodeDirectory_reflection_;
  delete INodeSection_INodeSymlink::default_instance_;
  delete INodeSection_INodeSymlink_reflection_;
  delete INodeSection_INode::default_instance_;
  delete INodeSection_INode_reflection_;
  delete FilesUnderConstructionSection::default_instance_;
  delete FilesUnderConstructionSection_reflection_;
  delete FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance_;
  delete FilesUnderConstructionSection_FileUnderConstructionEntry_reflection_;
  delete INodeDirectorySection::default_instance_;
  delete INodeDirectorySection_reflection_;
  delete INodeDirectorySection_DirEntry::default_instance_;
  delete INodeDirectorySection_DirEntry_reflection_;
  delete INodeReferenceSection::default_instance_;
  delete INodeReferenceSection_reflection_;
  delete INodeReferenceSection_INodeReference::default_instance_;
  delete INodeReferenceSection_INodeReference_reflection_;
  delete SnapshotSection::default_instance_;
  delete SnapshotSection_reflection_;
  delete SnapshotSection_Snapshot::default_instance_;
  delete SnapshotSection_Snapshot_reflection_;
  delete SnapshotDiffSection::default_instance_;
  delete SnapshotDiffSection_reflection_;
  delete SnapshotDiffSection_CreatedListEntry::default_instance_;
  delete SnapshotDiffSection_CreatedListEntry_reflection_;
  delete SnapshotDiffSection_DirectoryDiff::default_instance_;
  delete SnapshotDiffSection_DirectoryDiff_reflection_;
  delete SnapshotDiffSection_FileDiff::default_instance_;
  delete SnapshotDiffSection_FileDiff_reflection_;
  delete SnapshotDiffSection_DiffEntry::default_instance_;
  delete SnapshotDiffSection_DiffEntry_reflection_;
  delete StringTableSection::default_instance_;
  delete StringTableSection_reflection_;
  delete StringTableSection_Entry::default_instance_;
  delete StringTableSection_Entry_reflection_;
  delete SecretManagerSection::default_instance_;
  delete SecretManagerSection_reflection_;
  delete SecretManagerSection_DelegationKey::default_instance_;
  delete SecretManagerSection_DelegationKey_reflection_;
  delete SecretManagerSection_PersistToken::default_instance_;
  delete SecretManagerSection_PersistToken_reflection_;
  delete CacheManagerSection::default_instance_;
  delete CacheManagerSection_reflection_;
}

void protobuf_AddDesc_fsimage_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::hadoop::hdfs::protobuf_AddDesc_acl_2eproto();
  ::hadoop::hdfs::protobuf_AddDesc_xattr_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rfsimage.proto\022\023hadoop.hdfs.fsimage\032\nhd"
    "fs.proto\032\tacl.proto\032\013xattr.proto\"\277\001\n\013Fil"
    "eSummary\022\025\n\rondiskVersion\030\001 \002(\r\022\025\n\rlayou"
    "tVersion\030\002 \002(\r\022\r\n\005codec\030\003 \001(\t\022:\n\010section"
    "s\030\004 \003(\0132(.hadoop.hdfs.fsimage.FileSummar"
    "y.Section\0327\n\007Section\022\014\n\004name\030\001 \001(\t\022\016\n\006le"
    "ngth\030\002 \001(\004\022\016\n\006offset\030\003 \001(\004\"\277\001\n\021NameSyste"
    "mSection\022\023\n\013namespaceId\030\001 \001(\r\022\022\n\ngenstam"
    "pV1\030\002 \001(\004\022\022\n\ngenstampV2\030\003 \001(\004\022\027\n\017genstam"
    "pV1Limit\030\004 \001(\004\022\034\n\024lastAllocatedBlockId\030\005"
    " \001(\004\022\025\n\rtransactionId\030\006 \001(\004\022\037\n\027rollingUp"
    "gradeStartTime\030\007 \001(\004\"\346\n\n\014INodeSection\022\023\n"
    "\013lastInodeId\030\001 \001(\004\022\021\n\tnumInodes\030\002 \001(\004\032I\n"
    "\034FileUnderConstructionFeature\022\022\n\nclientN"
    "ame\030\001 \001(\t\022\025\n\rclientMachine\030\002 \001(\t\032&\n\017AclF"
    "eatureProto\022\023\n\007entries\030\002 \003(\007B\002\020\001\0320\n\021XAtt"
    "rCompactProto\022\014\n\004name\030\001 \002(\007\022\r\n\005value\030\002 \001"
    "(\014\032X\n\021XAttrFeatureProto\022C\n\006xAttrs\030\001 \003(\0132"
    "3.hadoop.hdfs.fsimage.INodeSection.XAttr"
    "CompactProto\032\225\003\n\tINodeFile\022\023\n\013replicatio"
    "n\030\001 \001(\r\022\030\n\020modificationTime\030\002 \001(\004\022\022\n\nacc"
    "essTime\030\003 \001(\004\022\032\n\022preferredBlockSize\030\004 \001("
    "\004\022\022\n\npermission\030\005 \001(\006\022\'\n\006blocks\030\006 \003(\0132\027."
    "hadoop.hdfs.BlockProto\022N\n\006fileUC\030\007 \001(\0132>"
    ".hadoop.hdfs.fsimage.INodeSection.FileUn"
    "derConstructionFeature\022>\n\003acl\030\010 \001(\01321.ha"
    "doop.hdfs.fsimage.INodeSection.AclFeatur"
    "eProto\022C\n\006xAttrs\030\t \001(\01323.hadoop.hdfs.fsi"
    "mage.INodeSection.XAttrFeatureProto\022\027\n\017s"
    "toragePolicyID\030\n \001(\r\032\345\001\n\016INodeDirectory\022"
    "\030\n\020modificationTime\030\001 \001(\004\022\017\n\007nsQuota\030\002 \001"
    "(\004\022\017\n\007dsQuota\030\003 \001(\004\022\022\n\npermission\030\004 \001(\006\022"
    ">\n\003acl\030\005 \001(\01321.hadoop.hdfs.fsimage.INode"
    "Section.AclFeatureProto\022C\n\006xAttrs\030\006 \001(\0132"
    "3.hadoop.hdfs.fsimage.INodeSection.XAttr"
    "FeatureProto\032`\n\014INodeSymlink\022\022\n\npermissi"
    "on\030\001 \001(\006\022\016\n\006target\030\002 \001(\014\022\030\n\020modification"
    "Time\030\003 \001(\004\022\022\n\naccessTime\030\004 \001(\004\032\314\002\n\005INode"
    "\022:\n\004type\030\001 \002(\0162,.hadoop.hdfs.fsimage.INo"
    "deSection.INode.Type\022\n\n\002id\030\002 \002(\004\022\014\n\004name"
    "\030\003 \001(\014\0229\n\004file\030\004 \001(\0132+.hadoop.hdfs.fsima"
    "ge.INodeSection.INodeFile\022C\n\tdirectory\030\005"
    " \001(\01320.hadoop.hdfs.fsimage.INodeSection."
    "INodeDirectory\022\?\n\007symlink\030\006 \001(\0132..hadoop"
    ".hdfs.fsimage.INodeSection.INodeSymlink\""
    ",\n\004Type\022\010\n\004FILE\020\001\022\r\n\tDIRECTORY\020\002\022\013\n\007SYML"
    "INK\020\003\"`\n\035FilesUnderConstructionSection\032\?"
    "\n\032FileUnderConstructionEntry\022\017\n\007inodeId\030"
    "\001 \001(\004\022\020\n\010fullPath\030\002 \001(\t\"b\n\025INodeDirector"
    "ySection\032I\n\010DirEntry\022\016\n\006parent\030\001 \001(\004\022\024\n\010"
    "children\030\002 \003(\004B\002\020\001\022\027\n\013refChildren\030\003 \003(\rB"
    "\002\020\001\"z\n\025INodeReferenceSection\032a\n\016INodeRef"
    "erence\022\022\n\nreferredId\030\001 \001(\004\022\014\n\004name\030\002 \001(\014"
    "\022\025\n\rdstSnapshotId\030\003 \001(\r\022\026\n\016lastSnapshotI"
    "d\030\004 \001(\r\"\265\001\n\017SnapshotSection\022\027\n\017snapshotC"
    "ounter\030\001 \001(\r\022\034\n\020snapshottableDir\030\002 \003(\004B\002"
    "\020\001\022\024\n\014numSnapshots\030\003 \001(\r\032U\n\010Snapshot\022\022\n\n"
    "snapshotId\030\001 \001(\r\0225\n\004root\030\002 \001(\0132\'.hadoop."
    "hdfs.fsimage.INodeSection.INode\"\327\004\n\023Snap"
    "shotDiffSection\032 \n\020CreatedListEntry\022\014\n\004n"
    "ame\030\001 \001(\014\032\367\001\n\rDirectoryDiff\022\022\n\nsnapshotI"
    "d\030\001 \001(\r\022\024\n\014childrenSize\030\002 \001(\r\022\026\n\016isSnaps"
    "hotRoot\030\003 \001(\010\022\014\n\004name\030\004 \001(\014\022F\n\014snapshotC"
    "opy\030\005 \001(\01320.hadoop.hdfs.fsimage.INodeSec"
    "tion.INodeDirectory\022\027\n\017createdListSize\030\006"
    " \001(\r\022\030\n\014deletedINode\030\007 \003(\004B\002\020\001\022\033\n\017delete"
    "dINodeRef\030\010 \003(\rB\002\020\001\032\201\001\n\010FileDiff\022\022\n\nsnap"
    "shotId\030\001 \001(\r\022\020\n\010fileSize\030\002 \001(\004\022\014\n\004name\030\003"
    " \001(\014\022A\n\014snapshotCopy\030\004 \001(\0132+.hadoop.hdfs"
    ".fsimage.INodeSection.INodeFile\032\237\001\n\tDiff"
    "Entry\022E\n\004type\030\001 \002(\01627.hadoop.hdfs.fsimag"
    "e.SnapshotDiffSection.DiffEntry.Type\022\017\n\007"
    "inodeId\030\002 \001(\004\022\021\n\tnumOfDiff\030\003 \001(\r\"\'\n\004Type"
    "\022\014\n\010FILEDIFF\020\001\022\021\n\rDIRECTORYDIFF\020\002\"H\n\022Str"
    "ingTableSection\022\020\n\010numEntry\030\001 \001(\r\032 \n\005Ent"
    "ry\022\n\n\002id\030\001 \001(\r\022\013\n\003str\030\002 \001(\t\"\341\002\n\024SecretMa"
    "nagerSection\022\021\n\tcurrentId\030\001 \001(\r\022\033\n\023token"
    "SequenceNumber\030\002 \001(\r\022\017\n\007numKeys\030\003 \001(\r\022\021\n"
    "\tnumTokens\030\004 \001(\r\032<\n\rDelegationKey\022\n\n\002id\030"
    "\001 \001(\r\022\022\n\nexpiryDate\030\002 \001(\004\022\013\n\003key\030\003 \001(\014\032\266"
    "\001\n\014PersistToken\022\017\n\007version\030\001 \001(\r\022\r\n\005owne"
    "r\030\002 \001(\t\022\017\n\007renewer\030\003 \001(\t\022\020\n\010realUser\030\004 \001"
    "(\t\022\021\n\tissueDate\030\005 \001(\004\022\017\n\007maxDate\030\006 \001(\004\022\026"
    "\n\016sequenceNumber\030\007 \001(\r\022\023\n\013masterKeyId\030\010 "
    "\001(\r\022\022\n\nexpiryDate\030\t \001(\004\"W\n\023CacheManagerS"
    "ection\022\027\n\017nextDirectiveId\030\001 \002(\004\022\020\n\010numPo"
    "ols\030\002 \002(\r\022\025\n\rnumDirectives\030\003 \002(\rB6\n&org."
    "apache.hadoop.hdfs.server.namenodeB\014FsIm"
    "ageProto", 3528);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "fsimage.proto", &protobuf_RegisterTypes);
  FileSummary::default_instance_ = new FileSummary();
  FileSummary_Section::default_instance_ = new FileSummary_Section();
  NameSystemSection::default_instance_ = new NameSystemSection();
  INodeSection::default_instance_ = new INodeSection();
  INodeSection_FileUnderConstructionFeature::default_instance_ = new INodeSection_FileUnderConstructionFeature();
  INodeSection_AclFeatureProto::default_instance_ = new INodeSection_AclFeatureProto();
  INodeSection_XAttrCompactProto::default_instance_ = new INodeSection_XAttrCompactProto();
  INodeSection_XAttrFeatureProto::default_instance_ = new INodeSection_XAttrFeatureProto();
  INodeSection_INodeFile::default_instance_ = new INodeSection_INodeFile();
  INodeSection_INodeDirectory::default_instance_ = new INodeSection_INodeDirectory();
  INodeSection_INodeSymlink::default_instance_ = new INodeSection_INodeSymlink();
  INodeSection_INode::default_instance_ = new INodeSection_INode();
  FilesUnderConstructionSection::default_instance_ = new FilesUnderConstructionSection();
  FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance_ = new FilesUnderConstructionSection_FileUnderConstructionEntry();
  INodeDirectorySection::default_instance_ = new INodeDirectorySection();
  INodeDirectorySection_DirEntry::default_instance_ = new INodeDirectorySection_DirEntry();
  INodeReferenceSection::default_instance_ = new INodeReferenceSection();
  INodeReferenceSection_INodeReference::default_instance_ = new INodeReferenceSection_INodeReference();
  SnapshotSection::default_instance_ = new SnapshotSection();
  SnapshotSection_Snapshot::default_instance_ = new SnapshotSection_Snapshot();
  SnapshotDiffSection::default_instance_ = new SnapshotDiffSection();
  SnapshotDiffSection_CreatedListEntry::default_instance_ = new SnapshotDiffSection_CreatedListEntry();
  SnapshotDiffSection_DirectoryDiff::default_instance_ = new SnapshotDiffSection_DirectoryDiff();
  SnapshotDiffSection_FileDiff::default_instance_ = new SnapshotDiffSection_FileDiff();
  SnapshotDiffSection_DiffEntry::default_instance_ = new SnapshotDiffSection_DiffEntry();
  StringTableSection::default_instance_ = new StringTableSection();
  StringTableSection_Entry::default_instance_ = new StringTableSection_Entry();
  SecretManagerSection::default_instance_ = new SecretManagerSection();
  SecretManagerSection_DelegationKey::default_instance_ = new SecretManagerSection_DelegationKey();
  SecretManagerSection_PersistToken::default_instance_ = new SecretManagerSection_PersistToken();
  CacheManagerSection::default_instance_ = new CacheManagerSection();
  FileSummary::default_instance_->InitAsDefaultInstance();
  FileSummary_Section::default_instance_->InitAsDefaultInstance();
  NameSystemSection::default_instance_->InitAsDefaultInstance();
  INodeSection::default_instance_->InitAsDefaultInstance();
  INodeSection_FileUnderConstructionFeature::default_instance_->InitAsDefaultInstance();
  INodeSection_AclFeatureProto::default_instance_->InitAsDefaultInstance();
  INodeSection_XAttrCompactProto::default_instance_->InitAsDefaultInstance();
  INodeSection_XAttrFeatureProto::default_instance_->InitAsDefaultInstance();
  INodeSection_INodeFile::default_instance_->InitAsDefaultInstance();
  INodeSection_INodeDirectory::default_instance_->InitAsDefaultInstance();
  INodeSection_INodeSymlink::default_instance_->InitAsDefaultInstance();
  INodeSection_INode::default_instance_->InitAsDefaultInstance();
  FilesUnderConstructionSection::default_instance_->InitAsDefaultInstance();
  FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance_->InitAsDefaultInstance();
  INodeDirectorySection::default_instance_->InitAsDefaultInstance();
  INodeDirectorySection_DirEntry::default_instance_->InitAsDefaultInstance();
  INodeReferenceSection::default_instance_->InitAsDefaultInstance();
  INodeReferenceSection_INodeReference::default_instance_->InitAsDefaultInstance();
  SnapshotSection::default_instance_->InitAsDefaultInstance();
  SnapshotSection_Snapshot::default_instance_->InitAsDefaultInstance();
  SnapshotDiffSection::default_instance_->InitAsDefaultInstance();
  SnapshotDiffSection_CreatedListEntry::default_instance_->InitAsDefaultInstance();
  SnapshotDiffSection_DirectoryDiff::default_instance_->InitAsDefaultInstance();
  SnapshotDiffSection_FileDiff::default_instance_->InitAsDefaultInstance();
  SnapshotDiffSection_DiffEntry::default_instance_->InitAsDefaultInstance();
  StringTableSection::default_instance_->InitAsDefaultInstance();
  StringTableSection_Entry::default_instance_->InitAsDefaultInstance();
  SecretManagerSection::default_instance_->InitAsDefaultInstance();
  SecretManagerSection_DelegationKey::default_instance_->InitAsDefaultInstance();
  SecretManagerSection_PersistToken::default_instance_->InitAsDefaultInstance();
  CacheManagerSection::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_fsimage_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_fsimage_2eproto {
  StaticDescriptorInitializer_fsimage_2eproto() {
    protobuf_AddDesc_fsimage_2eproto();
  }
} static_descriptor_initializer_fsimage_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int FileSummary_Section::kNameFieldNumber;
const int FileSummary_Section::kLengthFieldNumber;
const int FileSummary_Section::kOffsetFieldNumber;
#endif  // !_MSC_VER

FileSummary_Section::FileSummary_Section()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FileSummary_Section::InitAsDefaultInstance() {
}

FileSummary_Section::FileSummary_Section(const FileSummary_Section& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FileSummary_Section::SharedCtor() {
  _cached_size_ = 0;
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  length_ = GOOGLE_ULONGLONG(0);
  offset_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FileSummary_Section::~FileSummary_Section() {
  SharedDtor();
}

void FileSummary_Section::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
  }
}

void FileSummary_Section::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FileSummary_Section::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FileSummary_Section_descriptor_;
}

const FileSummary_Section& FileSummary_Section::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

FileSummary_Section* FileSummary_Section::default_instance_ = NULL;

FileSummary_Section* FileSummary_Section::New() const {
  return new FileSummary_Section;
}

void FileSummary_Section::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
    length_ = GOOGLE_ULONGLONG(0);
    offset_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FileSummary_Section::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string name = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->name().data(), this->name().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_length;
        break;
      }

      // optional uint64 length = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_length:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &length_)));
          set_has_length();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_offset;
        break;
      }

      // optional uint64 offset = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_offset:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &offset_)));
          set_has_offset();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void FileSummary_Section::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional string name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->name(), output);
  }

  // optional uint64 length = 2;
  if (has_length()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->length(), output);
  }

  // optional uint64 offset = 3;
  if (has_offset()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->offset(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FileSummary_Section::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional string name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // optional uint64 length = 2;
  if (has_length()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->length(), target);
  }

  // optional uint64 offset = 3;
  if (has_offset()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->offset(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FileSummary_Section::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional string name = 1;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->name());
    }

    // optional uint64 length = 2;
    if (has_length()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->length());
    }

    // optional uint64 offset = 3;
    if (has_offset()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->offset());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FileSummary_Section::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FileSummary_Section* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FileSummary_Section*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FileSummary_Section::MergeFrom(const FileSummary_Section& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_length()) {
      set_length(from.length());
    }
    if (from.has_offset()) {
      set_offset(from.offset());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FileSummary_Section::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FileSummary_Section::CopyFrom(const FileSummary_Section& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FileSummary_Section::IsInitialized() const {

  return true;
}

void FileSummary_Section::Swap(FileSummary_Section* other) {
  if (other != this) {
    std::swap(name_, other->name_);
    std::swap(length_, other->length_);
    std::swap(offset_, other->offset_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FileSummary_Section::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FileSummary_Section_descriptor_;
  metadata.reflection = FileSummary_Section_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int FileSummary::kOndiskVersionFieldNumber;
const int FileSummary::kLayoutVersionFieldNumber;
const int FileSummary::kCodecFieldNumber;
const int FileSummary::kSectionsFieldNumber;
#endif  // !_MSC_VER

FileSummary::FileSummary()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FileSummary::InitAsDefaultInstance() {
}

FileSummary::FileSummary(const FileSummary& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FileSummary::SharedCtor() {
  _cached_size_ = 0;
  ondiskversion_ = 0u;
  layoutversion_ = 0u;
  codec_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FileSummary::~FileSummary() {
  SharedDtor();
}

void FileSummary::SharedDtor() {
  if (codec_ != &::google::protobuf::internal::kEmptyString) {
    delete codec_;
  }
  if (this != default_instance_) {
  }
}

void FileSummary::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FileSummary::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FileSummary_descriptor_;
}

const FileSummary& FileSummary::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

FileSummary* FileSummary::default_instance_ = NULL;

FileSummary* FileSummary::New() const {
  return new FileSummary;
}

void FileSummary::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    ondiskversion_ = 0u;
    layoutversion_ = 0u;
    if (has_codec()) {
      if (codec_ != &::google::protobuf::internal::kEmptyString) {
        codec_->clear();
      }
    }
  }
  sections_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FileSummary::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint32 ondiskVersion = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &ondiskversion_)));
          set_has_ondiskversion();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_layoutVersion;
        break;
      }

      // required uint32 layoutVersion = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_layoutVersion:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &layoutversion_)));
          set_has_layoutversion();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_codec;
        break;
      }

      // optional string codec = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_codec:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_codec()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->codec().data(), this->codec().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_sections;
        break;
      }

      // repeated .hadoop.hdfs.fsimage.FileSummary.Section sections = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_sections:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_sections()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_sections;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void FileSummary::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint32 ondiskVersion = 1;
  if (has_ondiskversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->ondiskversion(), output);
  }

  // required uint32 layoutVersion = 2;
  if (has_layoutversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->layoutversion(), output);
  }

  // optional string codec = 3;
  if (has_codec()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->codec().data(), this->codec().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->codec(), output);
  }

  // repeated .hadoop.hdfs.fsimage.FileSummary.Section sections = 4;
  for (int i = 0; i < this->sections_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->sections(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FileSummary::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint32 ondiskVersion = 1;
  if (has_ondiskversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->ondiskversion(), target);
  }

  // required uint32 layoutVersion = 2;
  if (has_layoutversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->layoutversion(), target);
  }

  // optional string codec = 3;
  if (has_codec()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->codec().data(), this->codec().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->codec(), target);
  }

  // repeated .hadoop.hdfs.fsimage.FileSummary.Section sections = 4;
  for (int i = 0; i < this->sections_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->sections(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FileSummary::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint32 ondiskVersion = 1;
    if (has_ondiskversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->ondiskversion());
    }

    // required uint32 layoutVersion = 2;
    if (has_layoutversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->layoutversion());
    }

    // optional string codec = 3;
    if (has_codec()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->codec());
    }

  }
  // repeated .hadoop.hdfs.fsimage.FileSummary.Section sections = 4;
  total_size += 1 * this->sections_size();
  for (int i = 0; i < this->sections_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->sections(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FileSummary::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FileSummary* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FileSummary*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FileSummary::MergeFrom(const FileSummary& from) {
  GOOGLE_CHECK_NE(&from, this);
  sections_.MergeFrom(from.sections_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_ondiskversion()) {
      set_ondiskversion(from.ondiskversion());
    }
    if (from.has_layoutversion()) {
      set_layoutversion(from.layoutversion());
    }
    if (from.has_codec()) {
      set_codec(from.codec());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FileSummary::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FileSummary::CopyFrom(const FileSummary& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FileSummary::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void FileSummary::Swap(FileSummary* other) {
  if (other != this) {
    std::swap(ondiskversion_, other->ondiskversion_);
    std::swap(layoutversion_, other->layoutversion_);
    std::swap(codec_, other->codec_);
    sections_.Swap(&other->sections_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FileSummary::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FileSummary_descriptor_;
  metadata.reflection = FileSummary_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int NameSystemSection::kNamespaceIdFieldNumber;
const int NameSystemSection::kGenstampV1FieldNumber;
const int NameSystemSection::kGenstampV2FieldNumber;
const int NameSystemSection::kGenstampV1LimitFieldNumber;
const int NameSystemSection::kLastAllocatedBlockIdFieldNumber;
const int NameSystemSection::kTransactionIdFieldNumber;
const int NameSystemSection::kRollingUpgradeStartTimeFieldNumber;
#endif  // !_MSC_VER

NameSystemSection::NameSystemSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void NameSystemSection::InitAsDefaultInstance() {
}

NameSystemSection::NameSystemSection(const NameSystemSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void NameSystemSection::SharedCtor() {
  _cached_size_ = 0;
  namespaceid_ = 0u;
  genstampv1_ = GOOGLE_ULONGLONG(0);
  genstampv2_ = GOOGLE_ULONGLONG(0);
  genstampv1limit_ = GOOGLE_ULONGLONG(0);
  lastallocatedblockid_ = GOOGLE_ULONGLONG(0);
  transactionid_ = GOOGLE_ULONGLONG(0);
  rollingupgradestarttime_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

NameSystemSection::~NameSystemSection() {
  SharedDtor();
}

void NameSystemSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void NameSystemSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NameSystemSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NameSystemSection_descriptor_;
}

const NameSystemSection& NameSystemSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

NameSystemSection* NameSystemSection::default_instance_ = NULL;

NameSystemSection* NameSystemSection::New() const {
  return new NameSystemSection;
}

void NameSystemSection::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    namespaceid_ = 0u;
    genstampv1_ = GOOGLE_ULONGLONG(0);
    genstampv2_ = GOOGLE_ULONGLONG(0);
    genstampv1limit_ = GOOGLE_ULONGLONG(0);
    lastallocatedblockid_ = GOOGLE_ULONGLONG(0);
    transactionid_ = GOOGLE_ULONGLONG(0);
    rollingupgradestarttime_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool NameSystemSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 namespaceId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &namespaceid_)));
          set_has_namespaceid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_genstampV1;
        break;
      }

      // optional uint64 genstampV1 = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_genstampV1:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &genstampv1_)));
          set_has_genstampv1();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_genstampV2;
        break;
      }

      // optional uint64 genstampV2 = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_genstampV2:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &genstampv2_)));
          set_has_genstampv2();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_genstampV1Limit;
        break;
      }

      // optional uint64 genstampV1Limit = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_genstampV1Limit:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &genstampv1limit_)));
          set_has_genstampv1limit();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_lastAllocatedBlockId;
        break;
      }

      // optional uint64 lastAllocatedBlockId = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lastAllocatedBlockId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &lastallocatedblockid_)));
          set_has_lastallocatedblockid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(48)) goto parse_transactionId;
        break;
      }

      // optional uint64 transactionId = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_transactionId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &transactionid_)));
          set_has_transactionid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(56)) goto parse_rollingUpgradeStartTime;
        break;
      }

      // optional uint64 rollingUpgradeStartTime = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_rollingUpgradeStartTime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &rollingupgradestarttime_)));
          set_has_rollingupgradestarttime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void NameSystemSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 namespaceId = 1;
  if (has_namespaceid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->namespaceid(), output);
  }

  // optional uint64 genstampV1 = 2;
  if (has_genstampv1()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->genstampv1(), output);
  }

  // optional uint64 genstampV2 = 3;
  if (has_genstampv2()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->genstampv2(), output);
  }

  // optional uint64 genstampV1Limit = 4;
  if (has_genstampv1limit()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->genstampv1limit(), output);
  }

  // optional uint64 lastAllocatedBlockId = 5;
  if (has_lastallocatedblockid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(5, this->lastallocatedblockid(), output);
  }

  // optional uint64 transactionId = 6;
  if (has_transactionid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(6, this->transactionid(), output);
  }

  // optional uint64 rollingUpgradeStartTime = 7;
  if (has_rollingupgradestarttime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(7, this->rollingupgradestarttime(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* NameSystemSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 namespaceId = 1;
  if (has_namespaceid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->namespaceid(), target);
  }

  // optional uint64 genstampV1 = 2;
  if (has_genstampv1()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->genstampv1(), target);
  }

  // optional uint64 genstampV2 = 3;
  if (has_genstampv2()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->genstampv2(), target);
  }

  // optional uint64 genstampV1Limit = 4;
  if (has_genstampv1limit()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->genstampv1limit(), target);
  }

  // optional uint64 lastAllocatedBlockId = 5;
  if (has_lastallocatedblockid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(5, this->lastallocatedblockid(), target);
  }

  // optional uint64 transactionId = 6;
  if (has_transactionid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(6, this->transactionid(), target);
  }

  // optional uint64 rollingUpgradeStartTime = 7;
  if (has_rollingupgradestarttime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(7, this->rollingupgradestarttime(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int NameSystemSection::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 namespaceId = 1;
    if (has_namespaceid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->namespaceid());
    }

    // optional uint64 genstampV1 = 2;
    if (has_genstampv1()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->genstampv1());
    }

    // optional uint64 genstampV2 = 3;
    if (has_genstampv2()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->genstampv2());
    }

    // optional uint64 genstampV1Limit = 4;
    if (has_genstampv1limit()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->genstampv1limit());
    }

    // optional uint64 lastAllocatedBlockId = 5;
    if (has_lastallocatedblockid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->lastallocatedblockid());
    }

    // optional uint64 transactionId = 6;
    if (has_transactionid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->transactionid());
    }

    // optional uint64 rollingUpgradeStartTime = 7;
    if (has_rollingupgradestarttime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->rollingupgradestarttime());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NameSystemSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const NameSystemSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const NameSystemSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void NameSystemSection::MergeFrom(const NameSystemSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_namespaceid()) {
      set_namespaceid(from.namespaceid());
    }
    if (from.has_genstampv1()) {
      set_genstampv1(from.genstampv1());
    }
    if (from.has_genstampv2()) {
      set_genstampv2(from.genstampv2());
    }
    if (from.has_genstampv1limit()) {
      set_genstampv1limit(from.genstampv1limit());
    }
    if (from.has_lastallocatedblockid()) {
      set_lastallocatedblockid(from.lastallocatedblockid());
    }
    if (from.has_transactionid()) {
      set_transactionid(from.transactionid());
    }
    if (from.has_rollingupgradestarttime()) {
      set_rollingupgradestarttime(from.rollingupgradestarttime());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void NameSystemSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NameSystemSection::CopyFrom(const NameSystemSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NameSystemSection::IsInitialized() const {

  return true;
}

void NameSystemSection::Swap(NameSystemSection* other) {
  if (other != this) {
    std::swap(namespaceid_, other->namespaceid_);
    std::swap(genstampv1_, other->genstampv1_);
    std::swap(genstampv2_, other->genstampv2_);
    std::swap(genstampv1limit_, other->genstampv1limit_);
    std::swap(lastallocatedblockid_, other->lastallocatedblockid_);
    std::swap(transactionid_, other->transactionid_);
    std::swap(rollingupgradestarttime_, other->rollingupgradestarttime_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata NameSystemSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NameSystemSection_descriptor_;
  metadata.reflection = NameSystemSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int INodeSection_FileUnderConstructionFeature::kClientNameFieldNumber;
const int INodeSection_FileUnderConstructionFeature::kClientMachineFieldNumber;
#endif  // !_MSC_VER

INodeSection_FileUnderConstructionFeature::INodeSection_FileUnderConstructionFeature()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_FileUnderConstructionFeature::InitAsDefaultInstance() {
}

INodeSection_FileUnderConstructionFeature::INodeSection_FileUnderConstructionFeature(const INodeSection_FileUnderConstructionFeature& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_FileUnderConstructionFeature::SharedCtor() {
  _cached_size_ = 0;
  clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  clientmachine_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_FileUnderConstructionFeature::~INodeSection_FileUnderConstructionFeature() {
  SharedDtor();
}

void INodeSection_FileUnderConstructionFeature::SharedDtor() {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    delete clientname_;
  }
  if (clientmachine_ != &::google::protobuf::internal::kEmptyString) {
    delete clientmachine_;
  }
  if (this != default_instance_) {
  }
}

void INodeSection_FileUnderConstructionFeature::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_FileUnderConstructionFeature::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_FileUnderConstructionFeature_descriptor_;
}

const INodeSection_FileUnderConstructionFeature& INodeSection_FileUnderConstructionFeature::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_FileUnderConstructionFeature* INodeSection_FileUnderConstructionFeature::default_instance_ = NULL;

INodeSection_FileUnderConstructionFeature* INodeSection_FileUnderConstructionFeature::New() const {
  return new INodeSection_FileUnderConstructionFeature;
}

void INodeSection_FileUnderConstructionFeature::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_clientname()) {
      if (clientname_ != &::google::protobuf::internal::kEmptyString) {
        clientname_->clear();
      }
    }
    if (has_clientmachine()) {
      if (clientmachine_ != &::google::protobuf::internal::kEmptyString) {
        clientmachine_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_FileUnderConstructionFeature::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string clientName = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_clientname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->clientname().data(), this->clientname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_clientMachine;
        break;
      }

      // optional string clientMachine = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_clientMachine:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_clientmachine()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->clientmachine().data(), this->clientmachine().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_FileUnderConstructionFeature::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional string clientName = 1;
  if (has_clientname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientname().data(), this->clientname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->clientname(), output);
  }

  // optional string clientMachine = 2;
  if (has_clientmachine()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientmachine().data(), this->clientmachine().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->clientmachine(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_FileUnderConstructionFeature::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional string clientName = 1;
  if (has_clientname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientname().data(), this->clientname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->clientname(), target);
  }

  // optional string clientMachine = 2;
  if (has_clientmachine()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->clientmachine().data(), this->clientmachine().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->clientmachine(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_FileUnderConstructionFeature::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional string clientName = 1;
    if (has_clientname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->clientname());
    }

    // optional string clientMachine = 2;
    if (has_clientmachine()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->clientmachine());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_FileUnderConstructionFeature::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_FileUnderConstructionFeature* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_FileUnderConstructionFeature*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_FileUnderConstructionFeature::MergeFrom(const INodeSection_FileUnderConstructionFeature& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_clientname()) {
      set_clientname(from.clientname());
    }
    if (from.has_clientmachine()) {
      set_clientmachine(from.clientmachine());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_FileUnderConstructionFeature::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_FileUnderConstructionFeature::CopyFrom(const INodeSection_FileUnderConstructionFeature& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_FileUnderConstructionFeature::IsInitialized() const {

  return true;
}

void INodeSection_FileUnderConstructionFeature::Swap(INodeSection_FileUnderConstructionFeature* other) {
  if (other != this) {
    std::swap(clientname_, other->clientname_);
    std::swap(clientmachine_, other->clientmachine_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_FileUnderConstructionFeature::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_FileUnderConstructionFeature_descriptor_;
  metadata.reflection = INodeSection_FileUnderConstructionFeature_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection_AclFeatureProto::kEntriesFieldNumber;
#endif  // !_MSC_VER

INodeSection_AclFeatureProto::INodeSection_AclFeatureProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_AclFeatureProto::InitAsDefaultInstance() {
}

INodeSection_AclFeatureProto::INodeSection_AclFeatureProto(const INodeSection_AclFeatureProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_AclFeatureProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_AclFeatureProto::~INodeSection_AclFeatureProto() {
  SharedDtor();
}

void INodeSection_AclFeatureProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void INodeSection_AclFeatureProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_AclFeatureProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_AclFeatureProto_descriptor_;
}

const INodeSection_AclFeatureProto& INodeSection_AclFeatureProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_AclFeatureProto* INodeSection_AclFeatureProto::default_instance_ = NULL;

INodeSection_AclFeatureProto* INodeSection_AclFeatureProto::New() const {
  return new INodeSection_AclFeatureProto;
}

void INodeSection_AclFeatureProto::Clear() {
  entries_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_AclFeatureProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated fixed32 entries = 2 [packed = true];
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_entries())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_FIXED32) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 1, 18, input, this->mutable_entries())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_AclFeatureProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated fixed32 entries = 2 [packed = true];
  if (this->entries_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_entries_cached_byte_size_);
  }
  for (int i = 0; i < this->entries_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32NoTag(
      this->entries(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_AclFeatureProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated fixed32 entries = 2 [packed = true];
  if (this->entries_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _entries_cached_byte_size_, target);
  }
  for (int i = 0; i < this->entries_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed32NoTagToArray(this->entries(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_AclFeatureProto::ByteSize() const {
  int total_size = 0;

  // repeated fixed32 entries = 2 [packed = true];
  {
    int data_size = 0;
    data_size = 4 * this->entries_size();
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _entries_cached_byte_size_ = data_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_AclFeatureProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_AclFeatureProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_AclFeatureProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_AclFeatureProto::MergeFrom(const INodeSection_AclFeatureProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  entries_.MergeFrom(from.entries_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_AclFeatureProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_AclFeatureProto::CopyFrom(const INodeSection_AclFeatureProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_AclFeatureProto::IsInitialized() const {

  return true;
}

void INodeSection_AclFeatureProto::Swap(INodeSection_AclFeatureProto* other) {
  if (other != this) {
    entries_.Swap(&other->entries_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_AclFeatureProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_AclFeatureProto_descriptor_;
  metadata.reflection = INodeSection_AclFeatureProto_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection_XAttrCompactProto::kNameFieldNumber;
const int INodeSection_XAttrCompactProto::kValueFieldNumber;
#endif  // !_MSC_VER

INodeSection_XAttrCompactProto::INodeSection_XAttrCompactProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_XAttrCompactProto::InitAsDefaultInstance() {
}

INodeSection_XAttrCompactProto::INodeSection_XAttrCompactProto(const INodeSection_XAttrCompactProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_XAttrCompactProto::SharedCtor() {
  _cached_size_ = 0;
  name_ = 0u;
  value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_XAttrCompactProto::~INodeSection_XAttrCompactProto() {
  SharedDtor();
}

void INodeSection_XAttrCompactProto::SharedDtor() {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    delete value_;
  }
  if (this != default_instance_) {
  }
}

void INodeSection_XAttrCompactProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_XAttrCompactProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_XAttrCompactProto_descriptor_;
}

const INodeSection_XAttrCompactProto& INodeSection_XAttrCompactProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_XAttrCompactProto* INodeSection_XAttrCompactProto::default_instance_ = NULL;

INodeSection_XAttrCompactProto* INodeSection_XAttrCompactProto::New() const {
  return new INodeSection_XAttrCompactProto;
}

void INodeSection_XAttrCompactProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    name_ = 0u;
    if (has_value()) {
      if (value_ != &::google::protobuf::internal::kEmptyString) {
        value_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_XAttrCompactProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required fixed32 name = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED32) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &name_)));
          set_has_name();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional bytes value = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_value()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_XAttrCompactProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required fixed32 name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(1, this->name(), output);
  }

  // optional bytes value = 2;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->value(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_XAttrCompactProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required fixed32 name = 1;
  if (has_name()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed32ToArray(1, this->name(), target);
  }

  // optional bytes value = 2;
  if (has_value()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->value(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_XAttrCompactProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required fixed32 name = 1;
    if (has_name()) {
      total_size += 1 + 4;
    }

    // optional bytes value = 2;
    if (has_value()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->value());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_XAttrCompactProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_XAttrCompactProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_XAttrCompactProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_XAttrCompactProto::MergeFrom(const INodeSection_XAttrCompactProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_value()) {
      set_value(from.value());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_XAttrCompactProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_XAttrCompactProto::CopyFrom(const INodeSection_XAttrCompactProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_XAttrCompactProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void INodeSection_XAttrCompactProto::Swap(INodeSection_XAttrCompactProto* other) {
  if (other != this) {
    std::swap(name_, other->name_);
    std::swap(value_, other->value_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_XAttrCompactProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_XAttrCompactProto_descriptor_;
  metadata.reflection = INodeSection_XAttrCompactProto_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection_XAttrFeatureProto::kXAttrsFieldNumber;
#endif  // !_MSC_VER

INodeSection_XAttrFeatureProto::INodeSection_XAttrFeatureProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_XAttrFeatureProto::InitAsDefaultInstance() {
}

INodeSection_XAttrFeatureProto::INodeSection_XAttrFeatureProto(const INodeSection_XAttrFeatureProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_XAttrFeatureProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_XAttrFeatureProto::~INodeSection_XAttrFeatureProto() {
  SharedDtor();
}

void INodeSection_XAttrFeatureProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void INodeSection_XAttrFeatureProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_XAttrFeatureProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_XAttrFeatureProto_descriptor_;
}

const INodeSection_XAttrFeatureProto& INodeSection_XAttrFeatureProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_XAttrFeatureProto* INodeSection_XAttrFeatureProto::default_instance_ = NULL;

INodeSection_XAttrFeatureProto* INodeSection_XAttrFeatureProto::New() const {
  return new INodeSection_XAttrFeatureProto;
}

void INodeSection_XAttrFeatureProto::Clear() {
  xattrs_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_XAttrFeatureProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto xAttrs = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_xAttrs;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_XAttrFeatureProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto xAttrs = 1;
  for (int i = 0; i < this->xattrs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->xattrs(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_XAttrFeatureProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto xAttrs = 1;
  for (int i = 0; i < this->xattrs_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->xattrs(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_XAttrFeatureProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto xAttrs = 1;
  total_size += 1 * this->xattrs_size();
  for (int i = 0; i < this->xattrs_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->xattrs(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_XAttrFeatureProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_XAttrFeatureProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_XAttrFeatureProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_XAttrFeatureProto::MergeFrom(const INodeSection_XAttrFeatureProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  xattrs_.MergeFrom(from.xattrs_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_XAttrFeatureProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_XAttrFeatureProto::CopyFrom(const INodeSection_XAttrFeatureProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_XAttrFeatureProto::IsInitialized() const {

  for (int i = 0; i < xattrs_size(); i++) {
    if (!this->xattrs(i).IsInitialized()) return false;
  }
  return true;
}

void INodeSection_XAttrFeatureProto::Swap(INodeSection_XAttrFeatureProto* other) {
  if (other != this) {
    xattrs_.Swap(&other->xattrs_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_XAttrFeatureProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_XAttrFeatureProto_descriptor_;
  metadata.reflection = INodeSection_XAttrFeatureProto_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection_INodeFile::kReplicationFieldNumber;
const int INodeSection_INodeFile::kModificationTimeFieldNumber;
const int INodeSection_INodeFile::kAccessTimeFieldNumber;
const int INodeSection_INodeFile::kPreferredBlockSizeFieldNumber;
const int INodeSection_INodeFile::kPermissionFieldNumber;
const int INodeSection_INodeFile::kBlocksFieldNumber;
const int INodeSection_INodeFile::kFileUCFieldNumber;
const int INodeSection_INodeFile::kAclFieldNumber;
const int INodeSection_INodeFile::kXAttrsFieldNumber;
const int INodeSection_INodeFile::kStoragePolicyIDFieldNumber;
#endif  // !_MSC_VER

INodeSection_INodeFile::INodeSection_INodeFile()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_INodeFile::InitAsDefaultInstance() {
  fileuc_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature*>(&::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature::default_instance());
  acl_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto*>(&::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::default_instance());
  xattrs_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto*>(&::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::default_instance());
}

INodeSection_INodeFile::INodeSection_INodeFile(const INodeSection_INodeFile& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_INodeFile::SharedCtor() {
  _cached_size_ = 0;
  replication_ = 0u;
  modificationtime_ = GOOGLE_ULONGLONG(0);
  accesstime_ = GOOGLE_ULONGLONG(0);
  preferredblocksize_ = GOOGLE_ULONGLONG(0);
  permission_ = GOOGLE_ULONGLONG(0);
  fileuc_ = NULL;
  acl_ = NULL;
  xattrs_ = NULL;
  storagepolicyid_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_INodeFile::~INodeSection_INodeFile() {
  SharedDtor();
}

void INodeSection_INodeFile::SharedDtor() {
  if (this != default_instance_) {
    delete fileuc_;
    delete acl_;
    delete xattrs_;
  }
}

void INodeSection_INodeFile::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_INodeFile::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_INodeFile_descriptor_;
}

const INodeSection_INodeFile& INodeSection_INodeFile::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_INodeFile* INodeSection_INodeFile::default_instance_ = NULL;

INodeSection_INodeFile* INodeSection_INodeFile::New() const {
  return new INodeSection_INodeFile;
}

void INodeSection_INodeFile::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    replication_ = 0u;
    modificationtime_ = GOOGLE_ULONGLONG(0);
    accesstime_ = GOOGLE_ULONGLONG(0);
    preferredblocksize_ = GOOGLE_ULONGLONG(0);
    permission_ = GOOGLE_ULONGLONG(0);
    if (has_fileuc()) {
      if (fileuc_ != NULL) fileuc_->::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature::Clear();
    }
    if (has_acl()) {
      if (acl_ != NULL) acl_->::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::Clear();
    }
  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (has_xattrs()) {
      if (xattrs_ != NULL) xattrs_->::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::Clear();
    }
    storagepolicyid_ = 0u;
  }
  blocks_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_INodeFile::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 replication = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &replication_)));
          set_has_replication();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_modificationTime;
        break;
      }

      // optional uint64 modificationTime = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_modificationTime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &modificationtime_)));
          set_has_modificationtime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_accessTime;
        break;
      }

      // optional uint64 accessTime = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_accessTime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &accesstime_)));
          set_has_accesstime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_preferredBlockSize;
        break;
      }

      // optional uint64 preferredBlockSize = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_preferredBlockSize:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &preferredblocksize_)));
          set_has_preferredblocksize();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(41)) goto parse_permission;
        break;
      }

      // optional fixed64 permission = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED64) {
         parse_permission:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &permission_)));
          set_has_permission();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_blocks;
        break;
      }

      // repeated .hadoop.hdfs.BlockProto blocks = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_blocks:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_blocks()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_blocks;
        if (input->ExpectTag(58)) goto parse_fileUC;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature fileUC = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_fileUC:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_fileuc()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(66)) goto parse_acl;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 8;
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_acl:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_acl()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(74)) goto parse_xAttrs;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 9;
      case 9: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(80)) goto parse_storagePolicyID;
        break;
      }

      // optional uint32 storagePolicyID = 10;
      case 10: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_storagePolicyID:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &storagepolicyid_)));
          set_has_storagepolicyid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_INodeFile::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 replication = 1;
  if (has_replication()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->replication(), output);
  }

  // optional uint64 modificationTime = 2;
  if (has_modificationtime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->modificationtime(), output);
  }

  // optional uint64 accessTime = 3;
  if (has_accesstime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->accesstime(), output);
  }

  // optional uint64 preferredBlockSize = 4;
  if (has_preferredblocksize()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->preferredblocksize(), output);
  }

  // optional fixed64 permission = 5;
  if (has_permission()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(5, this->permission(), output);
  }

  // repeated .hadoop.hdfs.BlockProto blocks = 6;
  for (int i = 0; i < this->blocks_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->blocks(i), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature fileUC = 7;
  if (has_fileuc()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->fileuc(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 8;
  if (has_acl()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->acl(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 9;
  if (has_xattrs()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->xattrs(), output);
  }

  // optional uint32 storagePolicyID = 10;
  if (has_storagepolicyid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(10, this->storagepolicyid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_INodeFile::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 replication = 1;
  if (has_replication()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->replication(), target);
  }

  // optional uint64 modificationTime = 2;
  if (has_modificationtime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->modificationtime(), target);
  }

  // optional uint64 accessTime = 3;
  if (has_accesstime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->accesstime(), target);
  }

  // optional uint64 preferredBlockSize = 4;
  if (has_preferredblocksize()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->preferredblocksize(), target);
  }

  // optional fixed64 permission = 5;
  if (has_permission()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(5, this->permission(), target);
  }

  // repeated .hadoop.hdfs.BlockProto blocks = 6;
  for (int i = 0; i < this->blocks_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        6, this->blocks(i), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature fileUC = 7;
  if (has_fileuc()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        7, this->fileuc(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 8;
  if (has_acl()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        8, this->acl(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 9;
  if (has_xattrs()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        9, this->xattrs(), target);
  }

  // optional uint32 storagePolicyID = 10;
  if (has_storagepolicyid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(10, this->storagepolicyid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_INodeFile::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 replication = 1;
    if (has_replication()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->replication());
    }

    // optional uint64 modificationTime = 2;
    if (has_modificationtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->modificationtime());
    }

    // optional uint64 accessTime = 3;
    if (has_accesstime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->accesstime());
    }

    // optional uint64 preferredBlockSize = 4;
    if (has_preferredblocksize()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->preferredblocksize());
    }

    // optional fixed64 permission = 5;
    if (has_permission()) {
      total_size += 1 + 8;
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature fileUC = 7;
    if (has_fileuc()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->fileuc());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 8;
    if (has_acl()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->acl());
    }

  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 9;
    if (has_xattrs()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->xattrs());
    }

    // optional uint32 storagePolicyID = 10;
    if (has_storagepolicyid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->storagepolicyid());
    }

  }
  // repeated .hadoop.hdfs.BlockProto blocks = 6;
  total_size += 1 * this->blocks_size();
  for (int i = 0; i < this->blocks_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->blocks(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_INodeFile::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_INodeFile* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_INodeFile*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_INodeFile::MergeFrom(const INodeSection_INodeFile& from) {
  GOOGLE_CHECK_NE(&from, this);
  blocks_.MergeFrom(from.blocks_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_replication()) {
      set_replication(from.replication());
    }
    if (from.has_modificationtime()) {
      set_modificationtime(from.modificationtime());
    }
    if (from.has_accesstime()) {
      set_accesstime(from.accesstime());
    }
    if (from.has_preferredblocksize()) {
      set_preferredblocksize(from.preferredblocksize());
    }
    if (from.has_permission()) {
      set_permission(from.permission());
    }
    if (from.has_fileuc()) {
      mutable_fileuc()->::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature::MergeFrom(from.fileuc());
    }
    if (from.has_acl()) {
      mutable_acl()->::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::MergeFrom(from.acl());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_xattrs()) {
      mutable_xattrs()->::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::MergeFrom(from.xattrs());
    }
    if (from.has_storagepolicyid()) {
      set_storagepolicyid(from.storagepolicyid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_INodeFile::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_INodeFile::CopyFrom(const INodeSection_INodeFile& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_INodeFile::IsInitialized() const {

  for (int i = 0; i < blocks_size(); i++) {
    if (!this->blocks(i).IsInitialized()) return false;
  }
  if (has_xattrs()) {
    if (!this->xattrs().IsInitialized()) return false;
  }
  return true;
}

void INodeSection_INodeFile::Swap(INodeSection_INodeFile* other) {
  if (other != this) {
    std::swap(replication_, other->replication_);
    std::swap(modificationtime_, other->modificationtime_);
    std::swap(accesstime_, other->accesstime_);
    std::swap(preferredblocksize_, other->preferredblocksize_);
    std::swap(permission_, other->permission_);
    blocks_.Swap(&other->blocks_);
    std::swap(fileuc_, other->fileuc_);
    std::swap(acl_, other->acl_);
    std::swap(xattrs_, other->xattrs_);
    std::swap(storagepolicyid_, other->storagepolicyid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_INodeFile::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_INodeFile_descriptor_;
  metadata.reflection = INodeSection_INodeFile_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection_INodeDirectory::kModificationTimeFieldNumber;
const int INodeSection_INodeDirectory::kNsQuotaFieldNumber;
const int INodeSection_INodeDirectory::kDsQuotaFieldNumber;
const int INodeSection_INodeDirectory::kPermissionFieldNumber;
const int INodeSection_INodeDirectory::kAclFieldNumber;
const int INodeSection_INodeDirectory::kXAttrsFieldNumber;
#endif  // !_MSC_VER

INodeSection_INodeDirectory::INodeSection_INodeDirectory()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_INodeDirectory::InitAsDefaultInstance() {
  acl_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto*>(&::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::default_instance());
  xattrs_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto*>(&::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::default_instance());
}

INodeSection_INodeDirectory::INodeSection_INodeDirectory(const INodeSection_INodeDirectory& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_INodeDirectory::SharedCtor() {
  _cached_size_ = 0;
  modificationtime_ = GOOGLE_ULONGLONG(0);
  nsquota_ = GOOGLE_ULONGLONG(0);
  dsquota_ = GOOGLE_ULONGLONG(0);
  permission_ = GOOGLE_ULONGLONG(0);
  acl_ = NULL;
  xattrs_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_INodeDirectory::~INodeSection_INodeDirectory() {
  SharedDtor();
}

void INodeSection_INodeDirectory::SharedDtor() {
  if (this != default_instance_) {
    delete acl_;
    delete xattrs_;
  }
}

void INodeSection_INodeDirectory::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_INodeDirectory::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_INodeDirectory_descriptor_;
}

const INodeSection_INodeDirectory& INodeSection_INodeDirectory::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_INodeDirectory* INodeSection_INodeDirectory::default_instance_ = NULL;

INodeSection_INodeDirectory* INodeSection_INodeDirectory::New() const {
  return new INodeSection_INodeDirectory;
}

void INodeSection_INodeDirectory::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    modificationtime_ = GOOGLE_ULONGLONG(0);
    nsquota_ = GOOGLE_ULONGLONG(0);
    dsquota_ = GOOGLE_ULONGLONG(0);
    permission_ = GOOGLE_ULONGLONG(0);
    if (has_acl()) {
      if (acl_ != NULL) acl_->::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::Clear();
    }
    if (has_xattrs()) {
      if (xattrs_ != NULL) xattrs_->::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_INodeDirectory::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 modificationTime = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &modificationtime_)));
          set_has_modificationtime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_nsQuota;
        break;
      }

      // optional uint64 nsQuota = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_nsQuota:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &nsquota_)));
          set_has_nsquota();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_dsQuota;
        break;
      }

      // optional uint64 dsQuota = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_dsQuota:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &dsquota_)));
          set_has_dsquota();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(33)) goto parse_permission;
        break;
      }

      // optional fixed64 permission = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED64) {
         parse_permission:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &permission_)));
          set_has_permission();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_acl;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_acl:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_acl()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_xAttrs;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_xAttrs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_xattrs()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_INodeDirectory::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint64 modificationTime = 1;
  if (has_modificationtime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->modificationtime(), output);
  }

  // optional uint64 nsQuota = 2;
  if (has_nsquota()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->nsquota(), output);
  }

  // optional uint64 dsQuota = 3;
  if (has_dsquota()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->dsquota(), output);
  }

  // optional fixed64 permission = 4;
  if (has_permission()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(4, this->permission(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 5;
  if (has_acl()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->acl(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 6;
  if (has_xattrs()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->xattrs(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_INodeDirectory::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint64 modificationTime = 1;
  if (has_modificationtime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->modificationtime(), target);
  }

  // optional uint64 nsQuota = 2;
  if (has_nsquota()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->nsquota(), target);
  }

  // optional uint64 dsQuota = 3;
  if (has_dsquota()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->dsquota(), target);
  }

  // optional fixed64 permission = 4;
  if (has_permission()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(4, this->permission(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 5;
  if (has_acl()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        5, this->acl(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 6;
  if (has_xattrs()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        6, this->xattrs(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_INodeDirectory::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint64 modificationTime = 1;
    if (has_modificationtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->modificationtime());
    }

    // optional uint64 nsQuota = 2;
    if (has_nsquota()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->nsquota());
    }

    // optional uint64 dsQuota = 3;
    if (has_dsquota()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->dsquota());
    }

    // optional fixed64 permission = 4;
    if (has_permission()) {
      total_size += 1 + 8;
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 5;
    if (has_acl()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->acl());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 6;
    if (has_xattrs()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->xattrs());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_INodeDirectory::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_INodeDirectory* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_INodeDirectory*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_INodeDirectory::MergeFrom(const INodeSection_INodeDirectory& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_modificationtime()) {
      set_modificationtime(from.modificationtime());
    }
    if (from.has_nsquota()) {
      set_nsquota(from.nsquota());
    }
    if (from.has_dsquota()) {
      set_dsquota(from.dsquota());
    }
    if (from.has_permission()) {
      set_permission(from.permission());
    }
    if (from.has_acl()) {
      mutable_acl()->::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::MergeFrom(from.acl());
    }
    if (from.has_xattrs()) {
      mutable_xattrs()->::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::MergeFrom(from.xattrs());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_INodeDirectory::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_INodeDirectory::CopyFrom(const INodeSection_INodeDirectory& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_INodeDirectory::IsInitialized() const {

  if (has_xattrs()) {
    if (!this->xattrs().IsInitialized()) return false;
  }
  return true;
}

void INodeSection_INodeDirectory::Swap(INodeSection_INodeDirectory* other) {
  if (other != this) {
    std::swap(modificationtime_, other->modificationtime_);
    std::swap(nsquota_, other->nsquota_);
    std::swap(dsquota_, other->dsquota_);
    std::swap(permission_, other->permission_);
    std::swap(acl_, other->acl_);
    std::swap(xattrs_, other->xattrs_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_INodeDirectory::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_INodeDirectory_descriptor_;
  metadata.reflection = INodeSection_INodeDirectory_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection_INodeSymlink::kPermissionFieldNumber;
const int INodeSection_INodeSymlink::kTargetFieldNumber;
const int INodeSection_INodeSymlink::kModificationTimeFieldNumber;
const int INodeSection_INodeSymlink::kAccessTimeFieldNumber;
#endif  // !_MSC_VER

INodeSection_INodeSymlink::INodeSection_INodeSymlink()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_INodeSymlink::InitAsDefaultInstance() {
}

INodeSection_INodeSymlink::INodeSection_INodeSymlink(const INodeSection_INodeSymlink& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_INodeSymlink::SharedCtor() {
  _cached_size_ = 0;
  permission_ = GOOGLE_ULONGLONG(0);
  target_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  modificationtime_ = GOOGLE_ULONGLONG(0);
  accesstime_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_INodeSymlink::~INodeSection_INodeSymlink() {
  SharedDtor();
}

void INodeSection_INodeSymlink::SharedDtor() {
  if (target_ != &::google::protobuf::internal::kEmptyString) {
    delete target_;
  }
  if (this != default_instance_) {
  }
}

void INodeSection_INodeSymlink::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_INodeSymlink::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_INodeSymlink_descriptor_;
}

const INodeSection_INodeSymlink& INodeSection_INodeSymlink::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_INodeSymlink* INodeSection_INodeSymlink::default_instance_ = NULL;

INodeSection_INodeSymlink* INodeSection_INodeSymlink::New() const {
  return new INodeSection_INodeSymlink;
}

void INodeSection_INodeSymlink::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    permission_ = GOOGLE_ULONGLONG(0);
    if (has_target()) {
      if (target_ != &::google::protobuf::internal::kEmptyString) {
        target_->clear();
      }
    }
    modificationtime_ = GOOGLE_ULONGLONG(0);
    accesstime_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_INodeSymlink::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional fixed64 permission = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_FIXED64) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &permission_)));
          set_has_permission();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_target;
        break;
      }

      // optional bytes target = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_target:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_target()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_modificationTime;
        break;
      }

      // optional uint64 modificationTime = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_modificationTime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &modificationtime_)));
          set_has_modificationtime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_accessTime;
        break;
      }

      // optional uint64 accessTime = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_accessTime:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &accesstime_)));
          set_has_accesstime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_INodeSymlink::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional fixed64 permission = 1;
  if (has_permission()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->permission(), output);
  }

  // optional bytes target = 2;
  if (has_target()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->target(), output);
  }

  // optional uint64 modificationTime = 3;
  if (has_modificationtime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->modificationtime(), output);
  }

  // optional uint64 accessTime = 4;
  if (has_accesstime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->accesstime(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_INodeSymlink::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional fixed64 permission = 1;
  if (has_permission()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->permission(), target);
  }

  // optional bytes target = 2;
  if (has_target()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->target(), target);
  }

  // optional uint64 modificationTime = 3;
  if (has_modificationtime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->modificationtime(), target);
  }

  // optional uint64 accessTime = 4;
  if (has_accesstime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->accesstime(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_INodeSymlink::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional fixed64 permission = 1;
    if (has_permission()) {
      total_size += 1 + 8;
    }

    // optional bytes target = 2;
    if (has_target()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->target());
    }

    // optional uint64 modificationTime = 3;
    if (has_modificationtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->modificationtime());
    }

    // optional uint64 accessTime = 4;
    if (has_accesstime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->accesstime());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_INodeSymlink::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_INodeSymlink* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_INodeSymlink*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_INodeSymlink::MergeFrom(const INodeSection_INodeSymlink& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_permission()) {
      set_permission(from.permission());
    }
    if (from.has_target()) {
      set_target(from.target());
    }
    if (from.has_modificationtime()) {
      set_modificationtime(from.modificationtime());
    }
    if (from.has_accesstime()) {
      set_accesstime(from.accesstime());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_INodeSymlink::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_INodeSymlink::CopyFrom(const INodeSection_INodeSymlink& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_INodeSymlink::IsInitialized() const {

  return true;
}

void INodeSection_INodeSymlink::Swap(INodeSection_INodeSymlink* other) {
  if (other != this) {
    std::swap(permission_, other->permission_);
    std::swap(target_, other->target_);
    std::swap(modificationtime_, other->modificationtime_);
    std::swap(accesstime_, other->accesstime_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_INodeSymlink::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_INodeSymlink_descriptor_;
  metadata.reflection = INodeSection_INodeSymlink_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

const ::google::protobuf::EnumDescriptor* INodeSection_INode_Type_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_INode_Type_descriptor_;
}
bool INodeSection_INode_Type_IsValid(int value) {
  switch(value) {
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const INodeSection_INode_Type INodeSection_INode::FILE;
const INodeSection_INode_Type INodeSection_INode::DIRECTORY;
const INodeSection_INode_Type INodeSection_INode::SYMLINK;
const INodeSection_INode_Type INodeSection_INode::Type_MIN;
const INodeSection_INode_Type INodeSection_INode::Type_MAX;
const int INodeSection_INode::Type_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int INodeSection_INode::kTypeFieldNumber;
const int INodeSection_INode::kIdFieldNumber;
const int INodeSection_INode::kNameFieldNumber;
const int INodeSection_INode::kFileFieldNumber;
const int INodeSection_INode::kDirectoryFieldNumber;
const int INodeSection_INode::kSymlinkFieldNumber;
#endif  // !_MSC_VER

INodeSection_INode::INodeSection_INode()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection_INode::InitAsDefaultInstance() {
  file_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_INodeFile*>(&::hadoop::hdfs::fsimage::INodeSection_INodeFile::default_instance());
  directory_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory*>(&::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::default_instance());
  symlink_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink*>(&::hadoop::hdfs::fsimage::INodeSection_INodeSymlink::default_instance());
}

INodeSection_INode::INodeSection_INode(const INodeSection_INode& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection_INode::SharedCtor() {
  _cached_size_ = 0;
  type_ = 1;
  id_ = GOOGLE_ULONGLONG(0);
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  file_ = NULL;
  directory_ = NULL;
  symlink_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection_INode::~INodeSection_INode() {
  SharedDtor();
}

void INodeSection_INode::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
    delete file_;
    delete directory_;
    delete symlink_;
  }
}

void INodeSection_INode::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection_INode::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_INode_descriptor_;
}

const INodeSection_INode& INodeSection_INode::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection_INode* INodeSection_INode::default_instance_ = NULL;

INodeSection_INode* INodeSection_INode::New() const {
  return new INodeSection_INode;
}

void INodeSection_INode::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    type_ = 1;
    id_ = GOOGLE_ULONGLONG(0);
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
    if (has_file()) {
      if (file_ != NULL) file_->::hadoop::hdfs::fsimage::INodeSection_INodeFile::Clear();
    }
    if (has_directory()) {
      if (directory_ != NULL) directory_->::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::Clear();
    }
    if (has_symlink()) {
      if (symlink_ != NULL) symlink_->::hadoop::hdfs::fsimage::INodeSection_INodeSymlink::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection_INode::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.fsimage.INodeSection.INode.Type type = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::fsimage::INodeSection_INode_Type_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::fsimage::INodeSection_INode_Type >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_id;
        break;
      }

      // required uint64 id = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_id:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_name;
        break;
      }

      // optional bytes name = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_file;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile file = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_file:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_file()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_directory;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory directory = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_directory:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_directory()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_symlink;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.INodeSymlink symlink = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_symlink:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_symlink()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection_INode::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.fsimage.INodeSection.INode.Type type = 1;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // required uint64 id = 2;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->id(), output);
  }

  // optional bytes name = 3;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      3, this->name(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile file = 4;
  if (has_file()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->file(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory directory = 5;
  if (has_directory()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->directory(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeSymlink symlink = 6;
  if (has_symlink()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->symlink(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection_INode::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.fsimage.INodeSection.INode.Type type = 1;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // required uint64 id = 2;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->id(), target);
  }

  // optional bytes name = 3;
  if (has_name()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->name(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile file = 4;
  if (has_file()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->file(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory directory = 5;
  if (has_directory()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        5, this->directory(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeSymlink symlink = 6;
  if (has_symlink()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        6, this->symlink(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection_INode::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.fsimage.INodeSection.INode.Type type = 1;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // required uint64 id = 2;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->id());
    }

    // optional bytes name = 3;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->name());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile file = 4;
    if (has_file()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->file());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory directory = 5;
    if (has_directory()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->directory());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.INodeSymlink symlink = 6;
    if (has_symlink()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->symlink());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection_INode::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection_INode* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection_INode*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection_INode::MergeFrom(const INodeSection_INode& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_id()) {
      set_id(from.id());
    }
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_file()) {
      mutable_file()->::hadoop::hdfs::fsimage::INodeSection_INodeFile::MergeFrom(from.file());
    }
    if (from.has_directory()) {
      mutable_directory()->::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::MergeFrom(from.directory());
    }
    if (from.has_symlink()) {
      mutable_symlink()->::hadoop::hdfs::fsimage::INodeSection_INodeSymlink::MergeFrom(from.symlink());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection_INode::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection_INode::CopyFrom(const INodeSection_INode& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection_INode::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_file()) {
    if (!this->file().IsInitialized()) return false;
  }
  if (has_directory()) {
    if (!this->directory().IsInitialized()) return false;
  }
  return true;
}

void INodeSection_INode::Swap(INodeSection_INode* other) {
  if (other != this) {
    std::swap(type_, other->type_);
    std::swap(id_, other->id_);
    std::swap(name_, other->name_);
    std::swap(file_, other->file_);
    std::swap(directory_, other->directory_);
    std::swap(symlink_, other->symlink_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection_INode::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_INode_descriptor_;
  metadata.reflection = INodeSection_INode_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int INodeSection::kLastInodeIdFieldNumber;
const int INodeSection::kNumInodesFieldNumber;
#endif  // !_MSC_VER

INodeSection::INodeSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeSection::InitAsDefaultInstance() {
}

INodeSection::INodeSection(const INodeSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeSection::SharedCtor() {
  _cached_size_ = 0;
  lastinodeid_ = GOOGLE_ULONGLONG(0);
  numinodes_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeSection::~INodeSection() {
  SharedDtor();
}

void INodeSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void INodeSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeSection_descriptor_;
}

const INodeSection& INodeSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeSection* INodeSection::default_instance_ = NULL;

INodeSection* INodeSection::New() const {
  return new INodeSection;
}

void INodeSection::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    lastinodeid_ = GOOGLE_ULONGLONG(0);
    numinodes_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 lastInodeId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &lastinodeid_)));
          set_has_lastinodeid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_numInodes;
        break;
      }

      // optional uint64 numInodes = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numInodes:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &numinodes_)));
          set_has_numinodes();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint64 lastInodeId = 1;
  if (has_lastinodeid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->lastinodeid(), output);
  }

  // optional uint64 numInodes = 2;
  if (has_numinodes()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->numinodes(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint64 lastInodeId = 1;
  if (has_lastinodeid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->lastinodeid(), target);
  }

  // optional uint64 numInodes = 2;
  if (has_numinodes()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->numinodes(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeSection::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint64 lastInodeId = 1;
    if (has_lastinodeid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->lastinodeid());
    }

    // optional uint64 numInodes = 2;
    if (has_numinodes()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->numinodes());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeSection::MergeFrom(const INodeSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_lastinodeid()) {
      set_lastinodeid(from.lastinodeid());
    }
    if (from.has_numinodes()) {
      set_numinodes(from.numinodes());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeSection::CopyFrom(const INodeSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeSection::IsInitialized() const {

  return true;
}

void INodeSection::Swap(INodeSection* other) {
  if (other != this) {
    std::swap(lastinodeid_, other->lastinodeid_);
    std::swap(numinodes_, other->numinodes_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeSection_descriptor_;
  metadata.reflection = INodeSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int FilesUnderConstructionSection_FileUnderConstructionEntry::kInodeIdFieldNumber;
const int FilesUnderConstructionSection_FileUnderConstructionEntry::kFullPathFieldNumber;
#endif  // !_MSC_VER

FilesUnderConstructionSection_FileUnderConstructionEntry::FilesUnderConstructionSection_FileUnderConstructionEntry()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::InitAsDefaultInstance() {
}

FilesUnderConstructionSection_FileUnderConstructionEntry::FilesUnderConstructionSection_FileUnderConstructionEntry(const FilesUnderConstructionSection_FileUnderConstructionEntry& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::SharedCtor() {
  _cached_size_ = 0;
  inodeid_ = GOOGLE_ULONGLONG(0);
  fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FilesUnderConstructionSection_FileUnderConstructionEntry::~FilesUnderConstructionSection_FileUnderConstructionEntry() {
  SharedDtor();
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::SharedDtor() {
  if (fullpath_ != &::google::protobuf::internal::kEmptyString) {
    delete fullpath_;
  }
  if (this != default_instance_) {
  }
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FilesUnderConstructionSection_FileUnderConstructionEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FilesUnderConstructionSection_FileUnderConstructionEntry_descriptor_;
}

const FilesUnderConstructionSection_FileUnderConstructionEntry& FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

FilesUnderConstructionSection_FileUnderConstructionEntry* FilesUnderConstructionSection_FileUnderConstructionEntry::default_instance_ = NULL;

FilesUnderConstructionSection_FileUnderConstructionEntry* FilesUnderConstructionSection_FileUnderConstructionEntry::New() const {
  return new FilesUnderConstructionSection_FileUnderConstructionEntry;
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    inodeid_ = GOOGLE_ULONGLONG(0);
    if (has_fullpath()) {
      if (fullpath_ != &::google::protobuf::internal::kEmptyString) {
        fullpath_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FilesUnderConstructionSection_FileUnderConstructionEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 inodeId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &inodeid_)));
          set_has_inodeid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_fullPath;
        break;
      }

      // optional string fullPath = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_fullPath:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fullpath()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->fullpath().data(), this->fullpath().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint64 inodeId = 1;
  if (has_inodeid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->inodeid(), output);
  }

  // optional string fullPath = 2;
  if (has_fullpath()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fullpath().data(), this->fullpath().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->fullpath(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FilesUnderConstructionSection_FileUnderConstructionEntry::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint64 inodeId = 1;
  if (has_inodeid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->inodeid(), target);
  }

  // optional string fullPath = 2;
  if (has_fullpath()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fullpath().data(), this->fullpath().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->fullpath(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FilesUnderConstructionSection_FileUnderConstructionEntry::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint64 inodeId = 1;
    if (has_inodeid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->inodeid());
    }

    // optional string fullPath = 2;
    if (has_fullpath()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->fullpath());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FilesUnderConstructionSection_FileUnderConstructionEntry* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FilesUnderConstructionSection_FileUnderConstructionEntry*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::MergeFrom(const FilesUnderConstructionSection_FileUnderConstructionEntry& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_inodeid()) {
      set_inodeid(from.inodeid());
    }
    if (from.has_fullpath()) {
      set_fullpath(from.fullpath());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::CopyFrom(const FilesUnderConstructionSection_FileUnderConstructionEntry& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FilesUnderConstructionSection_FileUnderConstructionEntry::IsInitialized() const {

  return true;
}

void FilesUnderConstructionSection_FileUnderConstructionEntry::Swap(FilesUnderConstructionSection_FileUnderConstructionEntry* other) {
  if (other != this) {
    std::swap(inodeid_, other->inodeid_);
    std::swap(fullpath_, other->fullpath_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FilesUnderConstructionSection_FileUnderConstructionEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FilesUnderConstructionSection_FileUnderConstructionEntry_descriptor_;
  metadata.reflection = FilesUnderConstructionSection_FileUnderConstructionEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
#endif  // !_MSC_VER

FilesUnderConstructionSection::FilesUnderConstructionSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FilesUnderConstructionSection::InitAsDefaultInstance() {
}

FilesUnderConstructionSection::FilesUnderConstructionSection(const FilesUnderConstructionSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FilesUnderConstructionSection::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FilesUnderConstructionSection::~FilesUnderConstructionSection() {
  SharedDtor();
}

void FilesUnderConstructionSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void FilesUnderConstructionSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FilesUnderConstructionSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FilesUnderConstructionSection_descriptor_;
}

const FilesUnderConstructionSection& FilesUnderConstructionSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

FilesUnderConstructionSection* FilesUnderConstructionSection::default_instance_ = NULL;

FilesUnderConstructionSection* FilesUnderConstructionSection::New() const {
  return new FilesUnderConstructionSection;
}

void FilesUnderConstructionSection::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FilesUnderConstructionSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void FilesUnderConstructionSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FilesUnderConstructionSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FilesUnderConstructionSection::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FilesUnderConstructionSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FilesUnderConstructionSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FilesUnderConstructionSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FilesUnderConstructionSection::MergeFrom(const FilesUnderConstructionSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FilesUnderConstructionSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FilesUnderConstructionSection::CopyFrom(const FilesUnderConstructionSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FilesUnderConstructionSection::IsInitialized() const {

  return true;
}

void FilesUnderConstructionSection::Swap(FilesUnderConstructionSection* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FilesUnderConstructionSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FilesUnderConstructionSection_descriptor_;
  metadata.reflection = FilesUnderConstructionSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int INodeDirectorySection_DirEntry::kParentFieldNumber;
const int INodeDirectorySection_DirEntry::kChildrenFieldNumber;
const int INodeDirectorySection_DirEntry::kRefChildrenFieldNumber;
#endif  // !_MSC_VER

INodeDirectorySection_DirEntry::INodeDirectorySection_DirEntry()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeDirectorySection_DirEntry::InitAsDefaultInstance() {
}

INodeDirectorySection_DirEntry::INodeDirectorySection_DirEntry(const INodeDirectorySection_DirEntry& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeDirectorySection_DirEntry::SharedCtor() {
  _cached_size_ = 0;
  parent_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeDirectorySection_DirEntry::~INodeDirectorySection_DirEntry() {
  SharedDtor();
}

void INodeDirectorySection_DirEntry::SharedDtor() {
  if (this != default_instance_) {
  }
}

void INodeDirectorySection_DirEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeDirectorySection_DirEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeDirectorySection_DirEntry_descriptor_;
}

const INodeDirectorySection_DirEntry& INodeDirectorySection_DirEntry::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeDirectorySection_DirEntry* INodeDirectorySection_DirEntry::default_instance_ = NULL;

INodeDirectorySection_DirEntry* INodeDirectorySection_DirEntry::New() const {
  return new INodeDirectorySection_DirEntry;
}

void INodeDirectorySection_DirEntry::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    parent_ = GOOGLE_ULONGLONG(0);
  }
  children_.Clear();
  refchildren_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeDirectorySection_DirEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 parent = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &parent_)));
          set_has_parent();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_children;
        break;
      }

      // repeated uint64 children = 2 [packed = true];
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_children:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_children())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 18, input, this->mutable_children())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_refChildren;
        break;
      }

      // repeated uint32 refChildren = 3 [packed = true];
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_refChildren:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_refchildren())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 26, input, this->mutable_refchildren())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeDirectorySection_DirEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint64 parent = 1;
  if (has_parent()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->parent(), output);
  }

  // repeated uint64 children = 2 [packed = true];
  if (this->children_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_children_cached_byte_size_);
  }
  for (int i = 0; i < this->children_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->children(i), output);
  }

  // repeated uint32 refChildren = 3 [packed = true];
  if (this->refchildren_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_refchildren_cached_byte_size_);
  }
  for (int i = 0; i < this->refchildren_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->refchildren(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeDirectorySection_DirEntry::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint64 parent = 1;
  if (has_parent()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->parent(), target);
  }

  // repeated uint64 children = 2 [packed = true];
  if (this->children_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _children_cached_byte_size_, target);
  }
  for (int i = 0; i < this->children_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->children(i), target);
  }

  // repeated uint32 refChildren = 3 [packed = true];
  if (this->refchildren_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _refchildren_cached_byte_size_, target);
  }
  for (int i = 0; i < this->refchildren_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->refchildren(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeDirectorySection_DirEntry::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint64 parent = 1;
    if (has_parent()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->parent());
    }

  }
  // repeated uint64 children = 2 [packed = true];
  {
    int data_size = 0;
    for (int i = 0; i < this->children_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->children(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _children_cached_byte_size_ = data_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 refChildren = 3 [packed = true];
  {
    int data_size = 0;
    for (int i = 0; i < this->refchildren_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->refchildren(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _refchildren_cached_byte_size_ = data_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeDirectorySection_DirEntry::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeDirectorySection_DirEntry* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeDirectorySection_DirEntry*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeDirectorySection_DirEntry::MergeFrom(const INodeDirectorySection_DirEntry& from) {
  GOOGLE_CHECK_NE(&from, this);
  children_.MergeFrom(from.children_);
  refchildren_.MergeFrom(from.refchildren_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_parent()) {
      set_parent(from.parent());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeDirectorySection_DirEntry::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeDirectorySection_DirEntry::CopyFrom(const INodeDirectorySection_DirEntry& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeDirectorySection_DirEntry::IsInitialized() const {

  return true;
}

void INodeDirectorySection_DirEntry::Swap(INodeDirectorySection_DirEntry* other) {
  if (other != this) {
    std::swap(parent_, other->parent_);
    children_.Swap(&other->children_);
    refchildren_.Swap(&other->refchildren_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeDirectorySection_DirEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeDirectorySection_DirEntry_descriptor_;
  metadata.reflection = INodeDirectorySection_DirEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
#endif  // !_MSC_VER

INodeDirectorySection::INodeDirectorySection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeDirectorySection::InitAsDefaultInstance() {
}

INodeDirectorySection::INodeDirectorySection(const INodeDirectorySection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeDirectorySection::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeDirectorySection::~INodeDirectorySection() {
  SharedDtor();
}

void INodeDirectorySection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void INodeDirectorySection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeDirectorySection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeDirectorySection_descriptor_;
}

const INodeDirectorySection& INodeDirectorySection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeDirectorySection* INodeDirectorySection::default_instance_ = NULL;

INodeDirectorySection* INodeDirectorySection::New() const {
  return new INodeDirectorySection;
}

void INodeDirectorySection::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeDirectorySection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void INodeDirectorySection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeDirectorySection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeDirectorySection::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeDirectorySection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeDirectorySection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeDirectorySection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeDirectorySection::MergeFrom(const INodeDirectorySection& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeDirectorySection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeDirectorySection::CopyFrom(const INodeDirectorySection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeDirectorySection::IsInitialized() const {

  return true;
}

void INodeDirectorySection::Swap(INodeDirectorySection* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeDirectorySection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeDirectorySection_descriptor_;
  metadata.reflection = INodeDirectorySection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int INodeReferenceSection_INodeReference::kReferredIdFieldNumber;
const int INodeReferenceSection_INodeReference::kNameFieldNumber;
const int INodeReferenceSection_INodeReference::kDstSnapshotIdFieldNumber;
const int INodeReferenceSection_INodeReference::kLastSnapshotIdFieldNumber;
#endif  // !_MSC_VER

INodeReferenceSection_INodeReference::INodeReferenceSection_INodeReference()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeReferenceSection_INodeReference::InitAsDefaultInstance() {
}

INodeReferenceSection_INodeReference::INodeReferenceSection_INodeReference(const INodeReferenceSection_INodeReference& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeReferenceSection_INodeReference::SharedCtor() {
  _cached_size_ = 0;
  referredid_ = GOOGLE_ULONGLONG(0);
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  dstsnapshotid_ = 0u;
  lastsnapshotid_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeReferenceSection_INodeReference::~INodeReferenceSection_INodeReference() {
  SharedDtor();
}

void INodeReferenceSection_INodeReference::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
  }
}

void INodeReferenceSection_INodeReference::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeReferenceSection_INodeReference::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeReferenceSection_INodeReference_descriptor_;
}

const INodeReferenceSection_INodeReference& INodeReferenceSection_INodeReference::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeReferenceSection_INodeReference* INodeReferenceSection_INodeReference::default_instance_ = NULL;

INodeReferenceSection_INodeReference* INodeReferenceSection_INodeReference::New() const {
  return new INodeReferenceSection_INodeReference;
}

void INodeReferenceSection_INodeReference::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    referredid_ = GOOGLE_ULONGLONG(0);
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
    dstsnapshotid_ = 0u;
    lastsnapshotid_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeReferenceSection_INodeReference::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 referredId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &referredid_)));
          set_has_referredid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_name;
        break;
      }

      // optional bytes name = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_dstSnapshotId;
        break;
      }

      // optional uint32 dstSnapshotId = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_dstSnapshotId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &dstsnapshotid_)));
          set_has_dstsnapshotid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_lastSnapshotId;
        break;
      }

      // optional uint32 lastSnapshotId = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lastSnapshotId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &lastsnapshotid_)));
          set_has_lastsnapshotid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void INodeReferenceSection_INodeReference::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint64 referredId = 1;
  if (has_referredid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->referredid(), output);
  }

  // optional bytes name = 2;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->name(), output);
  }

  // optional uint32 dstSnapshotId = 3;
  if (has_dstsnapshotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->dstsnapshotid(), output);
  }

  // optional uint32 lastSnapshotId = 4;
  if (has_lastsnapshotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(4, this->lastsnapshotid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeReferenceSection_INodeReference::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint64 referredId = 1;
  if (has_referredid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->referredid(), target);
  }

  // optional bytes name = 2;
  if (has_name()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->name(), target);
  }

  // optional uint32 dstSnapshotId = 3;
  if (has_dstsnapshotid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->dstsnapshotid(), target);
  }

  // optional uint32 lastSnapshotId = 4;
  if (has_lastsnapshotid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(4, this->lastsnapshotid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeReferenceSection_INodeReference::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint64 referredId = 1;
    if (has_referredid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->referredid());
    }

    // optional bytes name = 2;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->name());
    }

    // optional uint32 dstSnapshotId = 3;
    if (has_dstsnapshotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->dstsnapshotid());
    }

    // optional uint32 lastSnapshotId = 4;
    if (has_lastsnapshotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->lastsnapshotid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeReferenceSection_INodeReference::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeReferenceSection_INodeReference* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeReferenceSection_INodeReference*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeReferenceSection_INodeReference::MergeFrom(const INodeReferenceSection_INodeReference& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_referredid()) {
      set_referredid(from.referredid());
    }
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_dstsnapshotid()) {
      set_dstsnapshotid(from.dstsnapshotid());
    }
    if (from.has_lastsnapshotid()) {
      set_lastsnapshotid(from.lastsnapshotid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeReferenceSection_INodeReference::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeReferenceSection_INodeReference::CopyFrom(const INodeReferenceSection_INodeReference& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeReferenceSection_INodeReference::IsInitialized() const {

  return true;
}

void INodeReferenceSection_INodeReference::Swap(INodeReferenceSection_INodeReference* other) {
  if (other != this) {
    std::swap(referredid_, other->referredid_);
    std::swap(name_, other->name_);
    std::swap(dstsnapshotid_, other->dstsnapshotid_);
    std::swap(lastsnapshotid_, other->lastsnapshotid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeReferenceSection_INodeReference::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeReferenceSection_INodeReference_descriptor_;
  metadata.reflection = INodeReferenceSection_INodeReference_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
#endif  // !_MSC_VER

INodeReferenceSection::INodeReferenceSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void INodeReferenceSection::InitAsDefaultInstance() {
}

INodeReferenceSection::INodeReferenceSection(const INodeReferenceSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void INodeReferenceSection::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

INodeReferenceSection::~INodeReferenceSection() {
  SharedDtor();
}

void INodeReferenceSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void INodeReferenceSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* INodeReferenceSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return INodeReferenceSection_descriptor_;
}

const INodeReferenceSection& INodeReferenceSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

INodeReferenceSection* INodeReferenceSection::default_instance_ = NULL;

INodeReferenceSection* INodeReferenceSection::New() const {
  return new INodeReferenceSection;
}

void INodeReferenceSection::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool INodeReferenceSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void INodeReferenceSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* INodeReferenceSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int INodeReferenceSection::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void INodeReferenceSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const INodeReferenceSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const INodeReferenceSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void INodeReferenceSection::MergeFrom(const INodeReferenceSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void INodeReferenceSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void INodeReferenceSection::CopyFrom(const INodeReferenceSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool INodeReferenceSection::IsInitialized() const {

  return true;
}

void INodeReferenceSection::Swap(INodeReferenceSection* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata INodeReferenceSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = INodeReferenceSection_descriptor_;
  metadata.reflection = INodeReferenceSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SnapshotSection_Snapshot::kSnapshotIdFieldNumber;
const int SnapshotSection_Snapshot::kRootFieldNumber;
#endif  // !_MSC_VER

SnapshotSection_Snapshot::SnapshotSection_Snapshot()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotSection_Snapshot::InitAsDefaultInstance() {
  root_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_INode*>(&::hadoop::hdfs::fsimage::INodeSection_INode::default_instance());
}

SnapshotSection_Snapshot::SnapshotSection_Snapshot(const SnapshotSection_Snapshot& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotSection_Snapshot::SharedCtor() {
  _cached_size_ = 0;
  snapshotid_ = 0u;
  root_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotSection_Snapshot::~SnapshotSection_Snapshot() {
  SharedDtor();
}

void SnapshotSection_Snapshot::SharedDtor() {
  if (this != default_instance_) {
    delete root_;
  }
}

void SnapshotSection_Snapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotSection_Snapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotSection_Snapshot_descriptor_;
}

const SnapshotSection_Snapshot& SnapshotSection_Snapshot::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotSection_Snapshot* SnapshotSection_Snapshot::default_instance_ = NULL;

SnapshotSection_Snapshot* SnapshotSection_Snapshot::New() const {
  return new SnapshotSection_Snapshot;
}

void SnapshotSection_Snapshot::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    snapshotid_ = 0u;
    if (has_root()) {
      if (root_ != NULL) root_->::hadoop::hdfs::fsimage::INodeSection_INode::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotSection_Snapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 snapshotId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &snapshotid_)));
          set_has_snapshotid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_root;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.INode root = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_root:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_root()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SnapshotSection_Snapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 snapshotId = 1;
  if (has_snapshotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->snapshotid(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INode root = 2;
  if (has_root()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->root(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotSection_Snapshot::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 snapshotId = 1;
  if (has_snapshotid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->snapshotid(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INode root = 2;
  if (has_root()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->root(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotSection_Snapshot::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 snapshotId = 1;
    if (has_snapshotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->snapshotid());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.INode root = 2;
    if (has_root()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->root());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotSection_Snapshot::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotSection_Snapshot* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotSection_Snapshot*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotSection_Snapshot::MergeFrom(const SnapshotSection_Snapshot& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_snapshotid()) {
      set_snapshotid(from.snapshotid());
    }
    if (from.has_root()) {
      mutable_root()->::hadoop::hdfs::fsimage::INodeSection_INode::MergeFrom(from.root());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotSection_Snapshot::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotSection_Snapshot::CopyFrom(const SnapshotSection_Snapshot& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotSection_Snapshot::IsInitialized() const {

  if (has_root()) {
    if (!this->root().IsInitialized()) return false;
  }
  return true;
}

void SnapshotSection_Snapshot::Swap(SnapshotSection_Snapshot* other) {
  if (other != this) {
    std::swap(snapshotid_, other->snapshotid_);
    std::swap(root_, other->root_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotSection_Snapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotSection_Snapshot_descriptor_;
  metadata.reflection = SnapshotSection_Snapshot_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int SnapshotSection::kSnapshotCounterFieldNumber;
const int SnapshotSection::kSnapshottableDirFieldNumber;
const int SnapshotSection::kNumSnapshotsFieldNumber;
#endif  // !_MSC_VER

SnapshotSection::SnapshotSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotSection::InitAsDefaultInstance() {
}

SnapshotSection::SnapshotSection(const SnapshotSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotSection::SharedCtor() {
  _cached_size_ = 0;
  snapshotcounter_ = 0u;
  numsnapshots_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotSection::~SnapshotSection() {
  SharedDtor();
}

void SnapshotSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SnapshotSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotSection_descriptor_;
}

const SnapshotSection& SnapshotSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotSection* SnapshotSection::default_instance_ = NULL;

SnapshotSection* SnapshotSection::New() const {
  return new SnapshotSection;
}

void SnapshotSection::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    snapshotcounter_ = 0u;
    numsnapshots_ = 0u;
  }
  snapshottabledir_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 snapshotCounter = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &snapshotcounter_)));
          set_has_snapshotcounter();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_snapshottableDir;
        break;
      }

      // repeated uint64 snapshottableDir = 2 [packed = true];
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_snapshottableDir:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_snapshottabledir())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 18, input, this->mutable_snapshottabledir())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_numSnapshots;
        break;
      }

      // optional uint32 numSnapshots = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numSnapshots:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numsnapshots_)));
          set_has_numsnapshots();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SnapshotSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 snapshotCounter = 1;
  if (has_snapshotcounter()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->snapshotcounter(), output);
  }

  // repeated uint64 snapshottableDir = 2 [packed = true];
  if (this->snapshottabledir_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_snapshottabledir_cached_byte_size_);
  }
  for (int i = 0; i < this->snapshottabledir_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->snapshottabledir(i), output);
  }

  // optional uint32 numSnapshots = 3;
  if (has_numsnapshots()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->numsnapshots(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 snapshotCounter = 1;
  if (has_snapshotcounter()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->snapshotcounter(), target);
  }

  // repeated uint64 snapshottableDir = 2 [packed = true];
  if (this->snapshottabledir_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _snapshottabledir_cached_byte_size_, target);
  }
  for (int i = 0; i < this->snapshottabledir_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->snapshottabledir(i), target);
  }

  // optional uint32 numSnapshots = 3;
  if (has_numsnapshots()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->numsnapshots(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotSection::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 snapshotCounter = 1;
    if (has_snapshotcounter()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->snapshotcounter());
    }

    // optional uint32 numSnapshots = 3;
    if (has_numsnapshots()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numsnapshots());
    }

  }
  // repeated uint64 snapshottableDir = 2 [packed = true];
  {
    int data_size = 0;
    for (int i = 0; i < this->snapshottabledir_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->snapshottabledir(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _snapshottabledir_cached_byte_size_ = data_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotSection::MergeFrom(const SnapshotSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  snapshottabledir_.MergeFrom(from.snapshottabledir_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_snapshotcounter()) {
      set_snapshotcounter(from.snapshotcounter());
    }
    if (from.has_numsnapshots()) {
      set_numsnapshots(from.numsnapshots());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotSection::CopyFrom(const SnapshotSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotSection::IsInitialized() const {

  return true;
}

void SnapshotSection::Swap(SnapshotSection* other) {
  if (other != this) {
    std::swap(snapshotcounter_, other->snapshotcounter_);
    snapshottabledir_.Swap(&other->snapshottabledir_);
    std::swap(numsnapshots_, other->numsnapshots_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotSection_descriptor_;
  metadata.reflection = SnapshotSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SnapshotDiffSection_CreatedListEntry::kNameFieldNumber;
#endif  // !_MSC_VER

SnapshotDiffSection_CreatedListEntry::SnapshotDiffSection_CreatedListEntry()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotDiffSection_CreatedListEntry::InitAsDefaultInstance() {
}

SnapshotDiffSection_CreatedListEntry::SnapshotDiffSection_CreatedListEntry(const SnapshotDiffSection_CreatedListEntry& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotDiffSection_CreatedListEntry::SharedCtor() {
  _cached_size_ = 0;
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotDiffSection_CreatedListEntry::~SnapshotDiffSection_CreatedListEntry() {
  SharedDtor();
}

void SnapshotDiffSection_CreatedListEntry::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
  }
}

void SnapshotDiffSection_CreatedListEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotDiffSection_CreatedListEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotDiffSection_CreatedListEntry_descriptor_;
}

const SnapshotDiffSection_CreatedListEntry& SnapshotDiffSection_CreatedListEntry::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotDiffSection_CreatedListEntry* SnapshotDiffSection_CreatedListEntry::default_instance_ = NULL;

SnapshotDiffSection_CreatedListEntry* SnapshotDiffSection_CreatedListEntry::New() const {
  return new SnapshotDiffSection_CreatedListEntry;
}

void SnapshotDiffSection_CreatedListEntry::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotDiffSection_CreatedListEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bytes name = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SnapshotDiffSection_CreatedListEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional bytes name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      1, this->name(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotDiffSection_CreatedListEntry::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional bytes name = 1;
  if (has_name()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->name(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotDiffSection_CreatedListEntry::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional bytes name = 1;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->name());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotDiffSection_CreatedListEntry::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotDiffSection_CreatedListEntry* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotDiffSection_CreatedListEntry*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotDiffSection_CreatedListEntry::MergeFrom(const SnapshotDiffSection_CreatedListEntry& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_name()) {
      set_name(from.name());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotDiffSection_CreatedListEntry::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotDiffSection_CreatedListEntry::CopyFrom(const SnapshotDiffSection_CreatedListEntry& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotDiffSection_CreatedListEntry::IsInitialized() const {

  return true;
}

void SnapshotDiffSection_CreatedListEntry::Swap(SnapshotDiffSection_CreatedListEntry* other) {
  if (other != this) {
    std::swap(name_, other->name_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotDiffSection_CreatedListEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotDiffSection_CreatedListEntry_descriptor_;
  metadata.reflection = SnapshotDiffSection_CreatedListEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int SnapshotDiffSection_DirectoryDiff::kSnapshotIdFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kChildrenSizeFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kIsSnapshotRootFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kNameFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kSnapshotCopyFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kCreatedListSizeFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kDeletedINodeFieldNumber;
const int SnapshotDiffSection_DirectoryDiff::kDeletedINodeRefFieldNumber;
#endif  // !_MSC_VER

SnapshotDiffSection_DirectoryDiff::SnapshotDiffSection_DirectoryDiff()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotDiffSection_DirectoryDiff::InitAsDefaultInstance() {
  snapshotcopy_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory*>(&::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::default_instance());
}

SnapshotDiffSection_DirectoryDiff::SnapshotDiffSection_DirectoryDiff(const SnapshotDiffSection_DirectoryDiff& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotDiffSection_DirectoryDiff::SharedCtor() {
  _cached_size_ = 0;
  snapshotid_ = 0u;
  childrensize_ = 0u;
  issnapshotroot_ = false;
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  snapshotcopy_ = NULL;
  createdlistsize_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotDiffSection_DirectoryDiff::~SnapshotDiffSection_DirectoryDiff() {
  SharedDtor();
}

void SnapshotDiffSection_DirectoryDiff::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
    delete snapshotcopy_;
  }
}

void SnapshotDiffSection_DirectoryDiff::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotDiffSection_DirectoryDiff::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotDiffSection_DirectoryDiff_descriptor_;
}

const SnapshotDiffSection_DirectoryDiff& SnapshotDiffSection_DirectoryDiff::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotDiffSection_DirectoryDiff* SnapshotDiffSection_DirectoryDiff::default_instance_ = NULL;

SnapshotDiffSection_DirectoryDiff* SnapshotDiffSection_DirectoryDiff::New() const {
  return new SnapshotDiffSection_DirectoryDiff;
}

void SnapshotDiffSection_DirectoryDiff::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    snapshotid_ = 0u;
    childrensize_ = 0u;
    issnapshotroot_ = false;
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
    if (has_snapshotcopy()) {
      if (snapshotcopy_ != NULL) snapshotcopy_->::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::Clear();
    }
    createdlistsize_ = 0u;
  }
  deletedinode_.Clear();
  deletedinoderef_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotDiffSection_DirectoryDiff::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 snapshotId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &snapshotid_)));
          set_has_snapshotid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_childrenSize;
        break;
      }

      // optional uint32 childrenSize = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_childrenSize:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &childrensize_)));
          set_has_childrensize();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_isSnapshotRoot;
        break;
      }

      // optional bool isSnapshotRoot = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_isSnapshotRoot:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issnapshotroot_)));
          set_has_issnapshotroot();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_name;
        break;
      }

      // optional bytes name = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_snapshotCopy;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory snapshotCopy = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_snapshotCopy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_snapshotcopy()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(48)) goto parse_createdListSize;
        break;
      }

      // optional uint32 createdListSize = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_createdListSize:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &createdlistsize_)));
          set_has_createdlistsize();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(58)) goto parse_deletedINode;
        break;
      }

      // repeated uint64 deletedINode = 7 [packed = true];
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_deletedINode:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_deletedinode())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 58, input, this->mutable_deletedinode())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(66)) goto parse_deletedINodeRef;
        break;
      }

      // repeated uint32 deletedINodeRef = 8 [packed = true];
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_deletedINodeRef:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_deletedinoderef())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 66, input, this->mutable_deletedinoderef())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SnapshotDiffSection_DirectoryDiff::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 snapshotId = 1;
  if (has_snapshotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->snapshotid(), output);
  }

  // optional uint32 childrenSize = 2;
  if (has_childrensize()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->childrensize(), output);
  }

  // optional bool isSnapshotRoot = 3;
  if (has_issnapshotroot()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->issnapshotroot(), output);
  }

  // optional bytes name = 4;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      4, this->name(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory snapshotCopy = 5;
  if (has_snapshotcopy()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->snapshotcopy(), output);
  }

  // optional uint32 createdListSize = 6;
  if (has_createdlistsize()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(6, this->createdlistsize(), output);
  }

  // repeated uint64 deletedINode = 7 [packed = true];
  if (this->deletedinode_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(7, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_deletedinode_cached_byte_size_);
  }
  for (int i = 0; i < this->deletedinode_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->deletedinode(i), output);
  }

  // repeated uint32 deletedINodeRef = 8 [packed = true];
  if (this->deletedinoderef_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(8, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_deletedinoderef_cached_byte_size_);
  }
  for (int i = 0; i < this->deletedinoderef_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->deletedinoderef(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotDiffSection_DirectoryDiff::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 snapshotId = 1;
  if (has_snapshotid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->snapshotid(), target);
  }

  // optional uint32 childrenSize = 2;
  if (has_childrensize()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->childrensize(), target);
  }

  // optional bool isSnapshotRoot = 3;
  if (has_issnapshotroot()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->issnapshotroot(), target);
  }

  // optional bytes name = 4;
  if (has_name()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->name(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory snapshotCopy = 5;
  if (has_snapshotcopy()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        5, this->snapshotcopy(), target);
  }

  // optional uint32 createdListSize = 6;
  if (has_createdlistsize()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(6, this->createdlistsize(), target);
  }

  // repeated uint64 deletedINode = 7 [packed = true];
  if (this->deletedinode_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      7,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _deletedinode_cached_byte_size_, target);
  }
  for (int i = 0; i < this->deletedinode_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->deletedinode(i), target);
  }

  // repeated uint32 deletedINodeRef = 8 [packed = true];
  if (this->deletedinoderef_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      8,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _deletedinoderef_cached_byte_size_, target);
  }
  for (int i = 0; i < this->deletedinoderef_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->deletedinoderef(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotDiffSection_DirectoryDiff::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 snapshotId = 1;
    if (has_snapshotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->snapshotid());
    }

    // optional uint32 childrenSize = 2;
    if (has_childrensize()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->childrensize());
    }

    // optional bool isSnapshotRoot = 3;
    if (has_issnapshotroot()) {
      total_size += 1 + 1;
    }

    // optional bytes name = 4;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->name());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory snapshotCopy = 5;
    if (has_snapshotcopy()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->snapshotcopy());
    }

    // optional uint32 createdListSize = 6;
    if (has_createdlistsize()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->createdlistsize());
    }

  }
  // repeated uint64 deletedINode = 7 [packed = true];
  {
    int data_size = 0;
    for (int i = 0; i < this->deletedinode_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->deletedinode(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _deletedinode_cached_byte_size_ = data_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 deletedINodeRef = 8 [packed = true];
  {
    int data_size = 0;
    for (int i = 0; i < this->deletedinoderef_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->deletedinoderef(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _deletedinoderef_cached_byte_size_ = data_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotDiffSection_DirectoryDiff::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotDiffSection_DirectoryDiff* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotDiffSection_DirectoryDiff*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotDiffSection_DirectoryDiff::MergeFrom(const SnapshotDiffSection_DirectoryDiff& from) {
  GOOGLE_CHECK_NE(&from, this);
  deletedinode_.MergeFrom(from.deletedinode_);
  deletedinoderef_.MergeFrom(from.deletedinoderef_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_snapshotid()) {
      set_snapshotid(from.snapshotid());
    }
    if (from.has_childrensize()) {
      set_childrensize(from.childrensize());
    }
    if (from.has_issnapshotroot()) {
      set_issnapshotroot(from.issnapshotroot());
    }
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_snapshotcopy()) {
      mutable_snapshotcopy()->::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::MergeFrom(from.snapshotcopy());
    }
    if (from.has_createdlistsize()) {
      set_createdlistsize(from.createdlistsize());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotDiffSection_DirectoryDiff::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotDiffSection_DirectoryDiff::CopyFrom(const SnapshotDiffSection_DirectoryDiff& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotDiffSection_DirectoryDiff::IsInitialized() const {

  if (has_snapshotcopy()) {
    if (!this->snapshotcopy().IsInitialized()) return false;
  }
  return true;
}

void SnapshotDiffSection_DirectoryDiff::Swap(SnapshotDiffSection_DirectoryDiff* other) {
  if (other != this) {
    std::swap(snapshotid_, other->snapshotid_);
    std::swap(childrensize_, other->childrensize_);
    std::swap(issnapshotroot_, other->issnapshotroot_);
    std::swap(name_, other->name_);
    std::swap(snapshotcopy_, other->snapshotcopy_);
    std::swap(createdlistsize_, other->createdlistsize_);
    deletedinode_.Swap(&other->deletedinode_);
    deletedinoderef_.Swap(&other->deletedinoderef_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotDiffSection_DirectoryDiff::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotDiffSection_DirectoryDiff_descriptor_;
  metadata.reflection = SnapshotDiffSection_DirectoryDiff_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int SnapshotDiffSection_FileDiff::kSnapshotIdFieldNumber;
const int SnapshotDiffSection_FileDiff::kFileSizeFieldNumber;
const int SnapshotDiffSection_FileDiff::kNameFieldNumber;
const int SnapshotDiffSection_FileDiff::kSnapshotCopyFieldNumber;
#endif  // !_MSC_VER

SnapshotDiffSection_FileDiff::SnapshotDiffSection_FileDiff()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotDiffSection_FileDiff::InitAsDefaultInstance() {
  snapshotcopy_ = const_cast< ::hadoop::hdfs::fsimage::INodeSection_INodeFile*>(&::hadoop::hdfs::fsimage::INodeSection_INodeFile::default_instance());
}

SnapshotDiffSection_FileDiff::SnapshotDiffSection_FileDiff(const SnapshotDiffSection_FileDiff& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotDiffSection_FileDiff::SharedCtor() {
  _cached_size_ = 0;
  snapshotid_ = 0u;
  filesize_ = GOOGLE_ULONGLONG(0);
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  snapshotcopy_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotDiffSection_FileDiff::~SnapshotDiffSection_FileDiff() {
  SharedDtor();
}

void SnapshotDiffSection_FileDiff::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
    delete snapshotcopy_;
  }
}

void SnapshotDiffSection_FileDiff::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotDiffSection_FileDiff::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotDiffSection_FileDiff_descriptor_;
}

const SnapshotDiffSection_FileDiff& SnapshotDiffSection_FileDiff::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotDiffSection_FileDiff* SnapshotDiffSection_FileDiff::default_instance_ = NULL;

SnapshotDiffSection_FileDiff* SnapshotDiffSection_FileDiff::New() const {
  return new SnapshotDiffSection_FileDiff;
}

void SnapshotDiffSection_FileDiff::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    snapshotid_ = 0u;
    filesize_ = GOOGLE_ULONGLONG(0);
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
    if (has_snapshotcopy()) {
      if (snapshotcopy_ != NULL) snapshotcopy_->::hadoop::hdfs::fsimage::INodeSection_INodeFile::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotDiffSection_FileDiff::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 snapshotId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &snapshotid_)));
          set_has_snapshotid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_fileSize;
        break;
      }

      // optional uint64 fileSize = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_fileSize:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &filesize_)));
          set_has_filesize();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_name;
        break;
      }

      // optional bytes name = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_name()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_snapshotCopy;
        break;
      }

      // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile snapshotCopy = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_snapshotCopy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_snapshotcopy()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SnapshotDiffSection_FileDiff::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 snapshotId = 1;
  if (has_snapshotid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->snapshotid(), output);
  }

  // optional uint64 fileSize = 2;
  if (has_filesize()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->filesize(), output);
  }

  // optional bytes name = 3;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      3, this->name(), output);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile snapshotCopy = 4;
  if (has_snapshotcopy()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->snapshotcopy(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotDiffSection_FileDiff::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 snapshotId = 1;
  if (has_snapshotid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->snapshotid(), target);
  }

  // optional uint64 fileSize = 2;
  if (has_filesize()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->filesize(), target);
  }

  // optional bytes name = 3;
  if (has_name()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->name(), target);
  }

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile snapshotCopy = 4;
  if (has_snapshotcopy()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->snapshotcopy(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotDiffSection_FileDiff::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 snapshotId = 1;
    if (has_snapshotid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->snapshotid());
    }

    // optional uint64 fileSize = 2;
    if (has_filesize()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->filesize());
    }

    // optional bytes name = 3;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->name());
    }

    // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile snapshotCopy = 4;
    if (has_snapshotcopy()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->snapshotcopy());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotDiffSection_FileDiff::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotDiffSection_FileDiff* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotDiffSection_FileDiff*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotDiffSection_FileDiff::MergeFrom(const SnapshotDiffSection_FileDiff& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_snapshotid()) {
      set_snapshotid(from.snapshotid());
    }
    if (from.has_filesize()) {
      set_filesize(from.filesize());
    }
    if (from.has_name()) {
      set_name(from.name());
    }
    if (from.has_snapshotcopy()) {
      mutable_snapshotcopy()->::hadoop::hdfs::fsimage::INodeSection_INodeFile::MergeFrom(from.snapshotcopy());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotDiffSection_FileDiff::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotDiffSection_FileDiff::CopyFrom(const SnapshotDiffSection_FileDiff& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotDiffSection_FileDiff::IsInitialized() const {

  if (has_snapshotcopy()) {
    if (!this->snapshotcopy().IsInitialized()) return false;
  }
  return true;
}

void SnapshotDiffSection_FileDiff::Swap(SnapshotDiffSection_FileDiff* other) {
  if (other != this) {
    std::swap(snapshotid_, other->snapshotid_);
    std::swap(filesize_, other->filesize_);
    std::swap(name_, other->name_);
    std::swap(snapshotcopy_, other->snapshotcopy_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotDiffSection_FileDiff::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotDiffSection_FileDiff_descriptor_;
  metadata.reflection = SnapshotDiffSection_FileDiff_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

const ::google::protobuf::EnumDescriptor* SnapshotDiffSection_DiffEntry_Type_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotDiffSection_DiffEntry_Type_descriptor_;
}
bool SnapshotDiffSection_DiffEntry_Type_IsValid(int value) {
  switch(value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry::FILEDIFF;
const SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry::DIRECTORYDIFF;
const SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry::Type_MIN;
const SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry::Type_MAX;
const int SnapshotDiffSection_DiffEntry::Type_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int SnapshotDiffSection_DiffEntry::kTypeFieldNumber;
const int SnapshotDiffSection_DiffEntry::kInodeIdFieldNumber;
const int SnapshotDiffSection_DiffEntry::kNumOfDiffFieldNumber;
#endif  // !_MSC_VER

SnapshotDiffSection_DiffEntry::SnapshotDiffSection_DiffEntry()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotDiffSection_DiffEntry::InitAsDefaultInstance() {
}

SnapshotDiffSection_DiffEntry::SnapshotDiffSection_DiffEntry(const SnapshotDiffSection_DiffEntry& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotDiffSection_DiffEntry::SharedCtor() {
  _cached_size_ = 0;
  type_ = 1;
  inodeid_ = GOOGLE_ULONGLONG(0);
  numofdiff_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotDiffSection_DiffEntry::~SnapshotDiffSection_DiffEntry() {
  SharedDtor();
}

void SnapshotDiffSection_DiffEntry::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SnapshotDiffSection_DiffEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotDiffSection_DiffEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotDiffSection_DiffEntry_descriptor_;
}

const SnapshotDiffSection_DiffEntry& SnapshotDiffSection_DiffEntry::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotDiffSection_DiffEntry* SnapshotDiffSection_DiffEntry::default_instance_ = NULL;

SnapshotDiffSection_DiffEntry* SnapshotDiffSection_DiffEntry::New() const {
  return new SnapshotDiffSection_DiffEntry;
}

void SnapshotDiffSection_DiffEntry::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    type_ = 1;
    inodeid_ = GOOGLE_ULONGLONG(0);
    numofdiff_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotDiffSection_DiffEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry.Type type = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_inodeId;
        break;
      }

      // optional uint64 inodeId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_inodeId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &inodeid_)));
          set_has_inodeid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_numOfDiff;
        break;
      }

      // optional uint32 numOfDiff = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numOfDiff:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numofdiff_)));
          set_has_numofdiff();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SnapshotDiffSection_DiffEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry.Type type = 1;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // optional uint64 inodeId = 2;
  if (has_inodeid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->inodeid(), output);
  }

  // optional uint32 numOfDiff = 3;
  if (has_numofdiff()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->numofdiff(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotDiffSection_DiffEntry::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry.Type type = 1;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // optional uint64 inodeId = 2;
  if (has_inodeid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->inodeid(), target);
  }

  // optional uint32 numOfDiff = 3;
  if (has_numofdiff()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->numofdiff(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotDiffSection_DiffEntry::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry.Type type = 1;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // optional uint64 inodeId = 2;
    if (has_inodeid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->inodeid());
    }

    // optional uint32 numOfDiff = 3;
    if (has_numofdiff()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numofdiff());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotDiffSection_DiffEntry::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotDiffSection_DiffEntry* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotDiffSection_DiffEntry*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotDiffSection_DiffEntry::MergeFrom(const SnapshotDiffSection_DiffEntry& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_inodeid()) {
      set_inodeid(from.inodeid());
    }
    if (from.has_numofdiff()) {
      set_numofdiff(from.numofdiff());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotDiffSection_DiffEntry::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotDiffSection_DiffEntry::CopyFrom(const SnapshotDiffSection_DiffEntry& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotDiffSection_DiffEntry::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void SnapshotDiffSection_DiffEntry::Swap(SnapshotDiffSection_DiffEntry* other) {
  if (other != this) {
    std::swap(type_, other->type_);
    std::swap(inodeid_, other->inodeid_);
    std::swap(numofdiff_, other->numofdiff_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotDiffSection_DiffEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotDiffSection_DiffEntry_descriptor_;
  metadata.reflection = SnapshotDiffSection_DiffEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
#endif  // !_MSC_VER

SnapshotDiffSection::SnapshotDiffSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SnapshotDiffSection::InitAsDefaultInstance() {
}

SnapshotDiffSection::SnapshotDiffSection(const SnapshotDiffSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SnapshotDiffSection::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SnapshotDiffSection::~SnapshotDiffSection() {
  SharedDtor();
}

void SnapshotDiffSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SnapshotDiffSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SnapshotDiffSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SnapshotDiffSection_descriptor_;
}

const SnapshotDiffSection& SnapshotDiffSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SnapshotDiffSection* SnapshotDiffSection::default_instance_ = NULL;

SnapshotDiffSection* SnapshotDiffSection::New() const {
  return new SnapshotDiffSection;
}

void SnapshotDiffSection::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SnapshotDiffSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void SnapshotDiffSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SnapshotDiffSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SnapshotDiffSection::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SnapshotDiffSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SnapshotDiffSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SnapshotDiffSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SnapshotDiffSection::MergeFrom(const SnapshotDiffSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SnapshotDiffSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SnapshotDiffSection::CopyFrom(const SnapshotDiffSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapshotDiffSection::IsInitialized() const {

  return true;
}

void SnapshotDiffSection::Swap(SnapshotDiffSection* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SnapshotDiffSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SnapshotDiffSection_descriptor_;
  metadata.reflection = SnapshotDiffSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int StringTableSection_Entry::kIdFieldNumber;
const int StringTableSection_Entry::kStrFieldNumber;
#endif  // !_MSC_VER

StringTableSection_Entry::StringTableSection_Entry()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void StringTableSection_Entry::InitAsDefaultInstance() {
}

StringTableSection_Entry::StringTableSection_Entry(const StringTableSection_Entry& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void StringTableSection_Entry::SharedCtor() {
  _cached_size_ = 0;
  id_ = 0u;
  str_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

StringTableSection_Entry::~StringTableSection_Entry() {
  SharedDtor();
}

void StringTableSection_Entry::SharedDtor() {
  if (str_ != &::google::protobuf::internal::kEmptyString) {
    delete str_;
  }
  if (this != default_instance_) {
  }
}

void StringTableSection_Entry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StringTableSection_Entry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StringTableSection_Entry_descriptor_;
}

const StringTableSection_Entry& StringTableSection_Entry::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

StringTableSection_Entry* StringTableSection_Entry::default_instance_ = NULL;

StringTableSection_Entry* StringTableSection_Entry::New() const {
  return new StringTableSection_Entry;
}

void StringTableSection_Entry::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = 0u;
    if (has_str()) {
      if (str_ != &::google::protobuf::internal::kEmptyString) {
        str_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool StringTableSection_Entry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_str;
        break;
      }

      // optional string str = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_str:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->str().data(), this->str().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void StringTableSection_Entry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->id(), output);
  }

  // optional string str = 2;
  if (has_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->str(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* StringTableSection_Entry::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->id(), target);
  }

  // optional string str = 2;
  if (has_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int StringTableSection_Entry::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->id());
    }

    // optional string str = 2;
    if (has_str()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StringTableSection_Entry::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const StringTableSection_Entry* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const StringTableSection_Entry*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void StringTableSection_Entry::MergeFrom(const StringTableSection_Entry& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
    if (from.has_str()) {
      set_str(from.str());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void StringTableSection_Entry::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringTableSection_Entry::CopyFrom(const StringTableSection_Entry& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringTableSection_Entry::IsInitialized() const {

  return true;
}

void StringTableSection_Entry::Swap(StringTableSection_Entry* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(str_, other->str_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata StringTableSection_Entry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StringTableSection_Entry_descriptor_;
  metadata.reflection = StringTableSection_Entry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int StringTableSection::kNumEntryFieldNumber;
#endif  // !_MSC_VER

StringTableSection::StringTableSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void StringTableSection::InitAsDefaultInstance() {
}

StringTableSection::StringTableSection(const StringTableSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void StringTableSection::SharedCtor() {
  _cached_size_ = 0;
  numentry_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

StringTableSection::~StringTableSection() {
  SharedDtor();
}

void StringTableSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void StringTableSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StringTableSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StringTableSection_descriptor_;
}

const StringTableSection& StringTableSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

StringTableSection* StringTableSection::default_instance_ = NULL;

StringTableSection* StringTableSection::New() const {
  return new StringTableSection;
}

void StringTableSection::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    numentry_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool StringTableSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 numEntry = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numentry_)));
          set_has_numentry();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void StringTableSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 numEntry = 1;
  if (has_numentry()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->numentry(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* StringTableSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 numEntry = 1;
  if (has_numentry()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->numentry(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int StringTableSection::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 numEntry = 1;
    if (has_numentry()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numentry());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StringTableSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const StringTableSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const StringTableSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void StringTableSection::MergeFrom(const StringTableSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_numentry()) {
      set_numentry(from.numentry());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void StringTableSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringTableSection::CopyFrom(const StringTableSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringTableSection::IsInitialized() const {

  return true;
}

void StringTableSection::Swap(StringTableSection* other) {
  if (other != this) {
    std::swap(numentry_, other->numentry_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata StringTableSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StringTableSection_descriptor_;
  metadata.reflection = StringTableSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SecretManagerSection_DelegationKey::kIdFieldNumber;
const int SecretManagerSection_DelegationKey::kExpiryDateFieldNumber;
const int SecretManagerSection_DelegationKey::kKeyFieldNumber;
#endif  // !_MSC_VER

SecretManagerSection_DelegationKey::SecretManagerSection_DelegationKey()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SecretManagerSection_DelegationKey::InitAsDefaultInstance() {
}

SecretManagerSection_DelegationKey::SecretManagerSection_DelegationKey(const SecretManagerSection_DelegationKey& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SecretManagerSection_DelegationKey::SharedCtor() {
  _cached_size_ = 0;
  id_ = 0u;
  expirydate_ = GOOGLE_ULONGLONG(0);
  key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SecretManagerSection_DelegationKey::~SecretManagerSection_DelegationKey() {
  SharedDtor();
}

void SecretManagerSection_DelegationKey::SharedDtor() {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    delete key_;
  }
  if (this != default_instance_) {
  }
}

void SecretManagerSection_DelegationKey::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SecretManagerSection_DelegationKey::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SecretManagerSection_DelegationKey_descriptor_;
}

const SecretManagerSection_DelegationKey& SecretManagerSection_DelegationKey::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SecretManagerSection_DelegationKey* SecretManagerSection_DelegationKey::default_instance_ = NULL;

SecretManagerSection_DelegationKey* SecretManagerSection_DelegationKey::New() const {
  return new SecretManagerSection_DelegationKey;
}

void SecretManagerSection_DelegationKey::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = 0u;
    expirydate_ = GOOGLE_ULONGLONG(0);
    if (has_key()) {
      if (key_ != &::google::protobuf::internal::kEmptyString) {
        key_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SecretManagerSection_DelegationKey::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_expiryDate;
        break;
      }

      // optional uint64 expiryDate = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_expiryDate:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &expirydate_)));
          set_has_expirydate();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_key;
        break;
      }

      // optional bytes key = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_key:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_key()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SecretManagerSection_DelegationKey::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->id(), output);
  }

  // optional uint64 expiryDate = 2;
  if (has_expirydate()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->expirydate(), output);
  }

  // optional bytes key = 3;
  if (has_key()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      3, this->key(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SecretManagerSection_DelegationKey::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->id(), target);
  }

  // optional uint64 expiryDate = 2;
  if (has_expirydate()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->expirydate(), target);
  }

  // optional bytes key = 3;
  if (has_key()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->key(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SecretManagerSection_DelegationKey::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->id());
    }

    // optional uint64 expiryDate = 2;
    if (has_expirydate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->expirydate());
    }

    // optional bytes key = 3;
    if (has_key()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->key());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SecretManagerSection_DelegationKey::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SecretManagerSection_DelegationKey* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SecretManagerSection_DelegationKey*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SecretManagerSection_DelegationKey::MergeFrom(const SecretManagerSection_DelegationKey& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
    if (from.has_expirydate()) {
      set_expirydate(from.expirydate());
    }
    if (from.has_key()) {
      set_key(from.key());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SecretManagerSection_DelegationKey::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SecretManagerSection_DelegationKey::CopyFrom(const SecretManagerSection_DelegationKey& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SecretManagerSection_DelegationKey::IsInitialized() const {

  return true;
}

void SecretManagerSection_DelegationKey::Swap(SecretManagerSection_DelegationKey* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(expirydate_, other->expirydate_);
    std::swap(key_, other->key_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SecretManagerSection_DelegationKey::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SecretManagerSection_DelegationKey_descriptor_;
  metadata.reflection = SecretManagerSection_DelegationKey_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int SecretManagerSection_PersistToken::kVersionFieldNumber;
const int SecretManagerSection_PersistToken::kOwnerFieldNumber;
const int SecretManagerSection_PersistToken::kRenewerFieldNumber;
const int SecretManagerSection_PersistToken::kRealUserFieldNumber;
const int SecretManagerSection_PersistToken::kIssueDateFieldNumber;
const int SecretManagerSection_PersistToken::kMaxDateFieldNumber;
const int SecretManagerSection_PersistToken::kSequenceNumberFieldNumber;
const int SecretManagerSection_PersistToken::kMasterKeyIdFieldNumber;
const int SecretManagerSection_PersistToken::kExpiryDateFieldNumber;
#endif  // !_MSC_VER

SecretManagerSection_PersistToken::SecretManagerSection_PersistToken()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SecretManagerSection_PersistToken::InitAsDefaultInstance() {
}

SecretManagerSection_PersistToken::SecretManagerSection_PersistToken(const SecretManagerSection_PersistToken& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SecretManagerSection_PersistToken::SharedCtor() {
  _cached_size_ = 0;
  version_ = 0u;
  owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  renewer_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  realuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  issuedate_ = GOOGLE_ULONGLONG(0);
  maxdate_ = GOOGLE_ULONGLONG(0);
  sequencenumber_ = 0u;
  masterkeyid_ = 0u;
  expirydate_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SecretManagerSection_PersistToken::~SecretManagerSection_PersistToken() {
  SharedDtor();
}

void SecretManagerSection_PersistToken::SharedDtor() {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    delete owner_;
  }
  if (renewer_ != &::google::protobuf::internal::kEmptyString) {
    delete renewer_;
  }
  if (realuser_ != &::google::protobuf::internal::kEmptyString) {
    delete realuser_;
  }
  if (this != default_instance_) {
  }
}

void SecretManagerSection_PersistToken::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SecretManagerSection_PersistToken::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SecretManagerSection_PersistToken_descriptor_;
}

const SecretManagerSection_PersistToken& SecretManagerSection_PersistToken::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SecretManagerSection_PersistToken* SecretManagerSection_PersistToken::default_instance_ = NULL;

SecretManagerSection_PersistToken* SecretManagerSection_PersistToken::New() const {
  return new SecretManagerSection_PersistToken;
}

void SecretManagerSection_PersistToken::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    version_ = 0u;
    if (has_owner()) {
      if (owner_ != &::google::protobuf::internal::kEmptyString) {
        owner_->clear();
      }
    }
    if (has_renewer()) {
      if (renewer_ != &::google::protobuf::internal::kEmptyString) {
        renewer_->clear();
      }
    }
    if (has_realuser()) {
      if (realuser_ != &::google::protobuf::internal::kEmptyString) {
        realuser_->clear();
      }
    }
    issuedate_ = GOOGLE_ULONGLONG(0);
    maxdate_ = GOOGLE_ULONGLONG(0);
    sequencenumber_ = 0u;
    masterkeyid_ = 0u;
  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    expirydate_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SecretManagerSection_PersistToken::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 version = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &version_)));
          set_has_version();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_owner;
        break;
      }

      // optional string owner = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_owner:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_owner()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->owner().data(), this->owner().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_renewer;
        break;
      }

      // optional string renewer = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_renewer:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_renewer()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->renewer().data(), this->renewer().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_realUser;
        break;
      }

      // optional string realUser = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_realUser:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_realuser()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->realuser().data(), this->realuser().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_issueDate;
        break;
      }

      // optional uint64 issueDate = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_issueDate:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &issuedate_)));
          set_has_issuedate();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(48)) goto parse_maxDate;
        break;
      }

      // optional uint64 maxDate = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_maxDate:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &maxdate_)));
          set_has_maxdate();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(56)) goto parse_sequenceNumber;
        break;
      }

      // optional uint32 sequenceNumber = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_sequenceNumber:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &sequencenumber_)));
          set_has_sequencenumber();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(64)) goto parse_masterKeyId;
        break;
      }

      // optional uint32 masterKeyId = 8;
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_masterKeyId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &masterkeyid_)));
          set_has_masterkeyid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(72)) goto parse_expiryDate;
        break;
      }

      // optional uint64 expiryDate = 9;
      case 9: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_expiryDate:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &expirydate_)));
          set_has_expirydate();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SecretManagerSection_PersistToken::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 version = 1;
  if (has_version()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->version(), output);
  }

  // optional string owner = 2;
  if (has_owner()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->owner().data(), this->owner().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->owner(), output);
  }

  // optional string renewer = 3;
  if (has_renewer()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->renewer().data(), this->renewer().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->renewer(), output);
  }

  // optional string realUser = 4;
  if (has_realuser()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->realuser().data(), this->realuser().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->realuser(), output);
  }

  // optional uint64 issueDate = 5;
  if (has_issuedate()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(5, this->issuedate(), output);
  }

  // optional uint64 maxDate = 6;
  if (has_maxdate()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(6, this->maxdate(), output);
  }

  // optional uint32 sequenceNumber = 7;
  if (has_sequencenumber()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(7, this->sequencenumber(), output);
  }

  // optional uint32 masterKeyId = 8;
  if (has_masterkeyid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(8, this->masterkeyid(), output);
  }

  // optional uint64 expiryDate = 9;
  if (has_expirydate()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(9, this->expirydate(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SecretManagerSection_PersistToken::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 version = 1;
  if (has_version()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->version(), target);
  }

  // optional string owner = 2;
  if (has_owner()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->owner().data(), this->owner().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->owner(), target);
  }

  // optional string renewer = 3;
  if (has_renewer()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->renewer().data(), this->renewer().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->renewer(), target);
  }

  // optional string realUser = 4;
  if (has_realuser()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->realuser().data(), this->realuser().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->realuser(), target);
  }

  // optional uint64 issueDate = 5;
  if (has_issuedate()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(5, this->issuedate(), target);
  }

  // optional uint64 maxDate = 6;
  if (has_maxdate()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(6, this->maxdate(), target);
  }

  // optional uint32 sequenceNumber = 7;
  if (has_sequencenumber()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(7, this->sequencenumber(), target);
  }

  // optional uint32 masterKeyId = 8;
  if (has_masterkeyid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(8, this->masterkeyid(), target);
  }

  // optional uint64 expiryDate = 9;
  if (has_expirydate()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(9, this->expirydate(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SecretManagerSection_PersistToken::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 version = 1;
    if (has_version()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->version());
    }

    // optional string owner = 2;
    if (has_owner()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->owner());
    }

    // optional string renewer = 3;
    if (has_renewer()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->renewer());
    }

    // optional string realUser = 4;
    if (has_realuser()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->realuser());
    }

    // optional uint64 issueDate = 5;
    if (has_issuedate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->issuedate());
    }

    // optional uint64 maxDate = 6;
    if (has_maxdate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->maxdate());
    }

    // optional uint32 sequenceNumber = 7;
    if (has_sequencenumber()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->sequencenumber());
    }

    // optional uint32 masterKeyId = 8;
    if (has_masterkeyid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->masterkeyid());
    }

  }
  if (_has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    // optional uint64 expiryDate = 9;
    if (has_expirydate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->expirydate());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SecretManagerSection_PersistToken::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SecretManagerSection_PersistToken* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SecretManagerSection_PersistToken*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SecretManagerSection_PersistToken::MergeFrom(const SecretManagerSection_PersistToken& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_version()) {
      set_version(from.version());
    }
    if (from.has_owner()) {
      set_owner(from.owner());
    }
    if (from.has_renewer()) {
      set_renewer(from.renewer());
    }
    if (from.has_realuser()) {
      set_realuser(from.realuser());
    }
    if (from.has_issuedate()) {
      set_issuedate(from.issuedate());
    }
    if (from.has_maxdate()) {
      set_maxdate(from.maxdate());
    }
    if (from.has_sequencenumber()) {
      set_sequencenumber(from.sequencenumber());
    }
    if (from.has_masterkeyid()) {
      set_masterkeyid(from.masterkeyid());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_expirydate()) {
      set_expirydate(from.expirydate());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SecretManagerSection_PersistToken::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SecretManagerSection_PersistToken::CopyFrom(const SecretManagerSection_PersistToken& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SecretManagerSection_PersistToken::IsInitialized() const {

  return true;
}

void SecretManagerSection_PersistToken::Swap(SecretManagerSection_PersistToken* other) {
  if (other != this) {
    std::swap(version_, other->version_);
    std::swap(owner_, other->owner_);
    std::swap(renewer_, other->renewer_);
    std::swap(realuser_, other->realuser_);
    std::swap(issuedate_, other->issuedate_);
    std::swap(maxdate_, other->maxdate_);
    std::swap(sequencenumber_, other->sequencenumber_);
    std::swap(masterkeyid_, other->masterkeyid_);
    std::swap(expirydate_, other->expirydate_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SecretManagerSection_PersistToken::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SecretManagerSection_PersistToken_descriptor_;
  metadata.reflection = SecretManagerSection_PersistToken_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int SecretManagerSection::kCurrentIdFieldNumber;
const int SecretManagerSection::kTokenSequenceNumberFieldNumber;
const int SecretManagerSection::kNumKeysFieldNumber;
const int SecretManagerSection::kNumTokensFieldNumber;
#endif  // !_MSC_VER

SecretManagerSection::SecretManagerSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SecretManagerSection::InitAsDefaultInstance() {
}

SecretManagerSection::SecretManagerSection(const SecretManagerSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SecretManagerSection::SharedCtor() {
  _cached_size_ = 0;
  currentid_ = 0u;
  tokensequencenumber_ = 0u;
  numkeys_ = 0u;
  numtokens_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SecretManagerSection::~SecretManagerSection() {
  SharedDtor();
}

void SecretManagerSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SecretManagerSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SecretManagerSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SecretManagerSection_descriptor_;
}

const SecretManagerSection& SecretManagerSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

SecretManagerSection* SecretManagerSection::default_instance_ = NULL;

SecretManagerSection* SecretManagerSection::New() const {
  return new SecretManagerSection;
}

void SecretManagerSection::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    currentid_ = 0u;
    tokensequencenumber_ = 0u;
    numkeys_ = 0u;
    numtokens_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SecretManagerSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 currentId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &currentid_)));
          set_has_currentid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_tokenSequenceNumber;
        break;
      }

      // optional uint32 tokenSequenceNumber = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_tokenSequenceNumber:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &tokensequencenumber_)));
          set_has_tokensequencenumber();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_numKeys;
        break;
      }

      // optional uint32 numKeys = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numKeys:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numkeys_)));
          set_has_numkeys();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_numTokens;
        break;
      }

      // optional uint32 numTokens = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numTokens:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numtokens_)));
          set_has_numtokens();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SecretManagerSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 currentId = 1;
  if (has_currentid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->currentid(), output);
  }

  // optional uint32 tokenSequenceNumber = 2;
  if (has_tokensequencenumber()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->tokensequencenumber(), output);
  }

  // optional uint32 numKeys = 3;
  if (has_numkeys()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->numkeys(), output);
  }

  // optional uint32 numTokens = 4;
  if (has_numtokens()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(4, this->numtokens(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SecretManagerSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 currentId = 1;
  if (has_currentid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->currentid(), target);
  }

  // optional uint32 tokenSequenceNumber = 2;
  if (has_tokensequencenumber()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->tokensequencenumber(), target);
  }

  // optional uint32 numKeys = 3;
  if (has_numkeys()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->numkeys(), target);
  }

  // optional uint32 numTokens = 4;
  if (has_numtokens()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(4, this->numtokens(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SecretManagerSection::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 currentId = 1;
    if (has_currentid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->currentid());
    }

    // optional uint32 tokenSequenceNumber = 2;
    if (has_tokensequencenumber()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->tokensequencenumber());
    }

    // optional uint32 numKeys = 3;
    if (has_numkeys()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numkeys());
    }

    // optional uint32 numTokens = 4;
    if (has_numtokens()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numtokens());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SecretManagerSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SecretManagerSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SecretManagerSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SecretManagerSection::MergeFrom(const SecretManagerSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_currentid()) {
      set_currentid(from.currentid());
    }
    if (from.has_tokensequencenumber()) {
      set_tokensequencenumber(from.tokensequencenumber());
    }
    if (from.has_numkeys()) {
      set_numkeys(from.numkeys());
    }
    if (from.has_numtokens()) {
      set_numtokens(from.numtokens());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SecretManagerSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SecretManagerSection::CopyFrom(const SecretManagerSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SecretManagerSection::IsInitialized() const {

  return true;
}

void SecretManagerSection::Swap(SecretManagerSection* other) {
  if (other != this) {
    std::swap(currentid_, other->currentid_);
    std::swap(tokensequencenumber_, other->tokensequencenumber_);
    std::swap(numkeys_, other->numkeys_);
    std::swap(numtokens_, other->numtokens_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SecretManagerSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SecretManagerSection_descriptor_;
  metadata.reflection = SecretManagerSection_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CacheManagerSection::kNextDirectiveIdFieldNumber;
const int CacheManagerSection::kNumPoolsFieldNumber;
const int CacheManagerSection::kNumDirectivesFieldNumber;
#endif  // !_MSC_VER

CacheManagerSection::CacheManagerSection()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CacheManagerSection::InitAsDefaultInstance() {
}

CacheManagerSection::CacheManagerSection(const CacheManagerSection& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CacheManagerSection::SharedCtor() {
  _cached_size_ = 0;
  nextdirectiveid_ = GOOGLE_ULONGLONG(0);
  numpools_ = 0u;
  numdirectives_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CacheManagerSection::~CacheManagerSection() {
  SharedDtor();
}

void CacheManagerSection::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CacheManagerSection::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CacheManagerSection::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CacheManagerSection_descriptor_;
}

const CacheManagerSection& CacheManagerSection::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_fsimage_2eproto();
  return *default_instance_;
}

CacheManagerSection* CacheManagerSection::default_instance_ = NULL;

CacheManagerSection* CacheManagerSection::New() const {
  return new CacheManagerSection;
}

void CacheManagerSection::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    nextdirectiveid_ = GOOGLE_ULONGLONG(0);
    numpools_ = 0u;
    numdirectives_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CacheManagerSection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 nextDirectiveId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &nextdirectiveid_)));
          set_has_nextdirectiveid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_numPools;
        break;
      }

      // required uint32 numPools = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numPools:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numpools_)));
          set_has_numpools();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_numDirectives;
        break;
      }

      // required uint32 numDirectives = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numDirectives:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numdirectives_)));
          set_has_numdirectives();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CacheManagerSection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 nextDirectiveId = 1;
  if (has_nextdirectiveid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->nextdirectiveid(), output);
  }

  // required uint32 numPools = 2;
  if (has_numpools()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->numpools(), output);
  }

  // required uint32 numDirectives = 3;
  if (has_numdirectives()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->numdirectives(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CacheManagerSection::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 nextDirectiveId = 1;
  if (has_nextdirectiveid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->nextdirectiveid(), target);
  }

  // required uint32 numPools = 2;
  if (has_numpools()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->numpools(), target);
  }

  // required uint32 numDirectives = 3;
  if (has_numdirectives()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->numdirectives(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CacheManagerSection::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 nextDirectiveId = 1;
    if (has_nextdirectiveid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->nextdirectiveid());
    }

    // required uint32 numPools = 2;
    if (has_numpools()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numpools());
    }

    // required uint32 numDirectives = 3;
    if (has_numdirectives()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numdirectives());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CacheManagerSection::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CacheManagerSection* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CacheManagerSection*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CacheManagerSection::MergeFrom(const CacheManagerSection& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_nextdirectiveid()) {
      set_nextdirectiveid(from.nextdirectiveid());
    }
    if (from.has_numpools()) {
      set_numpools(from.numpools());
    }
    if (from.has_numdirectives()) {
      set_numdirectives(from.numdirectives());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CacheManagerSection::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CacheManagerSection::CopyFrom(const CacheManagerSection& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CacheManagerSection::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void CacheManagerSection::Swap(CacheManagerSection* other) {
  if (other != this) {
    std::swap(nextdirectiveid_, other->nextdirectiveid_);
    std::swap(numpools_, other->numpools_);
    std::swap(numdirectives_, other->numdirectives_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CacheManagerSection::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CacheManagerSection_descriptor_;
  metadata.reflection = CacheManagerSection_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace fsimage
}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
