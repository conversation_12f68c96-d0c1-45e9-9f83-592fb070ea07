// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: JournalProtocol.proto

#ifndef PROTOBUF_JournalProtocol_2eproto__INCLUDED
#define PROTOBUF_JournalProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_JournalProtocol_2eproto();
void protobuf_AssignDesc_JournalProtocol_2eproto();
void protobuf_ShutdownFile_JournalProtocol_2eproto();

class JournalInfoProto;
class JournalRequestProto;
class JournalResponseProto;
class StartLogSegmentRequestProto;
class StartLogSegmentResponseProto;
class FenceRequestProto;
class FenceResponseProto;

// ===================================================================

class JournalInfoProto : public ::google::protobuf::Message {
 public:
  JournalInfoProto();
  virtual ~JournalInfoProto();

  JournalInfoProto(const JournalInfoProto& from);

  inline JournalInfoProto& operator=(const JournalInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JournalInfoProto& default_instance();

  void Swap(JournalInfoProto* other);

  // implements Message ----------------------------------------------

  JournalInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JournalInfoProto& from);
  void MergeFrom(const JournalInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string clusterID = 1;
  inline bool has_clusterid() const;
  inline void clear_clusterid();
  static const int kClusterIDFieldNumber = 1;
  inline const ::std::string& clusterid() const;
  inline void set_clusterid(const ::std::string& value);
  inline void set_clusterid(const char* value);
  inline void set_clusterid(const char* value, size_t size);
  inline ::std::string* mutable_clusterid();
  inline ::std::string* release_clusterid();
  inline void set_allocated_clusterid(::std::string* clusterid);

  // optional uint32 layoutVersion = 2;
  inline bool has_layoutversion() const;
  inline void clear_layoutversion();
  static const int kLayoutVersionFieldNumber = 2;
  inline ::google::protobuf::uint32 layoutversion() const;
  inline void set_layoutversion(::google::protobuf::uint32 value);

  // optional uint32 namespaceID = 3;
  inline bool has_namespaceid() const;
  inline void clear_namespaceid();
  static const int kNamespaceIDFieldNumber = 3;
  inline ::google::protobuf::uint32 namespaceid() const;
  inline void set_namespaceid(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.JournalInfoProto)
 private:
  inline void set_has_clusterid();
  inline void clear_has_clusterid();
  inline void set_has_layoutversion();
  inline void clear_has_layoutversion();
  inline void set_has_namespaceid();
  inline void clear_has_namespaceid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* clusterid_;
  ::google::protobuf::uint32 layoutversion_;
  ::google::protobuf::uint32 namespaceid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static JournalInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class JournalRequestProto : public ::google::protobuf::Message {
 public:
  JournalRequestProto();
  virtual ~JournalRequestProto();

  JournalRequestProto(const JournalRequestProto& from);

  inline JournalRequestProto& operator=(const JournalRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JournalRequestProto& default_instance();

  void Swap(JournalRequestProto* other);

  // implements Message ----------------------------------------------

  JournalRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JournalRequestProto& from);
  void MergeFrom(const JournalRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.JournalInfoProto journalInfo = 1;
  inline bool has_journalinfo() const;
  inline void clear_journalinfo();
  static const int kJournalInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::JournalInfoProto& journalinfo() const;
  inline ::hadoop::hdfs::JournalInfoProto* mutable_journalinfo();
  inline ::hadoop::hdfs::JournalInfoProto* release_journalinfo();
  inline void set_allocated_journalinfo(::hadoop::hdfs::JournalInfoProto* journalinfo);

  // required uint64 firstTxnId = 2;
  inline bool has_firsttxnid() const;
  inline void clear_firsttxnid();
  static const int kFirstTxnIdFieldNumber = 2;
  inline ::google::protobuf::uint64 firsttxnid() const;
  inline void set_firsttxnid(::google::protobuf::uint64 value);

  // required uint32 numTxns = 3;
  inline bool has_numtxns() const;
  inline void clear_numtxns();
  static const int kNumTxnsFieldNumber = 3;
  inline ::google::protobuf::uint32 numtxns() const;
  inline void set_numtxns(::google::protobuf::uint32 value);

  // required bytes records = 4;
  inline bool has_records() const;
  inline void clear_records();
  static const int kRecordsFieldNumber = 4;
  inline const ::std::string& records() const;
  inline void set_records(const ::std::string& value);
  inline void set_records(const char* value);
  inline void set_records(const void* value, size_t size);
  inline ::std::string* mutable_records();
  inline ::std::string* release_records();
  inline void set_allocated_records(::std::string* records);

  // required uint64 epoch = 5;
  inline bool has_epoch() const;
  inline void clear_epoch();
  static const int kEpochFieldNumber = 5;
  inline ::google::protobuf::uint64 epoch() const;
  inline void set_epoch(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.JournalRequestProto)
 private:
  inline void set_has_journalinfo();
  inline void clear_has_journalinfo();
  inline void set_has_firsttxnid();
  inline void clear_has_firsttxnid();
  inline void set_has_numtxns();
  inline void clear_has_numtxns();
  inline void set_has_records();
  inline void clear_has_records();
  inline void set_has_epoch();
  inline void clear_has_epoch();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::JournalInfoProto* journalinfo_;
  ::google::protobuf::uint64 firsttxnid_;
  ::std::string* records_;
  ::google::protobuf::uint64 epoch_;
  ::google::protobuf::uint32 numtxns_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static JournalRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class JournalResponseProto : public ::google::protobuf::Message {
 public:
  JournalResponseProto();
  virtual ~JournalResponseProto();

  JournalResponseProto(const JournalResponseProto& from);

  inline JournalResponseProto& operator=(const JournalResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JournalResponseProto& default_instance();

  void Swap(JournalResponseProto* other);

  // implements Message ----------------------------------------------

  JournalResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JournalResponseProto& from);
  void MergeFrom(const JournalResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.JournalResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static JournalResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class StartLogSegmentRequestProto : public ::google::protobuf::Message {
 public:
  StartLogSegmentRequestProto();
  virtual ~StartLogSegmentRequestProto();

  StartLogSegmentRequestProto(const StartLogSegmentRequestProto& from);

  inline StartLogSegmentRequestProto& operator=(const StartLogSegmentRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StartLogSegmentRequestProto& default_instance();

  void Swap(StartLogSegmentRequestProto* other);

  // implements Message ----------------------------------------------

  StartLogSegmentRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StartLogSegmentRequestProto& from);
  void MergeFrom(const StartLogSegmentRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.JournalInfoProto journalInfo = 1;
  inline bool has_journalinfo() const;
  inline void clear_journalinfo();
  static const int kJournalInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::JournalInfoProto& journalinfo() const;
  inline ::hadoop::hdfs::JournalInfoProto* mutable_journalinfo();
  inline ::hadoop::hdfs::JournalInfoProto* release_journalinfo();
  inline void set_allocated_journalinfo(::hadoop::hdfs::JournalInfoProto* journalinfo);

  // required uint64 txid = 2;
  inline bool has_txid() const;
  inline void clear_txid();
  static const int kTxidFieldNumber = 2;
  inline ::google::protobuf::uint64 txid() const;
  inline void set_txid(::google::protobuf::uint64 value);

  // required uint64 epoch = 3;
  inline bool has_epoch() const;
  inline void clear_epoch();
  static const int kEpochFieldNumber = 3;
  inline ::google::protobuf::uint64 epoch() const;
  inline void set_epoch(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.StartLogSegmentRequestProto)
 private:
  inline void set_has_journalinfo();
  inline void clear_has_journalinfo();
  inline void set_has_txid();
  inline void clear_has_txid();
  inline void set_has_epoch();
  inline void clear_has_epoch();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::JournalInfoProto* journalinfo_;
  ::google::protobuf::uint64 txid_;
  ::google::protobuf::uint64 epoch_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static StartLogSegmentRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class StartLogSegmentResponseProto : public ::google::protobuf::Message {
 public:
  StartLogSegmentResponseProto();
  virtual ~StartLogSegmentResponseProto();

  StartLogSegmentResponseProto(const StartLogSegmentResponseProto& from);

  inline StartLogSegmentResponseProto& operator=(const StartLogSegmentResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StartLogSegmentResponseProto& default_instance();

  void Swap(StartLogSegmentResponseProto* other);

  // implements Message ----------------------------------------------

  StartLogSegmentResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StartLogSegmentResponseProto& from);
  void MergeFrom(const StartLogSegmentResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.StartLogSegmentResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static StartLogSegmentResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class FenceRequestProto : public ::google::protobuf::Message {
 public:
  FenceRequestProto();
  virtual ~FenceRequestProto();

  FenceRequestProto(const FenceRequestProto& from);

  inline FenceRequestProto& operator=(const FenceRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FenceRequestProto& default_instance();

  void Swap(FenceRequestProto* other);

  // implements Message ----------------------------------------------

  FenceRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FenceRequestProto& from);
  void MergeFrom(const FenceRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.JournalInfoProto journalInfo = 1;
  inline bool has_journalinfo() const;
  inline void clear_journalinfo();
  static const int kJournalInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::JournalInfoProto& journalinfo() const;
  inline ::hadoop::hdfs::JournalInfoProto* mutable_journalinfo();
  inline ::hadoop::hdfs::JournalInfoProto* release_journalinfo();
  inline void set_allocated_journalinfo(::hadoop::hdfs::JournalInfoProto* journalinfo);

  // required uint64 epoch = 2;
  inline bool has_epoch() const;
  inline void clear_epoch();
  static const int kEpochFieldNumber = 2;
  inline ::google::protobuf::uint64 epoch() const;
  inline void set_epoch(::google::protobuf::uint64 value);

  // optional string fencerInfo = 3;
  inline bool has_fencerinfo() const;
  inline void clear_fencerinfo();
  static const int kFencerInfoFieldNumber = 3;
  inline const ::std::string& fencerinfo() const;
  inline void set_fencerinfo(const ::std::string& value);
  inline void set_fencerinfo(const char* value);
  inline void set_fencerinfo(const char* value, size_t size);
  inline ::std::string* mutable_fencerinfo();
  inline ::std::string* release_fencerinfo();
  inline void set_allocated_fencerinfo(::std::string* fencerinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.FenceRequestProto)
 private:
  inline void set_has_journalinfo();
  inline void clear_has_journalinfo();
  inline void set_has_epoch();
  inline void clear_has_epoch();
  inline void set_has_fencerinfo();
  inline void clear_has_fencerinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::JournalInfoProto* journalinfo_;
  ::google::protobuf::uint64 epoch_;
  ::std::string* fencerinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static FenceRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class FenceResponseProto : public ::google::protobuf::Message {
 public:
  FenceResponseProto();
  virtual ~FenceResponseProto();

  FenceResponseProto(const FenceResponseProto& from);

  inline FenceResponseProto& operator=(const FenceResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FenceResponseProto& default_instance();

  void Swap(FenceResponseProto* other);

  // implements Message ----------------------------------------------

  FenceResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FenceResponseProto& from);
  void MergeFrom(const FenceResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 previousEpoch = 1;
  inline bool has_previousepoch() const;
  inline void clear_previousepoch();
  static const int kPreviousEpochFieldNumber = 1;
  inline ::google::protobuf::uint64 previousepoch() const;
  inline void set_previousepoch(::google::protobuf::uint64 value);

  // optional uint64 lastTransactionId = 2;
  inline bool has_lasttransactionid() const;
  inline void clear_lasttransactionid();
  static const int kLastTransactionIdFieldNumber = 2;
  inline ::google::protobuf::uint64 lasttransactionid() const;
  inline void set_lasttransactionid(::google::protobuf::uint64 value);

  // optional bool inSync = 3;
  inline bool has_insync() const;
  inline void clear_insync();
  static const int kInSyncFieldNumber = 3;
  inline bool insync() const;
  inline void set_insync(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.FenceResponseProto)
 private:
  inline void set_has_previousepoch();
  inline void clear_has_previousepoch();
  inline void set_has_lasttransactionid();
  inline void clear_has_lasttransactionid();
  inline void set_has_insync();
  inline void clear_has_insync();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 previousepoch_;
  ::google::protobuf::uint64 lasttransactionid_;
  bool insync_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_JournalProtocol_2eproto();
  friend void protobuf_AssignDesc_JournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_JournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static FenceResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// JournalInfoProto

// required string clusterID = 1;
inline bool JournalInfoProto::has_clusterid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void JournalInfoProto::set_has_clusterid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void JournalInfoProto::clear_has_clusterid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void JournalInfoProto::clear_clusterid() {
  if (clusterid_ != &::google::protobuf::internal::kEmptyString) {
    clusterid_->clear();
  }
  clear_has_clusterid();
}
inline const ::std::string& JournalInfoProto::clusterid() const {
  return *clusterid_;
}
inline void JournalInfoProto::set_clusterid(const ::std::string& value) {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  clusterid_->assign(value);
}
inline void JournalInfoProto::set_clusterid(const char* value) {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  clusterid_->assign(value);
}
inline void JournalInfoProto::set_clusterid(const char* value, size_t size) {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  clusterid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* JournalInfoProto::mutable_clusterid() {
  set_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    clusterid_ = new ::std::string;
  }
  return clusterid_;
}
inline ::std::string* JournalInfoProto::release_clusterid() {
  clear_has_clusterid();
  if (clusterid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clusterid_;
    clusterid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void JournalInfoProto::set_allocated_clusterid(::std::string* clusterid) {
  if (clusterid_ != &::google::protobuf::internal::kEmptyString) {
    delete clusterid_;
  }
  if (clusterid) {
    set_has_clusterid();
    clusterid_ = clusterid;
  } else {
    clear_has_clusterid();
    clusterid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint32 layoutVersion = 2;
inline bool JournalInfoProto::has_layoutversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void JournalInfoProto::set_has_layoutversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void JournalInfoProto::clear_has_layoutversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void JournalInfoProto::clear_layoutversion() {
  layoutversion_ = 0u;
  clear_has_layoutversion();
}
inline ::google::protobuf::uint32 JournalInfoProto::layoutversion() const {
  return layoutversion_;
}
inline void JournalInfoProto::set_layoutversion(::google::protobuf::uint32 value) {
  set_has_layoutversion();
  layoutversion_ = value;
}

// optional uint32 namespaceID = 3;
inline bool JournalInfoProto::has_namespaceid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void JournalInfoProto::set_has_namespaceid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void JournalInfoProto::clear_has_namespaceid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void JournalInfoProto::clear_namespaceid() {
  namespaceid_ = 0u;
  clear_has_namespaceid();
}
inline ::google::protobuf::uint32 JournalInfoProto::namespaceid() const {
  return namespaceid_;
}
inline void JournalInfoProto::set_namespaceid(::google::protobuf::uint32 value) {
  set_has_namespaceid();
  namespaceid_ = value;
}

// -------------------------------------------------------------------

// JournalRequestProto

// required .hadoop.hdfs.JournalInfoProto journalInfo = 1;
inline bool JournalRequestProto::has_journalinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void JournalRequestProto::set_has_journalinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void JournalRequestProto::clear_has_journalinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void JournalRequestProto::clear_journalinfo() {
  if (journalinfo_ != NULL) journalinfo_->::hadoop::hdfs::JournalInfoProto::Clear();
  clear_has_journalinfo();
}
inline const ::hadoop::hdfs::JournalInfoProto& JournalRequestProto::journalinfo() const {
  return journalinfo_ != NULL ? *journalinfo_ : *default_instance_->journalinfo_;
}
inline ::hadoop::hdfs::JournalInfoProto* JournalRequestProto::mutable_journalinfo() {
  set_has_journalinfo();
  if (journalinfo_ == NULL) journalinfo_ = new ::hadoop::hdfs::JournalInfoProto;
  return journalinfo_;
}
inline ::hadoop::hdfs::JournalInfoProto* JournalRequestProto::release_journalinfo() {
  clear_has_journalinfo();
  ::hadoop::hdfs::JournalInfoProto* temp = journalinfo_;
  journalinfo_ = NULL;
  return temp;
}
inline void JournalRequestProto::set_allocated_journalinfo(::hadoop::hdfs::JournalInfoProto* journalinfo) {
  delete journalinfo_;
  journalinfo_ = journalinfo;
  if (journalinfo) {
    set_has_journalinfo();
  } else {
    clear_has_journalinfo();
  }
}

// required uint64 firstTxnId = 2;
inline bool JournalRequestProto::has_firsttxnid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void JournalRequestProto::set_has_firsttxnid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void JournalRequestProto::clear_has_firsttxnid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void JournalRequestProto::clear_firsttxnid() {
  firsttxnid_ = GOOGLE_ULONGLONG(0);
  clear_has_firsttxnid();
}
inline ::google::protobuf::uint64 JournalRequestProto::firsttxnid() const {
  return firsttxnid_;
}
inline void JournalRequestProto::set_firsttxnid(::google::protobuf::uint64 value) {
  set_has_firsttxnid();
  firsttxnid_ = value;
}

// required uint32 numTxns = 3;
inline bool JournalRequestProto::has_numtxns() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void JournalRequestProto::set_has_numtxns() {
  _has_bits_[0] |= 0x00000004u;
}
inline void JournalRequestProto::clear_has_numtxns() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void JournalRequestProto::clear_numtxns() {
  numtxns_ = 0u;
  clear_has_numtxns();
}
inline ::google::protobuf::uint32 JournalRequestProto::numtxns() const {
  return numtxns_;
}
inline void JournalRequestProto::set_numtxns(::google::protobuf::uint32 value) {
  set_has_numtxns();
  numtxns_ = value;
}

// required bytes records = 4;
inline bool JournalRequestProto::has_records() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void JournalRequestProto::set_has_records() {
  _has_bits_[0] |= 0x00000008u;
}
inline void JournalRequestProto::clear_has_records() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void JournalRequestProto::clear_records() {
  if (records_ != &::google::protobuf::internal::kEmptyString) {
    records_->clear();
  }
  clear_has_records();
}
inline const ::std::string& JournalRequestProto::records() const {
  return *records_;
}
inline void JournalRequestProto::set_records(const ::std::string& value) {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  records_->assign(value);
}
inline void JournalRequestProto::set_records(const char* value) {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  records_->assign(value);
}
inline void JournalRequestProto::set_records(const void* value, size_t size) {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  records_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* JournalRequestProto::mutable_records() {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  return records_;
}
inline ::std::string* JournalRequestProto::release_records() {
  clear_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = records_;
    records_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void JournalRequestProto::set_allocated_records(::std::string* records) {
  if (records_ != &::google::protobuf::internal::kEmptyString) {
    delete records_;
  }
  if (records) {
    set_has_records();
    records_ = records;
  } else {
    clear_has_records();
    records_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 epoch = 5;
inline bool JournalRequestProto::has_epoch() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void JournalRequestProto::set_has_epoch() {
  _has_bits_[0] |= 0x00000010u;
}
inline void JournalRequestProto::clear_has_epoch() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void JournalRequestProto::clear_epoch() {
  epoch_ = GOOGLE_ULONGLONG(0);
  clear_has_epoch();
}
inline ::google::protobuf::uint64 JournalRequestProto::epoch() const {
  return epoch_;
}
inline void JournalRequestProto::set_epoch(::google::protobuf::uint64 value) {
  set_has_epoch();
  epoch_ = value;
}

// -------------------------------------------------------------------

// JournalResponseProto

// -------------------------------------------------------------------

// StartLogSegmentRequestProto

// required .hadoop.hdfs.JournalInfoProto journalInfo = 1;
inline bool StartLogSegmentRequestProto::has_journalinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StartLogSegmentRequestProto::set_has_journalinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StartLogSegmentRequestProto::clear_has_journalinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StartLogSegmentRequestProto::clear_journalinfo() {
  if (journalinfo_ != NULL) journalinfo_->::hadoop::hdfs::JournalInfoProto::Clear();
  clear_has_journalinfo();
}
inline const ::hadoop::hdfs::JournalInfoProto& StartLogSegmentRequestProto::journalinfo() const {
  return journalinfo_ != NULL ? *journalinfo_ : *default_instance_->journalinfo_;
}
inline ::hadoop::hdfs::JournalInfoProto* StartLogSegmentRequestProto::mutable_journalinfo() {
  set_has_journalinfo();
  if (journalinfo_ == NULL) journalinfo_ = new ::hadoop::hdfs::JournalInfoProto;
  return journalinfo_;
}
inline ::hadoop::hdfs::JournalInfoProto* StartLogSegmentRequestProto::release_journalinfo() {
  clear_has_journalinfo();
  ::hadoop::hdfs::JournalInfoProto* temp = journalinfo_;
  journalinfo_ = NULL;
  return temp;
}
inline void StartLogSegmentRequestProto::set_allocated_journalinfo(::hadoop::hdfs::JournalInfoProto* journalinfo) {
  delete journalinfo_;
  journalinfo_ = journalinfo;
  if (journalinfo) {
    set_has_journalinfo();
  } else {
    clear_has_journalinfo();
  }
}

// required uint64 txid = 2;
inline bool StartLogSegmentRequestProto::has_txid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void StartLogSegmentRequestProto::set_has_txid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void StartLogSegmentRequestProto::clear_has_txid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void StartLogSegmentRequestProto::clear_txid() {
  txid_ = GOOGLE_ULONGLONG(0);
  clear_has_txid();
}
inline ::google::protobuf::uint64 StartLogSegmentRequestProto::txid() const {
  return txid_;
}
inline void StartLogSegmentRequestProto::set_txid(::google::protobuf::uint64 value) {
  set_has_txid();
  txid_ = value;
}

// required uint64 epoch = 3;
inline bool StartLogSegmentRequestProto::has_epoch() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void StartLogSegmentRequestProto::set_has_epoch() {
  _has_bits_[0] |= 0x00000004u;
}
inline void StartLogSegmentRequestProto::clear_has_epoch() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void StartLogSegmentRequestProto::clear_epoch() {
  epoch_ = GOOGLE_ULONGLONG(0);
  clear_has_epoch();
}
inline ::google::protobuf::uint64 StartLogSegmentRequestProto::epoch() const {
  return epoch_;
}
inline void StartLogSegmentRequestProto::set_epoch(::google::protobuf::uint64 value) {
  set_has_epoch();
  epoch_ = value;
}

// -------------------------------------------------------------------

// StartLogSegmentResponseProto

// -------------------------------------------------------------------

// FenceRequestProto

// required .hadoop.hdfs.JournalInfoProto journalInfo = 1;
inline bool FenceRequestProto::has_journalinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FenceRequestProto::set_has_journalinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FenceRequestProto::clear_has_journalinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FenceRequestProto::clear_journalinfo() {
  if (journalinfo_ != NULL) journalinfo_->::hadoop::hdfs::JournalInfoProto::Clear();
  clear_has_journalinfo();
}
inline const ::hadoop::hdfs::JournalInfoProto& FenceRequestProto::journalinfo() const {
  return journalinfo_ != NULL ? *journalinfo_ : *default_instance_->journalinfo_;
}
inline ::hadoop::hdfs::JournalInfoProto* FenceRequestProto::mutable_journalinfo() {
  set_has_journalinfo();
  if (journalinfo_ == NULL) journalinfo_ = new ::hadoop::hdfs::JournalInfoProto;
  return journalinfo_;
}
inline ::hadoop::hdfs::JournalInfoProto* FenceRequestProto::release_journalinfo() {
  clear_has_journalinfo();
  ::hadoop::hdfs::JournalInfoProto* temp = journalinfo_;
  journalinfo_ = NULL;
  return temp;
}
inline void FenceRequestProto::set_allocated_journalinfo(::hadoop::hdfs::JournalInfoProto* journalinfo) {
  delete journalinfo_;
  journalinfo_ = journalinfo;
  if (journalinfo) {
    set_has_journalinfo();
  } else {
    clear_has_journalinfo();
  }
}

// required uint64 epoch = 2;
inline bool FenceRequestProto::has_epoch() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FenceRequestProto::set_has_epoch() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FenceRequestProto::clear_has_epoch() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FenceRequestProto::clear_epoch() {
  epoch_ = GOOGLE_ULONGLONG(0);
  clear_has_epoch();
}
inline ::google::protobuf::uint64 FenceRequestProto::epoch() const {
  return epoch_;
}
inline void FenceRequestProto::set_epoch(::google::protobuf::uint64 value) {
  set_has_epoch();
  epoch_ = value;
}

// optional string fencerInfo = 3;
inline bool FenceRequestProto::has_fencerinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FenceRequestProto::set_has_fencerinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FenceRequestProto::clear_has_fencerinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FenceRequestProto::clear_fencerinfo() {
  if (fencerinfo_ != &::google::protobuf::internal::kEmptyString) {
    fencerinfo_->clear();
  }
  clear_has_fencerinfo();
}
inline const ::std::string& FenceRequestProto::fencerinfo() const {
  return *fencerinfo_;
}
inline void FenceRequestProto::set_fencerinfo(const ::std::string& value) {
  set_has_fencerinfo();
  if (fencerinfo_ == &::google::protobuf::internal::kEmptyString) {
    fencerinfo_ = new ::std::string;
  }
  fencerinfo_->assign(value);
}
inline void FenceRequestProto::set_fencerinfo(const char* value) {
  set_has_fencerinfo();
  if (fencerinfo_ == &::google::protobuf::internal::kEmptyString) {
    fencerinfo_ = new ::std::string;
  }
  fencerinfo_->assign(value);
}
inline void FenceRequestProto::set_fencerinfo(const char* value, size_t size) {
  set_has_fencerinfo();
  if (fencerinfo_ == &::google::protobuf::internal::kEmptyString) {
    fencerinfo_ = new ::std::string;
  }
  fencerinfo_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FenceRequestProto::mutable_fencerinfo() {
  set_has_fencerinfo();
  if (fencerinfo_ == &::google::protobuf::internal::kEmptyString) {
    fencerinfo_ = new ::std::string;
  }
  return fencerinfo_;
}
inline ::std::string* FenceRequestProto::release_fencerinfo() {
  clear_has_fencerinfo();
  if (fencerinfo_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fencerinfo_;
    fencerinfo_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FenceRequestProto::set_allocated_fencerinfo(::std::string* fencerinfo) {
  if (fencerinfo_ != &::google::protobuf::internal::kEmptyString) {
    delete fencerinfo_;
  }
  if (fencerinfo) {
    set_has_fencerinfo();
    fencerinfo_ = fencerinfo;
  } else {
    clear_has_fencerinfo();
    fencerinfo_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// FenceResponseProto

// optional uint64 previousEpoch = 1;
inline bool FenceResponseProto::has_previousepoch() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FenceResponseProto::set_has_previousepoch() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FenceResponseProto::clear_has_previousepoch() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FenceResponseProto::clear_previousepoch() {
  previousepoch_ = GOOGLE_ULONGLONG(0);
  clear_has_previousepoch();
}
inline ::google::protobuf::uint64 FenceResponseProto::previousepoch() const {
  return previousepoch_;
}
inline void FenceResponseProto::set_previousepoch(::google::protobuf::uint64 value) {
  set_has_previousepoch();
  previousepoch_ = value;
}

// optional uint64 lastTransactionId = 2;
inline bool FenceResponseProto::has_lasttransactionid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FenceResponseProto::set_has_lasttransactionid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FenceResponseProto::clear_has_lasttransactionid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FenceResponseProto::clear_lasttransactionid() {
  lasttransactionid_ = GOOGLE_ULONGLONG(0);
  clear_has_lasttransactionid();
}
inline ::google::protobuf::uint64 FenceResponseProto::lasttransactionid() const {
  return lasttransactionid_;
}
inline void FenceResponseProto::set_lasttransactionid(::google::protobuf::uint64 value) {
  set_has_lasttransactionid();
  lasttransactionid_ = value;
}

// optional bool inSync = 3;
inline bool FenceResponseProto::has_insync() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FenceResponseProto::set_has_insync() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FenceResponseProto::clear_has_insync() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FenceResponseProto::clear_insync() {
  insync_ = false;
  clear_has_insync();
}
inline bool FenceResponseProto::insync() const {
  return insync_;
}
inline void FenceResponseProto::set_insync(bool value) {
  set_has_insync();
  insync_ = value;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_JournalProtocol_2eproto__INCLUDED
