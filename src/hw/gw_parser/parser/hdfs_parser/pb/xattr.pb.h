// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xattr.proto

#ifndef PROTOBUF_xattr_2eproto__INCLUDED
#define PROTOBUF_xattr_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_xattr_2eproto();
void protobuf_AssignDesc_xattr_2eproto();
void protobuf_ShutdownFile_xattr_2eproto();

class XAttrProto;
class XAttrEditLogProto;
class SetXAttrRequestProto;
class SetXAttrResponseProto;
class GetXAttrsRequestProto;
class GetXAttrsResponseProto;
class ListXAttrsRequestProto;
class ListXAttrsResponseProto;
class RemoveXAttrRequestProto;
class RemoveXAttrResponseProto;

enum XAttrProto_XAttrNamespaceProto {
  XAttrProto_XAttrNamespaceProto_USER = 0,
  XAttrProto_XAttrNamespaceProto_TRUSTED = 1,
  XAttrProto_XAttrNamespaceProto_SECURITY = 2,
  XAttrProto_XAttrNamespaceProto_SYSTEM = 3,
  XAttrProto_XAttrNamespaceProto_RAW = 4
};
bool XAttrProto_XAttrNamespaceProto_IsValid(int value);
const XAttrProto_XAttrNamespaceProto XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_MIN = XAttrProto_XAttrNamespaceProto_USER;
const XAttrProto_XAttrNamespaceProto XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_MAX = XAttrProto_XAttrNamespaceProto_RAW;
const int XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_ARRAYSIZE = XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* XAttrProto_XAttrNamespaceProto_descriptor();
inline const ::std::string& XAttrProto_XAttrNamespaceProto_Name(XAttrProto_XAttrNamespaceProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    XAttrProto_XAttrNamespaceProto_descriptor(), value);
}
inline bool XAttrProto_XAttrNamespaceProto_Parse(
    const ::std::string& name, XAttrProto_XAttrNamespaceProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<XAttrProto_XAttrNamespaceProto>(
    XAttrProto_XAttrNamespaceProto_descriptor(), name, value);
}
enum XAttrSetFlagProto {
  XATTR_CREATE = 1,
  XATTR_REPLACE = 2
};
bool XAttrSetFlagProto_IsValid(int value);
const XAttrSetFlagProto XAttrSetFlagProto_MIN = XATTR_CREATE;
const XAttrSetFlagProto XAttrSetFlagProto_MAX = XATTR_REPLACE;
const int XAttrSetFlagProto_ARRAYSIZE = XAttrSetFlagProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* XAttrSetFlagProto_descriptor();
inline const ::std::string& XAttrSetFlagProto_Name(XAttrSetFlagProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    XAttrSetFlagProto_descriptor(), value);
}
inline bool XAttrSetFlagProto_Parse(
    const ::std::string& name, XAttrSetFlagProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<XAttrSetFlagProto>(
    XAttrSetFlagProto_descriptor(), name, value);
}
// ===================================================================

class XAttrProto : public ::google::protobuf::Message {
 public:
  XAttrProto();
  virtual ~XAttrProto();

  XAttrProto(const XAttrProto& from);

  inline XAttrProto& operator=(const XAttrProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const XAttrProto& default_instance();

  void Swap(XAttrProto* other);

  // implements Message ----------------------------------------------

  XAttrProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const XAttrProto& from);
  void MergeFrom(const XAttrProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef XAttrProto_XAttrNamespaceProto XAttrNamespaceProto;
  static const XAttrNamespaceProto USER = XAttrProto_XAttrNamespaceProto_USER;
  static const XAttrNamespaceProto TRUSTED = XAttrProto_XAttrNamespaceProto_TRUSTED;
  static const XAttrNamespaceProto SECURITY = XAttrProto_XAttrNamespaceProto_SECURITY;
  static const XAttrNamespaceProto SYSTEM = XAttrProto_XAttrNamespaceProto_SYSTEM;
  static const XAttrNamespaceProto RAW = XAttrProto_XAttrNamespaceProto_RAW;
  static inline bool XAttrNamespaceProto_IsValid(int value) {
    return XAttrProto_XAttrNamespaceProto_IsValid(value);
  }
  static const XAttrNamespaceProto XAttrNamespaceProto_MIN =
    XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_MIN;
  static const XAttrNamespaceProto XAttrNamespaceProto_MAX =
    XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_MAX;
  static const int XAttrNamespaceProto_ARRAYSIZE =
    XAttrProto_XAttrNamespaceProto_XAttrNamespaceProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  XAttrNamespaceProto_descriptor() {
    return XAttrProto_XAttrNamespaceProto_descriptor();
  }
  static inline const ::std::string& XAttrNamespaceProto_Name(XAttrNamespaceProto value) {
    return XAttrProto_XAttrNamespaceProto_Name(value);
  }
  static inline bool XAttrNamespaceProto_Parse(const ::std::string& name,
      XAttrNamespaceProto* value) {
    return XAttrProto_XAttrNamespaceProto_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.XAttrProto.XAttrNamespaceProto namespace = 1;
  inline bool has_namespace_() const;
  inline void clear_namespace_();
  static const int kNamespaceFieldNumber = 1;
  inline ::hadoop::hdfs::XAttrProto_XAttrNamespaceProto namespace_() const;
  inline void set_namespace_(::hadoop::hdfs::XAttrProto_XAttrNamespaceProto value);

  // required string name = 2;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 2;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional bytes value = 3;
  inline bool has_value() const;
  inline void clear_value();
  static const int kValueFieldNumber = 3;
  inline const ::std::string& value() const;
  inline void set_value(const ::std::string& value);
  inline void set_value(const char* value);
  inline void set_value(const void* value, size_t size);
  inline ::std::string* mutable_value();
  inline ::std::string* release_value();
  inline void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.XAttrProto)
 private:
  inline void set_has_namespace_();
  inline void clear_has_namespace_();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* name_;
  ::std::string* value_;
  int namespace__;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static XAttrProto* default_instance_;
};
// -------------------------------------------------------------------

class XAttrEditLogProto : public ::google::protobuf::Message {
 public:
  XAttrEditLogProto();
  virtual ~XAttrEditLogProto();

  XAttrEditLogProto(const XAttrEditLogProto& from);

  inline XAttrEditLogProto& operator=(const XAttrEditLogProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const XAttrEditLogProto& default_instance();

  void Swap(XAttrEditLogProto* other);

  // implements Message ----------------------------------------------

  XAttrEditLogProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const XAttrEditLogProto& from);
  void MergeFrom(const XAttrEditLogProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  inline int xattrs_size() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 2;
  inline const ::hadoop::hdfs::XAttrProto& xattrs(int index) const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattrs(int index);
  inline ::hadoop::hdfs::XAttrProto* add_xattrs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
      xattrs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
      mutable_xattrs();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.XAttrEditLogProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto > xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static XAttrEditLogProto* default_instance_;
};
// -------------------------------------------------------------------

class SetXAttrRequestProto : public ::google::protobuf::Message {
 public:
  SetXAttrRequestProto();
  virtual ~SetXAttrRequestProto();

  SetXAttrRequestProto(const SetXAttrRequestProto& from);

  inline SetXAttrRequestProto& operator=(const SetXAttrRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SetXAttrRequestProto& default_instance();

  void Swap(SetXAttrRequestProto* other);

  // implements Message ----------------------------------------------

  SetXAttrRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SetXAttrRequestProto& from);
  void MergeFrom(const SetXAttrRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // optional .hadoop.hdfs.XAttrProto xAttr = 2;
  inline bool has_xattr() const;
  inline void clear_xattr();
  static const int kXAttrFieldNumber = 2;
  inline const ::hadoop::hdfs::XAttrProto& xattr() const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattr();
  inline ::hadoop::hdfs::XAttrProto* release_xattr();
  inline void set_allocated_xattr(::hadoop::hdfs::XAttrProto* xattr);

  // optional uint32 flag = 3;
  inline bool has_flag() const;
  inline void clear_flag();
  static const int kFlagFieldNumber = 3;
  inline ::google::protobuf::uint32 flag() const;
  inline void set_flag(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SetXAttrRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();
  inline void set_has_xattr();
  inline void clear_has_xattr();
  inline void set_has_flag();
  inline void clear_has_flag();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::hadoop::hdfs::XAttrProto* xattr_;
  ::google::protobuf::uint32 flag_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static SetXAttrRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class SetXAttrResponseProto : public ::google::protobuf::Message {
 public:
  SetXAttrResponseProto();
  virtual ~SetXAttrResponseProto();

  SetXAttrResponseProto(const SetXAttrResponseProto& from);

  inline SetXAttrResponseProto& operator=(const SetXAttrResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SetXAttrResponseProto& default_instance();

  void Swap(SetXAttrResponseProto* other);

  // implements Message ----------------------------------------------

  SetXAttrResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SetXAttrResponseProto& from);
  void MergeFrom(const SetXAttrResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.SetXAttrResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static SetXAttrResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetXAttrsRequestProto : public ::google::protobuf::Message {
 public:
  GetXAttrsRequestProto();
  virtual ~GetXAttrsRequestProto();

  GetXAttrsRequestProto(const GetXAttrsRequestProto& from);

  inline GetXAttrsRequestProto& operator=(const GetXAttrsRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetXAttrsRequestProto& default_instance();

  void Swap(GetXAttrsRequestProto* other);

  // implements Message ----------------------------------------------

  GetXAttrsRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetXAttrsRequestProto& from);
  void MergeFrom(const GetXAttrsRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
  inline int xattrs_size() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 2;
  inline const ::hadoop::hdfs::XAttrProto& xattrs(int index) const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattrs(int index);
  inline ::hadoop::hdfs::XAttrProto* add_xattrs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
      xattrs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
      mutable_xattrs();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.GetXAttrsRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto > xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static GetXAttrsRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetXAttrsResponseProto : public ::google::protobuf::Message {
 public:
  GetXAttrsResponseProto();
  virtual ~GetXAttrsResponseProto();

  GetXAttrsResponseProto(const GetXAttrsResponseProto& from);

  inline GetXAttrsResponseProto& operator=(const GetXAttrsResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetXAttrsResponseProto& default_instance();

  void Swap(GetXAttrsResponseProto* other);

  // implements Message ----------------------------------------------

  GetXAttrsResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetXAttrsResponseProto& from);
  void MergeFrom(const GetXAttrsResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  inline int xattrs_size() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 1;
  inline const ::hadoop::hdfs::XAttrProto& xattrs(int index) const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattrs(int index);
  inline ::hadoop::hdfs::XAttrProto* add_xattrs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
      xattrs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
      mutable_xattrs();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.GetXAttrsResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto > xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static GetXAttrsResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ListXAttrsRequestProto : public ::google::protobuf::Message {
 public:
  ListXAttrsRequestProto();
  virtual ~ListXAttrsRequestProto();

  ListXAttrsRequestProto(const ListXAttrsRequestProto& from);

  inline ListXAttrsRequestProto& operator=(const ListXAttrsRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListXAttrsRequestProto& default_instance();

  void Swap(ListXAttrsRequestProto* other);

  // implements Message ----------------------------------------------

  ListXAttrsRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListXAttrsRequestProto& from);
  void MergeFrom(const ListXAttrsRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ListXAttrsRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static ListXAttrsRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class ListXAttrsResponseProto : public ::google::protobuf::Message {
 public:
  ListXAttrsResponseProto();
  virtual ~ListXAttrsResponseProto();

  ListXAttrsResponseProto(const ListXAttrsResponseProto& from);

  inline ListXAttrsResponseProto& operator=(const ListXAttrsResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListXAttrsResponseProto& default_instance();

  void Swap(ListXAttrsResponseProto* other);

  // implements Message ----------------------------------------------

  ListXAttrsResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListXAttrsResponseProto& from);
  void MergeFrom(const ListXAttrsResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
  inline int xattrs_size() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 1;
  inline const ::hadoop::hdfs::XAttrProto& xattrs(int index) const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattrs(int index);
  inline ::hadoop::hdfs::XAttrProto* add_xattrs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
      xattrs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
      mutable_xattrs();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ListXAttrsResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto > xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static ListXAttrsResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveXAttrRequestProto : public ::google::protobuf::Message {
 public:
  RemoveXAttrRequestProto();
  virtual ~RemoveXAttrRequestProto();

  RemoveXAttrRequestProto(const RemoveXAttrRequestProto& from);

  inline RemoveXAttrRequestProto& operator=(const RemoveXAttrRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveXAttrRequestProto& default_instance();

  void Swap(RemoveXAttrRequestProto* other);

  // implements Message ----------------------------------------------

  RemoveXAttrRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveXAttrRequestProto& from);
  void MergeFrom(const RemoveXAttrRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::std::string& src() const;
  inline void set_src(const ::std::string& value);
  inline void set_src(const char* value);
  inline void set_src(const char* value, size_t size);
  inline ::std::string* mutable_src();
  inline ::std::string* release_src();
  inline void set_allocated_src(::std::string* src);

  // optional .hadoop.hdfs.XAttrProto xAttr = 2;
  inline bool has_xattr() const;
  inline void clear_xattr();
  static const int kXAttrFieldNumber = 2;
  inline const ::hadoop::hdfs::XAttrProto& xattr() const;
  inline ::hadoop::hdfs::XAttrProto* mutable_xattr();
  inline ::hadoop::hdfs::XAttrProto* release_xattr();
  inline void set_allocated_xattr(::hadoop::hdfs::XAttrProto* xattr);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveXAttrRequestProto)
 private:
  inline void set_has_src();
  inline void clear_has_src();
  inline void set_has_xattr();
  inline void clear_has_xattr();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* src_;
  ::hadoop::hdfs::XAttrProto* xattr_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static RemoveXAttrRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveXAttrResponseProto : public ::google::protobuf::Message {
 public:
  RemoveXAttrResponseProto();
  virtual ~RemoveXAttrResponseProto();

  RemoveXAttrResponseProto(const RemoveXAttrResponseProto& from);

  inline RemoveXAttrResponseProto& operator=(const RemoveXAttrResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveXAttrResponseProto& default_instance();

  void Swap(RemoveXAttrResponseProto* other);

  // implements Message ----------------------------------------------

  RemoveXAttrResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveXAttrResponseProto& from);
  void MergeFrom(const RemoveXAttrResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.RemoveXAttrResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_xattr_2eproto();
  friend void protobuf_AssignDesc_xattr_2eproto();
  friend void protobuf_ShutdownFile_xattr_2eproto();

  void InitAsDefaultInstance();
  static RemoveXAttrResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// XAttrProto

// required .hadoop.hdfs.XAttrProto.XAttrNamespaceProto namespace = 1;
inline bool XAttrProto::has_namespace_() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void XAttrProto::set_has_namespace_() {
  _has_bits_[0] |= 0x00000001u;
}
inline void XAttrProto::clear_has_namespace_() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void XAttrProto::clear_namespace_() {
  namespace__ = 0;
  clear_has_namespace_();
}
inline ::hadoop::hdfs::XAttrProto_XAttrNamespaceProto XAttrProto::namespace_() const {
  return static_cast< ::hadoop::hdfs::XAttrProto_XAttrNamespaceProto >(namespace__);
}
inline void XAttrProto::set_namespace_(::hadoop::hdfs::XAttrProto_XAttrNamespaceProto value) {
  assert(::hadoop::hdfs::XAttrProto_XAttrNamespaceProto_IsValid(value));
  set_has_namespace_();
  namespace__ = value;
}

// required string name = 2;
inline bool XAttrProto::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void XAttrProto::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void XAttrProto::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void XAttrProto::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& XAttrProto::name() const {
  return *name_;
}
inline void XAttrProto::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void XAttrProto::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void XAttrProto::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* XAttrProto::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* XAttrProto::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void XAttrProto::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bytes value = 3;
inline bool XAttrProto::has_value() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void XAttrProto::set_has_value() {
  _has_bits_[0] |= 0x00000004u;
}
inline void XAttrProto::clear_has_value() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void XAttrProto::clear_value() {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    value_->clear();
  }
  clear_has_value();
}
inline const ::std::string& XAttrProto::value() const {
  return *value_;
}
inline void XAttrProto::set_value(const ::std::string& value) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(value);
}
inline void XAttrProto::set_value(const char* value) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(value);
}
inline void XAttrProto::set_value(const void* value, size_t size) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* XAttrProto::mutable_value() {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  return value_;
}
inline ::std::string* XAttrProto::release_value() {
  clear_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = value_;
    value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void XAttrProto::set_allocated_value(::std::string* value) {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    delete value_;
  }
  if (value) {
    set_has_value();
    value_ = value;
  } else {
    clear_has_value();
    value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// XAttrEditLogProto

// optional string src = 1;
inline bool XAttrEditLogProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void XAttrEditLogProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void XAttrEditLogProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void XAttrEditLogProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& XAttrEditLogProto::src() const {
  return *src_;
}
inline void XAttrEditLogProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void XAttrEditLogProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void XAttrEditLogProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* XAttrEditLogProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* XAttrEditLogProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void XAttrEditLogProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
inline int XAttrEditLogProto::xattrs_size() const {
  return xattrs_.size();
}
inline void XAttrEditLogProto::clear_xattrs() {
  xattrs_.Clear();
}
inline const ::hadoop::hdfs::XAttrProto& XAttrEditLogProto::xattrs(int index) const {
  return xattrs_.Get(index);
}
inline ::hadoop::hdfs::XAttrProto* XAttrEditLogProto::mutable_xattrs(int index) {
  return xattrs_.Mutable(index);
}
inline ::hadoop::hdfs::XAttrProto* XAttrEditLogProto::add_xattrs() {
  return xattrs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
XAttrEditLogProto::xattrs() const {
  return xattrs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
XAttrEditLogProto::mutable_xattrs() {
  return &xattrs_;
}

// -------------------------------------------------------------------

// SetXAttrRequestProto

// required string src = 1;
inline bool SetXAttrRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SetXAttrRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SetXAttrRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SetXAttrRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& SetXAttrRequestProto::src() const {
  return *src_;
}
inline void SetXAttrRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void SetXAttrRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void SetXAttrRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SetXAttrRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* SetXAttrRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SetXAttrRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.XAttrProto xAttr = 2;
inline bool SetXAttrRequestProto::has_xattr() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SetXAttrRequestProto::set_has_xattr() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SetXAttrRequestProto::clear_has_xattr() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SetXAttrRequestProto::clear_xattr() {
  if (xattr_ != NULL) xattr_->::hadoop::hdfs::XAttrProto::Clear();
  clear_has_xattr();
}
inline const ::hadoop::hdfs::XAttrProto& SetXAttrRequestProto::xattr() const {
  return xattr_ != NULL ? *xattr_ : *default_instance_->xattr_;
}
inline ::hadoop::hdfs::XAttrProto* SetXAttrRequestProto::mutable_xattr() {
  set_has_xattr();
  if (xattr_ == NULL) xattr_ = new ::hadoop::hdfs::XAttrProto;
  return xattr_;
}
inline ::hadoop::hdfs::XAttrProto* SetXAttrRequestProto::release_xattr() {
  clear_has_xattr();
  ::hadoop::hdfs::XAttrProto* temp = xattr_;
  xattr_ = NULL;
  return temp;
}
inline void SetXAttrRequestProto::set_allocated_xattr(::hadoop::hdfs::XAttrProto* xattr) {
  delete xattr_;
  xattr_ = xattr;
  if (xattr) {
    set_has_xattr();
  } else {
    clear_has_xattr();
  }
}

// optional uint32 flag = 3;
inline bool SetXAttrRequestProto::has_flag() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SetXAttrRequestProto::set_has_flag() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SetXAttrRequestProto::clear_has_flag() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SetXAttrRequestProto::clear_flag() {
  flag_ = 0u;
  clear_has_flag();
}
inline ::google::protobuf::uint32 SetXAttrRequestProto::flag() const {
  return flag_;
}
inline void SetXAttrRequestProto::set_flag(::google::protobuf::uint32 value) {
  set_has_flag();
  flag_ = value;
}

// -------------------------------------------------------------------

// SetXAttrResponseProto

// -------------------------------------------------------------------

// GetXAttrsRequestProto

// required string src = 1;
inline bool GetXAttrsRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetXAttrsRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetXAttrsRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetXAttrsRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& GetXAttrsRequestProto::src() const {
  return *src_;
}
inline void GetXAttrsRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void GetXAttrsRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void GetXAttrsRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetXAttrsRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* GetXAttrsRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetXAttrsRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.XAttrProto xAttrs = 2;
inline int GetXAttrsRequestProto::xattrs_size() const {
  return xattrs_.size();
}
inline void GetXAttrsRequestProto::clear_xattrs() {
  xattrs_.Clear();
}
inline const ::hadoop::hdfs::XAttrProto& GetXAttrsRequestProto::xattrs(int index) const {
  return xattrs_.Get(index);
}
inline ::hadoop::hdfs::XAttrProto* GetXAttrsRequestProto::mutable_xattrs(int index) {
  return xattrs_.Mutable(index);
}
inline ::hadoop::hdfs::XAttrProto* GetXAttrsRequestProto::add_xattrs() {
  return xattrs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
GetXAttrsRequestProto::xattrs() const {
  return xattrs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
GetXAttrsRequestProto::mutable_xattrs() {
  return &xattrs_;
}

// -------------------------------------------------------------------

// GetXAttrsResponseProto

// repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
inline int GetXAttrsResponseProto::xattrs_size() const {
  return xattrs_.size();
}
inline void GetXAttrsResponseProto::clear_xattrs() {
  xattrs_.Clear();
}
inline const ::hadoop::hdfs::XAttrProto& GetXAttrsResponseProto::xattrs(int index) const {
  return xattrs_.Get(index);
}
inline ::hadoop::hdfs::XAttrProto* GetXAttrsResponseProto::mutable_xattrs(int index) {
  return xattrs_.Mutable(index);
}
inline ::hadoop::hdfs::XAttrProto* GetXAttrsResponseProto::add_xattrs() {
  return xattrs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
GetXAttrsResponseProto::xattrs() const {
  return xattrs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
GetXAttrsResponseProto::mutable_xattrs() {
  return &xattrs_;
}

// -------------------------------------------------------------------

// ListXAttrsRequestProto

// required string src = 1;
inline bool ListXAttrsRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ListXAttrsRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ListXAttrsRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ListXAttrsRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& ListXAttrsRequestProto::src() const {
  return *src_;
}
inline void ListXAttrsRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void ListXAttrsRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void ListXAttrsRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ListXAttrsRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* ListXAttrsRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ListXAttrsRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ListXAttrsResponseProto

// repeated .hadoop.hdfs.XAttrProto xAttrs = 1;
inline int ListXAttrsResponseProto::xattrs_size() const {
  return xattrs_.size();
}
inline void ListXAttrsResponseProto::clear_xattrs() {
  xattrs_.Clear();
}
inline const ::hadoop::hdfs::XAttrProto& ListXAttrsResponseProto::xattrs(int index) const {
  return xattrs_.Get(index);
}
inline ::hadoop::hdfs::XAttrProto* ListXAttrsResponseProto::mutable_xattrs(int index) {
  return xattrs_.Mutable(index);
}
inline ::hadoop::hdfs::XAttrProto* ListXAttrsResponseProto::add_xattrs() {
  return xattrs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >&
ListXAttrsResponseProto::xattrs() const {
  return xattrs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::XAttrProto >*
ListXAttrsResponseProto::mutable_xattrs() {
  return &xattrs_;
}

// -------------------------------------------------------------------

// RemoveXAttrRequestProto

// required string src = 1;
inline bool RemoveXAttrRequestProto::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RemoveXAttrRequestProto::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RemoveXAttrRequestProto::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RemoveXAttrRequestProto::clear_src() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    src_->clear();
  }
  clear_has_src();
}
inline const ::std::string& RemoveXAttrRequestProto::src() const {
  return *src_;
}
inline void RemoveXAttrRequestProto::set_src(const ::std::string& value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveXAttrRequestProto::set_src(const char* value) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(value);
}
inline void RemoveXAttrRequestProto::set_src(const char* value, size_t size) {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  src_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RemoveXAttrRequestProto::mutable_src() {
  set_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    src_ = new ::std::string;
  }
  return src_;
}
inline ::std::string* RemoveXAttrRequestProto::release_src() {
  clear_has_src();
  if (src_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = src_;
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RemoveXAttrRequestProto::set_allocated_src(::std::string* src) {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (src) {
    set_has_src();
    src_ = src;
  } else {
    clear_has_src();
    src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.XAttrProto xAttr = 2;
inline bool RemoveXAttrRequestProto::has_xattr() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RemoveXAttrRequestProto::set_has_xattr() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RemoveXAttrRequestProto::clear_has_xattr() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RemoveXAttrRequestProto::clear_xattr() {
  if (xattr_ != NULL) xattr_->::hadoop::hdfs::XAttrProto::Clear();
  clear_has_xattr();
}
inline const ::hadoop::hdfs::XAttrProto& RemoveXAttrRequestProto::xattr() const {
  return xattr_ != NULL ? *xattr_ : *default_instance_->xattr_;
}
inline ::hadoop::hdfs::XAttrProto* RemoveXAttrRequestProto::mutable_xattr() {
  set_has_xattr();
  if (xattr_ == NULL) xattr_ = new ::hadoop::hdfs::XAttrProto;
  return xattr_;
}
inline ::hadoop::hdfs::XAttrProto* RemoveXAttrRequestProto::release_xattr() {
  clear_has_xattr();
  ::hadoop::hdfs::XAttrProto* temp = xattr_;
  xattr_ = NULL;
  return temp;
}
inline void RemoveXAttrRequestProto::set_allocated_xattr(::hadoop::hdfs::XAttrProto* xattr) {
  delete xattr_;
  xattr_ = xattr;
  if (xattr) {
    set_has_xattr();
  } else {
    clear_has_xattr();
  }
}

// -------------------------------------------------------------------

// RemoveXAttrResponseProto


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::XAttrProto_XAttrNamespaceProto>() {
  return ::hadoop::hdfs::XAttrProto_XAttrNamespaceProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::XAttrSetFlagProto>() {
  return ::hadoop::hdfs::XAttrSetFlagProto_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_xattr_2eproto__INCLUDED
