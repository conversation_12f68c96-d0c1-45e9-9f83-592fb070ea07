// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: acl.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "acl.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

namespace {

const ::google::protobuf::Descriptor* AclEntryProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AclEntryProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* AclEntryProto_AclEntryScopeProto_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* AclEntryProto_AclEntryTypeProto_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* AclEntryProto_FsActionProto_descriptor_ = NULL;
const ::google::protobuf::Descriptor* AclStatusProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AclStatusProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* AclEditLogProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AclEditLogProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ModifyAclEntriesRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ModifyAclEntriesRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ModifyAclEntriesResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ModifyAclEntriesResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveAclRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveAclRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveAclResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveAclResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveAclEntriesRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveAclEntriesRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveAclEntriesResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveAclEntriesResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveDefaultAclRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveDefaultAclRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveDefaultAclResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveDefaultAclResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* SetAclRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SetAclRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* SetAclResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SetAclResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetAclStatusRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetAclStatusRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetAclStatusResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetAclStatusResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_acl_2eproto() {
  protobuf_AddDesc_acl_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "acl.proto");
  GOOGLE_CHECK(file != NULL);
  AclEntryProto_descriptor_ = file->message_type(0);
  static const int AclEntryProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEntryProto, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEntryProto, scope_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEntryProto, permissions_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEntryProto, name_),
  };
  AclEntryProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AclEntryProto_descriptor_,
      AclEntryProto::default_instance_,
      AclEntryProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEntryProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEntryProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AclEntryProto));
  AclEntryProto_AclEntryScopeProto_descriptor_ = AclEntryProto_descriptor_->enum_type(0);
  AclEntryProto_AclEntryTypeProto_descriptor_ = AclEntryProto_descriptor_->enum_type(1);
  AclEntryProto_FsActionProto_descriptor_ = AclEntryProto_descriptor_->enum_type(2);
  AclStatusProto_descriptor_ = file->message_type(1);
  static const int AclStatusProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclStatusProto, owner_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclStatusProto, group_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclStatusProto, sticky_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclStatusProto, entries_),
  };
  AclStatusProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AclStatusProto_descriptor_,
      AclStatusProto::default_instance_,
      AclStatusProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclStatusProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclStatusProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AclStatusProto));
  AclEditLogProto_descriptor_ = file->message_type(2);
  static const int AclEditLogProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEditLogProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEditLogProto, entries_),
  };
  AclEditLogProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AclEditLogProto_descriptor_,
      AclEditLogProto::default_instance_,
      AclEditLogProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEditLogProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AclEditLogProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AclEditLogProto));
  ModifyAclEntriesRequestProto_descriptor_ = file->message_type(3);
  static const int ModifyAclEntriesRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ModifyAclEntriesRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ModifyAclEntriesRequestProto, aclspec_),
  };
  ModifyAclEntriesRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ModifyAclEntriesRequestProto_descriptor_,
      ModifyAclEntriesRequestProto::default_instance_,
      ModifyAclEntriesRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ModifyAclEntriesRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ModifyAclEntriesRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ModifyAclEntriesRequestProto));
  ModifyAclEntriesResponseProto_descriptor_ = file->message_type(4);
  static const int ModifyAclEntriesResponseProto_offsets_[1] = {
  };
  ModifyAclEntriesResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ModifyAclEntriesResponseProto_descriptor_,
      ModifyAclEntriesResponseProto::default_instance_,
      ModifyAclEntriesResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ModifyAclEntriesResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ModifyAclEntriesResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ModifyAclEntriesResponseProto));
  RemoveAclRequestProto_descriptor_ = file->message_type(5);
  static const int RemoveAclRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclRequestProto, src_),
  };
  RemoveAclRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveAclRequestProto_descriptor_,
      RemoveAclRequestProto::default_instance_,
      RemoveAclRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveAclRequestProto));
  RemoveAclResponseProto_descriptor_ = file->message_type(6);
  static const int RemoveAclResponseProto_offsets_[1] = {
  };
  RemoveAclResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveAclResponseProto_descriptor_,
      RemoveAclResponseProto::default_instance_,
      RemoveAclResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveAclResponseProto));
  RemoveAclEntriesRequestProto_descriptor_ = file->message_type(7);
  static const int RemoveAclEntriesRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclEntriesRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclEntriesRequestProto, aclspec_),
  };
  RemoveAclEntriesRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveAclEntriesRequestProto_descriptor_,
      RemoveAclEntriesRequestProto::default_instance_,
      RemoveAclEntriesRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclEntriesRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclEntriesRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveAclEntriesRequestProto));
  RemoveAclEntriesResponseProto_descriptor_ = file->message_type(8);
  static const int RemoveAclEntriesResponseProto_offsets_[1] = {
  };
  RemoveAclEntriesResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveAclEntriesResponseProto_descriptor_,
      RemoveAclEntriesResponseProto::default_instance_,
      RemoveAclEntriesResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclEntriesResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveAclEntriesResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveAclEntriesResponseProto));
  RemoveDefaultAclRequestProto_descriptor_ = file->message_type(9);
  static const int RemoveDefaultAclRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveDefaultAclRequestProto, src_),
  };
  RemoveDefaultAclRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveDefaultAclRequestProto_descriptor_,
      RemoveDefaultAclRequestProto::default_instance_,
      RemoveDefaultAclRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveDefaultAclRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveDefaultAclRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveDefaultAclRequestProto));
  RemoveDefaultAclResponseProto_descriptor_ = file->message_type(10);
  static const int RemoveDefaultAclResponseProto_offsets_[1] = {
  };
  RemoveDefaultAclResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveDefaultAclResponseProto_descriptor_,
      RemoveDefaultAclResponseProto::default_instance_,
      RemoveDefaultAclResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveDefaultAclResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveDefaultAclResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveDefaultAclResponseProto));
  SetAclRequestProto_descriptor_ = file->message_type(11);
  static const int SetAclRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetAclRequestProto, src_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetAclRequestProto, aclspec_),
  };
  SetAclRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SetAclRequestProto_descriptor_,
      SetAclRequestProto::default_instance_,
      SetAclRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetAclRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetAclRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SetAclRequestProto));
  SetAclResponseProto_descriptor_ = file->message_type(12);
  static const int SetAclResponseProto_offsets_[1] = {
  };
  SetAclResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SetAclResponseProto_descriptor_,
      SetAclResponseProto::default_instance_,
      SetAclResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetAclResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SetAclResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SetAclResponseProto));
  GetAclStatusRequestProto_descriptor_ = file->message_type(13);
  static const int GetAclStatusRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetAclStatusRequestProto, src_),
  };
  GetAclStatusRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetAclStatusRequestProto_descriptor_,
      GetAclStatusRequestProto::default_instance_,
      GetAclStatusRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetAclStatusRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetAclStatusRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetAclStatusRequestProto));
  GetAclStatusResponseProto_descriptor_ = file->message_type(14);
  static const int GetAclStatusResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetAclStatusResponseProto, result_),
  };
  GetAclStatusResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetAclStatusResponseProto_descriptor_,
      GetAclStatusResponseProto::default_instance_,
      GetAclStatusResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetAclStatusResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetAclStatusResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetAclStatusResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_acl_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AclEntryProto_descriptor_, &AclEntryProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AclStatusProto_descriptor_, &AclStatusProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AclEditLogProto_descriptor_, &AclEditLogProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ModifyAclEntriesRequestProto_descriptor_, &ModifyAclEntriesRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ModifyAclEntriesResponseProto_descriptor_, &ModifyAclEntriesResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveAclRequestProto_descriptor_, &RemoveAclRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveAclResponseProto_descriptor_, &RemoveAclResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveAclEntriesRequestProto_descriptor_, &RemoveAclEntriesRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveAclEntriesResponseProto_descriptor_, &RemoveAclEntriesResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveDefaultAclRequestProto_descriptor_, &RemoveDefaultAclRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveDefaultAclResponseProto_descriptor_, &RemoveDefaultAclResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SetAclRequestProto_descriptor_, &SetAclRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SetAclResponseProto_descriptor_, &SetAclResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetAclStatusRequestProto_descriptor_, &GetAclStatusRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetAclStatusResponseProto_descriptor_, &GetAclStatusResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_acl_2eproto() {
  delete AclEntryProto::default_instance_;
  delete AclEntryProto_reflection_;
  delete AclStatusProto::default_instance_;
  delete AclStatusProto_reflection_;
  delete AclEditLogProto::default_instance_;
  delete AclEditLogProto_reflection_;
  delete ModifyAclEntriesRequestProto::default_instance_;
  delete ModifyAclEntriesRequestProto_reflection_;
  delete ModifyAclEntriesResponseProto::default_instance_;
  delete ModifyAclEntriesResponseProto_reflection_;
  delete RemoveAclRequestProto::default_instance_;
  delete RemoveAclRequestProto_reflection_;
  delete RemoveAclResponseProto::default_instance_;
  delete RemoveAclResponseProto_reflection_;
  delete RemoveAclEntriesRequestProto::default_instance_;
  delete RemoveAclEntriesRequestProto_reflection_;
  delete RemoveAclEntriesResponseProto::default_instance_;
  delete RemoveAclEntriesResponseProto_reflection_;
  delete RemoveDefaultAclRequestProto::default_instance_;
  delete RemoveDefaultAclRequestProto_reflection_;
  delete RemoveDefaultAclResponseProto::default_instance_;
  delete RemoveDefaultAclResponseProto_reflection_;
  delete SetAclRequestProto::default_instance_;
  delete SetAclRequestProto_reflection_;
  delete SetAclResponseProto::default_instance_;
  delete SetAclResponseProto_reflection_;
  delete GetAclStatusRequestProto::default_instance_;
  delete GetAclStatusRequestProto_reflection_;
  delete GetAclStatusResponseProto::default_instance_;
  delete GetAclStatusResponseProto_reflection_;
}

void protobuf_AddDesc_acl_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\tacl.proto\022\013hadoop.hdfs\032\nhdfs.proto\"\304\003\n"
    "\rAclEntryProto\022:\n\004type\030\001 \002(\0162,.hadoop.hd"
    "fs.AclEntryProto.AclEntryTypeProto\022<\n\005sc"
    "ope\030\002 \002(\0162-.hadoop.hdfs.AclEntryProto.Ac"
    "lEntryScopeProto\022=\n\013permissions\030\003 \002(\0162(."
    "hadoop.hdfs.AclEntryProto.FsActionProto\022"
    "\014\n\004name\030\004 \001(\t\"-\n\022AclEntryScopeProto\022\n\n\006A"
    "CCESS\020\000\022\013\n\007DEFAULT\020\001\"=\n\021AclEntryTypeProt"
    "o\022\010\n\004USER\020\000\022\t\n\005GROUP\020\001\022\010\n\004MASK\020\002\022\t\n\005OTHE"
    "R\020\003\"~\n\rFsActionProto\022\010\n\004NONE\020\000\022\013\n\007EXECUT"
    "E\020\001\022\t\n\005WRITE\020\002\022\021\n\rWRITE_EXECUTE\020\003\022\010\n\004REA"
    "D\020\004\022\020\n\014READ_EXECUTE\020\005\022\016\n\nREAD_WRITE\020\006\022\014\n"
    "\010PERM_ALL\020\007\"k\n\016AclStatusProto\022\r\n\005owner\030\001"
    " \002(\t\022\r\n\005group\030\002 \002(\t\022\016\n\006sticky\030\003 \002(\010\022+\n\007e"
    "ntries\030\004 \003(\0132\032.hadoop.hdfs.AclEntryProto"
    "\"K\n\017AclEditLogProto\022\013\n\003src\030\001 \002(\t\022+\n\007entr"
    "ies\030\002 \003(\0132\032.hadoop.hdfs.AclEntryProto\"X\n"
    "\034ModifyAclEntriesRequestProto\022\013\n\003src\030\001 \002"
    "(\t\022+\n\007aclSpec\030\002 \003(\0132\032.hadoop.hdfs.AclEnt"
    "ryProto\"\037\n\035ModifyAclEntriesResponseProto"
    "\"$\n\025RemoveAclRequestProto\022\013\n\003src\030\001 \002(\t\"\030"
    "\n\026RemoveAclResponseProto\"X\n\034RemoveAclEnt"
    "riesRequestProto\022\013\n\003src\030\001 \002(\t\022+\n\007aclSpec"
    "\030\002 \003(\0132\032.hadoop.hdfs.AclEntryProto\"\037\n\035Re"
    "moveAclEntriesResponseProto\"+\n\034RemoveDef"
    "aultAclRequestProto\022\013\n\003src\030\001 \002(\t\"\037\n\035Remo"
    "veDefaultAclResponseProto\"N\n\022SetAclReque"
    "stProto\022\013\n\003src\030\001 \002(\t\022+\n\007aclSpec\030\002 \003(\0132\032."
    "hadoop.hdfs.AclEntryProto\"\025\n\023SetAclRespo"
    "nseProto\"\'\n\030GetAclStatusRequestProto\022\013\n\003"
    "src\030\001 \002(\t\"H\n\031GetAclStatusResponseProto\022+"
    "\n\006result\030\001 \002(\0132\033.hadoop.hdfs.AclStatusPr"
    "otoB5\n%org.apache.hadoop.hdfs.protocol.p"
    "rotoB\tAclProtos\240\001\001", 1338);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "acl.proto", &protobuf_RegisterTypes);
  AclEntryProto::default_instance_ = new AclEntryProto();
  AclStatusProto::default_instance_ = new AclStatusProto();
  AclEditLogProto::default_instance_ = new AclEditLogProto();
  ModifyAclEntriesRequestProto::default_instance_ = new ModifyAclEntriesRequestProto();
  ModifyAclEntriesResponseProto::default_instance_ = new ModifyAclEntriesResponseProto();
  RemoveAclRequestProto::default_instance_ = new RemoveAclRequestProto();
  RemoveAclResponseProto::default_instance_ = new RemoveAclResponseProto();
  RemoveAclEntriesRequestProto::default_instance_ = new RemoveAclEntriesRequestProto();
  RemoveAclEntriesResponseProto::default_instance_ = new RemoveAclEntriesResponseProto();
  RemoveDefaultAclRequestProto::default_instance_ = new RemoveDefaultAclRequestProto();
  RemoveDefaultAclResponseProto::default_instance_ = new RemoveDefaultAclResponseProto();
  SetAclRequestProto::default_instance_ = new SetAclRequestProto();
  SetAclResponseProto::default_instance_ = new SetAclResponseProto();
  GetAclStatusRequestProto::default_instance_ = new GetAclStatusRequestProto();
  GetAclStatusResponseProto::default_instance_ = new GetAclStatusResponseProto();
  AclEntryProto::default_instance_->InitAsDefaultInstance();
  AclStatusProto::default_instance_->InitAsDefaultInstance();
  AclEditLogProto::default_instance_->InitAsDefaultInstance();
  ModifyAclEntriesRequestProto::default_instance_->InitAsDefaultInstance();
  ModifyAclEntriesResponseProto::default_instance_->InitAsDefaultInstance();
  RemoveAclRequestProto::default_instance_->InitAsDefaultInstance();
  RemoveAclResponseProto::default_instance_->InitAsDefaultInstance();
  RemoveAclEntriesRequestProto::default_instance_->InitAsDefaultInstance();
  RemoveAclEntriesResponseProto::default_instance_->InitAsDefaultInstance();
  RemoveDefaultAclRequestProto::default_instance_->InitAsDefaultInstance();
  RemoveDefaultAclResponseProto::default_instance_->InitAsDefaultInstance();
  SetAclRequestProto::default_instance_->InitAsDefaultInstance();
  SetAclResponseProto::default_instance_->InitAsDefaultInstance();
  GetAclStatusRequestProto::default_instance_->InitAsDefaultInstance();
  GetAclStatusResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_acl_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_acl_2eproto {
  StaticDescriptorInitializer_acl_2eproto() {
    protobuf_AddDesc_acl_2eproto();
  }
} static_descriptor_initializer_acl_2eproto_;

// ===================================================================

const ::google::protobuf::EnumDescriptor* AclEntryProto_AclEntryScopeProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AclEntryProto_AclEntryScopeProto_descriptor_;
}
bool AclEntryProto_AclEntryScopeProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const AclEntryProto_AclEntryScopeProto AclEntryProto::ACCESS;
const AclEntryProto_AclEntryScopeProto AclEntryProto::DEFAULT;
const AclEntryProto_AclEntryScopeProto AclEntryProto::AclEntryScopeProto_MIN;
const AclEntryProto_AclEntryScopeProto AclEntryProto::AclEntryScopeProto_MAX;
const int AclEntryProto::AclEntryScopeProto_ARRAYSIZE;
#endif  // _MSC_VER
const ::google::protobuf::EnumDescriptor* AclEntryProto_AclEntryTypeProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AclEntryProto_AclEntryTypeProto_descriptor_;
}
bool AclEntryProto_AclEntryTypeProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const AclEntryProto_AclEntryTypeProto AclEntryProto::USER;
const AclEntryProto_AclEntryTypeProto AclEntryProto::GROUP;
const AclEntryProto_AclEntryTypeProto AclEntryProto::MASK;
const AclEntryProto_AclEntryTypeProto AclEntryProto::OTHER;
const AclEntryProto_AclEntryTypeProto AclEntryProto::AclEntryTypeProto_MIN;
const AclEntryProto_AclEntryTypeProto AclEntryProto::AclEntryTypeProto_MAX;
const int AclEntryProto::AclEntryTypeProto_ARRAYSIZE;
#endif  // _MSC_VER
const ::google::protobuf::EnumDescriptor* AclEntryProto_FsActionProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AclEntryProto_FsActionProto_descriptor_;
}
bool AclEntryProto_FsActionProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const AclEntryProto_FsActionProto AclEntryProto::NONE;
const AclEntryProto_FsActionProto AclEntryProto::EXECUTE;
const AclEntryProto_FsActionProto AclEntryProto::WRITE;
const AclEntryProto_FsActionProto AclEntryProto::WRITE_EXECUTE;
const AclEntryProto_FsActionProto AclEntryProto::READ;
const AclEntryProto_FsActionProto AclEntryProto::READ_EXECUTE;
const AclEntryProto_FsActionProto AclEntryProto::READ_WRITE;
const AclEntryProto_FsActionProto AclEntryProto::PERM_ALL;
const AclEntryProto_FsActionProto AclEntryProto::FsActionProto_MIN;
const AclEntryProto_FsActionProto AclEntryProto::FsActionProto_MAX;
const int AclEntryProto::FsActionProto_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int AclEntryProto::kTypeFieldNumber;
const int AclEntryProto::kScopeFieldNumber;
const int AclEntryProto::kPermissionsFieldNumber;
const int AclEntryProto::kNameFieldNumber;
#endif  // !_MSC_VER

AclEntryProto::AclEntryProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AclEntryProto::InitAsDefaultInstance() {
}

AclEntryProto::AclEntryProto(const AclEntryProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AclEntryProto::SharedCtor() {
  _cached_size_ = 0;
  type_ = 0;
  scope_ = 0;
  permissions_ = 0;
  name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AclEntryProto::~AclEntryProto() {
  SharedDtor();
}

void AclEntryProto::SharedDtor() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (this != default_instance_) {
  }
}

void AclEntryProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AclEntryProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AclEntryProto_descriptor_;
}

const AclEntryProto& AclEntryProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

AclEntryProto* AclEntryProto::default_instance_ = NULL;

AclEntryProto* AclEntryProto::New() const {
  return new AclEntryProto;
}

void AclEntryProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    type_ = 0;
    scope_ = 0;
    permissions_ = 0;
    if (has_name()) {
      if (name_ != &::google::protobuf::internal::kEmptyString) {
        name_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AclEntryProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.AclEntryProto.AclEntryTypeProto type = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::AclEntryProto_AclEntryTypeProto_IsValid(value)) {
            set_type(static_cast< ::hadoop::hdfs::AclEntryProto_AclEntryTypeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_scope;
        break;
      }

      // required .hadoop.hdfs.AclEntryProto.AclEntryScopeProto scope = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_scope:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::AclEntryProto_AclEntryScopeProto_IsValid(value)) {
            set_scope(static_cast< ::hadoop::hdfs::AclEntryProto_AclEntryScopeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(2, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_permissions;
        break;
      }

      // required .hadoop.hdfs.AclEntryProto.FsActionProto permissions = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_permissions:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::hdfs::AclEntryProto_FsActionProto_IsValid(value)) {
            set_permissions(static_cast< ::hadoop::hdfs::AclEntryProto_FsActionProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(3, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_name;
        break;
      }

      // optional string name = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->name().data(), this->name().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AclEntryProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.AclEntryProto.AclEntryTypeProto type = 1;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // required .hadoop.hdfs.AclEntryProto.AclEntryScopeProto scope = 2;
  if (has_scope()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->scope(), output);
  }

  // required .hadoop.hdfs.AclEntryProto.FsActionProto permissions = 3;
  if (has_permissions()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->permissions(), output);
  }

  // optional string name = 4;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->name(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AclEntryProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.AclEntryProto.AclEntryTypeProto type = 1;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // required .hadoop.hdfs.AclEntryProto.AclEntryScopeProto scope = 2;
  if (has_scope()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->scope(), target);
  }

  // required .hadoop.hdfs.AclEntryProto.FsActionProto permissions = 3;
  if (has_permissions()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->permissions(), target);
  }

  // optional string name = 4;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->name(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AclEntryProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.AclEntryProto.AclEntryTypeProto type = 1;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
    }

    // required .hadoop.hdfs.AclEntryProto.AclEntryScopeProto scope = 2;
    if (has_scope()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->scope());
    }

    // required .hadoop.hdfs.AclEntryProto.FsActionProto permissions = 3;
    if (has_permissions()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->permissions());
    }

    // optional string name = 4;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->name());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AclEntryProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AclEntryProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AclEntryProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AclEntryProto::MergeFrom(const AclEntryProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
    if (from.has_scope()) {
      set_scope(from.scope());
    }
    if (from.has_permissions()) {
      set_permissions(from.permissions());
    }
    if (from.has_name()) {
      set_name(from.name());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AclEntryProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AclEntryProto::CopyFrom(const AclEntryProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AclEntryProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void AclEntryProto::Swap(AclEntryProto* other) {
  if (other != this) {
    std::swap(type_, other->type_);
    std::swap(scope_, other->scope_);
    std::swap(permissions_, other->permissions_);
    std::swap(name_, other->name_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AclEntryProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AclEntryProto_descriptor_;
  metadata.reflection = AclEntryProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int AclStatusProto::kOwnerFieldNumber;
const int AclStatusProto::kGroupFieldNumber;
const int AclStatusProto::kStickyFieldNumber;
const int AclStatusProto::kEntriesFieldNumber;
#endif  // !_MSC_VER

AclStatusProto::AclStatusProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AclStatusProto::InitAsDefaultInstance() {
}

AclStatusProto::AclStatusProto(const AclStatusProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AclStatusProto::SharedCtor() {
  _cached_size_ = 0;
  owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  group_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  sticky_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AclStatusProto::~AclStatusProto() {
  SharedDtor();
}

void AclStatusProto::SharedDtor() {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    delete owner_;
  }
  if (group_ != &::google::protobuf::internal::kEmptyString) {
    delete group_;
  }
  if (this != default_instance_) {
  }
}

void AclStatusProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AclStatusProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AclStatusProto_descriptor_;
}

const AclStatusProto& AclStatusProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

AclStatusProto* AclStatusProto::default_instance_ = NULL;

AclStatusProto* AclStatusProto::New() const {
  return new AclStatusProto;
}

void AclStatusProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_owner()) {
      if (owner_ != &::google::protobuf::internal::kEmptyString) {
        owner_->clear();
      }
    }
    if (has_group()) {
      if (group_ != &::google::protobuf::internal::kEmptyString) {
        group_->clear();
      }
    }
    sticky_ = false;
  }
  entries_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AclStatusProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string owner = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_owner()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->owner().data(), this->owner().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_group;
        break;
      }

      // required string group = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_group:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_group()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->group().data(), this->group().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_sticky;
        break;
      }

      // required bool sticky = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_sticky:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &sticky_)));
          set_has_sticky();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_entries;
        break;
      }

      // repeated .hadoop.hdfs.AclEntryProto entries = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_entries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_entries()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_entries;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AclStatusProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string owner = 1;
  if (has_owner()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->owner().data(), this->owner().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->owner(), output);
  }

  // required string group = 2;
  if (has_group()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->group().data(), this->group().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->group(), output);
  }

  // required bool sticky = 3;
  if (has_sticky()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->sticky(), output);
  }

  // repeated .hadoop.hdfs.AclEntryProto entries = 4;
  for (int i = 0; i < this->entries_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->entries(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AclStatusProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string owner = 1;
  if (has_owner()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->owner().data(), this->owner().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->owner(), target);
  }

  // required string group = 2;
  if (has_group()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->group().data(), this->group().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->group(), target);
  }

  // required bool sticky = 3;
  if (has_sticky()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->sticky(), target);
  }

  // repeated .hadoop.hdfs.AclEntryProto entries = 4;
  for (int i = 0; i < this->entries_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->entries(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AclStatusProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string owner = 1;
    if (has_owner()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->owner());
    }

    // required string group = 2;
    if (has_group()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->group());
    }

    // required bool sticky = 3;
    if (has_sticky()) {
      total_size += 1 + 1;
    }

  }
  // repeated .hadoop.hdfs.AclEntryProto entries = 4;
  total_size += 1 * this->entries_size();
  for (int i = 0; i < this->entries_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->entries(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AclStatusProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AclStatusProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AclStatusProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AclStatusProto::MergeFrom(const AclStatusProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  entries_.MergeFrom(from.entries_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_owner()) {
      set_owner(from.owner());
    }
    if (from.has_group()) {
      set_group(from.group());
    }
    if (from.has_sticky()) {
      set_sticky(from.sticky());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AclStatusProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AclStatusProto::CopyFrom(const AclStatusProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AclStatusProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  for (int i = 0; i < entries_size(); i++) {
    if (!this->entries(i).IsInitialized()) return false;
  }
  return true;
}

void AclStatusProto::Swap(AclStatusProto* other) {
  if (other != this) {
    std::swap(owner_, other->owner_);
    std::swap(group_, other->group_);
    std::swap(sticky_, other->sticky_);
    entries_.Swap(&other->entries_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AclStatusProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AclStatusProto_descriptor_;
  metadata.reflection = AclStatusProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int AclEditLogProto::kSrcFieldNumber;
const int AclEditLogProto::kEntriesFieldNumber;
#endif  // !_MSC_VER

AclEditLogProto::AclEditLogProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AclEditLogProto::InitAsDefaultInstance() {
}

AclEditLogProto::AclEditLogProto(const AclEditLogProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AclEditLogProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AclEditLogProto::~AclEditLogProto() {
  SharedDtor();
}

void AclEditLogProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void AclEditLogProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AclEditLogProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AclEditLogProto_descriptor_;
}

const AclEditLogProto& AclEditLogProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

AclEditLogProto* AclEditLogProto::default_instance_ = NULL;

AclEditLogProto* AclEditLogProto::New() const {
  return new AclEditLogProto;
}

void AclEditLogProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  entries_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AclEditLogProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_entries;
        break;
      }

      // repeated .hadoop.hdfs.AclEntryProto entries = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_entries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_entries()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_entries;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AclEditLogProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // repeated .hadoop.hdfs.AclEntryProto entries = 2;
  for (int i = 0; i < this->entries_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->entries(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AclEditLogProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // repeated .hadoop.hdfs.AclEntryProto entries = 2;
  for (int i = 0; i < this->entries_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->entries(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AclEditLogProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  // repeated .hadoop.hdfs.AclEntryProto entries = 2;
  total_size += 1 * this->entries_size();
  for (int i = 0; i < this->entries_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->entries(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AclEditLogProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AclEditLogProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AclEditLogProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AclEditLogProto::MergeFrom(const AclEditLogProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  entries_.MergeFrom(from.entries_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AclEditLogProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AclEditLogProto::CopyFrom(const AclEditLogProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AclEditLogProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < entries_size(); i++) {
    if (!this->entries(i).IsInitialized()) return false;
  }
  return true;
}

void AclEditLogProto::Swap(AclEditLogProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    entries_.Swap(&other->entries_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AclEditLogProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AclEditLogProto_descriptor_;
  metadata.reflection = AclEditLogProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ModifyAclEntriesRequestProto::kSrcFieldNumber;
const int ModifyAclEntriesRequestProto::kAclSpecFieldNumber;
#endif  // !_MSC_VER

ModifyAclEntriesRequestProto::ModifyAclEntriesRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ModifyAclEntriesRequestProto::InitAsDefaultInstance() {
}

ModifyAclEntriesRequestProto::ModifyAclEntriesRequestProto(const ModifyAclEntriesRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ModifyAclEntriesRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ModifyAclEntriesRequestProto::~ModifyAclEntriesRequestProto() {
  SharedDtor();
}

void ModifyAclEntriesRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void ModifyAclEntriesRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ModifyAclEntriesRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ModifyAclEntriesRequestProto_descriptor_;
}

const ModifyAclEntriesRequestProto& ModifyAclEntriesRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

ModifyAclEntriesRequestProto* ModifyAclEntriesRequestProto::default_instance_ = NULL;

ModifyAclEntriesRequestProto* ModifyAclEntriesRequestProto::New() const {
  return new ModifyAclEntriesRequestProto;
}

void ModifyAclEntriesRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  aclspec_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ModifyAclEntriesRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_aclSpec;
        break;
      }

      // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_aclSpec:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_aclspec()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_aclSpec;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ModifyAclEntriesRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  for (int i = 0; i < this->aclspec_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->aclspec(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ModifyAclEntriesRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  for (int i = 0; i < this->aclspec_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->aclspec(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ModifyAclEntriesRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  total_size += 1 * this->aclspec_size();
  for (int i = 0; i < this->aclspec_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->aclspec(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ModifyAclEntriesRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ModifyAclEntriesRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ModifyAclEntriesRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ModifyAclEntriesRequestProto::MergeFrom(const ModifyAclEntriesRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  aclspec_.MergeFrom(from.aclspec_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ModifyAclEntriesRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ModifyAclEntriesRequestProto::CopyFrom(const ModifyAclEntriesRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModifyAclEntriesRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < aclspec_size(); i++) {
    if (!this->aclspec(i).IsInitialized()) return false;
  }
  return true;
}

void ModifyAclEntriesRequestProto::Swap(ModifyAclEntriesRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    aclspec_.Swap(&other->aclspec_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ModifyAclEntriesRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ModifyAclEntriesRequestProto_descriptor_;
  metadata.reflection = ModifyAclEntriesRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

ModifyAclEntriesResponseProto::ModifyAclEntriesResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ModifyAclEntriesResponseProto::InitAsDefaultInstance() {
}

ModifyAclEntriesResponseProto::ModifyAclEntriesResponseProto(const ModifyAclEntriesResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ModifyAclEntriesResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ModifyAclEntriesResponseProto::~ModifyAclEntriesResponseProto() {
  SharedDtor();
}

void ModifyAclEntriesResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ModifyAclEntriesResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ModifyAclEntriesResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ModifyAclEntriesResponseProto_descriptor_;
}

const ModifyAclEntriesResponseProto& ModifyAclEntriesResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

ModifyAclEntriesResponseProto* ModifyAclEntriesResponseProto::default_instance_ = NULL;

ModifyAclEntriesResponseProto* ModifyAclEntriesResponseProto::New() const {
  return new ModifyAclEntriesResponseProto;
}

void ModifyAclEntriesResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ModifyAclEntriesResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void ModifyAclEntriesResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ModifyAclEntriesResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ModifyAclEntriesResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ModifyAclEntriesResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ModifyAclEntriesResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ModifyAclEntriesResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ModifyAclEntriesResponseProto::MergeFrom(const ModifyAclEntriesResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ModifyAclEntriesResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ModifyAclEntriesResponseProto::CopyFrom(const ModifyAclEntriesResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModifyAclEntriesResponseProto::IsInitialized() const {

  return true;
}

void ModifyAclEntriesResponseProto::Swap(ModifyAclEntriesResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ModifyAclEntriesResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ModifyAclEntriesResponseProto_descriptor_;
  metadata.reflection = ModifyAclEntriesResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RemoveAclRequestProto::kSrcFieldNumber;
#endif  // !_MSC_VER

RemoveAclRequestProto::RemoveAclRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveAclRequestProto::InitAsDefaultInstance() {
}

RemoveAclRequestProto::RemoveAclRequestProto(const RemoveAclRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveAclRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveAclRequestProto::~RemoveAclRequestProto() {
  SharedDtor();
}

void RemoveAclRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void RemoveAclRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveAclRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveAclRequestProto_descriptor_;
}

const RemoveAclRequestProto& RemoveAclRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

RemoveAclRequestProto* RemoveAclRequestProto::default_instance_ = NULL;

RemoveAclRequestProto* RemoveAclRequestProto::New() const {
  return new RemoveAclRequestProto;
}

void RemoveAclRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveAclRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RemoveAclRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveAclRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveAclRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveAclRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveAclRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveAclRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveAclRequestProto::MergeFrom(const RemoveAclRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveAclRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveAclRequestProto::CopyFrom(const RemoveAclRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveAclRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void RemoveAclRequestProto::Swap(RemoveAclRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveAclRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveAclRequestProto_descriptor_;
  metadata.reflection = RemoveAclRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RemoveAclResponseProto::RemoveAclResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveAclResponseProto::InitAsDefaultInstance() {
}

RemoveAclResponseProto::RemoveAclResponseProto(const RemoveAclResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveAclResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveAclResponseProto::~RemoveAclResponseProto() {
  SharedDtor();
}

void RemoveAclResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RemoveAclResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveAclResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveAclResponseProto_descriptor_;
}

const RemoveAclResponseProto& RemoveAclResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

RemoveAclResponseProto* RemoveAclResponseProto::default_instance_ = NULL;

RemoveAclResponseProto* RemoveAclResponseProto::New() const {
  return new RemoveAclResponseProto;
}

void RemoveAclResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveAclResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RemoveAclResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveAclResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveAclResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveAclResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveAclResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveAclResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveAclResponseProto::MergeFrom(const RemoveAclResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveAclResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveAclResponseProto::CopyFrom(const RemoveAclResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveAclResponseProto::IsInitialized() const {

  return true;
}

void RemoveAclResponseProto::Swap(RemoveAclResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveAclResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveAclResponseProto_descriptor_;
  metadata.reflection = RemoveAclResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RemoveAclEntriesRequestProto::kSrcFieldNumber;
const int RemoveAclEntriesRequestProto::kAclSpecFieldNumber;
#endif  // !_MSC_VER

RemoveAclEntriesRequestProto::RemoveAclEntriesRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveAclEntriesRequestProto::InitAsDefaultInstance() {
}

RemoveAclEntriesRequestProto::RemoveAclEntriesRequestProto(const RemoveAclEntriesRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveAclEntriesRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveAclEntriesRequestProto::~RemoveAclEntriesRequestProto() {
  SharedDtor();
}

void RemoveAclEntriesRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void RemoveAclEntriesRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveAclEntriesRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveAclEntriesRequestProto_descriptor_;
}

const RemoveAclEntriesRequestProto& RemoveAclEntriesRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

RemoveAclEntriesRequestProto* RemoveAclEntriesRequestProto::default_instance_ = NULL;

RemoveAclEntriesRequestProto* RemoveAclEntriesRequestProto::New() const {
  return new RemoveAclEntriesRequestProto;
}

void RemoveAclEntriesRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  aclspec_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveAclEntriesRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_aclSpec;
        break;
      }

      // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_aclSpec:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_aclspec()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_aclSpec;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RemoveAclEntriesRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  for (int i = 0; i < this->aclspec_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->aclspec(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveAclEntriesRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  for (int i = 0; i < this->aclspec_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->aclspec(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveAclEntriesRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  total_size += 1 * this->aclspec_size();
  for (int i = 0; i < this->aclspec_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->aclspec(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveAclEntriesRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveAclEntriesRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveAclEntriesRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveAclEntriesRequestProto::MergeFrom(const RemoveAclEntriesRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  aclspec_.MergeFrom(from.aclspec_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveAclEntriesRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveAclEntriesRequestProto::CopyFrom(const RemoveAclEntriesRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveAclEntriesRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < aclspec_size(); i++) {
    if (!this->aclspec(i).IsInitialized()) return false;
  }
  return true;
}

void RemoveAclEntriesRequestProto::Swap(RemoveAclEntriesRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    aclspec_.Swap(&other->aclspec_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveAclEntriesRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveAclEntriesRequestProto_descriptor_;
  metadata.reflection = RemoveAclEntriesRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RemoveAclEntriesResponseProto::RemoveAclEntriesResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveAclEntriesResponseProto::InitAsDefaultInstance() {
}

RemoveAclEntriesResponseProto::RemoveAclEntriesResponseProto(const RemoveAclEntriesResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveAclEntriesResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveAclEntriesResponseProto::~RemoveAclEntriesResponseProto() {
  SharedDtor();
}

void RemoveAclEntriesResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RemoveAclEntriesResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveAclEntriesResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveAclEntriesResponseProto_descriptor_;
}

const RemoveAclEntriesResponseProto& RemoveAclEntriesResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

RemoveAclEntriesResponseProto* RemoveAclEntriesResponseProto::default_instance_ = NULL;

RemoveAclEntriesResponseProto* RemoveAclEntriesResponseProto::New() const {
  return new RemoveAclEntriesResponseProto;
}

void RemoveAclEntriesResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveAclEntriesResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RemoveAclEntriesResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveAclEntriesResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveAclEntriesResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveAclEntriesResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveAclEntriesResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveAclEntriesResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveAclEntriesResponseProto::MergeFrom(const RemoveAclEntriesResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveAclEntriesResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveAclEntriesResponseProto::CopyFrom(const RemoveAclEntriesResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveAclEntriesResponseProto::IsInitialized() const {

  return true;
}

void RemoveAclEntriesResponseProto::Swap(RemoveAclEntriesResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveAclEntriesResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveAclEntriesResponseProto_descriptor_;
  metadata.reflection = RemoveAclEntriesResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RemoveDefaultAclRequestProto::kSrcFieldNumber;
#endif  // !_MSC_VER

RemoveDefaultAclRequestProto::RemoveDefaultAclRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveDefaultAclRequestProto::InitAsDefaultInstance() {
}

RemoveDefaultAclRequestProto::RemoveDefaultAclRequestProto(const RemoveDefaultAclRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveDefaultAclRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveDefaultAclRequestProto::~RemoveDefaultAclRequestProto() {
  SharedDtor();
}

void RemoveDefaultAclRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void RemoveDefaultAclRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveDefaultAclRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveDefaultAclRequestProto_descriptor_;
}

const RemoveDefaultAclRequestProto& RemoveDefaultAclRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

RemoveDefaultAclRequestProto* RemoveDefaultAclRequestProto::default_instance_ = NULL;

RemoveDefaultAclRequestProto* RemoveDefaultAclRequestProto::New() const {
  return new RemoveDefaultAclRequestProto;
}

void RemoveDefaultAclRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveDefaultAclRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RemoveDefaultAclRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveDefaultAclRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveDefaultAclRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveDefaultAclRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveDefaultAclRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveDefaultAclRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveDefaultAclRequestProto::MergeFrom(const RemoveDefaultAclRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveDefaultAclRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveDefaultAclRequestProto::CopyFrom(const RemoveDefaultAclRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveDefaultAclRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void RemoveDefaultAclRequestProto::Swap(RemoveDefaultAclRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveDefaultAclRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveDefaultAclRequestProto_descriptor_;
  metadata.reflection = RemoveDefaultAclRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RemoveDefaultAclResponseProto::RemoveDefaultAclResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveDefaultAclResponseProto::InitAsDefaultInstance() {
}

RemoveDefaultAclResponseProto::RemoveDefaultAclResponseProto(const RemoveDefaultAclResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveDefaultAclResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveDefaultAclResponseProto::~RemoveDefaultAclResponseProto() {
  SharedDtor();
}

void RemoveDefaultAclResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RemoveDefaultAclResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveDefaultAclResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveDefaultAclResponseProto_descriptor_;
}

const RemoveDefaultAclResponseProto& RemoveDefaultAclResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

RemoveDefaultAclResponseProto* RemoveDefaultAclResponseProto::default_instance_ = NULL;

RemoveDefaultAclResponseProto* RemoveDefaultAclResponseProto::New() const {
  return new RemoveDefaultAclResponseProto;
}

void RemoveDefaultAclResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveDefaultAclResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RemoveDefaultAclResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveDefaultAclResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveDefaultAclResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveDefaultAclResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveDefaultAclResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveDefaultAclResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveDefaultAclResponseProto::MergeFrom(const RemoveDefaultAclResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveDefaultAclResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveDefaultAclResponseProto::CopyFrom(const RemoveDefaultAclResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveDefaultAclResponseProto::IsInitialized() const {

  return true;
}

void RemoveDefaultAclResponseProto::Swap(RemoveDefaultAclResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveDefaultAclResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveDefaultAclResponseProto_descriptor_;
  metadata.reflection = RemoveDefaultAclResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SetAclRequestProto::kSrcFieldNumber;
const int SetAclRequestProto::kAclSpecFieldNumber;
#endif  // !_MSC_VER

SetAclRequestProto::SetAclRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SetAclRequestProto::InitAsDefaultInstance() {
}

SetAclRequestProto::SetAclRequestProto(const SetAclRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SetAclRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SetAclRequestProto::~SetAclRequestProto() {
  SharedDtor();
}

void SetAclRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void SetAclRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SetAclRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SetAclRequestProto_descriptor_;
}

const SetAclRequestProto& SetAclRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

SetAclRequestProto* SetAclRequestProto::default_instance_ = NULL;

SetAclRequestProto* SetAclRequestProto::New() const {
  return new SetAclRequestProto;
}

void SetAclRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  aclspec_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SetAclRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_aclSpec;
        break;
      }

      // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_aclSpec:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_aclspec()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_aclSpec;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SetAclRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  for (int i = 0; i < this->aclspec_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->aclspec(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SetAclRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  for (int i = 0; i < this->aclspec_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->aclspec(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SetAclRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  // repeated .hadoop.hdfs.AclEntryProto aclSpec = 2;
  total_size += 1 * this->aclspec_size();
  for (int i = 0; i < this->aclspec_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->aclspec(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SetAclRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SetAclRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SetAclRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SetAclRequestProto::MergeFrom(const SetAclRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  aclspec_.MergeFrom(from.aclspec_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SetAclRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SetAclRequestProto::CopyFrom(const SetAclRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetAclRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < aclspec_size(); i++) {
    if (!this->aclspec(i).IsInitialized()) return false;
  }
  return true;
}

void SetAclRequestProto::Swap(SetAclRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    aclspec_.Swap(&other->aclspec_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SetAclRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SetAclRequestProto_descriptor_;
  metadata.reflection = SetAclRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

SetAclResponseProto::SetAclResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SetAclResponseProto::InitAsDefaultInstance() {
}

SetAclResponseProto::SetAclResponseProto(const SetAclResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SetAclResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SetAclResponseProto::~SetAclResponseProto() {
  SharedDtor();
}

void SetAclResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SetAclResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SetAclResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SetAclResponseProto_descriptor_;
}

const SetAclResponseProto& SetAclResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

SetAclResponseProto* SetAclResponseProto::default_instance_ = NULL;

SetAclResponseProto* SetAclResponseProto::New() const {
  return new SetAclResponseProto;
}

void SetAclResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SetAclResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void SetAclResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SetAclResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SetAclResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SetAclResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SetAclResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SetAclResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SetAclResponseProto::MergeFrom(const SetAclResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SetAclResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SetAclResponseProto::CopyFrom(const SetAclResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetAclResponseProto::IsInitialized() const {

  return true;
}

void SetAclResponseProto::Swap(SetAclResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SetAclResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SetAclResponseProto_descriptor_;
  metadata.reflection = SetAclResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetAclStatusRequestProto::kSrcFieldNumber;
#endif  // !_MSC_VER

GetAclStatusRequestProto::GetAclStatusRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetAclStatusRequestProto::InitAsDefaultInstance() {
}

GetAclStatusRequestProto::GetAclStatusRequestProto(const GetAclStatusRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetAclStatusRequestProto::SharedCtor() {
  _cached_size_ = 0;
  src_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetAclStatusRequestProto::~GetAclStatusRequestProto() {
  SharedDtor();
}

void GetAclStatusRequestProto::SharedDtor() {
  if (src_ != &::google::protobuf::internal::kEmptyString) {
    delete src_;
  }
  if (this != default_instance_) {
  }
}

void GetAclStatusRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetAclStatusRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetAclStatusRequestProto_descriptor_;
}

const GetAclStatusRequestProto& GetAclStatusRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

GetAclStatusRequestProto* GetAclStatusRequestProto::default_instance_ = NULL;

GetAclStatusRequestProto* GetAclStatusRequestProto::New() const {
  return new GetAclStatusRequestProto;
}

void GetAclStatusRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_src()) {
      if (src_ != &::google::protobuf::internal::kEmptyString) {
        src_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetAclStatusRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string src = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_src()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->src().data(), this->src().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetAclStatusRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->src(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetAclStatusRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string src = 1;
  if (has_src()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->src().data(), this->src().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->src(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetAclStatusRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string src = 1;
    if (has_src()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->src());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetAclStatusRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetAclStatusRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetAclStatusRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetAclStatusRequestProto::MergeFrom(const GetAclStatusRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_src()) {
      set_src(from.src());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetAclStatusRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetAclStatusRequestProto::CopyFrom(const GetAclStatusRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetAclStatusRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetAclStatusRequestProto::Swap(GetAclStatusRequestProto* other) {
  if (other != this) {
    std::swap(src_, other->src_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetAclStatusRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetAclStatusRequestProto_descriptor_;
  metadata.reflection = GetAclStatusRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetAclStatusResponseProto::kResultFieldNumber;
#endif  // !_MSC_VER

GetAclStatusResponseProto::GetAclStatusResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetAclStatusResponseProto::InitAsDefaultInstance() {
  result_ = const_cast< ::hadoop::hdfs::AclStatusProto*>(&::hadoop::hdfs::AclStatusProto::default_instance());
}

GetAclStatusResponseProto::GetAclStatusResponseProto(const GetAclStatusResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetAclStatusResponseProto::SharedCtor() {
  _cached_size_ = 0;
  result_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetAclStatusResponseProto::~GetAclStatusResponseProto() {
  SharedDtor();
}

void GetAclStatusResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete result_;
  }
}

void GetAclStatusResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetAclStatusResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetAclStatusResponseProto_descriptor_;
}

const GetAclStatusResponseProto& GetAclStatusResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_acl_2eproto();
  return *default_instance_;
}

GetAclStatusResponseProto* GetAclStatusResponseProto::default_instance_ = NULL;

GetAclStatusResponseProto* GetAclStatusResponseProto::New() const {
  return new GetAclStatusResponseProto;
}

void GetAclStatusResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_result()) {
      if (result_ != NULL) result_->::hadoop::hdfs::AclStatusProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetAclStatusResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.AclStatusProto result = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_result()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetAclStatusResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.AclStatusProto result = 1;
  if (has_result()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->result(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetAclStatusResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.AclStatusProto result = 1;
  if (has_result()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->result(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetAclStatusResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.AclStatusProto result = 1;
    if (has_result()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->result());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetAclStatusResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetAclStatusResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetAclStatusResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetAclStatusResponseProto::MergeFrom(const GetAclStatusResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_result()) {
      mutable_result()->::hadoop::hdfs::AclStatusProto::MergeFrom(from.result());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetAclStatusResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetAclStatusResponseProto::CopyFrom(const GetAclStatusResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetAclStatusResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_result()) {
    if (!this->result().IsInitialized()) return false;
  }
  return true;
}

void GetAclStatusResponseProto::Swap(GetAclStatusResponseProto* other) {
  if (other != this) {
    std::swap(result_, other->result_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetAclStatusResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetAclStatusResponseProto_descriptor_;
  metadata.reflection = GetAclStatusResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
