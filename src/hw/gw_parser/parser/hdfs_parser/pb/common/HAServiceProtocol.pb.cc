// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/HAServiceProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/HAServiceProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* HAStateChangeRequestInfoProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  HAStateChangeRequestInfoProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* MonitorHealthRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MonitorHealthRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* MonitorHealthResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MonitorHealthResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* TransitionToActiveRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TransitionToActiveRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* TransitionToActiveResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TransitionToActiveResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* TransitionToStandbyRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TransitionToStandbyRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* TransitionToStandbyResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TransitionToStandbyResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetServiceStatusRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetServiceStatusRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetServiceStatusResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetServiceStatusResponseProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* HAServiceStateProto_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* HARequestSource_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto() {
  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/HAServiceProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  HAStateChangeRequestInfoProto_descriptor_ = file->message_type(0);
  static const int HAStateChangeRequestInfoProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HAStateChangeRequestInfoProto, reqsource_),
  };
  HAStateChangeRequestInfoProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      HAStateChangeRequestInfoProto_descriptor_,
      HAStateChangeRequestInfoProto::default_instance_,
      HAStateChangeRequestInfoProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HAStateChangeRequestInfoProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HAStateChangeRequestInfoProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(HAStateChangeRequestInfoProto));
  MonitorHealthRequestProto_descriptor_ = file->message_type(1);
  static const int MonitorHealthRequestProto_offsets_[1] = {
  };
  MonitorHealthRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      MonitorHealthRequestProto_descriptor_,
      MonitorHealthRequestProto::default_instance_,
      MonitorHealthRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MonitorHealthRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MonitorHealthRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(MonitorHealthRequestProto));
  MonitorHealthResponseProto_descriptor_ = file->message_type(2);
  static const int MonitorHealthResponseProto_offsets_[1] = {
  };
  MonitorHealthResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      MonitorHealthResponseProto_descriptor_,
      MonitorHealthResponseProto::default_instance_,
      MonitorHealthResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MonitorHealthResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MonitorHealthResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(MonitorHealthResponseProto));
  TransitionToActiveRequestProto_descriptor_ = file->message_type(3);
  static const int TransitionToActiveRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToActiveRequestProto, reqinfo_),
  };
  TransitionToActiveRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      TransitionToActiveRequestProto_descriptor_,
      TransitionToActiveRequestProto::default_instance_,
      TransitionToActiveRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToActiveRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToActiveRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(TransitionToActiveRequestProto));
  TransitionToActiveResponseProto_descriptor_ = file->message_type(4);
  static const int TransitionToActiveResponseProto_offsets_[1] = {
  };
  TransitionToActiveResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      TransitionToActiveResponseProto_descriptor_,
      TransitionToActiveResponseProto::default_instance_,
      TransitionToActiveResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToActiveResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToActiveResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(TransitionToActiveResponseProto));
  TransitionToStandbyRequestProto_descriptor_ = file->message_type(5);
  static const int TransitionToStandbyRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToStandbyRequestProto, reqinfo_),
  };
  TransitionToStandbyRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      TransitionToStandbyRequestProto_descriptor_,
      TransitionToStandbyRequestProto::default_instance_,
      TransitionToStandbyRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToStandbyRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToStandbyRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(TransitionToStandbyRequestProto));
  TransitionToStandbyResponseProto_descriptor_ = file->message_type(6);
  static const int TransitionToStandbyResponseProto_offsets_[1] = {
  };
  TransitionToStandbyResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      TransitionToStandbyResponseProto_descriptor_,
      TransitionToStandbyResponseProto::default_instance_,
      TransitionToStandbyResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToStandbyResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TransitionToStandbyResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(TransitionToStandbyResponseProto));
  GetServiceStatusRequestProto_descriptor_ = file->message_type(7);
  static const int GetServiceStatusRequestProto_offsets_[1] = {
  };
  GetServiceStatusRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetServiceStatusRequestProto_descriptor_,
      GetServiceStatusRequestProto::default_instance_,
      GetServiceStatusRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetServiceStatusRequestProto));
  GetServiceStatusResponseProto_descriptor_ = file->message_type(8);
  static const int GetServiceStatusResponseProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusResponseProto, state_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusResponseProto, readytobecomeactive_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusResponseProto, notreadyreason_),
  };
  GetServiceStatusResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetServiceStatusResponseProto_descriptor_,
      GetServiceStatusResponseProto::default_instance_,
      GetServiceStatusResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetServiceStatusResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetServiceStatusResponseProto));
  HAServiceStateProto_descriptor_ = file->enum_type(0);
  HARequestSource_descriptor_ = file->enum_type(1);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    HAStateChangeRequestInfoProto_descriptor_, &HAStateChangeRequestInfoProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    MonitorHealthRequestProto_descriptor_, &MonitorHealthRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    MonitorHealthResponseProto_descriptor_, &MonitorHealthResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    TransitionToActiveRequestProto_descriptor_, &TransitionToActiveRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    TransitionToActiveResponseProto_descriptor_, &TransitionToActiveResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    TransitionToStandbyRequestProto_descriptor_, &TransitionToStandbyRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    TransitionToStandbyResponseProto_descriptor_, &TransitionToStandbyResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetServiceStatusRequestProto_descriptor_, &GetServiceStatusRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetServiceStatusResponseProto_descriptor_, &GetServiceStatusResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto() {
  delete HAStateChangeRequestInfoProto::default_instance_;
  delete HAStateChangeRequestInfoProto_reflection_;
  delete MonitorHealthRequestProto::default_instance_;
  delete MonitorHealthRequestProto_reflection_;
  delete MonitorHealthResponseProto::default_instance_;
  delete MonitorHealthResponseProto_reflection_;
  delete TransitionToActiveRequestProto::default_instance_;
  delete TransitionToActiveRequestProto_reflection_;
  delete TransitionToActiveResponseProto::default_instance_;
  delete TransitionToActiveResponseProto_reflection_;
  delete TransitionToStandbyRequestProto::default_instance_;
  delete TransitionToStandbyRequestProto_reflection_;
  delete TransitionToStandbyResponseProto::default_instance_;
  delete TransitionToStandbyResponseProto_reflection_;
  delete GetServiceStatusRequestProto::default_instance_;
  delete GetServiceStatusRequestProto_reflection_;
  delete GetServiceStatusResponseProto::default_instance_;
  delete GetServiceStatusResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fHAServiceProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\036common/HAServiceProtocol.proto\022\rhadoop"
    ".common\"R\n\035HAStateChangeRequestInfoProto"
    "\0221\n\treqSource\030\001 \002(\0162\036.hadoop.common.HARe"
    "questSource\"\033\n\031MonitorHealthRequestProto"
    "\"\034\n\032MonitorHealthResponseProto\"_\n\036Transi"
    "tionToActiveRequestProto\022=\n\007reqInfo\030\001 \002("
    "\0132,.hadoop.common.HAStateChangeRequestIn"
    "foProto\"!\n\037TransitionToActiveResponsePro"
    "to\"`\n\037TransitionToStandbyRequestProto\022=\n"
    "\007reqInfo\030\001 \002(\0132,.hadoop.common.HAStateCh"
    "angeRequestInfoProto\"\"\n TransitionToStan"
    "dbyResponseProto\"\036\n\034GetServiceStatusRequ"
    "estProto\"\207\001\n\035GetServiceStatusResponsePro"
    "to\0221\n\005state\030\001 \002(\0162\".hadoop.common.HAServ"
    "iceStateProto\022\033\n\023readyToBecomeActive\030\002 \001"
    "(\010\022\026\n\016notReadyReason\030\003 \001(\t*@\n\023HAServiceS"
    "tateProto\022\020\n\014INITIALIZING\020\000\022\n\n\006ACTIVE\020\001\022"
    "\013\n\007STANDBY\020\002*W\n\017HARequestSource\022\023\n\017REQUE"
    "ST_BY_USER\020\000\022\032\n\026REQUEST_BY_USER_FORCED\020\001"
    "\022\023\n\017REQUEST_BY_ZKFC\020\0022\334\003\n\030HAServiceProto"
    "colService\022d\n\rmonitorHealth\022(.hadoop.com"
    "mon.MonitorHealthRequestProto\032).hadoop.c"
    "ommon.MonitorHealthResponseProto\022s\n\022tran"
    "sitionToActive\022-.hadoop.common.Transitio"
    "nToActiveRequestProto\032..hadoop.common.Tr"
    "ansitionToActiveResponseProto\022v\n\023transit"
    "ionToStandby\022..hadoop.common.TransitionT"
    "oStandbyRequestProto\032/.hadoop.common.Tra"
    "nsitionToStandbyResponseProto\022m\n\020getServ"
    "iceStatus\022+.hadoop.common.GetServiceStat"
    "usRequestProto\032,.hadoop.common.GetServic"
    "eStatusResponseProtoB;\n\032org.apache.hadoo"
    "p.ha.protoB\027HAServiceProtocolProtos\210\001\001\240\001"
    "\001", 1321);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/HAServiceProtocol.proto", &protobuf_RegisterTypes);
  HAStateChangeRequestInfoProto::default_instance_ = new HAStateChangeRequestInfoProto();
  MonitorHealthRequestProto::default_instance_ = new MonitorHealthRequestProto();
  MonitorHealthResponseProto::default_instance_ = new MonitorHealthResponseProto();
  TransitionToActiveRequestProto::default_instance_ = new TransitionToActiveRequestProto();
  TransitionToActiveResponseProto::default_instance_ = new TransitionToActiveResponseProto();
  TransitionToStandbyRequestProto::default_instance_ = new TransitionToStandbyRequestProto();
  TransitionToStandbyResponseProto::default_instance_ = new TransitionToStandbyResponseProto();
  GetServiceStatusRequestProto::default_instance_ = new GetServiceStatusRequestProto();
  GetServiceStatusResponseProto::default_instance_ = new GetServiceStatusResponseProto();
  HAStateChangeRequestInfoProto::default_instance_->InitAsDefaultInstance();
  MonitorHealthRequestProto::default_instance_->InitAsDefaultInstance();
  MonitorHealthResponseProto::default_instance_->InitAsDefaultInstance();
  TransitionToActiveRequestProto::default_instance_->InitAsDefaultInstance();
  TransitionToActiveResponseProto::default_instance_->InitAsDefaultInstance();
  TransitionToStandbyRequestProto::default_instance_->InitAsDefaultInstance();
  TransitionToStandbyResponseProto::default_instance_->InitAsDefaultInstance();
  GetServiceStatusRequestProto::default_instance_->InitAsDefaultInstance();
  GetServiceStatusResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fHAServiceProtocol_2eproto {
  StaticDescriptorInitializer_common_2fHAServiceProtocol_2eproto() {
    protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fHAServiceProtocol_2eproto_;
const ::google::protobuf::EnumDescriptor* HAServiceStateProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return HAServiceStateProto_descriptor_;
}
bool HAServiceStateProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* HARequestSource_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return HARequestSource_descriptor_;
}
bool HARequestSource_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

#ifndef _MSC_VER
const int HAStateChangeRequestInfoProto::kReqSourceFieldNumber;
#endif  // !_MSC_VER

HAStateChangeRequestInfoProto::HAStateChangeRequestInfoProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void HAStateChangeRequestInfoProto::InitAsDefaultInstance() {
}

HAStateChangeRequestInfoProto::HAStateChangeRequestInfoProto(const HAStateChangeRequestInfoProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void HAStateChangeRequestInfoProto::SharedCtor() {
  _cached_size_ = 0;
  reqsource_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

HAStateChangeRequestInfoProto::~HAStateChangeRequestInfoProto() {
  SharedDtor();
}

void HAStateChangeRequestInfoProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void HAStateChangeRequestInfoProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HAStateChangeRequestInfoProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return HAStateChangeRequestInfoProto_descriptor_;
}

const HAStateChangeRequestInfoProto& HAStateChangeRequestInfoProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

HAStateChangeRequestInfoProto* HAStateChangeRequestInfoProto::default_instance_ = NULL;

HAStateChangeRequestInfoProto* HAStateChangeRequestInfoProto::New() const {
  return new HAStateChangeRequestInfoProto;
}

void HAStateChangeRequestInfoProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    reqsource_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool HAStateChangeRequestInfoProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.common.HARequestSource reqSource = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::HARequestSource_IsValid(value)) {
            set_reqsource(static_cast< ::hadoop::common::HARequestSource >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void HAStateChangeRequestInfoProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.common.HARequestSource reqSource = 1;
  if (has_reqsource()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->reqsource(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* HAStateChangeRequestInfoProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.common.HARequestSource reqSource = 1;
  if (has_reqsource()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->reqsource(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int HAStateChangeRequestInfoProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.common.HARequestSource reqSource = 1;
    if (has_reqsource()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->reqsource());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HAStateChangeRequestInfoProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const HAStateChangeRequestInfoProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const HAStateChangeRequestInfoProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void HAStateChangeRequestInfoProto::MergeFrom(const HAStateChangeRequestInfoProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqsource()) {
      set_reqsource(from.reqsource());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void HAStateChangeRequestInfoProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HAStateChangeRequestInfoProto::CopyFrom(const HAStateChangeRequestInfoProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HAStateChangeRequestInfoProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void HAStateChangeRequestInfoProto::Swap(HAStateChangeRequestInfoProto* other) {
  if (other != this) {
    std::swap(reqsource_, other->reqsource_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata HAStateChangeRequestInfoProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = HAStateChangeRequestInfoProto_descriptor_;
  metadata.reflection = HAStateChangeRequestInfoProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

MonitorHealthRequestProto::MonitorHealthRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void MonitorHealthRequestProto::InitAsDefaultInstance() {
}

MonitorHealthRequestProto::MonitorHealthRequestProto(const MonitorHealthRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void MonitorHealthRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

MonitorHealthRequestProto::~MonitorHealthRequestProto() {
  SharedDtor();
}

void MonitorHealthRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void MonitorHealthRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MonitorHealthRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MonitorHealthRequestProto_descriptor_;
}

const MonitorHealthRequestProto& MonitorHealthRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

MonitorHealthRequestProto* MonitorHealthRequestProto::default_instance_ = NULL;

MonitorHealthRequestProto* MonitorHealthRequestProto::New() const {
  return new MonitorHealthRequestProto;
}

void MonitorHealthRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool MonitorHealthRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void MonitorHealthRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* MonitorHealthRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int MonitorHealthRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MonitorHealthRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const MonitorHealthRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const MonitorHealthRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void MonitorHealthRequestProto::MergeFrom(const MonitorHealthRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void MonitorHealthRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MonitorHealthRequestProto::CopyFrom(const MonitorHealthRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MonitorHealthRequestProto::IsInitialized() const {

  return true;
}

void MonitorHealthRequestProto::Swap(MonitorHealthRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata MonitorHealthRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MonitorHealthRequestProto_descriptor_;
  metadata.reflection = MonitorHealthRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

MonitorHealthResponseProto::MonitorHealthResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void MonitorHealthResponseProto::InitAsDefaultInstance() {
}

MonitorHealthResponseProto::MonitorHealthResponseProto(const MonitorHealthResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void MonitorHealthResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

MonitorHealthResponseProto::~MonitorHealthResponseProto() {
  SharedDtor();
}

void MonitorHealthResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void MonitorHealthResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MonitorHealthResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MonitorHealthResponseProto_descriptor_;
}

const MonitorHealthResponseProto& MonitorHealthResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

MonitorHealthResponseProto* MonitorHealthResponseProto::default_instance_ = NULL;

MonitorHealthResponseProto* MonitorHealthResponseProto::New() const {
  return new MonitorHealthResponseProto;
}

void MonitorHealthResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool MonitorHealthResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void MonitorHealthResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* MonitorHealthResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int MonitorHealthResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MonitorHealthResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const MonitorHealthResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const MonitorHealthResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void MonitorHealthResponseProto::MergeFrom(const MonitorHealthResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void MonitorHealthResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MonitorHealthResponseProto::CopyFrom(const MonitorHealthResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MonitorHealthResponseProto::IsInitialized() const {

  return true;
}

void MonitorHealthResponseProto::Swap(MonitorHealthResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata MonitorHealthResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MonitorHealthResponseProto_descriptor_;
  metadata.reflection = MonitorHealthResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int TransitionToActiveRequestProto::kReqInfoFieldNumber;
#endif  // !_MSC_VER

TransitionToActiveRequestProto::TransitionToActiveRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void TransitionToActiveRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::common::HAStateChangeRequestInfoProto*>(&::hadoop::common::HAStateChangeRequestInfoProto::default_instance());
}

TransitionToActiveRequestProto::TransitionToActiveRequestProto(const TransitionToActiveRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void TransitionToActiveRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

TransitionToActiveRequestProto::~TransitionToActiveRequestProto() {
  SharedDtor();
}

void TransitionToActiveRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void TransitionToActiveRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TransitionToActiveRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TransitionToActiveRequestProto_descriptor_;
}

const TransitionToActiveRequestProto& TransitionToActiveRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

TransitionToActiveRequestProto* TransitionToActiveRequestProto::default_instance_ = NULL;

TransitionToActiveRequestProto* TransitionToActiveRequestProto::New() const {
  return new TransitionToActiveRequestProto;
}

void TransitionToActiveRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::common::HAStateChangeRequestInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool TransitionToActiveRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void TransitionToActiveRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* TransitionToActiveRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int TransitionToActiveRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TransitionToActiveRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const TransitionToActiveRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const TransitionToActiveRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void TransitionToActiveRequestProto::MergeFrom(const TransitionToActiveRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::common::HAStateChangeRequestInfoProto::MergeFrom(from.reqinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void TransitionToActiveRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TransitionToActiveRequestProto::CopyFrom(const TransitionToActiveRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TransitionToActiveRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void TransitionToActiveRequestProto::Swap(TransitionToActiveRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata TransitionToActiveRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TransitionToActiveRequestProto_descriptor_;
  metadata.reflection = TransitionToActiveRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

TransitionToActiveResponseProto::TransitionToActiveResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void TransitionToActiveResponseProto::InitAsDefaultInstance() {
}

TransitionToActiveResponseProto::TransitionToActiveResponseProto(const TransitionToActiveResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void TransitionToActiveResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

TransitionToActiveResponseProto::~TransitionToActiveResponseProto() {
  SharedDtor();
}

void TransitionToActiveResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void TransitionToActiveResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TransitionToActiveResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TransitionToActiveResponseProto_descriptor_;
}

const TransitionToActiveResponseProto& TransitionToActiveResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

TransitionToActiveResponseProto* TransitionToActiveResponseProto::default_instance_ = NULL;

TransitionToActiveResponseProto* TransitionToActiveResponseProto::New() const {
  return new TransitionToActiveResponseProto;
}

void TransitionToActiveResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool TransitionToActiveResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void TransitionToActiveResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* TransitionToActiveResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int TransitionToActiveResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TransitionToActiveResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const TransitionToActiveResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const TransitionToActiveResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void TransitionToActiveResponseProto::MergeFrom(const TransitionToActiveResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void TransitionToActiveResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TransitionToActiveResponseProto::CopyFrom(const TransitionToActiveResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TransitionToActiveResponseProto::IsInitialized() const {

  return true;
}

void TransitionToActiveResponseProto::Swap(TransitionToActiveResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata TransitionToActiveResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TransitionToActiveResponseProto_descriptor_;
  metadata.reflection = TransitionToActiveResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int TransitionToStandbyRequestProto::kReqInfoFieldNumber;
#endif  // !_MSC_VER

TransitionToStandbyRequestProto::TransitionToStandbyRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void TransitionToStandbyRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::common::HAStateChangeRequestInfoProto*>(&::hadoop::common::HAStateChangeRequestInfoProto::default_instance());
}

TransitionToStandbyRequestProto::TransitionToStandbyRequestProto(const TransitionToStandbyRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void TransitionToStandbyRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

TransitionToStandbyRequestProto::~TransitionToStandbyRequestProto() {
  SharedDtor();
}

void TransitionToStandbyRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void TransitionToStandbyRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TransitionToStandbyRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TransitionToStandbyRequestProto_descriptor_;
}

const TransitionToStandbyRequestProto& TransitionToStandbyRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

TransitionToStandbyRequestProto* TransitionToStandbyRequestProto::default_instance_ = NULL;

TransitionToStandbyRequestProto* TransitionToStandbyRequestProto::New() const {
  return new TransitionToStandbyRequestProto;
}

void TransitionToStandbyRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::common::HAStateChangeRequestInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool TransitionToStandbyRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void TransitionToStandbyRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* TransitionToStandbyRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int TransitionToStandbyRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TransitionToStandbyRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const TransitionToStandbyRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const TransitionToStandbyRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void TransitionToStandbyRequestProto::MergeFrom(const TransitionToStandbyRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::common::HAStateChangeRequestInfoProto::MergeFrom(from.reqinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void TransitionToStandbyRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TransitionToStandbyRequestProto::CopyFrom(const TransitionToStandbyRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TransitionToStandbyRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void TransitionToStandbyRequestProto::Swap(TransitionToStandbyRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata TransitionToStandbyRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TransitionToStandbyRequestProto_descriptor_;
  metadata.reflection = TransitionToStandbyRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

TransitionToStandbyResponseProto::TransitionToStandbyResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void TransitionToStandbyResponseProto::InitAsDefaultInstance() {
}

TransitionToStandbyResponseProto::TransitionToStandbyResponseProto(const TransitionToStandbyResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void TransitionToStandbyResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

TransitionToStandbyResponseProto::~TransitionToStandbyResponseProto() {
  SharedDtor();
}

void TransitionToStandbyResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void TransitionToStandbyResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TransitionToStandbyResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TransitionToStandbyResponseProto_descriptor_;
}

const TransitionToStandbyResponseProto& TransitionToStandbyResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

TransitionToStandbyResponseProto* TransitionToStandbyResponseProto::default_instance_ = NULL;

TransitionToStandbyResponseProto* TransitionToStandbyResponseProto::New() const {
  return new TransitionToStandbyResponseProto;
}

void TransitionToStandbyResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool TransitionToStandbyResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void TransitionToStandbyResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* TransitionToStandbyResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int TransitionToStandbyResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TransitionToStandbyResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const TransitionToStandbyResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const TransitionToStandbyResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void TransitionToStandbyResponseProto::MergeFrom(const TransitionToStandbyResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void TransitionToStandbyResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TransitionToStandbyResponseProto::CopyFrom(const TransitionToStandbyResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TransitionToStandbyResponseProto::IsInitialized() const {

  return true;
}

void TransitionToStandbyResponseProto::Swap(TransitionToStandbyResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata TransitionToStandbyResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TransitionToStandbyResponseProto_descriptor_;
  metadata.reflection = TransitionToStandbyResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

GetServiceStatusRequestProto::GetServiceStatusRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetServiceStatusRequestProto::InitAsDefaultInstance() {
}

GetServiceStatusRequestProto::GetServiceStatusRequestProto(const GetServiceStatusRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetServiceStatusRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetServiceStatusRequestProto::~GetServiceStatusRequestProto() {
  SharedDtor();
}

void GetServiceStatusRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetServiceStatusRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetServiceStatusRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetServiceStatusRequestProto_descriptor_;
}

const GetServiceStatusRequestProto& GetServiceStatusRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

GetServiceStatusRequestProto* GetServiceStatusRequestProto::default_instance_ = NULL;

GetServiceStatusRequestProto* GetServiceStatusRequestProto::New() const {
  return new GetServiceStatusRequestProto;
}

void GetServiceStatusRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetServiceStatusRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void GetServiceStatusRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetServiceStatusRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetServiceStatusRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetServiceStatusRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetServiceStatusRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetServiceStatusRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetServiceStatusRequestProto::MergeFrom(const GetServiceStatusRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetServiceStatusRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetServiceStatusRequestProto::CopyFrom(const GetServiceStatusRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetServiceStatusRequestProto::IsInitialized() const {

  return true;
}

void GetServiceStatusRequestProto::Swap(GetServiceStatusRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetServiceStatusRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetServiceStatusRequestProto_descriptor_;
  metadata.reflection = GetServiceStatusRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetServiceStatusResponseProto::kStateFieldNumber;
const int GetServiceStatusResponseProto::kReadyToBecomeActiveFieldNumber;
const int GetServiceStatusResponseProto::kNotReadyReasonFieldNumber;
#endif  // !_MSC_VER

GetServiceStatusResponseProto::GetServiceStatusResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetServiceStatusResponseProto::InitAsDefaultInstance() {
}

GetServiceStatusResponseProto::GetServiceStatusResponseProto(const GetServiceStatusResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetServiceStatusResponseProto::SharedCtor() {
  _cached_size_ = 0;
  state_ = 0;
  readytobecomeactive_ = false;
  notreadyreason_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetServiceStatusResponseProto::~GetServiceStatusResponseProto() {
  SharedDtor();
}

void GetServiceStatusResponseProto::SharedDtor() {
  if (notreadyreason_ != &::google::protobuf::internal::kEmptyString) {
    delete notreadyreason_;
  }
  if (this != default_instance_) {
  }
}

void GetServiceStatusResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetServiceStatusResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetServiceStatusResponseProto_descriptor_;
}

const GetServiceStatusResponseProto& GetServiceStatusResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  return *default_instance_;
}

GetServiceStatusResponseProto* GetServiceStatusResponseProto::default_instance_ = NULL;

GetServiceStatusResponseProto* GetServiceStatusResponseProto::New() const {
  return new GetServiceStatusResponseProto;
}

void GetServiceStatusResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    state_ = 0;
    readytobecomeactive_ = false;
    if (has_notreadyreason()) {
      if (notreadyreason_ != &::google::protobuf::internal::kEmptyString) {
        notreadyreason_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetServiceStatusResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.common.HAServiceStateProto state = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::HAServiceStateProto_IsValid(value)) {
            set_state(static_cast< ::hadoop::common::HAServiceStateProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_readyToBecomeActive;
        break;
      }

      // optional bool readyToBecomeActive = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_readyToBecomeActive:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &readytobecomeactive_)));
          set_has_readytobecomeactive();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_notReadyReason;
        break;
      }

      // optional string notReadyReason = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_notReadyReason:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_notreadyreason()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->notreadyreason().data(), this->notreadyreason().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetServiceStatusResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.common.HAServiceStateProto state = 1;
  if (has_state()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->state(), output);
  }

  // optional bool readyToBecomeActive = 2;
  if (has_readytobecomeactive()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->readytobecomeactive(), output);
  }

  // optional string notReadyReason = 3;
  if (has_notreadyreason()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->notreadyreason().data(), this->notreadyreason().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->notreadyreason(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetServiceStatusResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.common.HAServiceStateProto state = 1;
  if (has_state()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->state(), target);
  }

  // optional bool readyToBecomeActive = 2;
  if (has_readytobecomeactive()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->readytobecomeactive(), target);
  }

  // optional string notReadyReason = 3;
  if (has_notreadyreason()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->notreadyreason().data(), this->notreadyreason().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->notreadyreason(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetServiceStatusResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.common.HAServiceStateProto state = 1;
    if (has_state()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->state());
    }

    // optional bool readyToBecomeActive = 2;
    if (has_readytobecomeactive()) {
      total_size += 1 + 1;
    }

    // optional string notReadyReason = 3;
    if (has_notreadyreason()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->notreadyreason());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetServiceStatusResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetServiceStatusResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetServiceStatusResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetServiceStatusResponseProto::MergeFrom(const GetServiceStatusResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_state()) {
      set_state(from.state());
    }
    if (from.has_readytobecomeactive()) {
      set_readytobecomeactive(from.readytobecomeactive());
    }
    if (from.has_notreadyreason()) {
      set_notreadyreason(from.notreadyreason());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetServiceStatusResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetServiceStatusResponseProto::CopyFrom(const GetServiceStatusResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetServiceStatusResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetServiceStatusResponseProto::Swap(GetServiceStatusResponseProto* other) {
  if (other != this) {
    std::swap(state_, other->state_);
    std::swap(readytobecomeactive_, other->readytobecomeactive_);
    std::swap(notreadyreason_, other->notreadyreason_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetServiceStatusResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetServiceStatusResponseProto_descriptor_;
  metadata.reflection = GetServiceStatusResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
