// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/ProtocolInfo.proto

#ifndef PROTOBUF_common_2fProtocolInfo_2eproto__INCLUDED
#define PROTOBUF_common_2fProtocolInfo_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

class GetProtocolVersionsRequestProto;
class ProtocolVersionProto;
class GetProtocolVersionsResponseProto;
class GetProtocolSignatureRequestProto;
class GetProtocolSignatureResponseProto;
class ProtocolSignatureProto;

// ===================================================================

class GetProtocolVersionsRequestProto : public ::google::protobuf::Message {
 public:
  GetProtocolVersionsRequestProto();
  virtual ~GetProtocolVersionsRequestProto();

  GetProtocolVersionsRequestProto(const GetProtocolVersionsRequestProto& from);

  inline GetProtocolVersionsRequestProto& operator=(const GetProtocolVersionsRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetProtocolVersionsRequestProto& default_instance();

  void Swap(GetProtocolVersionsRequestProto* other);

  // implements Message ----------------------------------------------

  GetProtocolVersionsRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetProtocolVersionsRequestProto& from);
  void MergeFrom(const GetProtocolVersionsRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string protocol = 1;
  inline bool has_protocol() const;
  inline void clear_protocol();
  static const int kProtocolFieldNumber = 1;
  inline const ::std::string& protocol() const;
  inline void set_protocol(const ::std::string& value);
  inline void set_protocol(const char* value);
  inline void set_protocol(const char* value, size_t size);
  inline ::std::string* mutable_protocol();
  inline ::std::string* release_protocol();
  inline void set_allocated_protocol(::std::string* protocol);

  // @@protoc_insertion_point(class_scope:hadoop.common.GetProtocolVersionsRequestProto)
 private:
  inline void set_has_protocol();
  inline void clear_has_protocol();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* protocol_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

  void InitAsDefaultInstance();
  static GetProtocolVersionsRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class ProtocolVersionProto : public ::google::protobuf::Message {
 public:
  ProtocolVersionProto();
  virtual ~ProtocolVersionProto();

  ProtocolVersionProto(const ProtocolVersionProto& from);

  inline ProtocolVersionProto& operator=(const ProtocolVersionProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtocolVersionProto& default_instance();

  void Swap(ProtocolVersionProto* other);

  // implements Message ----------------------------------------------

  ProtocolVersionProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtocolVersionProto& from);
  void MergeFrom(const ProtocolVersionProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string rpcKind = 1;
  inline bool has_rpckind() const;
  inline void clear_rpckind();
  static const int kRpcKindFieldNumber = 1;
  inline const ::std::string& rpckind() const;
  inline void set_rpckind(const ::std::string& value);
  inline void set_rpckind(const char* value);
  inline void set_rpckind(const char* value, size_t size);
  inline ::std::string* mutable_rpckind();
  inline ::std::string* release_rpckind();
  inline void set_allocated_rpckind(::std::string* rpckind);

  // repeated uint64 versions = 2;
  inline int versions_size() const;
  inline void clear_versions();
  static const int kVersionsFieldNumber = 2;
  inline ::google::protobuf::uint64 versions(int index) const;
  inline void set_versions(int index, ::google::protobuf::uint64 value);
  inline void add_versions(::google::protobuf::uint64 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      versions() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_versions();

  // @@protoc_insertion_point(class_scope:hadoop.common.ProtocolVersionProto)
 private:
  inline void set_has_rpckind();
  inline void clear_has_rpckind();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* rpckind_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > versions_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

  void InitAsDefaultInstance();
  static ProtocolVersionProto* default_instance_;
};
// -------------------------------------------------------------------

class GetProtocolVersionsResponseProto : public ::google::protobuf::Message {
 public:
  GetProtocolVersionsResponseProto();
  virtual ~GetProtocolVersionsResponseProto();

  GetProtocolVersionsResponseProto(const GetProtocolVersionsResponseProto& from);

  inline GetProtocolVersionsResponseProto& operator=(const GetProtocolVersionsResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetProtocolVersionsResponseProto& default_instance();

  void Swap(GetProtocolVersionsResponseProto* other);

  // implements Message ----------------------------------------------

  GetProtocolVersionsResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetProtocolVersionsResponseProto& from);
  void MergeFrom(const GetProtocolVersionsResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.common.ProtocolVersionProto protocolVersions = 1;
  inline int protocolversions_size() const;
  inline void clear_protocolversions();
  static const int kProtocolVersionsFieldNumber = 1;
  inline const ::hadoop::common::ProtocolVersionProto& protocolversions(int index) const;
  inline ::hadoop::common::ProtocolVersionProto* mutable_protocolversions(int index);
  inline ::hadoop::common::ProtocolVersionProto* add_protocolversions();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolVersionProto >&
      protocolversions() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolVersionProto >*
      mutable_protocolversions();

  // @@protoc_insertion_point(class_scope:hadoop.common.GetProtocolVersionsResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolVersionProto > protocolversions_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

  void InitAsDefaultInstance();
  static GetProtocolVersionsResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetProtocolSignatureRequestProto : public ::google::protobuf::Message {
 public:
  GetProtocolSignatureRequestProto();
  virtual ~GetProtocolSignatureRequestProto();

  GetProtocolSignatureRequestProto(const GetProtocolSignatureRequestProto& from);

  inline GetProtocolSignatureRequestProto& operator=(const GetProtocolSignatureRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetProtocolSignatureRequestProto& default_instance();

  void Swap(GetProtocolSignatureRequestProto* other);

  // implements Message ----------------------------------------------

  GetProtocolSignatureRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetProtocolSignatureRequestProto& from);
  void MergeFrom(const GetProtocolSignatureRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string protocol = 1;
  inline bool has_protocol() const;
  inline void clear_protocol();
  static const int kProtocolFieldNumber = 1;
  inline const ::std::string& protocol() const;
  inline void set_protocol(const ::std::string& value);
  inline void set_protocol(const char* value);
  inline void set_protocol(const char* value, size_t size);
  inline ::std::string* mutable_protocol();
  inline ::std::string* release_protocol();
  inline void set_allocated_protocol(::std::string* protocol);

  // required string rpcKind = 2;
  inline bool has_rpckind() const;
  inline void clear_rpckind();
  static const int kRpcKindFieldNumber = 2;
  inline const ::std::string& rpckind() const;
  inline void set_rpckind(const ::std::string& value);
  inline void set_rpckind(const char* value);
  inline void set_rpckind(const char* value, size_t size);
  inline ::std::string* mutable_rpckind();
  inline ::std::string* release_rpckind();
  inline void set_allocated_rpckind(::std::string* rpckind);

  // @@protoc_insertion_point(class_scope:hadoop.common.GetProtocolSignatureRequestProto)
 private:
  inline void set_has_protocol();
  inline void clear_has_protocol();
  inline void set_has_rpckind();
  inline void clear_has_rpckind();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* protocol_;
  ::std::string* rpckind_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

  void InitAsDefaultInstance();
  static GetProtocolSignatureRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetProtocolSignatureResponseProto : public ::google::protobuf::Message {
 public:
  GetProtocolSignatureResponseProto();
  virtual ~GetProtocolSignatureResponseProto();

  GetProtocolSignatureResponseProto(const GetProtocolSignatureResponseProto& from);

  inline GetProtocolSignatureResponseProto& operator=(const GetProtocolSignatureResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetProtocolSignatureResponseProto& default_instance();

  void Swap(GetProtocolSignatureResponseProto* other);

  // implements Message ----------------------------------------------

  GetProtocolSignatureResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetProtocolSignatureResponseProto& from);
  void MergeFrom(const GetProtocolSignatureResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.common.ProtocolSignatureProto protocolSignature = 1;
  inline int protocolsignature_size() const;
  inline void clear_protocolsignature();
  static const int kProtocolSignatureFieldNumber = 1;
  inline const ::hadoop::common::ProtocolSignatureProto& protocolsignature(int index) const;
  inline ::hadoop::common::ProtocolSignatureProto* mutable_protocolsignature(int index);
  inline ::hadoop::common::ProtocolSignatureProto* add_protocolsignature();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolSignatureProto >&
      protocolsignature() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolSignatureProto >*
      mutable_protocolsignature();

  // @@protoc_insertion_point(class_scope:hadoop.common.GetProtocolSignatureResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolSignatureProto > protocolsignature_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

  void InitAsDefaultInstance();
  static GetProtocolSignatureResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ProtocolSignatureProto : public ::google::protobuf::Message {
 public:
  ProtocolSignatureProto();
  virtual ~ProtocolSignatureProto();

  ProtocolSignatureProto(const ProtocolSignatureProto& from);

  inline ProtocolSignatureProto& operator=(const ProtocolSignatureProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtocolSignatureProto& default_instance();

  void Swap(ProtocolSignatureProto* other);

  // implements Message ----------------------------------------------

  ProtocolSignatureProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtocolSignatureProto& from);
  void MergeFrom(const ProtocolSignatureProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 version = 1;
  inline bool has_version() const;
  inline void clear_version();
  static const int kVersionFieldNumber = 1;
  inline ::google::protobuf::uint64 version() const;
  inline void set_version(::google::protobuf::uint64 value);

  // repeated uint32 methods = 2;
  inline int methods_size() const;
  inline void clear_methods();
  static const int kMethodsFieldNumber = 2;
  inline ::google::protobuf::uint32 methods(int index) const;
  inline void set_methods(int index, ::google::protobuf::uint32 value);
  inline void add_methods(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      methods() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_methods();

  // @@protoc_insertion_point(class_scope:hadoop.common.ProtocolSignatureProto)
 private:
  inline void set_has_version();
  inline void clear_has_version();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 version_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > methods_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_AssignDesc_common_2fProtocolInfo_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto();

  void InitAsDefaultInstance();
  static ProtocolSignatureProto* default_instance_;
};
// ===================================================================


// ===================================================================

// GetProtocolVersionsRequestProto

// required string protocol = 1;
inline bool GetProtocolVersionsRequestProto::has_protocol() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetProtocolVersionsRequestProto::set_has_protocol() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetProtocolVersionsRequestProto::clear_has_protocol() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetProtocolVersionsRequestProto::clear_protocol() {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    protocol_->clear();
  }
  clear_has_protocol();
}
inline const ::std::string& GetProtocolVersionsRequestProto::protocol() const {
  return *protocol_;
}
inline void GetProtocolVersionsRequestProto::set_protocol(const ::std::string& value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void GetProtocolVersionsRequestProto::set_protocol(const char* value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void GetProtocolVersionsRequestProto::set_protocol(const char* value, size_t size) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetProtocolVersionsRequestProto::mutable_protocol() {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  return protocol_;
}
inline ::std::string* GetProtocolVersionsRequestProto::release_protocol() {
  clear_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = protocol_;
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetProtocolVersionsRequestProto::set_allocated_protocol(::std::string* protocol) {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (protocol) {
    set_has_protocol();
    protocol_ = protocol;
  } else {
    clear_has_protocol();
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ProtocolVersionProto

// required string rpcKind = 1;
inline bool ProtocolVersionProto::has_rpckind() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtocolVersionProto::set_has_rpckind() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtocolVersionProto::clear_has_rpckind() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtocolVersionProto::clear_rpckind() {
  if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
    rpckind_->clear();
  }
  clear_has_rpckind();
}
inline const ::std::string& ProtocolVersionProto::rpckind() const {
  return *rpckind_;
}
inline void ProtocolVersionProto::set_rpckind(const ::std::string& value) {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  rpckind_->assign(value);
}
inline void ProtocolVersionProto::set_rpckind(const char* value) {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  rpckind_->assign(value);
}
inline void ProtocolVersionProto::set_rpckind(const char* value, size_t size) {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  rpckind_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ProtocolVersionProto::mutable_rpckind() {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  return rpckind_;
}
inline ::std::string* ProtocolVersionProto::release_rpckind() {
  clear_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = rpckind_;
    rpckind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ProtocolVersionProto::set_allocated_rpckind(::std::string* rpckind) {
  if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
    delete rpckind_;
  }
  if (rpckind) {
    set_has_rpckind();
    rpckind_ = rpckind;
  } else {
    clear_has_rpckind();
    rpckind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated uint64 versions = 2;
inline int ProtocolVersionProto::versions_size() const {
  return versions_.size();
}
inline void ProtocolVersionProto::clear_versions() {
  versions_.Clear();
}
inline ::google::protobuf::uint64 ProtocolVersionProto::versions(int index) const {
  return versions_.Get(index);
}
inline void ProtocolVersionProto::set_versions(int index, ::google::protobuf::uint64 value) {
  versions_.Set(index, value);
}
inline void ProtocolVersionProto::add_versions(::google::protobuf::uint64 value) {
  versions_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
ProtocolVersionProto::versions() const {
  return versions_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
ProtocolVersionProto::mutable_versions() {
  return &versions_;
}

// -------------------------------------------------------------------

// GetProtocolVersionsResponseProto

// repeated .hadoop.common.ProtocolVersionProto protocolVersions = 1;
inline int GetProtocolVersionsResponseProto::protocolversions_size() const {
  return protocolversions_.size();
}
inline void GetProtocolVersionsResponseProto::clear_protocolversions() {
  protocolversions_.Clear();
}
inline const ::hadoop::common::ProtocolVersionProto& GetProtocolVersionsResponseProto::protocolversions(int index) const {
  return protocolversions_.Get(index);
}
inline ::hadoop::common::ProtocolVersionProto* GetProtocolVersionsResponseProto::mutable_protocolversions(int index) {
  return protocolversions_.Mutable(index);
}
inline ::hadoop::common::ProtocolVersionProto* GetProtocolVersionsResponseProto::add_protocolversions() {
  return protocolversions_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolVersionProto >&
GetProtocolVersionsResponseProto::protocolversions() const {
  return protocolversions_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolVersionProto >*
GetProtocolVersionsResponseProto::mutable_protocolversions() {
  return &protocolversions_;
}

// -------------------------------------------------------------------

// GetProtocolSignatureRequestProto

// required string protocol = 1;
inline bool GetProtocolSignatureRequestProto::has_protocol() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetProtocolSignatureRequestProto::set_has_protocol() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetProtocolSignatureRequestProto::clear_has_protocol() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetProtocolSignatureRequestProto::clear_protocol() {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    protocol_->clear();
  }
  clear_has_protocol();
}
inline const ::std::string& GetProtocolSignatureRequestProto::protocol() const {
  return *protocol_;
}
inline void GetProtocolSignatureRequestProto::set_protocol(const ::std::string& value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void GetProtocolSignatureRequestProto::set_protocol(const char* value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void GetProtocolSignatureRequestProto::set_protocol(const char* value, size_t size) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetProtocolSignatureRequestProto::mutable_protocol() {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  return protocol_;
}
inline ::std::string* GetProtocolSignatureRequestProto::release_protocol() {
  clear_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = protocol_;
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetProtocolSignatureRequestProto::set_allocated_protocol(::std::string* protocol) {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (protocol) {
    set_has_protocol();
    protocol_ = protocol;
  } else {
    clear_has_protocol();
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string rpcKind = 2;
inline bool GetProtocolSignatureRequestProto::has_rpckind() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GetProtocolSignatureRequestProto::set_has_rpckind() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GetProtocolSignatureRequestProto::clear_has_rpckind() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GetProtocolSignatureRequestProto::clear_rpckind() {
  if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
    rpckind_->clear();
  }
  clear_has_rpckind();
}
inline const ::std::string& GetProtocolSignatureRequestProto::rpckind() const {
  return *rpckind_;
}
inline void GetProtocolSignatureRequestProto::set_rpckind(const ::std::string& value) {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  rpckind_->assign(value);
}
inline void GetProtocolSignatureRequestProto::set_rpckind(const char* value) {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  rpckind_->assign(value);
}
inline void GetProtocolSignatureRequestProto::set_rpckind(const char* value, size_t size) {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  rpckind_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetProtocolSignatureRequestProto::mutable_rpckind() {
  set_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    rpckind_ = new ::std::string;
  }
  return rpckind_;
}
inline ::std::string* GetProtocolSignatureRequestProto::release_rpckind() {
  clear_has_rpckind();
  if (rpckind_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = rpckind_;
    rpckind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetProtocolSignatureRequestProto::set_allocated_rpckind(::std::string* rpckind) {
  if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
    delete rpckind_;
  }
  if (rpckind) {
    set_has_rpckind();
    rpckind_ = rpckind;
  } else {
    clear_has_rpckind();
    rpckind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// GetProtocolSignatureResponseProto

// repeated .hadoop.common.ProtocolSignatureProto protocolSignature = 1;
inline int GetProtocolSignatureResponseProto::protocolsignature_size() const {
  return protocolsignature_.size();
}
inline void GetProtocolSignatureResponseProto::clear_protocolsignature() {
  protocolsignature_.Clear();
}
inline const ::hadoop::common::ProtocolSignatureProto& GetProtocolSignatureResponseProto::protocolsignature(int index) const {
  return protocolsignature_.Get(index);
}
inline ::hadoop::common::ProtocolSignatureProto* GetProtocolSignatureResponseProto::mutable_protocolsignature(int index) {
  return protocolsignature_.Mutable(index);
}
inline ::hadoop::common::ProtocolSignatureProto* GetProtocolSignatureResponseProto::add_protocolsignature() {
  return protocolsignature_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolSignatureProto >&
GetProtocolSignatureResponseProto::protocolsignature() const {
  return protocolsignature_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::ProtocolSignatureProto >*
GetProtocolSignatureResponseProto::mutable_protocolsignature() {
  return &protocolsignature_;
}

// -------------------------------------------------------------------

// ProtocolSignatureProto

// required uint64 version = 1;
inline bool ProtocolSignatureProto::has_version() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtocolSignatureProto::set_has_version() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtocolSignatureProto::clear_has_version() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtocolSignatureProto::clear_version() {
  version_ = GOOGLE_ULONGLONG(0);
  clear_has_version();
}
inline ::google::protobuf::uint64 ProtocolSignatureProto::version() const {
  return version_;
}
inline void ProtocolSignatureProto::set_version(::google::protobuf::uint64 value) {
  set_has_version();
  version_ = value;
}

// repeated uint32 methods = 2;
inline int ProtocolSignatureProto::methods_size() const {
  return methods_.size();
}
inline void ProtocolSignatureProto::clear_methods() {
  methods_.Clear();
}
inline ::google::protobuf::uint32 ProtocolSignatureProto::methods(int index) const {
  return methods_.Get(index);
}
inline void ProtocolSignatureProto::set_methods(int index, ::google::protobuf::uint32 value) {
  methods_.Set(index, value);
}
inline void ProtocolSignatureProto::add_methods(::google::protobuf::uint32 value) {
  methods_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtocolSignatureProto::methods() const {
  return methods_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtocolSignatureProto::mutable_methods() {
  return &methods_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fProtocolInfo_2eproto__INCLUDED
