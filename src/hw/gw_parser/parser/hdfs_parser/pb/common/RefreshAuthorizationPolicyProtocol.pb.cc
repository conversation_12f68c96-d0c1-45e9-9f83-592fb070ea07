// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/RefreshAuthorizationPolicyProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/RefreshAuthorizationPolicyProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* RefreshServiceAclRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshServiceAclRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RefreshServiceAclResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshServiceAclResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto() {
  protobuf_AddDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/RefreshAuthorizationPolicyProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  RefreshServiceAclRequestProto_descriptor_ = file->message_type(0);
  static const int RefreshServiceAclRequestProto_offsets_[1] = {
  };
  RefreshServiceAclRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshServiceAclRequestProto_descriptor_,
      RefreshServiceAclRequestProto::default_instance_,
      RefreshServiceAclRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshServiceAclRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshServiceAclRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshServiceAclRequestProto));
  RefreshServiceAclResponseProto_descriptor_ = file->message_type(1);
  static const int RefreshServiceAclResponseProto_offsets_[1] = {
  };
  RefreshServiceAclResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshServiceAclResponseProto_descriptor_,
      RefreshServiceAclResponseProto::default_instance_,
      RefreshServiceAclResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshServiceAclResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshServiceAclResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshServiceAclResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshServiceAclRequestProto_descriptor_, &RefreshServiceAclRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshServiceAclResponseProto_descriptor_, &RefreshServiceAclResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fRefreshAuthorizationPolicyProtocol_2eproto() {
  delete RefreshServiceAclRequestProto::default_instance_;
  delete RefreshServiceAclRequestProto_reflection_;
  delete RefreshServiceAclResponseProto::default_instance_;
  delete RefreshServiceAclResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n/common/RefreshAuthorizationPolicyProto"
    "col.proto\022\rhadoop.common\"\037\n\035RefreshServi"
    "ceAclRequestProto\" \n\036RefreshServiceAclRe"
    "sponseProto2\235\001\n)RefreshAuthorizationPoli"
    "cyProtocolService\022p\n\021refreshServiceAcl\022,"
    ".hadoop.common.RefreshServiceAclRequestP"
    "roto\032-.hadoop.common.RefreshServiceAclRe"
    "sponseProtoBR\n org.apache.hadoop.securit"
    "y.protoB(RefreshAuthorizationPolicyProto"
    "colProtos\210\001\001\240\001\001", 375);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/RefreshAuthorizationPolicyProtocol.proto", &protobuf_RegisterTypes);
  RefreshServiceAclRequestProto::default_instance_ = new RefreshServiceAclRequestProto();
  RefreshServiceAclResponseProto::default_instance_ = new RefreshServiceAclResponseProto();
  RefreshServiceAclRequestProto::default_instance_->InitAsDefaultInstance();
  RefreshServiceAclResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fRefreshAuthorizationPolicyProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fRefreshAuthorizationPolicyProtocol_2eproto {
  StaticDescriptorInitializer_common_2fRefreshAuthorizationPolicyProtocol_2eproto() {
    protobuf_AddDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fRefreshAuthorizationPolicyProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshServiceAclRequestProto::RefreshServiceAclRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshServiceAclRequestProto::InitAsDefaultInstance() {
}

RefreshServiceAclRequestProto::RefreshServiceAclRequestProto(const RefreshServiceAclRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshServiceAclRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshServiceAclRequestProto::~RefreshServiceAclRequestProto() {
  SharedDtor();
}

void RefreshServiceAclRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshServiceAclRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshServiceAclRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshServiceAclRequestProto_descriptor_;
}

const RefreshServiceAclRequestProto& RefreshServiceAclRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto();
  return *default_instance_;
}

RefreshServiceAclRequestProto* RefreshServiceAclRequestProto::default_instance_ = NULL;

RefreshServiceAclRequestProto* RefreshServiceAclRequestProto::New() const {
  return new RefreshServiceAclRequestProto;
}

void RefreshServiceAclRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshServiceAclRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshServiceAclRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshServiceAclRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshServiceAclRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshServiceAclRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshServiceAclRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshServiceAclRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshServiceAclRequestProto::MergeFrom(const RefreshServiceAclRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshServiceAclRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshServiceAclRequestProto::CopyFrom(const RefreshServiceAclRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshServiceAclRequestProto::IsInitialized() const {

  return true;
}

void RefreshServiceAclRequestProto::Swap(RefreshServiceAclRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshServiceAclRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshServiceAclRequestProto_descriptor_;
  metadata.reflection = RefreshServiceAclRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshServiceAclResponseProto::RefreshServiceAclResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshServiceAclResponseProto::InitAsDefaultInstance() {
}

RefreshServiceAclResponseProto::RefreshServiceAclResponseProto(const RefreshServiceAclResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshServiceAclResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshServiceAclResponseProto::~RefreshServiceAclResponseProto() {
  SharedDtor();
}

void RefreshServiceAclResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshServiceAclResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshServiceAclResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshServiceAclResponseProto_descriptor_;
}

const RefreshServiceAclResponseProto& RefreshServiceAclResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshAuthorizationPolicyProtocol_2eproto();
  return *default_instance_;
}

RefreshServiceAclResponseProto* RefreshServiceAclResponseProto::default_instance_ = NULL;

RefreshServiceAclResponseProto* RefreshServiceAclResponseProto::New() const {
  return new RefreshServiceAclResponseProto;
}

void RefreshServiceAclResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshServiceAclResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshServiceAclResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshServiceAclResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshServiceAclResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshServiceAclResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshServiceAclResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshServiceAclResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshServiceAclResponseProto::MergeFrom(const RefreshServiceAclResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshServiceAclResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshServiceAclResponseProto::CopyFrom(const RefreshServiceAclResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshServiceAclResponseProto::IsInitialized() const {

  return true;
}

void RefreshServiceAclResponseProto::Swap(RefreshServiceAclResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshServiceAclResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshServiceAclResponseProto_descriptor_;
  metadata.reflection = RefreshServiceAclResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
