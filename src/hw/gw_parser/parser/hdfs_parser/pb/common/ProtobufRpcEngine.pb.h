// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/ProtobufRpcEngine.proto

#ifndef PROTOBUF_common_2fProtobufRpcEngine_2eproto__INCLUDED
#define PROTOBUF_common_2fProtobufRpcEngine_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fProtobufRpcEngine_2eproto();
void protobuf_AssignDesc_common_2fProtobufRpcEngine_2eproto();
void protobuf_ShutdownFile_common_2fProtobufRpcEngine_2eproto();

class RequestHeaderProto;

// ===================================================================

class RequestHeaderProto : public ::google::protobuf::Message {
 public:
  RequestHeaderProto();
  virtual ~RequestHeaderProto();

  RequestHeaderProto(const RequestHeaderProto& from);

  inline RequestHeaderProto& operator=(const RequestHeaderProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RequestHeaderProto& default_instance();

  void Swap(RequestHeaderProto* other);

  // implements Message ----------------------------------------------

  RequestHeaderProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RequestHeaderProto& from);
  void MergeFrom(const RequestHeaderProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string methodName = 1;
  inline bool has_methodname() const;
  inline void clear_methodname();
  static const int kMethodNameFieldNumber = 1;
  inline const ::std::string& methodname() const;
  inline void set_methodname(const ::std::string& value);
  inline void set_methodname(const char* value);
  inline void set_methodname(const char* value, size_t size);
  inline ::std::string* mutable_methodname();
  inline ::std::string* release_methodname();
  inline void set_allocated_methodname(::std::string* methodname);

  // required string declaringClassProtocolName = 2;
  inline bool has_declaringclassprotocolname() const;
  inline void clear_declaringclassprotocolname();
  static const int kDeclaringClassProtocolNameFieldNumber = 2;
  inline const ::std::string& declaringclassprotocolname() const;
  inline void set_declaringclassprotocolname(const ::std::string& value);
  inline void set_declaringclassprotocolname(const char* value);
  inline void set_declaringclassprotocolname(const char* value, size_t size);
  inline ::std::string* mutable_declaringclassprotocolname();
  inline ::std::string* release_declaringclassprotocolname();
  inline void set_allocated_declaringclassprotocolname(::std::string* declaringclassprotocolname);

  // required uint64 clientProtocolVersion = 3;
  inline bool has_clientprotocolversion() const;
  inline void clear_clientprotocolversion();
  static const int kClientProtocolVersionFieldNumber = 3;
  inline ::google::protobuf::uint64 clientprotocolversion() const;
  inline void set_clientprotocolversion(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.common.RequestHeaderProto)
 private:
  inline void set_has_methodname();
  inline void clear_has_methodname();
  inline void set_has_declaringclassprotocolname();
  inline void clear_has_declaringclassprotocolname();
  inline void set_has_clientprotocolversion();
  inline void clear_has_clientprotocolversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* methodname_;
  ::std::string* declaringclassprotocolname_;
  ::google::protobuf::uint64 clientprotocolversion_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fProtobufRpcEngine_2eproto();
  friend void protobuf_AssignDesc_common_2fProtobufRpcEngine_2eproto();
  friend void protobuf_ShutdownFile_common_2fProtobufRpcEngine_2eproto();

  void InitAsDefaultInstance();
  static RequestHeaderProto* default_instance_;
};
// ===================================================================


// ===================================================================

// RequestHeaderProto

// required string methodName = 1;
inline bool RequestHeaderProto::has_methodname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RequestHeaderProto::set_has_methodname() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RequestHeaderProto::clear_has_methodname() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RequestHeaderProto::clear_methodname() {
  if (methodname_ != &::google::protobuf::internal::kEmptyString) {
    methodname_->clear();
  }
  clear_has_methodname();
}
inline const ::std::string& RequestHeaderProto::methodname() const {
  return *methodname_;
}
inline void RequestHeaderProto::set_methodname(const ::std::string& value) {
  set_has_methodname();
  if (methodname_ == &::google::protobuf::internal::kEmptyString) {
    methodname_ = new ::std::string;
  }
  methodname_->assign(value);
}
inline void RequestHeaderProto::set_methodname(const char* value) {
  set_has_methodname();
  if (methodname_ == &::google::protobuf::internal::kEmptyString) {
    methodname_ = new ::std::string;
  }
  methodname_->assign(value);
}
inline void RequestHeaderProto::set_methodname(const char* value, size_t size) {
  set_has_methodname();
  if (methodname_ == &::google::protobuf::internal::kEmptyString) {
    methodname_ = new ::std::string;
  }
  methodname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RequestHeaderProto::mutable_methodname() {
  set_has_methodname();
  if (methodname_ == &::google::protobuf::internal::kEmptyString) {
    methodname_ = new ::std::string;
  }
  return methodname_;
}
inline ::std::string* RequestHeaderProto::release_methodname() {
  clear_has_methodname();
  if (methodname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = methodname_;
    methodname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RequestHeaderProto::set_allocated_methodname(::std::string* methodname) {
  if (methodname_ != &::google::protobuf::internal::kEmptyString) {
    delete methodname_;
  }
  if (methodname) {
    set_has_methodname();
    methodname_ = methodname;
  } else {
    clear_has_methodname();
    methodname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string declaringClassProtocolName = 2;
inline bool RequestHeaderProto::has_declaringclassprotocolname() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RequestHeaderProto::set_has_declaringclassprotocolname() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RequestHeaderProto::clear_has_declaringclassprotocolname() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RequestHeaderProto::clear_declaringclassprotocolname() {
  if (declaringclassprotocolname_ != &::google::protobuf::internal::kEmptyString) {
    declaringclassprotocolname_->clear();
  }
  clear_has_declaringclassprotocolname();
}
inline const ::std::string& RequestHeaderProto::declaringclassprotocolname() const {
  return *declaringclassprotocolname_;
}
inline void RequestHeaderProto::set_declaringclassprotocolname(const ::std::string& value) {
  set_has_declaringclassprotocolname();
  if (declaringclassprotocolname_ == &::google::protobuf::internal::kEmptyString) {
    declaringclassprotocolname_ = new ::std::string;
  }
  declaringclassprotocolname_->assign(value);
}
inline void RequestHeaderProto::set_declaringclassprotocolname(const char* value) {
  set_has_declaringclassprotocolname();
  if (declaringclassprotocolname_ == &::google::protobuf::internal::kEmptyString) {
    declaringclassprotocolname_ = new ::std::string;
  }
  declaringclassprotocolname_->assign(value);
}
inline void RequestHeaderProto::set_declaringclassprotocolname(const char* value, size_t size) {
  set_has_declaringclassprotocolname();
  if (declaringclassprotocolname_ == &::google::protobuf::internal::kEmptyString) {
    declaringclassprotocolname_ = new ::std::string;
  }
  declaringclassprotocolname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RequestHeaderProto::mutable_declaringclassprotocolname() {
  set_has_declaringclassprotocolname();
  if (declaringclassprotocolname_ == &::google::protobuf::internal::kEmptyString) {
    declaringclassprotocolname_ = new ::std::string;
  }
  return declaringclassprotocolname_;
}
inline ::std::string* RequestHeaderProto::release_declaringclassprotocolname() {
  clear_has_declaringclassprotocolname();
  if (declaringclassprotocolname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = declaringclassprotocolname_;
    declaringclassprotocolname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RequestHeaderProto::set_allocated_declaringclassprotocolname(::std::string* declaringclassprotocolname) {
  if (declaringclassprotocolname_ != &::google::protobuf::internal::kEmptyString) {
    delete declaringclassprotocolname_;
  }
  if (declaringclassprotocolname) {
    set_has_declaringclassprotocolname();
    declaringclassprotocolname_ = declaringclassprotocolname;
  } else {
    clear_has_declaringclassprotocolname();
    declaringclassprotocolname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 clientProtocolVersion = 3;
inline bool RequestHeaderProto::has_clientprotocolversion() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RequestHeaderProto::set_has_clientprotocolversion() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RequestHeaderProto::clear_has_clientprotocolversion() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RequestHeaderProto::clear_clientprotocolversion() {
  clientprotocolversion_ = GOOGLE_ULONGLONG(0);
  clear_has_clientprotocolversion();
}
inline ::google::protobuf::uint64 RequestHeaderProto::clientprotocolversion() const {
  return clientprotocolversion_;
}
inline void RequestHeaderProto::set_clientprotocolversion(::google::protobuf::uint64 value) {
  set_has_clientprotocolversion();
  clientprotocolversion_ = value;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fProtobufRpcEngine_2eproto__INCLUDED
