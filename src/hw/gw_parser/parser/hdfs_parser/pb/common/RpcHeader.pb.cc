// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/RpcHeader.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/RpcHeader.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* RPCTraceInfoProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RPCTraceInfoProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RpcRequestHeaderProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RpcRequestHeaderProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* RpcRequestHeaderProto_OperationProto_descriptor_ = NULL;
const ::google::protobuf::Descriptor* RpcResponseHeaderProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RpcResponseHeaderProto_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* RpcResponseHeaderProto_RpcStatusProto_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* RpcResponseHeaderProto_RpcErrorCodeProto_descriptor_ = NULL;
const ::google::protobuf::Descriptor* RpcSaslProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RpcSaslProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RpcSaslProto_SaslAuth_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RpcSaslProto_SaslAuth_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* RpcSaslProto_SaslState_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* RpcKindProto_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fRpcHeader_2eproto() {
  protobuf_AddDesc_common_2fRpcHeader_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/RpcHeader.proto");
  GOOGLE_CHECK(file != NULL);
  RPCTraceInfoProto_descriptor_ = file->message_type(0);
  static const int RPCTraceInfoProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RPCTraceInfoProto, traceid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RPCTraceInfoProto, parentid_),
  };
  RPCTraceInfoProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RPCTraceInfoProto_descriptor_,
      RPCTraceInfoProto::default_instance_,
      RPCTraceInfoProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RPCTraceInfoProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RPCTraceInfoProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RPCTraceInfoProto));
  RpcRequestHeaderProto_descriptor_ = file->message_type(1);
  static const int RpcRequestHeaderProto_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, rpckind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, rpcop_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, callid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, clientid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, retrycount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, traceinfo_),
  };
  RpcRequestHeaderProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RpcRequestHeaderProto_descriptor_,
      RpcRequestHeaderProto::default_instance_,
      RpcRequestHeaderProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcRequestHeaderProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RpcRequestHeaderProto));
  RpcRequestHeaderProto_OperationProto_descriptor_ = RpcRequestHeaderProto_descriptor_->enum_type(0);
  RpcResponseHeaderProto_descriptor_ = file->message_type(2);
  static const int RpcResponseHeaderProto_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, callid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, serveripcversionnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, exceptionclassname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, errormsg_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, errordetail_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, clientid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, retrycount_),
  };
  RpcResponseHeaderProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RpcResponseHeaderProto_descriptor_,
      RpcResponseHeaderProto::default_instance_,
      RpcResponseHeaderProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcResponseHeaderProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RpcResponseHeaderProto));
  RpcResponseHeaderProto_RpcStatusProto_descriptor_ = RpcResponseHeaderProto_descriptor_->enum_type(0);
  RpcResponseHeaderProto_RpcErrorCodeProto_descriptor_ = RpcResponseHeaderProto_descriptor_->enum_type(1);
  RpcSaslProto_descriptor_ = file->message_type(3);
  static const int RpcSaslProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto, version_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto, state_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto, token_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto, auths_),
  };
  RpcSaslProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RpcSaslProto_descriptor_,
      RpcSaslProto::default_instance_,
      RpcSaslProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RpcSaslProto));
  RpcSaslProto_SaslAuth_descriptor_ = RpcSaslProto_descriptor_->nested_type(0);
  static const int RpcSaslProto_SaslAuth_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, method_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, mechanism_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, protocol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, serverid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, challenge_),
  };
  RpcSaslProto_SaslAuth_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RpcSaslProto_SaslAuth_descriptor_,
      RpcSaslProto_SaslAuth::default_instance_,
      RpcSaslProto_SaslAuth_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RpcSaslProto_SaslAuth, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RpcSaslProto_SaslAuth));
  RpcSaslProto_SaslState_descriptor_ = RpcSaslProto_descriptor_->enum_type(0);
  RpcKindProto_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fRpcHeader_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RPCTraceInfoProto_descriptor_, &RPCTraceInfoProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RpcRequestHeaderProto_descriptor_, &RpcRequestHeaderProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RpcResponseHeaderProto_descriptor_, &RpcResponseHeaderProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RpcSaslProto_descriptor_, &RpcSaslProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RpcSaslProto_SaslAuth_descriptor_, &RpcSaslProto_SaslAuth::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fRpcHeader_2eproto() {
  delete RPCTraceInfoProto::default_instance_;
  delete RPCTraceInfoProto_reflection_;
  delete RpcRequestHeaderProto::default_instance_;
  delete RpcRequestHeaderProto_reflection_;
  delete RpcResponseHeaderProto::default_instance_;
  delete RpcResponseHeaderProto_reflection_;
  delete RpcSaslProto::default_instance_;
  delete RpcSaslProto_reflection_;
  delete RpcSaslProto_SaslAuth::default_instance_;
  delete RpcSaslProto_SaslAuth_reflection_;
}

void protobuf_AddDesc_common_2fRpcHeader_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\026common/RpcHeader.proto\022\rhadoop.common\""
    "6\n\021RPCTraceInfoProto\022\017\n\007traceId\030\001 \001(\003\022\020\n"
    "\010parentId\030\002 \001(\003\"\327\002\n\025RpcRequestHeaderProt"
    "o\022,\n\007rpcKind\030\001 \001(\0162\033.hadoop.common.RpcKi"
    "ndProto\022B\n\005rpcOp\030\002 \001(\01623.hadoop.common.R"
    "pcRequestHeaderProto.OperationProto\022\016\n\006c"
    "allId\030\003 \002(\021\022\020\n\010clientId\030\004 \002(\014\022\026\n\nretryCo"
    "unt\030\005 \001(\021:\002-1\0223\n\ttraceInfo\030\006 \001(\0132 .hadoo"
    "p.common.RPCTraceInfoProto\"]\n\016OperationP"
    "roto\022\024\n\020RPC_FINAL_PACKET\020\000\022\033\n\027RPC_CONTIN"
    "UATION_PACKET\020\001\022\030\n\024RPC_CLOSE_CONNECTION\020"
    "\002\"\312\005\n\026RpcResponseHeaderProto\022\016\n\006callId\030\001"
    " \002(\r\022D\n\006status\030\002 \002(\01624.hadoop.common.Rpc"
    "ResponseHeaderProto.RpcStatusProto\022\033\n\023se"
    "rverIpcVersionNum\030\003 \001(\r\022\032\n\022exceptionClas"
    "sName\030\004 \001(\t\022\020\n\010errorMsg\030\005 \001(\t\022L\n\013errorDe"
    "tail\030\006 \001(\01627.hadoop.common.RpcResponseHe"
    "aderProto.RpcErrorCodeProto\022\020\n\010clientId\030"
    "\007 \001(\014\022\026\n\nretryCount\030\010 \001(\021:\002-1\"3\n\016RpcStat"
    "usProto\022\013\n\007SUCCESS\020\000\022\t\n\005ERROR\020\001\022\t\n\005FATAL"
    "\020\002\"\341\002\n\021RpcErrorCodeProto\022\025\n\021ERROR_APPLIC"
    "ATION\020\001\022\030\n\024ERROR_NO_SUCH_METHOD\020\002\022\032\n\026ERR"
    "OR_NO_SUCH_PROTOCOL\020\003\022\024\n\020ERROR_RPC_SERVE"
    "R\020\004\022\036\n\032ERROR_SERIALIZING_RESPONSE\020\005\022\036\n\032E"
    "RROR_RPC_VERSION_MISMATCH\020\006\022\021\n\rFATAL_UNK"
    "NOWN\020\n\022#\n\037FATAL_UNSUPPORTED_SERIALIZATIO"
    "N\020\013\022\034\n\030FATAL_INVALID_RPC_HEADER\020\014\022\037\n\033FAT"
    "AL_DESERIALIZING_REQUEST\020\r\022\032\n\026FATAL_VERS"
    "ION_MISMATCH\020\016\022\026\n\022FATAL_UNAUTHORIZED\020\017\"\335"
    "\002\n\014RpcSaslProto\022\017\n\007version\030\001 \001(\r\0224\n\005stat"
    "e\030\002 \002(\0162%.hadoop.common.RpcSaslProto.Sas"
    "lState\022\r\n\005token\030\003 \001(\014\0223\n\005auths\030\004 \003(\0132$.h"
    "adoop.common.RpcSaslProto.SaslAuth\032d\n\010Sa"
    "slAuth\022\016\n\006method\030\001 \002(\t\022\021\n\tmechanism\030\002 \002("
    "\t\022\020\n\010protocol\030\003 \001(\t\022\020\n\010serverId\030\004 \001(\t\022\021\n"
    "\tchallenge\030\005 \001(\014\"\\\n\tSaslState\022\013\n\007SUCCESS"
    "\020\000\022\r\n\tNEGOTIATE\020\001\022\014\n\010INITIATE\020\002\022\r\n\tCHALL"
    "ENGE\020\003\022\014\n\010RESPONSE\020\004\022\010\n\004WRAP\020\005*J\n\014RpcKin"
    "dProto\022\017\n\013RPC_BUILTIN\020\000\022\020\n\014RPC_WRITABLE\020"
    "\001\022\027\n\023RPC_PROTOCOL_BUFFER\020\002B4\n\036org.apache"
    ".hadoop.ipc.protobufB\017RpcHeaderProtos\240\001\001", 1640);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/RpcHeader.proto", &protobuf_RegisterTypes);
  RPCTraceInfoProto::default_instance_ = new RPCTraceInfoProto();
  RpcRequestHeaderProto::default_instance_ = new RpcRequestHeaderProto();
  RpcResponseHeaderProto::default_instance_ = new RpcResponseHeaderProto();
  RpcSaslProto::default_instance_ = new RpcSaslProto();
  RpcSaslProto_SaslAuth::default_instance_ = new RpcSaslProto_SaslAuth();
  RPCTraceInfoProto::default_instance_->InitAsDefaultInstance();
  RpcRequestHeaderProto::default_instance_->InitAsDefaultInstance();
  RpcResponseHeaderProto::default_instance_->InitAsDefaultInstance();
  RpcSaslProto::default_instance_->InitAsDefaultInstance();
  RpcSaslProto_SaslAuth::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fRpcHeader_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fRpcHeader_2eproto {
  StaticDescriptorInitializer_common_2fRpcHeader_2eproto() {
    protobuf_AddDesc_common_2fRpcHeader_2eproto();
  }
} static_descriptor_initializer_common_2fRpcHeader_2eproto_;
const ::google::protobuf::EnumDescriptor* RpcKindProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcKindProto_descriptor_;
}
bool RpcKindProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

#ifndef _MSC_VER
const int RPCTraceInfoProto::kTraceIdFieldNumber;
const int RPCTraceInfoProto::kParentIdFieldNumber;
#endif  // !_MSC_VER

RPCTraceInfoProto::RPCTraceInfoProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RPCTraceInfoProto::InitAsDefaultInstance() {
}

RPCTraceInfoProto::RPCTraceInfoProto(const RPCTraceInfoProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RPCTraceInfoProto::SharedCtor() {
  _cached_size_ = 0;
  traceid_ = GOOGLE_LONGLONG(0);
  parentid_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RPCTraceInfoProto::~RPCTraceInfoProto() {
  SharedDtor();
}

void RPCTraceInfoProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RPCTraceInfoProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RPCTraceInfoProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RPCTraceInfoProto_descriptor_;
}

const RPCTraceInfoProto& RPCTraceInfoProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRpcHeader_2eproto();
  return *default_instance_;
}

RPCTraceInfoProto* RPCTraceInfoProto::default_instance_ = NULL;

RPCTraceInfoProto* RPCTraceInfoProto::New() const {
  return new RPCTraceInfoProto;
}

void RPCTraceInfoProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    traceid_ = GOOGLE_LONGLONG(0);
    parentid_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RPCTraceInfoProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 traceId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &traceid_)));
          set_has_traceid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_parentId;
        break;
      }

      // optional int64 parentId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_parentId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &parentid_)));
          set_has_parentid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RPCTraceInfoProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional int64 traceId = 1;
  if (has_traceid()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->traceid(), output);
  }

  // optional int64 parentId = 2;
  if (has_parentid()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->parentid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RPCTraceInfoProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional int64 traceId = 1;
  if (has_traceid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->traceid(), target);
  }

  // optional int64 parentId = 2;
  if (has_parentid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->parentid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RPCTraceInfoProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional int64 traceId = 1;
    if (has_traceid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->traceid());
    }

    // optional int64 parentId = 2;
    if (has_parentid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->parentid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RPCTraceInfoProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RPCTraceInfoProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RPCTraceInfoProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RPCTraceInfoProto::MergeFrom(const RPCTraceInfoProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_traceid()) {
      set_traceid(from.traceid());
    }
    if (from.has_parentid()) {
      set_parentid(from.parentid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RPCTraceInfoProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RPCTraceInfoProto::CopyFrom(const RPCTraceInfoProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RPCTraceInfoProto::IsInitialized() const {

  return true;
}

void RPCTraceInfoProto::Swap(RPCTraceInfoProto* other) {
  if (other != this) {
    std::swap(traceid_, other->traceid_);
    std::swap(parentid_, other->parentid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RPCTraceInfoProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RPCTraceInfoProto_descriptor_;
  metadata.reflection = RPCTraceInfoProto_reflection_;
  return metadata;
}


// ===================================================================

const ::google::protobuf::EnumDescriptor* RpcRequestHeaderProto_OperationProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcRequestHeaderProto_OperationProto_descriptor_;
}
bool RpcRequestHeaderProto_OperationProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto::RPC_FINAL_PACKET;
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto::RPC_CONTINUATION_PACKET;
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto::RPC_CLOSE_CONNECTION;
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto::OperationProto_MIN;
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto::OperationProto_MAX;
const int RpcRequestHeaderProto::OperationProto_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int RpcRequestHeaderProto::kRpcKindFieldNumber;
const int RpcRequestHeaderProto::kRpcOpFieldNumber;
const int RpcRequestHeaderProto::kCallIdFieldNumber;
const int RpcRequestHeaderProto::kClientIdFieldNumber;
const int RpcRequestHeaderProto::kRetryCountFieldNumber;
const int RpcRequestHeaderProto::kTraceInfoFieldNumber;
#endif  // !_MSC_VER

RpcRequestHeaderProto::RpcRequestHeaderProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RpcRequestHeaderProto::InitAsDefaultInstance() {
  traceinfo_ = const_cast< ::hadoop::common::RPCTraceInfoProto*>(&::hadoop::common::RPCTraceInfoProto::default_instance());
}

RpcRequestHeaderProto::RpcRequestHeaderProto(const RpcRequestHeaderProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RpcRequestHeaderProto::SharedCtor() {
  _cached_size_ = 0;
  rpckind_ = 0;
  rpcop_ = 0;
  callid_ = 0;
  clientid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  retrycount_ = -1;
  traceinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RpcRequestHeaderProto::~RpcRequestHeaderProto() {
  SharedDtor();
}

void RpcRequestHeaderProto::SharedDtor() {
  if (clientid_ != &::google::protobuf::internal::kEmptyString) {
    delete clientid_;
  }
  if (this != default_instance_) {
    delete traceinfo_;
  }
}

void RpcRequestHeaderProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RpcRequestHeaderProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcRequestHeaderProto_descriptor_;
}

const RpcRequestHeaderProto& RpcRequestHeaderProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRpcHeader_2eproto();
  return *default_instance_;
}

RpcRequestHeaderProto* RpcRequestHeaderProto::default_instance_ = NULL;

RpcRequestHeaderProto* RpcRequestHeaderProto::New() const {
  return new RpcRequestHeaderProto;
}

void RpcRequestHeaderProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    rpckind_ = 0;
    rpcop_ = 0;
    callid_ = 0;
    if (has_clientid()) {
      if (clientid_ != &::google::protobuf::internal::kEmptyString) {
        clientid_->clear();
      }
    }
    retrycount_ = -1;
    if (has_traceinfo()) {
      if (traceinfo_ != NULL) traceinfo_->::hadoop::common::RPCTraceInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RpcRequestHeaderProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .hadoop.common.RpcKindProto rpcKind = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::RpcKindProto_IsValid(value)) {
            set_rpckind(static_cast< ::hadoop::common::RpcKindProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_rpcOp;
        break;
      }

      // optional .hadoop.common.RpcRequestHeaderProto.OperationProto rpcOp = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_rpcOp:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::RpcRequestHeaderProto_OperationProto_IsValid(value)) {
            set_rpcop(static_cast< ::hadoop::common::RpcRequestHeaderProto_OperationProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(2, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_callId;
        break;
      }

      // required sint32 callId = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_callId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &callid_)));
          set_has_callid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_clientId;
        break;
      }

      // required bytes clientId = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_clientId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_clientid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_retryCount;
        break;
      }

      // optional sint32 retryCount = 5 [default = -1];
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_retryCount:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &retrycount_)));
          set_has_retrycount();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(50)) goto parse_traceInfo;
        break;
      }

      // optional .hadoop.common.RPCTraceInfoProto traceInfo = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_traceInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_traceinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RpcRequestHeaderProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional .hadoop.common.RpcKindProto rpcKind = 1;
  if (has_rpckind()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->rpckind(), output);
  }

  // optional .hadoop.common.RpcRequestHeaderProto.OperationProto rpcOp = 2;
  if (has_rpcop()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->rpcop(), output);
  }

  // required sint32 callId = 3;
  if (has_callid()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(3, this->callid(), output);
  }

  // required bytes clientId = 4;
  if (has_clientid()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      4, this->clientid(), output);
  }

  // optional sint32 retryCount = 5 [default = -1];
  if (has_retrycount()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(5, this->retrycount(), output);
  }

  // optional .hadoop.common.RPCTraceInfoProto traceInfo = 6;
  if (has_traceinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->traceinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RpcRequestHeaderProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional .hadoop.common.RpcKindProto rpcKind = 1;
  if (has_rpckind()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->rpckind(), target);
  }

  // optional .hadoop.common.RpcRequestHeaderProto.OperationProto rpcOp = 2;
  if (has_rpcop()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->rpcop(), target);
  }

  // required sint32 callId = 3;
  if (has_callid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(3, this->callid(), target);
  }

  // required bytes clientId = 4;
  if (has_clientid()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->clientid(), target);
  }

  // optional sint32 retryCount = 5 [default = -1];
  if (has_retrycount()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(5, this->retrycount(), target);
  }

  // optional .hadoop.common.RPCTraceInfoProto traceInfo = 6;
  if (has_traceinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        6, this->traceinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RpcRequestHeaderProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional .hadoop.common.RpcKindProto rpcKind = 1;
    if (has_rpckind()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->rpckind());
    }

    // optional .hadoop.common.RpcRequestHeaderProto.OperationProto rpcOp = 2;
    if (has_rpcop()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->rpcop());
    }

    // required sint32 callId = 3;
    if (has_callid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->callid());
    }

    // required bytes clientId = 4;
    if (has_clientid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->clientid());
    }

    // optional sint32 retryCount = 5 [default = -1];
    if (has_retrycount()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->retrycount());
    }

    // optional .hadoop.common.RPCTraceInfoProto traceInfo = 6;
    if (has_traceinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->traceinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RpcRequestHeaderProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RpcRequestHeaderProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RpcRequestHeaderProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RpcRequestHeaderProto::MergeFrom(const RpcRequestHeaderProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_rpckind()) {
      set_rpckind(from.rpckind());
    }
    if (from.has_rpcop()) {
      set_rpcop(from.rpcop());
    }
    if (from.has_callid()) {
      set_callid(from.callid());
    }
    if (from.has_clientid()) {
      set_clientid(from.clientid());
    }
    if (from.has_retrycount()) {
      set_retrycount(from.retrycount());
    }
    if (from.has_traceinfo()) {
      mutable_traceinfo()->::hadoop::common::RPCTraceInfoProto::MergeFrom(from.traceinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RpcRequestHeaderProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RpcRequestHeaderProto::CopyFrom(const RpcRequestHeaderProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RpcRequestHeaderProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000000c) != 0x0000000c) return false;

  return true;
}

void RpcRequestHeaderProto::Swap(RpcRequestHeaderProto* other) {
  if (other != this) {
    std::swap(rpckind_, other->rpckind_);
    std::swap(rpcop_, other->rpcop_);
    std::swap(callid_, other->callid_);
    std::swap(clientid_, other->clientid_);
    std::swap(retrycount_, other->retrycount_);
    std::swap(traceinfo_, other->traceinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RpcRequestHeaderProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RpcRequestHeaderProto_descriptor_;
  metadata.reflection = RpcRequestHeaderProto_reflection_;
  return metadata;
}


// ===================================================================

const ::google::protobuf::EnumDescriptor* RpcResponseHeaderProto_RpcStatusProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcResponseHeaderProto_RpcStatusProto_descriptor_;
}
bool RpcResponseHeaderProto_RpcStatusProto_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto::SUCCESS;
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto::ERROR;
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto::FATAL;
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto::RpcStatusProto_MIN;
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto::RpcStatusProto_MAX;
const int RpcResponseHeaderProto::RpcStatusProto_ARRAYSIZE;
#endif  // _MSC_VER
const ::google::protobuf::EnumDescriptor* RpcResponseHeaderProto_RpcErrorCodeProto_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcResponseHeaderProto_RpcErrorCodeProto_descriptor_;
}
bool RpcResponseHeaderProto_RpcErrorCodeProto_IsValid(int value) {
  switch(value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::ERROR_APPLICATION;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::ERROR_NO_SUCH_METHOD;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::ERROR_NO_SUCH_PROTOCOL;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::ERROR_RPC_SERVER;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::ERROR_SERIALIZING_RESPONSE;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::ERROR_RPC_VERSION_MISMATCH;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::FATAL_UNKNOWN;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::FATAL_UNSUPPORTED_SERIALIZATION;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::FATAL_INVALID_RPC_HEADER;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::FATAL_DESERIALIZING_REQUEST;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::FATAL_VERSION_MISMATCH;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::FATAL_UNAUTHORIZED;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::RpcErrorCodeProto_MIN;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::RpcErrorCodeProto_MAX;
const int RpcResponseHeaderProto::RpcErrorCodeProto_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int RpcResponseHeaderProto::kCallIdFieldNumber;
const int RpcResponseHeaderProto::kStatusFieldNumber;
const int RpcResponseHeaderProto::kServerIpcVersionNumFieldNumber;
const int RpcResponseHeaderProto::kExceptionClassNameFieldNumber;
const int RpcResponseHeaderProto::kErrorMsgFieldNumber;
const int RpcResponseHeaderProto::kErrorDetailFieldNumber;
const int RpcResponseHeaderProto::kClientIdFieldNumber;
const int RpcResponseHeaderProto::kRetryCountFieldNumber;
#endif  // !_MSC_VER

RpcResponseHeaderProto::RpcResponseHeaderProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RpcResponseHeaderProto::InitAsDefaultInstance() {
}

RpcResponseHeaderProto::RpcResponseHeaderProto(const RpcResponseHeaderProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RpcResponseHeaderProto::SharedCtor() {
  _cached_size_ = 0;
  callid_ = 0u;
  status_ = 0;
  serveripcversionnum_ = 0u;
  exceptionclassname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  errordetail_ = 1;
  clientid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  retrycount_ = -1;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RpcResponseHeaderProto::~RpcResponseHeaderProto() {
  SharedDtor();
}

void RpcResponseHeaderProto::SharedDtor() {
  if (exceptionclassname_ != &::google::protobuf::internal::kEmptyString) {
    delete exceptionclassname_;
  }
  if (errormsg_ != &::google::protobuf::internal::kEmptyString) {
    delete errormsg_;
  }
  if (clientid_ != &::google::protobuf::internal::kEmptyString) {
    delete clientid_;
  }
  if (this != default_instance_) {
  }
}

void RpcResponseHeaderProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RpcResponseHeaderProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcResponseHeaderProto_descriptor_;
}

const RpcResponseHeaderProto& RpcResponseHeaderProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRpcHeader_2eproto();
  return *default_instance_;
}

RpcResponseHeaderProto* RpcResponseHeaderProto::default_instance_ = NULL;

RpcResponseHeaderProto* RpcResponseHeaderProto::New() const {
  return new RpcResponseHeaderProto;
}

void RpcResponseHeaderProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    callid_ = 0u;
    status_ = 0;
    serveripcversionnum_ = 0u;
    if (has_exceptionclassname()) {
      if (exceptionclassname_ != &::google::protobuf::internal::kEmptyString) {
        exceptionclassname_->clear();
      }
    }
    if (has_errormsg()) {
      if (errormsg_ != &::google::protobuf::internal::kEmptyString) {
        errormsg_->clear();
      }
    }
    errordetail_ = 1;
    if (has_clientid()) {
      if (clientid_ != &::google::protobuf::internal::kEmptyString) {
        clientid_->clear();
      }
    }
    retrycount_ = -1;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RpcResponseHeaderProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint32 callId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &callid_)));
          set_has_callid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_status;
        break;
      }

      // required .hadoop.common.RpcResponseHeaderProto.RpcStatusProto status = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_status:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::RpcResponseHeaderProto_RpcStatusProto_IsValid(value)) {
            set_status(static_cast< ::hadoop::common::RpcResponseHeaderProto_RpcStatusProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(2, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_serverIpcVersionNum;
        break;
      }

      // optional uint32 serverIpcVersionNum = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_serverIpcVersionNum:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &serveripcversionnum_)));
          set_has_serveripcversionnum();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_exceptionClassName;
        break;
      }

      // optional string exceptionClassName = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_exceptionClassName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exceptionclassname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->exceptionclassname().data(), this->exceptionclassname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_errorMsg;
        break;
      }

      // optional string errorMsg = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_errorMsg:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_errormsg()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->errormsg().data(), this->errormsg().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(48)) goto parse_errorDetail;
        break;
      }

      // optional .hadoop.common.RpcResponseHeaderProto.RpcErrorCodeProto errorDetail = 6;
      case 6: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_errorDetail:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto_IsValid(value)) {
            set_errordetail(static_cast< ::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto >(value));
          } else {
            mutable_unknown_fields()->AddVarint(6, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(58)) goto parse_clientId;
        break;
      }

      // optional bytes clientId = 7;
      case 7: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_clientId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_clientid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(64)) goto parse_retryCount;
        break;
      }

      // optional sint32 retryCount = 8 [default = -1];
      case 8: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_retryCount:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &retrycount_)));
          set_has_retrycount();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RpcResponseHeaderProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint32 callId = 1;
  if (has_callid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->callid(), output);
  }

  // required .hadoop.common.RpcResponseHeaderProto.RpcStatusProto status = 2;
  if (has_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->status(), output);
  }

  // optional uint32 serverIpcVersionNum = 3;
  if (has_serveripcversionnum()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->serveripcversionnum(), output);
  }

  // optional string exceptionClassName = 4;
  if (has_exceptionclassname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->exceptionclassname().data(), this->exceptionclassname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->exceptionclassname(), output);
  }

  // optional string errorMsg = 5;
  if (has_errormsg()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->errormsg().data(), this->errormsg().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->errormsg(), output);
  }

  // optional .hadoop.common.RpcResponseHeaderProto.RpcErrorCodeProto errorDetail = 6;
  if (has_errordetail()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->errordetail(), output);
  }

  // optional bytes clientId = 7;
  if (has_clientid()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      7, this->clientid(), output);
  }

  // optional sint32 retryCount = 8 [default = -1];
  if (has_retrycount()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(8, this->retrycount(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RpcResponseHeaderProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint32 callId = 1;
  if (has_callid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->callid(), target);
  }

  // required .hadoop.common.RpcResponseHeaderProto.RpcStatusProto status = 2;
  if (has_status()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->status(), target);
  }

  // optional uint32 serverIpcVersionNum = 3;
  if (has_serveripcversionnum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->serveripcversionnum(), target);
  }

  // optional string exceptionClassName = 4;
  if (has_exceptionclassname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->exceptionclassname().data(), this->exceptionclassname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->exceptionclassname(), target);
  }

  // optional string errorMsg = 5;
  if (has_errormsg()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->errormsg().data(), this->errormsg().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->errormsg(), target);
  }

  // optional .hadoop.common.RpcResponseHeaderProto.RpcErrorCodeProto errorDetail = 6;
  if (has_errordetail()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->errordetail(), target);
  }

  // optional bytes clientId = 7;
  if (has_clientid()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        7, this->clientid(), target);
  }

  // optional sint32 retryCount = 8 [default = -1];
  if (has_retrycount()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(8, this->retrycount(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RpcResponseHeaderProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint32 callId = 1;
    if (has_callid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->callid());
    }

    // required .hadoop.common.RpcResponseHeaderProto.RpcStatusProto status = 2;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
    }

    // optional uint32 serverIpcVersionNum = 3;
    if (has_serveripcversionnum()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->serveripcversionnum());
    }

    // optional string exceptionClassName = 4;
    if (has_exceptionclassname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->exceptionclassname());
    }

    // optional string errorMsg = 5;
    if (has_errormsg()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->errormsg());
    }

    // optional .hadoop.common.RpcResponseHeaderProto.RpcErrorCodeProto errorDetail = 6;
    if (has_errordetail()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->errordetail());
    }

    // optional bytes clientId = 7;
    if (has_clientid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->clientid());
    }

    // optional sint32 retryCount = 8 [default = -1];
    if (has_retrycount()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->retrycount());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RpcResponseHeaderProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RpcResponseHeaderProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RpcResponseHeaderProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RpcResponseHeaderProto::MergeFrom(const RpcResponseHeaderProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_callid()) {
      set_callid(from.callid());
    }
    if (from.has_status()) {
      set_status(from.status());
    }
    if (from.has_serveripcversionnum()) {
      set_serveripcversionnum(from.serveripcversionnum());
    }
    if (from.has_exceptionclassname()) {
      set_exceptionclassname(from.exceptionclassname());
    }
    if (from.has_errormsg()) {
      set_errormsg(from.errormsg());
    }
    if (from.has_errordetail()) {
      set_errordetail(from.errordetail());
    }
    if (from.has_clientid()) {
      set_clientid(from.clientid());
    }
    if (from.has_retrycount()) {
      set_retrycount(from.retrycount());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RpcResponseHeaderProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RpcResponseHeaderProto::CopyFrom(const RpcResponseHeaderProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RpcResponseHeaderProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void RpcResponseHeaderProto::Swap(RpcResponseHeaderProto* other) {
  if (other != this) {
    std::swap(callid_, other->callid_);
    std::swap(status_, other->status_);
    std::swap(serveripcversionnum_, other->serveripcversionnum_);
    std::swap(exceptionclassname_, other->exceptionclassname_);
    std::swap(errormsg_, other->errormsg_);
    std::swap(errordetail_, other->errordetail_);
    std::swap(clientid_, other->clientid_);
    std::swap(retrycount_, other->retrycount_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RpcResponseHeaderProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RpcResponseHeaderProto_descriptor_;
  metadata.reflection = RpcResponseHeaderProto_reflection_;
  return metadata;
}


// ===================================================================

const ::google::protobuf::EnumDescriptor* RpcSaslProto_SaslState_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcSaslProto_SaslState_descriptor_;
}
bool RpcSaslProto_SaslState_IsValid(int value) {
  switch(value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

#ifndef _MSC_VER
const RpcSaslProto_SaslState RpcSaslProto::SUCCESS;
const RpcSaslProto_SaslState RpcSaslProto::NEGOTIATE;
const RpcSaslProto_SaslState RpcSaslProto::INITIATE;
const RpcSaslProto_SaslState RpcSaslProto::CHALLENGE;
const RpcSaslProto_SaslState RpcSaslProto::RESPONSE;
const RpcSaslProto_SaslState RpcSaslProto::WRAP;
const RpcSaslProto_SaslState RpcSaslProto::SaslState_MIN;
const RpcSaslProto_SaslState RpcSaslProto::SaslState_MAX;
const int RpcSaslProto::SaslState_ARRAYSIZE;
#endif  // _MSC_VER
#ifndef _MSC_VER
const int RpcSaslProto_SaslAuth::kMethodFieldNumber;
const int RpcSaslProto_SaslAuth::kMechanismFieldNumber;
const int RpcSaslProto_SaslAuth::kProtocolFieldNumber;
const int RpcSaslProto_SaslAuth::kServerIdFieldNumber;
const int RpcSaslProto_SaslAuth::kChallengeFieldNumber;
#endif  // !_MSC_VER

RpcSaslProto_SaslAuth::RpcSaslProto_SaslAuth()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RpcSaslProto_SaslAuth::InitAsDefaultInstance() {
}

RpcSaslProto_SaslAuth::RpcSaslProto_SaslAuth(const RpcSaslProto_SaslAuth& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RpcSaslProto_SaslAuth::SharedCtor() {
  _cached_size_ = 0;
  method_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  mechanism_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  serverid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  challenge_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RpcSaslProto_SaslAuth::~RpcSaslProto_SaslAuth() {
  SharedDtor();
}

void RpcSaslProto_SaslAuth::SharedDtor() {
  if (method_ != &::google::protobuf::internal::kEmptyString) {
    delete method_;
  }
  if (mechanism_ != &::google::protobuf::internal::kEmptyString) {
    delete mechanism_;
  }
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (serverid_ != &::google::protobuf::internal::kEmptyString) {
    delete serverid_;
  }
  if (challenge_ != &::google::protobuf::internal::kEmptyString) {
    delete challenge_;
  }
  if (this != default_instance_) {
  }
}

void RpcSaslProto_SaslAuth::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RpcSaslProto_SaslAuth::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcSaslProto_SaslAuth_descriptor_;
}

const RpcSaslProto_SaslAuth& RpcSaslProto_SaslAuth::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRpcHeader_2eproto();
  return *default_instance_;
}

RpcSaslProto_SaslAuth* RpcSaslProto_SaslAuth::default_instance_ = NULL;

RpcSaslProto_SaslAuth* RpcSaslProto_SaslAuth::New() const {
  return new RpcSaslProto_SaslAuth;
}

void RpcSaslProto_SaslAuth::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_method()) {
      if (method_ != &::google::protobuf::internal::kEmptyString) {
        method_->clear();
      }
    }
    if (has_mechanism()) {
      if (mechanism_ != &::google::protobuf::internal::kEmptyString) {
        mechanism_->clear();
      }
    }
    if (has_protocol()) {
      if (protocol_ != &::google::protobuf::internal::kEmptyString) {
        protocol_->clear();
      }
    }
    if (has_serverid()) {
      if (serverid_ != &::google::protobuf::internal::kEmptyString) {
        serverid_->clear();
      }
    }
    if (has_challenge()) {
      if (challenge_ != &::google::protobuf::internal::kEmptyString) {
        challenge_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RpcSaslProto_SaslAuth::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string method = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_method()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->method().data(), this->method().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_mechanism;
        break;
      }

      // required string mechanism = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_mechanism:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_mechanism()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->mechanism().data(), this->mechanism().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_protocol;
        break;
      }

      // optional string protocol = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_protocol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_protocol()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->protocol().data(), this->protocol().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_serverId;
        break;
      }

      // optional string serverId = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_serverId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_serverid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->serverid().data(), this->serverid().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(42)) goto parse_challenge;
        break;
      }

      // optional bytes challenge = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_challenge:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_challenge()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RpcSaslProto_SaslAuth::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string method = 1;
  if (has_method()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->method().data(), this->method().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->method(), output);
  }

  // required string mechanism = 2;
  if (has_mechanism()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->mechanism().data(), this->mechanism().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->mechanism(), output);
  }

  // optional string protocol = 3;
  if (has_protocol()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->protocol().data(), this->protocol().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->protocol(), output);
  }

  // optional string serverId = 4;
  if (has_serverid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->serverid().data(), this->serverid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->serverid(), output);
  }

  // optional bytes challenge = 5;
  if (has_challenge()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      5, this->challenge(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RpcSaslProto_SaslAuth::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string method = 1;
  if (has_method()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->method().data(), this->method().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->method(), target);
  }

  // required string mechanism = 2;
  if (has_mechanism()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->mechanism().data(), this->mechanism().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->mechanism(), target);
  }

  // optional string protocol = 3;
  if (has_protocol()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->protocol().data(), this->protocol().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->protocol(), target);
  }

  // optional string serverId = 4;
  if (has_serverid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->serverid().data(), this->serverid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->serverid(), target);
  }

  // optional bytes challenge = 5;
  if (has_challenge()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        5, this->challenge(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RpcSaslProto_SaslAuth::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string method = 1;
    if (has_method()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->method());
    }

    // required string mechanism = 2;
    if (has_mechanism()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->mechanism());
    }

    // optional string protocol = 3;
    if (has_protocol()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->protocol());
    }

    // optional string serverId = 4;
    if (has_serverid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->serverid());
    }

    // optional bytes challenge = 5;
    if (has_challenge()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->challenge());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RpcSaslProto_SaslAuth::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RpcSaslProto_SaslAuth* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RpcSaslProto_SaslAuth*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RpcSaslProto_SaslAuth::MergeFrom(const RpcSaslProto_SaslAuth& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_method()) {
      set_method(from.method());
    }
    if (from.has_mechanism()) {
      set_mechanism(from.mechanism());
    }
    if (from.has_protocol()) {
      set_protocol(from.protocol());
    }
    if (from.has_serverid()) {
      set_serverid(from.serverid());
    }
    if (from.has_challenge()) {
      set_challenge(from.challenge());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RpcSaslProto_SaslAuth::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RpcSaslProto_SaslAuth::CopyFrom(const RpcSaslProto_SaslAuth& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RpcSaslProto_SaslAuth::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void RpcSaslProto_SaslAuth::Swap(RpcSaslProto_SaslAuth* other) {
  if (other != this) {
    std::swap(method_, other->method_);
    std::swap(mechanism_, other->mechanism_);
    std::swap(protocol_, other->protocol_);
    std::swap(serverid_, other->serverid_);
    std::swap(challenge_, other->challenge_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RpcSaslProto_SaslAuth::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RpcSaslProto_SaslAuth_descriptor_;
  metadata.reflection = RpcSaslProto_SaslAuth_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#ifndef _MSC_VER
const int RpcSaslProto::kVersionFieldNumber;
const int RpcSaslProto::kStateFieldNumber;
const int RpcSaslProto::kTokenFieldNumber;
const int RpcSaslProto::kAuthsFieldNumber;
#endif  // !_MSC_VER

RpcSaslProto::RpcSaslProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RpcSaslProto::InitAsDefaultInstance() {
}

RpcSaslProto::RpcSaslProto(const RpcSaslProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RpcSaslProto::SharedCtor() {
  _cached_size_ = 0;
  version_ = 0u;
  state_ = 0;
  token_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RpcSaslProto::~RpcSaslProto() {
  SharedDtor();
}

void RpcSaslProto::SharedDtor() {
  if (token_ != &::google::protobuf::internal::kEmptyString) {
    delete token_;
  }
  if (this != default_instance_) {
  }
}

void RpcSaslProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RpcSaslProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RpcSaslProto_descriptor_;
}

const RpcSaslProto& RpcSaslProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRpcHeader_2eproto();
  return *default_instance_;
}

RpcSaslProto* RpcSaslProto::default_instance_ = NULL;

RpcSaslProto* RpcSaslProto::New() const {
  return new RpcSaslProto;
}

void RpcSaslProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    version_ = 0u;
    state_ = 0;
    if (has_token()) {
      if (token_ != &::google::protobuf::internal::kEmptyString) {
        token_->clear();
      }
    }
  }
  auths_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RpcSaslProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 version = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &version_)));
          set_has_version();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_state;
        break;
      }

      // required .hadoop.common.RpcSaslProto.SaslState state = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_state:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::hadoop::common::RpcSaslProto_SaslState_IsValid(value)) {
            set_state(static_cast< ::hadoop::common::RpcSaslProto_SaslState >(value));
          } else {
            mutable_unknown_fields()->AddVarint(2, value);
          }
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_token;
        break;
      }

      // optional bytes token = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_token:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_token()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_auths;
        break;
      }

      // repeated .hadoop.common.RpcSaslProto.SaslAuth auths = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_auths:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_auths()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_auths;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RpcSaslProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint32 version = 1;
  if (has_version()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->version(), output);
  }

  // required .hadoop.common.RpcSaslProto.SaslState state = 2;
  if (has_state()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->state(), output);
  }

  // optional bytes token = 3;
  if (has_token()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      3, this->token(), output);
  }

  // repeated .hadoop.common.RpcSaslProto.SaslAuth auths = 4;
  for (int i = 0; i < this->auths_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->auths(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RpcSaslProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint32 version = 1;
  if (has_version()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->version(), target);
  }

  // required .hadoop.common.RpcSaslProto.SaslState state = 2;
  if (has_state()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->state(), target);
  }

  // optional bytes token = 3;
  if (has_token()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->token(), target);
  }

  // repeated .hadoop.common.RpcSaslProto.SaslAuth auths = 4;
  for (int i = 0; i < this->auths_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        4, this->auths(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RpcSaslProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint32 version = 1;
    if (has_version()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->version());
    }

    // required .hadoop.common.RpcSaslProto.SaslState state = 2;
    if (has_state()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->state());
    }

    // optional bytes token = 3;
    if (has_token()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->token());
    }

  }
  // repeated .hadoop.common.RpcSaslProto.SaslAuth auths = 4;
  total_size += 1 * this->auths_size();
  for (int i = 0; i < this->auths_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->auths(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RpcSaslProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RpcSaslProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RpcSaslProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RpcSaslProto::MergeFrom(const RpcSaslProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  auths_.MergeFrom(from.auths_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_version()) {
      set_version(from.version());
    }
    if (from.has_state()) {
      set_state(from.state());
    }
    if (from.has_token()) {
      set_token(from.token());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RpcSaslProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RpcSaslProto::CopyFrom(const RpcSaslProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RpcSaslProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000002) != 0x00000002) return false;

  for (int i = 0; i < auths_size(); i++) {
    if (!this->auths(i).IsInitialized()) return false;
  }
  return true;
}

void RpcSaslProto::Swap(RpcSaslProto* other) {
  if (other != this) {
    std::swap(version_, other->version_);
    std::swap(state_, other->state_);
    std::swap(token_, other->token_);
    auths_.Swap(&other->auths_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RpcSaslProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RpcSaslProto_descriptor_;
  metadata.reflection = RpcSaslProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
