// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/IpcConnectionContext.proto

#ifndef PROTOBUF_common_2fIpcConnectionContext_2eproto__INCLUDED
#define PROTOBUF_common_2fIpcConnectionContext_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fIpcConnectionContext_2eproto();
void protobuf_AssignDesc_common_2fIpcConnectionContext_2eproto();
void protobuf_ShutdownFile_common_2fIpcConnectionContext_2eproto();

class UserInformationProto;
class IpcConnectionContextProto;

// ===================================================================

class UserInformationProto : public ::google::protobuf::Message {
 public:
  UserInformationProto();
  virtual ~UserInformationProto();

  UserInformationProto(const UserInformationProto& from);

  inline UserInformationProto& operator=(const UserInformationProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UserInformationProto& default_instance();

  void Swap(UserInformationProto* other);

  // implements Message ----------------------------------------------

  UserInformationProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UserInformationProto& from);
  void MergeFrom(const UserInformationProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string effectiveUser = 1;
  inline bool has_effectiveuser() const;
  inline void clear_effectiveuser();
  static const int kEffectiveUserFieldNumber = 1;
  inline const ::std::string& effectiveuser() const;
  inline void set_effectiveuser(const ::std::string& value);
  inline void set_effectiveuser(const char* value);
  inline void set_effectiveuser(const char* value, size_t size);
  inline ::std::string* mutable_effectiveuser();
  inline ::std::string* release_effectiveuser();
  inline void set_allocated_effectiveuser(::std::string* effectiveuser);

  // optional string realUser = 2;
  inline bool has_realuser() const;
  inline void clear_realuser();
  static const int kRealUserFieldNumber = 2;
  inline const ::std::string& realuser() const;
  inline void set_realuser(const ::std::string& value);
  inline void set_realuser(const char* value);
  inline void set_realuser(const char* value, size_t size);
  inline ::std::string* mutable_realuser();
  inline ::std::string* release_realuser();
  inline void set_allocated_realuser(::std::string* realuser);

  // @@protoc_insertion_point(class_scope:hadoop.common.UserInformationProto)
 private:
  inline void set_has_effectiveuser();
  inline void clear_has_effectiveuser();
  inline void set_has_realuser();
  inline void clear_has_realuser();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* effectiveuser_;
  ::std::string* realuser_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fIpcConnectionContext_2eproto();
  friend void protobuf_AssignDesc_common_2fIpcConnectionContext_2eproto();
  friend void protobuf_ShutdownFile_common_2fIpcConnectionContext_2eproto();

  void InitAsDefaultInstance();
  static UserInformationProto* default_instance_;
};
// -------------------------------------------------------------------

class IpcConnectionContextProto : public ::google::protobuf::Message {
 public:
  IpcConnectionContextProto();
  virtual ~IpcConnectionContextProto();

  IpcConnectionContextProto(const IpcConnectionContextProto& from);

  inline IpcConnectionContextProto& operator=(const IpcConnectionContextProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const IpcConnectionContextProto& default_instance();

  void Swap(IpcConnectionContextProto* other);

  // implements Message ----------------------------------------------

  IpcConnectionContextProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const IpcConnectionContextProto& from);
  void MergeFrom(const IpcConnectionContextProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .hadoop.common.UserInformationProto userInfo = 2;
  inline bool has_userinfo() const;
  inline void clear_userinfo();
  static const int kUserInfoFieldNumber = 2;
  inline const ::hadoop::common::UserInformationProto& userinfo() const;
  inline ::hadoop::common::UserInformationProto* mutable_userinfo();
  inline ::hadoop::common::UserInformationProto* release_userinfo();
  inline void set_allocated_userinfo(::hadoop::common::UserInformationProto* userinfo);

  // optional string protocol = 3;
  inline bool has_protocol() const;
  inline void clear_protocol();
  static const int kProtocolFieldNumber = 3;
  inline const ::std::string& protocol() const;
  inline void set_protocol(const ::std::string& value);
  inline void set_protocol(const char* value);
  inline void set_protocol(const char* value, size_t size);
  inline ::std::string* mutable_protocol();
  inline ::std::string* release_protocol();
  inline void set_allocated_protocol(::std::string* protocol);

  // @@protoc_insertion_point(class_scope:hadoop.common.IpcConnectionContextProto)
 private:
  inline void set_has_userinfo();
  inline void clear_has_userinfo();
  inline void set_has_protocol();
  inline void clear_has_protocol();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::common::UserInformationProto* userinfo_;
  ::std::string* protocol_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fIpcConnectionContext_2eproto();
  friend void protobuf_AssignDesc_common_2fIpcConnectionContext_2eproto();
  friend void protobuf_ShutdownFile_common_2fIpcConnectionContext_2eproto();

  void InitAsDefaultInstance();
  static IpcConnectionContextProto* default_instance_;
};
// ===================================================================


// ===================================================================

// UserInformationProto

// optional string effectiveUser = 1;
inline bool UserInformationProto::has_effectiveuser() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void UserInformationProto::set_has_effectiveuser() {
  _has_bits_[0] |= 0x00000001u;
}
inline void UserInformationProto::clear_has_effectiveuser() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void UserInformationProto::clear_effectiveuser() {
  if (effectiveuser_ != &::google::protobuf::internal::kEmptyString) {
    effectiveuser_->clear();
  }
  clear_has_effectiveuser();
}
inline const ::std::string& UserInformationProto::effectiveuser() const {
  return *effectiveuser_;
}
inline void UserInformationProto::set_effectiveuser(const ::std::string& value) {
  set_has_effectiveuser();
  if (effectiveuser_ == &::google::protobuf::internal::kEmptyString) {
    effectiveuser_ = new ::std::string;
  }
  effectiveuser_->assign(value);
}
inline void UserInformationProto::set_effectiveuser(const char* value) {
  set_has_effectiveuser();
  if (effectiveuser_ == &::google::protobuf::internal::kEmptyString) {
    effectiveuser_ = new ::std::string;
  }
  effectiveuser_->assign(value);
}
inline void UserInformationProto::set_effectiveuser(const char* value, size_t size) {
  set_has_effectiveuser();
  if (effectiveuser_ == &::google::protobuf::internal::kEmptyString) {
    effectiveuser_ = new ::std::string;
  }
  effectiveuser_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* UserInformationProto::mutable_effectiveuser() {
  set_has_effectiveuser();
  if (effectiveuser_ == &::google::protobuf::internal::kEmptyString) {
    effectiveuser_ = new ::std::string;
  }
  return effectiveuser_;
}
inline ::std::string* UserInformationProto::release_effectiveuser() {
  clear_has_effectiveuser();
  if (effectiveuser_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = effectiveuser_;
    effectiveuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void UserInformationProto::set_allocated_effectiveuser(::std::string* effectiveuser) {
  if (effectiveuser_ != &::google::protobuf::internal::kEmptyString) {
    delete effectiveuser_;
  }
  if (effectiveuser) {
    set_has_effectiveuser();
    effectiveuser_ = effectiveuser;
  } else {
    clear_has_effectiveuser();
    effectiveuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string realUser = 2;
inline bool UserInformationProto::has_realuser() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void UserInformationProto::set_has_realuser() {
  _has_bits_[0] |= 0x00000002u;
}
inline void UserInformationProto::clear_has_realuser() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void UserInformationProto::clear_realuser() {
  if (realuser_ != &::google::protobuf::internal::kEmptyString) {
    realuser_->clear();
  }
  clear_has_realuser();
}
inline const ::std::string& UserInformationProto::realuser() const {
  return *realuser_;
}
inline void UserInformationProto::set_realuser(const ::std::string& value) {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  realuser_->assign(value);
}
inline void UserInformationProto::set_realuser(const char* value) {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  realuser_->assign(value);
}
inline void UserInformationProto::set_realuser(const char* value, size_t size) {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  realuser_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* UserInformationProto::mutable_realuser() {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  return realuser_;
}
inline ::std::string* UserInformationProto::release_realuser() {
  clear_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = realuser_;
    realuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void UserInformationProto::set_allocated_realuser(::std::string* realuser) {
  if (realuser_ != &::google::protobuf::internal::kEmptyString) {
    delete realuser_;
  }
  if (realuser) {
    set_has_realuser();
    realuser_ = realuser;
  } else {
    clear_has_realuser();
    realuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// IpcConnectionContextProto

// optional .hadoop.common.UserInformationProto userInfo = 2;
inline bool IpcConnectionContextProto::has_userinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void IpcConnectionContextProto::set_has_userinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void IpcConnectionContextProto::clear_has_userinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void IpcConnectionContextProto::clear_userinfo() {
  if (userinfo_ != NULL) userinfo_->::hadoop::common::UserInformationProto::Clear();
  clear_has_userinfo();
}
inline const ::hadoop::common::UserInformationProto& IpcConnectionContextProto::userinfo() const {
  return userinfo_ != NULL ? *userinfo_ : *default_instance_->userinfo_;
}
inline ::hadoop::common::UserInformationProto* IpcConnectionContextProto::mutable_userinfo() {
  set_has_userinfo();
  if (userinfo_ == NULL) userinfo_ = new ::hadoop::common::UserInformationProto;
  return userinfo_;
}
inline ::hadoop::common::UserInformationProto* IpcConnectionContextProto::release_userinfo() {
  clear_has_userinfo();
  ::hadoop::common::UserInformationProto* temp = userinfo_;
  userinfo_ = NULL;
  return temp;
}
inline void IpcConnectionContextProto::set_allocated_userinfo(::hadoop::common::UserInformationProto* userinfo) {
  delete userinfo_;
  userinfo_ = userinfo;
  if (userinfo) {
    set_has_userinfo();
  } else {
    clear_has_userinfo();
  }
}

// optional string protocol = 3;
inline bool IpcConnectionContextProto::has_protocol() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void IpcConnectionContextProto::set_has_protocol() {
  _has_bits_[0] |= 0x00000002u;
}
inline void IpcConnectionContextProto::clear_has_protocol() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void IpcConnectionContextProto::clear_protocol() {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    protocol_->clear();
  }
  clear_has_protocol();
}
inline const ::std::string& IpcConnectionContextProto::protocol() const {
  return *protocol_;
}
inline void IpcConnectionContextProto::set_protocol(const ::std::string& value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void IpcConnectionContextProto::set_protocol(const char* value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void IpcConnectionContextProto::set_protocol(const char* value, size_t size) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* IpcConnectionContextProto::mutable_protocol() {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  return protocol_;
}
inline ::std::string* IpcConnectionContextProto::release_protocol() {
  clear_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = protocol_;
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void IpcConnectionContextProto::set_allocated_protocol(::std::string* protocol) {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (protocol) {
    set_has_protocol();
    protocol_ = protocol;
  } else {
    clear_has_protocol();
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fIpcConnectionContext_2eproto__INCLUDED
