// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/TraceAdmin.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/TraceAdmin.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* ListSpanReceiversRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListSpanReceiversRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* SpanReceiverListInfo_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SpanReceiverListInfo_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListSpanReceiversResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListSpanReceiversResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ConfigPair_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ConfigPair_reflection_ = NULL;
const ::google::protobuf::Descriptor* AddSpanReceiverRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AddSpanReceiverRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* AddSpanReceiverResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AddSpanReceiverResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveSpanReceiverRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveSpanReceiverRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RemoveSpanReceiverResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RemoveSpanReceiverResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fTraceAdmin_2eproto() {
  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/TraceAdmin.proto");
  GOOGLE_CHECK(file != NULL);
  ListSpanReceiversRequestProto_descriptor_ = file->message_type(0);
  static const int ListSpanReceiversRequestProto_offsets_[1] = {
  };
  ListSpanReceiversRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ListSpanReceiversRequestProto_descriptor_,
      ListSpanReceiversRequestProto::default_instance_,
      ListSpanReceiversRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListSpanReceiversRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListSpanReceiversRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ListSpanReceiversRequestProto));
  SpanReceiverListInfo_descriptor_ = file->message_type(1);
  static const int SpanReceiverListInfo_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpanReceiverListInfo, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpanReceiverListInfo, classname_),
  };
  SpanReceiverListInfo_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SpanReceiverListInfo_descriptor_,
      SpanReceiverListInfo::default_instance_,
      SpanReceiverListInfo_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpanReceiverListInfo, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpanReceiverListInfo, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SpanReceiverListInfo));
  ListSpanReceiversResponseProto_descriptor_ = file->message_type(2);
  static const int ListSpanReceiversResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListSpanReceiversResponseProto, descriptions_),
  };
  ListSpanReceiversResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ListSpanReceiversResponseProto_descriptor_,
      ListSpanReceiversResponseProto::default_instance_,
      ListSpanReceiversResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListSpanReceiversResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListSpanReceiversResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ListSpanReceiversResponseProto));
  ConfigPair_descriptor_ = file->message_type(3);
  static const int ConfigPair_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ConfigPair, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ConfigPair, value_),
  };
  ConfigPair_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ConfigPair_descriptor_,
      ConfigPair::default_instance_,
      ConfigPair_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ConfigPair, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ConfigPair, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ConfigPair));
  AddSpanReceiverRequestProto_descriptor_ = file->message_type(4);
  static const int AddSpanReceiverRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverRequestProto, classname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverRequestProto, config_),
  };
  AddSpanReceiverRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AddSpanReceiverRequestProto_descriptor_,
      AddSpanReceiverRequestProto::default_instance_,
      AddSpanReceiverRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AddSpanReceiverRequestProto));
  AddSpanReceiverResponseProto_descriptor_ = file->message_type(5);
  static const int AddSpanReceiverResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverResponseProto, id_),
  };
  AddSpanReceiverResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AddSpanReceiverResponseProto_descriptor_,
      AddSpanReceiverResponseProto::default_instance_,
      AddSpanReceiverResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddSpanReceiverResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AddSpanReceiverResponseProto));
  RemoveSpanReceiverRequestProto_descriptor_ = file->message_type(6);
  static const int RemoveSpanReceiverRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveSpanReceiverRequestProto, id_),
  };
  RemoveSpanReceiverRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveSpanReceiverRequestProto_descriptor_,
      RemoveSpanReceiverRequestProto::default_instance_,
      RemoveSpanReceiverRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveSpanReceiverRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveSpanReceiverRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveSpanReceiverRequestProto));
  RemoveSpanReceiverResponseProto_descriptor_ = file->message_type(7);
  static const int RemoveSpanReceiverResponseProto_offsets_[1] = {
  };
  RemoveSpanReceiverResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RemoveSpanReceiverResponseProto_descriptor_,
      RemoveSpanReceiverResponseProto::default_instance_,
      RemoveSpanReceiverResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveSpanReceiverResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RemoveSpanReceiverResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RemoveSpanReceiverResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fTraceAdmin_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ListSpanReceiversRequestProto_descriptor_, &ListSpanReceiversRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SpanReceiverListInfo_descriptor_, &SpanReceiverListInfo::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ListSpanReceiversResponseProto_descriptor_, &ListSpanReceiversResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ConfigPair_descriptor_, &ConfigPair::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AddSpanReceiverRequestProto_descriptor_, &AddSpanReceiverRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AddSpanReceiverResponseProto_descriptor_, &AddSpanReceiverResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveSpanReceiverRequestProto_descriptor_, &RemoveSpanReceiverRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RemoveSpanReceiverResponseProto_descriptor_, &RemoveSpanReceiverResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto() {
  delete ListSpanReceiversRequestProto::default_instance_;
  delete ListSpanReceiversRequestProto_reflection_;
  delete SpanReceiverListInfo::default_instance_;
  delete SpanReceiverListInfo_reflection_;
  delete ListSpanReceiversResponseProto::default_instance_;
  delete ListSpanReceiversResponseProto_reflection_;
  delete ConfigPair::default_instance_;
  delete ConfigPair_reflection_;
  delete AddSpanReceiverRequestProto::default_instance_;
  delete AddSpanReceiverRequestProto_reflection_;
  delete AddSpanReceiverResponseProto::default_instance_;
  delete AddSpanReceiverResponseProto_reflection_;
  delete RemoveSpanReceiverRequestProto::default_instance_;
  delete RemoveSpanReceiverRequestProto_reflection_;
  delete RemoveSpanReceiverResponseProto::default_instance_;
  delete RemoveSpanReceiverResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fTraceAdmin_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\027common/TraceAdmin.proto\022\rhadoop.common"
    "\"\037\n\035ListSpanReceiversRequestProto\"5\n\024Spa"
    "nReceiverListInfo\022\n\n\002id\030\001 \002(\003\022\021\n\tclassNa"
    "me\030\002 \002(\t\"[\n\036ListSpanReceiversResponsePro"
    "to\0229\n\014descriptions\030\001 \003(\0132#.hadoop.common"
    ".SpanReceiverListInfo\"(\n\nConfigPair\022\013\n\003k"
    "ey\030\001 \002(\t\022\r\n\005value\030\002 \002(\t\"[\n\033AddSpanReceiv"
    "erRequestProto\022\021\n\tclassName\030\001 \002(\t\022)\n\006con"
    "fig\030\002 \003(\0132\031.hadoop.common.ConfigPair\"*\n\034"
    "AddSpanReceiverResponseProto\022\n\n\002id\030\001 \002(\003"
    "\",\n\036RemoveSpanReceiverRequestProto\022\n\n\002id"
    "\030\001 \002(\003\"!\n\037RemoveSpanReceiverResponseProt"
    "o2\346\002\n\021TraceAdminService\022p\n\021listSpanRecei"
    "vers\022,.hadoop.common.ListSpanReceiversRe"
    "questProto\032-.hadoop.common.ListSpanRecei"
    "versResponseProto\022j\n\017addSpanReceiver\022*.h"
    "adoop.common.AddSpanReceiverRequestProto"
    "\032+.hadoop.common.AddSpanReceiverResponse"
    "Proto\022s\n\022removeSpanReceiver\022-.hadoop.com"
    "mon.RemoveSpanReceiverRequestProto\032..had"
    "oop.common.RemoveSpanReceiverResponsePro"
    "toB/\n\031org.apache.hadoop.tracingB\014TraceAd"
    "minPB\210\001\001\240\001\001", 891);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/TraceAdmin.proto", &protobuf_RegisterTypes);
  ListSpanReceiversRequestProto::default_instance_ = new ListSpanReceiversRequestProto();
  SpanReceiverListInfo::default_instance_ = new SpanReceiverListInfo();
  ListSpanReceiversResponseProto::default_instance_ = new ListSpanReceiversResponseProto();
  ConfigPair::default_instance_ = new ConfigPair();
  AddSpanReceiverRequestProto::default_instance_ = new AddSpanReceiverRequestProto();
  AddSpanReceiverResponseProto::default_instance_ = new AddSpanReceiverResponseProto();
  RemoveSpanReceiverRequestProto::default_instance_ = new RemoveSpanReceiverRequestProto();
  RemoveSpanReceiverResponseProto::default_instance_ = new RemoveSpanReceiverResponseProto();
  ListSpanReceiversRequestProto::default_instance_->InitAsDefaultInstance();
  SpanReceiverListInfo::default_instance_->InitAsDefaultInstance();
  ListSpanReceiversResponseProto::default_instance_->InitAsDefaultInstance();
  ConfigPair::default_instance_->InitAsDefaultInstance();
  AddSpanReceiverRequestProto::default_instance_->InitAsDefaultInstance();
  AddSpanReceiverResponseProto::default_instance_->InitAsDefaultInstance();
  RemoveSpanReceiverRequestProto::default_instance_->InitAsDefaultInstance();
  RemoveSpanReceiverResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fTraceAdmin_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fTraceAdmin_2eproto {
  StaticDescriptorInitializer_common_2fTraceAdmin_2eproto() {
    protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  }
} static_descriptor_initializer_common_2fTraceAdmin_2eproto_;

// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

ListSpanReceiversRequestProto::ListSpanReceiversRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ListSpanReceiversRequestProto::InitAsDefaultInstance() {
}

ListSpanReceiversRequestProto::ListSpanReceiversRequestProto(const ListSpanReceiversRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ListSpanReceiversRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ListSpanReceiversRequestProto::~ListSpanReceiversRequestProto() {
  SharedDtor();
}

void ListSpanReceiversRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ListSpanReceiversRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListSpanReceiversRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListSpanReceiversRequestProto_descriptor_;
}

const ListSpanReceiversRequestProto& ListSpanReceiversRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

ListSpanReceiversRequestProto* ListSpanReceiversRequestProto::default_instance_ = NULL;

ListSpanReceiversRequestProto* ListSpanReceiversRequestProto::New() const {
  return new ListSpanReceiversRequestProto;
}

void ListSpanReceiversRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ListSpanReceiversRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void ListSpanReceiversRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ListSpanReceiversRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ListSpanReceiversRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListSpanReceiversRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ListSpanReceiversRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ListSpanReceiversRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ListSpanReceiversRequestProto::MergeFrom(const ListSpanReceiversRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ListSpanReceiversRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListSpanReceiversRequestProto::CopyFrom(const ListSpanReceiversRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListSpanReceiversRequestProto::IsInitialized() const {

  return true;
}

void ListSpanReceiversRequestProto::Swap(ListSpanReceiversRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ListSpanReceiversRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListSpanReceiversRequestProto_descriptor_;
  metadata.reflection = ListSpanReceiversRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SpanReceiverListInfo::kIdFieldNumber;
const int SpanReceiverListInfo::kClassNameFieldNumber;
#endif  // !_MSC_VER

SpanReceiverListInfo::SpanReceiverListInfo()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SpanReceiverListInfo::InitAsDefaultInstance() {
}

SpanReceiverListInfo::SpanReceiverListInfo(const SpanReceiverListInfo& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SpanReceiverListInfo::SharedCtor() {
  _cached_size_ = 0;
  id_ = GOOGLE_LONGLONG(0);
  classname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SpanReceiverListInfo::~SpanReceiverListInfo() {
  SharedDtor();
}

void SpanReceiverListInfo::SharedDtor() {
  if (classname_ != &::google::protobuf::internal::kEmptyString) {
    delete classname_;
  }
  if (this != default_instance_) {
  }
}

void SpanReceiverListInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SpanReceiverListInfo::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SpanReceiverListInfo_descriptor_;
}

const SpanReceiverListInfo& SpanReceiverListInfo::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

SpanReceiverListInfo* SpanReceiverListInfo::default_instance_ = NULL;

SpanReceiverListInfo* SpanReceiverListInfo::New() const {
  return new SpanReceiverListInfo;
}

void SpanReceiverListInfo::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = GOOGLE_LONGLONG(0);
    if (has_classname()) {
      if (classname_ != &::google::protobuf::internal::kEmptyString) {
        classname_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SpanReceiverListInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_className;
        break;
      }

      // required string className = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_className:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_classname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->classname().data(), this->classname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SpanReceiverListInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->id(), output);
  }

  // required string className = 2;
  if (has_classname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->classname().data(), this->classname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->classname(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SpanReceiverListInfo::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->id(), target);
  }

  // required string className = 2;
  if (has_classname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->classname().data(), this->classname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->classname(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SpanReceiverListInfo::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->id());
    }

    // required string className = 2;
    if (has_classname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->classname());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SpanReceiverListInfo::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SpanReceiverListInfo* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SpanReceiverListInfo*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SpanReceiverListInfo::MergeFrom(const SpanReceiverListInfo& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
    if (from.has_classname()) {
      set_classname(from.classname());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SpanReceiverListInfo::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SpanReceiverListInfo::CopyFrom(const SpanReceiverListInfo& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpanReceiverListInfo::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void SpanReceiverListInfo::Swap(SpanReceiverListInfo* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(classname_, other->classname_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SpanReceiverListInfo::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SpanReceiverListInfo_descriptor_;
  metadata.reflection = SpanReceiverListInfo_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ListSpanReceiversResponseProto::kDescriptionsFieldNumber;
#endif  // !_MSC_VER

ListSpanReceiversResponseProto::ListSpanReceiversResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ListSpanReceiversResponseProto::InitAsDefaultInstance() {
}

ListSpanReceiversResponseProto::ListSpanReceiversResponseProto(const ListSpanReceiversResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ListSpanReceiversResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ListSpanReceiversResponseProto::~ListSpanReceiversResponseProto() {
  SharedDtor();
}

void ListSpanReceiversResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ListSpanReceiversResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListSpanReceiversResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListSpanReceiversResponseProto_descriptor_;
}

const ListSpanReceiversResponseProto& ListSpanReceiversResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

ListSpanReceiversResponseProto* ListSpanReceiversResponseProto::default_instance_ = NULL;

ListSpanReceiversResponseProto* ListSpanReceiversResponseProto::New() const {
  return new ListSpanReceiversResponseProto;
}

void ListSpanReceiversResponseProto::Clear() {
  descriptions_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ListSpanReceiversResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.common.SpanReceiverListInfo descriptions = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_descriptions:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_descriptions()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_descriptions;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ListSpanReceiversResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.common.SpanReceiverListInfo descriptions = 1;
  for (int i = 0; i < this->descriptions_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->descriptions(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ListSpanReceiversResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.common.SpanReceiverListInfo descriptions = 1;
  for (int i = 0; i < this->descriptions_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->descriptions(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ListSpanReceiversResponseProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.common.SpanReceiverListInfo descriptions = 1;
  total_size += 1 * this->descriptions_size();
  for (int i = 0; i < this->descriptions_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->descriptions(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListSpanReceiversResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ListSpanReceiversResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ListSpanReceiversResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ListSpanReceiversResponseProto::MergeFrom(const ListSpanReceiversResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  descriptions_.MergeFrom(from.descriptions_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ListSpanReceiversResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListSpanReceiversResponseProto::CopyFrom(const ListSpanReceiversResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListSpanReceiversResponseProto::IsInitialized() const {

  for (int i = 0; i < descriptions_size(); i++) {
    if (!this->descriptions(i).IsInitialized()) return false;
  }
  return true;
}

void ListSpanReceiversResponseProto::Swap(ListSpanReceiversResponseProto* other) {
  if (other != this) {
    descriptions_.Swap(&other->descriptions_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ListSpanReceiversResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListSpanReceiversResponseProto_descriptor_;
  metadata.reflection = ListSpanReceiversResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ConfigPair::kKeyFieldNumber;
const int ConfigPair::kValueFieldNumber;
#endif  // !_MSC_VER

ConfigPair::ConfigPair()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ConfigPair::InitAsDefaultInstance() {
}

ConfigPair::ConfigPair(const ConfigPair& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ConfigPair::SharedCtor() {
  _cached_size_ = 0;
  key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ConfigPair::~ConfigPair() {
  SharedDtor();
}

void ConfigPair::SharedDtor() {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    delete key_;
  }
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    delete value_;
  }
  if (this != default_instance_) {
  }
}

void ConfigPair::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ConfigPair::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ConfigPair_descriptor_;
}

const ConfigPair& ConfigPair::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

ConfigPair* ConfigPair::default_instance_ = NULL;

ConfigPair* ConfigPair::New() const {
  return new ConfigPair;
}

void ConfigPair::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_key()) {
      if (key_ != &::google::protobuf::internal::kEmptyString) {
        key_->clear();
      }
    }
    if (has_value()) {
      if (value_ != &::google::protobuf::internal::kEmptyString) {
        value_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ConfigPair::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string key = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->key().data(), this->key().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // required string value = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ConfigPair::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string key = 1;
  if (has_key()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->key(), output);
  }

  // required string value = 2;
  if (has_value()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->value(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ConfigPair::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string key = 1;
  if (has_key()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // required string value = 2;
  if (has_value()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->value(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ConfigPair::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string key = 1;
    if (has_key()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->key());
    }

    // required string value = 2;
    if (has_value()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->value());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ConfigPair::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ConfigPair* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ConfigPair*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ConfigPair::MergeFrom(const ConfigPair& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_key()) {
      set_key(from.key());
    }
    if (from.has_value()) {
      set_value(from.value());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ConfigPair::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConfigPair::CopyFrom(const ConfigPair& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigPair::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void ConfigPair::Swap(ConfigPair* other) {
  if (other != this) {
    std::swap(key_, other->key_);
    std::swap(value_, other->value_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ConfigPair::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ConfigPair_descriptor_;
  metadata.reflection = ConfigPair_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int AddSpanReceiverRequestProto::kClassNameFieldNumber;
const int AddSpanReceiverRequestProto::kConfigFieldNumber;
#endif  // !_MSC_VER

AddSpanReceiverRequestProto::AddSpanReceiverRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AddSpanReceiverRequestProto::InitAsDefaultInstance() {
}

AddSpanReceiverRequestProto::AddSpanReceiverRequestProto(const AddSpanReceiverRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AddSpanReceiverRequestProto::SharedCtor() {
  _cached_size_ = 0;
  classname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AddSpanReceiverRequestProto::~AddSpanReceiverRequestProto() {
  SharedDtor();
}

void AddSpanReceiverRequestProto::SharedDtor() {
  if (classname_ != &::google::protobuf::internal::kEmptyString) {
    delete classname_;
  }
  if (this != default_instance_) {
  }
}

void AddSpanReceiverRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AddSpanReceiverRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AddSpanReceiverRequestProto_descriptor_;
}

const AddSpanReceiverRequestProto& AddSpanReceiverRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

AddSpanReceiverRequestProto* AddSpanReceiverRequestProto::default_instance_ = NULL;

AddSpanReceiverRequestProto* AddSpanReceiverRequestProto::New() const {
  return new AddSpanReceiverRequestProto;
}

void AddSpanReceiverRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_classname()) {
      if (classname_ != &::google::protobuf::internal::kEmptyString) {
        classname_->clear();
      }
    }
  }
  config_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AddSpanReceiverRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string className = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_classname()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->classname().data(), this->classname().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_config;
        break;
      }

      // repeated .hadoop.common.ConfigPair config = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_config:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_config()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_config;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AddSpanReceiverRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string className = 1;
  if (has_classname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->classname().data(), this->classname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->classname(), output);
  }

  // repeated .hadoop.common.ConfigPair config = 2;
  for (int i = 0; i < this->config_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->config(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AddSpanReceiverRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string className = 1;
  if (has_classname()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->classname().data(), this->classname().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->classname(), target);
  }

  // repeated .hadoop.common.ConfigPair config = 2;
  for (int i = 0; i < this->config_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->config(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AddSpanReceiverRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string className = 1;
    if (has_classname()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->classname());
    }

  }
  // repeated .hadoop.common.ConfigPair config = 2;
  total_size += 1 * this->config_size();
  for (int i = 0; i < this->config_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->config(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AddSpanReceiverRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AddSpanReceiverRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AddSpanReceiverRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AddSpanReceiverRequestProto::MergeFrom(const AddSpanReceiverRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  config_.MergeFrom(from.config_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_classname()) {
      set_classname(from.classname());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AddSpanReceiverRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AddSpanReceiverRequestProto::CopyFrom(const AddSpanReceiverRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddSpanReceiverRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  for (int i = 0; i < config_size(); i++) {
    if (!this->config(i).IsInitialized()) return false;
  }
  return true;
}

void AddSpanReceiverRequestProto::Swap(AddSpanReceiverRequestProto* other) {
  if (other != this) {
    std::swap(classname_, other->classname_);
    config_.Swap(&other->config_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AddSpanReceiverRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AddSpanReceiverRequestProto_descriptor_;
  metadata.reflection = AddSpanReceiverRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int AddSpanReceiverResponseProto::kIdFieldNumber;
#endif  // !_MSC_VER

AddSpanReceiverResponseProto::AddSpanReceiverResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AddSpanReceiverResponseProto::InitAsDefaultInstance() {
}

AddSpanReceiverResponseProto::AddSpanReceiverResponseProto(const AddSpanReceiverResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AddSpanReceiverResponseProto::SharedCtor() {
  _cached_size_ = 0;
  id_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AddSpanReceiverResponseProto::~AddSpanReceiverResponseProto() {
  SharedDtor();
}

void AddSpanReceiverResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void AddSpanReceiverResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AddSpanReceiverResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AddSpanReceiverResponseProto_descriptor_;
}

const AddSpanReceiverResponseProto& AddSpanReceiverResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

AddSpanReceiverResponseProto* AddSpanReceiverResponseProto::default_instance_ = NULL;

AddSpanReceiverResponseProto* AddSpanReceiverResponseProto::New() const {
  return new AddSpanReceiverResponseProto;
}

void AddSpanReceiverResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AddSpanReceiverResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AddSpanReceiverResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->id(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AddSpanReceiverResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->id(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AddSpanReceiverResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->id());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AddSpanReceiverResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AddSpanReceiverResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AddSpanReceiverResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AddSpanReceiverResponseProto::MergeFrom(const AddSpanReceiverResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AddSpanReceiverResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AddSpanReceiverResponseProto::CopyFrom(const AddSpanReceiverResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddSpanReceiverResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void AddSpanReceiverResponseProto::Swap(AddSpanReceiverResponseProto* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AddSpanReceiverResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AddSpanReceiverResponseProto_descriptor_;
  metadata.reflection = AddSpanReceiverResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RemoveSpanReceiverRequestProto::kIdFieldNumber;
#endif  // !_MSC_VER

RemoveSpanReceiverRequestProto::RemoveSpanReceiverRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveSpanReceiverRequestProto::InitAsDefaultInstance() {
}

RemoveSpanReceiverRequestProto::RemoveSpanReceiverRequestProto(const RemoveSpanReceiverRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveSpanReceiverRequestProto::SharedCtor() {
  _cached_size_ = 0;
  id_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveSpanReceiverRequestProto::~RemoveSpanReceiverRequestProto() {
  SharedDtor();
}

void RemoveSpanReceiverRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RemoveSpanReceiverRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveSpanReceiverRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveSpanReceiverRequestProto_descriptor_;
}

const RemoveSpanReceiverRequestProto& RemoveSpanReceiverRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

RemoveSpanReceiverRequestProto* RemoveSpanReceiverRequestProto::default_instance_ = NULL;

RemoveSpanReceiverRequestProto* RemoveSpanReceiverRequestProto::New() const {
  return new RemoveSpanReceiverRequestProto;
}

void RemoveSpanReceiverRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    id_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveSpanReceiverRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 id = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
          set_has_id();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RemoveSpanReceiverRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->id(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveSpanReceiverRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 id = 1;
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->id(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveSpanReceiverRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->id());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveSpanReceiverRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveSpanReceiverRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveSpanReceiverRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveSpanReceiverRequestProto::MergeFrom(const RemoveSpanReceiverRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveSpanReceiverRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveSpanReceiverRequestProto::CopyFrom(const RemoveSpanReceiverRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveSpanReceiverRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void RemoveSpanReceiverRequestProto::Swap(RemoveSpanReceiverRequestProto* other) {
  if (other != this) {
    std::swap(id_, other->id_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveSpanReceiverRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveSpanReceiverRequestProto_descriptor_;
  metadata.reflection = RemoveSpanReceiverRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RemoveSpanReceiverResponseProto::RemoveSpanReceiverResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RemoveSpanReceiverResponseProto::InitAsDefaultInstance() {
}

RemoveSpanReceiverResponseProto::RemoveSpanReceiverResponseProto(const RemoveSpanReceiverResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RemoveSpanReceiverResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RemoveSpanReceiverResponseProto::~RemoveSpanReceiverResponseProto() {
  SharedDtor();
}

void RemoveSpanReceiverResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RemoveSpanReceiverResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RemoveSpanReceiverResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RemoveSpanReceiverResponseProto_descriptor_;
}

const RemoveSpanReceiverResponseProto& RemoveSpanReceiverResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  return *default_instance_;
}

RemoveSpanReceiverResponseProto* RemoveSpanReceiverResponseProto::default_instance_ = NULL;

RemoveSpanReceiverResponseProto* RemoveSpanReceiverResponseProto::New() const {
  return new RemoveSpanReceiverResponseProto;
}

void RemoveSpanReceiverResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RemoveSpanReceiverResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RemoveSpanReceiverResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RemoveSpanReceiverResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RemoveSpanReceiverResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RemoveSpanReceiverResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RemoveSpanReceiverResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RemoveSpanReceiverResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RemoveSpanReceiverResponseProto::MergeFrom(const RemoveSpanReceiverResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RemoveSpanReceiverResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoveSpanReceiverResponseProto::CopyFrom(const RemoveSpanReceiverResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveSpanReceiverResponseProto::IsInitialized() const {

  return true;
}

void RemoveSpanReceiverResponseProto::Swap(RemoveSpanReceiverResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RemoveSpanReceiverResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RemoveSpanReceiverResponseProto_descriptor_;
  metadata.reflection = RemoveSpanReceiverResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
