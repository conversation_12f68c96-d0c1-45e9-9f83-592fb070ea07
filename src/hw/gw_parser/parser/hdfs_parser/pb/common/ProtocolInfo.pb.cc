// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/ProtocolInfo.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/ProtocolInfo.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* GetProtocolVersionsRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetProtocolVersionsRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ProtocolVersionProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ProtocolVersionProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetProtocolVersionsResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetProtocolVersionsResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetProtocolSignatureRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetProtocolSignatureRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetProtocolSignatureResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetProtocolSignatureResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ProtocolSignatureProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ProtocolSignatureProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fProtocolInfo_2eproto() {
  protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/ProtocolInfo.proto");
  GOOGLE_CHECK(file != NULL);
  GetProtocolVersionsRequestProto_descriptor_ = file->message_type(0);
  static const int GetProtocolVersionsRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolVersionsRequestProto, protocol_),
  };
  GetProtocolVersionsRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetProtocolVersionsRequestProto_descriptor_,
      GetProtocolVersionsRequestProto::default_instance_,
      GetProtocolVersionsRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolVersionsRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolVersionsRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetProtocolVersionsRequestProto));
  ProtocolVersionProto_descriptor_ = file->message_type(1);
  static const int ProtocolVersionProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolVersionProto, rpckind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolVersionProto, versions_),
  };
  ProtocolVersionProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ProtocolVersionProto_descriptor_,
      ProtocolVersionProto::default_instance_,
      ProtocolVersionProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolVersionProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolVersionProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ProtocolVersionProto));
  GetProtocolVersionsResponseProto_descriptor_ = file->message_type(2);
  static const int GetProtocolVersionsResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolVersionsResponseProto, protocolversions_),
  };
  GetProtocolVersionsResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetProtocolVersionsResponseProto_descriptor_,
      GetProtocolVersionsResponseProto::default_instance_,
      GetProtocolVersionsResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolVersionsResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolVersionsResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetProtocolVersionsResponseProto));
  GetProtocolSignatureRequestProto_descriptor_ = file->message_type(3);
  static const int GetProtocolSignatureRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureRequestProto, protocol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureRequestProto, rpckind_),
  };
  GetProtocolSignatureRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetProtocolSignatureRequestProto_descriptor_,
      GetProtocolSignatureRequestProto::default_instance_,
      GetProtocolSignatureRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetProtocolSignatureRequestProto));
  GetProtocolSignatureResponseProto_descriptor_ = file->message_type(4);
  static const int GetProtocolSignatureResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureResponseProto, protocolsignature_),
  };
  GetProtocolSignatureResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetProtocolSignatureResponseProto_descriptor_,
      GetProtocolSignatureResponseProto::default_instance_,
      GetProtocolSignatureResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetProtocolSignatureResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetProtocolSignatureResponseProto));
  ProtocolSignatureProto_descriptor_ = file->message_type(5);
  static const int ProtocolSignatureProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolSignatureProto, version_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolSignatureProto, methods_),
  };
  ProtocolSignatureProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ProtocolSignatureProto_descriptor_,
      ProtocolSignatureProto::default_instance_,
      ProtocolSignatureProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolSignatureProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ProtocolSignatureProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ProtocolSignatureProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fProtocolInfo_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetProtocolVersionsRequestProto_descriptor_, &GetProtocolVersionsRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ProtocolVersionProto_descriptor_, &ProtocolVersionProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetProtocolVersionsResponseProto_descriptor_, &GetProtocolVersionsResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetProtocolSignatureRequestProto_descriptor_, &GetProtocolSignatureRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetProtocolSignatureResponseProto_descriptor_, &GetProtocolSignatureResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ProtocolSignatureProto_descriptor_, &ProtocolSignatureProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fProtocolInfo_2eproto() {
  delete GetProtocolVersionsRequestProto::default_instance_;
  delete GetProtocolVersionsRequestProto_reflection_;
  delete ProtocolVersionProto::default_instance_;
  delete ProtocolVersionProto_reflection_;
  delete GetProtocolVersionsResponseProto::default_instance_;
  delete GetProtocolVersionsResponseProto_reflection_;
  delete GetProtocolSignatureRequestProto::default_instance_;
  delete GetProtocolSignatureRequestProto_reflection_;
  delete GetProtocolSignatureResponseProto::default_instance_;
  delete GetProtocolSignatureResponseProto_reflection_;
  delete ProtocolSignatureProto::default_instance_;
  delete ProtocolSignatureProto_reflection_;
}

void protobuf_AddDesc_common_2fProtocolInfo_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\031common/ProtocolInfo.proto\022\rhadoop.comm"
    "on\"3\n\037GetProtocolVersionsRequestProto\022\020\n"
    "\010protocol\030\001 \002(\t\"9\n\024ProtocolVersionProto\022"
    "\017\n\007rpcKind\030\001 \002(\t\022\020\n\010versions\030\002 \003(\004\"a\n Ge"
    "tProtocolVersionsResponseProto\022=\n\020protoc"
    "olVersions\030\001 \003(\0132#.hadoop.common.Protoco"
    "lVersionProto\"E\n GetProtocolSignatureReq"
    "uestProto\022\020\n\010protocol\030\001 \002(\t\022\017\n\007rpcKind\030\002"
    " \002(\t\"e\n!GetProtocolSignatureResponseProt"
    "o\022@\n\021protocolSignature\030\001 \003(\0132%.hadoop.co"
    "mmon.ProtocolSignatureProto\":\n\026ProtocolS"
    "ignatureProto\022\017\n\007version\030\001 \002(\004\022\017\n\007method"
    "s\030\002 \003(\r2\210\002\n\023ProtocolInfoService\022v\n\023getPr"
    "otocolVersions\022..hadoop.common.GetProtoc"
    "olVersionsRequestProto\032/.hadoop.common.G"
    "etProtocolVersionsResponseProto\022y\n\024getPr"
    "otocolSignature\022/.hadoop.common.GetProto"
    "colSignatureRequestProto\0320.hadoop.common"
    ".GetProtocolSignatureResponseProtoB:\n\036or"
    "g.apache.hadoop.ipc.protobufB\022ProtocolIn"
    "foProtos\210\001\001\240\001\001", 814);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/ProtocolInfo.proto", &protobuf_RegisterTypes);
  GetProtocolVersionsRequestProto::default_instance_ = new GetProtocolVersionsRequestProto();
  ProtocolVersionProto::default_instance_ = new ProtocolVersionProto();
  GetProtocolVersionsResponseProto::default_instance_ = new GetProtocolVersionsResponseProto();
  GetProtocolSignatureRequestProto::default_instance_ = new GetProtocolSignatureRequestProto();
  GetProtocolSignatureResponseProto::default_instance_ = new GetProtocolSignatureResponseProto();
  ProtocolSignatureProto::default_instance_ = new ProtocolSignatureProto();
  GetProtocolVersionsRequestProto::default_instance_->InitAsDefaultInstance();
  ProtocolVersionProto::default_instance_->InitAsDefaultInstance();
  GetProtocolVersionsResponseProto::default_instance_->InitAsDefaultInstance();
  GetProtocolSignatureRequestProto::default_instance_->InitAsDefaultInstance();
  GetProtocolSignatureResponseProto::default_instance_->InitAsDefaultInstance();
  ProtocolSignatureProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fProtocolInfo_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fProtocolInfo_2eproto {
  StaticDescriptorInitializer_common_2fProtocolInfo_2eproto() {
    protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  }
} static_descriptor_initializer_common_2fProtocolInfo_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int GetProtocolVersionsRequestProto::kProtocolFieldNumber;
#endif  // !_MSC_VER

GetProtocolVersionsRequestProto::GetProtocolVersionsRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetProtocolVersionsRequestProto::InitAsDefaultInstance() {
}

GetProtocolVersionsRequestProto::GetProtocolVersionsRequestProto(const GetProtocolVersionsRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetProtocolVersionsRequestProto::SharedCtor() {
  _cached_size_ = 0;
  protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetProtocolVersionsRequestProto::~GetProtocolVersionsRequestProto() {
  SharedDtor();
}

void GetProtocolVersionsRequestProto::SharedDtor() {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (this != default_instance_) {
  }
}

void GetProtocolVersionsRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetProtocolVersionsRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetProtocolVersionsRequestProto_descriptor_;
}

const GetProtocolVersionsRequestProto& GetProtocolVersionsRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  return *default_instance_;
}

GetProtocolVersionsRequestProto* GetProtocolVersionsRequestProto::default_instance_ = NULL;

GetProtocolVersionsRequestProto* GetProtocolVersionsRequestProto::New() const {
  return new GetProtocolVersionsRequestProto;
}

void GetProtocolVersionsRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_protocol()) {
      if (protocol_ != &::google::protobuf::internal::kEmptyString) {
        protocol_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetProtocolVersionsRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string protocol = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_protocol()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->protocol().data(), this->protocol().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetProtocolVersionsRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string protocol = 1;
  if (has_protocol()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->protocol().data(), this->protocol().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->protocol(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetProtocolVersionsRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string protocol = 1;
  if (has_protocol()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->protocol().data(), this->protocol().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->protocol(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetProtocolVersionsRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string protocol = 1;
    if (has_protocol()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->protocol());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetProtocolVersionsRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetProtocolVersionsRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetProtocolVersionsRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetProtocolVersionsRequestProto::MergeFrom(const GetProtocolVersionsRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_protocol()) {
      set_protocol(from.protocol());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetProtocolVersionsRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetProtocolVersionsRequestProto::CopyFrom(const GetProtocolVersionsRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProtocolVersionsRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetProtocolVersionsRequestProto::Swap(GetProtocolVersionsRequestProto* other) {
  if (other != this) {
    std::swap(protocol_, other->protocol_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetProtocolVersionsRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetProtocolVersionsRequestProto_descriptor_;
  metadata.reflection = GetProtocolVersionsRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ProtocolVersionProto::kRpcKindFieldNumber;
const int ProtocolVersionProto::kVersionsFieldNumber;
#endif  // !_MSC_VER

ProtocolVersionProto::ProtocolVersionProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ProtocolVersionProto::InitAsDefaultInstance() {
}

ProtocolVersionProto::ProtocolVersionProto(const ProtocolVersionProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ProtocolVersionProto::SharedCtor() {
  _cached_size_ = 0;
  rpckind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ProtocolVersionProto::~ProtocolVersionProto() {
  SharedDtor();
}

void ProtocolVersionProto::SharedDtor() {
  if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
    delete rpckind_;
  }
  if (this != default_instance_) {
  }
}

void ProtocolVersionProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ProtocolVersionProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ProtocolVersionProto_descriptor_;
}

const ProtocolVersionProto& ProtocolVersionProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  return *default_instance_;
}

ProtocolVersionProto* ProtocolVersionProto::default_instance_ = NULL;

ProtocolVersionProto* ProtocolVersionProto::New() const {
  return new ProtocolVersionProto;
}

void ProtocolVersionProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_rpckind()) {
      if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
        rpckind_->clear();
      }
    }
  }
  versions_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ProtocolVersionProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string rpcKind = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rpckind()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->rpckind().data(), this->rpckind().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_versions;
        break;
      }

      // repeated uint64 versions = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_versions:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 16, input, this->mutable_versions())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_LENGTH_DELIMITED) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_versions())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_versions;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ProtocolVersionProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string rpcKind = 1;
  if (has_rpckind()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->rpckind().data(), this->rpckind().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->rpckind(), output);
  }

  // repeated uint64 versions = 2;
  for (int i = 0; i < this->versions_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(
      2, this->versions(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ProtocolVersionProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string rpcKind = 1;
  if (has_rpckind()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->rpckind().data(), this->rpckind().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->rpckind(), target);
  }

  // repeated uint64 versions = 2;
  for (int i = 0; i < this->versions_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64ToArray(2, this->versions(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ProtocolVersionProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string rpcKind = 1;
    if (has_rpckind()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->rpckind());
    }

  }
  // repeated uint64 versions = 2;
  {
    int data_size = 0;
    for (int i = 0; i < this->versions_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->versions(i));
    }
    total_size += 1 * this->versions_size() + data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ProtocolVersionProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ProtocolVersionProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ProtocolVersionProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ProtocolVersionProto::MergeFrom(const ProtocolVersionProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  versions_.MergeFrom(from.versions_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_rpckind()) {
      set_rpckind(from.rpckind());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ProtocolVersionProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ProtocolVersionProto::CopyFrom(const ProtocolVersionProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProtocolVersionProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void ProtocolVersionProto::Swap(ProtocolVersionProto* other) {
  if (other != this) {
    std::swap(rpckind_, other->rpckind_);
    versions_.Swap(&other->versions_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ProtocolVersionProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ProtocolVersionProto_descriptor_;
  metadata.reflection = ProtocolVersionProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetProtocolVersionsResponseProto::kProtocolVersionsFieldNumber;
#endif  // !_MSC_VER

GetProtocolVersionsResponseProto::GetProtocolVersionsResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetProtocolVersionsResponseProto::InitAsDefaultInstance() {
}

GetProtocolVersionsResponseProto::GetProtocolVersionsResponseProto(const GetProtocolVersionsResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetProtocolVersionsResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetProtocolVersionsResponseProto::~GetProtocolVersionsResponseProto() {
  SharedDtor();
}

void GetProtocolVersionsResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetProtocolVersionsResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetProtocolVersionsResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetProtocolVersionsResponseProto_descriptor_;
}

const GetProtocolVersionsResponseProto& GetProtocolVersionsResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  return *default_instance_;
}

GetProtocolVersionsResponseProto* GetProtocolVersionsResponseProto::default_instance_ = NULL;

GetProtocolVersionsResponseProto* GetProtocolVersionsResponseProto::New() const {
  return new GetProtocolVersionsResponseProto;
}

void GetProtocolVersionsResponseProto::Clear() {
  protocolversions_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetProtocolVersionsResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.common.ProtocolVersionProto protocolVersions = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_protocolVersions:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_protocolversions()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_protocolVersions;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetProtocolVersionsResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.common.ProtocolVersionProto protocolVersions = 1;
  for (int i = 0; i < this->protocolversions_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->protocolversions(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetProtocolVersionsResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.common.ProtocolVersionProto protocolVersions = 1;
  for (int i = 0; i < this->protocolversions_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->protocolversions(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetProtocolVersionsResponseProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.common.ProtocolVersionProto protocolVersions = 1;
  total_size += 1 * this->protocolversions_size();
  for (int i = 0; i < this->protocolversions_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->protocolversions(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetProtocolVersionsResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetProtocolVersionsResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetProtocolVersionsResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetProtocolVersionsResponseProto::MergeFrom(const GetProtocolVersionsResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  protocolversions_.MergeFrom(from.protocolversions_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetProtocolVersionsResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetProtocolVersionsResponseProto::CopyFrom(const GetProtocolVersionsResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProtocolVersionsResponseProto::IsInitialized() const {

  for (int i = 0; i < protocolversions_size(); i++) {
    if (!this->protocolversions(i).IsInitialized()) return false;
  }
  return true;
}

void GetProtocolVersionsResponseProto::Swap(GetProtocolVersionsResponseProto* other) {
  if (other != this) {
    protocolversions_.Swap(&other->protocolversions_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetProtocolVersionsResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetProtocolVersionsResponseProto_descriptor_;
  metadata.reflection = GetProtocolVersionsResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetProtocolSignatureRequestProto::kProtocolFieldNumber;
const int GetProtocolSignatureRequestProto::kRpcKindFieldNumber;
#endif  // !_MSC_VER

GetProtocolSignatureRequestProto::GetProtocolSignatureRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetProtocolSignatureRequestProto::InitAsDefaultInstance() {
}

GetProtocolSignatureRequestProto::GetProtocolSignatureRequestProto(const GetProtocolSignatureRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetProtocolSignatureRequestProto::SharedCtor() {
  _cached_size_ = 0;
  protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  rpckind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetProtocolSignatureRequestProto::~GetProtocolSignatureRequestProto() {
  SharedDtor();
}

void GetProtocolSignatureRequestProto::SharedDtor() {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
    delete rpckind_;
  }
  if (this != default_instance_) {
  }
}

void GetProtocolSignatureRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetProtocolSignatureRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetProtocolSignatureRequestProto_descriptor_;
}

const GetProtocolSignatureRequestProto& GetProtocolSignatureRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  return *default_instance_;
}

GetProtocolSignatureRequestProto* GetProtocolSignatureRequestProto::default_instance_ = NULL;

GetProtocolSignatureRequestProto* GetProtocolSignatureRequestProto::New() const {
  return new GetProtocolSignatureRequestProto;
}

void GetProtocolSignatureRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_protocol()) {
      if (protocol_ != &::google::protobuf::internal::kEmptyString) {
        protocol_->clear();
      }
    }
    if (has_rpckind()) {
      if (rpckind_ != &::google::protobuf::internal::kEmptyString) {
        rpckind_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetProtocolSignatureRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string protocol = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_protocol()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->protocol().data(), this->protocol().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_rpcKind;
        break;
      }

      // required string rpcKind = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_rpcKind:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rpckind()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->rpckind().data(), this->rpckind().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetProtocolSignatureRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string protocol = 1;
  if (has_protocol()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->protocol().data(), this->protocol().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->protocol(), output);
  }

  // required string rpcKind = 2;
  if (has_rpckind()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->rpckind().data(), this->rpckind().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->rpckind(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetProtocolSignatureRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string protocol = 1;
  if (has_protocol()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->protocol().data(), this->protocol().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->protocol(), target);
  }

  // required string rpcKind = 2;
  if (has_rpckind()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->rpckind().data(), this->rpckind().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->rpckind(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetProtocolSignatureRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string protocol = 1;
    if (has_protocol()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->protocol());
    }

    // required string rpcKind = 2;
    if (has_rpckind()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->rpckind());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetProtocolSignatureRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetProtocolSignatureRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetProtocolSignatureRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetProtocolSignatureRequestProto::MergeFrom(const GetProtocolSignatureRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_protocol()) {
      set_protocol(from.protocol());
    }
    if (from.has_rpckind()) {
      set_rpckind(from.rpckind());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetProtocolSignatureRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetProtocolSignatureRequestProto::CopyFrom(const GetProtocolSignatureRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProtocolSignatureRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void GetProtocolSignatureRequestProto::Swap(GetProtocolSignatureRequestProto* other) {
  if (other != this) {
    std::swap(protocol_, other->protocol_);
    std::swap(rpckind_, other->rpckind_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetProtocolSignatureRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetProtocolSignatureRequestProto_descriptor_;
  metadata.reflection = GetProtocolSignatureRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetProtocolSignatureResponseProto::kProtocolSignatureFieldNumber;
#endif  // !_MSC_VER

GetProtocolSignatureResponseProto::GetProtocolSignatureResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetProtocolSignatureResponseProto::InitAsDefaultInstance() {
}

GetProtocolSignatureResponseProto::GetProtocolSignatureResponseProto(const GetProtocolSignatureResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetProtocolSignatureResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetProtocolSignatureResponseProto::~GetProtocolSignatureResponseProto() {
  SharedDtor();
}

void GetProtocolSignatureResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetProtocolSignatureResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetProtocolSignatureResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetProtocolSignatureResponseProto_descriptor_;
}

const GetProtocolSignatureResponseProto& GetProtocolSignatureResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  return *default_instance_;
}

GetProtocolSignatureResponseProto* GetProtocolSignatureResponseProto::default_instance_ = NULL;

GetProtocolSignatureResponseProto* GetProtocolSignatureResponseProto::New() const {
  return new GetProtocolSignatureResponseProto;
}

void GetProtocolSignatureResponseProto::Clear() {
  protocolsignature_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetProtocolSignatureResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.common.ProtocolSignatureProto protocolSignature = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_protocolSignature:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_protocolsignature()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_protocolSignature;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetProtocolSignatureResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.common.ProtocolSignatureProto protocolSignature = 1;
  for (int i = 0; i < this->protocolsignature_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->protocolsignature(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetProtocolSignatureResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.common.ProtocolSignatureProto protocolSignature = 1;
  for (int i = 0; i < this->protocolsignature_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->protocolsignature(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetProtocolSignatureResponseProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.common.ProtocolSignatureProto protocolSignature = 1;
  total_size += 1 * this->protocolsignature_size();
  for (int i = 0; i < this->protocolsignature_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->protocolsignature(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetProtocolSignatureResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetProtocolSignatureResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetProtocolSignatureResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetProtocolSignatureResponseProto::MergeFrom(const GetProtocolSignatureResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  protocolsignature_.MergeFrom(from.protocolsignature_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetProtocolSignatureResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetProtocolSignatureResponseProto::CopyFrom(const GetProtocolSignatureResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProtocolSignatureResponseProto::IsInitialized() const {

  for (int i = 0; i < protocolsignature_size(); i++) {
    if (!this->protocolsignature(i).IsInitialized()) return false;
  }
  return true;
}

void GetProtocolSignatureResponseProto::Swap(GetProtocolSignatureResponseProto* other) {
  if (other != this) {
    protocolsignature_.Swap(&other->protocolsignature_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetProtocolSignatureResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetProtocolSignatureResponseProto_descriptor_;
  metadata.reflection = GetProtocolSignatureResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ProtocolSignatureProto::kVersionFieldNumber;
const int ProtocolSignatureProto::kMethodsFieldNumber;
#endif  // !_MSC_VER

ProtocolSignatureProto::ProtocolSignatureProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ProtocolSignatureProto::InitAsDefaultInstance() {
}

ProtocolSignatureProto::ProtocolSignatureProto(const ProtocolSignatureProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ProtocolSignatureProto::SharedCtor() {
  _cached_size_ = 0;
  version_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ProtocolSignatureProto::~ProtocolSignatureProto() {
  SharedDtor();
}

void ProtocolSignatureProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ProtocolSignatureProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ProtocolSignatureProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ProtocolSignatureProto_descriptor_;
}

const ProtocolSignatureProto& ProtocolSignatureProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fProtocolInfo_2eproto();
  return *default_instance_;
}

ProtocolSignatureProto* ProtocolSignatureProto::default_instance_ = NULL;

ProtocolSignatureProto* ProtocolSignatureProto::New() const {
  return new ProtocolSignatureProto;
}

void ProtocolSignatureProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    version_ = GOOGLE_ULONGLONG(0);
  }
  methods_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ProtocolSignatureProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 version = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &version_)));
          set_has_version();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_methods;
        break;
      }

      // repeated uint32 methods = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_methods:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 16, input, this->mutable_methods())));
        } else if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag)
                   == ::google::protobuf::internal::WireFormatLite::
                      WIRETYPE_LENGTH_DELIMITED) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_methods())));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_methods;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ProtocolSignatureProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 version = 1;
  if (has_version()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->version(), output);
  }

  // repeated uint32 methods = 2;
  for (int i = 0; i < this->methods_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(
      2, this->methods(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ProtocolSignatureProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 version = 1;
  if (has_version()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->version(), target);
  }

  // repeated uint32 methods = 2;
  for (int i = 0; i < this->methods_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32ToArray(2, this->methods(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ProtocolSignatureProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 version = 1;
    if (has_version()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->version());
    }

  }
  // repeated uint32 methods = 2;
  {
    int data_size = 0;
    for (int i = 0; i < this->methods_size(); i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->methods(i));
    }
    total_size += 1 * this->methods_size() + data_size;
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ProtocolSignatureProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ProtocolSignatureProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ProtocolSignatureProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ProtocolSignatureProto::MergeFrom(const ProtocolSignatureProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  methods_.MergeFrom(from.methods_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_version()) {
      set_version(from.version());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ProtocolSignatureProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ProtocolSignatureProto::CopyFrom(const ProtocolSignatureProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProtocolSignatureProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void ProtocolSignatureProto::Swap(ProtocolSignatureProto* other) {
  if (other != this) {
    std::swap(version_, other->version_);
    methods_.Swap(&other->methods_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ProtocolSignatureProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ProtocolSignatureProto_descriptor_;
  metadata.reflection = ProtocolSignatureProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
