// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/RefreshCallQueueProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/RefreshCallQueueProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* RefreshCallQueueRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshCallQueueRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RefreshCallQueueResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshCallQueueResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fRefreshCallQueueProtocol_2eproto() {
  protobuf_AddDesc_common_2fRefreshCallQueueProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/RefreshCallQueueProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  RefreshCallQueueRequestProto_descriptor_ = file->message_type(0);
  static const int RefreshCallQueueRequestProto_offsets_[1] = {
  };
  RefreshCallQueueRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshCallQueueRequestProto_descriptor_,
      RefreshCallQueueRequestProto::default_instance_,
      RefreshCallQueueRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshCallQueueRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshCallQueueRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshCallQueueRequestProto));
  RefreshCallQueueResponseProto_descriptor_ = file->message_type(1);
  static const int RefreshCallQueueResponseProto_offsets_[1] = {
  };
  RefreshCallQueueResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshCallQueueResponseProto_descriptor_,
      RefreshCallQueueResponseProto::default_instance_,
      RefreshCallQueueResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshCallQueueResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshCallQueueResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshCallQueueResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fRefreshCallQueueProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshCallQueueRequestProto_descriptor_, &RefreshCallQueueRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshCallQueueResponseProto_descriptor_, &RefreshCallQueueResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fRefreshCallQueueProtocol_2eproto() {
  delete RefreshCallQueueRequestProto::default_instance_;
  delete RefreshCallQueueRequestProto_reflection_;
  delete RefreshCallQueueResponseProto::default_instance_;
  delete RefreshCallQueueResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fRefreshCallQueueProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n%common/RefreshCallQueueProtocol.proto\022"
    "\rhadoop.common\"\036\n\034RefreshCallQueueReques"
    "tProto\"\037\n\035RefreshCallQueueResponseProto2"
    "\220\001\n\037RefreshCallQueueProtocolService\022m\n\020r"
    "efreshCallQueue\022+.hadoop.common.RefreshC"
    "allQueueRequestProto\032,.hadoop.common.Ref"
    "reshCallQueueResponseProtoBC\n\033org.apache"
    ".hadoop.ipc.protoB\036RefreshCallQueueProto"
    "colProtos\210\001\001\240\001\001", 335);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/RefreshCallQueueProtocol.proto", &protobuf_RegisterTypes);
  RefreshCallQueueRequestProto::default_instance_ = new RefreshCallQueueRequestProto();
  RefreshCallQueueResponseProto::default_instance_ = new RefreshCallQueueResponseProto();
  RefreshCallQueueRequestProto::default_instance_->InitAsDefaultInstance();
  RefreshCallQueueResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fRefreshCallQueueProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fRefreshCallQueueProtocol_2eproto {
  StaticDescriptorInitializer_common_2fRefreshCallQueueProtocol_2eproto() {
    protobuf_AddDesc_common_2fRefreshCallQueueProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fRefreshCallQueueProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshCallQueueRequestProto::RefreshCallQueueRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshCallQueueRequestProto::InitAsDefaultInstance() {
}

RefreshCallQueueRequestProto::RefreshCallQueueRequestProto(const RefreshCallQueueRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshCallQueueRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshCallQueueRequestProto::~RefreshCallQueueRequestProto() {
  SharedDtor();
}

void RefreshCallQueueRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshCallQueueRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshCallQueueRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshCallQueueRequestProto_descriptor_;
}

const RefreshCallQueueRequestProto& RefreshCallQueueRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshCallQueueProtocol_2eproto();
  return *default_instance_;
}

RefreshCallQueueRequestProto* RefreshCallQueueRequestProto::default_instance_ = NULL;

RefreshCallQueueRequestProto* RefreshCallQueueRequestProto::New() const {
  return new RefreshCallQueueRequestProto;
}

void RefreshCallQueueRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshCallQueueRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshCallQueueRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshCallQueueRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshCallQueueRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshCallQueueRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshCallQueueRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshCallQueueRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshCallQueueRequestProto::MergeFrom(const RefreshCallQueueRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshCallQueueRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshCallQueueRequestProto::CopyFrom(const RefreshCallQueueRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshCallQueueRequestProto::IsInitialized() const {

  return true;
}

void RefreshCallQueueRequestProto::Swap(RefreshCallQueueRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshCallQueueRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshCallQueueRequestProto_descriptor_;
  metadata.reflection = RefreshCallQueueRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshCallQueueResponseProto::RefreshCallQueueResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshCallQueueResponseProto::InitAsDefaultInstance() {
}

RefreshCallQueueResponseProto::RefreshCallQueueResponseProto(const RefreshCallQueueResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshCallQueueResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshCallQueueResponseProto::~RefreshCallQueueResponseProto() {
  SharedDtor();
}

void RefreshCallQueueResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshCallQueueResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshCallQueueResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshCallQueueResponseProto_descriptor_;
}

const RefreshCallQueueResponseProto& RefreshCallQueueResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshCallQueueProtocol_2eproto();
  return *default_instance_;
}

RefreshCallQueueResponseProto* RefreshCallQueueResponseProto::default_instance_ = NULL;

RefreshCallQueueResponseProto* RefreshCallQueueResponseProto::New() const {
  return new RefreshCallQueueResponseProto;
}

void RefreshCallQueueResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshCallQueueResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshCallQueueResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshCallQueueResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshCallQueueResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshCallQueueResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshCallQueueResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshCallQueueResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshCallQueueResponseProto::MergeFrom(const RefreshCallQueueResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshCallQueueResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshCallQueueResponseProto::CopyFrom(const RefreshCallQueueResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshCallQueueResponseProto::IsInitialized() const {

  return true;
}

void RefreshCallQueueResponseProto::Swap(RefreshCallQueueResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshCallQueueResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshCallQueueResponseProto_descriptor_;
  metadata.reflection = RefreshCallQueueResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
