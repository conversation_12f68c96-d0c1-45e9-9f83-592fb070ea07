// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/TraceAdmin.proto

#ifndef PROTOBUF_common_2fTraceAdmin_2eproto__INCLUDED
#define PROTOBUF_common_2fTraceAdmin_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

class ListSpanReceiversRequestProto;
class SpanReceiverListInfo;
class ListSpanReceiversResponseProto;
class ConfigPair;
class AddSpanReceiverRequestProto;
class AddSpanReceiverResponseProto;
class RemoveSpanReceiverRequestProto;
class RemoveSpanReceiverResponseProto;

// ===================================================================

class ListSpanReceiversRequestProto : public ::google::protobuf::Message {
 public:
  ListSpanReceiversRequestProto();
  virtual ~ListSpanReceiversRequestProto();

  ListSpanReceiversRequestProto(const ListSpanReceiversRequestProto& from);

  inline ListSpanReceiversRequestProto& operator=(const ListSpanReceiversRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListSpanReceiversRequestProto& default_instance();

  void Swap(ListSpanReceiversRequestProto* other);

  // implements Message ----------------------------------------------

  ListSpanReceiversRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListSpanReceiversRequestProto& from);
  void MergeFrom(const ListSpanReceiversRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.ListSpanReceiversRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static ListSpanReceiversRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class SpanReceiverListInfo : public ::google::protobuf::Message {
 public:
  SpanReceiverListInfo();
  virtual ~SpanReceiverListInfo();

  SpanReceiverListInfo(const SpanReceiverListInfo& from);

  inline SpanReceiverListInfo& operator=(const SpanReceiverListInfo& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SpanReceiverListInfo& default_instance();

  void Swap(SpanReceiverListInfo* other);

  // implements Message ----------------------------------------------

  SpanReceiverListInfo* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SpanReceiverListInfo& from);
  void MergeFrom(const SpanReceiverListInfo& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::int64 id() const;
  inline void set_id(::google::protobuf::int64 value);

  // required string className = 2;
  inline bool has_classname() const;
  inline void clear_classname();
  static const int kClassNameFieldNumber = 2;
  inline const ::std::string& classname() const;
  inline void set_classname(const ::std::string& value);
  inline void set_classname(const char* value);
  inline void set_classname(const char* value, size_t size);
  inline ::std::string* mutable_classname();
  inline ::std::string* release_classname();
  inline void set_allocated_classname(::std::string* classname);

  // @@protoc_insertion_point(class_scope:hadoop.common.SpanReceiverListInfo)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_classname();
  inline void clear_has_classname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 id_;
  ::std::string* classname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static SpanReceiverListInfo* default_instance_;
};
// -------------------------------------------------------------------

class ListSpanReceiversResponseProto : public ::google::protobuf::Message {
 public:
  ListSpanReceiversResponseProto();
  virtual ~ListSpanReceiversResponseProto();

  ListSpanReceiversResponseProto(const ListSpanReceiversResponseProto& from);

  inline ListSpanReceiversResponseProto& operator=(const ListSpanReceiversResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListSpanReceiversResponseProto& default_instance();

  void Swap(ListSpanReceiversResponseProto* other);

  // implements Message ----------------------------------------------

  ListSpanReceiversResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListSpanReceiversResponseProto& from);
  void MergeFrom(const ListSpanReceiversResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.common.SpanReceiverListInfo descriptions = 1;
  inline int descriptions_size() const;
  inline void clear_descriptions();
  static const int kDescriptionsFieldNumber = 1;
  inline const ::hadoop::common::SpanReceiverListInfo& descriptions(int index) const;
  inline ::hadoop::common::SpanReceiverListInfo* mutable_descriptions(int index);
  inline ::hadoop::common::SpanReceiverListInfo* add_descriptions();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::SpanReceiverListInfo >&
      descriptions() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::SpanReceiverListInfo >*
      mutable_descriptions();

  // @@protoc_insertion_point(class_scope:hadoop.common.ListSpanReceiversResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::common::SpanReceiverListInfo > descriptions_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static ListSpanReceiversResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ConfigPair : public ::google::protobuf::Message {
 public:
  ConfigPair();
  virtual ~ConfigPair();

  ConfigPair(const ConfigPair& from);

  inline ConfigPair& operator=(const ConfigPair& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ConfigPair& default_instance();

  void Swap(ConfigPair* other);

  // implements Message ----------------------------------------------

  ConfigPair* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ConfigPair& from);
  void MergeFrom(const ConfigPair& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string key = 1;
  inline bool has_key() const;
  inline void clear_key();
  static const int kKeyFieldNumber = 1;
  inline const ::std::string& key() const;
  inline void set_key(const ::std::string& value);
  inline void set_key(const char* value);
  inline void set_key(const char* value, size_t size);
  inline ::std::string* mutable_key();
  inline ::std::string* release_key();
  inline void set_allocated_key(::std::string* key);

  // required string value = 2;
  inline bool has_value() const;
  inline void clear_value();
  static const int kValueFieldNumber = 2;
  inline const ::std::string& value() const;
  inline void set_value(const ::std::string& value);
  inline void set_value(const char* value);
  inline void set_value(const char* value, size_t size);
  inline ::std::string* mutable_value();
  inline ::std::string* release_value();
  inline void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:hadoop.common.ConfigPair)
 private:
  inline void set_has_key();
  inline void clear_has_key();
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* key_;
  ::std::string* value_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static ConfigPair* default_instance_;
};
// -------------------------------------------------------------------

class AddSpanReceiverRequestProto : public ::google::protobuf::Message {
 public:
  AddSpanReceiverRequestProto();
  virtual ~AddSpanReceiverRequestProto();

  AddSpanReceiverRequestProto(const AddSpanReceiverRequestProto& from);

  inline AddSpanReceiverRequestProto& operator=(const AddSpanReceiverRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddSpanReceiverRequestProto& default_instance();

  void Swap(AddSpanReceiverRequestProto* other);

  // implements Message ----------------------------------------------

  AddSpanReceiverRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AddSpanReceiverRequestProto& from);
  void MergeFrom(const AddSpanReceiverRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string className = 1;
  inline bool has_classname() const;
  inline void clear_classname();
  static const int kClassNameFieldNumber = 1;
  inline const ::std::string& classname() const;
  inline void set_classname(const ::std::string& value);
  inline void set_classname(const char* value);
  inline void set_classname(const char* value, size_t size);
  inline ::std::string* mutable_classname();
  inline ::std::string* release_classname();
  inline void set_allocated_classname(::std::string* classname);

  // repeated .hadoop.common.ConfigPair config = 2;
  inline int config_size() const;
  inline void clear_config();
  static const int kConfigFieldNumber = 2;
  inline const ::hadoop::common::ConfigPair& config(int index) const;
  inline ::hadoop::common::ConfigPair* mutable_config(int index);
  inline ::hadoop::common::ConfigPair* add_config();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::ConfigPair >&
      config() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::ConfigPair >*
      mutable_config();

  // @@protoc_insertion_point(class_scope:hadoop.common.AddSpanReceiverRequestProto)
 private:
  inline void set_has_classname();
  inline void clear_has_classname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* classname_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::common::ConfigPair > config_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static AddSpanReceiverRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class AddSpanReceiverResponseProto : public ::google::protobuf::Message {
 public:
  AddSpanReceiverResponseProto();
  virtual ~AddSpanReceiverResponseProto();

  AddSpanReceiverResponseProto(const AddSpanReceiverResponseProto& from);

  inline AddSpanReceiverResponseProto& operator=(const AddSpanReceiverResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddSpanReceiverResponseProto& default_instance();

  void Swap(AddSpanReceiverResponseProto* other);

  // implements Message ----------------------------------------------

  AddSpanReceiverResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AddSpanReceiverResponseProto& from);
  void MergeFrom(const AddSpanReceiverResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::int64 id() const;
  inline void set_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.common.AddSpanReceiverResponseProto)
 private:
  inline void set_has_id();
  inline void clear_has_id();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 id_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static AddSpanReceiverResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveSpanReceiverRequestProto : public ::google::protobuf::Message {
 public:
  RemoveSpanReceiverRequestProto();
  virtual ~RemoveSpanReceiverRequestProto();

  RemoveSpanReceiverRequestProto(const RemoveSpanReceiverRequestProto& from);

  inline RemoveSpanReceiverRequestProto& operator=(const RemoveSpanReceiverRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveSpanReceiverRequestProto& default_instance();

  void Swap(RemoveSpanReceiverRequestProto* other);

  // implements Message ----------------------------------------------

  RemoveSpanReceiverRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveSpanReceiverRequestProto& from);
  void MergeFrom(const RemoveSpanReceiverRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::int64 id() const;
  inline void set_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.common.RemoveSpanReceiverRequestProto)
 private:
  inline void set_has_id();
  inline void clear_has_id();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 id_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static RemoveSpanReceiverRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RemoveSpanReceiverResponseProto : public ::google::protobuf::Message {
 public:
  RemoveSpanReceiverResponseProto();
  virtual ~RemoveSpanReceiverResponseProto();

  RemoveSpanReceiverResponseProto(const RemoveSpanReceiverResponseProto& from);

  inline RemoveSpanReceiverResponseProto& operator=(const RemoveSpanReceiverResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoveSpanReceiverResponseProto& default_instance();

  void Swap(RemoveSpanReceiverResponseProto* other);

  // implements Message ----------------------------------------------

  RemoveSpanReceiverResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RemoveSpanReceiverResponseProto& from);
  void MergeFrom(const RemoveSpanReceiverResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.RemoveSpanReceiverResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_AssignDesc_common_2fTraceAdmin_2eproto();
  friend void protobuf_ShutdownFile_common_2fTraceAdmin_2eproto();

  void InitAsDefaultInstance();
  static RemoveSpanReceiverResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// ListSpanReceiversRequestProto

// -------------------------------------------------------------------

// SpanReceiverListInfo

// required int64 id = 1;
inline bool SpanReceiverListInfo::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SpanReceiverListInfo::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SpanReceiverListInfo::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SpanReceiverListInfo::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::int64 SpanReceiverListInfo::id() const {
  return id_;
}
inline void SpanReceiverListInfo::set_id(::google::protobuf::int64 value) {
  set_has_id();
  id_ = value;
}

// required string className = 2;
inline bool SpanReceiverListInfo::has_classname() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SpanReceiverListInfo::set_has_classname() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SpanReceiverListInfo::clear_has_classname() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SpanReceiverListInfo::clear_classname() {
  if (classname_ != &::google::protobuf::internal::kEmptyString) {
    classname_->clear();
  }
  clear_has_classname();
}
inline const ::std::string& SpanReceiverListInfo::classname() const {
  return *classname_;
}
inline void SpanReceiverListInfo::set_classname(const ::std::string& value) {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  classname_->assign(value);
}
inline void SpanReceiverListInfo::set_classname(const char* value) {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  classname_->assign(value);
}
inline void SpanReceiverListInfo::set_classname(const char* value, size_t size) {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  classname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SpanReceiverListInfo::mutable_classname() {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  return classname_;
}
inline ::std::string* SpanReceiverListInfo::release_classname() {
  clear_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = classname_;
    classname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SpanReceiverListInfo::set_allocated_classname(::std::string* classname) {
  if (classname_ != &::google::protobuf::internal::kEmptyString) {
    delete classname_;
  }
  if (classname) {
    set_has_classname();
    classname_ = classname;
  } else {
    clear_has_classname();
    classname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ListSpanReceiversResponseProto

// repeated .hadoop.common.SpanReceiverListInfo descriptions = 1;
inline int ListSpanReceiversResponseProto::descriptions_size() const {
  return descriptions_.size();
}
inline void ListSpanReceiversResponseProto::clear_descriptions() {
  descriptions_.Clear();
}
inline const ::hadoop::common::SpanReceiverListInfo& ListSpanReceiversResponseProto::descriptions(int index) const {
  return descriptions_.Get(index);
}
inline ::hadoop::common::SpanReceiverListInfo* ListSpanReceiversResponseProto::mutable_descriptions(int index) {
  return descriptions_.Mutable(index);
}
inline ::hadoop::common::SpanReceiverListInfo* ListSpanReceiversResponseProto::add_descriptions() {
  return descriptions_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::SpanReceiverListInfo >&
ListSpanReceiversResponseProto::descriptions() const {
  return descriptions_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::SpanReceiverListInfo >*
ListSpanReceiversResponseProto::mutable_descriptions() {
  return &descriptions_;
}

// -------------------------------------------------------------------

// ConfigPair

// required string key = 1;
inline bool ConfigPair::has_key() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ConfigPair::set_has_key() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ConfigPair::clear_has_key() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ConfigPair::clear_key() {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    key_->clear();
  }
  clear_has_key();
}
inline const ::std::string& ConfigPair::key() const {
  return *key_;
}
inline void ConfigPair::set_key(const ::std::string& value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void ConfigPair::set_key(const char* value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void ConfigPair::set_key(const char* value, size_t size) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ConfigPair::mutable_key() {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  return key_;
}
inline ::std::string* ConfigPair::release_key() {
  clear_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = key_;
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ConfigPair::set_allocated_key(::std::string* key) {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    delete key_;
  }
  if (key) {
    set_has_key();
    key_ = key;
  } else {
    clear_has_key();
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string value = 2;
inline bool ConfigPair::has_value() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ConfigPair::set_has_value() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ConfigPair::clear_has_value() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ConfigPair::clear_value() {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    value_->clear();
  }
  clear_has_value();
}
inline const ::std::string& ConfigPair::value() const {
  return *value_;
}
inline void ConfigPair::set_value(const ::std::string& value) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(value);
}
inline void ConfigPair::set_value(const char* value) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(value);
}
inline void ConfigPair::set_value(const char* value, size_t size) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ConfigPair::mutable_value() {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  return value_;
}
inline ::std::string* ConfigPair::release_value() {
  clear_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = value_;
    value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ConfigPair::set_allocated_value(::std::string* value) {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    delete value_;
  }
  if (value) {
    set_has_value();
    value_ = value;
  } else {
    clear_has_value();
    value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// AddSpanReceiverRequestProto

// required string className = 1;
inline bool AddSpanReceiverRequestProto::has_classname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AddSpanReceiverRequestProto::set_has_classname() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AddSpanReceiverRequestProto::clear_has_classname() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AddSpanReceiverRequestProto::clear_classname() {
  if (classname_ != &::google::protobuf::internal::kEmptyString) {
    classname_->clear();
  }
  clear_has_classname();
}
inline const ::std::string& AddSpanReceiverRequestProto::classname() const {
  return *classname_;
}
inline void AddSpanReceiverRequestProto::set_classname(const ::std::string& value) {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  classname_->assign(value);
}
inline void AddSpanReceiverRequestProto::set_classname(const char* value) {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  classname_->assign(value);
}
inline void AddSpanReceiverRequestProto::set_classname(const char* value, size_t size) {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  classname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AddSpanReceiverRequestProto::mutable_classname() {
  set_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    classname_ = new ::std::string;
  }
  return classname_;
}
inline ::std::string* AddSpanReceiverRequestProto::release_classname() {
  clear_has_classname();
  if (classname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = classname_;
    classname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AddSpanReceiverRequestProto::set_allocated_classname(::std::string* classname) {
  if (classname_ != &::google::protobuf::internal::kEmptyString) {
    delete classname_;
  }
  if (classname) {
    set_has_classname();
    classname_ = classname;
  } else {
    clear_has_classname();
    classname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.common.ConfigPair config = 2;
inline int AddSpanReceiverRequestProto::config_size() const {
  return config_.size();
}
inline void AddSpanReceiverRequestProto::clear_config() {
  config_.Clear();
}
inline const ::hadoop::common::ConfigPair& AddSpanReceiverRequestProto::config(int index) const {
  return config_.Get(index);
}
inline ::hadoop::common::ConfigPair* AddSpanReceiverRequestProto::mutable_config(int index) {
  return config_.Mutable(index);
}
inline ::hadoop::common::ConfigPair* AddSpanReceiverRequestProto::add_config() {
  return config_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::ConfigPair >&
AddSpanReceiverRequestProto::config() const {
  return config_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::ConfigPair >*
AddSpanReceiverRequestProto::mutable_config() {
  return &config_;
}

// -------------------------------------------------------------------

// AddSpanReceiverResponseProto

// required int64 id = 1;
inline bool AddSpanReceiverResponseProto::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AddSpanReceiverResponseProto::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AddSpanReceiverResponseProto::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AddSpanReceiverResponseProto::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::int64 AddSpanReceiverResponseProto::id() const {
  return id_;
}
inline void AddSpanReceiverResponseProto::set_id(::google::protobuf::int64 value) {
  set_has_id();
  id_ = value;
}

// -------------------------------------------------------------------

// RemoveSpanReceiverRequestProto

// required int64 id = 1;
inline bool RemoveSpanReceiverRequestProto::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RemoveSpanReceiverRequestProto::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RemoveSpanReceiverRequestProto::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RemoveSpanReceiverRequestProto::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::int64 RemoveSpanReceiverRequestProto::id() const {
  return id_;
}
inline void RemoveSpanReceiverRequestProto::set_id(::google::protobuf::int64 value) {
  set_has_id();
  id_ = value;
}

// -------------------------------------------------------------------

// RemoveSpanReceiverResponseProto


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fTraceAdmin_2eproto__INCLUDED
