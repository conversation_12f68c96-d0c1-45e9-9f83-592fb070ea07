// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/GenericRefreshProtocol.proto

#ifndef PROTOBUF_common_2fGenericRefreshProtocol_2eproto__INCLUDED
#define PROTOBUF_common_2fGenericRefreshProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
void protobuf_AssignDesc_common_2fGenericRefreshProtocol_2eproto();
void protobuf_ShutdownFile_common_2fGenericRefreshProtocol_2eproto();

class GenericRefreshRequestProto;
class GenericRefreshResponseProto;
class GenericRefreshResponseCollectionProto;

// ===================================================================

class GenericRefreshRequestProto : public ::google::protobuf::Message {
 public:
  GenericRefreshRequestProto();
  virtual ~GenericRefreshRequestProto();

  GenericRefreshRequestProto(const GenericRefreshRequestProto& from);

  inline GenericRefreshRequestProto& operator=(const GenericRefreshRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GenericRefreshRequestProto& default_instance();

  void Swap(GenericRefreshRequestProto* other);

  // implements Message ----------------------------------------------

  GenericRefreshRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GenericRefreshRequestProto& from);
  void MergeFrom(const GenericRefreshRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string identifier = 1;
  inline bool has_identifier() const;
  inline void clear_identifier();
  static const int kIdentifierFieldNumber = 1;
  inline const ::std::string& identifier() const;
  inline void set_identifier(const ::std::string& value);
  inline void set_identifier(const char* value);
  inline void set_identifier(const char* value, size_t size);
  inline ::std::string* mutable_identifier();
  inline ::std::string* release_identifier();
  inline void set_allocated_identifier(::std::string* identifier);

  // repeated string args = 2;
  inline int args_size() const;
  inline void clear_args();
  static const int kArgsFieldNumber = 2;
  inline const ::std::string& args(int index) const;
  inline ::std::string* mutable_args(int index);
  inline void set_args(int index, const ::std::string& value);
  inline void set_args(int index, const char* value);
  inline void set_args(int index, const char* value, size_t size);
  inline ::std::string* add_args();
  inline void add_args(const ::std::string& value);
  inline void add_args(const char* value);
  inline void add_args(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& args() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_args();

  // @@protoc_insertion_point(class_scope:hadoop.common.GenericRefreshRequestProto)
 private:
  inline void set_has_identifier();
  inline void clear_has_identifier();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* identifier_;
  ::google::protobuf::RepeatedPtrField< ::std::string> args_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fGenericRefreshProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fGenericRefreshProtocol_2eproto();

  void InitAsDefaultInstance();
  static GenericRefreshRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GenericRefreshResponseProto : public ::google::protobuf::Message {
 public:
  GenericRefreshResponseProto();
  virtual ~GenericRefreshResponseProto();

  GenericRefreshResponseProto(const GenericRefreshResponseProto& from);

  inline GenericRefreshResponseProto& operator=(const GenericRefreshResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GenericRefreshResponseProto& default_instance();

  void Swap(GenericRefreshResponseProto* other);

  // implements Message ----------------------------------------------

  GenericRefreshResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GenericRefreshResponseProto& from);
  void MergeFrom(const GenericRefreshResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 exitStatus = 1;
  inline bool has_exitstatus() const;
  inline void clear_exitstatus();
  static const int kExitStatusFieldNumber = 1;
  inline ::google::protobuf::int32 exitstatus() const;
  inline void set_exitstatus(::google::protobuf::int32 value);

  // optional string userMessage = 2;
  inline bool has_usermessage() const;
  inline void clear_usermessage();
  static const int kUserMessageFieldNumber = 2;
  inline const ::std::string& usermessage() const;
  inline void set_usermessage(const ::std::string& value);
  inline void set_usermessage(const char* value);
  inline void set_usermessage(const char* value, size_t size);
  inline ::std::string* mutable_usermessage();
  inline ::std::string* release_usermessage();
  inline void set_allocated_usermessage(::std::string* usermessage);

  // optional string senderName = 3;
  inline bool has_sendername() const;
  inline void clear_sendername();
  static const int kSenderNameFieldNumber = 3;
  inline const ::std::string& sendername() const;
  inline void set_sendername(const ::std::string& value);
  inline void set_sendername(const char* value);
  inline void set_sendername(const char* value, size_t size);
  inline ::std::string* mutable_sendername();
  inline ::std::string* release_sendername();
  inline void set_allocated_sendername(::std::string* sendername);

  // @@protoc_insertion_point(class_scope:hadoop.common.GenericRefreshResponseProto)
 private:
  inline void set_has_exitstatus();
  inline void clear_has_exitstatus();
  inline void set_has_usermessage();
  inline void clear_has_usermessage();
  inline void set_has_sendername();
  inline void clear_has_sendername();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* usermessage_;
  ::std::string* sendername_;
  ::google::protobuf::int32 exitstatus_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fGenericRefreshProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fGenericRefreshProtocol_2eproto();

  void InitAsDefaultInstance();
  static GenericRefreshResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GenericRefreshResponseCollectionProto : public ::google::protobuf::Message {
 public:
  GenericRefreshResponseCollectionProto();
  virtual ~GenericRefreshResponseCollectionProto();

  GenericRefreshResponseCollectionProto(const GenericRefreshResponseCollectionProto& from);

  inline GenericRefreshResponseCollectionProto& operator=(const GenericRefreshResponseCollectionProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GenericRefreshResponseCollectionProto& default_instance();

  void Swap(GenericRefreshResponseCollectionProto* other);

  // implements Message ----------------------------------------------

  GenericRefreshResponseCollectionProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GenericRefreshResponseCollectionProto& from);
  void MergeFrom(const GenericRefreshResponseCollectionProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.common.GenericRefreshResponseProto responses = 1;
  inline int responses_size() const;
  inline void clear_responses();
  static const int kResponsesFieldNumber = 1;
  inline const ::hadoop::common::GenericRefreshResponseProto& responses(int index) const;
  inline ::hadoop::common::GenericRefreshResponseProto* mutable_responses(int index);
  inline ::hadoop::common::GenericRefreshResponseProto* add_responses();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::GenericRefreshResponseProto >&
      responses() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::GenericRefreshResponseProto >*
      mutable_responses();

  // @@protoc_insertion_point(class_scope:hadoop.common.GenericRefreshResponseCollectionProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::common::GenericRefreshResponseProto > responses_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fGenericRefreshProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fGenericRefreshProtocol_2eproto();

  void InitAsDefaultInstance();
  static GenericRefreshResponseCollectionProto* default_instance_;
};
// ===================================================================


// ===================================================================

// GenericRefreshRequestProto

// optional string identifier = 1;
inline bool GenericRefreshRequestProto::has_identifier() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GenericRefreshRequestProto::set_has_identifier() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GenericRefreshRequestProto::clear_has_identifier() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GenericRefreshRequestProto::clear_identifier() {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    identifier_->clear();
  }
  clear_has_identifier();
}
inline const ::std::string& GenericRefreshRequestProto::identifier() const {
  return *identifier_;
}
inline void GenericRefreshRequestProto::set_identifier(const ::std::string& value) {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  identifier_->assign(value);
}
inline void GenericRefreshRequestProto::set_identifier(const char* value) {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  identifier_->assign(value);
}
inline void GenericRefreshRequestProto::set_identifier(const char* value, size_t size) {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  identifier_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GenericRefreshRequestProto::mutable_identifier() {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  return identifier_;
}
inline ::std::string* GenericRefreshRequestProto::release_identifier() {
  clear_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = identifier_;
    identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GenericRefreshRequestProto::set_allocated_identifier(::std::string* identifier) {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    delete identifier_;
  }
  if (identifier) {
    set_has_identifier();
    identifier_ = identifier;
  } else {
    clear_has_identifier();
    identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated string args = 2;
inline int GenericRefreshRequestProto::args_size() const {
  return args_.size();
}
inline void GenericRefreshRequestProto::clear_args() {
  args_.Clear();
}
inline const ::std::string& GenericRefreshRequestProto::args(int index) const {
  return args_.Get(index);
}
inline ::std::string* GenericRefreshRequestProto::mutable_args(int index) {
  return args_.Mutable(index);
}
inline void GenericRefreshRequestProto::set_args(int index, const ::std::string& value) {
  args_.Mutable(index)->assign(value);
}
inline void GenericRefreshRequestProto::set_args(int index, const char* value) {
  args_.Mutable(index)->assign(value);
}
inline void GenericRefreshRequestProto::set_args(int index, const char* value, size_t size) {
  args_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GenericRefreshRequestProto::add_args() {
  return args_.Add();
}
inline void GenericRefreshRequestProto::add_args(const ::std::string& value) {
  args_.Add()->assign(value);
}
inline void GenericRefreshRequestProto::add_args(const char* value) {
  args_.Add()->assign(value);
}
inline void GenericRefreshRequestProto::add_args(const char* value, size_t size) {
  args_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
GenericRefreshRequestProto::args() const {
  return args_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
GenericRefreshRequestProto::mutable_args() {
  return &args_;
}

// -------------------------------------------------------------------

// GenericRefreshResponseProto

// optional int32 exitStatus = 1;
inline bool GenericRefreshResponseProto::has_exitstatus() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GenericRefreshResponseProto::set_has_exitstatus() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GenericRefreshResponseProto::clear_has_exitstatus() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GenericRefreshResponseProto::clear_exitstatus() {
  exitstatus_ = 0;
  clear_has_exitstatus();
}
inline ::google::protobuf::int32 GenericRefreshResponseProto::exitstatus() const {
  return exitstatus_;
}
inline void GenericRefreshResponseProto::set_exitstatus(::google::protobuf::int32 value) {
  set_has_exitstatus();
  exitstatus_ = value;
}

// optional string userMessage = 2;
inline bool GenericRefreshResponseProto::has_usermessage() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GenericRefreshResponseProto::set_has_usermessage() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GenericRefreshResponseProto::clear_has_usermessage() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GenericRefreshResponseProto::clear_usermessage() {
  if (usermessage_ != &::google::protobuf::internal::kEmptyString) {
    usermessage_->clear();
  }
  clear_has_usermessage();
}
inline const ::std::string& GenericRefreshResponseProto::usermessage() const {
  return *usermessage_;
}
inline void GenericRefreshResponseProto::set_usermessage(const ::std::string& value) {
  set_has_usermessage();
  if (usermessage_ == &::google::protobuf::internal::kEmptyString) {
    usermessage_ = new ::std::string;
  }
  usermessage_->assign(value);
}
inline void GenericRefreshResponseProto::set_usermessage(const char* value) {
  set_has_usermessage();
  if (usermessage_ == &::google::protobuf::internal::kEmptyString) {
    usermessage_ = new ::std::string;
  }
  usermessage_->assign(value);
}
inline void GenericRefreshResponseProto::set_usermessage(const char* value, size_t size) {
  set_has_usermessage();
  if (usermessage_ == &::google::protobuf::internal::kEmptyString) {
    usermessage_ = new ::std::string;
  }
  usermessage_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GenericRefreshResponseProto::mutable_usermessage() {
  set_has_usermessage();
  if (usermessage_ == &::google::protobuf::internal::kEmptyString) {
    usermessage_ = new ::std::string;
  }
  return usermessage_;
}
inline ::std::string* GenericRefreshResponseProto::release_usermessage() {
  clear_has_usermessage();
  if (usermessage_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = usermessage_;
    usermessage_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GenericRefreshResponseProto::set_allocated_usermessage(::std::string* usermessage) {
  if (usermessage_ != &::google::protobuf::internal::kEmptyString) {
    delete usermessage_;
  }
  if (usermessage) {
    set_has_usermessage();
    usermessage_ = usermessage;
  } else {
    clear_has_usermessage();
    usermessage_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string senderName = 3;
inline bool GenericRefreshResponseProto::has_sendername() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void GenericRefreshResponseProto::set_has_sendername() {
  _has_bits_[0] |= 0x00000004u;
}
inline void GenericRefreshResponseProto::clear_has_sendername() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void GenericRefreshResponseProto::clear_sendername() {
  if (sendername_ != &::google::protobuf::internal::kEmptyString) {
    sendername_->clear();
  }
  clear_has_sendername();
}
inline const ::std::string& GenericRefreshResponseProto::sendername() const {
  return *sendername_;
}
inline void GenericRefreshResponseProto::set_sendername(const ::std::string& value) {
  set_has_sendername();
  if (sendername_ == &::google::protobuf::internal::kEmptyString) {
    sendername_ = new ::std::string;
  }
  sendername_->assign(value);
}
inline void GenericRefreshResponseProto::set_sendername(const char* value) {
  set_has_sendername();
  if (sendername_ == &::google::protobuf::internal::kEmptyString) {
    sendername_ = new ::std::string;
  }
  sendername_->assign(value);
}
inline void GenericRefreshResponseProto::set_sendername(const char* value, size_t size) {
  set_has_sendername();
  if (sendername_ == &::google::protobuf::internal::kEmptyString) {
    sendername_ = new ::std::string;
  }
  sendername_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GenericRefreshResponseProto::mutable_sendername() {
  set_has_sendername();
  if (sendername_ == &::google::protobuf::internal::kEmptyString) {
    sendername_ = new ::std::string;
  }
  return sendername_;
}
inline ::std::string* GenericRefreshResponseProto::release_sendername() {
  clear_has_sendername();
  if (sendername_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = sendername_;
    sendername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GenericRefreshResponseProto::set_allocated_sendername(::std::string* sendername) {
  if (sendername_ != &::google::protobuf::internal::kEmptyString) {
    delete sendername_;
  }
  if (sendername) {
    set_has_sendername();
    sendername_ = sendername;
  } else {
    clear_has_sendername();
    sendername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// GenericRefreshResponseCollectionProto

// repeated .hadoop.common.GenericRefreshResponseProto responses = 1;
inline int GenericRefreshResponseCollectionProto::responses_size() const {
  return responses_.size();
}
inline void GenericRefreshResponseCollectionProto::clear_responses() {
  responses_.Clear();
}
inline const ::hadoop::common::GenericRefreshResponseProto& GenericRefreshResponseCollectionProto::responses(int index) const {
  return responses_.Get(index);
}
inline ::hadoop::common::GenericRefreshResponseProto* GenericRefreshResponseCollectionProto::mutable_responses(int index) {
  return responses_.Mutable(index);
}
inline ::hadoop::common::GenericRefreshResponseProto* GenericRefreshResponseCollectionProto::add_responses() {
  return responses_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::GenericRefreshResponseProto >&
GenericRefreshResponseCollectionProto::responses() const {
  return responses_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::GenericRefreshResponseProto >*
GenericRefreshResponseCollectionProto::mutable_responses() {
  return &responses_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fGenericRefreshProtocol_2eproto__INCLUDED
