// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/ZKFCProtocol.proto

#ifndef PROTOBUF_common_2fZKFCProtocol_2eproto__INCLUDED
#define PROTOBUF_common_2fZKFCProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
void protobuf_AssignDesc_common_2fZKFCProtocol_2eproto();
void protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto();

class CedeActiveRequestProto;
class CedeActiveResponseProto;
class GracefulFailoverRequestProto;
class GracefulFailoverResponseProto;

// ===================================================================

class CedeActiveRequestProto : public ::google::protobuf::Message {
 public:
  CedeActiveRequestProto();
  virtual ~CedeActiveRequestProto();

  CedeActiveRequestProto(const CedeActiveRequestProto& from);

  inline CedeActiveRequestProto& operator=(const CedeActiveRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CedeActiveRequestProto& default_instance();

  void Swap(CedeActiveRequestProto* other);

  // implements Message ----------------------------------------------

  CedeActiveRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CedeActiveRequestProto& from);
  void MergeFrom(const CedeActiveRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 millisToCede = 1;
  inline bool has_millistocede() const;
  inline void clear_millistocede();
  static const int kMillisToCedeFieldNumber = 1;
  inline ::google::protobuf::uint32 millistocede() const;
  inline void set_millistocede(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.common.CedeActiveRequestProto)
 private:
  inline void set_has_millistocede();
  inline void clear_has_millistocede();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 millistocede_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto();

  void InitAsDefaultInstance();
  static CedeActiveRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class CedeActiveResponseProto : public ::google::protobuf::Message {
 public:
  CedeActiveResponseProto();
  virtual ~CedeActiveResponseProto();

  CedeActiveResponseProto(const CedeActiveResponseProto& from);

  inline CedeActiveResponseProto& operator=(const CedeActiveResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CedeActiveResponseProto& default_instance();

  void Swap(CedeActiveResponseProto* other);

  // implements Message ----------------------------------------------

  CedeActiveResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CedeActiveResponseProto& from);
  void MergeFrom(const CedeActiveResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.CedeActiveResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto();

  void InitAsDefaultInstance();
  static CedeActiveResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GracefulFailoverRequestProto : public ::google::protobuf::Message {
 public:
  GracefulFailoverRequestProto();
  virtual ~GracefulFailoverRequestProto();

  GracefulFailoverRequestProto(const GracefulFailoverRequestProto& from);

  inline GracefulFailoverRequestProto& operator=(const GracefulFailoverRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GracefulFailoverRequestProto& default_instance();

  void Swap(GracefulFailoverRequestProto* other);

  // implements Message ----------------------------------------------

  GracefulFailoverRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GracefulFailoverRequestProto& from);
  void MergeFrom(const GracefulFailoverRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.GracefulFailoverRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto();

  void InitAsDefaultInstance();
  static GracefulFailoverRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GracefulFailoverResponseProto : public ::google::protobuf::Message {
 public:
  GracefulFailoverResponseProto();
  virtual ~GracefulFailoverResponseProto();

  GracefulFailoverResponseProto(const GracefulFailoverResponseProto& from);

  inline GracefulFailoverResponseProto& operator=(const GracefulFailoverResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GracefulFailoverResponseProto& default_instance();

  void Swap(GracefulFailoverResponseProto* other);

  // implements Message ----------------------------------------------

  GracefulFailoverResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GracefulFailoverResponseProto& from);
  void MergeFrom(const GracefulFailoverResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.GracefulFailoverResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fZKFCProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto();

  void InitAsDefaultInstance();
  static GracefulFailoverResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// CedeActiveRequestProto

// required uint32 millisToCede = 1;
inline bool CedeActiveRequestProto::has_millistocede() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CedeActiveRequestProto::set_has_millistocede() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CedeActiveRequestProto::clear_has_millistocede() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CedeActiveRequestProto::clear_millistocede() {
  millistocede_ = 0u;
  clear_has_millistocede();
}
inline ::google::protobuf::uint32 CedeActiveRequestProto::millistocede() const {
  return millistocede_;
}
inline void CedeActiveRequestProto::set_millistocede(::google::protobuf::uint32 value) {
  set_has_millistocede();
  millistocede_ = value;
}

// -------------------------------------------------------------------

// CedeActiveResponseProto

// -------------------------------------------------------------------

// GracefulFailoverRequestProto

// -------------------------------------------------------------------

// GracefulFailoverResponseProto


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fZKFCProtocol_2eproto__INCLUDED
