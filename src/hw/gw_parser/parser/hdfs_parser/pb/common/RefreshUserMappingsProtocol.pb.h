// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/RefreshUserMappingsProtocol.proto

#ifndef PROTOBUF_common_2fRefreshUserMappingsProtocol_2eproto__INCLUDED
#define PROTOBUF_common_2fRefreshUserMappingsProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
void protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto();
void protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto();

class RefreshUserToGroupsMappingsRequestProto;
class RefreshUserToGroupsMappingsResponseProto;
class RefreshSuperUserGroupsConfigurationRequestProto;
class RefreshSuperUserGroupsConfigurationResponseProto;

// ===================================================================

class RefreshUserToGroupsMappingsRequestProto : public ::google::protobuf::Message {
 public:
  RefreshUserToGroupsMappingsRequestProto();
  virtual ~RefreshUserToGroupsMappingsRequestProto();

  RefreshUserToGroupsMappingsRequestProto(const RefreshUserToGroupsMappingsRequestProto& from);

  inline RefreshUserToGroupsMappingsRequestProto& operator=(const RefreshUserToGroupsMappingsRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RefreshUserToGroupsMappingsRequestProto& default_instance();

  void Swap(RefreshUserToGroupsMappingsRequestProto* other);

  // implements Message ----------------------------------------------

  RefreshUserToGroupsMappingsRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RefreshUserToGroupsMappingsRequestProto& from);
  void MergeFrom(const RefreshUserToGroupsMappingsRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.RefreshUserToGroupsMappingsRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto();

  void InitAsDefaultInstance();
  static RefreshUserToGroupsMappingsRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RefreshUserToGroupsMappingsResponseProto : public ::google::protobuf::Message {
 public:
  RefreshUserToGroupsMappingsResponseProto();
  virtual ~RefreshUserToGroupsMappingsResponseProto();

  RefreshUserToGroupsMappingsResponseProto(const RefreshUserToGroupsMappingsResponseProto& from);

  inline RefreshUserToGroupsMappingsResponseProto& operator=(const RefreshUserToGroupsMappingsResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RefreshUserToGroupsMappingsResponseProto& default_instance();

  void Swap(RefreshUserToGroupsMappingsResponseProto* other);

  // implements Message ----------------------------------------------

  RefreshUserToGroupsMappingsResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RefreshUserToGroupsMappingsResponseProto& from);
  void MergeFrom(const RefreshUserToGroupsMappingsResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.RefreshUserToGroupsMappingsResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto();

  void InitAsDefaultInstance();
  static RefreshUserToGroupsMappingsResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class RefreshSuperUserGroupsConfigurationRequestProto : public ::google::protobuf::Message {
 public:
  RefreshSuperUserGroupsConfigurationRequestProto();
  virtual ~RefreshSuperUserGroupsConfigurationRequestProto();

  RefreshSuperUserGroupsConfigurationRequestProto(const RefreshSuperUserGroupsConfigurationRequestProto& from);

  inline RefreshSuperUserGroupsConfigurationRequestProto& operator=(const RefreshSuperUserGroupsConfigurationRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RefreshSuperUserGroupsConfigurationRequestProto& default_instance();

  void Swap(RefreshSuperUserGroupsConfigurationRequestProto* other);

  // implements Message ----------------------------------------------

  RefreshSuperUserGroupsConfigurationRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RefreshSuperUserGroupsConfigurationRequestProto& from);
  void MergeFrom(const RefreshSuperUserGroupsConfigurationRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.RefreshSuperUserGroupsConfigurationRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto();

  void InitAsDefaultInstance();
  static RefreshSuperUserGroupsConfigurationRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class RefreshSuperUserGroupsConfigurationResponseProto : public ::google::protobuf::Message {
 public:
  RefreshSuperUserGroupsConfigurationResponseProto();
  virtual ~RefreshSuperUserGroupsConfigurationResponseProto();

  RefreshSuperUserGroupsConfigurationResponseProto(const RefreshSuperUserGroupsConfigurationResponseProto& from);

  inline RefreshSuperUserGroupsConfigurationResponseProto& operator=(const RefreshSuperUserGroupsConfigurationResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RefreshSuperUserGroupsConfigurationResponseProto& default_instance();

  void Swap(RefreshSuperUserGroupsConfigurationResponseProto* other);

  // implements Message ----------------------------------------------

  RefreshSuperUserGroupsConfigurationResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RefreshSuperUserGroupsConfigurationResponseProto& from);
  void MergeFrom(const RefreshSuperUserGroupsConfigurationResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.RefreshSuperUserGroupsConfigurationResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto();

  void InitAsDefaultInstance();
  static RefreshSuperUserGroupsConfigurationResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// RefreshUserToGroupsMappingsRequestProto

// -------------------------------------------------------------------

// RefreshUserToGroupsMappingsResponseProto

// -------------------------------------------------------------------

// RefreshSuperUserGroupsConfigurationRequestProto

// -------------------------------------------------------------------

// RefreshSuperUserGroupsConfigurationResponseProto


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fRefreshUserMappingsProtocol_2eproto__INCLUDED
