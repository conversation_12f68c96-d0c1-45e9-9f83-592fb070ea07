// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/Security.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/Security.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* TokenProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TokenProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetDelegationTokenRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetDelegationTokenRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetDelegationTokenResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetDelegationTokenResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RenewDelegationTokenRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RenewDelegationTokenRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RenewDelegationTokenResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RenewDelegationTokenResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CancelDelegationTokenRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CancelDelegationTokenRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CancelDelegationTokenResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CancelDelegationTokenResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fSecurity_2eproto() {
  protobuf_AddDesc_common_2fSecurity_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/Security.proto");
  GOOGLE_CHECK(file != NULL);
  TokenProto_descriptor_ = file->message_type(0);
  static const int TokenProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TokenProto, identifier_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TokenProto, password_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TokenProto, kind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TokenProto, service_),
  };
  TokenProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      TokenProto_descriptor_,
      TokenProto::default_instance_,
      TokenProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TokenProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TokenProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(TokenProto));
  GetDelegationTokenRequestProto_descriptor_ = file->message_type(1);
  static const int GetDelegationTokenRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetDelegationTokenRequestProto, renewer_),
  };
  GetDelegationTokenRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetDelegationTokenRequestProto_descriptor_,
      GetDelegationTokenRequestProto::default_instance_,
      GetDelegationTokenRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetDelegationTokenRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetDelegationTokenRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetDelegationTokenRequestProto));
  GetDelegationTokenResponseProto_descriptor_ = file->message_type(2);
  static const int GetDelegationTokenResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetDelegationTokenResponseProto, token_),
  };
  GetDelegationTokenResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetDelegationTokenResponseProto_descriptor_,
      GetDelegationTokenResponseProto::default_instance_,
      GetDelegationTokenResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetDelegationTokenResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetDelegationTokenResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetDelegationTokenResponseProto));
  RenewDelegationTokenRequestProto_descriptor_ = file->message_type(3);
  static const int RenewDelegationTokenRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenewDelegationTokenRequestProto, token_),
  };
  RenewDelegationTokenRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RenewDelegationTokenRequestProto_descriptor_,
      RenewDelegationTokenRequestProto::default_instance_,
      RenewDelegationTokenRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenewDelegationTokenRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenewDelegationTokenRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RenewDelegationTokenRequestProto));
  RenewDelegationTokenResponseProto_descriptor_ = file->message_type(4);
  static const int RenewDelegationTokenResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenewDelegationTokenResponseProto, newexpirytime_),
  };
  RenewDelegationTokenResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RenewDelegationTokenResponseProto_descriptor_,
      RenewDelegationTokenResponseProto::default_instance_,
      RenewDelegationTokenResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenewDelegationTokenResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RenewDelegationTokenResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RenewDelegationTokenResponseProto));
  CancelDelegationTokenRequestProto_descriptor_ = file->message_type(5);
  static const int CancelDelegationTokenRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CancelDelegationTokenRequestProto, token_),
  };
  CancelDelegationTokenRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CancelDelegationTokenRequestProto_descriptor_,
      CancelDelegationTokenRequestProto::default_instance_,
      CancelDelegationTokenRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CancelDelegationTokenRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CancelDelegationTokenRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CancelDelegationTokenRequestProto));
  CancelDelegationTokenResponseProto_descriptor_ = file->message_type(6);
  static const int CancelDelegationTokenResponseProto_offsets_[1] = {
  };
  CancelDelegationTokenResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CancelDelegationTokenResponseProto_descriptor_,
      CancelDelegationTokenResponseProto::default_instance_,
      CancelDelegationTokenResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CancelDelegationTokenResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CancelDelegationTokenResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CancelDelegationTokenResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fSecurity_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    TokenProto_descriptor_, &TokenProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetDelegationTokenRequestProto_descriptor_, &GetDelegationTokenRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetDelegationTokenResponseProto_descriptor_, &GetDelegationTokenResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RenewDelegationTokenRequestProto_descriptor_, &RenewDelegationTokenRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RenewDelegationTokenResponseProto_descriptor_, &RenewDelegationTokenResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CancelDelegationTokenRequestProto_descriptor_, &CancelDelegationTokenRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CancelDelegationTokenResponseProto_descriptor_, &CancelDelegationTokenResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fSecurity_2eproto() {
  delete TokenProto::default_instance_;
  delete TokenProto_reflection_;
  delete GetDelegationTokenRequestProto::default_instance_;
  delete GetDelegationTokenRequestProto_reflection_;
  delete GetDelegationTokenResponseProto::default_instance_;
  delete GetDelegationTokenResponseProto_reflection_;
  delete RenewDelegationTokenRequestProto::default_instance_;
  delete RenewDelegationTokenRequestProto_reflection_;
  delete RenewDelegationTokenResponseProto::default_instance_;
  delete RenewDelegationTokenResponseProto_reflection_;
  delete CancelDelegationTokenRequestProto::default_instance_;
  delete CancelDelegationTokenRequestProto_reflection_;
  delete CancelDelegationTokenResponseProto::default_instance_;
  delete CancelDelegationTokenResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fSecurity_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\025common/Security.proto\022\rhadoop.common\"Q"
    "\n\nTokenProto\022\022\n\nidentifier\030\001 \002(\014\022\020\n\010pass"
    "word\030\002 \002(\014\022\014\n\004kind\030\003 \002(\t\022\017\n\007service\030\004 \002("
    "\t\"1\n\036GetDelegationTokenRequestProto\022\017\n\007r"
    "enewer\030\001 \002(\t\"K\n\037GetDelegationTokenRespon"
    "seProto\022(\n\005token\030\001 \001(\0132\031.hadoop.common.T"
    "okenProto\"L\n RenewDelegationTokenRequest"
    "Proto\022(\n\005token\030\001 \002(\0132\031.hadoop.common.Tok"
    "enProto\":\n!RenewDelegationTokenResponseP"
    "roto\022\025\n\rnewExpiryTime\030\001 \002(\004\"M\n!CancelDel"
    "egationTokenRequestProto\022(\n\005token\030\001 \002(\0132"
    "\031.hadoop.common.TokenProto\"$\n\"CancelDele"
    "gationTokenResponseProtoB8\n org.apache.h"
    "adoop.security.protoB\016SecurityProtos\210\001\001\240"
    "\001\001", 562);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/Security.proto", &protobuf_RegisterTypes);
  TokenProto::default_instance_ = new TokenProto();
  GetDelegationTokenRequestProto::default_instance_ = new GetDelegationTokenRequestProto();
  GetDelegationTokenResponseProto::default_instance_ = new GetDelegationTokenResponseProto();
  RenewDelegationTokenRequestProto::default_instance_ = new RenewDelegationTokenRequestProto();
  RenewDelegationTokenResponseProto::default_instance_ = new RenewDelegationTokenResponseProto();
  CancelDelegationTokenRequestProto::default_instance_ = new CancelDelegationTokenRequestProto();
  CancelDelegationTokenResponseProto::default_instance_ = new CancelDelegationTokenResponseProto();
  TokenProto::default_instance_->InitAsDefaultInstance();
  GetDelegationTokenRequestProto::default_instance_->InitAsDefaultInstance();
  GetDelegationTokenResponseProto::default_instance_->InitAsDefaultInstance();
  RenewDelegationTokenRequestProto::default_instance_->InitAsDefaultInstance();
  RenewDelegationTokenResponseProto::default_instance_->InitAsDefaultInstance();
  CancelDelegationTokenRequestProto::default_instance_->InitAsDefaultInstance();
  CancelDelegationTokenResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fSecurity_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fSecurity_2eproto {
  StaticDescriptorInitializer_common_2fSecurity_2eproto() {
    protobuf_AddDesc_common_2fSecurity_2eproto();
  }
} static_descriptor_initializer_common_2fSecurity_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int TokenProto::kIdentifierFieldNumber;
const int TokenProto::kPasswordFieldNumber;
const int TokenProto::kKindFieldNumber;
const int TokenProto::kServiceFieldNumber;
#endif  // !_MSC_VER

TokenProto::TokenProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void TokenProto::InitAsDefaultInstance() {
}

TokenProto::TokenProto(const TokenProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void TokenProto::SharedCtor() {
  _cached_size_ = 0;
  identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  password_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  kind_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  service_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

TokenProto::~TokenProto() {
  SharedDtor();
}

void TokenProto::SharedDtor() {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    delete identifier_;
  }
  if (password_ != &::google::protobuf::internal::kEmptyString) {
    delete password_;
  }
  if (kind_ != &::google::protobuf::internal::kEmptyString) {
    delete kind_;
  }
  if (service_ != &::google::protobuf::internal::kEmptyString) {
    delete service_;
  }
  if (this != default_instance_) {
  }
}

void TokenProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TokenProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TokenProto_descriptor_;
}

const TokenProto& TokenProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

TokenProto* TokenProto::default_instance_ = NULL;

TokenProto* TokenProto::New() const {
  return new TokenProto;
}

void TokenProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_identifier()) {
      if (identifier_ != &::google::protobuf::internal::kEmptyString) {
        identifier_->clear();
      }
    }
    if (has_password()) {
      if (password_ != &::google::protobuf::internal::kEmptyString) {
        password_->clear();
      }
    }
    if (has_kind()) {
      if (kind_ != &::google::protobuf::internal::kEmptyString) {
        kind_->clear();
      }
    }
    if (has_service()) {
      if (service_ != &::google::protobuf::internal::kEmptyString) {
        service_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool TokenProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required bytes identifier = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_identifier()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_password;
        break;
      }

      // required bytes password = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_password:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_password()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_kind;
        break;
      }

      // required string kind = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_kind:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_kind()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->kind().data(), this->kind().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_service;
        break;
      }

      // required string service = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_service:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_service()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->service().data(), this->service().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void TokenProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required bytes identifier = 1;
  if (has_identifier()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      1, this->identifier(), output);
  }

  // required bytes password = 2;
  if (has_password()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      2, this->password(), output);
  }

  // required string kind = 3;
  if (has_kind()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->kind().data(), this->kind().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->kind(), output);
  }

  // required string service = 4;
  if (has_service()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->service().data(), this->service().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->service(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* TokenProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required bytes identifier = 1;
  if (has_identifier()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->identifier(), target);
  }

  // required bytes password = 2;
  if (has_password()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->password(), target);
  }

  // required string kind = 3;
  if (has_kind()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->kind().data(), this->kind().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->kind(), target);
  }

  // required string service = 4;
  if (has_service()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->service().data(), this->service().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->service(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int TokenProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required bytes identifier = 1;
    if (has_identifier()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->identifier());
    }

    // required bytes password = 2;
    if (has_password()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->password());
    }

    // required string kind = 3;
    if (has_kind()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->kind());
    }

    // required string service = 4;
    if (has_service()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->service());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TokenProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const TokenProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const TokenProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void TokenProto::MergeFrom(const TokenProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_identifier()) {
      set_identifier(from.identifier());
    }
    if (from.has_password()) {
      set_password(from.password());
    }
    if (from.has_kind()) {
      set_kind(from.kind());
    }
    if (from.has_service()) {
      set_service(from.service());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void TokenProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TokenProto::CopyFrom(const TokenProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TokenProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000000f) != 0x0000000f) return false;

  return true;
}

void TokenProto::Swap(TokenProto* other) {
  if (other != this) {
    std::swap(identifier_, other->identifier_);
    std::swap(password_, other->password_);
    std::swap(kind_, other->kind_);
    std::swap(service_, other->service_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata TokenProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TokenProto_descriptor_;
  metadata.reflection = TokenProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetDelegationTokenRequestProto::kRenewerFieldNumber;
#endif  // !_MSC_VER

GetDelegationTokenRequestProto::GetDelegationTokenRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetDelegationTokenRequestProto::InitAsDefaultInstance() {
}

GetDelegationTokenRequestProto::GetDelegationTokenRequestProto(const GetDelegationTokenRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetDelegationTokenRequestProto::SharedCtor() {
  _cached_size_ = 0;
  renewer_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetDelegationTokenRequestProto::~GetDelegationTokenRequestProto() {
  SharedDtor();
}

void GetDelegationTokenRequestProto::SharedDtor() {
  if (renewer_ != &::google::protobuf::internal::kEmptyString) {
    delete renewer_;
  }
  if (this != default_instance_) {
  }
}

void GetDelegationTokenRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetDelegationTokenRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetDelegationTokenRequestProto_descriptor_;
}

const GetDelegationTokenRequestProto& GetDelegationTokenRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

GetDelegationTokenRequestProto* GetDelegationTokenRequestProto::default_instance_ = NULL;

GetDelegationTokenRequestProto* GetDelegationTokenRequestProto::New() const {
  return new GetDelegationTokenRequestProto;
}

void GetDelegationTokenRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_renewer()) {
      if (renewer_ != &::google::protobuf::internal::kEmptyString) {
        renewer_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetDelegationTokenRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string renewer = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_renewer()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->renewer().data(), this->renewer().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetDelegationTokenRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string renewer = 1;
  if (has_renewer()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->renewer().data(), this->renewer().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->renewer(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetDelegationTokenRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string renewer = 1;
  if (has_renewer()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->renewer().data(), this->renewer().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->renewer(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetDelegationTokenRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string renewer = 1;
    if (has_renewer()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->renewer());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetDelegationTokenRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetDelegationTokenRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetDelegationTokenRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetDelegationTokenRequestProto::MergeFrom(const GetDelegationTokenRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_renewer()) {
      set_renewer(from.renewer());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetDelegationTokenRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetDelegationTokenRequestProto::CopyFrom(const GetDelegationTokenRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetDelegationTokenRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetDelegationTokenRequestProto::Swap(GetDelegationTokenRequestProto* other) {
  if (other != this) {
    std::swap(renewer_, other->renewer_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetDelegationTokenRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetDelegationTokenRequestProto_descriptor_;
  metadata.reflection = GetDelegationTokenRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetDelegationTokenResponseProto::kTokenFieldNumber;
#endif  // !_MSC_VER

GetDelegationTokenResponseProto::GetDelegationTokenResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetDelegationTokenResponseProto::InitAsDefaultInstance() {
  token_ = const_cast< ::hadoop::common::TokenProto*>(&::hadoop::common::TokenProto::default_instance());
}

GetDelegationTokenResponseProto::GetDelegationTokenResponseProto(const GetDelegationTokenResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetDelegationTokenResponseProto::SharedCtor() {
  _cached_size_ = 0;
  token_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetDelegationTokenResponseProto::~GetDelegationTokenResponseProto() {
  SharedDtor();
}

void GetDelegationTokenResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete token_;
  }
}

void GetDelegationTokenResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetDelegationTokenResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetDelegationTokenResponseProto_descriptor_;
}

const GetDelegationTokenResponseProto& GetDelegationTokenResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

GetDelegationTokenResponseProto* GetDelegationTokenResponseProto::default_instance_ = NULL;

GetDelegationTokenResponseProto* GetDelegationTokenResponseProto::New() const {
  return new GetDelegationTokenResponseProto;
}

void GetDelegationTokenResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_token()) {
      if (token_ != NULL) token_->::hadoop::common::TokenProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetDelegationTokenResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .hadoop.common.TokenProto token = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_token()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetDelegationTokenResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional .hadoop.common.TokenProto token = 1;
  if (has_token()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->token(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetDelegationTokenResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional .hadoop.common.TokenProto token = 1;
  if (has_token()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->token(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetDelegationTokenResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional .hadoop.common.TokenProto token = 1;
    if (has_token()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->token());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetDelegationTokenResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetDelegationTokenResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetDelegationTokenResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetDelegationTokenResponseProto::MergeFrom(const GetDelegationTokenResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_token()) {
      mutable_token()->::hadoop::common::TokenProto::MergeFrom(from.token());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetDelegationTokenResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetDelegationTokenResponseProto::CopyFrom(const GetDelegationTokenResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetDelegationTokenResponseProto::IsInitialized() const {

  if (has_token()) {
    if (!this->token().IsInitialized()) return false;
  }
  return true;
}

void GetDelegationTokenResponseProto::Swap(GetDelegationTokenResponseProto* other) {
  if (other != this) {
    std::swap(token_, other->token_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetDelegationTokenResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetDelegationTokenResponseProto_descriptor_;
  metadata.reflection = GetDelegationTokenResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RenewDelegationTokenRequestProto::kTokenFieldNumber;
#endif  // !_MSC_VER

RenewDelegationTokenRequestProto::RenewDelegationTokenRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RenewDelegationTokenRequestProto::InitAsDefaultInstance() {
  token_ = const_cast< ::hadoop::common::TokenProto*>(&::hadoop::common::TokenProto::default_instance());
}

RenewDelegationTokenRequestProto::RenewDelegationTokenRequestProto(const RenewDelegationTokenRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RenewDelegationTokenRequestProto::SharedCtor() {
  _cached_size_ = 0;
  token_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RenewDelegationTokenRequestProto::~RenewDelegationTokenRequestProto() {
  SharedDtor();
}

void RenewDelegationTokenRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete token_;
  }
}

void RenewDelegationTokenRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RenewDelegationTokenRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RenewDelegationTokenRequestProto_descriptor_;
}

const RenewDelegationTokenRequestProto& RenewDelegationTokenRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

RenewDelegationTokenRequestProto* RenewDelegationTokenRequestProto::default_instance_ = NULL;

RenewDelegationTokenRequestProto* RenewDelegationTokenRequestProto::New() const {
  return new RenewDelegationTokenRequestProto;
}

void RenewDelegationTokenRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_token()) {
      if (token_ != NULL) token_->::hadoop::common::TokenProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RenewDelegationTokenRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.common.TokenProto token = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_token()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RenewDelegationTokenRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.common.TokenProto token = 1;
  if (has_token()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->token(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RenewDelegationTokenRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.common.TokenProto token = 1;
  if (has_token()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->token(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RenewDelegationTokenRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.common.TokenProto token = 1;
    if (has_token()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->token());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RenewDelegationTokenRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RenewDelegationTokenRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RenewDelegationTokenRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RenewDelegationTokenRequestProto::MergeFrom(const RenewDelegationTokenRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_token()) {
      mutable_token()->::hadoop::common::TokenProto::MergeFrom(from.token());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RenewDelegationTokenRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RenewDelegationTokenRequestProto::CopyFrom(const RenewDelegationTokenRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RenewDelegationTokenRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_token()) {
    if (!this->token().IsInitialized()) return false;
  }
  return true;
}

void RenewDelegationTokenRequestProto::Swap(RenewDelegationTokenRequestProto* other) {
  if (other != this) {
    std::swap(token_, other->token_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RenewDelegationTokenRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RenewDelegationTokenRequestProto_descriptor_;
  metadata.reflection = RenewDelegationTokenRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RenewDelegationTokenResponseProto::kNewExpiryTimeFieldNumber;
#endif  // !_MSC_VER

RenewDelegationTokenResponseProto::RenewDelegationTokenResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RenewDelegationTokenResponseProto::InitAsDefaultInstance() {
}

RenewDelegationTokenResponseProto::RenewDelegationTokenResponseProto(const RenewDelegationTokenResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RenewDelegationTokenResponseProto::SharedCtor() {
  _cached_size_ = 0;
  newexpirytime_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RenewDelegationTokenResponseProto::~RenewDelegationTokenResponseProto() {
  SharedDtor();
}

void RenewDelegationTokenResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RenewDelegationTokenResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RenewDelegationTokenResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RenewDelegationTokenResponseProto_descriptor_;
}

const RenewDelegationTokenResponseProto& RenewDelegationTokenResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

RenewDelegationTokenResponseProto* RenewDelegationTokenResponseProto::default_instance_ = NULL;

RenewDelegationTokenResponseProto* RenewDelegationTokenResponseProto::New() const {
  return new RenewDelegationTokenResponseProto;
}

void RenewDelegationTokenResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    newexpirytime_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RenewDelegationTokenResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 newExpiryTime = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &newexpirytime_)));
          set_has_newexpirytime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RenewDelegationTokenResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 newExpiryTime = 1;
  if (has_newexpirytime()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->newexpirytime(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RenewDelegationTokenResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 newExpiryTime = 1;
  if (has_newexpirytime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->newexpirytime(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RenewDelegationTokenResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 newExpiryTime = 1;
    if (has_newexpirytime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->newexpirytime());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RenewDelegationTokenResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RenewDelegationTokenResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RenewDelegationTokenResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RenewDelegationTokenResponseProto::MergeFrom(const RenewDelegationTokenResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_newexpirytime()) {
      set_newexpirytime(from.newexpirytime());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RenewDelegationTokenResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RenewDelegationTokenResponseProto::CopyFrom(const RenewDelegationTokenResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RenewDelegationTokenResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void RenewDelegationTokenResponseProto::Swap(RenewDelegationTokenResponseProto* other) {
  if (other != this) {
    std::swap(newexpirytime_, other->newexpirytime_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RenewDelegationTokenResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RenewDelegationTokenResponseProto_descriptor_;
  metadata.reflection = RenewDelegationTokenResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CancelDelegationTokenRequestProto::kTokenFieldNumber;
#endif  // !_MSC_VER

CancelDelegationTokenRequestProto::CancelDelegationTokenRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CancelDelegationTokenRequestProto::InitAsDefaultInstance() {
  token_ = const_cast< ::hadoop::common::TokenProto*>(&::hadoop::common::TokenProto::default_instance());
}

CancelDelegationTokenRequestProto::CancelDelegationTokenRequestProto(const CancelDelegationTokenRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CancelDelegationTokenRequestProto::SharedCtor() {
  _cached_size_ = 0;
  token_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CancelDelegationTokenRequestProto::~CancelDelegationTokenRequestProto() {
  SharedDtor();
}

void CancelDelegationTokenRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete token_;
  }
}

void CancelDelegationTokenRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CancelDelegationTokenRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CancelDelegationTokenRequestProto_descriptor_;
}

const CancelDelegationTokenRequestProto& CancelDelegationTokenRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

CancelDelegationTokenRequestProto* CancelDelegationTokenRequestProto::default_instance_ = NULL;

CancelDelegationTokenRequestProto* CancelDelegationTokenRequestProto::New() const {
  return new CancelDelegationTokenRequestProto;
}

void CancelDelegationTokenRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_token()) {
      if (token_ != NULL) token_->::hadoop::common::TokenProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CancelDelegationTokenRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.common.TokenProto token = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_token()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CancelDelegationTokenRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.common.TokenProto token = 1;
  if (has_token()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->token(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CancelDelegationTokenRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.common.TokenProto token = 1;
  if (has_token()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->token(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CancelDelegationTokenRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.common.TokenProto token = 1;
    if (has_token()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->token());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CancelDelegationTokenRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CancelDelegationTokenRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CancelDelegationTokenRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CancelDelegationTokenRequestProto::MergeFrom(const CancelDelegationTokenRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_token()) {
      mutable_token()->::hadoop::common::TokenProto::MergeFrom(from.token());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CancelDelegationTokenRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CancelDelegationTokenRequestProto::CopyFrom(const CancelDelegationTokenRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CancelDelegationTokenRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_token()) {
    if (!this->token().IsInitialized()) return false;
  }
  return true;
}

void CancelDelegationTokenRequestProto::Swap(CancelDelegationTokenRequestProto* other) {
  if (other != this) {
    std::swap(token_, other->token_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CancelDelegationTokenRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CancelDelegationTokenRequestProto_descriptor_;
  metadata.reflection = CancelDelegationTokenRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

CancelDelegationTokenResponseProto::CancelDelegationTokenResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CancelDelegationTokenResponseProto::InitAsDefaultInstance() {
}

CancelDelegationTokenResponseProto::CancelDelegationTokenResponseProto(const CancelDelegationTokenResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CancelDelegationTokenResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CancelDelegationTokenResponseProto::~CancelDelegationTokenResponseProto() {
  SharedDtor();
}

void CancelDelegationTokenResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CancelDelegationTokenResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CancelDelegationTokenResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CancelDelegationTokenResponseProto_descriptor_;
}

const CancelDelegationTokenResponseProto& CancelDelegationTokenResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fSecurity_2eproto();
  return *default_instance_;
}

CancelDelegationTokenResponseProto* CancelDelegationTokenResponseProto::default_instance_ = NULL;

CancelDelegationTokenResponseProto* CancelDelegationTokenResponseProto::New() const {
  return new CancelDelegationTokenResponseProto;
}

void CancelDelegationTokenResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CancelDelegationTokenResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void CancelDelegationTokenResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CancelDelegationTokenResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CancelDelegationTokenResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CancelDelegationTokenResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CancelDelegationTokenResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CancelDelegationTokenResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CancelDelegationTokenResponseProto::MergeFrom(const CancelDelegationTokenResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CancelDelegationTokenResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CancelDelegationTokenResponseProto::CopyFrom(const CancelDelegationTokenResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CancelDelegationTokenResponseProto::IsInitialized() const {

  return true;
}

void CancelDelegationTokenResponseProto::Swap(CancelDelegationTokenResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CancelDelegationTokenResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CancelDelegationTokenResponseProto_descriptor_;
  metadata.reflection = CancelDelegationTokenResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
