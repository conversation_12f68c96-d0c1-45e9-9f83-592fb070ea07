// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/GetUserMappingsProtocol.proto

#ifndef PROTOBUF_common_2fGetUserMappingsProtocol_2eproto__INCLUDED
#define PROTOBUF_common_2fGetUserMappingsProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
void protobuf_AssignDesc_common_2fGetUserMappingsProtocol_2eproto();
void protobuf_ShutdownFile_common_2fGetUserMappingsProtocol_2eproto();

class GetGroupsForUserRequestProto;
class GetGroupsForUserResponseProto;

// ===================================================================

class GetGroupsForUserRequestProto : public ::google::protobuf::Message {
 public:
  GetGroupsForUserRequestProto();
  virtual ~GetGroupsForUserRequestProto();

  GetGroupsForUserRequestProto(const GetGroupsForUserRequestProto& from);

  inline GetGroupsForUserRequestProto& operator=(const GetGroupsForUserRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetGroupsForUserRequestProto& default_instance();

  void Swap(GetGroupsForUserRequestProto* other);

  // implements Message ----------------------------------------------

  GetGroupsForUserRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetGroupsForUserRequestProto& from);
  void MergeFrom(const GetGroupsForUserRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string user = 1;
  inline bool has_user() const;
  inline void clear_user();
  static const int kUserFieldNumber = 1;
  inline const ::std::string& user() const;
  inline void set_user(const ::std::string& value);
  inline void set_user(const char* value);
  inline void set_user(const char* value, size_t size);
  inline ::std::string* mutable_user();
  inline ::std::string* release_user();
  inline void set_allocated_user(::std::string* user);

  // @@protoc_insertion_point(class_scope:hadoop.common.GetGroupsForUserRequestProto)
 private:
  inline void set_has_user();
  inline void clear_has_user();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* user_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fGetUserMappingsProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fGetUserMappingsProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetGroupsForUserRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetGroupsForUserResponseProto : public ::google::protobuf::Message {
 public:
  GetGroupsForUserResponseProto();
  virtual ~GetGroupsForUserResponseProto();

  GetGroupsForUserResponseProto(const GetGroupsForUserResponseProto& from);

  inline GetGroupsForUserResponseProto& operator=(const GetGroupsForUserResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetGroupsForUserResponseProto& default_instance();

  void Swap(GetGroupsForUserResponseProto* other);

  // implements Message ----------------------------------------------

  GetGroupsForUserResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetGroupsForUserResponseProto& from);
  void MergeFrom(const GetGroupsForUserResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string groups = 1;
  inline int groups_size() const;
  inline void clear_groups();
  static const int kGroupsFieldNumber = 1;
  inline const ::std::string& groups(int index) const;
  inline ::std::string* mutable_groups(int index);
  inline void set_groups(int index, const ::std::string& value);
  inline void set_groups(int index, const char* value);
  inline void set_groups(int index, const char* value, size_t size);
  inline ::std::string* add_groups();
  inline void add_groups(const ::std::string& value);
  inline void add_groups(const char* value);
  inline void add_groups(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& groups() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_groups();

  // @@protoc_insertion_point(class_scope:hadoop.common.GetGroupsForUserResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::std::string> groups_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fGetUserMappingsProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fGetUserMappingsProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetGroupsForUserResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// GetGroupsForUserRequestProto

// required string user = 1;
inline bool GetGroupsForUserRequestProto::has_user() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetGroupsForUserRequestProto::set_has_user() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetGroupsForUserRequestProto::clear_has_user() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetGroupsForUserRequestProto::clear_user() {
  if (user_ != &::google::protobuf::internal::kEmptyString) {
    user_->clear();
  }
  clear_has_user();
}
inline const ::std::string& GetGroupsForUserRequestProto::user() const {
  return *user_;
}
inline void GetGroupsForUserRequestProto::set_user(const ::std::string& value) {
  set_has_user();
  if (user_ == &::google::protobuf::internal::kEmptyString) {
    user_ = new ::std::string;
  }
  user_->assign(value);
}
inline void GetGroupsForUserRequestProto::set_user(const char* value) {
  set_has_user();
  if (user_ == &::google::protobuf::internal::kEmptyString) {
    user_ = new ::std::string;
  }
  user_->assign(value);
}
inline void GetGroupsForUserRequestProto::set_user(const char* value, size_t size) {
  set_has_user();
  if (user_ == &::google::protobuf::internal::kEmptyString) {
    user_ = new ::std::string;
  }
  user_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetGroupsForUserRequestProto::mutable_user() {
  set_has_user();
  if (user_ == &::google::protobuf::internal::kEmptyString) {
    user_ = new ::std::string;
  }
  return user_;
}
inline ::std::string* GetGroupsForUserRequestProto::release_user() {
  clear_has_user();
  if (user_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = user_;
    user_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetGroupsForUserRequestProto::set_allocated_user(::std::string* user) {
  if (user_ != &::google::protobuf::internal::kEmptyString) {
    delete user_;
  }
  if (user) {
    set_has_user();
    user_ = user;
  } else {
    clear_has_user();
    user_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// GetGroupsForUserResponseProto

// repeated string groups = 1;
inline int GetGroupsForUserResponseProto::groups_size() const {
  return groups_.size();
}
inline void GetGroupsForUserResponseProto::clear_groups() {
  groups_.Clear();
}
inline const ::std::string& GetGroupsForUserResponseProto::groups(int index) const {
  return groups_.Get(index);
}
inline ::std::string* GetGroupsForUserResponseProto::mutable_groups(int index) {
  return groups_.Mutable(index);
}
inline void GetGroupsForUserResponseProto::set_groups(int index, const ::std::string& value) {
  groups_.Mutable(index)->assign(value);
}
inline void GetGroupsForUserResponseProto::set_groups(int index, const char* value) {
  groups_.Mutable(index)->assign(value);
}
inline void GetGroupsForUserResponseProto::set_groups(int index, const char* value, size_t size) {
  groups_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetGroupsForUserResponseProto::add_groups() {
  return groups_.Add();
}
inline void GetGroupsForUserResponseProto::add_groups(const ::std::string& value) {
  groups_.Add()->assign(value);
}
inline void GetGroupsForUserResponseProto::add_groups(const char* value) {
  groups_.Add()->assign(value);
}
inline void GetGroupsForUserResponseProto::add_groups(const char* value, size_t size) {
  groups_.Add()->assign(reinterpret_cast<const char*>(value), size);
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
GetGroupsForUserResponseProto::groups() const {
  return groups_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
GetGroupsForUserResponseProto::mutable_groups() {
  return &groups_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fGetUserMappingsProtocol_2eproto__INCLUDED
