// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/HAServiceProtocol.proto

#ifndef PROTOBUF_common_2fHAServiceProtocol_2eproto__INCLUDED
#define PROTOBUF_common_2fHAServiceProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

class HAStateChangeRequestInfoProto;
class MonitorHealthRequestProto;
class MonitorHealthResponseProto;
class TransitionToActiveRequestProto;
class TransitionToActiveResponseProto;
class TransitionToStandbyRequestProto;
class TransitionToStandbyResponseProto;
class GetServiceStatusRequestProto;
class GetServiceStatusResponseProto;

enum HAServiceStateProto {
  INITIALIZING = 0,
  ACTIVE = 1,
  STANDBY = 2
};
bool HAServiceStateProto_IsValid(int value);
const HAServiceStateProto HAServiceStateProto_MIN = INITIALIZING;
const HAServiceStateProto HAServiceStateProto_MAX = STANDBY;
const int HAServiceStateProto_ARRAYSIZE = HAServiceStateProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* HAServiceStateProto_descriptor();
inline const ::std::string& HAServiceStateProto_Name(HAServiceStateProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    HAServiceStateProto_descriptor(), value);
}
inline bool HAServiceStateProto_Parse(
    const ::std::string& name, HAServiceStateProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<HAServiceStateProto>(
    HAServiceStateProto_descriptor(), name, value);
}
enum HARequestSource {
  REQUEST_BY_USER = 0,
  REQUEST_BY_USER_FORCED = 1,
  REQUEST_BY_ZKFC = 2
};
bool HARequestSource_IsValid(int value);
const HARequestSource HARequestSource_MIN = REQUEST_BY_USER;
const HARequestSource HARequestSource_MAX = REQUEST_BY_ZKFC;
const int HARequestSource_ARRAYSIZE = HARequestSource_MAX + 1;

const ::google::protobuf::EnumDescriptor* HARequestSource_descriptor();
inline const ::std::string& HARequestSource_Name(HARequestSource value) {
  return ::google::protobuf::internal::NameOfEnum(
    HARequestSource_descriptor(), value);
}
inline bool HARequestSource_Parse(
    const ::std::string& name, HARequestSource* value) {
  return ::google::protobuf::internal::ParseNamedEnum<HARequestSource>(
    HARequestSource_descriptor(), name, value);
}
// ===================================================================

class HAStateChangeRequestInfoProto : public ::google::protobuf::Message {
 public:
  HAStateChangeRequestInfoProto();
  virtual ~HAStateChangeRequestInfoProto();

  HAStateChangeRequestInfoProto(const HAStateChangeRequestInfoProto& from);

  inline HAStateChangeRequestInfoProto& operator=(const HAStateChangeRequestInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const HAStateChangeRequestInfoProto& default_instance();

  void Swap(HAStateChangeRequestInfoProto* other);

  // implements Message ----------------------------------------------

  HAStateChangeRequestInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const HAStateChangeRequestInfoProto& from);
  void MergeFrom(const HAStateChangeRequestInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.common.HARequestSource reqSource = 1;
  inline bool has_reqsource() const;
  inline void clear_reqsource();
  static const int kReqSourceFieldNumber = 1;
  inline ::hadoop::common::HARequestSource reqsource() const;
  inline void set_reqsource(::hadoop::common::HARequestSource value);

  // @@protoc_insertion_point(class_scope:hadoop.common.HAStateChangeRequestInfoProto)
 private:
  inline void set_has_reqsource();
  inline void clear_has_reqsource();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int reqsource_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static HAStateChangeRequestInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class MonitorHealthRequestProto : public ::google::protobuf::Message {
 public:
  MonitorHealthRequestProto();
  virtual ~MonitorHealthRequestProto();

  MonitorHealthRequestProto(const MonitorHealthRequestProto& from);

  inline MonitorHealthRequestProto& operator=(const MonitorHealthRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MonitorHealthRequestProto& default_instance();

  void Swap(MonitorHealthRequestProto* other);

  // implements Message ----------------------------------------------

  MonitorHealthRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MonitorHealthRequestProto& from);
  void MergeFrom(const MonitorHealthRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.MonitorHealthRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static MonitorHealthRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class MonitorHealthResponseProto : public ::google::protobuf::Message {
 public:
  MonitorHealthResponseProto();
  virtual ~MonitorHealthResponseProto();

  MonitorHealthResponseProto(const MonitorHealthResponseProto& from);

  inline MonitorHealthResponseProto& operator=(const MonitorHealthResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MonitorHealthResponseProto& default_instance();

  void Swap(MonitorHealthResponseProto* other);

  // implements Message ----------------------------------------------

  MonitorHealthResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MonitorHealthResponseProto& from);
  void MergeFrom(const MonitorHealthResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.MonitorHealthResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static MonitorHealthResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class TransitionToActiveRequestProto : public ::google::protobuf::Message {
 public:
  TransitionToActiveRequestProto();
  virtual ~TransitionToActiveRequestProto();

  TransitionToActiveRequestProto(const TransitionToActiveRequestProto& from);

  inline TransitionToActiveRequestProto& operator=(const TransitionToActiveRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TransitionToActiveRequestProto& default_instance();

  void Swap(TransitionToActiveRequestProto* other);

  // implements Message ----------------------------------------------

  TransitionToActiveRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TransitionToActiveRequestProto& from);
  void MergeFrom(const TransitionToActiveRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::common::HAStateChangeRequestInfoProto& reqinfo() const;
  inline ::hadoop::common::HAStateChangeRequestInfoProto* mutable_reqinfo();
  inline ::hadoop::common::HAStateChangeRequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::common::HAStateChangeRequestInfoProto* reqinfo);

  // @@protoc_insertion_point(class_scope:hadoop.common.TransitionToActiveRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::common::HAStateChangeRequestInfoProto* reqinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static TransitionToActiveRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class TransitionToActiveResponseProto : public ::google::protobuf::Message {
 public:
  TransitionToActiveResponseProto();
  virtual ~TransitionToActiveResponseProto();

  TransitionToActiveResponseProto(const TransitionToActiveResponseProto& from);

  inline TransitionToActiveResponseProto& operator=(const TransitionToActiveResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TransitionToActiveResponseProto& default_instance();

  void Swap(TransitionToActiveResponseProto* other);

  // implements Message ----------------------------------------------

  TransitionToActiveResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TransitionToActiveResponseProto& from);
  void MergeFrom(const TransitionToActiveResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.TransitionToActiveResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static TransitionToActiveResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class TransitionToStandbyRequestProto : public ::google::protobuf::Message {
 public:
  TransitionToStandbyRequestProto();
  virtual ~TransitionToStandbyRequestProto();

  TransitionToStandbyRequestProto(const TransitionToStandbyRequestProto& from);

  inline TransitionToStandbyRequestProto& operator=(const TransitionToStandbyRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TransitionToStandbyRequestProto& default_instance();

  void Swap(TransitionToStandbyRequestProto* other);

  // implements Message ----------------------------------------------

  TransitionToStandbyRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TransitionToStandbyRequestProto& from);
  void MergeFrom(const TransitionToStandbyRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::common::HAStateChangeRequestInfoProto& reqinfo() const;
  inline ::hadoop::common::HAStateChangeRequestInfoProto* mutable_reqinfo();
  inline ::hadoop::common::HAStateChangeRequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::common::HAStateChangeRequestInfoProto* reqinfo);

  // @@protoc_insertion_point(class_scope:hadoop.common.TransitionToStandbyRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::common::HAStateChangeRequestInfoProto* reqinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static TransitionToStandbyRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class TransitionToStandbyResponseProto : public ::google::protobuf::Message {
 public:
  TransitionToStandbyResponseProto();
  virtual ~TransitionToStandbyResponseProto();

  TransitionToStandbyResponseProto(const TransitionToStandbyResponseProto& from);

  inline TransitionToStandbyResponseProto& operator=(const TransitionToStandbyResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TransitionToStandbyResponseProto& default_instance();

  void Swap(TransitionToStandbyResponseProto* other);

  // implements Message ----------------------------------------------

  TransitionToStandbyResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TransitionToStandbyResponseProto& from);
  void MergeFrom(const TransitionToStandbyResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.TransitionToStandbyResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static TransitionToStandbyResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetServiceStatusRequestProto : public ::google::protobuf::Message {
 public:
  GetServiceStatusRequestProto();
  virtual ~GetServiceStatusRequestProto();

  GetServiceStatusRequestProto(const GetServiceStatusRequestProto& from);

  inline GetServiceStatusRequestProto& operator=(const GetServiceStatusRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetServiceStatusRequestProto& default_instance();

  void Swap(GetServiceStatusRequestProto* other);

  // implements Message ----------------------------------------------

  GetServiceStatusRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetServiceStatusRequestProto& from);
  void MergeFrom(const GetServiceStatusRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.common.GetServiceStatusRequestProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetServiceStatusRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetServiceStatusResponseProto : public ::google::protobuf::Message {
 public:
  GetServiceStatusResponseProto();
  virtual ~GetServiceStatusResponseProto();

  GetServiceStatusResponseProto(const GetServiceStatusResponseProto& from);

  inline GetServiceStatusResponseProto& operator=(const GetServiceStatusResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetServiceStatusResponseProto& default_instance();

  void Swap(GetServiceStatusResponseProto* other);

  // implements Message ----------------------------------------------

  GetServiceStatusResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetServiceStatusResponseProto& from);
  void MergeFrom(const GetServiceStatusResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.common.HAServiceStateProto state = 1;
  inline bool has_state() const;
  inline void clear_state();
  static const int kStateFieldNumber = 1;
  inline ::hadoop::common::HAServiceStateProto state() const;
  inline void set_state(::hadoop::common::HAServiceStateProto value);

  // optional bool readyToBecomeActive = 2;
  inline bool has_readytobecomeactive() const;
  inline void clear_readytobecomeactive();
  static const int kReadyToBecomeActiveFieldNumber = 2;
  inline bool readytobecomeactive() const;
  inline void set_readytobecomeactive(bool value);

  // optional string notReadyReason = 3;
  inline bool has_notreadyreason() const;
  inline void clear_notreadyreason();
  static const int kNotReadyReasonFieldNumber = 3;
  inline const ::std::string& notreadyreason() const;
  inline void set_notreadyreason(const ::std::string& value);
  inline void set_notreadyreason(const char* value);
  inline void set_notreadyreason(const char* value, size_t size);
  inline ::std::string* mutable_notreadyreason();
  inline ::std::string* release_notreadyreason();
  inline void set_allocated_notreadyreason(::std::string* notreadyreason);

  // @@protoc_insertion_point(class_scope:hadoop.common.GetServiceStatusResponseProto)
 private:
  inline void set_has_state();
  inline void clear_has_state();
  inline void set_has_readytobecomeactive();
  inline void clear_has_readytobecomeactive();
  inline void set_has_notreadyreason();
  inline void clear_has_notreadyreason();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int state_;
  bool readytobecomeactive_;
  ::std::string* notreadyreason_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_AssignDesc_common_2fHAServiceProtocol_2eproto();
  friend void protobuf_ShutdownFile_common_2fHAServiceProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetServiceStatusResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// HAStateChangeRequestInfoProto

// required .hadoop.common.HARequestSource reqSource = 1;
inline bool HAStateChangeRequestInfoProto::has_reqsource() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void HAStateChangeRequestInfoProto::set_has_reqsource() {
  _has_bits_[0] |= 0x00000001u;
}
inline void HAStateChangeRequestInfoProto::clear_has_reqsource() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void HAStateChangeRequestInfoProto::clear_reqsource() {
  reqsource_ = 0;
  clear_has_reqsource();
}
inline ::hadoop::common::HARequestSource HAStateChangeRequestInfoProto::reqsource() const {
  return static_cast< ::hadoop::common::HARequestSource >(reqsource_);
}
inline void HAStateChangeRequestInfoProto::set_reqsource(::hadoop::common::HARequestSource value) {
  assert(::hadoop::common::HARequestSource_IsValid(value));
  set_has_reqsource();
  reqsource_ = value;
}

// -------------------------------------------------------------------

// MonitorHealthRequestProto

// -------------------------------------------------------------------

// MonitorHealthResponseProto

// -------------------------------------------------------------------

// TransitionToActiveRequestProto

// required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
inline bool TransitionToActiveRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TransitionToActiveRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TransitionToActiveRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TransitionToActiveRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::common::HAStateChangeRequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::common::HAStateChangeRequestInfoProto& TransitionToActiveRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::common::HAStateChangeRequestInfoProto* TransitionToActiveRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::common::HAStateChangeRequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::common::HAStateChangeRequestInfoProto* TransitionToActiveRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::common::HAStateChangeRequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void TransitionToActiveRequestProto::set_allocated_reqinfo(::hadoop::common::HAStateChangeRequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// -------------------------------------------------------------------

// TransitionToActiveResponseProto

// -------------------------------------------------------------------

// TransitionToStandbyRequestProto

// required .hadoop.common.HAStateChangeRequestInfoProto reqInfo = 1;
inline bool TransitionToStandbyRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TransitionToStandbyRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TransitionToStandbyRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TransitionToStandbyRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::common::HAStateChangeRequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::common::HAStateChangeRequestInfoProto& TransitionToStandbyRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::common::HAStateChangeRequestInfoProto* TransitionToStandbyRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::common::HAStateChangeRequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::common::HAStateChangeRequestInfoProto* TransitionToStandbyRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::common::HAStateChangeRequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void TransitionToStandbyRequestProto::set_allocated_reqinfo(::hadoop::common::HAStateChangeRequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// -------------------------------------------------------------------

// TransitionToStandbyResponseProto

// -------------------------------------------------------------------

// GetServiceStatusRequestProto

// -------------------------------------------------------------------

// GetServiceStatusResponseProto

// required .hadoop.common.HAServiceStateProto state = 1;
inline bool GetServiceStatusResponseProto::has_state() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetServiceStatusResponseProto::set_has_state() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetServiceStatusResponseProto::clear_has_state() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetServiceStatusResponseProto::clear_state() {
  state_ = 0;
  clear_has_state();
}
inline ::hadoop::common::HAServiceStateProto GetServiceStatusResponseProto::state() const {
  return static_cast< ::hadoop::common::HAServiceStateProto >(state_);
}
inline void GetServiceStatusResponseProto::set_state(::hadoop::common::HAServiceStateProto value) {
  assert(::hadoop::common::HAServiceStateProto_IsValid(value));
  set_has_state();
  state_ = value;
}

// optional bool readyToBecomeActive = 2;
inline bool GetServiceStatusResponseProto::has_readytobecomeactive() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GetServiceStatusResponseProto::set_has_readytobecomeactive() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GetServiceStatusResponseProto::clear_has_readytobecomeactive() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GetServiceStatusResponseProto::clear_readytobecomeactive() {
  readytobecomeactive_ = false;
  clear_has_readytobecomeactive();
}
inline bool GetServiceStatusResponseProto::readytobecomeactive() const {
  return readytobecomeactive_;
}
inline void GetServiceStatusResponseProto::set_readytobecomeactive(bool value) {
  set_has_readytobecomeactive();
  readytobecomeactive_ = value;
}

// optional string notReadyReason = 3;
inline bool GetServiceStatusResponseProto::has_notreadyreason() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void GetServiceStatusResponseProto::set_has_notreadyreason() {
  _has_bits_[0] |= 0x00000004u;
}
inline void GetServiceStatusResponseProto::clear_has_notreadyreason() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void GetServiceStatusResponseProto::clear_notreadyreason() {
  if (notreadyreason_ != &::google::protobuf::internal::kEmptyString) {
    notreadyreason_->clear();
  }
  clear_has_notreadyreason();
}
inline const ::std::string& GetServiceStatusResponseProto::notreadyreason() const {
  return *notreadyreason_;
}
inline void GetServiceStatusResponseProto::set_notreadyreason(const ::std::string& value) {
  set_has_notreadyreason();
  if (notreadyreason_ == &::google::protobuf::internal::kEmptyString) {
    notreadyreason_ = new ::std::string;
  }
  notreadyreason_->assign(value);
}
inline void GetServiceStatusResponseProto::set_notreadyreason(const char* value) {
  set_has_notreadyreason();
  if (notreadyreason_ == &::google::protobuf::internal::kEmptyString) {
    notreadyreason_ = new ::std::string;
  }
  notreadyreason_->assign(value);
}
inline void GetServiceStatusResponseProto::set_notreadyreason(const char* value, size_t size) {
  set_has_notreadyreason();
  if (notreadyreason_ == &::google::protobuf::internal::kEmptyString) {
    notreadyreason_ = new ::std::string;
  }
  notreadyreason_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetServiceStatusResponseProto::mutable_notreadyreason() {
  set_has_notreadyreason();
  if (notreadyreason_ == &::google::protobuf::internal::kEmptyString) {
    notreadyreason_ = new ::std::string;
  }
  return notreadyreason_;
}
inline ::std::string* GetServiceStatusResponseProto::release_notreadyreason() {
  clear_has_notreadyreason();
  if (notreadyreason_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = notreadyreason_;
    notreadyreason_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetServiceStatusResponseProto::set_allocated_notreadyreason(::std::string* notreadyreason) {
  if (notreadyreason_ != &::google::protobuf::internal::kEmptyString) {
    delete notreadyreason_;
  }
  if (notreadyreason) {
    set_has_notreadyreason();
    notreadyreason_ = notreadyreason;
  } else {
    clear_has_notreadyreason();
    notreadyreason_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::HAServiceStateProto>() {
  return ::hadoop::common::HAServiceStateProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::HARequestSource>() {
  return ::hadoop::common::HARequestSource_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fHAServiceProtocol_2eproto__INCLUDED
