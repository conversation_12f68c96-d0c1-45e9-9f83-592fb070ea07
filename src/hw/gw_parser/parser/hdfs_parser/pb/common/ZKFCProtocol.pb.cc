// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/ZKFCProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/ZKFCProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* CedeActiveRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CedeActiveRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CedeActiveResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CedeActiveResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GracefulFailoverRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GracefulFailoverRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GracefulFailoverResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GracefulFailoverResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fZKFCProtocol_2eproto() {
  protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/ZKFCProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  CedeActiveRequestProto_descriptor_ = file->message_type(0);
  static const int CedeActiveRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CedeActiveRequestProto, millistocede_),
  };
  CedeActiveRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CedeActiveRequestProto_descriptor_,
      CedeActiveRequestProto::default_instance_,
      CedeActiveRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CedeActiveRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CedeActiveRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CedeActiveRequestProto));
  CedeActiveResponseProto_descriptor_ = file->message_type(1);
  static const int CedeActiveResponseProto_offsets_[1] = {
  };
  CedeActiveResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CedeActiveResponseProto_descriptor_,
      CedeActiveResponseProto::default_instance_,
      CedeActiveResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CedeActiveResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CedeActiveResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CedeActiveResponseProto));
  GracefulFailoverRequestProto_descriptor_ = file->message_type(2);
  static const int GracefulFailoverRequestProto_offsets_[1] = {
  };
  GracefulFailoverRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GracefulFailoverRequestProto_descriptor_,
      GracefulFailoverRequestProto::default_instance_,
      GracefulFailoverRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GracefulFailoverRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GracefulFailoverRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GracefulFailoverRequestProto));
  GracefulFailoverResponseProto_descriptor_ = file->message_type(3);
  static const int GracefulFailoverResponseProto_offsets_[1] = {
  };
  GracefulFailoverResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GracefulFailoverResponseProto_descriptor_,
      GracefulFailoverResponseProto::default_instance_,
      GracefulFailoverResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GracefulFailoverResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GracefulFailoverResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GracefulFailoverResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fZKFCProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CedeActiveRequestProto_descriptor_, &CedeActiveRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CedeActiveResponseProto_descriptor_, &CedeActiveResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GracefulFailoverRequestProto_descriptor_, &GracefulFailoverRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GracefulFailoverResponseProto_descriptor_, &GracefulFailoverResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto() {
  delete CedeActiveRequestProto::default_instance_;
  delete CedeActiveRequestProto_reflection_;
  delete CedeActiveResponseProto::default_instance_;
  delete CedeActiveResponseProto_reflection_;
  delete GracefulFailoverRequestProto::default_instance_;
  delete GracefulFailoverRequestProto_reflection_;
  delete GracefulFailoverResponseProto::default_instance_;
  delete GracefulFailoverResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fZKFCProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\031common/ZKFCProtocol.proto\022\rhadoop.comm"
    "on\".\n\026CedeActiveRequestProto\022\024\n\014millisTo"
    "Cede\030\001 \002(\r\"\031\n\027CedeActiveResponseProto\"\036\n"
    "\034GracefulFailoverRequestProto\"\037\n\035Gracefu"
    "lFailoverResponseProto2\341\001\n\023ZKFCProtocolS"
    "ervice\022[\n\ncedeActive\022%.hadoop.common.Ced"
    "eActiveRequestProto\032&.hadoop.common.Cede"
    "ActiveResponseProto\022m\n\020gracefulFailover\022"
    "+.hadoop.common.GracefulFailoverRequestP"
    "roto\032,.hadoop.common.GracefulFailoverRes"
    "ponseProtoB6\n\032org.apache.hadoop.ha.proto"
    "B\022ZKFCProtocolProtos\210\001\001\240\001\001", 466);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/ZKFCProtocol.proto", &protobuf_RegisterTypes);
  CedeActiveRequestProto::default_instance_ = new CedeActiveRequestProto();
  CedeActiveResponseProto::default_instance_ = new CedeActiveResponseProto();
  GracefulFailoverRequestProto::default_instance_ = new GracefulFailoverRequestProto();
  GracefulFailoverResponseProto::default_instance_ = new GracefulFailoverResponseProto();
  CedeActiveRequestProto::default_instance_->InitAsDefaultInstance();
  CedeActiveResponseProto::default_instance_->InitAsDefaultInstance();
  GracefulFailoverRequestProto::default_instance_->InitAsDefaultInstance();
  GracefulFailoverResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fZKFCProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fZKFCProtocol_2eproto {
  StaticDescriptorInitializer_common_2fZKFCProtocol_2eproto() {
    protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fZKFCProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int CedeActiveRequestProto::kMillisToCedeFieldNumber;
#endif  // !_MSC_VER

CedeActiveRequestProto::CedeActiveRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CedeActiveRequestProto::InitAsDefaultInstance() {
}

CedeActiveRequestProto::CedeActiveRequestProto(const CedeActiveRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CedeActiveRequestProto::SharedCtor() {
  _cached_size_ = 0;
  millistocede_ = 0u;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CedeActiveRequestProto::~CedeActiveRequestProto() {
  SharedDtor();
}

void CedeActiveRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CedeActiveRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CedeActiveRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CedeActiveRequestProto_descriptor_;
}

const CedeActiveRequestProto& CedeActiveRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  return *default_instance_;
}

CedeActiveRequestProto* CedeActiveRequestProto::default_instance_ = NULL;

CedeActiveRequestProto* CedeActiveRequestProto::New() const {
  return new CedeActiveRequestProto;
}

void CedeActiveRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    millistocede_ = 0u;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CedeActiveRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint32 millisToCede = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &millistocede_)));
          set_has_millistocede();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CedeActiveRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint32 millisToCede = 1;
  if (has_millistocede()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->millistocede(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CedeActiveRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint32 millisToCede = 1;
  if (has_millistocede()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->millistocede(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CedeActiveRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint32 millisToCede = 1;
    if (has_millistocede()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->millistocede());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CedeActiveRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CedeActiveRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CedeActiveRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CedeActiveRequestProto::MergeFrom(const CedeActiveRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_millistocede()) {
      set_millistocede(from.millistocede());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CedeActiveRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CedeActiveRequestProto::CopyFrom(const CedeActiveRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CedeActiveRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void CedeActiveRequestProto::Swap(CedeActiveRequestProto* other) {
  if (other != this) {
    std::swap(millistocede_, other->millistocede_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CedeActiveRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CedeActiveRequestProto_descriptor_;
  metadata.reflection = CedeActiveRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

CedeActiveResponseProto::CedeActiveResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CedeActiveResponseProto::InitAsDefaultInstance() {
}

CedeActiveResponseProto::CedeActiveResponseProto(const CedeActiveResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CedeActiveResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CedeActiveResponseProto::~CedeActiveResponseProto() {
  SharedDtor();
}

void CedeActiveResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CedeActiveResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CedeActiveResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CedeActiveResponseProto_descriptor_;
}

const CedeActiveResponseProto& CedeActiveResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  return *default_instance_;
}

CedeActiveResponseProto* CedeActiveResponseProto::default_instance_ = NULL;

CedeActiveResponseProto* CedeActiveResponseProto::New() const {
  return new CedeActiveResponseProto;
}

void CedeActiveResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CedeActiveResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void CedeActiveResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CedeActiveResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CedeActiveResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CedeActiveResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CedeActiveResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CedeActiveResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CedeActiveResponseProto::MergeFrom(const CedeActiveResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CedeActiveResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CedeActiveResponseProto::CopyFrom(const CedeActiveResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CedeActiveResponseProto::IsInitialized() const {

  return true;
}

void CedeActiveResponseProto::Swap(CedeActiveResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CedeActiveResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CedeActiveResponseProto_descriptor_;
  metadata.reflection = CedeActiveResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

GracefulFailoverRequestProto::GracefulFailoverRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GracefulFailoverRequestProto::InitAsDefaultInstance() {
}

GracefulFailoverRequestProto::GracefulFailoverRequestProto(const GracefulFailoverRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GracefulFailoverRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GracefulFailoverRequestProto::~GracefulFailoverRequestProto() {
  SharedDtor();
}

void GracefulFailoverRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GracefulFailoverRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GracefulFailoverRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GracefulFailoverRequestProto_descriptor_;
}

const GracefulFailoverRequestProto& GracefulFailoverRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  return *default_instance_;
}

GracefulFailoverRequestProto* GracefulFailoverRequestProto::default_instance_ = NULL;

GracefulFailoverRequestProto* GracefulFailoverRequestProto::New() const {
  return new GracefulFailoverRequestProto;
}

void GracefulFailoverRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GracefulFailoverRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void GracefulFailoverRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GracefulFailoverRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GracefulFailoverRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GracefulFailoverRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GracefulFailoverRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GracefulFailoverRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GracefulFailoverRequestProto::MergeFrom(const GracefulFailoverRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GracefulFailoverRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GracefulFailoverRequestProto::CopyFrom(const GracefulFailoverRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GracefulFailoverRequestProto::IsInitialized() const {

  return true;
}

void GracefulFailoverRequestProto::Swap(GracefulFailoverRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GracefulFailoverRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GracefulFailoverRequestProto_descriptor_;
  metadata.reflection = GracefulFailoverRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

GracefulFailoverResponseProto::GracefulFailoverResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GracefulFailoverResponseProto::InitAsDefaultInstance() {
}

GracefulFailoverResponseProto::GracefulFailoverResponseProto(const GracefulFailoverResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GracefulFailoverResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GracefulFailoverResponseProto::~GracefulFailoverResponseProto() {
  SharedDtor();
}

void GracefulFailoverResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GracefulFailoverResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GracefulFailoverResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GracefulFailoverResponseProto_descriptor_;
}

const GracefulFailoverResponseProto& GracefulFailoverResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fZKFCProtocol_2eproto();
  return *default_instance_;
}

GracefulFailoverResponseProto* GracefulFailoverResponseProto::default_instance_ = NULL;

GracefulFailoverResponseProto* GracefulFailoverResponseProto::New() const {
  return new GracefulFailoverResponseProto;
}

void GracefulFailoverResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GracefulFailoverResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void GracefulFailoverResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GracefulFailoverResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GracefulFailoverResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GracefulFailoverResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GracefulFailoverResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GracefulFailoverResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GracefulFailoverResponseProto::MergeFrom(const GracefulFailoverResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GracefulFailoverResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GracefulFailoverResponseProto::CopyFrom(const GracefulFailoverResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GracefulFailoverResponseProto::IsInitialized() const {

  return true;
}

void GracefulFailoverResponseProto::Swap(GracefulFailoverResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GracefulFailoverResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GracefulFailoverResponseProto_descriptor_;
  metadata.reflection = GracefulFailoverResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
