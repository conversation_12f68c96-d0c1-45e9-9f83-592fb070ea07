// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/GetUserMappingsProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/GetUserMappingsProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* GetGroupsForUserRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetGroupsForUserRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetGroupsForUserResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetGroupsForUserResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fGetUserMappingsProtocol_2eproto() {
  protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/GetUserMappingsProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  GetGroupsForUserRequestProto_descriptor_ = file->message_type(0);
  static const int GetGroupsForUserRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetGroupsForUserRequestProto, user_),
  };
  GetGroupsForUserRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetGroupsForUserRequestProto_descriptor_,
      GetGroupsForUserRequestProto::default_instance_,
      GetGroupsForUserRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetGroupsForUserRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetGroupsForUserRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetGroupsForUserRequestProto));
  GetGroupsForUserResponseProto_descriptor_ = file->message_type(1);
  static const int GetGroupsForUserResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetGroupsForUserResponseProto, groups_),
  };
  GetGroupsForUserResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetGroupsForUserResponseProto_descriptor_,
      GetGroupsForUserResponseProto::default_instance_,
      GetGroupsForUserResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetGroupsForUserResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetGroupsForUserResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetGroupsForUserResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fGetUserMappingsProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetGroupsForUserRequestProto_descriptor_, &GetGroupsForUserRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetGroupsForUserResponseProto_descriptor_, &GetGroupsForUserResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fGetUserMappingsProtocol_2eproto() {
  delete GetGroupsForUserRequestProto::default_instance_;
  delete GetGroupsForUserRequestProto_reflection_;
  delete GetGroupsForUserResponseProto::default_instance_;
  delete GetGroupsForUserResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n$common/GetUserMappingsProtocol.proto\022\r"
    "hadoop.common\",\n\034GetGroupsForUserRequest"
    "Proto\022\014\n\004user\030\001 \002(\t\"/\n\035GetGroupsForUserR"
    "esponseProto\022\016\n\006groups\030\001 \003(\t2\217\001\n\036GetUser"
    "MappingsProtocolService\022m\n\020getGroupsForU"
    "ser\022+.hadoop.common.GetGroupsForUserRequ"
    "estProto\032,.hadoop.common.GetGroupsForUse"
    "rResponseProtoBD\n\035org.apache.hadoop.tool"
    "s.protoB\035GetUserMappingsProtocolProtos\210\001"
    "\001\240\001\001", 364);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/GetUserMappingsProtocol.proto", &protobuf_RegisterTypes);
  GetGroupsForUserRequestProto::default_instance_ = new GetGroupsForUserRequestProto();
  GetGroupsForUserResponseProto::default_instance_ = new GetGroupsForUserResponseProto();
  GetGroupsForUserRequestProto::default_instance_->InitAsDefaultInstance();
  GetGroupsForUserResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fGetUserMappingsProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fGetUserMappingsProtocol_2eproto {
  StaticDescriptorInitializer_common_2fGetUserMappingsProtocol_2eproto() {
    protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fGetUserMappingsProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int GetGroupsForUserRequestProto::kUserFieldNumber;
#endif  // !_MSC_VER

GetGroupsForUserRequestProto::GetGroupsForUserRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetGroupsForUserRequestProto::InitAsDefaultInstance() {
}

GetGroupsForUserRequestProto::GetGroupsForUserRequestProto(const GetGroupsForUserRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetGroupsForUserRequestProto::SharedCtor() {
  _cached_size_ = 0;
  user_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetGroupsForUserRequestProto::~GetGroupsForUserRequestProto() {
  SharedDtor();
}

void GetGroupsForUserRequestProto::SharedDtor() {
  if (user_ != &::google::protobuf::internal::kEmptyString) {
    delete user_;
  }
  if (this != default_instance_) {
  }
}

void GetGroupsForUserRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetGroupsForUserRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetGroupsForUserRequestProto_descriptor_;
}

const GetGroupsForUserRequestProto& GetGroupsForUserRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
  return *default_instance_;
}

GetGroupsForUserRequestProto* GetGroupsForUserRequestProto::default_instance_ = NULL;

GetGroupsForUserRequestProto* GetGroupsForUserRequestProto::New() const {
  return new GetGroupsForUserRequestProto;
}

void GetGroupsForUserRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_user()) {
      if (user_ != &::google::protobuf::internal::kEmptyString) {
        user_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetGroupsForUserRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string user = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_user()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->user().data(), this->user().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetGroupsForUserRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string user = 1;
  if (has_user()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->user().data(), this->user().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->user(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetGroupsForUserRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string user = 1;
  if (has_user()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->user().data(), this->user().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->user(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetGroupsForUserRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string user = 1;
    if (has_user()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->user());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetGroupsForUserRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetGroupsForUserRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetGroupsForUserRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetGroupsForUserRequestProto::MergeFrom(const GetGroupsForUserRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_user()) {
      set_user(from.user());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetGroupsForUserRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetGroupsForUserRequestProto::CopyFrom(const GetGroupsForUserRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetGroupsForUserRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetGroupsForUserRequestProto::Swap(GetGroupsForUserRequestProto* other) {
  if (other != this) {
    std::swap(user_, other->user_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetGroupsForUserRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetGroupsForUserRequestProto_descriptor_;
  metadata.reflection = GetGroupsForUserRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetGroupsForUserResponseProto::kGroupsFieldNumber;
#endif  // !_MSC_VER

GetGroupsForUserResponseProto::GetGroupsForUserResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetGroupsForUserResponseProto::InitAsDefaultInstance() {
}

GetGroupsForUserResponseProto::GetGroupsForUserResponseProto(const GetGroupsForUserResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetGroupsForUserResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetGroupsForUserResponseProto::~GetGroupsForUserResponseProto() {
  SharedDtor();
}

void GetGroupsForUserResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetGroupsForUserResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetGroupsForUserResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetGroupsForUserResponseProto_descriptor_;
}

const GetGroupsForUserResponseProto& GetGroupsForUserResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fGetUserMappingsProtocol_2eproto();
  return *default_instance_;
}

GetGroupsForUserResponseProto* GetGroupsForUserResponseProto::default_instance_ = NULL;

GetGroupsForUserResponseProto* GetGroupsForUserResponseProto::New() const {
  return new GetGroupsForUserResponseProto;
}

void GetGroupsForUserResponseProto::Clear() {
  groups_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetGroupsForUserResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string groups = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_groups:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_groups()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->groups(this->groups_size() - 1).data(),
            this->groups(this->groups_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_groups;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetGroupsForUserResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated string groups = 1;
  for (int i = 0; i < this->groups_size(); i++) {
  ::google::protobuf::internal::WireFormat::VerifyUTF8String(
    this->groups(i).data(), this->groups(i).length(),
    ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->groups(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetGroupsForUserResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated string groups = 1;
  for (int i = 0; i < this->groups_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->groups(i).data(), this->groups(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->groups(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetGroupsForUserResponseProto::ByteSize() const {
  int total_size = 0;

  // repeated string groups = 1;
  total_size += 1 * this->groups_size();
  for (int i = 0; i < this->groups_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->groups(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetGroupsForUserResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetGroupsForUserResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetGroupsForUserResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetGroupsForUserResponseProto::MergeFrom(const GetGroupsForUserResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  groups_.MergeFrom(from.groups_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetGroupsForUserResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetGroupsForUserResponseProto::CopyFrom(const GetGroupsForUserResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetGroupsForUserResponseProto::IsInitialized() const {

  return true;
}

void GetGroupsForUserResponseProto::Swap(GetGroupsForUserResponseProto* other) {
  if (other != this) {
    groups_.Swap(&other->groups_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetGroupsForUserResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetGroupsForUserResponseProto_descriptor_;
  metadata.reflection = GetGroupsForUserResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
