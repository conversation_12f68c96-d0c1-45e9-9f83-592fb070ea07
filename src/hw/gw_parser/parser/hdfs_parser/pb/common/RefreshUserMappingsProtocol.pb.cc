// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/RefreshUserMappingsProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/RefreshUserMappingsProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* RefreshUserToGroupsMappingsRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshUserToGroupsMappingsRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RefreshUserToGroupsMappingsResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshUserToGroupsMappingsResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RefreshSuperUserGroupsConfigurationRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshSuperUserGroupsConfigurationRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RefreshSuperUserGroupsConfigurationResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RefreshSuperUserGroupsConfigurationResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto() {
  protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/RefreshUserMappingsProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  RefreshUserToGroupsMappingsRequestProto_descriptor_ = file->message_type(0);
  static const int RefreshUserToGroupsMappingsRequestProto_offsets_[1] = {
  };
  RefreshUserToGroupsMappingsRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshUserToGroupsMappingsRequestProto_descriptor_,
      RefreshUserToGroupsMappingsRequestProto::default_instance_,
      RefreshUserToGroupsMappingsRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshUserToGroupsMappingsRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshUserToGroupsMappingsRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshUserToGroupsMappingsRequestProto));
  RefreshUserToGroupsMappingsResponseProto_descriptor_ = file->message_type(1);
  static const int RefreshUserToGroupsMappingsResponseProto_offsets_[1] = {
  };
  RefreshUserToGroupsMappingsResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshUserToGroupsMappingsResponseProto_descriptor_,
      RefreshUserToGroupsMappingsResponseProto::default_instance_,
      RefreshUserToGroupsMappingsResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshUserToGroupsMappingsResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshUserToGroupsMappingsResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshUserToGroupsMappingsResponseProto));
  RefreshSuperUserGroupsConfigurationRequestProto_descriptor_ = file->message_type(2);
  static const int RefreshSuperUserGroupsConfigurationRequestProto_offsets_[1] = {
  };
  RefreshSuperUserGroupsConfigurationRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshSuperUserGroupsConfigurationRequestProto_descriptor_,
      RefreshSuperUserGroupsConfigurationRequestProto::default_instance_,
      RefreshSuperUserGroupsConfigurationRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshSuperUserGroupsConfigurationRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshSuperUserGroupsConfigurationRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshSuperUserGroupsConfigurationRequestProto));
  RefreshSuperUserGroupsConfigurationResponseProto_descriptor_ = file->message_type(3);
  static const int RefreshSuperUserGroupsConfigurationResponseProto_offsets_[1] = {
  };
  RefreshSuperUserGroupsConfigurationResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RefreshSuperUserGroupsConfigurationResponseProto_descriptor_,
      RefreshSuperUserGroupsConfigurationResponseProto::default_instance_,
      RefreshSuperUserGroupsConfigurationResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshSuperUserGroupsConfigurationResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RefreshSuperUserGroupsConfigurationResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RefreshSuperUserGroupsConfigurationResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fRefreshUserMappingsProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshUserToGroupsMappingsRequestProto_descriptor_, &RefreshUserToGroupsMappingsRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshUserToGroupsMappingsResponseProto_descriptor_, &RefreshUserToGroupsMappingsResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshSuperUserGroupsConfigurationRequestProto_descriptor_, &RefreshSuperUserGroupsConfigurationRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RefreshSuperUserGroupsConfigurationResponseProto_descriptor_, &RefreshSuperUserGroupsConfigurationResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto() {
  delete RefreshUserToGroupsMappingsRequestProto::default_instance_;
  delete RefreshUserToGroupsMappingsRequestProto_reflection_;
  delete RefreshUserToGroupsMappingsResponseProto::default_instance_;
  delete RefreshUserToGroupsMappingsResponseProto_reflection_;
  delete RefreshSuperUserGroupsConfigurationRequestProto::default_instance_;
  delete RefreshSuperUserGroupsConfigurationRequestProto_reflection_;
  delete RefreshSuperUserGroupsConfigurationResponseProto::default_instance_;
  delete RefreshSuperUserGroupsConfigurationResponseProto_reflection_;
}

void protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n(common/RefreshUserMappingsProtocol.pro"
    "to\022\rhadoop.common\")\n\'RefreshUserToGroups"
    "MappingsRequestProto\"*\n(RefreshUserToGro"
    "upsMappingsResponseProto\"1\n/RefreshSuper"
    "UserGroupsConfigurationRequestProto\"2\n0R"
    "efreshSuperUserGroupsConfigurationRespon"
    "seProto2\336\002\n\"RefreshUserMappingsProtocolS"
    "ervice\022\216\001\n\033refreshUserToGroupsMappings\0226"
    ".hadoop.common.RefreshUserToGroupsMappin"
    "gsRequestProto\0327.hadoop.common.RefreshUs"
    "erToGroupsMappingsResponseProto\022\246\001\n#refr"
    "eshSuperUserGroupsConfiguration\022>.hadoop"
    ".common.RefreshSuperUserGroupsConfigurat"
    "ionRequestProto\032\?.hadoop.common.RefreshS"
    "uperUserGroupsConfigurationResponseProto"
    "BK\n org.apache.hadoop.security.protoB!Re"
    "freshUserMappingsProtocolProtos\210\001\001\240\001\001", 677);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/RefreshUserMappingsProtocol.proto", &protobuf_RegisterTypes);
  RefreshUserToGroupsMappingsRequestProto::default_instance_ = new RefreshUserToGroupsMappingsRequestProto();
  RefreshUserToGroupsMappingsResponseProto::default_instance_ = new RefreshUserToGroupsMappingsResponseProto();
  RefreshSuperUserGroupsConfigurationRequestProto::default_instance_ = new RefreshSuperUserGroupsConfigurationRequestProto();
  RefreshSuperUserGroupsConfigurationResponseProto::default_instance_ = new RefreshSuperUserGroupsConfigurationResponseProto();
  RefreshUserToGroupsMappingsRequestProto::default_instance_->InitAsDefaultInstance();
  RefreshUserToGroupsMappingsResponseProto::default_instance_->InitAsDefaultInstance();
  RefreshSuperUserGroupsConfigurationRequestProto::default_instance_->InitAsDefaultInstance();
  RefreshSuperUserGroupsConfigurationResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fRefreshUserMappingsProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fRefreshUserMappingsProtocol_2eproto {
  StaticDescriptorInitializer_common_2fRefreshUserMappingsProtocol_2eproto() {
    protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fRefreshUserMappingsProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshUserToGroupsMappingsRequestProto::RefreshUserToGroupsMappingsRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshUserToGroupsMappingsRequestProto::InitAsDefaultInstance() {
}

RefreshUserToGroupsMappingsRequestProto::RefreshUserToGroupsMappingsRequestProto(const RefreshUserToGroupsMappingsRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshUserToGroupsMappingsRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshUserToGroupsMappingsRequestProto::~RefreshUserToGroupsMappingsRequestProto() {
  SharedDtor();
}

void RefreshUserToGroupsMappingsRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshUserToGroupsMappingsRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshUserToGroupsMappingsRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshUserToGroupsMappingsRequestProto_descriptor_;
}

const RefreshUserToGroupsMappingsRequestProto& RefreshUserToGroupsMappingsRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  return *default_instance_;
}

RefreshUserToGroupsMappingsRequestProto* RefreshUserToGroupsMappingsRequestProto::default_instance_ = NULL;

RefreshUserToGroupsMappingsRequestProto* RefreshUserToGroupsMappingsRequestProto::New() const {
  return new RefreshUserToGroupsMappingsRequestProto;
}

void RefreshUserToGroupsMappingsRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshUserToGroupsMappingsRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshUserToGroupsMappingsRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshUserToGroupsMappingsRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshUserToGroupsMappingsRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshUserToGroupsMappingsRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshUserToGroupsMappingsRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshUserToGroupsMappingsRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshUserToGroupsMappingsRequestProto::MergeFrom(const RefreshUserToGroupsMappingsRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshUserToGroupsMappingsRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshUserToGroupsMappingsRequestProto::CopyFrom(const RefreshUserToGroupsMappingsRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshUserToGroupsMappingsRequestProto::IsInitialized() const {

  return true;
}

void RefreshUserToGroupsMappingsRequestProto::Swap(RefreshUserToGroupsMappingsRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshUserToGroupsMappingsRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshUserToGroupsMappingsRequestProto_descriptor_;
  metadata.reflection = RefreshUserToGroupsMappingsRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshUserToGroupsMappingsResponseProto::RefreshUserToGroupsMappingsResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshUserToGroupsMappingsResponseProto::InitAsDefaultInstance() {
}

RefreshUserToGroupsMappingsResponseProto::RefreshUserToGroupsMappingsResponseProto(const RefreshUserToGroupsMappingsResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshUserToGroupsMappingsResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshUserToGroupsMappingsResponseProto::~RefreshUserToGroupsMappingsResponseProto() {
  SharedDtor();
}

void RefreshUserToGroupsMappingsResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshUserToGroupsMappingsResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshUserToGroupsMappingsResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshUserToGroupsMappingsResponseProto_descriptor_;
}

const RefreshUserToGroupsMappingsResponseProto& RefreshUserToGroupsMappingsResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  return *default_instance_;
}

RefreshUserToGroupsMappingsResponseProto* RefreshUserToGroupsMappingsResponseProto::default_instance_ = NULL;

RefreshUserToGroupsMappingsResponseProto* RefreshUserToGroupsMappingsResponseProto::New() const {
  return new RefreshUserToGroupsMappingsResponseProto;
}

void RefreshUserToGroupsMappingsResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshUserToGroupsMappingsResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshUserToGroupsMappingsResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshUserToGroupsMappingsResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshUserToGroupsMappingsResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshUserToGroupsMappingsResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshUserToGroupsMappingsResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshUserToGroupsMappingsResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshUserToGroupsMappingsResponseProto::MergeFrom(const RefreshUserToGroupsMappingsResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshUserToGroupsMappingsResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshUserToGroupsMappingsResponseProto::CopyFrom(const RefreshUserToGroupsMappingsResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshUserToGroupsMappingsResponseProto::IsInitialized() const {

  return true;
}

void RefreshUserToGroupsMappingsResponseProto::Swap(RefreshUserToGroupsMappingsResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshUserToGroupsMappingsResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshUserToGroupsMappingsResponseProto_descriptor_;
  metadata.reflection = RefreshUserToGroupsMappingsResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshSuperUserGroupsConfigurationRequestProto::RefreshSuperUserGroupsConfigurationRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshSuperUserGroupsConfigurationRequestProto::InitAsDefaultInstance() {
}

RefreshSuperUserGroupsConfigurationRequestProto::RefreshSuperUserGroupsConfigurationRequestProto(const RefreshSuperUserGroupsConfigurationRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshSuperUserGroupsConfigurationRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshSuperUserGroupsConfigurationRequestProto::~RefreshSuperUserGroupsConfigurationRequestProto() {
  SharedDtor();
}

void RefreshSuperUserGroupsConfigurationRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshSuperUserGroupsConfigurationRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshSuperUserGroupsConfigurationRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshSuperUserGroupsConfigurationRequestProto_descriptor_;
}

const RefreshSuperUserGroupsConfigurationRequestProto& RefreshSuperUserGroupsConfigurationRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  return *default_instance_;
}

RefreshSuperUserGroupsConfigurationRequestProto* RefreshSuperUserGroupsConfigurationRequestProto::default_instance_ = NULL;

RefreshSuperUserGroupsConfigurationRequestProto* RefreshSuperUserGroupsConfigurationRequestProto::New() const {
  return new RefreshSuperUserGroupsConfigurationRequestProto;
}

void RefreshSuperUserGroupsConfigurationRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshSuperUserGroupsConfigurationRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshSuperUserGroupsConfigurationRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshSuperUserGroupsConfigurationRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshSuperUserGroupsConfigurationRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshSuperUserGroupsConfigurationRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshSuperUserGroupsConfigurationRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshSuperUserGroupsConfigurationRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshSuperUserGroupsConfigurationRequestProto::MergeFrom(const RefreshSuperUserGroupsConfigurationRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshSuperUserGroupsConfigurationRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshSuperUserGroupsConfigurationRequestProto::CopyFrom(const RefreshSuperUserGroupsConfigurationRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshSuperUserGroupsConfigurationRequestProto::IsInitialized() const {

  return true;
}

void RefreshSuperUserGroupsConfigurationRequestProto::Swap(RefreshSuperUserGroupsConfigurationRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshSuperUserGroupsConfigurationRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshSuperUserGroupsConfigurationRequestProto_descriptor_;
  metadata.reflection = RefreshSuperUserGroupsConfigurationRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RefreshSuperUserGroupsConfigurationResponseProto::RefreshSuperUserGroupsConfigurationResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RefreshSuperUserGroupsConfigurationResponseProto::InitAsDefaultInstance() {
}

RefreshSuperUserGroupsConfigurationResponseProto::RefreshSuperUserGroupsConfigurationResponseProto(const RefreshSuperUserGroupsConfigurationResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RefreshSuperUserGroupsConfigurationResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RefreshSuperUserGroupsConfigurationResponseProto::~RefreshSuperUserGroupsConfigurationResponseProto() {
  SharedDtor();
}

void RefreshSuperUserGroupsConfigurationResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RefreshSuperUserGroupsConfigurationResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RefreshSuperUserGroupsConfigurationResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RefreshSuperUserGroupsConfigurationResponseProto_descriptor_;
}

const RefreshSuperUserGroupsConfigurationResponseProto& RefreshSuperUserGroupsConfigurationResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fRefreshUserMappingsProtocol_2eproto();
  return *default_instance_;
}

RefreshSuperUserGroupsConfigurationResponseProto* RefreshSuperUserGroupsConfigurationResponseProto::default_instance_ = NULL;

RefreshSuperUserGroupsConfigurationResponseProto* RefreshSuperUserGroupsConfigurationResponseProto::New() const {
  return new RefreshSuperUserGroupsConfigurationResponseProto;
}

void RefreshSuperUserGroupsConfigurationResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RefreshSuperUserGroupsConfigurationResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RefreshSuperUserGroupsConfigurationResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RefreshSuperUserGroupsConfigurationResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RefreshSuperUserGroupsConfigurationResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RefreshSuperUserGroupsConfigurationResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RefreshSuperUserGroupsConfigurationResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RefreshSuperUserGroupsConfigurationResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RefreshSuperUserGroupsConfigurationResponseProto::MergeFrom(const RefreshSuperUserGroupsConfigurationResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RefreshSuperUserGroupsConfigurationResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RefreshSuperUserGroupsConfigurationResponseProto::CopyFrom(const RefreshSuperUserGroupsConfigurationResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshSuperUserGroupsConfigurationResponseProto::IsInitialized() const {

  return true;
}

void RefreshSuperUserGroupsConfigurationResponseProto::Swap(RefreshSuperUserGroupsConfigurationResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RefreshSuperUserGroupsConfigurationResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RefreshSuperUserGroupsConfigurationResponseProto_descriptor_;
  metadata.reflection = RefreshSuperUserGroupsConfigurationResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
