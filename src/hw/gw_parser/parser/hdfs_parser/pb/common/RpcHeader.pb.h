// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/RpcHeader.proto

#ifndef PROTOBUF_common_2fRpcHeader_2eproto__INCLUDED
#define PROTOBUF_common_2fRpcHeader_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_common_2fRpcHeader_2eproto();
void protobuf_AssignDesc_common_2fRpcHeader_2eproto();
void protobuf_ShutdownFile_common_2fRpcHeader_2eproto();

class RPCTraceInfoProto;
class RpcRequestHeaderProto;
class RpcResponseHeaderProto;
class RpcSaslProto;
class RpcSaslProto_SaslAuth;

enum RpcRequestHeaderProto_OperationProto {
  RpcRequestHeaderProto_OperationProto_RPC_FINAL_PACKET = 0,
  RpcRequestHeaderProto_OperationProto_RPC_CONTINUATION_PACKET = 1,
  RpcRequestHeaderProto_OperationProto_RPC_CLOSE_CONNECTION = 2
};
bool RpcRequestHeaderProto_OperationProto_IsValid(int value);
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto_OperationProto_OperationProto_MIN = RpcRequestHeaderProto_OperationProto_RPC_FINAL_PACKET;
const RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto_OperationProto_OperationProto_MAX = RpcRequestHeaderProto_OperationProto_RPC_CLOSE_CONNECTION;
const int RpcRequestHeaderProto_OperationProto_OperationProto_ARRAYSIZE = RpcRequestHeaderProto_OperationProto_OperationProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* RpcRequestHeaderProto_OperationProto_descriptor();
inline const ::std::string& RpcRequestHeaderProto_OperationProto_Name(RpcRequestHeaderProto_OperationProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    RpcRequestHeaderProto_OperationProto_descriptor(), value);
}
inline bool RpcRequestHeaderProto_OperationProto_Parse(
    const ::std::string& name, RpcRequestHeaderProto_OperationProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RpcRequestHeaderProto_OperationProto>(
    RpcRequestHeaderProto_OperationProto_descriptor(), name, value);
}
enum RpcResponseHeaderProto_RpcStatusProto {
  RpcResponseHeaderProto_RpcStatusProto_SUCCESS = 0,
  RpcResponseHeaderProto_RpcStatusProto_ERROR = 1,
  RpcResponseHeaderProto_RpcStatusProto_FATAL = 2
};
bool RpcResponseHeaderProto_RpcStatusProto_IsValid(int value);
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_MIN = RpcResponseHeaderProto_RpcStatusProto_SUCCESS;
const RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_MAX = RpcResponseHeaderProto_RpcStatusProto_FATAL;
const int RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_ARRAYSIZE = RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* RpcResponseHeaderProto_RpcStatusProto_descriptor();
inline const ::std::string& RpcResponseHeaderProto_RpcStatusProto_Name(RpcResponseHeaderProto_RpcStatusProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    RpcResponseHeaderProto_RpcStatusProto_descriptor(), value);
}
inline bool RpcResponseHeaderProto_RpcStatusProto_Parse(
    const ::std::string& name, RpcResponseHeaderProto_RpcStatusProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RpcResponseHeaderProto_RpcStatusProto>(
    RpcResponseHeaderProto_RpcStatusProto_descriptor(), name, value);
}
enum RpcResponseHeaderProto_RpcErrorCodeProto {
  RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION = 1,
  RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_NO_SUCH_METHOD = 2,
  RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_NO_SUCH_PROTOCOL = 3,
  RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_RPC_SERVER = 4,
  RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_SERIALIZING_RESPONSE = 5,
  RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_RPC_VERSION_MISMATCH = 6,
  RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNKNOWN = 10,
  RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNSUPPORTED_SERIALIZATION = 11,
  RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_INVALID_RPC_HEADER = 12,
  RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_DESERIALIZING_REQUEST = 13,
  RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_VERSION_MISMATCH = 14,
  RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNAUTHORIZED = 15
};
bool RpcResponseHeaderProto_RpcErrorCodeProto_IsValid(int value);
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_MIN = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION;
const RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_MAX = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNAUTHORIZED;
const int RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_ARRAYSIZE = RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* RpcResponseHeaderProto_RpcErrorCodeProto_descriptor();
inline const ::std::string& RpcResponseHeaderProto_RpcErrorCodeProto_Name(RpcResponseHeaderProto_RpcErrorCodeProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    RpcResponseHeaderProto_RpcErrorCodeProto_descriptor(), value);
}
inline bool RpcResponseHeaderProto_RpcErrorCodeProto_Parse(
    const ::std::string& name, RpcResponseHeaderProto_RpcErrorCodeProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RpcResponseHeaderProto_RpcErrorCodeProto>(
    RpcResponseHeaderProto_RpcErrorCodeProto_descriptor(), name, value);
}
enum RpcSaslProto_SaslState {
  RpcSaslProto_SaslState_SUCCESS = 0,
  RpcSaslProto_SaslState_NEGOTIATE = 1,
  RpcSaslProto_SaslState_INITIATE = 2,
  RpcSaslProto_SaslState_CHALLENGE = 3,
  RpcSaslProto_SaslState_RESPONSE = 4,
  RpcSaslProto_SaslState_WRAP = 5
};
bool RpcSaslProto_SaslState_IsValid(int value);
const RpcSaslProto_SaslState RpcSaslProto_SaslState_SaslState_MIN = RpcSaslProto_SaslState_SUCCESS;
const RpcSaslProto_SaslState RpcSaslProto_SaslState_SaslState_MAX = RpcSaslProto_SaslState_WRAP;
const int RpcSaslProto_SaslState_SaslState_ARRAYSIZE = RpcSaslProto_SaslState_SaslState_MAX + 1;

const ::google::protobuf::EnumDescriptor* RpcSaslProto_SaslState_descriptor();
inline const ::std::string& RpcSaslProto_SaslState_Name(RpcSaslProto_SaslState value) {
  return ::google::protobuf::internal::NameOfEnum(
    RpcSaslProto_SaslState_descriptor(), value);
}
inline bool RpcSaslProto_SaslState_Parse(
    const ::std::string& name, RpcSaslProto_SaslState* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RpcSaslProto_SaslState>(
    RpcSaslProto_SaslState_descriptor(), name, value);
}
enum RpcKindProto {
  RPC_BUILTIN = 0,
  RPC_WRITABLE = 1,
  RPC_PROTOCOL_BUFFER = 2
};
bool RpcKindProto_IsValid(int value);
const RpcKindProto RpcKindProto_MIN = RPC_BUILTIN;
const RpcKindProto RpcKindProto_MAX = RPC_PROTOCOL_BUFFER;
const int RpcKindProto_ARRAYSIZE = RpcKindProto_MAX + 1;

const ::google::protobuf::EnumDescriptor* RpcKindProto_descriptor();
inline const ::std::string& RpcKindProto_Name(RpcKindProto value) {
  return ::google::protobuf::internal::NameOfEnum(
    RpcKindProto_descriptor(), value);
}
inline bool RpcKindProto_Parse(
    const ::std::string& name, RpcKindProto* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RpcKindProto>(
    RpcKindProto_descriptor(), name, value);
}
// ===================================================================

class RPCTraceInfoProto : public ::google::protobuf::Message {
 public:
  RPCTraceInfoProto();
  virtual ~RPCTraceInfoProto();

  RPCTraceInfoProto(const RPCTraceInfoProto& from);

  inline RPCTraceInfoProto& operator=(const RPCTraceInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RPCTraceInfoProto& default_instance();

  void Swap(RPCTraceInfoProto* other);

  // implements Message ----------------------------------------------

  RPCTraceInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RPCTraceInfoProto& from);
  void MergeFrom(const RPCTraceInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 traceId = 1;
  inline bool has_traceid() const;
  inline void clear_traceid();
  static const int kTraceIdFieldNumber = 1;
  inline ::google::protobuf::int64 traceid() const;
  inline void set_traceid(::google::protobuf::int64 value);

  // optional int64 parentId = 2;
  inline bool has_parentid() const;
  inline void clear_parentid();
  static const int kParentIdFieldNumber = 2;
  inline ::google::protobuf::int64 parentid() const;
  inline void set_parentid(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.common.RPCTraceInfoProto)
 private:
  inline void set_has_traceid();
  inline void clear_has_traceid();
  inline void set_has_parentid();
  inline void clear_has_parentid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 traceid_;
  ::google::protobuf::int64 parentid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_AssignDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_ShutdownFile_common_2fRpcHeader_2eproto();

  void InitAsDefaultInstance();
  static RPCTraceInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class RpcRequestHeaderProto : public ::google::protobuf::Message {
 public:
  RpcRequestHeaderProto();
  virtual ~RpcRequestHeaderProto();

  RpcRequestHeaderProto(const RpcRequestHeaderProto& from);

  inline RpcRequestHeaderProto& operator=(const RpcRequestHeaderProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RpcRequestHeaderProto& default_instance();

  void Swap(RpcRequestHeaderProto* other);

  // implements Message ----------------------------------------------

  RpcRequestHeaderProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RpcRequestHeaderProto& from);
  void MergeFrom(const RpcRequestHeaderProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef RpcRequestHeaderProto_OperationProto OperationProto;
  static const OperationProto RPC_FINAL_PACKET = RpcRequestHeaderProto_OperationProto_RPC_FINAL_PACKET;
  static const OperationProto RPC_CONTINUATION_PACKET = RpcRequestHeaderProto_OperationProto_RPC_CONTINUATION_PACKET;
  static const OperationProto RPC_CLOSE_CONNECTION = RpcRequestHeaderProto_OperationProto_RPC_CLOSE_CONNECTION;
  static inline bool OperationProto_IsValid(int value) {
    return RpcRequestHeaderProto_OperationProto_IsValid(value);
  }
  static const OperationProto OperationProto_MIN =
    RpcRequestHeaderProto_OperationProto_OperationProto_MIN;
  static const OperationProto OperationProto_MAX =
    RpcRequestHeaderProto_OperationProto_OperationProto_MAX;
  static const int OperationProto_ARRAYSIZE =
    RpcRequestHeaderProto_OperationProto_OperationProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  OperationProto_descriptor() {
    return RpcRequestHeaderProto_OperationProto_descriptor();
  }
  static inline const ::std::string& OperationProto_Name(OperationProto value) {
    return RpcRequestHeaderProto_OperationProto_Name(value);
  }
  static inline bool OperationProto_Parse(const ::std::string& name,
      OperationProto* value) {
    return RpcRequestHeaderProto_OperationProto_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional .hadoop.common.RpcKindProto rpcKind = 1;
  inline bool has_rpckind() const;
  inline void clear_rpckind();
  static const int kRpcKindFieldNumber = 1;
  inline ::hadoop::common::RpcKindProto rpckind() const;
  inline void set_rpckind(::hadoop::common::RpcKindProto value);

  // optional .hadoop.common.RpcRequestHeaderProto.OperationProto rpcOp = 2;
  inline bool has_rpcop() const;
  inline void clear_rpcop();
  static const int kRpcOpFieldNumber = 2;
  inline ::hadoop::common::RpcRequestHeaderProto_OperationProto rpcop() const;
  inline void set_rpcop(::hadoop::common::RpcRequestHeaderProto_OperationProto value);

  // required sint32 callId = 3;
  inline bool has_callid() const;
  inline void clear_callid();
  static const int kCallIdFieldNumber = 3;
  inline ::google::protobuf::int32 callid() const;
  inline void set_callid(::google::protobuf::int32 value);

  // required bytes clientId = 4;
  inline bool has_clientid() const;
  inline void clear_clientid();
  static const int kClientIdFieldNumber = 4;
  inline const ::std::string& clientid() const;
  inline void set_clientid(const ::std::string& value);
  inline void set_clientid(const char* value);
  inline void set_clientid(const void* value, size_t size);
  inline ::std::string* mutable_clientid();
  inline ::std::string* release_clientid();
  inline void set_allocated_clientid(::std::string* clientid);

  // optional sint32 retryCount = 5 [default = -1];
  inline bool has_retrycount() const;
  inline void clear_retrycount();
  static const int kRetryCountFieldNumber = 5;
  inline ::google::protobuf::int32 retrycount() const;
  inline void set_retrycount(::google::protobuf::int32 value);

  // optional .hadoop.common.RPCTraceInfoProto traceInfo = 6;
  inline bool has_traceinfo() const;
  inline void clear_traceinfo();
  static const int kTraceInfoFieldNumber = 6;
  inline const ::hadoop::common::RPCTraceInfoProto& traceinfo() const;
  inline ::hadoop::common::RPCTraceInfoProto* mutable_traceinfo();
  inline ::hadoop::common::RPCTraceInfoProto* release_traceinfo();
  inline void set_allocated_traceinfo(::hadoop::common::RPCTraceInfoProto* traceinfo);

  // @@protoc_insertion_point(class_scope:hadoop.common.RpcRequestHeaderProto)
 private:
  inline void set_has_rpckind();
  inline void clear_has_rpckind();
  inline void set_has_rpcop();
  inline void clear_has_rpcop();
  inline void set_has_callid();
  inline void clear_has_callid();
  inline void set_has_clientid();
  inline void clear_has_clientid();
  inline void set_has_retrycount();
  inline void clear_has_retrycount();
  inline void set_has_traceinfo();
  inline void clear_has_traceinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int rpckind_;
  int rpcop_;
  ::std::string* clientid_;
  ::google::protobuf::int32 callid_;
  ::google::protobuf::int32 retrycount_;
  ::hadoop::common::RPCTraceInfoProto* traceinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_AssignDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_ShutdownFile_common_2fRpcHeader_2eproto();

  void InitAsDefaultInstance();
  static RpcRequestHeaderProto* default_instance_;
};
// -------------------------------------------------------------------

class RpcResponseHeaderProto : public ::google::protobuf::Message {
 public:
  RpcResponseHeaderProto();
  virtual ~RpcResponseHeaderProto();

  RpcResponseHeaderProto(const RpcResponseHeaderProto& from);

  inline RpcResponseHeaderProto& operator=(const RpcResponseHeaderProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RpcResponseHeaderProto& default_instance();

  void Swap(RpcResponseHeaderProto* other);

  // implements Message ----------------------------------------------

  RpcResponseHeaderProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RpcResponseHeaderProto& from);
  void MergeFrom(const RpcResponseHeaderProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef RpcResponseHeaderProto_RpcStatusProto RpcStatusProto;
  static const RpcStatusProto SUCCESS = RpcResponseHeaderProto_RpcStatusProto_SUCCESS;
  static const RpcStatusProto ERROR = RpcResponseHeaderProto_RpcStatusProto_ERROR;
  static const RpcStatusProto FATAL = RpcResponseHeaderProto_RpcStatusProto_FATAL;
  static inline bool RpcStatusProto_IsValid(int value) {
    return RpcResponseHeaderProto_RpcStatusProto_IsValid(value);
  }
  static const RpcStatusProto RpcStatusProto_MIN =
    RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_MIN;
  static const RpcStatusProto RpcStatusProto_MAX =
    RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_MAX;
  static const int RpcStatusProto_ARRAYSIZE =
    RpcResponseHeaderProto_RpcStatusProto_RpcStatusProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  RpcStatusProto_descriptor() {
    return RpcResponseHeaderProto_RpcStatusProto_descriptor();
  }
  static inline const ::std::string& RpcStatusProto_Name(RpcStatusProto value) {
    return RpcResponseHeaderProto_RpcStatusProto_Name(value);
  }
  static inline bool RpcStatusProto_Parse(const ::std::string& name,
      RpcStatusProto* value) {
    return RpcResponseHeaderProto_RpcStatusProto_Parse(name, value);
  }

  typedef RpcResponseHeaderProto_RpcErrorCodeProto RpcErrorCodeProto;
  static const RpcErrorCodeProto ERROR_APPLICATION = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION;
  static const RpcErrorCodeProto ERROR_NO_SUCH_METHOD = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_NO_SUCH_METHOD;
  static const RpcErrorCodeProto ERROR_NO_SUCH_PROTOCOL = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_NO_SUCH_PROTOCOL;
  static const RpcErrorCodeProto ERROR_RPC_SERVER = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_RPC_SERVER;
  static const RpcErrorCodeProto ERROR_SERIALIZING_RESPONSE = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_SERIALIZING_RESPONSE;
  static const RpcErrorCodeProto ERROR_RPC_VERSION_MISMATCH = RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_RPC_VERSION_MISMATCH;
  static const RpcErrorCodeProto FATAL_UNKNOWN = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNKNOWN;
  static const RpcErrorCodeProto FATAL_UNSUPPORTED_SERIALIZATION = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNSUPPORTED_SERIALIZATION;
  static const RpcErrorCodeProto FATAL_INVALID_RPC_HEADER = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_INVALID_RPC_HEADER;
  static const RpcErrorCodeProto FATAL_DESERIALIZING_REQUEST = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_DESERIALIZING_REQUEST;
  static const RpcErrorCodeProto FATAL_VERSION_MISMATCH = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_VERSION_MISMATCH;
  static const RpcErrorCodeProto FATAL_UNAUTHORIZED = RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNAUTHORIZED;
  static inline bool RpcErrorCodeProto_IsValid(int value) {
    return RpcResponseHeaderProto_RpcErrorCodeProto_IsValid(value);
  }
  static const RpcErrorCodeProto RpcErrorCodeProto_MIN =
    RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_MIN;
  static const RpcErrorCodeProto RpcErrorCodeProto_MAX =
    RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_MAX;
  static const int RpcErrorCodeProto_ARRAYSIZE =
    RpcResponseHeaderProto_RpcErrorCodeProto_RpcErrorCodeProto_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  RpcErrorCodeProto_descriptor() {
    return RpcResponseHeaderProto_RpcErrorCodeProto_descriptor();
  }
  static inline const ::std::string& RpcErrorCodeProto_Name(RpcErrorCodeProto value) {
    return RpcResponseHeaderProto_RpcErrorCodeProto_Name(value);
  }
  static inline bool RpcErrorCodeProto_Parse(const ::std::string& name,
      RpcErrorCodeProto* value) {
    return RpcResponseHeaderProto_RpcErrorCodeProto_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required uint32 callId = 1;
  inline bool has_callid() const;
  inline void clear_callid();
  static const int kCallIdFieldNumber = 1;
  inline ::google::protobuf::uint32 callid() const;
  inline void set_callid(::google::protobuf::uint32 value);

  // required .hadoop.common.RpcResponseHeaderProto.RpcStatusProto status = 2;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 2;
  inline ::hadoop::common::RpcResponseHeaderProto_RpcStatusProto status() const;
  inline void set_status(::hadoop::common::RpcResponseHeaderProto_RpcStatusProto value);

  // optional uint32 serverIpcVersionNum = 3;
  inline bool has_serveripcversionnum() const;
  inline void clear_serveripcversionnum();
  static const int kServerIpcVersionNumFieldNumber = 3;
  inline ::google::protobuf::uint32 serveripcversionnum() const;
  inline void set_serveripcversionnum(::google::protobuf::uint32 value);

  // optional string exceptionClassName = 4;
  inline bool has_exceptionclassname() const;
  inline void clear_exceptionclassname();
  static const int kExceptionClassNameFieldNumber = 4;
  inline const ::std::string& exceptionclassname() const;
  inline void set_exceptionclassname(const ::std::string& value);
  inline void set_exceptionclassname(const char* value);
  inline void set_exceptionclassname(const char* value, size_t size);
  inline ::std::string* mutable_exceptionclassname();
  inline ::std::string* release_exceptionclassname();
  inline void set_allocated_exceptionclassname(::std::string* exceptionclassname);

  // optional string errorMsg = 5;
  inline bool has_errormsg() const;
  inline void clear_errormsg();
  static const int kErrorMsgFieldNumber = 5;
  inline const ::std::string& errormsg() const;
  inline void set_errormsg(const ::std::string& value);
  inline void set_errormsg(const char* value);
  inline void set_errormsg(const char* value, size_t size);
  inline ::std::string* mutable_errormsg();
  inline ::std::string* release_errormsg();
  inline void set_allocated_errormsg(::std::string* errormsg);

  // optional .hadoop.common.RpcResponseHeaderProto.RpcErrorCodeProto errorDetail = 6;
  inline bool has_errordetail() const;
  inline void clear_errordetail();
  static const int kErrorDetailFieldNumber = 6;
  inline ::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto errordetail() const;
  inline void set_errordetail(::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto value);

  // optional bytes clientId = 7;
  inline bool has_clientid() const;
  inline void clear_clientid();
  static const int kClientIdFieldNumber = 7;
  inline const ::std::string& clientid() const;
  inline void set_clientid(const ::std::string& value);
  inline void set_clientid(const char* value);
  inline void set_clientid(const void* value, size_t size);
  inline ::std::string* mutable_clientid();
  inline ::std::string* release_clientid();
  inline void set_allocated_clientid(::std::string* clientid);

  // optional sint32 retryCount = 8 [default = -1];
  inline bool has_retrycount() const;
  inline void clear_retrycount();
  static const int kRetryCountFieldNumber = 8;
  inline ::google::protobuf::int32 retrycount() const;
  inline void set_retrycount(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:hadoop.common.RpcResponseHeaderProto)
 private:
  inline void set_has_callid();
  inline void clear_has_callid();
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_serveripcversionnum();
  inline void clear_has_serveripcversionnum();
  inline void set_has_exceptionclassname();
  inline void clear_has_exceptionclassname();
  inline void set_has_errormsg();
  inline void clear_has_errormsg();
  inline void set_has_errordetail();
  inline void clear_has_errordetail();
  inline void set_has_clientid();
  inline void clear_has_clientid();
  inline void set_has_retrycount();
  inline void clear_has_retrycount();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 callid_;
  int status_;
  ::std::string* exceptionclassname_;
  ::google::protobuf::uint32 serveripcversionnum_;
  int errordetail_;
  ::std::string* errormsg_;
  ::std::string* clientid_;
  ::google::protobuf::int32 retrycount_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(8 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_AssignDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_ShutdownFile_common_2fRpcHeader_2eproto();

  void InitAsDefaultInstance();
  static RpcResponseHeaderProto* default_instance_;
};
// -------------------------------------------------------------------

class RpcSaslProto_SaslAuth : public ::google::protobuf::Message {
 public:
  RpcSaslProto_SaslAuth();
  virtual ~RpcSaslProto_SaslAuth();

  RpcSaslProto_SaslAuth(const RpcSaslProto_SaslAuth& from);

  inline RpcSaslProto_SaslAuth& operator=(const RpcSaslProto_SaslAuth& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RpcSaslProto_SaslAuth& default_instance();

  void Swap(RpcSaslProto_SaslAuth* other);

  // implements Message ----------------------------------------------

  RpcSaslProto_SaslAuth* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RpcSaslProto_SaslAuth& from);
  void MergeFrom(const RpcSaslProto_SaslAuth& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string method = 1;
  inline bool has_method() const;
  inline void clear_method();
  static const int kMethodFieldNumber = 1;
  inline const ::std::string& method() const;
  inline void set_method(const ::std::string& value);
  inline void set_method(const char* value);
  inline void set_method(const char* value, size_t size);
  inline ::std::string* mutable_method();
  inline ::std::string* release_method();
  inline void set_allocated_method(::std::string* method);

  // required string mechanism = 2;
  inline bool has_mechanism() const;
  inline void clear_mechanism();
  static const int kMechanismFieldNumber = 2;
  inline const ::std::string& mechanism() const;
  inline void set_mechanism(const ::std::string& value);
  inline void set_mechanism(const char* value);
  inline void set_mechanism(const char* value, size_t size);
  inline ::std::string* mutable_mechanism();
  inline ::std::string* release_mechanism();
  inline void set_allocated_mechanism(::std::string* mechanism);

  // optional string protocol = 3;
  inline bool has_protocol() const;
  inline void clear_protocol();
  static const int kProtocolFieldNumber = 3;
  inline const ::std::string& protocol() const;
  inline void set_protocol(const ::std::string& value);
  inline void set_protocol(const char* value);
  inline void set_protocol(const char* value, size_t size);
  inline ::std::string* mutable_protocol();
  inline ::std::string* release_protocol();
  inline void set_allocated_protocol(::std::string* protocol);

  // optional string serverId = 4;
  inline bool has_serverid() const;
  inline void clear_serverid();
  static const int kServerIdFieldNumber = 4;
  inline const ::std::string& serverid() const;
  inline void set_serverid(const ::std::string& value);
  inline void set_serverid(const char* value);
  inline void set_serverid(const char* value, size_t size);
  inline ::std::string* mutable_serverid();
  inline ::std::string* release_serverid();
  inline void set_allocated_serverid(::std::string* serverid);

  // optional bytes challenge = 5;
  inline bool has_challenge() const;
  inline void clear_challenge();
  static const int kChallengeFieldNumber = 5;
  inline const ::std::string& challenge() const;
  inline void set_challenge(const ::std::string& value);
  inline void set_challenge(const char* value);
  inline void set_challenge(const void* value, size_t size);
  inline ::std::string* mutable_challenge();
  inline ::std::string* release_challenge();
  inline void set_allocated_challenge(::std::string* challenge);

  // @@protoc_insertion_point(class_scope:hadoop.common.RpcSaslProto.SaslAuth)
 private:
  inline void set_has_method();
  inline void clear_has_method();
  inline void set_has_mechanism();
  inline void clear_has_mechanism();
  inline void set_has_protocol();
  inline void clear_has_protocol();
  inline void set_has_serverid();
  inline void clear_has_serverid();
  inline void set_has_challenge();
  inline void clear_has_challenge();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* method_;
  ::std::string* mechanism_;
  ::std::string* protocol_;
  ::std::string* serverid_;
  ::std::string* challenge_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_AssignDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_ShutdownFile_common_2fRpcHeader_2eproto();

  void InitAsDefaultInstance();
  static RpcSaslProto_SaslAuth* default_instance_;
};
// -------------------------------------------------------------------

class RpcSaslProto : public ::google::protobuf::Message {
 public:
  RpcSaslProto();
  virtual ~RpcSaslProto();

  RpcSaslProto(const RpcSaslProto& from);

  inline RpcSaslProto& operator=(const RpcSaslProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RpcSaslProto& default_instance();

  void Swap(RpcSaslProto* other);

  // implements Message ----------------------------------------------

  RpcSaslProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RpcSaslProto& from);
  void MergeFrom(const RpcSaslProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef RpcSaslProto_SaslAuth SaslAuth;

  typedef RpcSaslProto_SaslState SaslState;
  static const SaslState SUCCESS = RpcSaslProto_SaslState_SUCCESS;
  static const SaslState NEGOTIATE = RpcSaslProto_SaslState_NEGOTIATE;
  static const SaslState INITIATE = RpcSaslProto_SaslState_INITIATE;
  static const SaslState CHALLENGE = RpcSaslProto_SaslState_CHALLENGE;
  static const SaslState RESPONSE = RpcSaslProto_SaslState_RESPONSE;
  static const SaslState WRAP = RpcSaslProto_SaslState_WRAP;
  static inline bool SaslState_IsValid(int value) {
    return RpcSaslProto_SaslState_IsValid(value);
  }
  static const SaslState SaslState_MIN =
    RpcSaslProto_SaslState_SaslState_MIN;
  static const SaslState SaslState_MAX =
    RpcSaslProto_SaslState_SaslState_MAX;
  static const int SaslState_ARRAYSIZE =
    RpcSaslProto_SaslState_SaslState_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  SaslState_descriptor() {
    return RpcSaslProto_SaslState_descriptor();
  }
  static inline const ::std::string& SaslState_Name(SaslState value) {
    return RpcSaslProto_SaslState_Name(value);
  }
  static inline bool SaslState_Parse(const ::std::string& name,
      SaslState* value) {
    return RpcSaslProto_SaslState_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional uint32 version = 1;
  inline bool has_version() const;
  inline void clear_version();
  static const int kVersionFieldNumber = 1;
  inline ::google::protobuf::uint32 version() const;
  inline void set_version(::google::protobuf::uint32 value);

  // required .hadoop.common.RpcSaslProto.SaslState state = 2;
  inline bool has_state() const;
  inline void clear_state();
  static const int kStateFieldNumber = 2;
  inline ::hadoop::common::RpcSaslProto_SaslState state() const;
  inline void set_state(::hadoop::common::RpcSaslProto_SaslState value);

  // optional bytes token = 3;
  inline bool has_token() const;
  inline void clear_token();
  static const int kTokenFieldNumber = 3;
  inline const ::std::string& token() const;
  inline void set_token(const ::std::string& value);
  inline void set_token(const char* value);
  inline void set_token(const void* value, size_t size);
  inline ::std::string* mutable_token();
  inline ::std::string* release_token();
  inline void set_allocated_token(::std::string* token);

  // repeated .hadoop.common.RpcSaslProto.SaslAuth auths = 4;
  inline int auths_size() const;
  inline void clear_auths();
  static const int kAuthsFieldNumber = 4;
  inline const ::hadoop::common::RpcSaslProto_SaslAuth& auths(int index) const;
  inline ::hadoop::common::RpcSaslProto_SaslAuth* mutable_auths(int index);
  inline ::hadoop::common::RpcSaslProto_SaslAuth* add_auths();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::RpcSaslProto_SaslAuth >&
      auths() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::RpcSaslProto_SaslAuth >*
      mutable_auths();

  // @@protoc_insertion_point(class_scope:hadoop.common.RpcSaslProto)
 private:
  inline void set_has_version();
  inline void clear_has_version();
  inline void set_has_state();
  inline void clear_has_state();
  inline void set_has_token();
  inline void clear_has_token();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 version_;
  int state_;
  ::std::string* token_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::common::RpcSaslProto_SaslAuth > auths_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_AssignDesc_common_2fRpcHeader_2eproto();
  friend void protobuf_ShutdownFile_common_2fRpcHeader_2eproto();

  void InitAsDefaultInstance();
  static RpcSaslProto* default_instance_;
};
// ===================================================================


// ===================================================================

// RPCTraceInfoProto

// optional int64 traceId = 1;
inline bool RPCTraceInfoProto::has_traceid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RPCTraceInfoProto::set_has_traceid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RPCTraceInfoProto::clear_has_traceid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RPCTraceInfoProto::clear_traceid() {
  traceid_ = GOOGLE_LONGLONG(0);
  clear_has_traceid();
}
inline ::google::protobuf::int64 RPCTraceInfoProto::traceid() const {
  return traceid_;
}
inline void RPCTraceInfoProto::set_traceid(::google::protobuf::int64 value) {
  set_has_traceid();
  traceid_ = value;
}

// optional int64 parentId = 2;
inline bool RPCTraceInfoProto::has_parentid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RPCTraceInfoProto::set_has_parentid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RPCTraceInfoProto::clear_has_parentid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RPCTraceInfoProto::clear_parentid() {
  parentid_ = GOOGLE_LONGLONG(0);
  clear_has_parentid();
}
inline ::google::protobuf::int64 RPCTraceInfoProto::parentid() const {
  return parentid_;
}
inline void RPCTraceInfoProto::set_parentid(::google::protobuf::int64 value) {
  set_has_parentid();
  parentid_ = value;
}

// -------------------------------------------------------------------

// RpcRequestHeaderProto

// optional .hadoop.common.RpcKindProto rpcKind = 1;
inline bool RpcRequestHeaderProto::has_rpckind() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RpcRequestHeaderProto::set_has_rpckind() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RpcRequestHeaderProto::clear_has_rpckind() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RpcRequestHeaderProto::clear_rpckind() {
  rpckind_ = 0;
  clear_has_rpckind();
}
inline ::hadoop::common::RpcKindProto RpcRequestHeaderProto::rpckind() const {
  return static_cast< ::hadoop::common::RpcKindProto >(rpckind_);
}
inline void RpcRequestHeaderProto::set_rpckind(::hadoop::common::RpcKindProto value) {
  assert(::hadoop::common::RpcKindProto_IsValid(value));
  set_has_rpckind();
  rpckind_ = value;
}

// optional .hadoop.common.RpcRequestHeaderProto.OperationProto rpcOp = 2;
inline bool RpcRequestHeaderProto::has_rpcop() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RpcRequestHeaderProto::set_has_rpcop() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RpcRequestHeaderProto::clear_has_rpcop() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RpcRequestHeaderProto::clear_rpcop() {
  rpcop_ = 0;
  clear_has_rpcop();
}
inline ::hadoop::common::RpcRequestHeaderProto_OperationProto RpcRequestHeaderProto::rpcop() const {
  return static_cast< ::hadoop::common::RpcRequestHeaderProto_OperationProto >(rpcop_);
}
inline void RpcRequestHeaderProto::set_rpcop(::hadoop::common::RpcRequestHeaderProto_OperationProto value) {
  assert(::hadoop::common::RpcRequestHeaderProto_OperationProto_IsValid(value));
  set_has_rpcop();
  rpcop_ = value;
}

// required sint32 callId = 3;
inline bool RpcRequestHeaderProto::has_callid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RpcRequestHeaderProto::set_has_callid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RpcRequestHeaderProto::clear_has_callid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RpcRequestHeaderProto::clear_callid() {
  callid_ = 0;
  clear_has_callid();
}
inline ::google::protobuf::int32 RpcRequestHeaderProto::callid() const {
  return callid_;
}
inline void RpcRequestHeaderProto::set_callid(::google::protobuf::int32 value) {
  set_has_callid();
  callid_ = value;
}

// required bytes clientId = 4;
inline bool RpcRequestHeaderProto::has_clientid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RpcRequestHeaderProto::set_has_clientid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RpcRequestHeaderProto::clear_has_clientid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RpcRequestHeaderProto::clear_clientid() {
  if (clientid_ != &::google::protobuf::internal::kEmptyString) {
    clientid_->clear();
  }
  clear_has_clientid();
}
inline const ::std::string& RpcRequestHeaderProto::clientid() const {
  return *clientid_;
}
inline void RpcRequestHeaderProto::set_clientid(const ::std::string& value) {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  clientid_->assign(value);
}
inline void RpcRequestHeaderProto::set_clientid(const char* value) {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  clientid_->assign(value);
}
inline void RpcRequestHeaderProto::set_clientid(const void* value, size_t size) {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  clientid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcRequestHeaderProto::mutable_clientid() {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  return clientid_;
}
inline ::std::string* RpcRequestHeaderProto::release_clientid() {
  clear_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clientid_;
    clientid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcRequestHeaderProto::set_allocated_clientid(::std::string* clientid) {
  if (clientid_ != &::google::protobuf::internal::kEmptyString) {
    delete clientid_;
  }
  if (clientid) {
    set_has_clientid();
    clientid_ = clientid;
  } else {
    clear_has_clientid();
    clientid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional sint32 retryCount = 5 [default = -1];
inline bool RpcRequestHeaderProto::has_retrycount() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RpcRequestHeaderProto::set_has_retrycount() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RpcRequestHeaderProto::clear_has_retrycount() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RpcRequestHeaderProto::clear_retrycount() {
  retrycount_ = -1;
  clear_has_retrycount();
}
inline ::google::protobuf::int32 RpcRequestHeaderProto::retrycount() const {
  return retrycount_;
}
inline void RpcRequestHeaderProto::set_retrycount(::google::protobuf::int32 value) {
  set_has_retrycount();
  retrycount_ = value;
}

// optional .hadoop.common.RPCTraceInfoProto traceInfo = 6;
inline bool RpcRequestHeaderProto::has_traceinfo() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void RpcRequestHeaderProto::set_has_traceinfo() {
  _has_bits_[0] |= 0x00000020u;
}
inline void RpcRequestHeaderProto::clear_has_traceinfo() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void RpcRequestHeaderProto::clear_traceinfo() {
  if (traceinfo_ != NULL) traceinfo_->::hadoop::common::RPCTraceInfoProto::Clear();
  clear_has_traceinfo();
}
inline const ::hadoop::common::RPCTraceInfoProto& RpcRequestHeaderProto::traceinfo() const {
  return traceinfo_ != NULL ? *traceinfo_ : *default_instance_->traceinfo_;
}
inline ::hadoop::common::RPCTraceInfoProto* RpcRequestHeaderProto::mutable_traceinfo() {
  set_has_traceinfo();
  if (traceinfo_ == NULL) traceinfo_ = new ::hadoop::common::RPCTraceInfoProto;
  return traceinfo_;
}
inline ::hadoop::common::RPCTraceInfoProto* RpcRequestHeaderProto::release_traceinfo() {
  clear_has_traceinfo();
  ::hadoop::common::RPCTraceInfoProto* temp = traceinfo_;
  traceinfo_ = NULL;
  return temp;
}
inline void RpcRequestHeaderProto::set_allocated_traceinfo(::hadoop::common::RPCTraceInfoProto* traceinfo) {
  delete traceinfo_;
  traceinfo_ = traceinfo;
  if (traceinfo) {
    set_has_traceinfo();
  } else {
    clear_has_traceinfo();
  }
}

// -------------------------------------------------------------------

// RpcResponseHeaderProto

// required uint32 callId = 1;
inline bool RpcResponseHeaderProto::has_callid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RpcResponseHeaderProto::set_has_callid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RpcResponseHeaderProto::clear_has_callid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RpcResponseHeaderProto::clear_callid() {
  callid_ = 0u;
  clear_has_callid();
}
inline ::google::protobuf::uint32 RpcResponseHeaderProto::callid() const {
  return callid_;
}
inline void RpcResponseHeaderProto::set_callid(::google::protobuf::uint32 value) {
  set_has_callid();
  callid_ = value;
}

// required .hadoop.common.RpcResponseHeaderProto.RpcStatusProto status = 2;
inline bool RpcResponseHeaderProto::has_status() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RpcResponseHeaderProto::set_has_status() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RpcResponseHeaderProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RpcResponseHeaderProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::common::RpcResponseHeaderProto_RpcStatusProto RpcResponseHeaderProto::status() const {
  return static_cast< ::hadoop::common::RpcResponseHeaderProto_RpcStatusProto >(status_);
}
inline void RpcResponseHeaderProto::set_status(::hadoop::common::RpcResponseHeaderProto_RpcStatusProto value) {
  assert(::hadoop::common::RpcResponseHeaderProto_RpcStatusProto_IsValid(value));
  set_has_status();
  status_ = value;
}

// optional uint32 serverIpcVersionNum = 3;
inline bool RpcResponseHeaderProto::has_serveripcversionnum() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RpcResponseHeaderProto::set_has_serveripcversionnum() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RpcResponseHeaderProto::clear_has_serveripcversionnum() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RpcResponseHeaderProto::clear_serveripcversionnum() {
  serveripcversionnum_ = 0u;
  clear_has_serveripcversionnum();
}
inline ::google::protobuf::uint32 RpcResponseHeaderProto::serveripcversionnum() const {
  return serveripcversionnum_;
}
inline void RpcResponseHeaderProto::set_serveripcversionnum(::google::protobuf::uint32 value) {
  set_has_serveripcversionnum();
  serveripcversionnum_ = value;
}

// optional string exceptionClassName = 4;
inline bool RpcResponseHeaderProto::has_exceptionclassname() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RpcResponseHeaderProto::set_has_exceptionclassname() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RpcResponseHeaderProto::clear_has_exceptionclassname() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RpcResponseHeaderProto::clear_exceptionclassname() {
  if (exceptionclassname_ != &::google::protobuf::internal::kEmptyString) {
    exceptionclassname_->clear();
  }
  clear_has_exceptionclassname();
}
inline const ::std::string& RpcResponseHeaderProto::exceptionclassname() const {
  return *exceptionclassname_;
}
inline void RpcResponseHeaderProto::set_exceptionclassname(const ::std::string& value) {
  set_has_exceptionclassname();
  if (exceptionclassname_ == &::google::protobuf::internal::kEmptyString) {
    exceptionclassname_ = new ::std::string;
  }
  exceptionclassname_->assign(value);
}
inline void RpcResponseHeaderProto::set_exceptionclassname(const char* value) {
  set_has_exceptionclassname();
  if (exceptionclassname_ == &::google::protobuf::internal::kEmptyString) {
    exceptionclassname_ = new ::std::string;
  }
  exceptionclassname_->assign(value);
}
inline void RpcResponseHeaderProto::set_exceptionclassname(const char* value, size_t size) {
  set_has_exceptionclassname();
  if (exceptionclassname_ == &::google::protobuf::internal::kEmptyString) {
    exceptionclassname_ = new ::std::string;
  }
  exceptionclassname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcResponseHeaderProto::mutable_exceptionclassname() {
  set_has_exceptionclassname();
  if (exceptionclassname_ == &::google::protobuf::internal::kEmptyString) {
    exceptionclassname_ = new ::std::string;
  }
  return exceptionclassname_;
}
inline ::std::string* RpcResponseHeaderProto::release_exceptionclassname() {
  clear_has_exceptionclassname();
  if (exceptionclassname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = exceptionclassname_;
    exceptionclassname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcResponseHeaderProto::set_allocated_exceptionclassname(::std::string* exceptionclassname) {
  if (exceptionclassname_ != &::google::protobuf::internal::kEmptyString) {
    delete exceptionclassname_;
  }
  if (exceptionclassname) {
    set_has_exceptionclassname();
    exceptionclassname_ = exceptionclassname;
  } else {
    clear_has_exceptionclassname();
    exceptionclassname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string errorMsg = 5;
inline bool RpcResponseHeaderProto::has_errormsg() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RpcResponseHeaderProto::set_has_errormsg() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RpcResponseHeaderProto::clear_has_errormsg() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RpcResponseHeaderProto::clear_errormsg() {
  if (errormsg_ != &::google::protobuf::internal::kEmptyString) {
    errormsg_->clear();
  }
  clear_has_errormsg();
}
inline const ::std::string& RpcResponseHeaderProto::errormsg() const {
  return *errormsg_;
}
inline void RpcResponseHeaderProto::set_errormsg(const ::std::string& value) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::kEmptyString) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(value);
}
inline void RpcResponseHeaderProto::set_errormsg(const char* value) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::kEmptyString) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(value);
}
inline void RpcResponseHeaderProto::set_errormsg(const char* value, size_t size) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::kEmptyString) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcResponseHeaderProto::mutable_errormsg() {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::kEmptyString) {
    errormsg_ = new ::std::string;
  }
  return errormsg_;
}
inline ::std::string* RpcResponseHeaderProto::release_errormsg() {
  clear_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = errormsg_;
    errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcResponseHeaderProto::set_allocated_errormsg(::std::string* errormsg) {
  if (errormsg_ != &::google::protobuf::internal::kEmptyString) {
    delete errormsg_;
  }
  if (errormsg) {
    set_has_errormsg();
    errormsg_ = errormsg;
  } else {
    clear_has_errormsg();
    errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.common.RpcResponseHeaderProto.RpcErrorCodeProto errorDetail = 6;
inline bool RpcResponseHeaderProto::has_errordetail() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void RpcResponseHeaderProto::set_has_errordetail() {
  _has_bits_[0] |= 0x00000020u;
}
inline void RpcResponseHeaderProto::clear_has_errordetail() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void RpcResponseHeaderProto::clear_errordetail() {
  errordetail_ = 1;
  clear_has_errordetail();
}
inline ::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto RpcResponseHeaderProto::errordetail() const {
  return static_cast< ::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto >(errordetail_);
}
inline void RpcResponseHeaderProto::set_errordetail(::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto value) {
  assert(::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto_IsValid(value));
  set_has_errordetail();
  errordetail_ = value;
}

// optional bytes clientId = 7;
inline bool RpcResponseHeaderProto::has_clientid() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void RpcResponseHeaderProto::set_has_clientid() {
  _has_bits_[0] |= 0x00000040u;
}
inline void RpcResponseHeaderProto::clear_has_clientid() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void RpcResponseHeaderProto::clear_clientid() {
  if (clientid_ != &::google::protobuf::internal::kEmptyString) {
    clientid_->clear();
  }
  clear_has_clientid();
}
inline const ::std::string& RpcResponseHeaderProto::clientid() const {
  return *clientid_;
}
inline void RpcResponseHeaderProto::set_clientid(const ::std::string& value) {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  clientid_->assign(value);
}
inline void RpcResponseHeaderProto::set_clientid(const char* value) {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  clientid_->assign(value);
}
inline void RpcResponseHeaderProto::set_clientid(const void* value, size_t size) {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  clientid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcResponseHeaderProto::mutable_clientid() {
  set_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    clientid_ = new ::std::string;
  }
  return clientid_;
}
inline ::std::string* RpcResponseHeaderProto::release_clientid() {
  clear_has_clientid();
  if (clientid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clientid_;
    clientid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcResponseHeaderProto::set_allocated_clientid(::std::string* clientid) {
  if (clientid_ != &::google::protobuf::internal::kEmptyString) {
    delete clientid_;
  }
  if (clientid) {
    set_has_clientid();
    clientid_ = clientid;
  } else {
    clear_has_clientid();
    clientid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional sint32 retryCount = 8 [default = -1];
inline bool RpcResponseHeaderProto::has_retrycount() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void RpcResponseHeaderProto::set_has_retrycount() {
  _has_bits_[0] |= 0x00000080u;
}
inline void RpcResponseHeaderProto::clear_has_retrycount() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void RpcResponseHeaderProto::clear_retrycount() {
  retrycount_ = -1;
  clear_has_retrycount();
}
inline ::google::protobuf::int32 RpcResponseHeaderProto::retrycount() const {
  return retrycount_;
}
inline void RpcResponseHeaderProto::set_retrycount(::google::protobuf::int32 value) {
  set_has_retrycount();
  retrycount_ = value;
}

// -------------------------------------------------------------------

// RpcSaslProto_SaslAuth

// required string method = 1;
inline bool RpcSaslProto_SaslAuth::has_method() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RpcSaslProto_SaslAuth::set_has_method() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RpcSaslProto_SaslAuth::clear_has_method() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RpcSaslProto_SaslAuth::clear_method() {
  if (method_ != &::google::protobuf::internal::kEmptyString) {
    method_->clear();
  }
  clear_has_method();
}
inline const ::std::string& RpcSaslProto_SaslAuth::method() const {
  return *method_;
}
inline void RpcSaslProto_SaslAuth::set_method(const ::std::string& value) {
  set_has_method();
  if (method_ == &::google::protobuf::internal::kEmptyString) {
    method_ = new ::std::string;
  }
  method_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_method(const char* value) {
  set_has_method();
  if (method_ == &::google::protobuf::internal::kEmptyString) {
    method_ = new ::std::string;
  }
  method_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_method(const char* value, size_t size) {
  set_has_method();
  if (method_ == &::google::protobuf::internal::kEmptyString) {
    method_ = new ::std::string;
  }
  method_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcSaslProto_SaslAuth::mutable_method() {
  set_has_method();
  if (method_ == &::google::protobuf::internal::kEmptyString) {
    method_ = new ::std::string;
  }
  return method_;
}
inline ::std::string* RpcSaslProto_SaslAuth::release_method() {
  clear_has_method();
  if (method_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = method_;
    method_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcSaslProto_SaslAuth::set_allocated_method(::std::string* method) {
  if (method_ != &::google::protobuf::internal::kEmptyString) {
    delete method_;
  }
  if (method) {
    set_has_method();
    method_ = method;
  } else {
    clear_has_method();
    method_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required string mechanism = 2;
inline bool RpcSaslProto_SaslAuth::has_mechanism() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RpcSaslProto_SaslAuth::set_has_mechanism() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RpcSaslProto_SaslAuth::clear_has_mechanism() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RpcSaslProto_SaslAuth::clear_mechanism() {
  if (mechanism_ != &::google::protobuf::internal::kEmptyString) {
    mechanism_->clear();
  }
  clear_has_mechanism();
}
inline const ::std::string& RpcSaslProto_SaslAuth::mechanism() const {
  return *mechanism_;
}
inline void RpcSaslProto_SaslAuth::set_mechanism(const ::std::string& value) {
  set_has_mechanism();
  if (mechanism_ == &::google::protobuf::internal::kEmptyString) {
    mechanism_ = new ::std::string;
  }
  mechanism_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_mechanism(const char* value) {
  set_has_mechanism();
  if (mechanism_ == &::google::protobuf::internal::kEmptyString) {
    mechanism_ = new ::std::string;
  }
  mechanism_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_mechanism(const char* value, size_t size) {
  set_has_mechanism();
  if (mechanism_ == &::google::protobuf::internal::kEmptyString) {
    mechanism_ = new ::std::string;
  }
  mechanism_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcSaslProto_SaslAuth::mutable_mechanism() {
  set_has_mechanism();
  if (mechanism_ == &::google::protobuf::internal::kEmptyString) {
    mechanism_ = new ::std::string;
  }
  return mechanism_;
}
inline ::std::string* RpcSaslProto_SaslAuth::release_mechanism() {
  clear_has_mechanism();
  if (mechanism_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = mechanism_;
    mechanism_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcSaslProto_SaslAuth::set_allocated_mechanism(::std::string* mechanism) {
  if (mechanism_ != &::google::protobuf::internal::kEmptyString) {
    delete mechanism_;
  }
  if (mechanism) {
    set_has_mechanism();
    mechanism_ = mechanism;
  } else {
    clear_has_mechanism();
    mechanism_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string protocol = 3;
inline bool RpcSaslProto_SaslAuth::has_protocol() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RpcSaslProto_SaslAuth::set_has_protocol() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RpcSaslProto_SaslAuth::clear_has_protocol() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RpcSaslProto_SaslAuth::clear_protocol() {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    protocol_->clear();
  }
  clear_has_protocol();
}
inline const ::std::string& RpcSaslProto_SaslAuth::protocol() const {
  return *protocol_;
}
inline void RpcSaslProto_SaslAuth::set_protocol(const ::std::string& value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_protocol(const char* value) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_protocol(const char* value, size_t size) {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  protocol_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcSaslProto_SaslAuth::mutable_protocol() {
  set_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    protocol_ = new ::std::string;
  }
  return protocol_;
}
inline ::std::string* RpcSaslProto_SaslAuth::release_protocol() {
  clear_has_protocol();
  if (protocol_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = protocol_;
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcSaslProto_SaslAuth::set_allocated_protocol(::std::string* protocol) {
  if (protocol_ != &::google::protobuf::internal::kEmptyString) {
    delete protocol_;
  }
  if (protocol) {
    set_has_protocol();
    protocol_ = protocol;
  } else {
    clear_has_protocol();
    protocol_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string serverId = 4;
inline bool RpcSaslProto_SaslAuth::has_serverid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RpcSaslProto_SaslAuth::set_has_serverid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RpcSaslProto_SaslAuth::clear_has_serverid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RpcSaslProto_SaslAuth::clear_serverid() {
  if (serverid_ != &::google::protobuf::internal::kEmptyString) {
    serverid_->clear();
  }
  clear_has_serverid();
}
inline const ::std::string& RpcSaslProto_SaslAuth::serverid() const {
  return *serverid_;
}
inline void RpcSaslProto_SaslAuth::set_serverid(const ::std::string& value) {
  set_has_serverid();
  if (serverid_ == &::google::protobuf::internal::kEmptyString) {
    serverid_ = new ::std::string;
  }
  serverid_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_serverid(const char* value) {
  set_has_serverid();
  if (serverid_ == &::google::protobuf::internal::kEmptyString) {
    serverid_ = new ::std::string;
  }
  serverid_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_serverid(const char* value, size_t size) {
  set_has_serverid();
  if (serverid_ == &::google::protobuf::internal::kEmptyString) {
    serverid_ = new ::std::string;
  }
  serverid_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcSaslProto_SaslAuth::mutable_serverid() {
  set_has_serverid();
  if (serverid_ == &::google::protobuf::internal::kEmptyString) {
    serverid_ = new ::std::string;
  }
  return serverid_;
}
inline ::std::string* RpcSaslProto_SaslAuth::release_serverid() {
  clear_has_serverid();
  if (serverid_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = serverid_;
    serverid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcSaslProto_SaslAuth::set_allocated_serverid(::std::string* serverid) {
  if (serverid_ != &::google::protobuf::internal::kEmptyString) {
    delete serverid_;
  }
  if (serverid) {
    set_has_serverid();
    serverid_ = serverid;
  } else {
    clear_has_serverid();
    serverid_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional bytes challenge = 5;
inline bool RpcSaslProto_SaslAuth::has_challenge() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RpcSaslProto_SaslAuth::set_has_challenge() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RpcSaslProto_SaslAuth::clear_has_challenge() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RpcSaslProto_SaslAuth::clear_challenge() {
  if (challenge_ != &::google::protobuf::internal::kEmptyString) {
    challenge_->clear();
  }
  clear_has_challenge();
}
inline const ::std::string& RpcSaslProto_SaslAuth::challenge() const {
  return *challenge_;
}
inline void RpcSaslProto_SaslAuth::set_challenge(const ::std::string& value) {
  set_has_challenge();
  if (challenge_ == &::google::protobuf::internal::kEmptyString) {
    challenge_ = new ::std::string;
  }
  challenge_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_challenge(const char* value) {
  set_has_challenge();
  if (challenge_ == &::google::protobuf::internal::kEmptyString) {
    challenge_ = new ::std::string;
  }
  challenge_->assign(value);
}
inline void RpcSaslProto_SaslAuth::set_challenge(const void* value, size_t size) {
  set_has_challenge();
  if (challenge_ == &::google::protobuf::internal::kEmptyString) {
    challenge_ = new ::std::string;
  }
  challenge_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcSaslProto_SaslAuth::mutable_challenge() {
  set_has_challenge();
  if (challenge_ == &::google::protobuf::internal::kEmptyString) {
    challenge_ = new ::std::string;
  }
  return challenge_;
}
inline ::std::string* RpcSaslProto_SaslAuth::release_challenge() {
  clear_has_challenge();
  if (challenge_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = challenge_;
    challenge_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcSaslProto_SaslAuth::set_allocated_challenge(::std::string* challenge) {
  if (challenge_ != &::google::protobuf::internal::kEmptyString) {
    delete challenge_;
  }
  if (challenge) {
    set_has_challenge();
    challenge_ = challenge;
  } else {
    clear_has_challenge();
    challenge_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// RpcSaslProto

// optional uint32 version = 1;
inline bool RpcSaslProto::has_version() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RpcSaslProto::set_has_version() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RpcSaslProto::clear_has_version() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RpcSaslProto::clear_version() {
  version_ = 0u;
  clear_has_version();
}
inline ::google::protobuf::uint32 RpcSaslProto::version() const {
  return version_;
}
inline void RpcSaslProto::set_version(::google::protobuf::uint32 value) {
  set_has_version();
  version_ = value;
}

// required .hadoop.common.RpcSaslProto.SaslState state = 2;
inline bool RpcSaslProto::has_state() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RpcSaslProto::set_has_state() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RpcSaslProto::clear_has_state() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RpcSaslProto::clear_state() {
  state_ = 0;
  clear_has_state();
}
inline ::hadoop::common::RpcSaslProto_SaslState RpcSaslProto::state() const {
  return static_cast< ::hadoop::common::RpcSaslProto_SaslState >(state_);
}
inline void RpcSaslProto::set_state(::hadoop::common::RpcSaslProto_SaslState value) {
  assert(::hadoop::common::RpcSaslProto_SaslState_IsValid(value));
  set_has_state();
  state_ = value;
}

// optional bytes token = 3;
inline bool RpcSaslProto::has_token() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RpcSaslProto::set_has_token() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RpcSaslProto::clear_has_token() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RpcSaslProto::clear_token() {
  if (token_ != &::google::protobuf::internal::kEmptyString) {
    token_->clear();
  }
  clear_has_token();
}
inline const ::std::string& RpcSaslProto::token() const {
  return *token_;
}
inline void RpcSaslProto::set_token(const ::std::string& value) {
  set_has_token();
  if (token_ == &::google::protobuf::internal::kEmptyString) {
    token_ = new ::std::string;
  }
  token_->assign(value);
}
inline void RpcSaslProto::set_token(const char* value) {
  set_has_token();
  if (token_ == &::google::protobuf::internal::kEmptyString) {
    token_ = new ::std::string;
  }
  token_->assign(value);
}
inline void RpcSaslProto::set_token(const void* value, size_t size) {
  set_has_token();
  if (token_ == &::google::protobuf::internal::kEmptyString) {
    token_ = new ::std::string;
  }
  token_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* RpcSaslProto::mutable_token() {
  set_has_token();
  if (token_ == &::google::protobuf::internal::kEmptyString) {
    token_ = new ::std::string;
  }
  return token_;
}
inline ::std::string* RpcSaslProto::release_token() {
  clear_has_token();
  if (token_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = token_;
    token_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void RpcSaslProto::set_allocated_token(::std::string* token) {
  if (token_ != &::google::protobuf::internal::kEmptyString) {
    delete token_;
  }
  if (token) {
    set_has_token();
    token_ = token;
  } else {
    clear_has_token();
    token_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.common.RpcSaslProto.SaslAuth auths = 4;
inline int RpcSaslProto::auths_size() const {
  return auths_.size();
}
inline void RpcSaslProto::clear_auths() {
  auths_.Clear();
}
inline const ::hadoop::common::RpcSaslProto_SaslAuth& RpcSaslProto::auths(int index) const {
  return auths_.Get(index);
}
inline ::hadoop::common::RpcSaslProto_SaslAuth* RpcSaslProto::mutable_auths(int index) {
  return auths_.Mutable(index);
}
inline ::hadoop::common::RpcSaslProto_SaslAuth* RpcSaslProto::add_auths() {
  return auths_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::common::RpcSaslProto_SaslAuth >&
RpcSaslProto::auths() const {
  return auths_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::common::RpcSaslProto_SaslAuth >*
RpcSaslProto::mutable_auths() {
  return &auths_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::RpcRequestHeaderProto_OperationProto>() {
  return ::hadoop::common::RpcRequestHeaderProto_OperationProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::RpcResponseHeaderProto_RpcStatusProto>() {
  return ::hadoop::common::RpcResponseHeaderProto_RpcStatusProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto>() {
  return ::hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::RpcSaslProto_SaslState>() {
  return ::hadoop::common::RpcSaslProto_SaslState_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::common::RpcKindProto>() {
  return ::hadoop::common::RpcKindProto_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_common_2fRpcHeader_2eproto__INCLUDED
