// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/GenericRefreshProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "common/GenericRefreshProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace common {

namespace {

const ::google::protobuf::Descriptor* GenericRefreshRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GenericRefreshRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GenericRefreshResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GenericRefreshResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GenericRefreshResponseCollectionProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GenericRefreshResponseCollectionProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_common_2fGenericRefreshProtocol_2eproto() {
  protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "common/GenericRefreshProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  GenericRefreshRequestProto_descriptor_ = file->message_type(0);
  static const int GenericRefreshRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshRequestProto, identifier_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshRequestProto, args_),
  };
  GenericRefreshRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GenericRefreshRequestProto_descriptor_,
      GenericRefreshRequestProto::default_instance_,
      GenericRefreshRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GenericRefreshRequestProto));
  GenericRefreshResponseProto_descriptor_ = file->message_type(1);
  static const int GenericRefreshResponseProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseProto, exitstatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseProto, usermessage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseProto, sendername_),
  };
  GenericRefreshResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GenericRefreshResponseProto_descriptor_,
      GenericRefreshResponseProto::default_instance_,
      GenericRefreshResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GenericRefreshResponseProto));
  GenericRefreshResponseCollectionProto_descriptor_ = file->message_type(2);
  static const int GenericRefreshResponseCollectionProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseCollectionProto, responses_),
  };
  GenericRefreshResponseCollectionProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GenericRefreshResponseCollectionProto_descriptor_,
      GenericRefreshResponseCollectionProto::default_instance_,
      GenericRefreshResponseCollectionProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseCollectionProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GenericRefreshResponseCollectionProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GenericRefreshResponseCollectionProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_common_2fGenericRefreshProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GenericRefreshRequestProto_descriptor_, &GenericRefreshRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GenericRefreshResponseProto_descriptor_, &GenericRefreshResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GenericRefreshResponseCollectionProto_descriptor_, &GenericRefreshResponseCollectionProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_common_2fGenericRefreshProtocol_2eproto() {
  delete GenericRefreshRequestProto::default_instance_;
  delete GenericRefreshRequestProto_reflection_;
  delete GenericRefreshResponseProto::default_instance_;
  delete GenericRefreshResponseProto_reflection_;
  delete GenericRefreshResponseCollectionProto::default_instance_;
  delete GenericRefreshResponseCollectionProto_reflection_;
}

void protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n#common/GenericRefreshProtocol.proto\022\rh"
    "adoop.common\">\n\032GenericRefreshRequestPro"
    "to\022\022\n\nidentifier\030\001 \001(\t\022\014\n\004args\030\002 \003(\t\"Z\n\033"
    "GenericRefreshResponseProto\022\022\n\nexitStatu"
    "s\030\001 \001(\005\022\023\n\013userMessage\030\002 \001(\t\022\022\n\nsenderNa"
    "me\030\003 \001(\t\"f\n%GenericRefreshResponseCollec"
    "tionProto\022=\n\tresponses\030\001 \003(\0132*.hadoop.co"
    "mmon.GenericRefreshResponseProto2\213\001\n\035Gen"
    "ericRefreshProtocolService\022j\n\007refresh\022)."
    "hadoop.common.GenericRefreshRequestProto"
    "\0324.hadoop.common.GenericRefreshResponseC"
    "ollectionProtoBA\n\033org.apache.hadoop.ipc."
    "protoB\034GenericRefreshProtocolProtos\210\001\001\240\001"
    "\001", 521);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "common/GenericRefreshProtocol.proto", &protobuf_RegisterTypes);
  GenericRefreshRequestProto::default_instance_ = new GenericRefreshRequestProto();
  GenericRefreshResponseProto::default_instance_ = new GenericRefreshResponseProto();
  GenericRefreshResponseCollectionProto::default_instance_ = new GenericRefreshResponseCollectionProto();
  GenericRefreshRequestProto::default_instance_->InitAsDefaultInstance();
  GenericRefreshResponseProto::default_instance_->InitAsDefaultInstance();
  GenericRefreshResponseCollectionProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_common_2fGenericRefreshProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_common_2fGenericRefreshProtocol_2eproto {
  StaticDescriptorInitializer_common_2fGenericRefreshProtocol_2eproto() {
    protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  }
} static_descriptor_initializer_common_2fGenericRefreshProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int GenericRefreshRequestProto::kIdentifierFieldNumber;
const int GenericRefreshRequestProto::kArgsFieldNumber;
#endif  // !_MSC_VER

GenericRefreshRequestProto::GenericRefreshRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GenericRefreshRequestProto::InitAsDefaultInstance() {
}

GenericRefreshRequestProto::GenericRefreshRequestProto(const GenericRefreshRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GenericRefreshRequestProto::SharedCtor() {
  _cached_size_ = 0;
  identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GenericRefreshRequestProto::~GenericRefreshRequestProto() {
  SharedDtor();
}

void GenericRefreshRequestProto::SharedDtor() {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    delete identifier_;
  }
  if (this != default_instance_) {
  }
}

void GenericRefreshRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GenericRefreshRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GenericRefreshRequestProto_descriptor_;
}

const GenericRefreshRequestProto& GenericRefreshRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  return *default_instance_;
}

GenericRefreshRequestProto* GenericRefreshRequestProto::default_instance_ = NULL;

GenericRefreshRequestProto* GenericRefreshRequestProto::New() const {
  return new GenericRefreshRequestProto;
}

void GenericRefreshRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_identifier()) {
      if (identifier_ != &::google::protobuf::internal::kEmptyString) {
        identifier_->clear();
      }
    }
  }
  args_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GenericRefreshRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string identifier = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_identifier()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->identifier().data(), this->identifier().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_args;
        break;
      }

      // repeated string args = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_args:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_args()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->args(this->args_size() - 1).data(),
            this->args(this->args_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_args;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GenericRefreshRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional string identifier = 1;
  if (has_identifier()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->identifier().data(), this->identifier().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->identifier(), output);
  }

  // repeated string args = 2;
  for (int i = 0; i < this->args_size(); i++) {
  ::google::protobuf::internal::WireFormat::VerifyUTF8String(
    this->args(i).data(), this->args(i).length(),
    ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->args(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GenericRefreshRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional string identifier = 1;
  if (has_identifier()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->identifier().data(), this->identifier().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->identifier(), target);
  }

  // repeated string args = 2;
  for (int i = 0; i < this->args_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->args(i).data(), this->args(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->args(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GenericRefreshRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional string identifier = 1;
    if (has_identifier()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->identifier());
    }

  }
  // repeated string args = 2;
  total_size += 1 * this->args_size();
  for (int i = 0; i < this->args_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->args(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GenericRefreshRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GenericRefreshRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GenericRefreshRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GenericRefreshRequestProto::MergeFrom(const GenericRefreshRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  args_.MergeFrom(from.args_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_identifier()) {
      set_identifier(from.identifier());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GenericRefreshRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GenericRefreshRequestProto::CopyFrom(const GenericRefreshRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GenericRefreshRequestProto::IsInitialized() const {

  return true;
}

void GenericRefreshRequestProto::Swap(GenericRefreshRequestProto* other) {
  if (other != this) {
    std::swap(identifier_, other->identifier_);
    args_.Swap(&other->args_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GenericRefreshRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GenericRefreshRequestProto_descriptor_;
  metadata.reflection = GenericRefreshRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GenericRefreshResponseProto::kExitStatusFieldNumber;
const int GenericRefreshResponseProto::kUserMessageFieldNumber;
const int GenericRefreshResponseProto::kSenderNameFieldNumber;
#endif  // !_MSC_VER

GenericRefreshResponseProto::GenericRefreshResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GenericRefreshResponseProto::InitAsDefaultInstance() {
}

GenericRefreshResponseProto::GenericRefreshResponseProto(const GenericRefreshResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GenericRefreshResponseProto::SharedCtor() {
  _cached_size_ = 0;
  exitstatus_ = 0;
  usermessage_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  sendername_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GenericRefreshResponseProto::~GenericRefreshResponseProto() {
  SharedDtor();
}

void GenericRefreshResponseProto::SharedDtor() {
  if (usermessage_ != &::google::protobuf::internal::kEmptyString) {
    delete usermessage_;
  }
  if (sendername_ != &::google::protobuf::internal::kEmptyString) {
    delete sendername_;
  }
  if (this != default_instance_) {
  }
}

void GenericRefreshResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GenericRefreshResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GenericRefreshResponseProto_descriptor_;
}

const GenericRefreshResponseProto& GenericRefreshResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  return *default_instance_;
}

GenericRefreshResponseProto* GenericRefreshResponseProto::default_instance_ = NULL;

GenericRefreshResponseProto* GenericRefreshResponseProto::New() const {
  return new GenericRefreshResponseProto;
}

void GenericRefreshResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    exitstatus_ = 0;
    if (has_usermessage()) {
      if (usermessage_ != &::google::protobuf::internal::kEmptyString) {
        usermessage_->clear();
      }
    }
    if (has_sendername()) {
      if (sendername_ != &::google::protobuf::internal::kEmptyString) {
        sendername_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GenericRefreshResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 exitStatus = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exitstatus_)));
          set_has_exitstatus();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_userMessage;
        break;
      }

      // optional string userMessage = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_userMessage:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_usermessage()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->usermessage().data(), this->usermessage().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_senderName;
        break;
      }

      // optional string senderName = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_senderName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_sendername()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->sendername().data(), this->sendername().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GenericRefreshResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional int32 exitStatus = 1;
  if (has_exitstatus()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->exitstatus(), output);
  }

  // optional string userMessage = 2;
  if (has_usermessage()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->usermessage().data(), this->usermessage().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->usermessage(), output);
  }

  // optional string senderName = 3;
  if (has_sendername()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->sendername().data(), this->sendername().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->sendername(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GenericRefreshResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional int32 exitStatus = 1;
  if (has_exitstatus()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->exitstatus(), target);
  }

  // optional string userMessage = 2;
  if (has_usermessage()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->usermessage().data(), this->usermessage().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->usermessage(), target);
  }

  // optional string senderName = 3;
  if (has_sendername()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->sendername().data(), this->sendername().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->sendername(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GenericRefreshResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional int32 exitStatus = 1;
    if (has_exitstatus()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->exitstatus());
    }

    // optional string userMessage = 2;
    if (has_usermessage()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->usermessage());
    }

    // optional string senderName = 3;
    if (has_sendername()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->sendername());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GenericRefreshResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GenericRefreshResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GenericRefreshResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GenericRefreshResponseProto::MergeFrom(const GenericRefreshResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_exitstatus()) {
      set_exitstatus(from.exitstatus());
    }
    if (from.has_usermessage()) {
      set_usermessage(from.usermessage());
    }
    if (from.has_sendername()) {
      set_sendername(from.sendername());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GenericRefreshResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GenericRefreshResponseProto::CopyFrom(const GenericRefreshResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GenericRefreshResponseProto::IsInitialized() const {

  return true;
}

void GenericRefreshResponseProto::Swap(GenericRefreshResponseProto* other) {
  if (other != this) {
    std::swap(exitstatus_, other->exitstatus_);
    std::swap(usermessage_, other->usermessage_);
    std::swap(sendername_, other->sendername_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GenericRefreshResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GenericRefreshResponseProto_descriptor_;
  metadata.reflection = GenericRefreshResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GenericRefreshResponseCollectionProto::kResponsesFieldNumber;
#endif  // !_MSC_VER

GenericRefreshResponseCollectionProto::GenericRefreshResponseCollectionProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GenericRefreshResponseCollectionProto::InitAsDefaultInstance() {
}

GenericRefreshResponseCollectionProto::GenericRefreshResponseCollectionProto(const GenericRefreshResponseCollectionProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GenericRefreshResponseCollectionProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GenericRefreshResponseCollectionProto::~GenericRefreshResponseCollectionProto() {
  SharedDtor();
}

void GenericRefreshResponseCollectionProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GenericRefreshResponseCollectionProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GenericRefreshResponseCollectionProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GenericRefreshResponseCollectionProto_descriptor_;
}

const GenericRefreshResponseCollectionProto& GenericRefreshResponseCollectionProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_common_2fGenericRefreshProtocol_2eproto();
  return *default_instance_;
}

GenericRefreshResponseCollectionProto* GenericRefreshResponseCollectionProto::default_instance_ = NULL;

GenericRefreshResponseCollectionProto* GenericRefreshResponseCollectionProto::New() const {
  return new GenericRefreshResponseCollectionProto;
}

void GenericRefreshResponseCollectionProto::Clear() {
  responses_.Clear();
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GenericRefreshResponseCollectionProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .hadoop.common.GenericRefreshResponseProto responses = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_responses:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
                input, add_responses()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(10)) goto parse_responses;
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GenericRefreshResponseCollectionProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // repeated .hadoop.common.GenericRefreshResponseProto responses = 1;
  for (int i = 0; i < this->responses_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->responses(i), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GenericRefreshResponseCollectionProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // repeated .hadoop.common.GenericRefreshResponseProto responses = 1;
  for (int i = 0; i < this->responses_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->responses(i), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GenericRefreshResponseCollectionProto::ByteSize() const {
  int total_size = 0;

  // repeated .hadoop.common.GenericRefreshResponseProto responses = 1;
  total_size += 1 * this->responses_size();
  for (int i = 0; i < this->responses_size(); i++) {
    total_size +=
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        this->responses(i));
  }

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GenericRefreshResponseCollectionProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GenericRefreshResponseCollectionProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GenericRefreshResponseCollectionProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GenericRefreshResponseCollectionProto::MergeFrom(const GenericRefreshResponseCollectionProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  responses_.MergeFrom(from.responses_);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GenericRefreshResponseCollectionProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GenericRefreshResponseCollectionProto::CopyFrom(const GenericRefreshResponseCollectionProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GenericRefreshResponseCollectionProto::IsInitialized() const {

  return true;
}

void GenericRefreshResponseCollectionProto::Swap(GenericRefreshResponseCollectionProto* other) {
  if (other != this) {
    responses_.Swap(&other->responses_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GenericRefreshResponseCollectionProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GenericRefreshResponseCollectionProto_descriptor_;
  metadata.reflection = GenericRefreshResponseCollectionProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
