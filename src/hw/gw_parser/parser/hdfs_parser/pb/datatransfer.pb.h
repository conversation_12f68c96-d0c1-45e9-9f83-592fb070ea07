// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: datatransfer.proto

#ifndef PROTOBUF_datatransfer_2eproto__INCLUDED
#define PROTOBUF_datatransfer_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "Security.pb.h"
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_datatransfer_2eproto();
void protobuf_AssignDesc_datatransfer_2eproto();
void protobuf_ShutdownFile_datatransfer_2eproto();

class DataTransferEncryptorMessageProto;
class BaseHeaderProto;
class DataTransferTraceInfoProto;
class ClientOperationHeaderProto;
class CachingStrategyProto;
class OpReadBlockProto;
class ChecksumProto;
class OpWriteBlockProto;
class OpTransferBlockProto;
class OpReplaceBlockProto;
class OpCopyBlockProto;
class OpBlockChecksumProto;
class ShortCircuitShmIdProto;
class ShortCircuitShmSlotProto;
class OpRequestShortCircuitAccessProto;
class ReleaseShortCircuitAccessRequestProto;
class ReleaseShortCircuitAccessResponseProto;
class ShortCircuitShmRequestProto;
class ShortCircuitShmResponseProto;
class PacketHeaderProto;
class PipelineAckProto;
class ReadOpChecksumInfoProto;
class BlockOpResponseProto;
class ClientReadStatusProto;
class DNTransferAckProto;
class OpBlockChecksumResponseProto;

enum DataTransferEncryptorMessageProto_DataTransferEncryptorStatus {
  DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_SUCCESS = 0,
  DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_ERROR_UNKNOWN_KEY = 1,
  DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_ERROR = 2
};
bool DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_IsValid(int value);
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_MIN = DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_SUCCESS;
const DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_MAX = DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_ERROR;
const int DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_ARRAYSIZE = DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_MAX + 1;

const ::google::protobuf::EnumDescriptor* DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor();
inline const ::std::string& DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_Name(DataTransferEncryptorMessageProto_DataTransferEncryptorStatus value) {
  return ::google::protobuf::internal::NameOfEnum(
    DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor(), value);
}
inline bool DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_Parse(
    const ::std::string& name, DataTransferEncryptorMessageProto_DataTransferEncryptorStatus* value) {
  return ::google::protobuf::internal::ParseNamedEnum<DataTransferEncryptorMessageProto_DataTransferEncryptorStatus>(
    DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor(), name, value);
}
enum OpWriteBlockProto_BlockConstructionStage {
  OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_APPEND = 0,
  OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_APPEND_RECOVERY = 1,
  OpWriteBlockProto_BlockConstructionStage_DATA_STREAMING = 2,
  OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_STREAMING_RECOVERY = 3,
  OpWriteBlockProto_BlockConstructionStage_PIPELINE_CLOSE = 4,
  OpWriteBlockProto_BlockConstructionStage_PIPELINE_CLOSE_RECOVERY = 5,
  OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_CREATE = 6,
  OpWriteBlockProto_BlockConstructionStage_TRANSFER_RBW = 7,
  OpWriteBlockProto_BlockConstructionStage_TRANSFER_FINALIZED = 8
};
bool OpWriteBlockProto_BlockConstructionStage_IsValid(int value);
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_MIN = OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_APPEND;
const OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_MAX = OpWriteBlockProto_BlockConstructionStage_TRANSFER_FINALIZED;
const int OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_ARRAYSIZE = OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_MAX + 1;

const ::google::protobuf::EnumDescriptor* OpWriteBlockProto_BlockConstructionStage_descriptor();
inline const ::std::string& OpWriteBlockProto_BlockConstructionStage_Name(OpWriteBlockProto_BlockConstructionStage value) {
  return ::google::protobuf::internal::NameOfEnum(
    OpWriteBlockProto_BlockConstructionStage_descriptor(), value);
}
inline bool OpWriteBlockProto_BlockConstructionStage_Parse(
    const ::std::string& name, OpWriteBlockProto_BlockConstructionStage* value) {
  return ::google::protobuf::internal::ParseNamedEnum<OpWriteBlockProto_BlockConstructionStage>(
    OpWriteBlockProto_BlockConstructionStage_descriptor(), name, value);
}
enum Status {
  SUCCESS = 0,
  ERROR = 1,
  ERROR_CHECKSUM = 2,
  ERROR_INVALID = 3,
  ERROR_EXISTS = 4,
  ERROR_ACCESS_TOKEN = 5,
  CHECKSUM_OK = 6,
  ERROR_UNSUPPORTED = 7,
  OOB_RESTART = 8,
  OOB_RESERVED1 = 9,
  OOB_RESERVED2 = 10,
  OOB_RESERVED3 = 11,
  IN_PROGRESS = 12
};
bool Status_IsValid(int value);
const Status Status_MIN = SUCCESS;
const Status Status_MAX = IN_PROGRESS;
const int Status_ARRAYSIZE = Status_MAX + 1;

const ::google::protobuf::EnumDescriptor* Status_descriptor();
inline const ::std::string& Status_Name(Status value) {
  return ::google::protobuf::internal::NameOfEnum(
    Status_descriptor(), value);
}
inline bool Status_Parse(
    const ::std::string& name, Status* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Status>(
    Status_descriptor(), name, value);
}
// ===================================================================

class DataTransferEncryptorMessageProto : public ::google::protobuf::Message {
 public:
  DataTransferEncryptorMessageProto();
  virtual ~DataTransferEncryptorMessageProto();

  DataTransferEncryptorMessageProto(const DataTransferEncryptorMessageProto& from);

  inline DataTransferEncryptorMessageProto& operator=(const DataTransferEncryptorMessageProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DataTransferEncryptorMessageProto& default_instance();

  void Swap(DataTransferEncryptorMessageProto* other);

  // implements Message ----------------------------------------------

  DataTransferEncryptorMessageProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DataTransferEncryptorMessageProto& from);
  void MergeFrom(const DataTransferEncryptorMessageProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorStatus;
  static const DataTransferEncryptorStatus SUCCESS = DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_SUCCESS;
  static const DataTransferEncryptorStatus ERROR_UNKNOWN_KEY = DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_ERROR_UNKNOWN_KEY;
  static const DataTransferEncryptorStatus ERROR = DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_ERROR;
  static inline bool DataTransferEncryptorStatus_IsValid(int value) {
    return DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_IsValid(value);
  }
  static const DataTransferEncryptorStatus DataTransferEncryptorStatus_MIN =
    DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_MIN;
  static const DataTransferEncryptorStatus DataTransferEncryptorStatus_MAX =
    DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_MAX;
  static const int DataTransferEncryptorStatus_ARRAYSIZE =
    DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_DataTransferEncryptorStatus_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  DataTransferEncryptorStatus_descriptor() {
    return DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor();
  }
  static inline const ::std::string& DataTransferEncryptorStatus_Name(DataTransferEncryptorStatus value) {
    return DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_Name(value);
  }
  static inline bool DataTransferEncryptorStatus_Parse(const ::std::string& name,
      DataTransferEncryptorStatus* value) {
    return DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.DataTransferEncryptorMessageProto.DataTransferEncryptorStatus status = 1;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 1;
  inline ::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus status() const;
  inline void set_status(::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus value);

  // optional bytes payload = 2;
  inline bool has_payload() const;
  inline void clear_payload();
  static const int kPayloadFieldNumber = 2;
  inline const ::std::string& payload() const;
  inline void set_payload(const ::std::string& value);
  inline void set_payload(const char* value);
  inline void set_payload(const void* value, size_t size);
  inline ::std::string* mutable_payload();
  inline ::std::string* release_payload();
  inline void set_allocated_payload(::std::string* payload);

  // optional string message = 3;
  inline bool has_message() const;
  inline void clear_message();
  static const int kMessageFieldNumber = 3;
  inline const ::std::string& message() const;
  inline void set_message(const ::std::string& value);
  inline void set_message(const char* value);
  inline void set_message(const char* value, size_t size);
  inline ::std::string* mutable_message();
  inline ::std::string* release_message();
  inline void set_allocated_message(::std::string* message);

  // repeated .hadoop.hdfs.CipherOptionProto cipherOption = 4;
  inline int cipheroption_size() const;
  inline void clear_cipheroption();
  static const int kCipherOptionFieldNumber = 4;
  inline const ::hadoop::hdfs::CipherOptionProto& cipheroption(int index) const;
  inline ::hadoop::hdfs::CipherOptionProto* mutable_cipheroption(int index);
  inline ::hadoop::hdfs::CipherOptionProto* add_cipheroption();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::CipherOptionProto >&
      cipheroption() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::CipherOptionProto >*
      mutable_cipheroption();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DataTransferEncryptorMessageProto)
 private:
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_payload();
  inline void clear_has_payload();
  inline void set_has_message();
  inline void clear_has_message();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* payload_;
  ::std::string* message_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::CipherOptionProto > cipheroption_;
  int status_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static DataTransferEncryptorMessageProto* default_instance_;
};
// -------------------------------------------------------------------

class BaseHeaderProto : public ::google::protobuf::Message {
 public:
  BaseHeaderProto();
  virtual ~BaseHeaderProto();

  BaseHeaderProto(const BaseHeaderProto& from);

  inline BaseHeaderProto& operator=(const BaseHeaderProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BaseHeaderProto& default_instance();

  void Swap(BaseHeaderProto* other);

  // implements Message ----------------------------------------------

  BaseHeaderProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BaseHeaderProto& from);
  void MergeFrom(const BaseHeaderProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ExtendedBlockProto block = 1;
  inline bool has_block() const;
  inline void clear_block();
  static const int kBlockFieldNumber = 1;
  inline const ::hadoop::hdfs::ExtendedBlockProto& block() const;
  inline ::hadoop::hdfs::ExtendedBlockProto* mutable_block();
  inline ::hadoop::hdfs::ExtendedBlockProto* release_block();
  inline void set_allocated_block(::hadoop::hdfs::ExtendedBlockProto* block);

  // optional .hadoop.common.TokenProto token = 2;
  inline bool has_token() const;
  inline void clear_token();
  static const int kTokenFieldNumber = 2;
  inline const ::hadoop::common::TokenProto& token() const;
  inline ::hadoop::common::TokenProto* mutable_token();
  inline ::hadoop::common::TokenProto* release_token();
  inline void set_allocated_token(::hadoop::common::TokenProto* token);

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 3;
  inline bool has_traceinfo() const;
  inline void clear_traceinfo();
  static const int kTraceInfoFieldNumber = 3;
  inline const ::hadoop::hdfs::DataTransferTraceInfoProto& traceinfo() const;
  inline ::hadoop::hdfs::DataTransferTraceInfoProto* mutable_traceinfo();
  inline ::hadoop::hdfs::DataTransferTraceInfoProto* release_traceinfo();
  inline void set_allocated_traceinfo(::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BaseHeaderProto)
 private:
  inline void set_has_block();
  inline void clear_has_block();
  inline void set_has_token();
  inline void clear_has_token();
  inline void set_has_traceinfo();
  inline void clear_has_traceinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ExtendedBlockProto* block_;
  ::hadoop::common::TokenProto* token_;
  ::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static BaseHeaderProto* default_instance_;
};
// -------------------------------------------------------------------

class DataTransferTraceInfoProto : public ::google::protobuf::Message {
 public:
  DataTransferTraceInfoProto();
  virtual ~DataTransferTraceInfoProto();

  DataTransferTraceInfoProto(const DataTransferTraceInfoProto& from);

  inline DataTransferTraceInfoProto& operator=(const DataTransferTraceInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DataTransferTraceInfoProto& default_instance();

  void Swap(DataTransferTraceInfoProto* other);

  // implements Message ----------------------------------------------

  DataTransferTraceInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DataTransferTraceInfoProto& from);
  void MergeFrom(const DataTransferTraceInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 traceId = 1;
  inline bool has_traceid() const;
  inline void clear_traceid();
  static const int kTraceIdFieldNumber = 1;
  inline ::google::protobuf::uint64 traceid() const;
  inline void set_traceid(::google::protobuf::uint64 value);

  // required uint64 parentId = 2;
  inline bool has_parentid() const;
  inline void clear_parentid();
  static const int kParentIdFieldNumber = 2;
  inline ::google::protobuf::uint64 parentid() const;
  inline void set_parentid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DataTransferTraceInfoProto)
 private:
  inline void set_has_traceid();
  inline void clear_has_traceid();
  inline void set_has_parentid();
  inline void clear_has_parentid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 traceid_;
  ::google::protobuf::uint64 parentid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static DataTransferTraceInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class ClientOperationHeaderProto : public ::google::protobuf::Message {
 public:
  ClientOperationHeaderProto();
  virtual ~ClientOperationHeaderProto();

  ClientOperationHeaderProto(const ClientOperationHeaderProto& from);

  inline ClientOperationHeaderProto& operator=(const ClientOperationHeaderProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientOperationHeaderProto& default_instance();

  void Swap(ClientOperationHeaderProto* other);

  // implements Message ----------------------------------------------

  ClientOperationHeaderProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ClientOperationHeaderProto& from);
  void MergeFrom(const ClientOperationHeaderProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BaseHeaderProto baseHeader = 1;
  inline bool has_baseheader() const;
  inline void clear_baseheader();
  static const int kBaseHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::BaseHeaderProto& baseheader() const;
  inline ::hadoop::hdfs::BaseHeaderProto* mutable_baseheader();
  inline ::hadoop::hdfs::BaseHeaderProto* release_baseheader();
  inline void set_allocated_baseheader(::hadoop::hdfs::BaseHeaderProto* baseheader);

  // required string clientName = 2;
  inline bool has_clientname() const;
  inline void clear_clientname();
  static const int kClientNameFieldNumber = 2;
  inline const ::std::string& clientname() const;
  inline void set_clientname(const ::std::string& value);
  inline void set_clientname(const char* value);
  inline void set_clientname(const char* value, size_t size);
  inline ::std::string* mutable_clientname();
  inline ::std::string* release_clientname();
  inline void set_allocated_clientname(::std::string* clientname);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ClientOperationHeaderProto)
 private:
  inline void set_has_baseheader();
  inline void clear_has_baseheader();
  inline void set_has_clientname();
  inline void clear_has_clientname();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BaseHeaderProto* baseheader_;
  ::std::string* clientname_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ClientOperationHeaderProto* default_instance_;
};
// -------------------------------------------------------------------

class CachingStrategyProto : public ::google::protobuf::Message {
 public:
  CachingStrategyProto();
  virtual ~CachingStrategyProto();

  CachingStrategyProto(const CachingStrategyProto& from);

  inline CachingStrategyProto& operator=(const CachingStrategyProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CachingStrategyProto& default_instance();

  void Swap(CachingStrategyProto* other);

  // implements Message ----------------------------------------------

  CachingStrategyProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CachingStrategyProto& from);
  void MergeFrom(const CachingStrategyProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool dropBehind = 1;
  inline bool has_dropbehind() const;
  inline void clear_dropbehind();
  static const int kDropBehindFieldNumber = 1;
  inline bool dropbehind() const;
  inline void set_dropbehind(bool value);

  // optional int64 readahead = 2;
  inline bool has_readahead() const;
  inline void clear_readahead();
  static const int kReadaheadFieldNumber = 2;
  inline ::google::protobuf::int64 readahead() const;
  inline void set_readahead(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.CachingStrategyProto)
 private:
  inline void set_has_dropbehind();
  inline void clear_has_dropbehind();
  inline void set_has_readahead();
  inline void clear_has_readahead();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 readahead_;
  bool dropbehind_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static CachingStrategyProto* default_instance_;
};
// -------------------------------------------------------------------

class OpReadBlockProto : public ::google::protobuf::Message {
 public:
  OpReadBlockProto();
  virtual ~OpReadBlockProto();

  OpReadBlockProto(const OpReadBlockProto& from);

  inline OpReadBlockProto& operator=(const OpReadBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpReadBlockProto& default_instance();

  void Swap(OpReadBlockProto* other);

  // implements Message ----------------------------------------------

  OpReadBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpReadBlockProto& from);
  void MergeFrom(const OpReadBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::ClientOperationHeaderProto& header() const;
  inline ::hadoop::hdfs::ClientOperationHeaderProto* mutable_header();
  inline ::hadoop::hdfs::ClientOperationHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::ClientOperationHeaderProto* header);

  // required uint64 offset = 2;
  inline bool has_offset() const;
  inline void clear_offset();
  static const int kOffsetFieldNumber = 2;
  inline ::google::protobuf::uint64 offset() const;
  inline void set_offset(::google::protobuf::uint64 value);

  // required uint64 len = 3;
  inline bool has_len() const;
  inline void clear_len();
  static const int kLenFieldNumber = 3;
  inline ::google::protobuf::uint64 len() const;
  inline void set_len(::google::protobuf::uint64 value);

  // optional bool sendChecksums = 4 [default = true];
  inline bool has_sendchecksums() const;
  inline void clear_sendchecksums();
  static const int kSendChecksumsFieldNumber = 4;
  inline bool sendchecksums() const;
  inline void set_sendchecksums(bool value);

  // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 5;
  inline bool has_cachingstrategy() const;
  inline void clear_cachingstrategy();
  static const int kCachingStrategyFieldNumber = 5;
  inline const ::hadoop::hdfs::CachingStrategyProto& cachingstrategy() const;
  inline ::hadoop::hdfs::CachingStrategyProto* mutable_cachingstrategy();
  inline ::hadoop::hdfs::CachingStrategyProto* release_cachingstrategy();
  inline void set_allocated_cachingstrategy(::hadoop::hdfs::CachingStrategyProto* cachingstrategy);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpReadBlockProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();
  inline void set_has_offset();
  inline void clear_has_offset();
  inline void set_has_len();
  inline void clear_has_len();
  inline void set_has_sendchecksums();
  inline void clear_has_sendchecksums();
  inline void set_has_cachingstrategy();
  inline void clear_has_cachingstrategy();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ClientOperationHeaderProto* header_;
  ::google::protobuf::uint64 offset_;
  ::google::protobuf::uint64 len_;
  ::hadoop::hdfs::CachingStrategyProto* cachingstrategy_;
  bool sendchecksums_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpReadBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class ChecksumProto : public ::google::protobuf::Message {
 public:
  ChecksumProto();
  virtual ~ChecksumProto();

  ChecksumProto(const ChecksumProto& from);

  inline ChecksumProto& operator=(const ChecksumProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ChecksumProto& default_instance();

  void Swap(ChecksumProto* other);

  // implements Message ----------------------------------------------

  ChecksumProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ChecksumProto& from);
  void MergeFrom(const ChecksumProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ChecksumTypeProto type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::hadoop::hdfs::ChecksumTypeProto type() const;
  inline void set_type(::hadoop::hdfs::ChecksumTypeProto value);

  // required uint32 bytesPerChecksum = 2;
  inline bool has_bytesperchecksum() const;
  inline void clear_bytesperchecksum();
  static const int kBytesPerChecksumFieldNumber = 2;
  inline ::google::protobuf::uint32 bytesperchecksum() const;
  inline void set_bytesperchecksum(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ChecksumProto)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_bytesperchecksum();
  inline void clear_has_bytesperchecksum();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int type_;
  ::google::protobuf::uint32 bytesperchecksum_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ChecksumProto* default_instance_;
};
// -------------------------------------------------------------------

class OpWriteBlockProto : public ::google::protobuf::Message {
 public:
  OpWriteBlockProto();
  virtual ~OpWriteBlockProto();

  OpWriteBlockProto(const OpWriteBlockProto& from);

  inline OpWriteBlockProto& operator=(const OpWriteBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpWriteBlockProto& default_instance();

  void Swap(OpWriteBlockProto* other);

  // implements Message ----------------------------------------------

  OpWriteBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpWriteBlockProto& from);
  void MergeFrom(const OpWriteBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef OpWriteBlockProto_BlockConstructionStage BlockConstructionStage;
  static const BlockConstructionStage PIPELINE_SETUP_APPEND = OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_APPEND;
  static const BlockConstructionStage PIPELINE_SETUP_APPEND_RECOVERY = OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_APPEND_RECOVERY;
  static const BlockConstructionStage DATA_STREAMING = OpWriteBlockProto_BlockConstructionStage_DATA_STREAMING;
  static const BlockConstructionStage PIPELINE_SETUP_STREAMING_RECOVERY = OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_STREAMING_RECOVERY;
  static const BlockConstructionStage PIPELINE_CLOSE = OpWriteBlockProto_BlockConstructionStage_PIPELINE_CLOSE;
  static const BlockConstructionStage PIPELINE_CLOSE_RECOVERY = OpWriteBlockProto_BlockConstructionStage_PIPELINE_CLOSE_RECOVERY;
  static const BlockConstructionStage PIPELINE_SETUP_CREATE = OpWriteBlockProto_BlockConstructionStage_PIPELINE_SETUP_CREATE;
  static const BlockConstructionStage TRANSFER_RBW = OpWriteBlockProto_BlockConstructionStage_TRANSFER_RBW;
  static const BlockConstructionStage TRANSFER_FINALIZED = OpWriteBlockProto_BlockConstructionStage_TRANSFER_FINALIZED;
  static inline bool BlockConstructionStage_IsValid(int value) {
    return OpWriteBlockProto_BlockConstructionStage_IsValid(value);
  }
  static const BlockConstructionStage BlockConstructionStage_MIN =
    OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_MIN;
  static const BlockConstructionStage BlockConstructionStage_MAX =
    OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_MAX;
  static const int BlockConstructionStage_ARRAYSIZE =
    OpWriteBlockProto_BlockConstructionStage_BlockConstructionStage_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  BlockConstructionStage_descriptor() {
    return OpWriteBlockProto_BlockConstructionStage_descriptor();
  }
  static inline const ::std::string& BlockConstructionStage_Name(BlockConstructionStage value) {
    return OpWriteBlockProto_BlockConstructionStage_Name(value);
  }
  static inline bool BlockConstructionStage_Parse(const ::std::string& name,
      BlockConstructionStage* value) {
    return OpWriteBlockProto_BlockConstructionStage_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::ClientOperationHeaderProto& header() const;
  inline ::hadoop::hdfs::ClientOperationHeaderProto* mutable_header();
  inline ::hadoop::hdfs::ClientOperationHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::ClientOperationHeaderProto* header);

  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  inline int targets_size() const;
  inline void clear_targets();
  static const int kTargetsFieldNumber = 2;
  inline const ::hadoop::hdfs::DatanodeInfoProto& targets(int index) const;
  inline ::hadoop::hdfs::DatanodeInfoProto* mutable_targets(int index);
  inline ::hadoop::hdfs::DatanodeInfoProto* add_targets();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
      targets() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
      mutable_targets();

  // optional .hadoop.hdfs.DatanodeInfoProto source = 3;
  inline bool has_source() const;
  inline void clear_source();
  static const int kSourceFieldNumber = 3;
  inline const ::hadoop::hdfs::DatanodeInfoProto& source() const;
  inline ::hadoop::hdfs::DatanodeInfoProto* mutable_source();
  inline ::hadoop::hdfs::DatanodeInfoProto* release_source();
  inline void set_allocated_source(::hadoop::hdfs::DatanodeInfoProto* source);

  // required .hadoop.hdfs.OpWriteBlockProto.BlockConstructionStage stage = 4;
  inline bool has_stage() const;
  inline void clear_stage();
  static const int kStageFieldNumber = 4;
  inline ::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage stage() const;
  inline void set_stage(::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage value);

  // required uint32 pipelineSize = 5;
  inline bool has_pipelinesize() const;
  inline void clear_pipelinesize();
  static const int kPipelineSizeFieldNumber = 5;
  inline ::google::protobuf::uint32 pipelinesize() const;
  inline void set_pipelinesize(::google::protobuf::uint32 value);

  // required uint64 minBytesRcvd = 6;
  inline bool has_minbytesrcvd() const;
  inline void clear_minbytesrcvd();
  static const int kMinBytesRcvdFieldNumber = 6;
  inline ::google::protobuf::uint64 minbytesrcvd() const;
  inline void set_minbytesrcvd(::google::protobuf::uint64 value);

  // required uint64 maxBytesRcvd = 7;
  inline bool has_maxbytesrcvd() const;
  inline void clear_maxbytesrcvd();
  static const int kMaxBytesRcvdFieldNumber = 7;
  inline ::google::protobuf::uint64 maxbytesrcvd() const;
  inline void set_maxbytesrcvd(::google::protobuf::uint64 value);

  // required uint64 latestGenerationStamp = 8;
  inline bool has_latestgenerationstamp() const;
  inline void clear_latestgenerationstamp();
  static const int kLatestGenerationStampFieldNumber = 8;
  inline ::google::protobuf::uint64 latestgenerationstamp() const;
  inline void set_latestgenerationstamp(::google::protobuf::uint64 value);

  // required .hadoop.hdfs.ChecksumProto requestedChecksum = 9;
  inline bool has_requestedchecksum() const;
  inline void clear_requestedchecksum();
  static const int kRequestedChecksumFieldNumber = 9;
  inline const ::hadoop::hdfs::ChecksumProto& requestedchecksum() const;
  inline ::hadoop::hdfs::ChecksumProto* mutable_requestedchecksum();
  inline ::hadoop::hdfs::ChecksumProto* release_requestedchecksum();
  inline void set_allocated_requestedchecksum(::hadoop::hdfs::ChecksumProto* requestedchecksum);

  // optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 10;
  inline bool has_cachingstrategy() const;
  inline void clear_cachingstrategy();
  static const int kCachingStrategyFieldNumber = 10;
  inline const ::hadoop::hdfs::CachingStrategyProto& cachingstrategy() const;
  inline ::hadoop::hdfs::CachingStrategyProto* mutable_cachingstrategy();
  inline ::hadoop::hdfs::CachingStrategyProto* release_cachingstrategy();
  inline void set_allocated_cachingstrategy(::hadoop::hdfs::CachingStrategyProto* cachingstrategy);

  // optional .hadoop.hdfs.StorageTypeProto storageType = 11 [default = DISK];
  inline bool has_storagetype() const;
  inline void clear_storagetype();
  static const int kStorageTypeFieldNumber = 11;
  inline ::hadoop::hdfs::StorageTypeProto storagetype() const;
  inline void set_storagetype(::hadoop::hdfs::StorageTypeProto value);

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 12;
  inline int targetstoragetypes_size() const;
  inline void clear_targetstoragetypes();
  static const int kTargetStorageTypesFieldNumber = 12;
  inline ::hadoop::hdfs::StorageTypeProto targetstoragetypes(int index) const;
  inline void set_targetstoragetypes(int index, ::hadoop::hdfs::StorageTypeProto value);
  inline void add_targetstoragetypes(::hadoop::hdfs::StorageTypeProto value);
  inline const ::google::protobuf::RepeatedField<int>& targetstoragetypes() const;
  inline ::google::protobuf::RepeatedField<int>* mutable_targetstoragetypes();

  // optional bool allowLazyPersist = 13 [default = false];
  inline bool has_allowlazypersist() const;
  inline void clear_allowlazypersist();
  static const int kAllowLazyPersistFieldNumber = 13;
  inline bool allowlazypersist() const;
  inline void set_allowlazypersist(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpWriteBlockProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();
  inline void set_has_source();
  inline void clear_has_source();
  inline void set_has_stage();
  inline void clear_has_stage();
  inline void set_has_pipelinesize();
  inline void clear_has_pipelinesize();
  inline void set_has_minbytesrcvd();
  inline void clear_has_minbytesrcvd();
  inline void set_has_maxbytesrcvd();
  inline void clear_has_maxbytesrcvd();
  inline void set_has_latestgenerationstamp();
  inline void clear_has_latestgenerationstamp();
  inline void set_has_requestedchecksum();
  inline void clear_has_requestedchecksum();
  inline void set_has_cachingstrategy();
  inline void clear_has_cachingstrategy();
  inline void set_has_storagetype();
  inline void clear_has_storagetype();
  inline void set_has_allowlazypersist();
  inline void clear_has_allowlazypersist();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ClientOperationHeaderProto* header_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto > targets_;
  ::hadoop::hdfs::DatanodeInfoProto* source_;
  int stage_;
  ::google::protobuf::uint32 pipelinesize_;
  ::google::protobuf::uint64 minbytesrcvd_;
  ::google::protobuf::uint64 maxbytesrcvd_;
  ::google::protobuf::uint64 latestgenerationstamp_;
  ::hadoop::hdfs::ChecksumProto* requestedchecksum_;
  ::hadoop::hdfs::CachingStrategyProto* cachingstrategy_;
  ::google::protobuf::RepeatedField<int> targetstoragetypes_;
  int storagetype_;
  bool allowlazypersist_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(13 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpWriteBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class OpTransferBlockProto : public ::google::protobuf::Message {
 public:
  OpTransferBlockProto();
  virtual ~OpTransferBlockProto();

  OpTransferBlockProto(const OpTransferBlockProto& from);

  inline OpTransferBlockProto& operator=(const OpTransferBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpTransferBlockProto& default_instance();

  void Swap(OpTransferBlockProto* other);

  // implements Message ----------------------------------------------

  OpTransferBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpTransferBlockProto& from);
  void MergeFrom(const OpTransferBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::ClientOperationHeaderProto& header() const;
  inline ::hadoop::hdfs::ClientOperationHeaderProto* mutable_header();
  inline ::hadoop::hdfs::ClientOperationHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::ClientOperationHeaderProto* header);

  // repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
  inline int targets_size() const;
  inline void clear_targets();
  static const int kTargetsFieldNumber = 2;
  inline const ::hadoop::hdfs::DatanodeInfoProto& targets(int index) const;
  inline ::hadoop::hdfs::DatanodeInfoProto* mutable_targets(int index);
  inline ::hadoop::hdfs::DatanodeInfoProto* add_targets();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
      targets() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
      mutable_targets();

  // repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 3;
  inline int targetstoragetypes_size() const;
  inline void clear_targetstoragetypes();
  static const int kTargetStorageTypesFieldNumber = 3;
  inline ::hadoop::hdfs::StorageTypeProto targetstoragetypes(int index) const;
  inline void set_targetstoragetypes(int index, ::hadoop::hdfs::StorageTypeProto value);
  inline void add_targetstoragetypes(::hadoop::hdfs::StorageTypeProto value);
  inline const ::google::protobuf::RepeatedField<int>& targetstoragetypes() const;
  inline ::google::protobuf::RepeatedField<int>* mutable_targetstoragetypes();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpTransferBlockProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ClientOperationHeaderProto* header_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto > targets_;
  ::google::protobuf::RepeatedField<int> targetstoragetypes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpTransferBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class OpReplaceBlockProto : public ::google::protobuf::Message {
 public:
  OpReplaceBlockProto();
  virtual ~OpReplaceBlockProto();

  OpReplaceBlockProto(const OpReplaceBlockProto& from);

  inline OpReplaceBlockProto& operator=(const OpReplaceBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpReplaceBlockProto& default_instance();

  void Swap(OpReplaceBlockProto* other);

  // implements Message ----------------------------------------------

  OpReplaceBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpReplaceBlockProto& from);
  void MergeFrom(const OpReplaceBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::BaseHeaderProto& header() const;
  inline ::hadoop::hdfs::BaseHeaderProto* mutable_header();
  inline ::hadoop::hdfs::BaseHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header);

  // required string delHint = 2;
  inline bool has_delhint() const;
  inline void clear_delhint();
  static const int kDelHintFieldNumber = 2;
  inline const ::std::string& delhint() const;
  inline void set_delhint(const ::std::string& value);
  inline void set_delhint(const char* value);
  inline void set_delhint(const char* value, size_t size);
  inline ::std::string* mutable_delhint();
  inline ::std::string* release_delhint();
  inline void set_allocated_delhint(::std::string* delhint);

  // required .hadoop.hdfs.DatanodeInfoProto source = 3;
  inline bool has_source() const;
  inline void clear_source();
  static const int kSourceFieldNumber = 3;
  inline const ::hadoop::hdfs::DatanodeInfoProto& source() const;
  inline ::hadoop::hdfs::DatanodeInfoProto* mutable_source();
  inline ::hadoop::hdfs::DatanodeInfoProto* release_source();
  inline void set_allocated_source(::hadoop::hdfs::DatanodeInfoProto* source);

  // optional .hadoop.hdfs.StorageTypeProto storageType = 4 [default = DISK];
  inline bool has_storagetype() const;
  inline void clear_storagetype();
  static const int kStorageTypeFieldNumber = 4;
  inline ::hadoop::hdfs::StorageTypeProto storagetype() const;
  inline void set_storagetype(::hadoop::hdfs::StorageTypeProto value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpReplaceBlockProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();
  inline void set_has_delhint();
  inline void clear_has_delhint();
  inline void set_has_source();
  inline void clear_has_source();
  inline void set_has_storagetype();
  inline void clear_has_storagetype();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BaseHeaderProto* header_;
  ::std::string* delhint_;
  ::hadoop::hdfs::DatanodeInfoProto* source_;
  int storagetype_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpReplaceBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class OpCopyBlockProto : public ::google::protobuf::Message {
 public:
  OpCopyBlockProto();
  virtual ~OpCopyBlockProto();

  OpCopyBlockProto(const OpCopyBlockProto& from);

  inline OpCopyBlockProto& operator=(const OpCopyBlockProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpCopyBlockProto& default_instance();

  void Swap(OpCopyBlockProto* other);

  // implements Message ----------------------------------------------

  OpCopyBlockProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpCopyBlockProto& from);
  void MergeFrom(const OpCopyBlockProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::BaseHeaderProto& header() const;
  inline ::hadoop::hdfs::BaseHeaderProto* mutable_header();
  inline ::hadoop::hdfs::BaseHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpCopyBlockProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BaseHeaderProto* header_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpCopyBlockProto* default_instance_;
};
// -------------------------------------------------------------------

class OpBlockChecksumProto : public ::google::protobuf::Message {
 public:
  OpBlockChecksumProto();
  virtual ~OpBlockChecksumProto();

  OpBlockChecksumProto(const OpBlockChecksumProto& from);

  inline OpBlockChecksumProto& operator=(const OpBlockChecksumProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpBlockChecksumProto& default_instance();

  void Swap(OpBlockChecksumProto* other);

  // implements Message ----------------------------------------------

  OpBlockChecksumProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpBlockChecksumProto& from);
  void MergeFrom(const OpBlockChecksumProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::BaseHeaderProto& header() const;
  inline ::hadoop::hdfs::BaseHeaderProto* mutable_header();
  inline ::hadoop::hdfs::BaseHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpBlockChecksumProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BaseHeaderProto* header_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpBlockChecksumProto* default_instance_;
};
// -------------------------------------------------------------------

class ShortCircuitShmIdProto : public ::google::protobuf::Message {
 public:
  ShortCircuitShmIdProto();
  virtual ~ShortCircuitShmIdProto();

  ShortCircuitShmIdProto(const ShortCircuitShmIdProto& from);

  inline ShortCircuitShmIdProto& operator=(const ShortCircuitShmIdProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ShortCircuitShmIdProto& default_instance();

  void Swap(ShortCircuitShmIdProto* other);

  // implements Message ----------------------------------------------

  ShortCircuitShmIdProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ShortCircuitShmIdProto& from);
  void MergeFrom(const ShortCircuitShmIdProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 hi = 1;
  inline bool has_hi() const;
  inline void clear_hi();
  static const int kHiFieldNumber = 1;
  inline ::google::protobuf::int64 hi() const;
  inline void set_hi(::google::protobuf::int64 value);

  // required int64 lo = 2;
  inline bool has_lo() const;
  inline void clear_lo();
  static const int kLoFieldNumber = 2;
  inline ::google::protobuf::int64 lo() const;
  inline void set_lo(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ShortCircuitShmIdProto)
 private:
  inline void set_has_hi();
  inline void clear_has_hi();
  inline void set_has_lo();
  inline void clear_has_lo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 hi_;
  ::google::protobuf::int64 lo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ShortCircuitShmIdProto* default_instance_;
};
// -------------------------------------------------------------------

class ShortCircuitShmSlotProto : public ::google::protobuf::Message {
 public:
  ShortCircuitShmSlotProto();
  virtual ~ShortCircuitShmSlotProto();

  ShortCircuitShmSlotProto(const ShortCircuitShmSlotProto& from);

  inline ShortCircuitShmSlotProto& operator=(const ShortCircuitShmSlotProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ShortCircuitShmSlotProto& default_instance();

  void Swap(ShortCircuitShmSlotProto* other);

  // implements Message ----------------------------------------------

  ShortCircuitShmSlotProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ShortCircuitShmSlotProto& from);
  void MergeFrom(const ShortCircuitShmSlotProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ShortCircuitShmIdProto shmId = 1;
  inline bool has_shmid() const;
  inline void clear_shmid();
  static const int kShmIdFieldNumber = 1;
  inline const ::hadoop::hdfs::ShortCircuitShmIdProto& shmid() const;
  inline ::hadoop::hdfs::ShortCircuitShmIdProto* mutable_shmid();
  inline ::hadoop::hdfs::ShortCircuitShmIdProto* release_shmid();
  inline void set_allocated_shmid(::hadoop::hdfs::ShortCircuitShmIdProto* shmid);

  // required int32 slotIdx = 2;
  inline bool has_slotidx() const;
  inline void clear_slotidx();
  static const int kSlotIdxFieldNumber = 2;
  inline ::google::protobuf::int32 slotidx() const;
  inline void set_slotidx(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ShortCircuitShmSlotProto)
 private:
  inline void set_has_shmid();
  inline void clear_has_shmid();
  inline void set_has_slotidx();
  inline void clear_has_slotidx();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ShortCircuitShmIdProto* shmid_;
  ::google::protobuf::int32 slotidx_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ShortCircuitShmSlotProto* default_instance_;
};
// -------------------------------------------------------------------

class OpRequestShortCircuitAccessProto : public ::google::protobuf::Message {
 public:
  OpRequestShortCircuitAccessProto();
  virtual ~OpRequestShortCircuitAccessProto();

  OpRequestShortCircuitAccessProto(const OpRequestShortCircuitAccessProto& from);

  inline OpRequestShortCircuitAccessProto& operator=(const OpRequestShortCircuitAccessProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpRequestShortCircuitAccessProto& default_instance();

  void Swap(OpRequestShortCircuitAccessProto* other);

  // implements Message ----------------------------------------------

  OpRequestShortCircuitAccessProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpRequestShortCircuitAccessProto& from);
  void MergeFrom(const OpRequestShortCircuitAccessProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.BaseHeaderProto header = 1;
  inline bool has_header() const;
  inline void clear_header();
  static const int kHeaderFieldNumber = 1;
  inline const ::hadoop::hdfs::BaseHeaderProto& header() const;
  inline ::hadoop::hdfs::BaseHeaderProto* mutable_header();
  inline ::hadoop::hdfs::BaseHeaderProto* release_header();
  inline void set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header);

  // required uint32 maxVersion = 2;
  inline bool has_maxversion() const;
  inline void clear_maxversion();
  static const int kMaxVersionFieldNumber = 2;
  inline ::google::protobuf::uint32 maxversion() const;
  inline void set_maxversion(::google::protobuf::uint32 value);

  // optional .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 3;
  inline bool has_slotid() const;
  inline void clear_slotid();
  static const int kSlotIdFieldNumber = 3;
  inline const ::hadoop::hdfs::ShortCircuitShmSlotProto& slotid() const;
  inline ::hadoop::hdfs::ShortCircuitShmSlotProto* mutable_slotid();
  inline ::hadoop::hdfs::ShortCircuitShmSlotProto* release_slotid();
  inline void set_allocated_slotid(::hadoop::hdfs::ShortCircuitShmSlotProto* slotid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpRequestShortCircuitAccessProto)
 private:
  inline void set_has_header();
  inline void clear_has_header();
  inline void set_has_maxversion();
  inline void clear_has_maxversion();
  inline void set_has_slotid();
  inline void clear_has_slotid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::BaseHeaderProto* header_;
  ::hadoop::hdfs::ShortCircuitShmSlotProto* slotid_;
  ::google::protobuf::uint32 maxversion_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpRequestShortCircuitAccessProto* default_instance_;
};
// -------------------------------------------------------------------

class ReleaseShortCircuitAccessRequestProto : public ::google::protobuf::Message {
 public:
  ReleaseShortCircuitAccessRequestProto();
  virtual ~ReleaseShortCircuitAccessRequestProto();

  ReleaseShortCircuitAccessRequestProto(const ReleaseShortCircuitAccessRequestProto& from);

  inline ReleaseShortCircuitAccessRequestProto& operator=(const ReleaseShortCircuitAccessRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReleaseShortCircuitAccessRequestProto& default_instance();

  void Swap(ReleaseShortCircuitAccessRequestProto* other);

  // implements Message ----------------------------------------------

  ReleaseShortCircuitAccessRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ReleaseShortCircuitAccessRequestProto& from);
  void MergeFrom(const ReleaseShortCircuitAccessRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 1;
  inline bool has_slotid() const;
  inline void clear_slotid();
  static const int kSlotIdFieldNumber = 1;
  inline const ::hadoop::hdfs::ShortCircuitShmSlotProto& slotid() const;
  inline ::hadoop::hdfs::ShortCircuitShmSlotProto* mutable_slotid();
  inline ::hadoop::hdfs::ShortCircuitShmSlotProto* release_slotid();
  inline void set_allocated_slotid(::hadoop::hdfs::ShortCircuitShmSlotProto* slotid);

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
  inline bool has_traceinfo() const;
  inline void clear_traceinfo();
  static const int kTraceInfoFieldNumber = 2;
  inline const ::hadoop::hdfs::DataTransferTraceInfoProto& traceinfo() const;
  inline ::hadoop::hdfs::DataTransferTraceInfoProto* mutable_traceinfo();
  inline ::hadoop::hdfs::DataTransferTraceInfoProto* release_traceinfo();
  inline void set_allocated_traceinfo(::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ReleaseShortCircuitAccessRequestProto)
 private:
  inline void set_has_slotid();
  inline void clear_has_slotid();
  inline void set_has_traceinfo();
  inline void clear_has_traceinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ShortCircuitShmSlotProto* slotid_;
  ::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ReleaseShortCircuitAccessRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class ReleaseShortCircuitAccessResponseProto : public ::google::protobuf::Message {
 public:
  ReleaseShortCircuitAccessResponseProto();
  virtual ~ReleaseShortCircuitAccessResponseProto();

  ReleaseShortCircuitAccessResponseProto(const ReleaseShortCircuitAccessResponseProto& from);

  inline ReleaseShortCircuitAccessResponseProto& operator=(const ReleaseShortCircuitAccessResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReleaseShortCircuitAccessResponseProto& default_instance();

  void Swap(ReleaseShortCircuitAccessResponseProto* other);

  // implements Message ----------------------------------------------

  ReleaseShortCircuitAccessResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ReleaseShortCircuitAccessResponseProto& from);
  void MergeFrom(const ReleaseShortCircuitAccessResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.Status status = 1;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 1;
  inline ::hadoop::hdfs::Status status() const;
  inline void set_status(::hadoop::hdfs::Status value);

  // optional string error = 2;
  inline bool has_error() const;
  inline void clear_error();
  static const int kErrorFieldNumber = 2;
  inline const ::std::string& error() const;
  inline void set_error(const ::std::string& value);
  inline void set_error(const char* value);
  inline void set_error(const char* value, size_t size);
  inline ::std::string* mutable_error();
  inline ::std::string* release_error();
  inline void set_allocated_error(::std::string* error);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ReleaseShortCircuitAccessResponseProto)
 private:
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_error();
  inline void clear_has_error();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* error_;
  int status_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ReleaseShortCircuitAccessResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ShortCircuitShmRequestProto : public ::google::protobuf::Message {
 public:
  ShortCircuitShmRequestProto();
  virtual ~ShortCircuitShmRequestProto();

  ShortCircuitShmRequestProto(const ShortCircuitShmRequestProto& from);

  inline ShortCircuitShmRequestProto& operator=(const ShortCircuitShmRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ShortCircuitShmRequestProto& default_instance();

  void Swap(ShortCircuitShmRequestProto* other);

  // implements Message ----------------------------------------------

  ShortCircuitShmRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ShortCircuitShmRequestProto& from);
  void MergeFrom(const ShortCircuitShmRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string clientName = 1;
  inline bool has_clientname() const;
  inline void clear_clientname();
  static const int kClientNameFieldNumber = 1;
  inline const ::std::string& clientname() const;
  inline void set_clientname(const ::std::string& value);
  inline void set_clientname(const char* value);
  inline void set_clientname(const char* value, size_t size);
  inline ::std::string* mutable_clientname();
  inline ::std::string* release_clientname();
  inline void set_allocated_clientname(::std::string* clientname);

  // optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
  inline bool has_traceinfo() const;
  inline void clear_traceinfo();
  static const int kTraceInfoFieldNumber = 2;
  inline const ::hadoop::hdfs::DataTransferTraceInfoProto& traceinfo() const;
  inline ::hadoop::hdfs::DataTransferTraceInfoProto* mutable_traceinfo();
  inline ::hadoop::hdfs::DataTransferTraceInfoProto* release_traceinfo();
  inline void set_allocated_traceinfo(::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ShortCircuitShmRequestProto)
 private:
  inline void set_has_clientname();
  inline void clear_has_clientname();
  inline void set_has_traceinfo();
  inline void clear_has_traceinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* clientname_;
  ::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ShortCircuitShmRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class ShortCircuitShmResponseProto : public ::google::protobuf::Message {
 public:
  ShortCircuitShmResponseProto();
  virtual ~ShortCircuitShmResponseProto();

  ShortCircuitShmResponseProto(const ShortCircuitShmResponseProto& from);

  inline ShortCircuitShmResponseProto& operator=(const ShortCircuitShmResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ShortCircuitShmResponseProto& default_instance();

  void Swap(ShortCircuitShmResponseProto* other);

  // implements Message ----------------------------------------------

  ShortCircuitShmResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ShortCircuitShmResponseProto& from);
  void MergeFrom(const ShortCircuitShmResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.Status status = 1;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 1;
  inline ::hadoop::hdfs::Status status() const;
  inline void set_status(::hadoop::hdfs::Status value);

  // optional string error = 2;
  inline bool has_error() const;
  inline void clear_error();
  static const int kErrorFieldNumber = 2;
  inline const ::std::string& error() const;
  inline void set_error(const ::std::string& value);
  inline void set_error(const char* value);
  inline void set_error(const char* value, size_t size);
  inline ::std::string* mutable_error();
  inline ::std::string* release_error();
  inline void set_allocated_error(::std::string* error);

  // optional .hadoop.hdfs.ShortCircuitShmIdProto id = 3;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 3;
  inline const ::hadoop::hdfs::ShortCircuitShmIdProto& id() const;
  inline ::hadoop::hdfs::ShortCircuitShmIdProto* mutable_id();
  inline ::hadoop::hdfs::ShortCircuitShmIdProto* release_id();
  inline void set_allocated_id(::hadoop::hdfs::ShortCircuitShmIdProto* id);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ShortCircuitShmResponseProto)
 private:
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_error();
  inline void clear_has_error();
  inline void set_has_id();
  inline void clear_has_id();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* error_;
  ::hadoop::hdfs::ShortCircuitShmIdProto* id_;
  int status_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ShortCircuitShmResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class PacketHeaderProto : public ::google::protobuf::Message {
 public:
  PacketHeaderProto();
  virtual ~PacketHeaderProto();

  PacketHeaderProto(const PacketHeaderProto& from);

  inline PacketHeaderProto& operator=(const PacketHeaderProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PacketHeaderProto& default_instance();

  void Swap(PacketHeaderProto* other);

  // implements Message ----------------------------------------------

  PacketHeaderProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PacketHeaderProto& from);
  void MergeFrom(const PacketHeaderProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required sfixed64 offsetInBlock = 1;
  inline bool has_offsetinblock() const;
  inline void clear_offsetinblock();
  static const int kOffsetInBlockFieldNumber = 1;
  inline ::google::protobuf::int64 offsetinblock() const;
  inline void set_offsetinblock(::google::protobuf::int64 value);

  // required sfixed64 seqno = 2;
  inline bool has_seqno() const;
  inline void clear_seqno();
  static const int kSeqnoFieldNumber = 2;
  inline ::google::protobuf::int64 seqno() const;
  inline void set_seqno(::google::protobuf::int64 value);

  // required bool lastPacketInBlock = 3;
  inline bool has_lastpacketinblock() const;
  inline void clear_lastpacketinblock();
  static const int kLastPacketInBlockFieldNumber = 3;
  inline bool lastpacketinblock() const;
  inline void set_lastpacketinblock(bool value);

  // required sfixed32 dataLen = 4;
  inline bool has_datalen() const;
  inline void clear_datalen();
  static const int kDataLenFieldNumber = 4;
  inline ::google::protobuf::int32 datalen() const;
  inline void set_datalen(::google::protobuf::int32 value);

  // optional bool syncBlock = 5 [default = false];
  inline bool has_syncblock() const;
  inline void clear_syncblock();
  static const int kSyncBlockFieldNumber = 5;
  inline bool syncblock() const;
  inline void set_syncblock(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.PacketHeaderProto)
 private:
  inline void set_has_offsetinblock();
  inline void clear_has_offsetinblock();
  inline void set_has_seqno();
  inline void clear_has_seqno();
  inline void set_has_lastpacketinblock();
  inline void clear_has_lastpacketinblock();
  inline void set_has_datalen();
  inline void clear_has_datalen();
  inline void set_has_syncblock();
  inline void clear_has_syncblock();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 offsetinblock_;
  ::google::protobuf::int64 seqno_;
  ::google::protobuf::int32 datalen_;
  bool lastpacketinblock_;
  bool syncblock_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static PacketHeaderProto* default_instance_;
};
// -------------------------------------------------------------------

class PipelineAckProto : public ::google::protobuf::Message {
 public:
  PipelineAckProto();
  virtual ~PipelineAckProto();

  PipelineAckProto(const PipelineAckProto& from);

  inline PipelineAckProto& operator=(const PipelineAckProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PipelineAckProto& default_instance();

  void Swap(PipelineAckProto* other);

  // implements Message ----------------------------------------------

  PipelineAckProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PipelineAckProto& from);
  void MergeFrom(const PipelineAckProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required sint64 seqno = 1;
  inline bool has_seqno() const;
  inline void clear_seqno();
  static const int kSeqnoFieldNumber = 1;
  inline ::google::protobuf::int64 seqno() const;
  inline void set_seqno(::google::protobuf::int64 value);

  // repeated .hadoop.hdfs.Status status = 2;
  inline int status_size() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 2;
  inline ::hadoop::hdfs::Status status(int index) const;
  inline void set_status(int index, ::hadoop::hdfs::Status value);
  inline void add_status(::hadoop::hdfs::Status value);
  inline const ::google::protobuf::RepeatedField<int>& status() const;
  inline ::google::protobuf::RepeatedField<int>* mutable_status();

  // optional uint64 downstreamAckTimeNanos = 3 [default = 0];
  inline bool has_downstreamacktimenanos() const;
  inline void clear_downstreamacktimenanos();
  static const int kDownstreamAckTimeNanosFieldNumber = 3;
  inline ::google::protobuf::uint64 downstreamacktimenanos() const;
  inline void set_downstreamacktimenanos(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.PipelineAckProto)
 private:
  inline void set_has_seqno();
  inline void clear_has_seqno();
  inline void set_has_downstreamacktimenanos();
  inline void clear_has_downstreamacktimenanos();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 seqno_;
  ::google::protobuf::RepeatedField<int> status_;
  ::google::protobuf::uint64 downstreamacktimenanos_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static PipelineAckProto* default_instance_;
};
// -------------------------------------------------------------------

class ReadOpChecksumInfoProto : public ::google::protobuf::Message {
 public:
  ReadOpChecksumInfoProto();
  virtual ~ReadOpChecksumInfoProto();

  ReadOpChecksumInfoProto(const ReadOpChecksumInfoProto& from);

  inline ReadOpChecksumInfoProto& operator=(const ReadOpChecksumInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReadOpChecksumInfoProto& default_instance();

  void Swap(ReadOpChecksumInfoProto* other);

  // implements Message ----------------------------------------------

  ReadOpChecksumInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ReadOpChecksumInfoProto& from);
  void MergeFrom(const ReadOpChecksumInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.ChecksumProto checksum = 1;
  inline bool has_checksum() const;
  inline void clear_checksum();
  static const int kChecksumFieldNumber = 1;
  inline const ::hadoop::hdfs::ChecksumProto& checksum() const;
  inline ::hadoop::hdfs::ChecksumProto* mutable_checksum();
  inline ::hadoop::hdfs::ChecksumProto* release_checksum();
  inline void set_allocated_checksum(::hadoop::hdfs::ChecksumProto* checksum);

  // required uint64 chunkOffset = 2;
  inline bool has_chunkoffset() const;
  inline void clear_chunkoffset();
  static const int kChunkOffsetFieldNumber = 2;
  inline ::google::protobuf::uint64 chunkoffset() const;
  inline void set_chunkoffset(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ReadOpChecksumInfoProto)
 private:
  inline void set_has_checksum();
  inline void clear_has_checksum();
  inline void set_has_chunkoffset();
  inline void clear_has_chunkoffset();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::ChecksumProto* checksum_;
  ::google::protobuf::uint64 chunkoffset_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ReadOpChecksumInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class BlockOpResponseProto : public ::google::protobuf::Message {
 public:
  BlockOpResponseProto();
  virtual ~BlockOpResponseProto();

  BlockOpResponseProto(const BlockOpResponseProto& from);

  inline BlockOpResponseProto& operator=(const BlockOpResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BlockOpResponseProto& default_instance();

  void Swap(BlockOpResponseProto* other);

  // implements Message ----------------------------------------------

  BlockOpResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BlockOpResponseProto& from);
  void MergeFrom(const BlockOpResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.Status status = 1;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 1;
  inline ::hadoop::hdfs::Status status() const;
  inline void set_status(::hadoop::hdfs::Status value);

  // optional string firstBadLink = 2;
  inline bool has_firstbadlink() const;
  inline void clear_firstbadlink();
  static const int kFirstBadLinkFieldNumber = 2;
  inline const ::std::string& firstbadlink() const;
  inline void set_firstbadlink(const ::std::string& value);
  inline void set_firstbadlink(const char* value);
  inline void set_firstbadlink(const char* value, size_t size);
  inline ::std::string* mutable_firstbadlink();
  inline ::std::string* release_firstbadlink();
  inline void set_allocated_firstbadlink(::std::string* firstbadlink);

  // optional .hadoop.hdfs.OpBlockChecksumResponseProto checksumResponse = 3;
  inline bool has_checksumresponse() const;
  inline void clear_checksumresponse();
  static const int kChecksumResponseFieldNumber = 3;
  inline const ::hadoop::hdfs::OpBlockChecksumResponseProto& checksumresponse() const;
  inline ::hadoop::hdfs::OpBlockChecksumResponseProto* mutable_checksumresponse();
  inline ::hadoop::hdfs::OpBlockChecksumResponseProto* release_checksumresponse();
  inline void set_allocated_checksumresponse(::hadoop::hdfs::OpBlockChecksumResponseProto* checksumresponse);

  // optional .hadoop.hdfs.ReadOpChecksumInfoProto readOpChecksumInfo = 4;
  inline bool has_readopchecksuminfo() const;
  inline void clear_readopchecksuminfo();
  static const int kReadOpChecksumInfoFieldNumber = 4;
  inline const ::hadoop::hdfs::ReadOpChecksumInfoProto& readopchecksuminfo() const;
  inline ::hadoop::hdfs::ReadOpChecksumInfoProto* mutable_readopchecksuminfo();
  inline ::hadoop::hdfs::ReadOpChecksumInfoProto* release_readopchecksuminfo();
  inline void set_allocated_readopchecksuminfo(::hadoop::hdfs::ReadOpChecksumInfoProto* readopchecksuminfo);

  // optional string message = 5;
  inline bool has_message() const;
  inline void clear_message();
  static const int kMessageFieldNumber = 5;
  inline const ::std::string& message() const;
  inline void set_message(const ::std::string& value);
  inline void set_message(const char* value);
  inline void set_message(const char* value, size_t size);
  inline ::std::string* mutable_message();
  inline ::std::string* release_message();
  inline void set_allocated_message(::std::string* message);

  // optional uint32 shortCircuitAccessVersion = 6;
  inline bool has_shortcircuitaccessversion() const;
  inline void clear_shortcircuitaccessversion();
  static const int kShortCircuitAccessVersionFieldNumber = 6;
  inline ::google::protobuf::uint32 shortcircuitaccessversion() const;
  inline void set_shortcircuitaccessversion(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.BlockOpResponseProto)
 private:
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_firstbadlink();
  inline void clear_has_firstbadlink();
  inline void set_has_checksumresponse();
  inline void clear_has_checksumresponse();
  inline void set_has_readopchecksuminfo();
  inline void clear_has_readopchecksuminfo();
  inline void set_has_message();
  inline void clear_has_message();
  inline void set_has_shortcircuitaccessversion();
  inline void clear_has_shortcircuitaccessversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* firstbadlink_;
  ::hadoop::hdfs::OpBlockChecksumResponseProto* checksumresponse_;
  int status_;
  ::google::protobuf::uint32 shortcircuitaccessversion_;
  ::hadoop::hdfs::ReadOpChecksumInfoProto* readopchecksuminfo_;
  ::std::string* message_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static BlockOpResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class ClientReadStatusProto : public ::google::protobuf::Message {
 public:
  ClientReadStatusProto();
  virtual ~ClientReadStatusProto();

  ClientReadStatusProto(const ClientReadStatusProto& from);

  inline ClientReadStatusProto& operator=(const ClientReadStatusProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientReadStatusProto& default_instance();

  void Swap(ClientReadStatusProto* other);

  // implements Message ----------------------------------------------

  ClientReadStatusProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ClientReadStatusProto& from);
  void MergeFrom(const ClientReadStatusProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.Status status = 1;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 1;
  inline ::hadoop::hdfs::Status status() const;
  inline void set_status(::hadoop::hdfs::Status value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.ClientReadStatusProto)
 private:
  inline void set_has_status();
  inline void clear_has_status();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int status_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static ClientReadStatusProto* default_instance_;
};
// -------------------------------------------------------------------

class DNTransferAckProto : public ::google::protobuf::Message {
 public:
  DNTransferAckProto();
  virtual ~DNTransferAckProto();

  DNTransferAckProto(const DNTransferAckProto& from);

  inline DNTransferAckProto& operator=(const DNTransferAckProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DNTransferAckProto& default_instance();

  void Swap(DNTransferAckProto* other);

  // implements Message ----------------------------------------------

  DNTransferAckProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DNTransferAckProto& from);
  void MergeFrom(const DNTransferAckProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.Status status = 1;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 1;
  inline ::hadoop::hdfs::Status status() const;
  inline void set_status(::hadoop::hdfs::Status value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.DNTransferAckProto)
 private:
  inline void set_has_status();
  inline void clear_has_status();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  int status_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static DNTransferAckProto* default_instance_;
};
// -------------------------------------------------------------------

class OpBlockChecksumResponseProto : public ::google::protobuf::Message {
 public:
  OpBlockChecksumResponseProto();
  virtual ~OpBlockChecksumResponseProto();

  OpBlockChecksumResponseProto(const OpBlockChecksumResponseProto& from);

  inline OpBlockChecksumResponseProto& operator=(const OpBlockChecksumResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OpBlockChecksumResponseProto& default_instance();

  void Swap(OpBlockChecksumResponseProto* other);

  // implements Message ----------------------------------------------

  OpBlockChecksumResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OpBlockChecksumResponseProto& from);
  void MergeFrom(const OpBlockChecksumResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 bytesPerCrc = 1;
  inline bool has_bytespercrc() const;
  inline void clear_bytespercrc();
  static const int kBytesPerCrcFieldNumber = 1;
  inline ::google::protobuf::uint32 bytespercrc() const;
  inline void set_bytespercrc(::google::protobuf::uint32 value);

  // required uint64 crcPerBlock = 2;
  inline bool has_crcperblock() const;
  inline void clear_crcperblock();
  static const int kCrcPerBlockFieldNumber = 2;
  inline ::google::protobuf::uint64 crcperblock() const;
  inline void set_crcperblock(::google::protobuf::uint64 value);

  // required bytes md5 = 3;
  inline bool has_md5() const;
  inline void clear_md5();
  static const int kMd5FieldNumber = 3;
  inline const ::std::string& md5() const;
  inline void set_md5(const ::std::string& value);
  inline void set_md5(const char* value);
  inline void set_md5(const void* value, size_t size);
  inline ::std::string* mutable_md5();
  inline ::std::string* release_md5();
  inline void set_allocated_md5(::std::string* md5);

  // optional .hadoop.hdfs.ChecksumTypeProto crcType = 4;
  inline bool has_crctype() const;
  inline void clear_crctype();
  static const int kCrcTypeFieldNumber = 4;
  inline ::hadoop::hdfs::ChecksumTypeProto crctype() const;
  inline void set_crctype(::hadoop::hdfs::ChecksumTypeProto value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.OpBlockChecksumResponseProto)
 private:
  inline void set_has_bytespercrc();
  inline void clear_has_bytespercrc();
  inline void set_has_crcperblock();
  inline void clear_has_crcperblock();
  inline void set_has_md5();
  inline void clear_has_md5();
  inline void set_has_crctype();
  inline void clear_has_crctype();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 crcperblock_;
  ::google::protobuf::uint32 bytespercrc_;
  int crctype_;
  ::std::string* md5_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_datatransfer_2eproto();
  friend void protobuf_AssignDesc_datatransfer_2eproto();
  friend void protobuf_ShutdownFile_datatransfer_2eproto();

  void InitAsDefaultInstance();
  static OpBlockChecksumResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// DataTransferEncryptorMessageProto

// required .hadoop.hdfs.DataTransferEncryptorMessageProto.DataTransferEncryptorStatus status = 1;
inline bool DataTransferEncryptorMessageProto::has_status() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DataTransferEncryptorMessageProto::set_has_status() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DataTransferEncryptorMessageProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DataTransferEncryptorMessageProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus DataTransferEncryptorMessageProto::status() const {
  return static_cast< ::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus >(status_);
}
inline void DataTransferEncryptorMessageProto::set_status(::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus value) {
  assert(::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_IsValid(value));
  set_has_status();
  status_ = value;
}

// optional bytes payload = 2;
inline bool DataTransferEncryptorMessageProto::has_payload() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DataTransferEncryptorMessageProto::set_has_payload() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DataTransferEncryptorMessageProto::clear_has_payload() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DataTransferEncryptorMessageProto::clear_payload() {
  if (payload_ != &::google::protobuf::internal::kEmptyString) {
    payload_->clear();
  }
  clear_has_payload();
}
inline const ::std::string& DataTransferEncryptorMessageProto::payload() const {
  return *payload_;
}
inline void DataTransferEncryptorMessageProto::set_payload(const ::std::string& value) {
  set_has_payload();
  if (payload_ == &::google::protobuf::internal::kEmptyString) {
    payload_ = new ::std::string;
  }
  payload_->assign(value);
}
inline void DataTransferEncryptorMessageProto::set_payload(const char* value) {
  set_has_payload();
  if (payload_ == &::google::protobuf::internal::kEmptyString) {
    payload_ = new ::std::string;
  }
  payload_->assign(value);
}
inline void DataTransferEncryptorMessageProto::set_payload(const void* value, size_t size) {
  set_has_payload();
  if (payload_ == &::google::protobuf::internal::kEmptyString) {
    payload_ = new ::std::string;
  }
  payload_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DataTransferEncryptorMessageProto::mutable_payload() {
  set_has_payload();
  if (payload_ == &::google::protobuf::internal::kEmptyString) {
    payload_ = new ::std::string;
  }
  return payload_;
}
inline ::std::string* DataTransferEncryptorMessageProto::release_payload() {
  clear_has_payload();
  if (payload_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = payload_;
    payload_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DataTransferEncryptorMessageProto::set_allocated_payload(::std::string* payload) {
  if (payload_ != &::google::protobuf::internal::kEmptyString) {
    delete payload_;
  }
  if (payload) {
    set_has_payload();
    payload_ = payload;
  } else {
    clear_has_payload();
    payload_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string message = 3;
inline bool DataTransferEncryptorMessageProto::has_message() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DataTransferEncryptorMessageProto::set_has_message() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DataTransferEncryptorMessageProto::clear_has_message() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DataTransferEncryptorMessageProto::clear_message() {
  if (message_ != &::google::protobuf::internal::kEmptyString) {
    message_->clear();
  }
  clear_has_message();
}
inline const ::std::string& DataTransferEncryptorMessageProto::message() const {
  return *message_;
}
inline void DataTransferEncryptorMessageProto::set_message(const ::std::string& value) {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  message_->assign(value);
}
inline void DataTransferEncryptorMessageProto::set_message(const char* value) {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  message_->assign(value);
}
inline void DataTransferEncryptorMessageProto::set_message(const char* value, size_t size) {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  message_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* DataTransferEncryptorMessageProto::mutable_message() {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  return message_;
}
inline ::std::string* DataTransferEncryptorMessageProto::release_message() {
  clear_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = message_;
    message_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void DataTransferEncryptorMessageProto::set_allocated_message(::std::string* message) {
  if (message_ != &::google::protobuf::internal::kEmptyString) {
    delete message_;
  }
  if (message) {
    set_has_message();
    message_ = message;
  } else {
    clear_has_message();
    message_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.CipherOptionProto cipherOption = 4;
inline int DataTransferEncryptorMessageProto::cipheroption_size() const {
  return cipheroption_.size();
}
inline void DataTransferEncryptorMessageProto::clear_cipheroption() {
  cipheroption_.Clear();
}
inline const ::hadoop::hdfs::CipherOptionProto& DataTransferEncryptorMessageProto::cipheroption(int index) const {
  return cipheroption_.Get(index);
}
inline ::hadoop::hdfs::CipherOptionProto* DataTransferEncryptorMessageProto::mutable_cipheroption(int index) {
  return cipheroption_.Mutable(index);
}
inline ::hadoop::hdfs::CipherOptionProto* DataTransferEncryptorMessageProto::add_cipheroption() {
  return cipheroption_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::CipherOptionProto >&
DataTransferEncryptorMessageProto::cipheroption() const {
  return cipheroption_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::CipherOptionProto >*
DataTransferEncryptorMessageProto::mutable_cipheroption() {
  return &cipheroption_;
}

// -------------------------------------------------------------------

// BaseHeaderProto

// required .hadoop.hdfs.ExtendedBlockProto block = 1;
inline bool BaseHeaderProto::has_block() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BaseHeaderProto::set_has_block() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BaseHeaderProto::clear_has_block() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BaseHeaderProto::clear_block() {
  if (block_ != NULL) block_->::hadoop::hdfs::ExtendedBlockProto::Clear();
  clear_has_block();
}
inline const ::hadoop::hdfs::ExtendedBlockProto& BaseHeaderProto::block() const {
  return block_ != NULL ? *block_ : *default_instance_->block_;
}
inline ::hadoop::hdfs::ExtendedBlockProto* BaseHeaderProto::mutable_block() {
  set_has_block();
  if (block_ == NULL) block_ = new ::hadoop::hdfs::ExtendedBlockProto;
  return block_;
}
inline ::hadoop::hdfs::ExtendedBlockProto* BaseHeaderProto::release_block() {
  clear_has_block();
  ::hadoop::hdfs::ExtendedBlockProto* temp = block_;
  block_ = NULL;
  return temp;
}
inline void BaseHeaderProto::set_allocated_block(::hadoop::hdfs::ExtendedBlockProto* block) {
  delete block_;
  block_ = block;
  if (block) {
    set_has_block();
  } else {
    clear_has_block();
  }
}

// optional .hadoop.common.TokenProto token = 2;
inline bool BaseHeaderProto::has_token() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void BaseHeaderProto::set_has_token() {
  _has_bits_[0] |= 0x00000002u;
}
inline void BaseHeaderProto::clear_has_token() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void BaseHeaderProto::clear_token() {
  if (token_ != NULL) token_->::hadoop::common::TokenProto::Clear();
  clear_has_token();
}
inline const ::hadoop::common::TokenProto& BaseHeaderProto::token() const {
  return token_ != NULL ? *token_ : *default_instance_->token_;
}
inline ::hadoop::common::TokenProto* BaseHeaderProto::mutable_token() {
  set_has_token();
  if (token_ == NULL) token_ = new ::hadoop::common::TokenProto;
  return token_;
}
inline ::hadoop::common::TokenProto* BaseHeaderProto::release_token() {
  clear_has_token();
  ::hadoop::common::TokenProto* temp = token_;
  token_ = NULL;
  return temp;
}
inline void BaseHeaderProto::set_allocated_token(::hadoop::common::TokenProto* token) {
  delete token_;
  token_ = token;
  if (token) {
    set_has_token();
  } else {
    clear_has_token();
  }
}

// optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 3;
inline bool BaseHeaderProto::has_traceinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void BaseHeaderProto::set_has_traceinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void BaseHeaderProto::clear_has_traceinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void BaseHeaderProto::clear_traceinfo() {
  if (traceinfo_ != NULL) traceinfo_->::hadoop::hdfs::DataTransferTraceInfoProto::Clear();
  clear_has_traceinfo();
}
inline const ::hadoop::hdfs::DataTransferTraceInfoProto& BaseHeaderProto::traceinfo() const {
  return traceinfo_ != NULL ? *traceinfo_ : *default_instance_->traceinfo_;
}
inline ::hadoop::hdfs::DataTransferTraceInfoProto* BaseHeaderProto::mutable_traceinfo() {
  set_has_traceinfo();
  if (traceinfo_ == NULL) traceinfo_ = new ::hadoop::hdfs::DataTransferTraceInfoProto;
  return traceinfo_;
}
inline ::hadoop::hdfs::DataTransferTraceInfoProto* BaseHeaderProto::release_traceinfo() {
  clear_has_traceinfo();
  ::hadoop::hdfs::DataTransferTraceInfoProto* temp = traceinfo_;
  traceinfo_ = NULL;
  return temp;
}
inline void BaseHeaderProto::set_allocated_traceinfo(::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo) {
  delete traceinfo_;
  traceinfo_ = traceinfo;
  if (traceinfo) {
    set_has_traceinfo();
  } else {
    clear_has_traceinfo();
  }
}

// -------------------------------------------------------------------

// DataTransferTraceInfoProto

// required uint64 traceId = 1;
inline bool DataTransferTraceInfoProto::has_traceid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DataTransferTraceInfoProto::set_has_traceid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DataTransferTraceInfoProto::clear_has_traceid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DataTransferTraceInfoProto::clear_traceid() {
  traceid_ = GOOGLE_ULONGLONG(0);
  clear_has_traceid();
}
inline ::google::protobuf::uint64 DataTransferTraceInfoProto::traceid() const {
  return traceid_;
}
inline void DataTransferTraceInfoProto::set_traceid(::google::protobuf::uint64 value) {
  set_has_traceid();
  traceid_ = value;
}

// required uint64 parentId = 2;
inline bool DataTransferTraceInfoProto::has_parentid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DataTransferTraceInfoProto::set_has_parentid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DataTransferTraceInfoProto::clear_has_parentid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DataTransferTraceInfoProto::clear_parentid() {
  parentid_ = GOOGLE_ULONGLONG(0);
  clear_has_parentid();
}
inline ::google::protobuf::uint64 DataTransferTraceInfoProto::parentid() const {
  return parentid_;
}
inline void DataTransferTraceInfoProto::set_parentid(::google::protobuf::uint64 value) {
  set_has_parentid();
  parentid_ = value;
}

// -------------------------------------------------------------------

// ClientOperationHeaderProto

// required .hadoop.hdfs.BaseHeaderProto baseHeader = 1;
inline bool ClientOperationHeaderProto::has_baseheader() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ClientOperationHeaderProto::set_has_baseheader() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ClientOperationHeaderProto::clear_has_baseheader() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ClientOperationHeaderProto::clear_baseheader() {
  if (baseheader_ != NULL) baseheader_->::hadoop::hdfs::BaseHeaderProto::Clear();
  clear_has_baseheader();
}
inline const ::hadoop::hdfs::BaseHeaderProto& ClientOperationHeaderProto::baseheader() const {
  return baseheader_ != NULL ? *baseheader_ : *default_instance_->baseheader_;
}
inline ::hadoop::hdfs::BaseHeaderProto* ClientOperationHeaderProto::mutable_baseheader() {
  set_has_baseheader();
  if (baseheader_ == NULL) baseheader_ = new ::hadoop::hdfs::BaseHeaderProto;
  return baseheader_;
}
inline ::hadoop::hdfs::BaseHeaderProto* ClientOperationHeaderProto::release_baseheader() {
  clear_has_baseheader();
  ::hadoop::hdfs::BaseHeaderProto* temp = baseheader_;
  baseheader_ = NULL;
  return temp;
}
inline void ClientOperationHeaderProto::set_allocated_baseheader(::hadoop::hdfs::BaseHeaderProto* baseheader) {
  delete baseheader_;
  baseheader_ = baseheader;
  if (baseheader) {
    set_has_baseheader();
  } else {
    clear_has_baseheader();
  }
}

// required string clientName = 2;
inline bool ClientOperationHeaderProto::has_clientname() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ClientOperationHeaderProto::set_has_clientname() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ClientOperationHeaderProto::clear_has_clientname() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ClientOperationHeaderProto::clear_clientname() {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    clientname_->clear();
  }
  clear_has_clientname();
}
inline const ::std::string& ClientOperationHeaderProto::clientname() const {
  return *clientname_;
}
inline void ClientOperationHeaderProto::set_clientname(const ::std::string& value) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(value);
}
inline void ClientOperationHeaderProto::set_clientname(const char* value) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(value);
}
inline void ClientOperationHeaderProto::set_clientname(const char* value, size_t size) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ClientOperationHeaderProto::mutable_clientname() {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  return clientname_;
}
inline ::std::string* ClientOperationHeaderProto::release_clientname() {
  clear_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clientname_;
    clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ClientOperationHeaderProto::set_allocated_clientname(::std::string* clientname) {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    delete clientname_;
  }
  if (clientname) {
    set_has_clientname();
    clientname_ = clientname;
  } else {
    clear_has_clientname();
    clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// CachingStrategyProto

// optional bool dropBehind = 1;
inline bool CachingStrategyProto::has_dropbehind() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CachingStrategyProto::set_has_dropbehind() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CachingStrategyProto::clear_has_dropbehind() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CachingStrategyProto::clear_dropbehind() {
  dropbehind_ = false;
  clear_has_dropbehind();
}
inline bool CachingStrategyProto::dropbehind() const {
  return dropbehind_;
}
inline void CachingStrategyProto::set_dropbehind(bool value) {
  set_has_dropbehind();
  dropbehind_ = value;
}

// optional int64 readahead = 2;
inline bool CachingStrategyProto::has_readahead() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CachingStrategyProto::set_has_readahead() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CachingStrategyProto::clear_has_readahead() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CachingStrategyProto::clear_readahead() {
  readahead_ = GOOGLE_LONGLONG(0);
  clear_has_readahead();
}
inline ::google::protobuf::int64 CachingStrategyProto::readahead() const {
  return readahead_;
}
inline void CachingStrategyProto::set_readahead(::google::protobuf::int64 value) {
  set_has_readahead();
  readahead_ = value;
}

// -------------------------------------------------------------------

// OpReadBlockProto

// required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
inline bool OpReadBlockProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpReadBlockProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpReadBlockProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpReadBlockProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::ClientOperationHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::ClientOperationHeaderProto& OpReadBlockProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::ClientOperationHeaderProto* OpReadBlockProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::ClientOperationHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::ClientOperationHeaderProto* OpReadBlockProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::ClientOperationHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpReadBlockProto::set_allocated_header(::hadoop::hdfs::ClientOperationHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// required uint64 offset = 2;
inline bool OpReadBlockProto::has_offset() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void OpReadBlockProto::set_has_offset() {
  _has_bits_[0] |= 0x00000002u;
}
inline void OpReadBlockProto::clear_has_offset() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void OpReadBlockProto::clear_offset() {
  offset_ = GOOGLE_ULONGLONG(0);
  clear_has_offset();
}
inline ::google::protobuf::uint64 OpReadBlockProto::offset() const {
  return offset_;
}
inline void OpReadBlockProto::set_offset(::google::protobuf::uint64 value) {
  set_has_offset();
  offset_ = value;
}

// required uint64 len = 3;
inline bool OpReadBlockProto::has_len() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void OpReadBlockProto::set_has_len() {
  _has_bits_[0] |= 0x00000004u;
}
inline void OpReadBlockProto::clear_has_len() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void OpReadBlockProto::clear_len() {
  len_ = GOOGLE_ULONGLONG(0);
  clear_has_len();
}
inline ::google::protobuf::uint64 OpReadBlockProto::len() const {
  return len_;
}
inline void OpReadBlockProto::set_len(::google::protobuf::uint64 value) {
  set_has_len();
  len_ = value;
}

// optional bool sendChecksums = 4 [default = true];
inline bool OpReadBlockProto::has_sendchecksums() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void OpReadBlockProto::set_has_sendchecksums() {
  _has_bits_[0] |= 0x00000008u;
}
inline void OpReadBlockProto::clear_has_sendchecksums() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void OpReadBlockProto::clear_sendchecksums() {
  sendchecksums_ = true;
  clear_has_sendchecksums();
}
inline bool OpReadBlockProto::sendchecksums() const {
  return sendchecksums_;
}
inline void OpReadBlockProto::set_sendchecksums(bool value) {
  set_has_sendchecksums();
  sendchecksums_ = value;
}

// optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 5;
inline bool OpReadBlockProto::has_cachingstrategy() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void OpReadBlockProto::set_has_cachingstrategy() {
  _has_bits_[0] |= 0x00000010u;
}
inline void OpReadBlockProto::clear_has_cachingstrategy() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void OpReadBlockProto::clear_cachingstrategy() {
  if (cachingstrategy_ != NULL) cachingstrategy_->::hadoop::hdfs::CachingStrategyProto::Clear();
  clear_has_cachingstrategy();
}
inline const ::hadoop::hdfs::CachingStrategyProto& OpReadBlockProto::cachingstrategy() const {
  return cachingstrategy_ != NULL ? *cachingstrategy_ : *default_instance_->cachingstrategy_;
}
inline ::hadoop::hdfs::CachingStrategyProto* OpReadBlockProto::mutable_cachingstrategy() {
  set_has_cachingstrategy();
  if (cachingstrategy_ == NULL) cachingstrategy_ = new ::hadoop::hdfs::CachingStrategyProto;
  return cachingstrategy_;
}
inline ::hadoop::hdfs::CachingStrategyProto* OpReadBlockProto::release_cachingstrategy() {
  clear_has_cachingstrategy();
  ::hadoop::hdfs::CachingStrategyProto* temp = cachingstrategy_;
  cachingstrategy_ = NULL;
  return temp;
}
inline void OpReadBlockProto::set_allocated_cachingstrategy(::hadoop::hdfs::CachingStrategyProto* cachingstrategy) {
  delete cachingstrategy_;
  cachingstrategy_ = cachingstrategy;
  if (cachingstrategy) {
    set_has_cachingstrategy();
  } else {
    clear_has_cachingstrategy();
  }
}

// -------------------------------------------------------------------

// ChecksumProto

// required .hadoop.hdfs.ChecksumTypeProto type = 1;
inline bool ChecksumProto::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ChecksumProto::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ChecksumProto::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ChecksumProto::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::hadoop::hdfs::ChecksumTypeProto ChecksumProto::type() const {
  return static_cast< ::hadoop::hdfs::ChecksumTypeProto >(type_);
}
inline void ChecksumProto::set_type(::hadoop::hdfs::ChecksumTypeProto value) {
  assert(::hadoop::hdfs::ChecksumTypeProto_IsValid(value));
  set_has_type();
  type_ = value;
}

// required uint32 bytesPerChecksum = 2;
inline bool ChecksumProto::has_bytesperchecksum() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ChecksumProto::set_has_bytesperchecksum() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ChecksumProto::clear_has_bytesperchecksum() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ChecksumProto::clear_bytesperchecksum() {
  bytesperchecksum_ = 0u;
  clear_has_bytesperchecksum();
}
inline ::google::protobuf::uint32 ChecksumProto::bytesperchecksum() const {
  return bytesperchecksum_;
}
inline void ChecksumProto::set_bytesperchecksum(::google::protobuf::uint32 value) {
  set_has_bytesperchecksum();
  bytesperchecksum_ = value;
}

// -------------------------------------------------------------------

// OpWriteBlockProto

// required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
inline bool OpWriteBlockProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpWriteBlockProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpWriteBlockProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpWriteBlockProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::ClientOperationHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::ClientOperationHeaderProto& OpWriteBlockProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::ClientOperationHeaderProto* OpWriteBlockProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::ClientOperationHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::ClientOperationHeaderProto* OpWriteBlockProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::ClientOperationHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpWriteBlockProto::set_allocated_header(::hadoop::hdfs::ClientOperationHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
inline int OpWriteBlockProto::targets_size() const {
  return targets_.size();
}
inline void OpWriteBlockProto::clear_targets() {
  targets_.Clear();
}
inline const ::hadoop::hdfs::DatanodeInfoProto& OpWriteBlockProto::targets(int index) const {
  return targets_.Get(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpWriteBlockProto::mutable_targets(int index) {
  return targets_.Mutable(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpWriteBlockProto::add_targets() {
  return targets_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
OpWriteBlockProto::targets() const {
  return targets_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
OpWriteBlockProto::mutable_targets() {
  return &targets_;
}

// optional .hadoop.hdfs.DatanodeInfoProto source = 3;
inline bool OpWriteBlockProto::has_source() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void OpWriteBlockProto::set_has_source() {
  _has_bits_[0] |= 0x00000004u;
}
inline void OpWriteBlockProto::clear_has_source() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void OpWriteBlockProto::clear_source() {
  if (source_ != NULL) source_->::hadoop::hdfs::DatanodeInfoProto::Clear();
  clear_has_source();
}
inline const ::hadoop::hdfs::DatanodeInfoProto& OpWriteBlockProto::source() const {
  return source_ != NULL ? *source_ : *default_instance_->source_;
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpWriteBlockProto::mutable_source() {
  set_has_source();
  if (source_ == NULL) source_ = new ::hadoop::hdfs::DatanodeInfoProto;
  return source_;
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpWriteBlockProto::release_source() {
  clear_has_source();
  ::hadoop::hdfs::DatanodeInfoProto* temp = source_;
  source_ = NULL;
  return temp;
}
inline void OpWriteBlockProto::set_allocated_source(::hadoop::hdfs::DatanodeInfoProto* source) {
  delete source_;
  source_ = source;
  if (source) {
    set_has_source();
  } else {
    clear_has_source();
  }
}

// required .hadoop.hdfs.OpWriteBlockProto.BlockConstructionStage stage = 4;
inline bool OpWriteBlockProto::has_stage() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void OpWriteBlockProto::set_has_stage() {
  _has_bits_[0] |= 0x00000008u;
}
inline void OpWriteBlockProto::clear_has_stage() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void OpWriteBlockProto::clear_stage() {
  stage_ = 0;
  clear_has_stage();
}
inline ::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage OpWriteBlockProto::stage() const {
  return static_cast< ::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage >(stage_);
}
inline void OpWriteBlockProto::set_stage(::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage value) {
  assert(::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage_IsValid(value));
  set_has_stage();
  stage_ = value;
}

// required uint32 pipelineSize = 5;
inline bool OpWriteBlockProto::has_pipelinesize() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void OpWriteBlockProto::set_has_pipelinesize() {
  _has_bits_[0] |= 0x00000010u;
}
inline void OpWriteBlockProto::clear_has_pipelinesize() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void OpWriteBlockProto::clear_pipelinesize() {
  pipelinesize_ = 0u;
  clear_has_pipelinesize();
}
inline ::google::protobuf::uint32 OpWriteBlockProto::pipelinesize() const {
  return pipelinesize_;
}
inline void OpWriteBlockProto::set_pipelinesize(::google::protobuf::uint32 value) {
  set_has_pipelinesize();
  pipelinesize_ = value;
}

// required uint64 minBytesRcvd = 6;
inline bool OpWriteBlockProto::has_minbytesrcvd() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void OpWriteBlockProto::set_has_minbytesrcvd() {
  _has_bits_[0] |= 0x00000020u;
}
inline void OpWriteBlockProto::clear_has_minbytesrcvd() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void OpWriteBlockProto::clear_minbytesrcvd() {
  minbytesrcvd_ = GOOGLE_ULONGLONG(0);
  clear_has_minbytesrcvd();
}
inline ::google::protobuf::uint64 OpWriteBlockProto::minbytesrcvd() const {
  return minbytesrcvd_;
}
inline void OpWriteBlockProto::set_minbytesrcvd(::google::protobuf::uint64 value) {
  set_has_minbytesrcvd();
  minbytesrcvd_ = value;
}

// required uint64 maxBytesRcvd = 7;
inline bool OpWriteBlockProto::has_maxbytesrcvd() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void OpWriteBlockProto::set_has_maxbytesrcvd() {
  _has_bits_[0] |= 0x00000040u;
}
inline void OpWriteBlockProto::clear_has_maxbytesrcvd() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void OpWriteBlockProto::clear_maxbytesrcvd() {
  maxbytesrcvd_ = GOOGLE_ULONGLONG(0);
  clear_has_maxbytesrcvd();
}
inline ::google::protobuf::uint64 OpWriteBlockProto::maxbytesrcvd() const {
  return maxbytesrcvd_;
}
inline void OpWriteBlockProto::set_maxbytesrcvd(::google::protobuf::uint64 value) {
  set_has_maxbytesrcvd();
  maxbytesrcvd_ = value;
}

// required uint64 latestGenerationStamp = 8;
inline bool OpWriteBlockProto::has_latestgenerationstamp() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void OpWriteBlockProto::set_has_latestgenerationstamp() {
  _has_bits_[0] |= 0x00000080u;
}
inline void OpWriteBlockProto::clear_has_latestgenerationstamp() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void OpWriteBlockProto::clear_latestgenerationstamp() {
  latestgenerationstamp_ = GOOGLE_ULONGLONG(0);
  clear_has_latestgenerationstamp();
}
inline ::google::protobuf::uint64 OpWriteBlockProto::latestgenerationstamp() const {
  return latestgenerationstamp_;
}
inline void OpWriteBlockProto::set_latestgenerationstamp(::google::protobuf::uint64 value) {
  set_has_latestgenerationstamp();
  latestgenerationstamp_ = value;
}

// required .hadoop.hdfs.ChecksumProto requestedChecksum = 9;
inline bool OpWriteBlockProto::has_requestedchecksum() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void OpWriteBlockProto::set_has_requestedchecksum() {
  _has_bits_[0] |= 0x00000100u;
}
inline void OpWriteBlockProto::clear_has_requestedchecksum() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void OpWriteBlockProto::clear_requestedchecksum() {
  if (requestedchecksum_ != NULL) requestedchecksum_->::hadoop::hdfs::ChecksumProto::Clear();
  clear_has_requestedchecksum();
}
inline const ::hadoop::hdfs::ChecksumProto& OpWriteBlockProto::requestedchecksum() const {
  return requestedchecksum_ != NULL ? *requestedchecksum_ : *default_instance_->requestedchecksum_;
}
inline ::hadoop::hdfs::ChecksumProto* OpWriteBlockProto::mutable_requestedchecksum() {
  set_has_requestedchecksum();
  if (requestedchecksum_ == NULL) requestedchecksum_ = new ::hadoop::hdfs::ChecksumProto;
  return requestedchecksum_;
}
inline ::hadoop::hdfs::ChecksumProto* OpWriteBlockProto::release_requestedchecksum() {
  clear_has_requestedchecksum();
  ::hadoop::hdfs::ChecksumProto* temp = requestedchecksum_;
  requestedchecksum_ = NULL;
  return temp;
}
inline void OpWriteBlockProto::set_allocated_requestedchecksum(::hadoop::hdfs::ChecksumProto* requestedchecksum) {
  delete requestedchecksum_;
  requestedchecksum_ = requestedchecksum;
  if (requestedchecksum) {
    set_has_requestedchecksum();
  } else {
    clear_has_requestedchecksum();
  }
}

// optional .hadoop.hdfs.CachingStrategyProto cachingStrategy = 10;
inline bool OpWriteBlockProto::has_cachingstrategy() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void OpWriteBlockProto::set_has_cachingstrategy() {
  _has_bits_[0] |= 0x00000200u;
}
inline void OpWriteBlockProto::clear_has_cachingstrategy() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void OpWriteBlockProto::clear_cachingstrategy() {
  if (cachingstrategy_ != NULL) cachingstrategy_->::hadoop::hdfs::CachingStrategyProto::Clear();
  clear_has_cachingstrategy();
}
inline const ::hadoop::hdfs::CachingStrategyProto& OpWriteBlockProto::cachingstrategy() const {
  return cachingstrategy_ != NULL ? *cachingstrategy_ : *default_instance_->cachingstrategy_;
}
inline ::hadoop::hdfs::CachingStrategyProto* OpWriteBlockProto::mutable_cachingstrategy() {
  set_has_cachingstrategy();
  if (cachingstrategy_ == NULL) cachingstrategy_ = new ::hadoop::hdfs::CachingStrategyProto;
  return cachingstrategy_;
}
inline ::hadoop::hdfs::CachingStrategyProto* OpWriteBlockProto::release_cachingstrategy() {
  clear_has_cachingstrategy();
  ::hadoop::hdfs::CachingStrategyProto* temp = cachingstrategy_;
  cachingstrategy_ = NULL;
  return temp;
}
inline void OpWriteBlockProto::set_allocated_cachingstrategy(::hadoop::hdfs::CachingStrategyProto* cachingstrategy) {
  delete cachingstrategy_;
  cachingstrategy_ = cachingstrategy;
  if (cachingstrategy) {
    set_has_cachingstrategy();
  } else {
    clear_has_cachingstrategy();
  }
}

// optional .hadoop.hdfs.StorageTypeProto storageType = 11 [default = DISK];
inline bool OpWriteBlockProto::has_storagetype() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void OpWriteBlockProto::set_has_storagetype() {
  _has_bits_[0] |= 0x00000400u;
}
inline void OpWriteBlockProto::clear_has_storagetype() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void OpWriteBlockProto::clear_storagetype() {
  storagetype_ = 1;
  clear_has_storagetype();
}
inline ::hadoop::hdfs::StorageTypeProto OpWriteBlockProto::storagetype() const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(storagetype_);
}
inline void OpWriteBlockProto::set_storagetype(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  set_has_storagetype();
  storagetype_ = value;
}

// repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 12;
inline int OpWriteBlockProto::targetstoragetypes_size() const {
  return targetstoragetypes_.size();
}
inline void OpWriteBlockProto::clear_targetstoragetypes() {
  targetstoragetypes_.Clear();
}
inline ::hadoop::hdfs::StorageTypeProto OpWriteBlockProto::targetstoragetypes(int index) const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(targetstoragetypes_.Get(index));
}
inline void OpWriteBlockProto::set_targetstoragetypes(int index, ::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  targetstoragetypes_.Set(index, value);
}
inline void OpWriteBlockProto::add_targetstoragetypes(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  targetstoragetypes_.Add(value);
}
inline const ::google::protobuf::RepeatedField<int>&
OpWriteBlockProto::targetstoragetypes() const {
  return targetstoragetypes_;
}
inline ::google::protobuf::RepeatedField<int>*
OpWriteBlockProto::mutable_targetstoragetypes() {
  return &targetstoragetypes_;
}

// optional bool allowLazyPersist = 13 [default = false];
inline bool OpWriteBlockProto::has_allowlazypersist() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void OpWriteBlockProto::set_has_allowlazypersist() {
  _has_bits_[0] |= 0x00001000u;
}
inline void OpWriteBlockProto::clear_has_allowlazypersist() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void OpWriteBlockProto::clear_allowlazypersist() {
  allowlazypersist_ = false;
  clear_has_allowlazypersist();
}
inline bool OpWriteBlockProto::allowlazypersist() const {
  return allowlazypersist_;
}
inline void OpWriteBlockProto::set_allowlazypersist(bool value) {
  set_has_allowlazypersist();
  allowlazypersist_ = value;
}

// -------------------------------------------------------------------

// OpTransferBlockProto

// required .hadoop.hdfs.ClientOperationHeaderProto header = 1;
inline bool OpTransferBlockProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpTransferBlockProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpTransferBlockProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpTransferBlockProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::ClientOperationHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::ClientOperationHeaderProto& OpTransferBlockProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::ClientOperationHeaderProto* OpTransferBlockProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::ClientOperationHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::ClientOperationHeaderProto* OpTransferBlockProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::ClientOperationHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpTransferBlockProto::set_allocated_header(::hadoop::hdfs::ClientOperationHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// repeated .hadoop.hdfs.DatanodeInfoProto targets = 2;
inline int OpTransferBlockProto::targets_size() const {
  return targets_.size();
}
inline void OpTransferBlockProto::clear_targets() {
  targets_.Clear();
}
inline const ::hadoop::hdfs::DatanodeInfoProto& OpTransferBlockProto::targets(int index) const {
  return targets_.Get(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpTransferBlockProto::mutable_targets(int index) {
  return targets_.Mutable(index);
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpTransferBlockProto::add_targets() {
  return targets_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >&
OpTransferBlockProto::targets() const {
  return targets_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::DatanodeInfoProto >*
OpTransferBlockProto::mutable_targets() {
  return &targets_;
}

// repeated .hadoop.hdfs.StorageTypeProto targetStorageTypes = 3;
inline int OpTransferBlockProto::targetstoragetypes_size() const {
  return targetstoragetypes_.size();
}
inline void OpTransferBlockProto::clear_targetstoragetypes() {
  targetstoragetypes_.Clear();
}
inline ::hadoop::hdfs::StorageTypeProto OpTransferBlockProto::targetstoragetypes(int index) const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(targetstoragetypes_.Get(index));
}
inline void OpTransferBlockProto::set_targetstoragetypes(int index, ::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  targetstoragetypes_.Set(index, value);
}
inline void OpTransferBlockProto::add_targetstoragetypes(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  targetstoragetypes_.Add(value);
}
inline const ::google::protobuf::RepeatedField<int>&
OpTransferBlockProto::targetstoragetypes() const {
  return targetstoragetypes_;
}
inline ::google::protobuf::RepeatedField<int>*
OpTransferBlockProto::mutable_targetstoragetypes() {
  return &targetstoragetypes_;
}

// -------------------------------------------------------------------

// OpReplaceBlockProto

// required .hadoop.hdfs.BaseHeaderProto header = 1;
inline bool OpReplaceBlockProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpReplaceBlockProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpReplaceBlockProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpReplaceBlockProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::BaseHeaderProto& OpReplaceBlockProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpReplaceBlockProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::BaseHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpReplaceBlockProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::BaseHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpReplaceBlockProto::set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// required string delHint = 2;
inline bool OpReplaceBlockProto::has_delhint() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void OpReplaceBlockProto::set_has_delhint() {
  _has_bits_[0] |= 0x00000002u;
}
inline void OpReplaceBlockProto::clear_has_delhint() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void OpReplaceBlockProto::clear_delhint() {
  if (delhint_ != &::google::protobuf::internal::kEmptyString) {
    delhint_->clear();
  }
  clear_has_delhint();
}
inline const ::std::string& OpReplaceBlockProto::delhint() const {
  return *delhint_;
}
inline void OpReplaceBlockProto::set_delhint(const ::std::string& value) {
  set_has_delhint();
  if (delhint_ == &::google::protobuf::internal::kEmptyString) {
    delhint_ = new ::std::string;
  }
  delhint_->assign(value);
}
inline void OpReplaceBlockProto::set_delhint(const char* value) {
  set_has_delhint();
  if (delhint_ == &::google::protobuf::internal::kEmptyString) {
    delhint_ = new ::std::string;
  }
  delhint_->assign(value);
}
inline void OpReplaceBlockProto::set_delhint(const char* value, size_t size) {
  set_has_delhint();
  if (delhint_ == &::google::protobuf::internal::kEmptyString) {
    delhint_ = new ::std::string;
  }
  delhint_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* OpReplaceBlockProto::mutable_delhint() {
  set_has_delhint();
  if (delhint_ == &::google::protobuf::internal::kEmptyString) {
    delhint_ = new ::std::string;
  }
  return delhint_;
}
inline ::std::string* OpReplaceBlockProto::release_delhint() {
  clear_has_delhint();
  if (delhint_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = delhint_;
    delhint_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void OpReplaceBlockProto::set_allocated_delhint(::std::string* delhint) {
  if (delhint_ != &::google::protobuf::internal::kEmptyString) {
    delete delhint_;
  }
  if (delhint) {
    set_has_delhint();
    delhint_ = delhint;
  } else {
    clear_has_delhint();
    delhint_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required .hadoop.hdfs.DatanodeInfoProto source = 3;
inline bool OpReplaceBlockProto::has_source() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void OpReplaceBlockProto::set_has_source() {
  _has_bits_[0] |= 0x00000004u;
}
inline void OpReplaceBlockProto::clear_has_source() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void OpReplaceBlockProto::clear_source() {
  if (source_ != NULL) source_->::hadoop::hdfs::DatanodeInfoProto::Clear();
  clear_has_source();
}
inline const ::hadoop::hdfs::DatanodeInfoProto& OpReplaceBlockProto::source() const {
  return source_ != NULL ? *source_ : *default_instance_->source_;
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpReplaceBlockProto::mutable_source() {
  set_has_source();
  if (source_ == NULL) source_ = new ::hadoop::hdfs::DatanodeInfoProto;
  return source_;
}
inline ::hadoop::hdfs::DatanodeInfoProto* OpReplaceBlockProto::release_source() {
  clear_has_source();
  ::hadoop::hdfs::DatanodeInfoProto* temp = source_;
  source_ = NULL;
  return temp;
}
inline void OpReplaceBlockProto::set_allocated_source(::hadoop::hdfs::DatanodeInfoProto* source) {
  delete source_;
  source_ = source;
  if (source) {
    set_has_source();
  } else {
    clear_has_source();
  }
}

// optional .hadoop.hdfs.StorageTypeProto storageType = 4 [default = DISK];
inline bool OpReplaceBlockProto::has_storagetype() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void OpReplaceBlockProto::set_has_storagetype() {
  _has_bits_[0] |= 0x00000008u;
}
inline void OpReplaceBlockProto::clear_has_storagetype() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void OpReplaceBlockProto::clear_storagetype() {
  storagetype_ = 1;
  clear_has_storagetype();
}
inline ::hadoop::hdfs::StorageTypeProto OpReplaceBlockProto::storagetype() const {
  return static_cast< ::hadoop::hdfs::StorageTypeProto >(storagetype_);
}
inline void OpReplaceBlockProto::set_storagetype(::hadoop::hdfs::StorageTypeProto value) {
  assert(::hadoop::hdfs::StorageTypeProto_IsValid(value));
  set_has_storagetype();
  storagetype_ = value;
}

// -------------------------------------------------------------------

// OpCopyBlockProto

// required .hadoop.hdfs.BaseHeaderProto header = 1;
inline bool OpCopyBlockProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpCopyBlockProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpCopyBlockProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpCopyBlockProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::BaseHeaderProto& OpCopyBlockProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpCopyBlockProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::BaseHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpCopyBlockProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::BaseHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpCopyBlockProto::set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// -------------------------------------------------------------------

// OpBlockChecksumProto

// required .hadoop.hdfs.BaseHeaderProto header = 1;
inline bool OpBlockChecksumProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpBlockChecksumProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpBlockChecksumProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpBlockChecksumProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::BaseHeaderProto& OpBlockChecksumProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpBlockChecksumProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::BaseHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpBlockChecksumProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::BaseHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpBlockChecksumProto::set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// -------------------------------------------------------------------

// ShortCircuitShmIdProto

// required int64 hi = 1;
inline bool ShortCircuitShmIdProto::has_hi() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ShortCircuitShmIdProto::set_has_hi() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ShortCircuitShmIdProto::clear_has_hi() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ShortCircuitShmIdProto::clear_hi() {
  hi_ = GOOGLE_LONGLONG(0);
  clear_has_hi();
}
inline ::google::protobuf::int64 ShortCircuitShmIdProto::hi() const {
  return hi_;
}
inline void ShortCircuitShmIdProto::set_hi(::google::protobuf::int64 value) {
  set_has_hi();
  hi_ = value;
}

// required int64 lo = 2;
inline bool ShortCircuitShmIdProto::has_lo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ShortCircuitShmIdProto::set_has_lo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ShortCircuitShmIdProto::clear_has_lo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ShortCircuitShmIdProto::clear_lo() {
  lo_ = GOOGLE_LONGLONG(0);
  clear_has_lo();
}
inline ::google::protobuf::int64 ShortCircuitShmIdProto::lo() const {
  return lo_;
}
inline void ShortCircuitShmIdProto::set_lo(::google::protobuf::int64 value) {
  set_has_lo();
  lo_ = value;
}

// -------------------------------------------------------------------

// ShortCircuitShmSlotProto

// required .hadoop.hdfs.ShortCircuitShmIdProto shmId = 1;
inline bool ShortCircuitShmSlotProto::has_shmid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ShortCircuitShmSlotProto::set_has_shmid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ShortCircuitShmSlotProto::clear_has_shmid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ShortCircuitShmSlotProto::clear_shmid() {
  if (shmid_ != NULL) shmid_->::hadoop::hdfs::ShortCircuitShmIdProto::Clear();
  clear_has_shmid();
}
inline const ::hadoop::hdfs::ShortCircuitShmIdProto& ShortCircuitShmSlotProto::shmid() const {
  return shmid_ != NULL ? *shmid_ : *default_instance_->shmid_;
}
inline ::hadoop::hdfs::ShortCircuitShmIdProto* ShortCircuitShmSlotProto::mutable_shmid() {
  set_has_shmid();
  if (shmid_ == NULL) shmid_ = new ::hadoop::hdfs::ShortCircuitShmIdProto;
  return shmid_;
}
inline ::hadoop::hdfs::ShortCircuitShmIdProto* ShortCircuitShmSlotProto::release_shmid() {
  clear_has_shmid();
  ::hadoop::hdfs::ShortCircuitShmIdProto* temp = shmid_;
  shmid_ = NULL;
  return temp;
}
inline void ShortCircuitShmSlotProto::set_allocated_shmid(::hadoop::hdfs::ShortCircuitShmIdProto* shmid) {
  delete shmid_;
  shmid_ = shmid;
  if (shmid) {
    set_has_shmid();
  } else {
    clear_has_shmid();
  }
}

// required int32 slotIdx = 2;
inline bool ShortCircuitShmSlotProto::has_slotidx() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ShortCircuitShmSlotProto::set_has_slotidx() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ShortCircuitShmSlotProto::clear_has_slotidx() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ShortCircuitShmSlotProto::clear_slotidx() {
  slotidx_ = 0;
  clear_has_slotidx();
}
inline ::google::protobuf::int32 ShortCircuitShmSlotProto::slotidx() const {
  return slotidx_;
}
inline void ShortCircuitShmSlotProto::set_slotidx(::google::protobuf::int32 value) {
  set_has_slotidx();
  slotidx_ = value;
}

// -------------------------------------------------------------------

// OpRequestShortCircuitAccessProto

// required .hadoop.hdfs.BaseHeaderProto header = 1;
inline bool OpRequestShortCircuitAccessProto::has_header() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpRequestShortCircuitAccessProto::set_has_header() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpRequestShortCircuitAccessProto::clear_has_header() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpRequestShortCircuitAccessProto::clear_header() {
  if (header_ != NULL) header_->::hadoop::hdfs::BaseHeaderProto::Clear();
  clear_has_header();
}
inline const ::hadoop::hdfs::BaseHeaderProto& OpRequestShortCircuitAccessProto::header() const {
  return header_ != NULL ? *header_ : *default_instance_->header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpRequestShortCircuitAccessProto::mutable_header() {
  set_has_header();
  if (header_ == NULL) header_ = new ::hadoop::hdfs::BaseHeaderProto;
  return header_;
}
inline ::hadoop::hdfs::BaseHeaderProto* OpRequestShortCircuitAccessProto::release_header() {
  clear_has_header();
  ::hadoop::hdfs::BaseHeaderProto* temp = header_;
  header_ = NULL;
  return temp;
}
inline void OpRequestShortCircuitAccessProto::set_allocated_header(::hadoop::hdfs::BaseHeaderProto* header) {
  delete header_;
  header_ = header;
  if (header) {
    set_has_header();
  } else {
    clear_has_header();
  }
}

// required uint32 maxVersion = 2;
inline bool OpRequestShortCircuitAccessProto::has_maxversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void OpRequestShortCircuitAccessProto::set_has_maxversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void OpRequestShortCircuitAccessProto::clear_has_maxversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void OpRequestShortCircuitAccessProto::clear_maxversion() {
  maxversion_ = 0u;
  clear_has_maxversion();
}
inline ::google::protobuf::uint32 OpRequestShortCircuitAccessProto::maxversion() const {
  return maxversion_;
}
inline void OpRequestShortCircuitAccessProto::set_maxversion(::google::protobuf::uint32 value) {
  set_has_maxversion();
  maxversion_ = value;
}

// optional .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 3;
inline bool OpRequestShortCircuitAccessProto::has_slotid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void OpRequestShortCircuitAccessProto::set_has_slotid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void OpRequestShortCircuitAccessProto::clear_has_slotid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void OpRequestShortCircuitAccessProto::clear_slotid() {
  if (slotid_ != NULL) slotid_->::hadoop::hdfs::ShortCircuitShmSlotProto::Clear();
  clear_has_slotid();
}
inline const ::hadoop::hdfs::ShortCircuitShmSlotProto& OpRequestShortCircuitAccessProto::slotid() const {
  return slotid_ != NULL ? *slotid_ : *default_instance_->slotid_;
}
inline ::hadoop::hdfs::ShortCircuitShmSlotProto* OpRequestShortCircuitAccessProto::mutable_slotid() {
  set_has_slotid();
  if (slotid_ == NULL) slotid_ = new ::hadoop::hdfs::ShortCircuitShmSlotProto;
  return slotid_;
}
inline ::hadoop::hdfs::ShortCircuitShmSlotProto* OpRequestShortCircuitAccessProto::release_slotid() {
  clear_has_slotid();
  ::hadoop::hdfs::ShortCircuitShmSlotProto* temp = slotid_;
  slotid_ = NULL;
  return temp;
}
inline void OpRequestShortCircuitAccessProto::set_allocated_slotid(::hadoop::hdfs::ShortCircuitShmSlotProto* slotid) {
  delete slotid_;
  slotid_ = slotid;
  if (slotid) {
    set_has_slotid();
  } else {
    clear_has_slotid();
  }
}

// -------------------------------------------------------------------

// ReleaseShortCircuitAccessRequestProto

// required .hadoop.hdfs.ShortCircuitShmSlotProto slotId = 1;
inline bool ReleaseShortCircuitAccessRequestProto::has_slotid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ReleaseShortCircuitAccessRequestProto::set_has_slotid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ReleaseShortCircuitAccessRequestProto::clear_has_slotid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ReleaseShortCircuitAccessRequestProto::clear_slotid() {
  if (slotid_ != NULL) slotid_->::hadoop::hdfs::ShortCircuitShmSlotProto::Clear();
  clear_has_slotid();
}
inline const ::hadoop::hdfs::ShortCircuitShmSlotProto& ReleaseShortCircuitAccessRequestProto::slotid() const {
  return slotid_ != NULL ? *slotid_ : *default_instance_->slotid_;
}
inline ::hadoop::hdfs::ShortCircuitShmSlotProto* ReleaseShortCircuitAccessRequestProto::mutable_slotid() {
  set_has_slotid();
  if (slotid_ == NULL) slotid_ = new ::hadoop::hdfs::ShortCircuitShmSlotProto;
  return slotid_;
}
inline ::hadoop::hdfs::ShortCircuitShmSlotProto* ReleaseShortCircuitAccessRequestProto::release_slotid() {
  clear_has_slotid();
  ::hadoop::hdfs::ShortCircuitShmSlotProto* temp = slotid_;
  slotid_ = NULL;
  return temp;
}
inline void ReleaseShortCircuitAccessRequestProto::set_allocated_slotid(::hadoop::hdfs::ShortCircuitShmSlotProto* slotid) {
  delete slotid_;
  slotid_ = slotid;
  if (slotid) {
    set_has_slotid();
  } else {
    clear_has_slotid();
  }
}

// optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
inline bool ReleaseShortCircuitAccessRequestProto::has_traceinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ReleaseShortCircuitAccessRequestProto::set_has_traceinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ReleaseShortCircuitAccessRequestProto::clear_has_traceinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ReleaseShortCircuitAccessRequestProto::clear_traceinfo() {
  if (traceinfo_ != NULL) traceinfo_->::hadoop::hdfs::DataTransferTraceInfoProto::Clear();
  clear_has_traceinfo();
}
inline const ::hadoop::hdfs::DataTransferTraceInfoProto& ReleaseShortCircuitAccessRequestProto::traceinfo() const {
  return traceinfo_ != NULL ? *traceinfo_ : *default_instance_->traceinfo_;
}
inline ::hadoop::hdfs::DataTransferTraceInfoProto* ReleaseShortCircuitAccessRequestProto::mutable_traceinfo() {
  set_has_traceinfo();
  if (traceinfo_ == NULL) traceinfo_ = new ::hadoop::hdfs::DataTransferTraceInfoProto;
  return traceinfo_;
}
inline ::hadoop::hdfs::DataTransferTraceInfoProto* ReleaseShortCircuitAccessRequestProto::release_traceinfo() {
  clear_has_traceinfo();
  ::hadoop::hdfs::DataTransferTraceInfoProto* temp = traceinfo_;
  traceinfo_ = NULL;
  return temp;
}
inline void ReleaseShortCircuitAccessRequestProto::set_allocated_traceinfo(::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo) {
  delete traceinfo_;
  traceinfo_ = traceinfo;
  if (traceinfo) {
    set_has_traceinfo();
  } else {
    clear_has_traceinfo();
  }
}

// -------------------------------------------------------------------

// ReleaseShortCircuitAccessResponseProto

// required .hadoop.hdfs.Status status = 1;
inline bool ReleaseShortCircuitAccessResponseProto::has_status() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ReleaseShortCircuitAccessResponseProto::set_has_status() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ReleaseShortCircuitAccessResponseProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ReleaseShortCircuitAccessResponseProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::hdfs::Status ReleaseShortCircuitAccessResponseProto::status() const {
  return static_cast< ::hadoop::hdfs::Status >(status_);
}
inline void ReleaseShortCircuitAccessResponseProto::set_status(::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  set_has_status();
  status_ = value;
}

// optional string error = 2;
inline bool ReleaseShortCircuitAccessResponseProto::has_error() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ReleaseShortCircuitAccessResponseProto::set_has_error() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ReleaseShortCircuitAccessResponseProto::clear_has_error() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ReleaseShortCircuitAccessResponseProto::clear_error() {
  if (error_ != &::google::protobuf::internal::kEmptyString) {
    error_->clear();
  }
  clear_has_error();
}
inline const ::std::string& ReleaseShortCircuitAccessResponseProto::error() const {
  return *error_;
}
inline void ReleaseShortCircuitAccessResponseProto::set_error(const ::std::string& value) {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  error_->assign(value);
}
inline void ReleaseShortCircuitAccessResponseProto::set_error(const char* value) {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  error_->assign(value);
}
inline void ReleaseShortCircuitAccessResponseProto::set_error(const char* value, size_t size) {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  error_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ReleaseShortCircuitAccessResponseProto::mutable_error() {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  return error_;
}
inline ::std::string* ReleaseShortCircuitAccessResponseProto::release_error() {
  clear_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = error_;
    error_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ReleaseShortCircuitAccessResponseProto::set_allocated_error(::std::string* error) {
  if (error_ != &::google::protobuf::internal::kEmptyString) {
    delete error_;
  }
  if (error) {
    set_has_error();
    error_ = error;
  } else {
    clear_has_error();
    error_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// ShortCircuitShmRequestProto

// required string clientName = 1;
inline bool ShortCircuitShmRequestProto::has_clientname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ShortCircuitShmRequestProto::set_has_clientname() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ShortCircuitShmRequestProto::clear_has_clientname() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ShortCircuitShmRequestProto::clear_clientname() {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    clientname_->clear();
  }
  clear_has_clientname();
}
inline const ::std::string& ShortCircuitShmRequestProto::clientname() const {
  return *clientname_;
}
inline void ShortCircuitShmRequestProto::set_clientname(const ::std::string& value) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(value);
}
inline void ShortCircuitShmRequestProto::set_clientname(const char* value) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(value);
}
inline void ShortCircuitShmRequestProto::set_clientname(const char* value, size_t size) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ShortCircuitShmRequestProto::mutable_clientname() {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  return clientname_;
}
inline ::std::string* ShortCircuitShmRequestProto::release_clientname() {
  clear_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clientname_;
    clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ShortCircuitShmRequestProto::set_allocated_clientname(::std::string* clientname) {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    delete clientname_;
  }
  if (clientname) {
    set_has_clientname();
    clientname_ = clientname;
  } else {
    clear_has_clientname();
    clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.DataTransferTraceInfoProto traceInfo = 2;
inline bool ShortCircuitShmRequestProto::has_traceinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ShortCircuitShmRequestProto::set_has_traceinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ShortCircuitShmRequestProto::clear_has_traceinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ShortCircuitShmRequestProto::clear_traceinfo() {
  if (traceinfo_ != NULL) traceinfo_->::hadoop::hdfs::DataTransferTraceInfoProto::Clear();
  clear_has_traceinfo();
}
inline const ::hadoop::hdfs::DataTransferTraceInfoProto& ShortCircuitShmRequestProto::traceinfo() const {
  return traceinfo_ != NULL ? *traceinfo_ : *default_instance_->traceinfo_;
}
inline ::hadoop::hdfs::DataTransferTraceInfoProto* ShortCircuitShmRequestProto::mutable_traceinfo() {
  set_has_traceinfo();
  if (traceinfo_ == NULL) traceinfo_ = new ::hadoop::hdfs::DataTransferTraceInfoProto;
  return traceinfo_;
}
inline ::hadoop::hdfs::DataTransferTraceInfoProto* ShortCircuitShmRequestProto::release_traceinfo() {
  clear_has_traceinfo();
  ::hadoop::hdfs::DataTransferTraceInfoProto* temp = traceinfo_;
  traceinfo_ = NULL;
  return temp;
}
inline void ShortCircuitShmRequestProto::set_allocated_traceinfo(::hadoop::hdfs::DataTransferTraceInfoProto* traceinfo) {
  delete traceinfo_;
  traceinfo_ = traceinfo;
  if (traceinfo) {
    set_has_traceinfo();
  } else {
    clear_has_traceinfo();
  }
}

// -------------------------------------------------------------------

// ShortCircuitShmResponseProto

// required .hadoop.hdfs.Status status = 1;
inline bool ShortCircuitShmResponseProto::has_status() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ShortCircuitShmResponseProto::set_has_status() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ShortCircuitShmResponseProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ShortCircuitShmResponseProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::hdfs::Status ShortCircuitShmResponseProto::status() const {
  return static_cast< ::hadoop::hdfs::Status >(status_);
}
inline void ShortCircuitShmResponseProto::set_status(::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  set_has_status();
  status_ = value;
}

// optional string error = 2;
inline bool ShortCircuitShmResponseProto::has_error() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ShortCircuitShmResponseProto::set_has_error() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ShortCircuitShmResponseProto::clear_has_error() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ShortCircuitShmResponseProto::clear_error() {
  if (error_ != &::google::protobuf::internal::kEmptyString) {
    error_->clear();
  }
  clear_has_error();
}
inline const ::std::string& ShortCircuitShmResponseProto::error() const {
  return *error_;
}
inline void ShortCircuitShmResponseProto::set_error(const ::std::string& value) {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  error_->assign(value);
}
inline void ShortCircuitShmResponseProto::set_error(const char* value) {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  error_->assign(value);
}
inline void ShortCircuitShmResponseProto::set_error(const char* value, size_t size) {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  error_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* ShortCircuitShmResponseProto::mutable_error() {
  set_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    error_ = new ::std::string;
  }
  return error_;
}
inline ::std::string* ShortCircuitShmResponseProto::release_error() {
  clear_has_error();
  if (error_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = error_;
    error_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void ShortCircuitShmResponseProto::set_allocated_error(::std::string* error) {
  if (error_ != &::google::protobuf::internal::kEmptyString) {
    delete error_;
  }
  if (error) {
    set_has_error();
    error_ = error;
  } else {
    clear_has_error();
    error_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.ShortCircuitShmIdProto id = 3;
inline bool ShortCircuitShmResponseProto::has_id() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ShortCircuitShmResponseProto::set_has_id() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ShortCircuitShmResponseProto::clear_has_id() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ShortCircuitShmResponseProto::clear_id() {
  if (id_ != NULL) id_->::hadoop::hdfs::ShortCircuitShmIdProto::Clear();
  clear_has_id();
}
inline const ::hadoop::hdfs::ShortCircuitShmIdProto& ShortCircuitShmResponseProto::id() const {
  return id_ != NULL ? *id_ : *default_instance_->id_;
}
inline ::hadoop::hdfs::ShortCircuitShmIdProto* ShortCircuitShmResponseProto::mutable_id() {
  set_has_id();
  if (id_ == NULL) id_ = new ::hadoop::hdfs::ShortCircuitShmIdProto;
  return id_;
}
inline ::hadoop::hdfs::ShortCircuitShmIdProto* ShortCircuitShmResponseProto::release_id() {
  clear_has_id();
  ::hadoop::hdfs::ShortCircuitShmIdProto* temp = id_;
  id_ = NULL;
  return temp;
}
inline void ShortCircuitShmResponseProto::set_allocated_id(::hadoop::hdfs::ShortCircuitShmIdProto* id) {
  delete id_;
  id_ = id;
  if (id) {
    set_has_id();
  } else {
    clear_has_id();
  }
}

// -------------------------------------------------------------------

// PacketHeaderProto

// required sfixed64 offsetInBlock = 1;
inline bool PacketHeaderProto::has_offsetinblock() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PacketHeaderProto::set_has_offsetinblock() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PacketHeaderProto::clear_has_offsetinblock() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PacketHeaderProto::clear_offsetinblock() {
  offsetinblock_ = GOOGLE_LONGLONG(0);
  clear_has_offsetinblock();
}
inline ::google::protobuf::int64 PacketHeaderProto::offsetinblock() const {
  return offsetinblock_;
}
inline void PacketHeaderProto::set_offsetinblock(::google::protobuf::int64 value) {
  set_has_offsetinblock();
  offsetinblock_ = value;
}

// required sfixed64 seqno = 2;
inline bool PacketHeaderProto::has_seqno() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void PacketHeaderProto::set_has_seqno() {
  _has_bits_[0] |= 0x00000002u;
}
inline void PacketHeaderProto::clear_has_seqno() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void PacketHeaderProto::clear_seqno() {
  seqno_ = GOOGLE_LONGLONG(0);
  clear_has_seqno();
}
inline ::google::protobuf::int64 PacketHeaderProto::seqno() const {
  return seqno_;
}
inline void PacketHeaderProto::set_seqno(::google::protobuf::int64 value) {
  set_has_seqno();
  seqno_ = value;
}

// required bool lastPacketInBlock = 3;
inline bool PacketHeaderProto::has_lastpacketinblock() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void PacketHeaderProto::set_has_lastpacketinblock() {
  _has_bits_[0] |= 0x00000004u;
}
inline void PacketHeaderProto::clear_has_lastpacketinblock() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void PacketHeaderProto::clear_lastpacketinblock() {
  lastpacketinblock_ = false;
  clear_has_lastpacketinblock();
}
inline bool PacketHeaderProto::lastpacketinblock() const {
  return lastpacketinblock_;
}
inline void PacketHeaderProto::set_lastpacketinblock(bool value) {
  set_has_lastpacketinblock();
  lastpacketinblock_ = value;
}

// required sfixed32 dataLen = 4;
inline bool PacketHeaderProto::has_datalen() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void PacketHeaderProto::set_has_datalen() {
  _has_bits_[0] |= 0x00000008u;
}
inline void PacketHeaderProto::clear_has_datalen() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void PacketHeaderProto::clear_datalen() {
  datalen_ = 0;
  clear_has_datalen();
}
inline ::google::protobuf::int32 PacketHeaderProto::datalen() const {
  return datalen_;
}
inline void PacketHeaderProto::set_datalen(::google::protobuf::int32 value) {
  set_has_datalen();
  datalen_ = value;
}

// optional bool syncBlock = 5 [default = false];
inline bool PacketHeaderProto::has_syncblock() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void PacketHeaderProto::set_has_syncblock() {
  _has_bits_[0] |= 0x00000010u;
}
inline void PacketHeaderProto::clear_has_syncblock() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void PacketHeaderProto::clear_syncblock() {
  syncblock_ = false;
  clear_has_syncblock();
}
inline bool PacketHeaderProto::syncblock() const {
  return syncblock_;
}
inline void PacketHeaderProto::set_syncblock(bool value) {
  set_has_syncblock();
  syncblock_ = value;
}

// -------------------------------------------------------------------

// PipelineAckProto

// required sint64 seqno = 1;
inline bool PipelineAckProto::has_seqno() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PipelineAckProto::set_has_seqno() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PipelineAckProto::clear_has_seqno() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PipelineAckProto::clear_seqno() {
  seqno_ = GOOGLE_LONGLONG(0);
  clear_has_seqno();
}
inline ::google::protobuf::int64 PipelineAckProto::seqno() const {
  return seqno_;
}
inline void PipelineAckProto::set_seqno(::google::protobuf::int64 value) {
  set_has_seqno();
  seqno_ = value;
}

// repeated .hadoop.hdfs.Status status = 2;
inline int PipelineAckProto::status_size() const {
  return status_.size();
}
inline void PipelineAckProto::clear_status() {
  status_.Clear();
}
inline ::hadoop::hdfs::Status PipelineAckProto::status(int index) const {
  return static_cast< ::hadoop::hdfs::Status >(status_.Get(index));
}
inline void PipelineAckProto::set_status(int index, ::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  status_.Set(index, value);
}
inline void PipelineAckProto::add_status(::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  status_.Add(value);
}
inline const ::google::protobuf::RepeatedField<int>&
PipelineAckProto::status() const {
  return status_;
}
inline ::google::protobuf::RepeatedField<int>*
PipelineAckProto::mutable_status() {
  return &status_;
}

// optional uint64 downstreamAckTimeNanos = 3 [default = 0];
inline bool PipelineAckProto::has_downstreamacktimenanos() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void PipelineAckProto::set_has_downstreamacktimenanos() {
  _has_bits_[0] |= 0x00000004u;
}
inline void PipelineAckProto::clear_has_downstreamacktimenanos() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void PipelineAckProto::clear_downstreamacktimenanos() {
  downstreamacktimenanos_ = GOOGLE_ULONGLONG(0);
  clear_has_downstreamacktimenanos();
}
inline ::google::protobuf::uint64 PipelineAckProto::downstreamacktimenanos() const {
  return downstreamacktimenanos_;
}
inline void PipelineAckProto::set_downstreamacktimenanos(::google::protobuf::uint64 value) {
  set_has_downstreamacktimenanos();
  downstreamacktimenanos_ = value;
}

// -------------------------------------------------------------------

// ReadOpChecksumInfoProto

// required .hadoop.hdfs.ChecksumProto checksum = 1;
inline bool ReadOpChecksumInfoProto::has_checksum() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ReadOpChecksumInfoProto::set_has_checksum() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ReadOpChecksumInfoProto::clear_has_checksum() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ReadOpChecksumInfoProto::clear_checksum() {
  if (checksum_ != NULL) checksum_->::hadoop::hdfs::ChecksumProto::Clear();
  clear_has_checksum();
}
inline const ::hadoop::hdfs::ChecksumProto& ReadOpChecksumInfoProto::checksum() const {
  return checksum_ != NULL ? *checksum_ : *default_instance_->checksum_;
}
inline ::hadoop::hdfs::ChecksumProto* ReadOpChecksumInfoProto::mutable_checksum() {
  set_has_checksum();
  if (checksum_ == NULL) checksum_ = new ::hadoop::hdfs::ChecksumProto;
  return checksum_;
}
inline ::hadoop::hdfs::ChecksumProto* ReadOpChecksumInfoProto::release_checksum() {
  clear_has_checksum();
  ::hadoop::hdfs::ChecksumProto* temp = checksum_;
  checksum_ = NULL;
  return temp;
}
inline void ReadOpChecksumInfoProto::set_allocated_checksum(::hadoop::hdfs::ChecksumProto* checksum) {
  delete checksum_;
  checksum_ = checksum;
  if (checksum) {
    set_has_checksum();
  } else {
    clear_has_checksum();
  }
}

// required uint64 chunkOffset = 2;
inline bool ReadOpChecksumInfoProto::has_chunkoffset() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ReadOpChecksumInfoProto::set_has_chunkoffset() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ReadOpChecksumInfoProto::clear_has_chunkoffset() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ReadOpChecksumInfoProto::clear_chunkoffset() {
  chunkoffset_ = GOOGLE_ULONGLONG(0);
  clear_has_chunkoffset();
}
inline ::google::protobuf::uint64 ReadOpChecksumInfoProto::chunkoffset() const {
  return chunkoffset_;
}
inline void ReadOpChecksumInfoProto::set_chunkoffset(::google::protobuf::uint64 value) {
  set_has_chunkoffset();
  chunkoffset_ = value;
}

// -------------------------------------------------------------------

// BlockOpResponseProto

// required .hadoop.hdfs.Status status = 1;
inline bool BlockOpResponseProto::has_status() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BlockOpResponseProto::set_has_status() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BlockOpResponseProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BlockOpResponseProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::hdfs::Status BlockOpResponseProto::status() const {
  return static_cast< ::hadoop::hdfs::Status >(status_);
}
inline void BlockOpResponseProto::set_status(::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  set_has_status();
  status_ = value;
}

// optional string firstBadLink = 2;
inline bool BlockOpResponseProto::has_firstbadlink() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void BlockOpResponseProto::set_has_firstbadlink() {
  _has_bits_[0] |= 0x00000002u;
}
inline void BlockOpResponseProto::clear_has_firstbadlink() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void BlockOpResponseProto::clear_firstbadlink() {
  if (firstbadlink_ != &::google::protobuf::internal::kEmptyString) {
    firstbadlink_->clear();
  }
  clear_has_firstbadlink();
}
inline const ::std::string& BlockOpResponseProto::firstbadlink() const {
  return *firstbadlink_;
}
inline void BlockOpResponseProto::set_firstbadlink(const ::std::string& value) {
  set_has_firstbadlink();
  if (firstbadlink_ == &::google::protobuf::internal::kEmptyString) {
    firstbadlink_ = new ::std::string;
  }
  firstbadlink_->assign(value);
}
inline void BlockOpResponseProto::set_firstbadlink(const char* value) {
  set_has_firstbadlink();
  if (firstbadlink_ == &::google::protobuf::internal::kEmptyString) {
    firstbadlink_ = new ::std::string;
  }
  firstbadlink_->assign(value);
}
inline void BlockOpResponseProto::set_firstbadlink(const char* value, size_t size) {
  set_has_firstbadlink();
  if (firstbadlink_ == &::google::protobuf::internal::kEmptyString) {
    firstbadlink_ = new ::std::string;
  }
  firstbadlink_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* BlockOpResponseProto::mutable_firstbadlink() {
  set_has_firstbadlink();
  if (firstbadlink_ == &::google::protobuf::internal::kEmptyString) {
    firstbadlink_ = new ::std::string;
  }
  return firstbadlink_;
}
inline ::std::string* BlockOpResponseProto::release_firstbadlink() {
  clear_has_firstbadlink();
  if (firstbadlink_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = firstbadlink_;
    firstbadlink_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void BlockOpResponseProto::set_allocated_firstbadlink(::std::string* firstbadlink) {
  if (firstbadlink_ != &::google::protobuf::internal::kEmptyString) {
    delete firstbadlink_;
  }
  if (firstbadlink) {
    set_has_firstbadlink();
    firstbadlink_ = firstbadlink;
  } else {
    clear_has_firstbadlink();
    firstbadlink_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.OpBlockChecksumResponseProto checksumResponse = 3;
inline bool BlockOpResponseProto::has_checksumresponse() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void BlockOpResponseProto::set_has_checksumresponse() {
  _has_bits_[0] |= 0x00000004u;
}
inline void BlockOpResponseProto::clear_has_checksumresponse() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void BlockOpResponseProto::clear_checksumresponse() {
  if (checksumresponse_ != NULL) checksumresponse_->::hadoop::hdfs::OpBlockChecksumResponseProto::Clear();
  clear_has_checksumresponse();
}
inline const ::hadoop::hdfs::OpBlockChecksumResponseProto& BlockOpResponseProto::checksumresponse() const {
  return checksumresponse_ != NULL ? *checksumresponse_ : *default_instance_->checksumresponse_;
}
inline ::hadoop::hdfs::OpBlockChecksumResponseProto* BlockOpResponseProto::mutable_checksumresponse() {
  set_has_checksumresponse();
  if (checksumresponse_ == NULL) checksumresponse_ = new ::hadoop::hdfs::OpBlockChecksumResponseProto;
  return checksumresponse_;
}
inline ::hadoop::hdfs::OpBlockChecksumResponseProto* BlockOpResponseProto::release_checksumresponse() {
  clear_has_checksumresponse();
  ::hadoop::hdfs::OpBlockChecksumResponseProto* temp = checksumresponse_;
  checksumresponse_ = NULL;
  return temp;
}
inline void BlockOpResponseProto::set_allocated_checksumresponse(::hadoop::hdfs::OpBlockChecksumResponseProto* checksumresponse) {
  delete checksumresponse_;
  checksumresponse_ = checksumresponse;
  if (checksumresponse) {
    set_has_checksumresponse();
  } else {
    clear_has_checksumresponse();
  }
}

// optional .hadoop.hdfs.ReadOpChecksumInfoProto readOpChecksumInfo = 4;
inline bool BlockOpResponseProto::has_readopchecksuminfo() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void BlockOpResponseProto::set_has_readopchecksuminfo() {
  _has_bits_[0] |= 0x00000008u;
}
inline void BlockOpResponseProto::clear_has_readopchecksuminfo() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void BlockOpResponseProto::clear_readopchecksuminfo() {
  if (readopchecksuminfo_ != NULL) readopchecksuminfo_->::hadoop::hdfs::ReadOpChecksumInfoProto::Clear();
  clear_has_readopchecksuminfo();
}
inline const ::hadoop::hdfs::ReadOpChecksumInfoProto& BlockOpResponseProto::readopchecksuminfo() const {
  return readopchecksuminfo_ != NULL ? *readopchecksuminfo_ : *default_instance_->readopchecksuminfo_;
}
inline ::hadoop::hdfs::ReadOpChecksumInfoProto* BlockOpResponseProto::mutable_readopchecksuminfo() {
  set_has_readopchecksuminfo();
  if (readopchecksuminfo_ == NULL) readopchecksuminfo_ = new ::hadoop::hdfs::ReadOpChecksumInfoProto;
  return readopchecksuminfo_;
}
inline ::hadoop::hdfs::ReadOpChecksumInfoProto* BlockOpResponseProto::release_readopchecksuminfo() {
  clear_has_readopchecksuminfo();
  ::hadoop::hdfs::ReadOpChecksumInfoProto* temp = readopchecksuminfo_;
  readopchecksuminfo_ = NULL;
  return temp;
}
inline void BlockOpResponseProto::set_allocated_readopchecksuminfo(::hadoop::hdfs::ReadOpChecksumInfoProto* readopchecksuminfo) {
  delete readopchecksuminfo_;
  readopchecksuminfo_ = readopchecksuminfo;
  if (readopchecksuminfo) {
    set_has_readopchecksuminfo();
  } else {
    clear_has_readopchecksuminfo();
  }
}

// optional string message = 5;
inline bool BlockOpResponseProto::has_message() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void BlockOpResponseProto::set_has_message() {
  _has_bits_[0] |= 0x00000010u;
}
inline void BlockOpResponseProto::clear_has_message() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void BlockOpResponseProto::clear_message() {
  if (message_ != &::google::protobuf::internal::kEmptyString) {
    message_->clear();
  }
  clear_has_message();
}
inline const ::std::string& BlockOpResponseProto::message() const {
  return *message_;
}
inline void BlockOpResponseProto::set_message(const ::std::string& value) {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  message_->assign(value);
}
inline void BlockOpResponseProto::set_message(const char* value) {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  message_->assign(value);
}
inline void BlockOpResponseProto::set_message(const char* value, size_t size) {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  message_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* BlockOpResponseProto::mutable_message() {
  set_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    message_ = new ::std::string;
  }
  return message_;
}
inline ::std::string* BlockOpResponseProto::release_message() {
  clear_has_message();
  if (message_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = message_;
    message_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void BlockOpResponseProto::set_allocated_message(::std::string* message) {
  if (message_ != &::google::protobuf::internal::kEmptyString) {
    delete message_;
  }
  if (message) {
    set_has_message();
    message_ = message;
  } else {
    clear_has_message();
    message_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint32 shortCircuitAccessVersion = 6;
inline bool BlockOpResponseProto::has_shortcircuitaccessversion() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void BlockOpResponseProto::set_has_shortcircuitaccessversion() {
  _has_bits_[0] |= 0x00000020u;
}
inline void BlockOpResponseProto::clear_has_shortcircuitaccessversion() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void BlockOpResponseProto::clear_shortcircuitaccessversion() {
  shortcircuitaccessversion_ = 0u;
  clear_has_shortcircuitaccessversion();
}
inline ::google::protobuf::uint32 BlockOpResponseProto::shortcircuitaccessversion() const {
  return shortcircuitaccessversion_;
}
inline void BlockOpResponseProto::set_shortcircuitaccessversion(::google::protobuf::uint32 value) {
  set_has_shortcircuitaccessversion();
  shortcircuitaccessversion_ = value;
}

// -------------------------------------------------------------------

// ClientReadStatusProto

// required .hadoop.hdfs.Status status = 1;
inline bool ClientReadStatusProto::has_status() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ClientReadStatusProto::set_has_status() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ClientReadStatusProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ClientReadStatusProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::hdfs::Status ClientReadStatusProto::status() const {
  return static_cast< ::hadoop::hdfs::Status >(status_);
}
inline void ClientReadStatusProto::set_status(::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  set_has_status();
  status_ = value;
}

// -------------------------------------------------------------------

// DNTransferAckProto

// required .hadoop.hdfs.Status status = 1;
inline bool DNTransferAckProto::has_status() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DNTransferAckProto::set_has_status() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DNTransferAckProto::clear_has_status() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DNTransferAckProto::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::hadoop::hdfs::Status DNTransferAckProto::status() const {
  return static_cast< ::hadoop::hdfs::Status >(status_);
}
inline void DNTransferAckProto::set_status(::hadoop::hdfs::Status value) {
  assert(::hadoop::hdfs::Status_IsValid(value));
  set_has_status();
  status_ = value;
}

// -------------------------------------------------------------------

// OpBlockChecksumResponseProto

// required uint32 bytesPerCrc = 1;
inline bool OpBlockChecksumResponseProto::has_bytespercrc() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OpBlockChecksumResponseProto::set_has_bytespercrc() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OpBlockChecksumResponseProto::clear_has_bytespercrc() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OpBlockChecksumResponseProto::clear_bytespercrc() {
  bytespercrc_ = 0u;
  clear_has_bytespercrc();
}
inline ::google::protobuf::uint32 OpBlockChecksumResponseProto::bytespercrc() const {
  return bytespercrc_;
}
inline void OpBlockChecksumResponseProto::set_bytespercrc(::google::protobuf::uint32 value) {
  set_has_bytespercrc();
  bytespercrc_ = value;
}

// required uint64 crcPerBlock = 2;
inline bool OpBlockChecksumResponseProto::has_crcperblock() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void OpBlockChecksumResponseProto::set_has_crcperblock() {
  _has_bits_[0] |= 0x00000002u;
}
inline void OpBlockChecksumResponseProto::clear_has_crcperblock() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void OpBlockChecksumResponseProto::clear_crcperblock() {
  crcperblock_ = GOOGLE_ULONGLONG(0);
  clear_has_crcperblock();
}
inline ::google::protobuf::uint64 OpBlockChecksumResponseProto::crcperblock() const {
  return crcperblock_;
}
inline void OpBlockChecksumResponseProto::set_crcperblock(::google::protobuf::uint64 value) {
  set_has_crcperblock();
  crcperblock_ = value;
}

// required bytes md5 = 3;
inline bool OpBlockChecksumResponseProto::has_md5() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void OpBlockChecksumResponseProto::set_has_md5() {
  _has_bits_[0] |= 0x00000004u;
}
inline void OpBlockChecksumResponseProto::clear_has_md5() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void OpBlockChecksumResponseProto::clear_md5() {
  if (md5_ != &::google::protobuf::internal::kEmptyString) {
    md5_->clear();
  }
  clear_has_md5();
}
inline const ::std::string& OpBlockChecksumResponseProto::md5() const {
  return *md5_;
}
inline void OpBlockChecksumResponseProto::set_md5(const ::std::string& value) {
  set_has_md5();
  if (md5_ == &::google::protobuf::internal::kEmptyString) {
    md5_ = new ::std::string;
  }
  md5_->assign(value);
}
inline void OpBlockChecksumResponseProto::set_md5(const char* value) {
  set_has_md5();
  if (md5_ == &::google::protobuf::internal::kEmptyString) {
    md5_ = new ::std::string;
  }
  md5_->assign(value);
}
inline void OpBlockChecksumResponseProto::set_md5(const void* value, size_t size) {
  set_has_md5();
  if (md5_ == &::google::protobuf::internal::kEmptyString) {
    md5_ = new ::std::string;
  }
  md5_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* OpBlockChecksumResponseProto::mutable_md5() {
  set_has_md5();
  if (md5_ == &::google::protobuf::internal::kEmptyString) {
    md5_ = new ::std::string;
  }
  return md5_;
}
inline ::std::string* OpBlockChecksumResponseProto::release_md5() {
  clear_has_md5();
  if (md5_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = md5_;
    md5_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void OpBlockChecksumResponseProto::set_allocated_md5(::std::string* md5) {
  if (md5_ != &::google::protobuf::internal::kEmptyString) {
    delete md5_;
  }
  if (md5) {
    set_has_md5();
    md5_ = md5;
  } else {
    clear_has_md5();
    md5_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.ChecksumTypeProto crcType = 4;
inline bool OpBlockChecksumResponseProto::has_crctype() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void OpBlockChecksumResponseProto::set_has_crctype() {
  _has_bits_[0] |= 0x00000008u;
}
inline void OpBlockChecksumResponseProto::clear_has_crctype() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void OpBlockChecksumResponseProto::clear_crctype() {
  crctype_ = 0;
  clear_has_crctype();
}
inline ::hadoop::hdfs::ChecksumTypeProto OpBlockChecksumResponseProto::crctype() const {
  return static_cast< ::hadoop::hdfs::ChecksumTypeProto >(crctype_);
}
inline void OpBlockChecksumResponseProto::set_crctype(::hadoop::hdfs::ChecksumTypeProto value) {
  assert(::hadoop::hdfs::ChecksumTypeProto_IsValid(value));
  set_has_crctype();
  crctype_ = value;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus>() {
  return ::hadoop::hdfs::DataTransferEncryptorMessageProto_DataTransferEncryptorStatus_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage>() {
  return ::hadoop::hdfs::OpWriteBlockProto_BlockConstructionStage_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::Status>() {
  return ::hadoop::hdfs::Status_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_datatransfer_2eproto__INCLUDED
