// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: QJournalProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "QJournalProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {
namespace qjournal {

namespace {

const ::google::protobuf::Descriptor* JournalIdProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  JournalIdProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RequestInfoProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RequestInfoProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* SegmentStateProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SegmentStateProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PersistedRecoveryPaxosData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PersistedRecoveryPaxosData_reflection_ = NULL;
const ::google::protobuf::Descriptor* JournalRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  JournalRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* JournalResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  JournalResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* HeartbeatRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  HeartbeatRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* HeartbeatResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  HeartbeatResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* StartLogSegmentRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StartLogSegmentRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* StartLogSegmentResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StartLogSegmentResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* FinalizeLogSegmentRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FinalizeLogSegmentRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* FinalizeLogSegmentResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FinalizeLogSegmentResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PurgeLogsRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PurgeLogsRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PurgeLogsResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PurgeLogsResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* IsFormattedRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  IsFormattedRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* IsFormattedResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  IsFormattedResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DiscardSegmentsRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DiscardSegmentsRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DiscardSegmentsResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DiscardSegmentsResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetJournalCTimeRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetJournalCTimeRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetJournalCTimeResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetJournalCTimeResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoPreUpgradeRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoPreUpgradeRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoPreUpgradeResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoPreUpgradeResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoUpgradeRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoUpgradeRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoUpgradeResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoUpgradeResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoFinalizeRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoFinalizeRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoFinalizeResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoFinalizeResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CanRollBackRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CanRollBackRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* CanRollBackResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CanRollBackResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoRollbackRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoRollbackRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoRollbackResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoRollbackResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetJournalStateRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetJournalStateRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetJournalStateResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetJournalStateResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* FormatRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FormatRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* FormatResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FormatResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* NewEpochRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NewEpochRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* NewEpochResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NewEpochResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetEditLogManifestRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetEditLogManifestRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetEditLogManifestResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetEditLogManifestResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PrepareRecoveryRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PrepareRecoveryRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* PrepareRecoveryResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PrepareRecoveryResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* AcceptRecoveryRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AcceptRecoveryRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* AcceptRecoveryResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AcceptRecoveryResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_QJournalProtocol_2eproto() {
  protobuf_AddDesc_QJournalProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "QJournalProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  JournalIdProto_descriptor_ = file->message_type(0);
  static const int JournalIdProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalIdProto, identifier_),
  };
  JournalIdProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      JournalIdProto_descriptor_,
      JournalIdProto::default_instance_,
      JournalIdProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalIdProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalIdProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(JournalIdProto));
  RequestInfoProto_descriptor_ = file->message_type(1);
  static const int RequestInfoProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestInfoProto, journalid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestInfoProto, epoch_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestInfoProto, ipcserialnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestInfoProto, committedtxid_),
  };
  RequestInfoProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RequestInfoProto_descriptor_,
      RequestInfoProto::default_instance_,
      RequestInfoProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestInfoProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestInfoProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RequestInfoProto));
  SegmentStateProto_descriptor_ = file->message_type(2);
  static const int SegmentStateProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SegmentStateProto, starttxid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SegmentStateProto, endtxid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SegmentStateProto, isinprogress_),
  };
  SegmentStateProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      SegmentStateProto_descriptor_,
      SegmentStateProto::default_instance_,
      SegmentStateProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SegmentStateProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SegmentStateProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(SegmentStateProto));
  PersistedRecoveryPaxosData_descriptor_ = file->message_type(3);
  static const int PersistedRecoveryPaxosData_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PersistedRecoveryPaxosData, segmentstate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PersistedRecoveryPaxosData, acceptedinepoch_),
  };
  PersistedRecoveryPaxosData_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PersistedRecoveryPaxosData_descriptor_,
      PersistedRecoveryPaxosData::default_instance_,
      PersistedRecoveryPaxosData_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PersistedRecoveryPaxosData, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PersistedRecoveryPaxosData, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PersistedRecoveryPaxosData));
  JournalRequestProto_descriptor_ = file->message_type(4);
  static const int JournalRequestProto_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, reqinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, firsttxnid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, numtxns_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, records_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, segmenttxnid_),
  };
  JournalRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      JournalRequestProto_descriptor_,
      JournalRequestProto::default_instance_,
      JournalRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(JournalRequestProto));
  JournalResponseProto_descriptor_ = file->message_type(5);
  static const int JournalResponseProto_offsets_[1] = {
  };
  JournalResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      JournalResponseProto_descriptor_,
      JournalResponseProto::default_instance_,
      JournalResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(JournalResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(JournalResponseProto));
  HeartbeatRequestProto_descriptor_ = file->message_type(6);
  static const int HeartbeatRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HeartbeatRequestProto, reqinfo_),
  };
  HeartbeatRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      HeartbeatRequestProto_descriptor_,
      HeartbeatRequestProto::default_instance_,
      HeartbeatRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HeartbeatRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HeartbeatRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(HeartbeatRequestProto));
  HeartbeatResponseProto_descriptor_ = file->message_type(7);
  static const int HeartbeatResponseProto_offsets_[1] = {
  };
  HeartbeatResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      HeartbeatResponseProto_descriptor_,
      HeartbeatResponseProto::default_instance_,
      HeartbeatResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HeartbeatResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(HeartbeatResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(HeartbeatResponseProto));
  StartLogSegmentRequestProto_descriptor_ = file->message_type(8);
  static const int StartLogSegmentRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentRequestProto, reqinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentRequestProto, txid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentRequestProto, layoutversion_),
  };
  StartLogSegmentRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      StartLogSegmentRequestProto_descriptor_,
      StartLogSegmentRequestProto::default_instance_,
      StartLogSegmentRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(StartLogSegmentRequestProto));
  StartLogSegmentResponseProto_descriptor_ = file->message_type(9);
  static const int StartLogSegmentResponseProto_offsets_[1] = {
  };
  StartLogSegmentResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      StartLogSegmentResponseProto_descriptor_,
      StartLogSegmentResponseProto::default_instance_,
      StartLogSegmentResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartLogSegmentResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(StartLogSegmentResponseProto));
  FinalizeLogSegmentRequestProto_descriptor_ = file->message_type(10);
  static const int FinalizeLogSegmentRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentRequestProto, reqinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentRequestProto, starttxid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentRequestProto, endtxid_),
  };
  FinalizeLogSegmentRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FinalizeLogSegmentRequestProto_descriptor_,
      FinalizeLogSegmentRequestProto::default_instance_,
      FinalizeLogSegmentRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FinalizeLogSegmentRequestProto));
  FinalizeLogSegmentResponseProto_descriptor_ = file->message_type(11);
  static const int FinalizeLogSegmentResponseProto_offsets_[1] = {
  };
  FinalizeLogSegmentResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FinalizeLogSegmentResponseProto_descriptor_,
      FinalizeLogSegmentResponseProto::default_instance_,
      FinalizeLogSegmentResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FinalizeLogSegmentResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FinalizeLogSegmentResponseProto));
  PurgeLogsRequestProto_descriptor_ = file->message_type(12);
  static const int PurgeLogsRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PurgeLogsRequestProto, reqinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PurgeLogsRequestProto, mintxidtokeep_),
  };
  PurgeLogsRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PurgeLogsRequestProto_descriptor_,
      PurgeLogsRequestProto::default_instance_,
      PurgeLogsRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PurgeLogsRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PurgeLogsRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PurgeLogsRequestProto));
  PurgeLogsResponseProto_descriptor_ = file->message_type(13);
  static const int PurgeLogsResponseProto_offsets_[1] = {
  };
  PurgeLogsResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PurgeLogsResponseProto_descriptor_,
      PurgeLogsResponseProto::default_instance_,
      PurgeLogsResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PurgeLogsResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PurgeLogsResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PurgeLogsResponseProto));
  IsFormattedRequestProto_descriptor_ = file->message_type(14);
  static const int IsFormattedRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IsFormattedRequestProto, jid_),
  };
  IsFormattedRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      IsFormattedRequestProto_descriptor_,
      IsFormattedRequestProto::default_instance_,
      IsFormattedRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IsFormattedRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IsFormattedRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(IsFormattedRequestProto));
  IsFormattedResponseProto_descriptor_ = file->message_type(15);
  static const int IsFormattedResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IsFormattedResponseProto, isformatted_),
  };
  IsFormattedResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      IsFormattedResponseProto_descriptor_,
      IsFormattedResponseProto::default_instance_,
      IsFormattedResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IsFormattedResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IsFormattedResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(IsFormattedResponseProto));
  DiscardSegmentsRequestProto_descriptor_ = file->message_type(16);
  static const int DiscardSegmentsRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DiscardSegmentsRequestProto, jid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DiscardSegmentsRequestProto, starttxid_),
  };
  DiscardSegmentsRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DiscardSegmentsRequestProto_descriptor_,
      DiscardSegmentsRequestProto::default_instance_,
      DiscardSegmentsRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DiscardSegmentsRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DiscardSegmentsRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DiscardSegmentsRequestProto));
  DiscardSegmentsResponseProto_descriptor_ = file->message_type(17);
  static const int DiscardSegmentsResponseProto_offsets_[1] = {
  };
  DiscardSegmentsResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DiscardSegmentsResponseProto_descriptor_,
      DiscardSegmentsResponseProto::default_instance_,
      DiscardSegmentsResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DiscardSegmentsResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DiscardSegmentsResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DiscardSegmentsResponseProto));
  GetJournalCTimeRequestProto_descriptor_ = file->message_type(18);
  static const int GetJournalCTimeRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalCTimeRequestProto, jid_),
  };
  GetJournalCTimeRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetJournalCTimeRequestProto_descriptor_,
      GetJournalCTimeRequestProto::default_instance_,
      GetJournalCTimeRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalCTimeRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalCTimeRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetJournalCTimeRequestProto));
  GetJournalCTimeResponseProto_descriptor_ = file->message_type(19);
  static const int GetJournalCTimeResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalCTimeResponseProto, resultctime_),
  };
  GetJournalCTimeResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetJournalCTimeResponseProto_descriptor_,
      GetJournalCTimeResponseProto::default_instance_,
      GetJournalCTimeResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalCTimeResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalCTimeResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetJournalCTimeResponseProto));
  DoPreUpgradeRequestProto_descriptor_ = file->message_type(20);
  static const int DoPreUpgradeRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoPreUpgradeRequestProto, jid_),
  };
  DoPreUpgradeRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoPreUpgradeRequestProto_descriptor_,
      DoPreUpgradeRequestProto::default_instance_,
      DoPreUpgradeRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoPreUpgradeRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoPreUpgradeRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoPreUpgradeRequestProto));
  DoPreUpgradeResponseProto_descriptor_ = file->message_type(21);
  static const int DoPreUpgradeResponseProto_offsets_[1] = {
  };
  DoPreUpgradeResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoPreUpgradeResponseProto_descriptor_,
      DoPreUpgradeResponseProto::default_instance_,
      DoPreUpgradeResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoPreUpgradeResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoPreUpgradeResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoPreUpgradeResponseProto));
  DoUpgradeRequestProto_descriptor_ = file->message_type(22);
  static const int DoUpgradeRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoUpgradeRequestProto, jid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoUpgradeRequestProto, sinfo_),
  };
  DoUpgradeRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoUpgradeRequestProto_descriptor_,
      DoUpgradeRequestProto::default_instance_,
      DoUpgradeRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoUpgradeRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoUpgradeRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoUpgradeRequestProto));
  DoUpgradeResponseProto_descriptor_ = file->message_type(23);
  static const int DoUpgradeResponseProto_offsets_[1] = {
  };
  DoUpgradeResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoUpgradeResponseProto_descriptor_,
      DoUpgradeResponseProto::default_instance_,
      DoUpgradeResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoUpgradeResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoUpgradeResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoUpgradeResponseProto));
  DoFinalizeRequestProto_descriptor_ = file->message_type(24);
  static const int DoFinalizeRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoFinalizeRequestProto, jid_),
  };
  DoFinalizeRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoFinalizeRequestProto_descriptor_,
      DoFinalizeRequestProto::default_instance_,
      DoFinalizeRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoFinalizeRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoFinalizeRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoFinalizeRequestProto));
  DoFinalizeResponseProto_descriptor_ = file->message_type(25);
  static const int DoFinalizeResponseProto_offsets_[1] = {
  };
  DoFinalizeResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoFinalizeResponseProto_descriptor_,
      DoFinalizeResponseProto::default_instance_,
      DoFinalizeResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoFinalizeResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoFinalizeResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoFinalizeResponseProto));
  CanRollBackRequestProto_descriptor_ = file->message_type(26);
  static const int CanRollBackRequestProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackRequestProto, jid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackRequestProto, storage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackRequestProto, prevstorage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackRequestProto, targetlayoutversion_),
  };
  CanRollBackRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CanRollBackRequestProto_descriptor_,
      CanRollBackRequestProto::default_instance_,
      CanRollBackRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CanRollBackRequestProto));
  CanRollBackResponseProto_descriptor_ = file->message_type(27);
  static const int CanRollBackResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackResponseProto, canrollback_),
  };
  CanRollBackResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      CanRollBackResponseProto_descriptor_,
      CanRollBackResponseProto::default_instance_,
      CanRollBackResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CanRollBackResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(CanRollBackResponseProto));
  DoRollbackRequestProto_descriptor_ = file->message_type(28);
  static const int DoRollbackRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoRollbackRequestProto, jid_),
  };
  DoRollbackRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoRollbackRequestProto_descriptor_,
      DoRollbackRequestProto::default_instance_,
      DoRollbackRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoRollbackRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoRollbackRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoRollbackRequestProto));
  DoRollbackResponseProto_descriptor_ = file->message_type(29);
  static const int DoRollbackResponseProto_offsets_[1] = {
  };
  DoRollbackResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      DoRollbackResponseProto_descriptor_,
      DoRollbackResponseProto::default_instance_,
      DoRollbackResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoRollbackResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoRollbackResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(DoRollbackResponseProto));
  GetJournalStateRequestProto_descriptor_ = file->message_type(30);
  static const int GetJournalStateRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateRequestProto, jid_),
  };
  GetJournalStateRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetJournalStateRequestProto_descriptor_,
      GetJournalStateRequestProto::default_instance_,
      GetJournalStateRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetJournalStateRequestProto));
  GetJournalStateResponseProto_descriptor_ = file->message_type(31);
  static const int GetJournalStateResponseProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateResponseProto, lastpromisedepoch_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateResponseProto, httpport_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateResponseProto, fromurl_),
  };
  GetJournalStateResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetJournalStateResponseProto_descriptor_,
      GetJournalStateResponseProto::default_instance_,
      GetJournalStateResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetJournalStateResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetJournalStateResponseProto));
  FormatRequestProto_descriptor_ = file->message_type(32);
  static const int FormatRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FormatRequestProto, jid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FormatRequestProto, nsinfo_),
  };
  FormatRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FormatRequestProto_descriptor_,
      FormatRequestProto::default_instance_,
      FormatRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FormatRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FormatRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FormatRequestProto));
  FormatResponseProto_descriptor_ = file->message_type(33);
  static const int FormatResponseProto_offsets_[1] = {
  };
  FormatResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      FormatResponseProto_descriptor_,
      FormatResponseProto::default_instance_,
      FormatResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FormatResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FormatResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(FormatResponseProto));
  NewEpochRequestProto_descriptor_ = file->message_type(34);
  static const int NewEpochRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochRequestProto, jid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochRequestProto, nsinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochRequestProto, epoch_),
  };
  NewEpochRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      NewEpochRequestProto_descriptor_,
      NewEpochRequestProto::default_instance_,
      NewEpochRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(NewEpochRequestProto));
  NewEpochResponseProto_descriptor_ = file->message_type(35);
  static const int NewEpochResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochResponseProto, lastsegmenttxid_),
  };
  NewEpochResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      NewEpochResponseProto_descriptor_,
      NewEpochResponseProto::default_instance_,
      NewEpochResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewEpochResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(NewEpochResponseProto));
  GetEditLogManifestRequestProto_descriptor_ = file->message_type(36);
  static const int GetEditLogManifestRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, jid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, sincetxid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, inprogressok_),
  };
  GetEditLogManifestRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetEditLogManifestRequestProto_descriptor_,
      GetEditLogManifestRequestProto::default_instance_,
      GetEditLogManifestRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetEditLogManifestRequestProto));
  GetEditLogManifestResponseProto_descriptor_ = file->message_type(37);
  static const int GetEditLogManifestResponseProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, manifest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, httpport_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, fromurl_),
  };
  GetEditLogManifestResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetEditLogManifestResponseProto_descriptor_,
      GetEditLogManifestResponseProto::default_instance_,
      GetEditLogManifestResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetEditLogManifestResponseProto));
  PrepareRecoveryRequestProto_descriptor_ = file->message_type(38);
  static const int PrepareRecoveryRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryRequestProto, reqinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryRequestProto, segmenttxid_),
  };
  PrepareRecoveryRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PrepareRecoveryRequestProto_descriptor_,
      PrepareRecoveryRequestProto::default_instance_,
      PrepareRecoveryRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PrepareRecoveryRequestProto));
  PrepareRecoveryResponseProto_descriptor_ = file->message_type(39);
  static const int PrepareRecoveryResponseProto_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryResponseProto, segmentstate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryResponseProto, acceptedinepoch_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryResponseProto, lastwriterepoch_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryResponseProto, lastcommittedtxid_),
  };
  PrepareRecoveryResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      PrepareRecoveryResponseProto_descriptor_,
      PrepareRecoveryResponseProto::default_instance_,
      PrepareRecoveryResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PrepareRecoveryResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(PrepareRecoveryResponseProto));
  AcceptRecoveryRequestProto_descriptor_ = file->message_type(40);
  static const int AcceptRecoveryRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryRequestProto, reqinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryRequestProto, statetoaccept_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryRequestProto, fromurl_),
  };
  AcceptRecoveryRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AcceptRecoveryRequestProto_descriptor_,
      AcceptRecoveryRequestProto::default_instance_,
      AcceptRecoveryRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AcceptRecoveryRequestProto));
  AcceptRecoveryResponseProto_descriptor_ = file->message_type(41);
  static const int AcceptRecoveryResponseProto_offsets_[1] = {
  };
  AcceptRecoveryResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      AcceptRecoveryResponseProto_descriptor_,
      AcceptRecoveryResponseProto::default_instance_,
      AcceptRecoveryResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AcceptRecoveryResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(AcceptRecoveryResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_QJournalProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    JournalIdProto_descriptor_, &JournalIdProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RequestInfoProto_descriptor_, &RequestInfoProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    SegmentStateProto_descriptor_, &SegmentStateProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PersistedRecoveryPaxosData_descriptor_, &PersistedRecoveryPaxosData::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    JournalRequestProto_descriptor_, &JournalRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    JournalResponseProto_descriptor_, &JournalResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    HeartbeatRequestProto_descriptor_, &HeartbeatRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    HeartbeatResponseProto_descriptor_, &HeartbeatResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    StartLogSegmentRequestProto_descriptor_, &StartLogSegmentRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    StartLogSegmentResponseProto_descriptor_, &StartLogSegmentResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FinalizeLogSegmentRequestProto_descriptor_, &FinalizeLogSegmentRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FinalizeLogSegmentResponseProto_descriptor_, &FinalizeLogSegmentResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PurgeLogsRequestProto_descriptor_, &PurgeLogsRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PurgeLogsResponseProto_descriptor_, &PurgeLogsResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    IsFormattedRequestProto_descriptor_, &IsFormattedRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    IsFormattedResponseProto_descriptor_, &IsFormattedResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DiscardSegmentsRequestProto_descriptor_, &DiscardSegmentsRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DiscardSegmentsResponseProto_descriptor_, &DiscardSegmentsResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetJournalCTimeRequestProto_descriptor_, &GetJournalCTimeRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetJournalCTimeResponseProto_descriptor_, &GetJournalCTimeResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoPreUpgradeRequestProto_descriptor_, &DoPreUpgradeRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoPreUpgradeResponseProto_descriptor_, &DoPreUpgradeResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoUpgradeRequestProto_descriptor_, &DoUpgradeRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoUpgradeResponseProto_descriptor_, &DoUpgradeResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoFinalizeRequestProto_descriptor_, &DoFinalizeRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoFinalizeResponseProto_descriptor_, &DoFinalizeResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CanRollBackRequestProto_descriptor_, &CanRollBackRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    CanRollBackResponseProto_descriptor_, &CanRollBackResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoRollbackRequestProto_descriptor_, &DoRollbackRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    DoRollbackResponseProto_descriptor_, &DoRollbackResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetJournalStateRequestProto_descriptor_, &GetJournalStateRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetJournalStateResponseProto_descriptor_, &GetJournalStateResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FormatRequestProto_descriptor_, &FormatRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    FormatResponseProto_descriptor_, &FormatResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    NewEpochRequestProto_descriptor_, &NewEpochRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    NewEpochResponseProto_descriptor_, &NewEpochResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetEditLogManifestRequestProto_descriptor_, &GetEditLogManifestRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetEditLogManifestResponseProto_descriptor_, &GetEditLogManifestResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PrepareRecoveryRequestProto_descriptor_, &PrepareRecoveryRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    PrepareRecoveryResponseProto_descriptor_, &PrepareRecoveryResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AcceptRecoveryRequestProto_descriptor_, &AcceptRecoveryRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    AcceptRecoveryResponseProto_descriptor_, &AcceptRecoveryResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_QJournalProtocol_2eproto() {
  delete JournalIdProto::default_instance_;
  delete JournalIdProto_reflection_;
  delete RequestInfoProto::default_instance_;
  delete RequestInfoProto_reflection_;
  delete SegmentStateProto::default_instance_;
  delete SegmentStateProto_reflection_;
  delete PersistedRecoveryPaxosData::default_instance_;
  delete PersistedRecoveryPaxosData_reflection_;
  delete JournalRequestProto::default_instance_;
  delete JournalRequestProto_reflection_;
  delete JournalResponseProto::default_instance_;
  delete JournalResponseProto_reflection_;
  delete HeartbeatRequestProto::default_instance_;
  delete HeartbeatRequestProto_reflection_;
  delete HeartbeatResponseProto::default_instance_;
  delete HeartbeatResponseProto_reflection_;
  delete StartLogSegmentRequestProto::default_instance_;
  delete StartLogSegmentRequestProto_reflection_;
  delete StartLogSegmentResponseProto::default_instance_;
  delete StartLogSegmentResponseProto_reflection_;
  delete FinalizeLogSegmentRequestProto::default_instance_;
  delete FinalizeLogSegmentRequestProto_reflection_;
  delete FinalizeLogSegmentResponseProto::default_instance_;
  delete FinalizeLogSegmentResponseProto_reflection_;
  delete PurgeLogsRequestProto::default_instance_;
  delete PurgeLogsRequestProto_reflection_;
  delete PurgeLogsResponseProto::default_instance_;
  delete PurgeLogsResponseProto_reflection_;
  delete IsFormattedRequestProto::default_instance_;
  delete IsFormattedRequestProto_reflection_;
  delete IsFormattedResponseProto::default_instance_;
  delete IsFormattedResponseProto_reflection_;
  delete DiscardSegmentsRequestProto::default_instance_;
  delete DiscardSegmentsRequestProto_reflection_;
  delete DiscardSegmentsResponseProto::default_instance_;
  delete DiscardSegmentsResponseProto_reflection_;
  delete GetJournalCTimeRequestProto::default_instance_;
  delete GetJournalCTimeRequestProto_reflection_;
  delete GetJournalCTimeResponseProto::default_instance_;
  delete GetJournalCTimeResponseProto_reflection_;
  delete DoPreUpgradeRequestProto::default_instance_;
  delete DoPreUpgradeRequestProto_reflection_;
  delete DoPreUpgradeResponseProto::default_instance_;
  delete DoPreUpgradeResponseProto_reflection_;
  delete DoUpgradeRequestProto::default_instance_;
  delete DoUpgradeRequestProto_reflection_;
  delete DoUpgradeResponseProto::default_instance_;
  delete DoUpgradeResponseProto_reflection_;
  delete DoFinalizeRequestProto::default_instance_;
  delete DoFinalizeRequestProto_reflection_;
  delete DoFinalizeResponseProto::default_instance_;
  delete DoFinalizeResponseProto_reflection_;
  delete CanRollBackRequestProto::default_instance_;
  delete CanRollBackRequestProto_reflection_;
  delete CanRollBackResponseProto::default_instance_;
  delete CanRollBackResponseProto_reflection_;
  delete DoRollbackRequestProto::default_instance_;
  delete DoRollbackRequestProto_reflection_;
  delete DoRollbackResponseProto::default_instance_;
  delete DoRollbackResponseProto_reflection_;
  delete GetJournalStateRequestProto::default_instance_;
  delete GetJournalStateRequestProto_reflection_;
  delete GetJournalStateResponseProto::default_instance_;
  delete GetJournalStateResponseProto_reflection_;
  delete FormatRequestProto::default_instance_;
  delete FormatRequestProto_reflection_;
  delete FormatResponseProto::default_instance_;
  delete FormatResponseProto_reflection_;
  delete NewEpochRequestProto::default_instance_;
  delete NewEpochRequestProto_reflection_;
  delete NewEpochResponseProto::default_instance_;
  delete NewEpochResponseProto_reflection_;
  delete GetEditLogManifestRequestProto::default_instance_;
  delete GetEditLogManifestRequestProto_reflection_;
  delete GetEditLogManifestResponseProto::default_instance_;
  delete GetEditLogManifestResponseProto_reflection_;
  delete PrepareRecoveryRequestProto::default_instance_;
  delete PrepareRecoveryRequestProto_reflection_;
  delete PrepareRecoveryResponseProto::default_instance_;
  delete PrepareRecoveryResponseProto_reflection_;
  delete AcceptRecoveryRequestProto::default_instance_;
  delete AcceptRecoveryRequestProto_reflection_;
  delete AcceptRecoveryResponseProto::default_instance_;
  delete AcceptRecoveryResponseProto_reflection_;
}

void protobuf_AddDesc_QJournalProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\026QJournalProtocol.proto\022\024hadoop.hdfs.qj"
    "ournal\032\nhdfs.proto\"$\n\016JournalIdProto\022\022\n\n"
    "identifier\030\001 \002(\t\"\212\001\n\020RequestInfoProto\0227\n"
    "\tjournalId\030\001 \002(\0132$.hadoop.hdfs.qjournal."
    "JournalIdProto\022\r\n\005epoch\030\002 \002(\004\022\027\n\017ipcSeri"
    "alNumber\030\003 \002(\004\022\025\n\rcommittedTxId\030\004 \001(\004\"M\n"
    "\021SegmentStateProto\022\021\n\tstartTxId\030\001 \002(\004\022\017\n"
    "\007endTxId\030\002 \002(\004\022\024\n\014isInProgress\030\003 \002(\010\"t\n\032"
    "PersistedRecoveryPaxosData\022=\n\014segmentSta"
    "te\030\001 \002(\0132\'.hadoop.hdfs.qjournal.SegmentS"
    "tateProto\022\027\n\017acceptedInEpoch\030\002 \002(\004\"\232\001\n\023J"
    "ournalRequestProto\0227\n\007reqInfo\030\001 \002(\0132&.ha"
    "doop.hdfs.qjournal.RequestInfoProto\022\022\n\nf"
    "irstTxnId\030\002 \002(\004\022\017\n\007numTxns\030\003 \002(\r\022\017\n\007reco"
    "rds\030\004 \002(\014\022\024\n\014segmentTxnId\030\005 \002(\004\"\026\n\024Journ"
    "alResponseProto\"P\n\025HeartbeatRequestProto"
    "\0227\n\007reqInfo\030\001 \002(\0132&.hadoop.hdfs.qjournal"
    ".RequestInfoProto\"\030\n\026HeartbeatResponsePr"
    "oto\"{\n\033StartLogSegmentRequestProto\0227\n\007re"
    "qInfo\030\001 \002(\0132&.hadoop.hdfs.qjournal.Reque"
    "stInfoProto\022\014\n\004txid\030\002 \002(\004\022\025\n\rlayoutVersi"
    "on\030\003 \001(\021\"\036\n\034StartLogSegmentResponseProto"
    "\"}\n\036FinalizeLogSegmentRequestProto\0227\n\007re"
    "qInfo\030\001 \002(\0132&.hadoop.hdfs.qjournal.Reque"
    "stInfoProto\022\021\n\tstartTxId\030\002 \002(\004\022\017\n\007endTxI"
    "d\030\003 \002(\004\"!\n\037FinalizeLogSegmentResponsePro"
    "to\"g\n\025PurgeLogsRequestProto\0227\n\007reqInfo\030\001"
    " \002(\0132&.hadoop.hdfs.qjournal.RequestInfoP"
    "roto\022\025\n\rminTxIdToKeep\030\002 \002(\004\"\030\n\026PurgeLogs"
    "ResponseProto\"L\n\027IsFormattedRequestProto"
    "\0221\n\003jid\030\001 \002(\0132$.hadoop.hdfs.qjournal.Jou"
    "rnalIdProto\"/\n\030IsFormattedResponseProto\022"
    "\023\n\013isFormatted\030\001 \002(\010\"c\n\033DiscardSegmentsR"
    "equestProto\0221\n\003jid\030\001 \002(\0132$.hadoop.hdfs.q"
    "journal.JournalIdProto\022\021\n\tstartTxId\030\002 \002("
    "\004\"\036\n\034DiscardSegmentsResponseProto\"P\n\033Get"
    "JournalCTimeRequestProto\0221\n\003jid\030\001 \002(\0132$."
    "hadoop.hdfs.qjournal.JournalIdProto\"3\n\034G"
    "etJournalCTimeResponseProto\022\023\n\013resultCTi"
    "me\030\001 \002(\003\"M\n\030DoPreUpgradeRequestProto\0221\n\003"
    "jid\030\001 \002(\0132$.hadoop.hdfs.qjournal.Journal"
    "IdProto\"\033\n\031DoPreUpgradeResponseProto\"x\n\025"
    "DoUpgradeRequestProto\0221\n\003jid\030\001 \002(\0132$.had"
    "oop.hdfs.qjournal.JournalIdProto\022,\n\005sInf"
    "o\030\002 \002(\0132\035.hadoop.hdfs.StorageInfoProto\"\030"
    "\n\026DoUpgradeResponseProto\"K\n\026DoFinalizeRe"
    "questProto\0221\n\003jid\030\001 \002(\0132$.hadoop.hdfs.qj"
    "ournal.JournalIdProto\"\031\n\027DoFinalizeRespo"
    "nseProto\"\315\001\n\027CanRollBackRequestProto\0221\n\003"
    "jid\030\001 \002(\0132$.hadoop.hdfs.qjournal.Journal"
    "IdProto\022.\n\007storage\030\002 \002(\0132\035.hadoop.hdfs.S"
    "torageInfoProto\0222\n\013prevStorage\030\003 \002(\0132\035.h"
    "adoop.hdfs.StorageInfoProto\022\033\n\023targetLay"
    "outVersion\030\004 \002(\005\"/\n\030CanRollBackResponseP"
    "roto\022\023\n\013canRollBack\030\001 \002(\010\"K\n\026DoRollbackR"
    "equestProto\0221\n\003jid\030\001 \002(\0132$.hadoop.hdfs.q"
    "journal.JournalIdProto\"\031\n\027DoRollbackResp"
    "onseProto\"P\n\033GetJournalStateRequestProto"
    "\0221\n\003jid\030\001 \002(\0132$.hadoop.hdfs.qjournal.Jou"
    "rnalIdProto\"\\\n\034GetJournalStateResponsePr"
    "oto\022\031\n\021lastPromisedEpoch\030\001 \002(\004\022\020\n\010httpPo"
    "rt\030\002 \002(\r\022\017\n\007fromURL\030\003 \001(\t\"x\n\022FormatReque"
    "stProto\0221\n\003jid\030\001 \002(\0132$.hadoop.hdfs.qjour"
    "nal.JournalIdProto\022/\n\006nsInfo\030\002 \002(\0132\037.had"
    "oop.hdfs.NamespaceInfoProto\"\025\n\023FormatRes"
    "ponseProto\"\211\001\n\024NewEpochRequestProto\0221\n\003j"
    "id\030\001 \002(\0132$.hadoop.hdfs.qjournal.JournalI"
    "dProto\022/\n\006nsInfo\030\002 \002(\0132\037.hadoop.hdfs.Nam"
    "espaceInfoProto\022\r\n\005epoch\030\003 \002(\004\"0\n\025NewEpo"
    "chResponseProto\022\027\n\017lastSegmentTxId\030\001 \001(\004"
    "\"\203\001\n\036GetEditLogManifestRequestProto\0221\n\003j"
    "id\030\001 \002(\0132$.hadoop.hdfs.qjournal.JournalI"
    "dProto\022\021\n\tsinceTxId\030\002 \002(\004\022\033\n\014inProgressO"
    "k\030\004 \001(\010:\005false\"\177\n\037GetEditLogManifestResp"
    "onseProto\0229\n\010manifest\030\001 \002(\0132\'.hadoop.hdf"
    "s.RemoteEditLogManifestProto\022\020\n\010httpPort"
    "\030\002 \002(\r\022\017\n\007fromURL\030\003 \001(\t\"k\n\033PrepareRecove"
    "ryRequestProto\0227\n\007reqInfo\030\001 \002(\0132&.hadoop"
    ".hdfs.qjournal.RequestInfoProto\022\023\n\013segme"
    "ntTxId\030\002 \002(\004\"\252\001\n\034PrepareRecoveryResponse"
    "Proto\022=\n\014segmentState\030\001 \001(\0132\'.hadoop.hdf"
    "s.qjournal.SegmentStateProto\022\027\n\017accepted"
    "InEpoch\030\002 \001(\004\022\027\n\017lastWriterEpoch\030\003 \002(\004\022\031"
    "\n\021lastCommittedTxId\030\004 \001(\004\"\246\001\n\032AcceptReco"
    "veryRequestProto\0227\n\007reqInfo\030\001 \002(\0132&.hado"
    "op.hdfs.qjournal.RequestInfoProto\022>\n\rsta"
    "teToAccept\030\002 \002(\0132\'.hadoop.hdfs.qjournal."
    "SegmentStateProto\022\017\n\007fromURL\030\003 \002(\t\"\035\n\033Ac"
    "ceptRecoveryResponseProto2\373\020\n\027QJournalPr"
    "otocolService\022l\n\013isFormatted\022-.hadoop.hd"
    "fs.qjournal.IsFormattedRequestProto\032..ha"
    "doop.hdfs.qjournal.IsFormattedResponsePr"
    "oto\022x\n\017discardSegments\0221.hadoop.hdfs.qjo"
    "urnal.DiscardSegmentsRequestProto\0322.hado"
    "op.hdfs.qjournal.DiscardSegmentsResponse"
    "Proto\022x\n\017getJournalCTime\0221.hadoop.hdfs.q"
    "journal.GetJournalCTimeRequestProto\0322.ha"
    "doop.hdfs.qjournal.GetJournalCTimeRespon"
    "seProto\022o\n\014doPreUpgrade\022..hadoop.hdfs.qj"
    "ournal.DoPreUpgradeRequestProto\032/.hadoop"
    ".hdfs.qjournal.DoPreUpgradeResponseProto"
    "\022f\n\tdoUpgrade\022+.hadoop.hdfs.qjournal.DoU"
    "pgradeRequestProto\032,.hadoop.hdfs.qjourna"
    "l.DoUpgradeResponseProto\022i\n\ndoFinalize\022,"
    ".hadoop.hdfs.qjournal.DoFinalizeRequestP"
    "roto\032-.hadoop.hdfs.qjournal.DoFinalizeRe"
    "sponseProto\022l\n\013canRollBack\022-.hadoop.hdfs"
    ".qjournal.CanRollBackRequestProto\032..hado"
    "op.hdfs.qjournal.CanRollBackResponseProt"
    "o\022i\n\ndoRollback\022,.hadoop.hdfs.qjournal.D"
    "oRollbackRequestProto\032-.hadoop.hdfs.qjou"
    "rnal.DoRollbackResponseProto\022x\n\017getJourn"
    "alState\0221.hadoop.hdfs.qjournal.GetJourna"
    "lStateRequestProto\0322.hadoop.hdfs.qjourna"
    "l.GetJournalStateResponseProto\022c\n\010newEpo"
    "ch\022*.hadoop.hdfs.qjournal.NewEpochReques"
    "tProto\032+.hadoop.hdfs.qjournal.NewEpochRe"
    "sponseProto\022]\n\006format\022(.hadoop.hdfs.qjou"
    "rnal.FormatRequestProto\032).hadoop.hdfs.qj"
    "ournal.FormatResponseProto\022`\n\007journal\022)."
    "hadoop.hdfs.qjournal.JournalRequestProto"
    "\032*.hadoop.hdfs.qjournal.JournalResponseP"
    "roto\022f\n\theartbeat\022+.hadoop.hdfs.qjournal"
    ".HeartbeatRequestProto\032,.hadoop.hdfs.qjo"
    "urnal.HeartbeatResponseProto\022x\n\017startLog"
    "Segment\0221.hadoop.hdfs.qjournal.StartLogS"
    "egmentRequestProto\0322.hadoop.hdfs.qjourna"
    "l.StartLogSegmentResponseProto\022\201\001\n\022final"
    "izeLogSegment\0224.hadoop.hdfs.qjournal.Fin"
    "alizeLogSegmentRequestProto\0325.hadoop.hdf"
    "s.qjournal.FinalizeLogSegmentResponsePro"
    "to\022f\n\tpurgeLogs\022+.hadoop.hdfs.qjournal.P"
    "urgeLogsRequestProto\032,.hadoop.hdfs.qjour"
    "nal.PurgeLogsResponseProto\022\201\001\n\022getEditLo"
    "gManifest\0224.hadoop.hdfs.qjournal.GetEdit"
    "LogManifestRequestProto\0325.hadoop.hdfs.qj"
    "ournal.GetEditLogManifestResponseProto\022x"
    "\n\017prepareRecovery\0221.hadoop.hdfs.qjournal"
    ".PrepareRecoveryRequestProto\0322.hadoop.hd"
    "fs.qjournal.PrepareRecoveryResponseProto"
    "\022u\n\016acceptRecovery\0220.hadoop.hdfs.qjourna"
    "l.AcceptRecoveryRequestProto\0321.hadoop.hd"
    "fs.qjournal.AcceptRecoveryResponseProtoB"
    "H\n(org.apache.hadoop.hdfs.qjournal.proto"
    "colB\026QJournalProtocolProtos\210\001\001\240\001\001", 5793);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "QJournalProtocol.proto", &protobuf_RegisterTypes);
  JournalIdProto::default_instance_ = new JournalIdProto();
  RequestInfoProto::default_instance_ = new RequestInfoProto();
  SegmentStateProto::default_instance_ = new SegmentStateProto();
  PersistedRecoveryPaxosData::default_instance_ = new PersistedRecoveryPaxosData();
  JournalRequestProto::default_instance_ = new JournalRequestProto();
  JournalResponseProto::default_instance_ = new JournalResponseProto();
  HeartbeatRequestProto::default_instance_ = new HeartbeatRequestProto();
  HeartbeatResponseProto::default_instance_ = new HeartbeatResponseProto();
  StartLogSegmentRequestProto::default_instance_ = new StartLogSegmentRequestProto();
  StartLogSegmentResponseProto::default_instance_ = new StartLogSegmentResponseProto();
  FinalizeLogSegmentRequestProto::default_instance_ = new FinalizeLogSegmentRequestProto();
  FinalizeLogSegmentResponseProto::default_instance_ = new FinalizeLogSegmentResponseProto();
  PurgeLogsRequestProto::default_instance_ = new PurgeLogsRequestProto();
  PurgeLogsResponseProto::default_instance_ = new PurgeLogsResponseProto();
  IsFormattedRequestProto::default_instance_ = new IsFormattedRequestProto();
  IsFormattedResponseProto::default_instance_ = new IsFormattedResponseProto();
  DiscardSegmentsRequestProto::default_instance_ = new DiscardSegmentsRequestProto();
  DiscardSegmentsResponseProto::default_instance_ = new DiscardSegmentsResponseProto();
  GetJournalCTimeRequestProto::default_instance_ = new GetJournalCTimeRequestProto();
  GetJournalCTimeResponseProto::default_instance_ = new GetJournalCTimeResponseProto();
  DoPreUpgradeRequestProto::default_instance_ = new DoPreUpgradeRequestProto();
  DoPreUpgradeResponseProto::default_instance_ = new DoPreUpgradeResponseProto();
  DoUpgradeRequestProto::default_instance_ = new DoUpgradeRequestProto();
  DoUpgradeResponseProto::default_instance_ = new DoUpgradeResponseProto();
  DoFinalizeRequestProto::default_instance_ = new DoFinalizeRequestProto();
  DoFinalizeResponseProto::default_instance_ = new DoFinalizeResponseProto();
  CanRollBackRequestProto::default_instance_ = new CanRollBackRequestProto();
  CanRollBackResponseProto::default_instance_ = new CanRollBackResponseProto();
  DoRollbackRequestProto::default_instance_ = new DoRollbackRequestProto();
  DoRollbackResponseProto::default_instance_ = new DoRollbackResponseProto();
  GetJournalStateRequestProto::default_instance_ = new GetJournalStateRequestProto();
  GetJournalStateResponseProto::default_instance_ = new GetJournalStateResponseProto();
  FormatRequestProto::default_instance_ = new FormatRequestProto();
  FormatResponseProto::default_instance_ = new FormatResponseProto();
  NewEpochRequestProto::default_instance_ = new NewEpochRequestProto();
  NewEpochResponseProto::default_instance_ = new NewEpochResponseProto();
  GetEditLogManifestRequestProto::default_instance_ = new GetEditLogManifestRequestProto();
  GetEditLogManifestResponseProto::default_instance_ = new GetEditLogManifestResponseProto();
  PrepareRecoveryRequestProto::default_instance_ = new PrepareRecoveryRequestProto();
  PrepareRecoveryResponseProto::default_instance_ = new PrepareRecoveryResponseProto();
  AcceptRecoveryRequestProto::default_instance_ = new AcceptRecoveryRequestProto();
  AcceptRecoveryResponseProto::default_instance_ = new AcceptRecoveryResponseProto();
  JournalIdProto::default_instance_->InitAsDefaultInstance();
  RequestInfoProto::default_instance_->InitAsDefaultInstance();
  SegmentStateProto::default_instance_->InitAsDefaultInstance();
  PersistedRecoveryPaxosData::default_instance_->InitAsDefaultInstance();
  JournalRequestProto::default_instance_->InitAsDefaultInstance();
  JournalResponseProto::default_instance_->InitAsDefaultInstance();
  HeartbeatRequestProto::default_instance_->InitAsDefaultInstance();
  HeartbeatResponseProto::default_instance_->InitAsDefaultInstance();
  StartLogSegmentRequestProto::default_instance_->InitAsDefaultInstance();
  StartLogSegmentResponseProto::default_instance_->InitAsDefaultInstance();
  FinalizeLogSegmentRequestProto::default_instance_->InitAsDefaultInstance();
  FinalizeLogSegmentResponseProto::default_instance_->InitAsDefaultInstance();
  PurgeLogsRequestProto::default_instance_->InitAsDefaultInstance();
  PurgeLogsResponseProto::default_instance_->InitAsDefaultInstance();
  IsFormattedRequestProto::default_instance_->InitAsDefaultInstance();
  IsFormattedResponseProto::default_instance_->InitAsDefaultInstance();
  DiscardSegmentsRequestProto::default_instance_->InitAsDefaultInstance();
  DiscardSegmentsResponseProto::default_instance_->InitAsDefaultInstance();
  GetJournalCTimeRequestProto::default_instance_->InitAsDefaultInstance();
  GetJournalCTimeResponseProto::default_instance_->InitAsDefaultInstance();
  DoPreUpgradeRequestProto::default_instance_->InitAsDefaultInstance();
  DoPreUpgradeResponseProto::default_instance_->InitAsDefaultInstance();
  DoUpgradeRequestProto::default_instance_->InitAsDefaultInstance();
  DoUpgradeResponseProto::default_instance_->InitAsDefaultInstance();
  DoFinalizeRequestProto::default_instance_->InitAsDefaultInstance();
  DoFinalizeResponseProto::default_instance_->InitAsDefaultInstance();
  CanRollBackRequestProto::default_instance_->InitAsDefaultInstance();
  CanRollBackResponseProto::default_instance_->InitAsDefaultInstance();
  DoRollbackRequestProto::default_instance_->InitAsDefaultInstance();
  DoRollbackResponseProto::default_instance_->InitAsDefaultInstance();
  GetJournalStateRequestProto::default_instance_->InitAsDefaultInstance();
  GetJournalStateResponseProto::default_instance_->InitAsDefaultInstance();
  FormatRequestProto::default_instance_->InitAsDefaultInstance();
  FormatResponseProto::default_instance_->InitAsDefaultInstance();
  NewEpochRequestProto::default_instance_->InitAsDefaultInstance();
  NewEpochResponseProto::default_instance_->InitAsDefaultInstance();
  GetEditLogManifestRequestProto::default_instance_->InitAsDefaultInstance();
  GetEditLogManifestResponseProto::default_instance_->InitAsDefaultInstance();
  PrepareRecoveryRequestProto::default_instance_->InitAsDefaultInstance();
  PrepareRecoveryResponseProto::default_instance_->InitAsDefaultInstance();
  AcceptRecoveryRequestProto::default_instance_->InitAsDefaultInstance();
  AcceptRecoveryResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_QJournalProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_QJournalProtocol_2eproto {
  StaticDescriptorInitializer_QJournalProtocol_2eproto() {
    protobuf_AddDesc_QJournalProtocol_2eproto();
  }
} static_descriptor_initializer_QJournalProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int JournalIdProto::kIdentifierFieldNumber;
#endif  // !_MSC_VER

JournalIdProto::JournalIdProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void JournalIdProto::InitAsDefaultInstance() {
}

JournalIdProto::JournalIdProto(const JournalIdProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void JournalIdProto::SharedCtor() {
  _cached_size_ = 0;
  identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

JournalIdProto::~JournalIdProto() {
  SharedDtor();
}

void JournalIdProto::SharedDtor() {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    delete identifier_;
  }
  if (this != default_instance_) {
  }
}

void JournalIdProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* JournalIdProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return JournalIdProto_descriptor_;
}

const JournalIdProto& JournalIdProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

JournalIdProto* JournalIdProto::default_instance_ = NULL;

JournalIdProto* JournalIdProto::New() const {
  return new JournalIdProto;
}

void JournalIdProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_identifier()) {
      if (identifier_ != &::google::protobuf::internal::kEmptyString) {
        identifier_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool JournalIdProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string identifier = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_identifier()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->identifier().data(), this->identifier().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void JournalIdProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required string identifier = 1;
  if (has_identifier()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->identifier().data(), this->identifier().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->identifier(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* JournalIdProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required string identifier = 1;
  if (has_identifier()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->identifier().data(), this->identifier().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->identifier(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int JournalIdProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required string identifier = 1;
    if (has_identifier()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->identifier());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void JournalIdProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const JournalIdProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const JournalIdProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void JournalIdProto::MergeFrom(const JournalIdProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_identifier()) {
      set_identifier(from.identifier());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void JournalIdProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void JournalIdProto::CopyFrom(const JournalIdProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JournalIdProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void JournalIdProto::Swap(JournalIdProto* other) {
  if (other != this) {
    std::swap(identifier_, other->identifier_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata JournalIdProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = JournalIdProto_descriptor_;
  metadata.reflection = JournalIdProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RequestInfoProto::kJournalIdFieldNumber;
const int RequestInfoProto::kEpochFieldNumber;
const int RequestInfoProto::kIpcSerialNumberFieldNumber;
const int RequestInfoProto::kCommittedTxIdFieldNumber;
#endif  // !_MSC_VER

RequestInfoProto::RequestInfoProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RequestInfoProto::InitAsDefaultInstance() {
  journalid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

RequestInfoProto::RequestInfoProto(const RequestInfoProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RequestInfoProto::SharedCtor() {
  _cached_size_ = 0;
  journalid_ = NULL;
  epoch_ = GOOGLE_ULONGLONG(0);
  ipcserialnumber_ = GOOGLE_ULONGLONG(0);
  committedtxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RequestInfoProto::~RequestInfoProto() {
  SharedDtor();
}

void RequestInfoProto::SharedDtor() {
  if (this != default_instance_) {
    delete journalid_;
  }
}

void RequestInfoProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RequestInfoProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RequestInfoProto_descriptor_;
}

const RequestInfoProto& RequestInfoProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

RequestInfoProto* RequestInfoProto::default_instance_ = NULL;

RequestInfoProto* RequestInfoProto::New() const {
  return new RequestInfoProto;
}

void RequestInfoProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_journalid()) {
      if (journalid_ != NULL) journalid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    epoch_ = GOOGLE_ULONGLONG(0);
    ipcserialnumber_ = GOOGLE_ULONGLONG(0);
    committedtxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RequestInfoProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto journalId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_journalid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_epoch;
        break;
      }

      // required uint64 epoch = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_epoch:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &epoch_)));
          set_has_epoch();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_ipcSerialNumber;
        break;
      }

      // required uint64 ipcSerialNumber = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_ipcSerialNumber:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &ipcserialnumber_)));
          set_has_ipcserialnumber();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_committedTxId;
        break;
      }

      // optional uint64 committedTxId = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_committedTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &committedtxid_)));
          set_has_committedtxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RequestInfoProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto journalId = 1;
  if (has_journalid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->journalid(), output);
  }

  // required uint64 epoch = 2;
  if (has_epoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->epoch(), output);
  }

  // required uint64 ipcSerialNumber = 3;
  if (has_ipcserialnumber()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->ipcserialnumber(), output);
  }

  // optional uint64 committedTxId = 4;
  if (has_committedtxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->committedtxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RequestInfoProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto journalId = 1;
  if (has_journalid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->journalid(), target);
  }

  // required uint64 epoch = 2;
  if (has_epoch()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->epoch(), target);
  }

  // required uint64 ipcSerialNumber = 3;
  if (has_ipcserialnumber()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->ipcserialnumber(), target);
  }

  // optional uint64 committedTxId = 4;
  if (has_committedtxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->committedtxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RequestInfoProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto journalId = 1;
    if (has_journalid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->journalid());
    }

    // required uint64 epoch = 2;
    if (has_epoch()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->epoch());
    }

    // required uint64 ipcSerialNumber = 3;
    if (has_ipcserialnumber()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->ipcserialnumber());
    }

    // optional uint64 committedTxId = 4;
    if (has_committedtxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->committedtxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RequestInfoProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RequestInfoProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RequestInfoProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RequestInfoProto::MergeFrom(const RequestInfoProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_journalid()) {
      mutable_journalid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.journalid());
    }
    if (from.has_epoch()) {
      set_epoch(from.epoch());
    }
    if (from.has_ipcserialnumber()) {
      set_ipcserialnumber(from.ipcserialnumber());
    }
    if (from.has_committedtxid()) {
      set_committedtxid(from.committedtxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RequestInfoProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RequestInfoProto::CopyFrom(const RequestInfoProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RequestInfoProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_journalid()) {
    if (!this->journalid().IsInitialized()) return false;
  }
  return true;
}

void RequestInfoProto::Swap(RequestInfoProto* other) {
  if (other != this) {
    std::swap(journalid_, other->journalid_);
    std::swap(epoch_, other->epoch_);
    std::swap(ipcserialnumber_, other->ipcserialnumber_);
    std::swap(committedtxid_, other->committedtxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RequestInfoProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RequestInfoProto_descriptor_;
  metadata.reflection = RequestInfoProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int SegmentStateProto::kStartTxIdFieldNumber;
const int SegmentStateProto::kEndTxIdFieldNumber;
const int SegmentStateProto::kIsInProgressFieldNumber;
#endif  // !_MSC_VER

SegmentStateProto::SegmentStateProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void SegmentStateProto::InitAsDefaultInstance() {
}

SegmentStateProto::SegmentStateProto(const SegmentStateProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void SegmentStateProto::SharedCtor() {
  _cached_size_ = 0;
  starttxid_ = GOOGLE_ULONGLONG(0);
  endtxid_ = GOOGLE_ULONGLONG(0);
  isinprogress_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

SegmentStateProto::~SegmentStateProto() {
  SharedDtor();
}

void SegmentStateProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void SegmentStateProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SegmentStateProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SegmentStateProto_descriptor_;
}

const SegmentStateProto& SegmentStateProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

SegmentStateProto* SegmentStateProto::default_instance_ = NULL;

SegmentStateProto* SegmentStateProto::New() const {
  return new SegmentStateProto;
}

void SegmentStateProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    starttxid_ = GOOGLE_ULONGLONG(0);
    endtxid_ = GOOGLE_ULONGLONG(0);
    isinprogress_ = false;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool SegmentStateProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 startTxId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &starttxid_)));
          set_has_starttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_endTxId;
        break;
      }

      // required uint64 endTxId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_endTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &endtxid_)));
          set_has_endtxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_isInProgress;
        break;
      }

      // required bool isInProgress = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_isInProgress:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isinprogress_)));
          set_has_isinprogress();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void SegmentStateProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 startTxId = 1;
  if (has_starttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->starttxid(), output);
  }

  // required uint64 endTxId = 2;
  if (has_endtxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->endtxid(), output);
  }

  // required bool isInProgress = 3;
  if (has_isinprogress()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->isinprogress(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* SegmentStateProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 startTxId = 1;
  if (has_starttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->starttxid(), target);
  }

  // required uint64 endTxId = 2;
  if (has_endtxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->endtxid(), target);
  }

  // required bool isInProgress = 3;
  if (has_isinprogress()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->isinprogress(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int SegmentStateProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 startTxId = 1;
    if (has_starttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->starttxid());
    }

    // required uint64 endTxId = 2;
    if (has_endtxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->endtxid());
    }

    // required bool isInProgress = 3;
    if (has_isinprogress()) {
      total_size += 1 + 1;
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SegmentStateProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const SegmentStateProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const SegmentStateProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void SegmentStateProto::MergeFrom(const SegmentStateProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_starttxid()) {
      set_starttxid(from.starttxid());
    }
    if (from.has_endtxid()) {
      set_endtxid(from.endtxid());
    }
    if (from.has_isinprogress()) {
      set_isinprogress(from.isinprogress());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void SegmentStateProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SegmentStateProto::CopyFrom(const SegmentStateProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SegmentStateProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void SegmentStateProto::Swap(SegmentStateProto* other) {
  if (other != this) {
    std::swap(starttxid_, other->starttxid_);
    std::swap(endtxid_, other->endtxid_);
    std::swap(isinprogress_, other->isinprogress_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata SegmentStateProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SegmentStateProto_descriptor_;
  metadata.reflection = SegmentStateProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int PersistedRecoveryPaxosData::kSegmentStateFieldNumber;
const int PersistedRecoveryPaxosData::kAcceptedInEpochFieldNumber;
#endif  // !_MSC_VER

PersistedRecoveryPaxosData::PersistedRecoveryPaxosData()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PersistedRecoveryPaxosData::InitAsDefaultInstance() {
  segmentstate_ = const_cast< ::hadoop::hdfs::qjournal::SegmentStateProto*>(&::hadoop::hdfs::qjournal::SegmentStateProto::default_instance());
}

PersistedRecoveryPaxosData::PersistedRecoveryPaxosData(const PersistedRecoveryPaxosData& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PersistedRecoveryPaxosData::SharedCtor() {
  _cached_size_ = 0;
  segmentstate_ = NULL;
  acceptedinepoch_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PersistedRecoveryPaxosData::~PersistedRecoveryPaxosData() {
  SharedDtor();
}

void PersistedRecoveryPaxosData::SharedDtor() {
  if (this != default_instance_) {
    delete segmentstate_;
  }
}

void PersistedRecoveryPaxosData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PersistedRecoveryPaxosData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PersistedRecoveryPaxosData_descriptor_;
}

const PersistedRecoveryPaxosData& PersistedRecoveryPaxosData::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

PersistedRecoveryPaxosData* PersistedRecoveryPaxosData::default_instance_ = NULL;

PersistedRecoveryPaxosData* PersistedRecoveryPaxosData::New() const {
  return new PersistedRecoveryPaxosData;
}

void PersistedRecoveryPaxosData::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_segmentstate()) {
      if (segmentstate_ != NULL) segmentstate_->::hadoop::hdfs::qjournal::SegmentStateProto::Clear();
    }
    acceptedinepoch_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PersistedRecoveryPaxosData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_segmentstate()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_acceptedInEpoch;
        break;
      }

      // required uint64 acceptedInEpoch = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_acceptedInEpoch:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &acceptedinepoch_)));
          set_has_acceptedinepoch();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void PersistedRecoveryPaxosData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
  if (has_segmentstate()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->segmentstate(), output);
  }

  // required uint64 acceptedInEpoch = 2;
  if (has_acceptedinepoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->acceptedinepoch(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PersistedRecoveryPaxosData::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
  if (has_segmentstate()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->segmentstate(), target);
  }

  // required uint64 acceptedInEpoch = 2;
  if (has_acceptedinepoch()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->acceptedinepoch(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PersistedRecoveryPaxosData::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
    if (has_segmentstate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->segmentstate());
    }

    // required uint64 acceptedInEpoch = 2;
    if (has_acceptedinepoch()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->acceptedinepoch());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PersistedRecoveryPaxosData::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PersistedRecoveryPaxosData* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PersistedRecoveryPaxosData*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PersistedRecoveryPaxosData::MergeFrom(const PersistedRecoveryPaxosData& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_segmentstate()) {
      mutable_segmentstate()->::hadoop::hdfs::qjournal::SegmentStateProto::MergeFrom(from.segmentstate());
    }
    if (from.has_acceptedinepoch()) {
      set_acceptedinepoch(from.acceptedinepoch());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PersistedRecoveryPaxosData::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PersistedRecoveryPaxosData::CopyFrom(const PersistedRecoveryPaxosData& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PersistedRecoveryPaxosData::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_segmentstate()) {
    if (!this->segmentstate().IsInitialized()) return false;
  }
  return true;
}

void PersistedRecoveryPaxosData::Swap(PersistedRecoveryPaxosData* other) {
  if (other != this) {
    std::swap(segmentstate_, other->segmentstate_);
    std::swap(acceptedinepoch_, other->acceptedinepoch_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PersistedRecoveryPaxosData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PersistedRecoveryPaxosData_descriptor_;
  metadata.reflection = PersistedRecoveryPaxosData_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int JournalRequestProto::kReqInfoFieldNumber;
const int JournalRequestProto::kFirstTxnIdFieldNumber;
const int JournalRequestProto::kNumTxnsFieldNumber;
const int JournalRequestProto::kRecordsFieldNumber;
const int JournalRequestProto::kSegmentTxnIdFieldNumber;
#endif  // !_MSC_VER

JournalRequestProto::JournalRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void JournalRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
}

JournalRequestProto::JournalRequestProto(const JournalRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void JournalRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  firsttxnid_ = GOOGLE_ULONGLONG(0);
  numtxns_ = 0u;
  records_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  segmenttxnid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

JournalRequestProto::~JournalRequestProto() {
  SharedDtor();
}

void JournalRequestProto::SharedDtor() {
  if (records_ != &::google::protobuf::internal::kEmptyString) {
    delete records_;
  }
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void JournalRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* JournalRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return JournalRequestProto_descriptor_;
}

const JournalRequestProto& JournalRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

JournalRequestProto* JournalRequestProto::default_instance_ = NULL;

JournalRequestProto* JournalRequestProto::New() const {
  return new JournalRequestProto;
}

void JournalRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
    firsttxnid_ = GOOGLE_ULONGLONG(0);
    numtxns_ = 0u;
    if (has_records()) {
      if (records_ != &::google::protobuf::internal::kEmptyString) {
        records_->clear();
      }
    }
    segmenttxnid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool JournalRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_firstTxnId;
        break;
      }

      // required uint64 firstTxnId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_firstTxnId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &firsttxnid_)));
          set_has_firsttxnid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_numTxns;
        break;
      }

      // required uint32 numTxns = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_numTxns:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &numtxns_)));
          set_has_numtxns();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(34)) goto parse_records;
        break;
      }

      // required bytes records = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_records:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_records()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(40)) goto parse_segmentTxnId;
        break;
      }

      // required uint64 segmentTxnId = 5;
      case 5: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_segmentTxnId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &segmenttxnid_)));
          set_has_segmenttxnid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void JournalRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  // required uint64 firstTxnId = 2;
  if (has_firsttxnid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->firsttxnid(), output);
  }

  // required uint32 numTxns = 3;
  if (has_numtxns()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->numtxns(), output);
  }

  // required bytes records = 4;
  if (has_records()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      4, this->records(), output);
  }

  // required uint64 segmentTxnId = 5;
  if (has_segmenttxnid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(5, this->segmenttxnid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* JournalRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  // required uint64 firstTxnId = 2;
  if (has_firsttxnid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->firsttxnid(), target);
  }

  // required uint32 numTxns = 3;
  if (has_numtxns()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->numtxns(), target);
  }

  // required bytes records = 4;
  if (has_records()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->records(), target);
  }

  // required uint64 segmentTxnId = 5;
  if (has_segmenttxnid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(5, this->segmenttxnid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int JournalRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

    // required uint64 firstTxnId = 2;
    if (has_firsttxnid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->firsttxnid());
    }

    // required uint32 numTxns = 3;
    if (has_numtxns()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->numtxns());
    }

    // required bytes records = 4;
    if (has_records()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->records());
    }

    // required uint64 segmentTxnId = 5;
    if (has_segmenttxnid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->segmenttxnid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void JournalRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const JournalRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const JournalRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void JournalRequestProto::MergeFrom(const JournalRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
    if (from.has_firsttxnid()) {
      set_firsttxnid(from.firsttxnid());
    }
    if (from.has_numtxns()) {
      set_numtxns(from.numtxns());
    }
    if (from.has_records()) {
      set_records(from.records());
    }
    if (from.has_segmenttxnid()) {
      set_segmenttxnid(from.segmenttxnid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void JournalRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void JournalRequestProto::CopyFrom(const JournalRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JournalRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000001f) != 0x0000001f) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void JournalRequestProto::Swap(JournalRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(firsttxnid_, other->firsttxnid_);
    std::swap(numtxns_, other->numtxns_);
    std::swap(records_, other->records_);
    std::swap(segmenttxnid_, other->segmenttxnid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata JournalRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = JournalRequestProto_descriptor_;
  metadata.reflection = JournalRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

JournalResponseProto::JournalResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void JournalResponseProto::InitAsDefaultInstance() {
}

JournalResponseProto::JournalResponseProto(const JournalResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void JournalResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

JournalResponseProto::~JournalResponseProto() {
  SharedDtor();
}

void JournalResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void JournalResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* JournalResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return JournalResponseProto_descriptor_;
}

const JournalResponseProto& JournalResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

JournalResponseProto* JournalResponseProto::default_instance_ = NULL;

JournalResponseProto* JournalResponseProto::New() const {
  return new JournalResponseProto;
}

void JournalResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool JournalResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void JournalResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* JournalResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int JournalResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void JournalResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const JournalResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const JournalResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void JournalResponseProto::MergeFrom(const JournalResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void JournalResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void JournalResponseProto::CopyFrom(const JournalResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JournalResponseProto::IsInitialized() const {

  return true;
}

void JournalResponseProto::Swap(JournalResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata JournalResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = JournalResponseProto_descriptor_;
  metadata.reflection = JournalResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int HeartbeatRequestProto::kReqInfoFieldNumber;
#endif  // !_MSC_VER

HeartbeatRequestProto::HeartbeatRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void HeartbeatRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
}

HeartbeatRequestProto::HeartbeatRequestProto(const HeartbeatRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void HeartbeatRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

HeartbeatRequestProto::~HeartbeatRequestProto() {
  SharedDtor();
}

void HeartbeatRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void HeartbeatRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HeartbeatRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return HeartbeatRequestProto_descriptor_;
}

const HeartbeatRequestProto& HeartbeatRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

HeartbeatRequestProto* HeartbeatRequestProto::default_instance_ = NULL;

HeartbeatRequestProto* HeartbeatRequestProto::New() const {
  return new HeartbeatRequestProto;
}

void HeartbeatRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool HeartbeatRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void HeartbeatRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* HeartbeatRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int HeartbeatRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HeartbeatRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const HeartbeatRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const HeartbeatRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void HeartbeatRequestProto::MergeFrom(const HeartbeatRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void HeartbeatRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HeartbeatRequestProto::CopyFrom(const HeartbeatRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HeartbeatRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void HeartbeatRequestProto::Swap(HeartbeatRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata HeartbeatRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = HeartbeatRequestProto_descriptor_;
  metadata.reflection = HeartbeatRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

HeartbeatResponseProto::HeartbeatResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void HeartbeatResponseProto::InitAsDefaultInstance() {
}

HeartbeatResponseProto::HeartbeatResponseProto(const HeartbeatResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void HeartbeatResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

HeartbeatResponseProto::~HeartbeatResponseProto() {
  SharedDtor();
}

void HeartbeatResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void HeartbeatResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HeartbeatResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return HeartbeatResponseProto_descriptor_;
}

const HeartbeatResponseProto& HeartbeatResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

HeartbeatResponseProto* HeartbeatResponseProto::default_instance_ = NULL;

HeartbeatResponseProto* HeartbeatResponseProto::New() const {
  return new HeartbeatResponseProto;
}

void HeartbeatResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool HeartbeatResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void HeartbeatResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* HeartbeatResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int HeartbeatResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HeartbeatResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const HeartbeatResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const HeartbeatResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void HeartbeatResponseProto::MergeFrom(const HeartbeatResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void HeartbeatResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HeartbeatResponseProto::CopyFrom(const HeartbeatResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HeartbeatResponseProto::IsInitialized() const {

  return true;
}

void HeartbeatResponseProto::Swap(HeartbeatResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata HeartbeatResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = HeartbeatResponseProto_descriptor_;
  metadata.reflection = HeartbeatResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int StartLogSegmentRequestProto::kReqInfoFieldNumber;
const int StartLogSegmentRequestProto::kTxidFieldNumber;
const int StartLogSegmentRequestProto::kLayoutVersionFieldNumber;
#endif  // !_MSC_VER

StartLogSegmentRequestProto::StartLogSegmentRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void StartLogSegmentRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
}

StartLogSegmentRequestProto::StartLogSegmentRequestProto(const StartLogSegmentRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void StartLogSegmentRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  txid_ = GOOGLE_ULONGLONG(0);
  layoutversion_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

StartLogSegmentRequestProto::~StartLogSegmentRequestProto() {
  SharedDtor();
}

void StartLogSegmentRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void StartLogSegmentRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StartLogSegmentRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StartLogSegmentRequestProto_descriptor_;
}

const StartLogSegmentRequestProto& StartLogSegmentRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

StartLogSegmentRequestProto* StartLogSegmentRequestProto::default_instance_ = NULL;

StartLogSegmentRequestProto* StartLogSegmentRequestProto::New() const {
  return new StartLogSegmentRequestProto;
}

void StartLogSegmentRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
    txid_ = GOOGLE_ULONGLONG(0);
    layoutversion_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool StartLogSegmentRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_txid;
        break;
      }

      // required uint64 txid = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_txid:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &txid_)));
          set_has_txid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_layoutVersion;
        break;
      }

      // optional sint32 layoutVersion = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_layoutVersion:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &layoutversion_)));
          set_has_layoutversion();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void StartLogSegmentRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  // required uint64 txid = 2;
  if (has_txid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->txid(), output);
  }

  // optional sint32 layoutVersion = 3;
  if (has_layoutversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(3, this->layoutversion(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* StartLogSegmentRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  // required uint64 txid = 2;
  if (has_txid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->txid(), target);
  }

  // optional sint32 layoutVersion = 3;
  if (has_layoutversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(3, this->layoutversion(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int StartLogSegmentRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

    // required uint64 txid = 2;
    if (has_txid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->txid());
    }

    // optional sint32 layoutVersion = 3;
    if (has_layoutversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->layoutversion());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StartLogSegmentRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const StartLogSegmentRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const StartLogSegmentRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void StartLogSegmentRequestProto::MergeFrom(const StartLogSegmentRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
    if (from.has_txid()) {
      set_txid(from.txid());
    }
    if (from.has_layoutversion()) {
      set_layoutversion(from.layoutversion());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void StartLogSegmentRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StartLogSegmentRequestProto::CopyFrom(const StartLogSegmentRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartLogSegmentRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void StartLogSegmentRequestProto::Swap(StartLogSegmentRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(txid_, other->txid_);
    std::swap(layoutversion_, other->layoutversion_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata StartLogSegmentRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StartLogSegmentRequestProto_descriptor_;
  metadata.reflection = StartLogSegmentRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

StartLogSegmentResponseProto::StartLogSegmentResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void StartLogSegmentResponseProto::InitAsDefaultInstance() {
}

StartLogSegmentResponseProto::StartLogSegmentResponseProto(const StartLogSegmentResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void StartLogSegmentResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

StartLogSegmentResponseProto::~StartLogSegmentResponseProto() {
  SharedDtor();
}

void StartLogSegmentResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void StartLogSegmentResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StartLogSegmentResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StartLogSegmentResponseProto_descriptor_;
}

const StartLogSegmentResponseProto& StartLogSegmentResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

StartLogSegmentResponseProto* StartLogSegmentResponseProto::default_instance_ = NULL;

StartLogSegmentResponseProto* StartLogSegmentResponseProto::New() const {
  return new StartLogSegmentResponseProto;
}

void StartLogSegmentResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool StartLogSegmentResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void StartLogSegmentResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* StartLogSegmentResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int StartLogSegmentResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StartLogSegmentResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const StartLogSegmentResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const StartLogSegmentResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void StartLogSegmentResponseProto::MergeFrom(const StartLogSegmentResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void StartLogSegmentResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StartLogSegmentResponseProto::CopyFrom(const StartLogSegmentResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartLogSegmentResponseProto::IsInitialized() const {

  return true;
}

void StartLogSegmentResponseProto::Swap(StartLogSegmentResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata StartLogSegmentResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StartLogSegmentResponseProto_descriptor_;
  metadata.reflection = StartLogSegmentResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int FinalizeLogSegmentRequestProto::kReqInfoFieldNumber;
const int FinalizeLogSegmentRequestProto::kStartTxIdFieldNumber;
const int FinalizeLogSegmentRequestProto::kEndTxIdFieldNumber;
#endif  // !_MSC_VER

FinalizeLogSegmentRequestProto::FinalizeLogSegmentRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FinalizeLogSegmentRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
}

FinalizeLogSegmentRequestProto::FinalizeLogSegmentRequestProto(const FinalizeLogSegmentRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FinalizeLogSegmentRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  starttxid_ = GOOGLE_ULONGLONG(0);
  endtxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FinalizeLogSegmentRequestProto::~FinalizeLogSegmentRequestProto() {
  SharedDtor();
}

void FinalizeLogSegmentRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void FinalizeLogSegmentRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FinalizeLogSegmentRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FinalizeLogSegmentRequestProto_descriptor_;
}

const FinalizeLogSegmentRequestProto& FinalizeLogSegmentRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

FinalizeLogSegmentRequestProto* FinalizeLogSegmentRequestProto::default_instance_ = NULL;

FinalizeLogSegmentRequestProto* FinalizeLogSegmentRequestProto::New() const {
  return new FinalizeLogSegmentRequestProto;
}

void FinalizeLogSegmentRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
    starttxid_ = GOOGLE_ULONGLONG(0);
    endtxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FinalizeLogSegmentRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_startTxId;
        break;
      }

      // required uint64 startTxId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_startTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &starttxid_)));
          set_has_starttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_endTxId;
        break;
      }

      // required uint64 endTxId = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_endTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &endtxid_)));
          set_has_endtxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void FinalizeLogSegmentRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  // required uint64 startTxId = 2;
  if (has_starttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->starttxid(), output);
  }

  // required uint64 endTxId = 3;
  if (has_endtxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->endtxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FinalizeLogSegmentRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  // required uint64 startTxId = 2;
  if (has_starttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->starttxid(), target);
  }

  // required uint64 endTxId = 3;
  if (has_endtxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->endtxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FinalizeLogSegmentRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

    // required uint64 startTxId = 2;
    if (has_starttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->starttxid());
    }

    // required uint64 endTxId = 3;
    if (has_endtxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->endtxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FinalizeLogSegmentRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FinalizeLogSegmentRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FinalizeLogSegmentRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FinalizeLogSegmentRequestProto::MergeFrom(const FinalizeLogSegmentRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
    if (from.has_starttxid()) {
      set_starttxid(from.starttxid());
    }
    if (from.has_endtxid()) {
      set_endtxid(from.endtxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FinalizeLogSegmentRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FinalizeLogSegmentRequestProto::CopyFrom(const FinalizeLogSegmentRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FinalizeLogSegmentRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void FinalizeLogSegmentRequestProto::Swap(FinalizeLogSegmentRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(starttxid_, other->starttxid_);
    std::swap(endtxid_, other->endtxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FinalizeLogSegmentRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FinalizeLogSegmentRequestProto_descriptor_;
  metadata.reflection = FinalizeLogSegmentRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

FinalizeLogSegmentResponseProto::FinalizeLogSegmentResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FinalizeLogSegmentResponseProto::InitAsDefaultInstance() {
}

FinalizeLogSegmentResponseProto::FinalizeLogSegmentResponseProto(const FinalizeLogSegmentResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FinalizeLogSegmentResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FinalizeLogSegmentResponseProto::~FinalizeLogSegmentResponseProto() {
  SharedDtor();
}

void FinalizeLogSegmentResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void FinalizeLogSegmentResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FinalizeLogSegmentResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FinalizeLogSegmentResponseProto_descriptor_;
}

const FinalizeLogSegmentResponseProto& FinalizeLogSegmentResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

FinalizeLogSegmentResponseProto* FinalizeLogSegmentResponseProto::default_instance_ = NULL;

FinalizeLogSegmentResponseProto* FinalizeLogSegmentResponseProto::New() const {
  return new FinalizeLogSegmentResponseProto;
}

void FinalizeLogSegmentResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FinalizeLogSegmentResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void FinalizeLogSegmentResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FinalizeLogSegmentResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FinalizeLogSegmentResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FinalizeLogSegmentResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FinalizeLogSegmentResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FinalizeLogSegmentResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FinalizeLogSegmentResponseProto::MergeFrom(const FinalizeLogSegmentResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FinalizeLogSegmentResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FinalizeLogSegmentResponseProto::CopyFrom(const FinalizeLogSegmentResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FinalizeLogSegmentResponseProto::IsInitialized() const {

  return true;
}

void FinalizeLogSegmentResponseProto::Swap(FinalizeLogSegmentResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FinalizeLogSegmentResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FinalizeLogSegmentResponseProto_descriptor_;
  metadata.reflection = FinalizeLogSegmentResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int PurgeLogsRequestProto::kReqInfoFieldNumber;
const int PurgeLogsRequestProto::kMinTxIdToKeepFieldNumber;
#endif  // !_MSC_VER

PurgeLogsRequestProto::PurgeLogsRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PurgeLogsRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
}

PurgeLogsRequestProto::PurgeLogsRequestProto(const PurgeLogsRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PurgeLogsRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  mintxidtokeep_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PurgeLogsRequestProto::~PurgeLogsRequestProto() {
  SharedDtor();
}

void PurgeLogsRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void PurgeLogsRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PurgeLogsRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PurgeLogsRequestProto_descriptor_;
}

const PurgeLogsRequestProto& PurgeLogsRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

PurgeLogsRequestProto* PurgeLogsRequestProto::default_instance_ = NULL;

PurgeLogsRequestProto* PurgeLogsRequestProto::New() const {
  return new PurgeLogsRequestProto;
}

void PurgeLogsRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
    mintxidtokeep_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PurgeLogsRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_minTxIdToKeep;
        break;
      }

      // required uint64 minTxIdToKeep = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_minTxIdToKeep:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &mintxidtokeep_)));
          set_has_mintxidtokeep();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void PurgeLogsRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  // required uint64 minTxIdToKeep = 2;
  if (has_mintxidtokeep()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->mintxidtokeep(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PurgeLogsRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  // required uint64 minTxIdToKeep = 2;
  if (has_mintxidtokeep()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->mintxidtokeep(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PurgeLogsRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

    // required uint64 minTxIdToKeep = 2;
    if (has_mintxidtokeep()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->mintxidtokeep());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PurgeLogsRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PurgeLogsRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PurgeLogsRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PurgeLogsRequestProto::MergeFrom(const PurgeLogsRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
    if (from.has_mintxidtokeep()) {
      set_mintxidtokeep(from.mintxidtokeep());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PurgeLogsRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PurgeLogsRequestProto::CopyFrom(const PurgeLogsRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PurgeLogsRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void PurgeLogsRequestProto::Swap(PurgeLogsRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(mintxidtokeep_, other->mintxidtokeep_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PurgeLogsRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PurgeLogsRequestProto_descriptor_;
  metadata.reflection = PurgeLogsRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

PurgeLogsResponseProto::PurgeLogsResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PurgeLogsResponseProto::InitAsDefaultInstance() {
}

PurgeLogsResponseProto::PurgeLogsResponseProto(const PurgeLogsResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PurgeLogsResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PurgeLogsResponseProto::~PurgeLogsResponseProto() {
  SharedDtor();
}

void PurgeLogsResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void PurgeLogsResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PurgeLogsResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PurgeLogsResponseProto_descriptor_;
}

const PurgeLogsResponseProto& PurgeLogsResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

PurgeLogsResponseProto* PurgeLogsResponseProto::default_instance_ = NULL;

PurgeLogsResponseProto* PurgeLogsResponseProto::New() const {
  return new PurgeLogsResponseProto;
}

void PurgeLogsResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PurgeLogsResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void PurgeLogsResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PurgeLogsResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PurgeLogsResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PurgeLogsResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PurgeLogsResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PurgeLogsResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PurgeLogsResponseProto::MergeFrom(const PurgeLogsResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PurgeLogsResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PurgeLogsResponseProto::CopyFrom(const PurgeLogsResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PurgeLogsResponseProto::IsInitialized() const {

  return true;
}

void PurgeLogsResponseProto::Swap(PurgeLogsResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PurgeLogsResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PurgeLogsResponseProto_descriptor_;
  metadata.reflection = PurgeLogsResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int IsFormattedRequestProto::kJidFieldNumber;
#endif  // !_MSC_VER

IsFormattedRequestProto::IsFormattedRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void IsFormattedRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

IsFormattedRequestProto::IsFormattedRequestProto(const IsFormattedRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void IsFormattedRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

IsFormattedRequestProto::~IsFormattedRequestProto() {
  SharedDtor();
}

void IsFormattedRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void IsFormattedRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* IsFormattedRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return IsFormattedRequestProto_descriptor_;
}

const IsFormattedRequestProto& IsFormattedRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

IsFormattedRequestProto* IsFormattedRequestProto::default_instance_ = NULL;

IsFormattedRequestProto* IsFormattedRequestProto::New() const {
  return new IsFormattedRequestProto;
}

void IsFormattedRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool IsFormattedRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void IsFormattedRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* IsFormattedRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int IsFormattedRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void IsFormattedRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const IsFormattedRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const IsFormattedRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void IsFormattedRequestProto::MergeFrom(const IsFormattedRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void IsFormattedRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void IsFormattedRequestProto::CopyFrom(const IsFormattedRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IsFormattedRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void IsFormattedRequestProto::Swap(IsFormattedRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata IsFormattedRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = IsFormattedRequestProto_descriptor_;
  metadata.reflection = IsFormattedRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int IsFormattedResponseProto::kIsFormattedFieldNumber;
#endif  // !_MSC_VER

IsFormattedResponseProto::IsFormattedResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void IsFormattedResponseProto::InitAsDefaultInstance() {
}

IsFormattedResponseProto::IsFormattedResponseProto(const IsFormattedResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void IsFormattedResponseProto::SharedCtor() {
  _cached_size_ = 0;
  isformatted_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

IsFormattedResponseProto::~IsFormattedResponseProto() {
  SharedDtor();
}

void IsFormattedResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void IsFormattedResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* IsFormattedResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return IsFormattedResponseProto_descriptor_;
}

const IsFormattedResponseProto& IsFormattedResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

IsFormattedResponseProto* IsFormattedResponseProto::default_instance_ = NULL;

IsFormattedResponseProto* IsFormattedResponseProto::New() const {
  return new IsFormattedResponseProto;
}

void IsFormattedResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    isformatted_ = false;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool IsFormattedResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required bool isFormatted = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isformatted_)));
          set_has_isformatted();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void IsFormattedResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required bool isFormatted = 1;
  if (has_isformatted()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->isformatted(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* IsFormattedResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required bool isFormatted = 1;
  if (has_isformatted()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->isformatted(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int IsFormattedResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required bool isFormatted = 1;
    if (has_isformatted()) {
      total_size += 1 + 1;
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void IsFormattedResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const IsFormattedResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const IsFormattedResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void IsFormattedResponseProto::MergeFrom(const IsFormattedResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_isformatted()) {
      set_isformatted(from.isformatted());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void IsFormattedResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void IsFormattedResponseProto::CopyFrom(const IsFormattedResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IsFormattedResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void IsFormattedResponseProto::Swap(IsFormattedResponseProto* other) {
  if (other != this) {
    std::swap(isformatted_, other->isformatted_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata IsFormattedResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = IsFormattedResponseProto_descriptor_;
  metadata.reflection = IsFormattedResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DiscardSegmentsRequestProto::kJidFieldNumber;
const int DiscardSegmentsRequestProto::kStartTxIdFieldNumber;
#endif  // !_MSC_VER

DiscardSegmentsRequestProto::DiscardSegmentsRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DiscardSegmentsRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

DiscardSegmentsRequestProto::DiscardSegmentsRequestProto(const DiscardSegmentsRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DiscardSegmentsRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  starttxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DiscardSegmentsRequestProto::~DiscardSegmentsRequestProto() {
  SharedDtor();
}

void DiscardSegmentsRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void DiscardSegmentsRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DiscardSegmentsRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DiscardSegmentsRequestProto_descriptor_;
}

const DiscardSegmentsRequestProto& DiscardSegmentsRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DiscardSegmentsRequestProto* DiscardSegmentsRequestProto::default_instance_ = NULL;

DiscardSegmentsRequestProto* DiscardSegmentsRequestProto::New() const {
  return new DiscardSegmentsRequestProto;
}

void DiscardSegmentsRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    starttxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DiscardSegmentsRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_startTxId;
        break;
      }

      // required uint64 startTxId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_startTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &starttxid_)));
          set_has_starttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DiscardSegmentsRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  // required uint64 startTxId = 2;
  if (has_starttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->starttxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DiscardSegmentsRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  // required uint64 startTxId = 2;
  if (has_starttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->starttxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DiscardSegmentsRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

    // required uint64 startTxId = 2;
    if (has_starttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->starttxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DiscardSegmentsRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DiscardSegmentsRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DiscardSegmentsRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DiscardSegmentsRequestProto::MergeFrom(const DiscardSegmentsRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
    if (from.has_starttxid()) {
      set_starttxid(from.starttxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DiscardSegmentsRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DiscardSegmentsRequestProto::CopyFrom(const DiscardSegmentsRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DiscardSegmentsRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void DiscardSegmentsRequestProto::Swap(DiscardSegmentsRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(starttxid_, other->starttxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DiscardSegmentsRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DiscardSegmentsRequestProto_descriptor_;
  metadata.reflection = DiscardSegmentsRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

DiscardSegmentsResponseProto::DiscardSegmentsResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DiscardSegmentsResponseProto::InitAsDefaultInstance() {
}

DiscardSegmentsResponseProto::DiscardSegmentsResponseProto(const DiscardSegmentsResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DiscardSegmentsResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DiscardSegmentsResponseProto::~DiscardSegmentsResponseProto() {
  SharedDtor();
}

void DiscardSegmentsResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DiscardSegmentsResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DiscardSegmentsResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DiscardSegmentsResponseProto_descriptor_;
}

const DiscardSegmentsResponseProto& DiscardSegmentsResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DiscardSegmentsResponseProto* DiscardSegmentsResponseProto::default_instance_ = NULL;

DiscardSegmentsResponseProto* DiscardSegmentsResponseProto::New() const {
  return new DiscardSegmentsResponseProto;
}

void DiscardSegmentsResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DiscardSegmentsResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void DiscardSegmentsResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DiscardSegmentsResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DiscardSegmentsResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DiscardSegmentsResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DiscardSegmentsResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DiscardSegmentsResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DiscardSegmentsResponseProto::MergeFrom(const DiscardSegmentsResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DiscardSegmentsResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DiscardSegmentsResponseProto::CopyFrom(const DiscardSegmentsResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DiscardSegmentsResponseProto::IsInitialized() const {

  return true;
}

void DiscardSegmentsResponseProto::Swap(DiscardSegmentsResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DiscardSegmentsResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DiscardSegmentsResponseProto_descriptor_;
  metadata.reflection = DiscardSegmentsResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetJournalCTimeRequestProto::kJidFieldNumber;
#endif  // !_MSC_VER

GetJournalCTimeRequestProto::GetJournalCTimeRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetJournalCTimeRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

GetJournalCTimeRequestProto::GetJournalCTimeRequestProto(const GetJournalCTimeRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetJournalCTimeRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetJournalCTimeRequestProto::~GetJournalCTimeRequestProto() {
  SharedDtor();
}

void GetJournalCTimeRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void GetJournalCTimeRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetJournalCTimeRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetJournalCTimeRequestProto_descriptor_;
}

const GetJournalCTimeRequestProto& GetJournalCTimeRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

GetJournalCTimeRequestProto* GetJournalCTimeRequestProto::default_instance_ = NULL;

GetJournalCTimeRequestProto* GetJournalCTimeRequestProto::New() const {
  return new GetJournalCTimeRequestProto;
}

void GetJournalCTimeRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetJournalCTimeRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetJournalCTimeRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetJournalCTimeRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetJournalCTimeRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetJournalCTimeRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetJournalCTimeRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetJournalCTimeRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetJournalCTimeRequestProto::MergeFrom(const GetJournalCTimeRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetJournalCTimeRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetJournalCTimeRequestProto::CopyFrom(const GetJournalCTimeRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJournalCTimeRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void GetJournalCTimeRequestProto::Swap(GetJournalCTimeRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetJournalCTimeRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetJournalCTimeRequestProto_descriptor_;
  metadata.reflection = GetJournalCTimeRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetJournalCTimeResponseProto::kResultCTimeFieldNumber;
#endif  // !_MSC_VER

GetJournalCTimeResponseProto::GetJournalCTimeResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetJournalCTimeResponseProto::InitAsDefaultInstance() {
}

GetJournalCTimeResponseProto::GetJournalCTimeResponseProto(const GetJournalCTimeResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetJournalCTimeResponseProto::SharedCtor() {
  _cached_size_ = 0;
  resultctime_ = GOOGLE_LONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetJournalCTimeResponseProto::~GetJournalCTimeResponseProto() {
  SharedDtor();
}

void GetJournalCTimeResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetJournalCTimeResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetJournalCTimeResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetJournalCTimeResponseProto_descriptor_;
}

const GetJournalCTimeResponseProto& GetJournalCTimeResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

GetJournalCTimeResponseProto* GetJournalCTimeResponseProto::default_instance_ = NULL;

GetJournalCTimeResponseProto* GetJournalCTimeResponseProto::New() const {
  return new GetJournalCTimeResponseProto;
}

void GetJournalCTimeResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    resultctime_ = GOOGLE_LONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetJournalCTimeResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int64 resultCTime = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &resultctime_)));
          set_has_resultctime();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetJournalCTimeResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required int64 resultCTime = 1;
  if (has_resultctime()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->resultctime(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetJournalCTimeResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required int64 resultCTime = 1;
  if (has_resultctime()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->resultctime(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetJournalCTimeResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required int64 resultCTime = 1;
    if (has_resultctime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->resultctime());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetJournalCTimeResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetJournalCTimeResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetJournalCTimeResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetJournalCTimeResponseProto::MergeFrom(const GetJournalCTimeResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_resultctime()) {
      set_resultctime(from.resultctime());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetJournalCTimeResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetJournalCTimeResponseProto::CopyFrom(const GetJournalCTimeResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJournalCTimeResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetJournalCTimeResponseProto::Swap(GetJournalCTimeResponseProto* other) {
  if (other != this) {
    std::swap(resultctime_, other->resultctime_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetJournalCTimeResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetJournalCTimeResponseProto_descriptor_;
  metadata.reflection = GetJournalCTimeResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DoPreUpgradeRequestProto::kJidFieldNumber;
#endif  // !_MSC_VER

DoPreUpgradeRequestProto::DoPreUpgradeRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoPreUpgradeRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

DoPreUpgradeRequestProto::DoPreUpgradeRequestProto(const DoPreUpgradeRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoPreUpgradeRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoPreUpgradeRequestProto::~DoPreUpgradeRequestProto() {
  SharedDtor();
}

void DoPreUpgradeRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void DoPreUpgradeRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoPreUpgradeRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoPreUpgradeRequestProto_descriptor_;
}

const DoPreUpgradeRequestProto& DoPreUpgradeRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoPreUpgradeRequestProto* DoPreUpgradeRequestProto::default_instance_ = NULL;

DoPreUpgradeRequestProto* DoPreUpgradeRequestProto::New() const {
  return new DoPreUpgradeRequestProto;
}

void DoPreUpgradeRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoPreUpgradeRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DoPreUpgradeRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoPreUpgradeRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoPreUpgradeRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoPreUpgradeRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoPreUpgradeRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoPreUpgradeRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoPreUpgradeRequestProto::MergeFrom(const DoPreUpgradeRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoPreUpgradeRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoPreUpgradeRequestProto::CopyFrom(const DoPreUpgradeRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoPreUpgradeRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void DoPreUpgradeRequestProto::Swap(DoPreUpgradeRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoPreUpgradeRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoPreUpgradeRequestProto_descriptor_;
  metadata.reflection = DoPreUpgradeRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

DoPreUpgradeResponseProto::DoPreUpgradeResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoPreUpgradeResponseProto::InitAsDefaultInstance() {
}

DoPreUpgradeResponseProto::DoPreUpgradeResponseProto(const DoPreUpgradeResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoPreUpgradeResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoPreUpgradeResponseProto::~DoPreUpgradeResponseProto() {
  SharedDtor();
}

void DoPreUpgradeResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DoPreUpgradeResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoPreUpgradeResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoPreUpgradeResponseProto_descriptor_;
}

const DoPreUpgradeResponseProto& DoPreUpgradeResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoPreUpgradeResponseProto* DoPreUpgradeResponseProto::default_instance_ = NULL;

DoPreUpgradeResponseProto* DoPreUpgradeResponseProto::New() const {
  return new DoPreUpgradeResponseProto;
}

void DoPreUpgradeResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoPreUpgradeResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void DoPreUpgradeResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoPreUpgradeResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoPreUpgradeResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoPreUpgradeResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoPreUpgradeResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoPreUpgradeResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoPreUpgradeResponseProto::MergeFrom(const DoPreUpgradeResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoPreUpgradeResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoPreUpgradeResponseProto::CopyFrom(const DoPreUpgradeResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoPreUpgradeResponseProto::IsInitialized() const {

  return true;
}

void DoPreUpgradeResponseProto::Swap(DoPreUpgradeResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoPreUpgradeResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoPreUpgradeResponseProto_descriptor_;
  metadata.reflection = DoPreUpgradeResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DoUpgradeRequestProto::kJidFieldNumber;
const int DoUpgradeRequestProto::kSInfoFieldNumber;
#endif  // !_MSC_VER

DoUpgradeRequestProto::DoUpgradeRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoUpgradeRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
  sinfo_ = const_cast< ::hadoop::hdfs::StorageInfoProto*>(&::hadoop::hdfs::StorageInfoProto::default_instance());
}

DoUpgradeRequestProto::DoUpgradeRequestProto(const DoUpgradeRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoUpgradeRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  sinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoUpgradeRequestProto::~DoUpgradeRequestProto() {
  SharedDtor();
}

void DoUpgradeRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
    delete sinfo_;
  }
}

void DoUpgradeRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoUpgradeRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoUpgradeRequestProto_descriptor_;
}

const DoUpgradeRequestProto& DoUpgradeRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoUpgradeRequestProto* DoUpgradeRequestProto::default_instance_ = NULL;

DoUpgradeRequestProto* DoUpgradeRequestProto::New() const {
  return new DoUpgradeRequestProto;
}

void DoUpgradeRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    if (has_sinfo()) {
      if (sinfo_ != NULL) sinfo_->::hadoop::hdfs::StorageInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoUpgradeRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_sInfo;
        break;
      }

      // required .hadoop.hdfs.StorageInfoProto sInfo = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_sInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_sinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DoUpgradeRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  // required .hadoop.hdfs.StorageInfoProto sInfo = 2;
  if (has_sinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->sinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoUpgradeRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  // required .hadoop.hdfs.StorageInfoProto sInfo = 2;
  if (has_sinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->sinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoUpgradeRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

    // required .hadoop.hdfs.StorageInfoProto sInfo = 2;
    if (has_sinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->sinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoUpgradeRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoUpgradeRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoUpgradeRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoUpgradeRequestProto::MergeFrom(const DoUpgradeRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
    if (from.has_sinfo()) {
      mutable_sinfo()->::hadoop::hdfs::StorageInfoProto::MergeFrom(from.sinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoUpgradeRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoUpgradeRequestProto::CopyFrom(const DoUpgradeRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoUpgradeRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  if (has_sinfo()) {
    if (!this->sinfo().IsInitialized()) return false;
  }
  return true;
}

void DoUpgradeRequestProto::Swap(DoUpgradeRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(sinfo_, other->sinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoUpgradeRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoUpgradeRequestProto_descriptor_;
  metadata.reflection = DoUpgradeRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

DoUpgradeResponseProto::DoUpgradeResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoUpgradeResponseProto::InitAsDefaultInstance() {
}

DoUpgradeResponseProto::DoUpgradeResponseProto(const DoUpgradeResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoUpgradeResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoUpgradeResponseProto::~DoUpgradeResponseProto() {
  SharedDtor();
}

void DoUpgradeResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DoUpgradeResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoUpgradeResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoUpgradeResponseProto_descriptor_;
}

const DoUpgradeResponseProto& DoUpgradeResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoUpgradeResponseProto* DoUpgradeResponseProto::default_instance_ = NULL;

DoUpgradeResponseProto* DoUpgradeResponseProto::New() const {
  return new DoUpgradeResponseProto;
}

void DoUpgradeResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoUpgradeResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void DoUpgradeResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoUpgradeResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoUpgradeResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoUpgradeResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoUpgradeResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoUpgradeResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoUpgradeResponseProto::MergeFrom(const DoUpgradeResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoUpgradeResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoUpgradeResponseProto::CopyFrom(const DoUpgradeResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoUpgradeResponseProto::IsInitialized() const {

  return true;
}

void DoUpgradeResponseProto::Swap(DoUpgradeResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoUpgradeResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoUpgradeResponseProto_descriptor_;
  metadata.reflection = DoUpgradeResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DoFinalizeRequestProto::kJidFieldNumber;
#endif  // !_MSC_VER

DoFinalizeRequestProto::DoFinalizeRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoFinalizeRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

DoFinalizeRequestProto::DoFinalizeRequestProto(const DoFinalizeRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoFinalizeRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoFinalizeRequestProto::~DoFinalizeRequestProto() {
  SharedDtor();
}

void DoFinalizeRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void DoFinalizeRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoFinalizeRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoFinalizeRequestProto_descriptor_;
}

const DoFinalizeRequestProto& DoFinalizeRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoFinalizeRequestProto* DoFinalizeRequestProto::default_instance_ = NULL;

DoFinalizeRequestProto* DoFinalizeRequestProto::New() const {
  return new DoFinalizeRequestProto;
}

void DoFinalizeRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoFinalizeRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DoFinalizeRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoFinalizeRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoFinalizeRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoFinalizeRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoFinalizeRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoFinalizeRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoFinalizeRequestProto::MergeFrom(const DoFinalizeRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoFinalizeRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoFinalizeRequestProto::CopyFrom(const DoFinalizeRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoFinalizeRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void DoFinalizeRequestProto::Swap(DoFinalizeRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoFinalizeRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoFinalizeRequestProto_descriptor_;
  metadata.reflection = DoFinalizeRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

DoFinalizeResponseProto::DoFinalizeResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoFinalizeResponseProto::InitAsDefaultInstance() {
}

DoFinalizeResponseProto::DoFinalizeResponseProto(const DoFinalizeResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoFinalizeResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoFinalizeResponseProto::~DoFinalizeResponseProto() {
  SharedDtor();
}

void DoFinalizeResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DoFinalizeResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoFinalizeResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoFinalizeResponseProto_descriptor_;
}

const DoFinalizeResponseProto& DoFinalizeResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoFinalizeResponseProto* DoFinalizeResponseProto::default_instance_ = NULL;

DoFinalizeResponseProto* DoFinalizeResponseProto::New() const {
  return new DoFinalizeResponseProto;
}

void DoFinalizeResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoFinalizeResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void DoFinalizeResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoFinalizeResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoFinalizeResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoFinalizeResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoFinalizeResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoFinalizeResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoFinalizeResponseProto::MergeFrom(const DoFinalizeResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoFinalizeResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoFinalizeResponseProto::CopyFrom(const DoFinalizeResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoFinalizeResponseProto::IsInitialized() const {

  return true;
}

void DoFinalizeResponseProto::Swap(DoFinalizeResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoFinalizeResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoFinalizeResponseProto_descriptor_;
  metadata.reflection = DoFinalizeResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CanRollBackRequestProto::kJidFieldNumber;
const int CanRollBackRequestProto::kStorageFieldNumber;
const int CanRollBackRequestProto::kPrevStorageFieldNumber;
const int CanRollBackRequestProto::kTargetLayoutVersionFieldNumber;
#endif  // !_MSC_VER

CanRollBackRequestProto::CanRollBackRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CanRollBackRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
  storage_ = const_cast< ::hadoop::hdfs::StorageInfoProto*>(&::hadoop::hdfs::StorageInfoProto::default_instance());
  prevstorage_ = const_cast< ::hadoop::hdfs::StorageInfoProto*>(&::hadoop::hdfs::StorageInfoProto::default_instance());
}

CanRollBackRequestProto::CanRollBackRequestProto(const CanRollBackRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CanRollBackRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  storage_ = NULL;
  prevstorage_ = NULL;
  targetlayoutversion_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CanRollBackRequestProto::~CanRollBackRequestProto() {
  SharedDtor();
}

void CanRollBackRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
    delete storage_;
    delete prevstorage_;
  }
}

void CanRollBackRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CanRollBackRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CanRollBackRequestProto_descriptor_;
}

const CanRollBackRequestProto& CanRollBackRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

CanRollBackRequestProto* CanRollBackRequestProto::default_instance_ = NULL;

CanRollBackRequestProto* CanRollBackRequestProto::New() const {
  return new CanRollBackRequestProto;
}

void CanRollBackRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    if (has_storage()) {
      if (storage_ != NULL) storage_->::hadoop::hdfs::StorageInfoProto::Clear();
    }
    if (has_prevstorage()) {
      if (prevstorage_ != NULL) prevstorage_->::hadoop::hdfs::StorageInfoProto::Clear();
    }
    targetlayoutversion_ = 0;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CanRollBackRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_storage;
        break;
      }

      // required .hadoop.hdfs.StorageInfoProto storage = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_storage:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_storage()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_prevStorage;
        break;
      }

      // required .hadoop.hdfs.StorageInfoProto prevStorage = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_prevStorage:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_prevstorage()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_targetLayoutVersion;
        break;
      }

      // required int32 targetLayoutVersion = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_targetLayoutVersion:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &targetlayoutversion_)));
          set_has_targetlayoutversion();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CanRollBackRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  // required .hadoop.hdfs.StorageInfoProto storage = 2;
  if (has_storage()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->storage(), output);
  }

  // required .hadoop.hdfs.StorageInfoProto prevStorage = 3;
  if (has_prevstorage()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->prevstorage(), output);
  }

  // required int32 targetLayoutVersion = 4;
  if (has_targetlayoutversion()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->targetlayoutversion(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CanRollBackRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  // required .hadoop.hdfs.StorageInfoProto storage = 2;
  if (has_storage()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->storage(), target);
  }

  // required .hadoop.hdfs.StorageInfoProto prevStorage = 3;
  if (has_prevstorage()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        3, this->prevstorage(), target);
  }

  // required int32 targetLayoutVersion = 4;
  if (has_targetlayoutversion()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->targetlayoutversion(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CanRollBackRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

    // required .hadoop.hdfs.StorageInfoProto storage = 2;
    if (has_storage()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->storage());
    }

    // required .hadoop.hdfs.StorageInfoProto prevStorage = 3;
    if (has_prevstorage()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->prevstorage());
    }

    // required int32 targetLayoutVersion = 4;
    if (has_targetlayoutversion()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->targetlayoutversion());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CanRollBackRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CanRollBackRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CanRollBackRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CanRollBackRequestProto::MergeFrom(const CanRollBackRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
    if (from.has_storage()) {
      mutable_storage()->::hadoop::hdfs::StorageInfoProto::MergeFrom(from.storage());
    }
    if (from.has_prevstorage()) {
      mutable_prevstorage()->::hadoop::hdfs::StorageInfoProto::MergeFrom(from.prevstorage());
    }
    if (from.has_targetlayoutversion()) {
      set_targetlayoutversion(from.targetlayoutversion());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CanRollBackRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CanRollBackRequestProto::CopyFrom(const CanRollBackRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CanRollBackRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x0000000f) != 0x0000000f) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  if (has_storage()) {
    if (!this->storage().IsInitialized()) return false;
  }
  if (has_prevstorage()) {
    if (!this->prevstorage().IsInitialized()) return false;
  }
  return true;
}

void CanRollBackRequestProto::Swap(CanRollBackRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(storage_, other->storage_);
    std::swap(prevstorage_, other->prevstorage_);
    std::swap(targetlayoutversion_, other->targetlayoutversion_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CanRollBackRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CanRollBackRequestProto_descriptor_;
  metadata.reflection = CanRollBackRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int CanRollBackResponseProto::kCanRollBackFieldNumber;
#endif  // !_MSC_VER

CanRollBackResponseProto::CanRollBackResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void CanRollBackResponseProto::InitAsDefaultInstance() {
}

CanRollBackResponseProto::CanRollBackResponseProto(const CanRollBackResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void CanRollBackResponseProto::SharedCtor() {
  _cached_size_ = 0;
  canrollback_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

CanRollBackResponseProto::~CanRollBackResponseProto() {
  SharedDtor();
}

void CanRollBackResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void CanRollBackResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CanRollBackResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CanRollBackResponseProto_descriptor_;
}

const CanRollBackResponseProto& CanRollBackResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

CanRollBackResponseProto* CanRollBackResponseProto::default_instance_ = NULL;

CanRollBackResponseProto* CanRollBackResponseProto::New() const {
  return new CanRollBackResponseProto;
}

void CanRollBackResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    canrollback_ = false;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool CanRollBackResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required bool canRollBack = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &canrollback_)));
          set_has_canrollback();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void CanRollBackResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required bool canRollBack = 1;
  if (has_canrollback()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->canrollback(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* CanRollBackResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required bool canRollBack = 1;
  if (has_canrollback()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->canrollback(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int CanRollBackResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required bool canRollBack = 1;
    if (has_canrollback()) {
      total_size += 1 + 1;
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CanRollBackResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const CanRollBackResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const CanRollBackResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void CanRollBackResponseProto::MergeFrom(const CanRollBackResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_canrollback()) {
      set_canrollback(from.canrollback());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void CanRollBackResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CanRollBackResponseProto::CopyFrom(const CanRollBackResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CanRollBackResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void CanRollBackResponseProto::Swap(CanRollBackResponseProto* other) {
  if (other != this) {
    std::swap(canrollback_, other->canrollback_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata CanRollBackResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CanRollBackResponseProto_descriptor_;
  metadata.reflection = CanRollBackResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int DoRollbackRequestProto::kJidFieldNumber;
#endif  // !_MSC_VER

DoRollbackRequestProto::DoRollbackRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoRollbackRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

DoRollbackRequestProto::DoRollbackRequestProto(const DoRollbackRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoRollbackRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoRollbackRequestProto::~DoRollbackRequestProto() {
  SharedDtor();
}

void DoRollbackRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void DoRollbackRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoRollbackRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoRollbackRequestProto_descriptor_;
}

const DoRollbackRequestProto& DoRollbackRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoRollbackRequestProto* DoRollbackRequestProto::default_instance_ = NULL;

DoRollbackRequestProto* DoRollbackRequestProto::New() const {
  return new DoRollbackRequestProto;
}

void DoRollbackRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoRollbackRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void DoRollbackRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoRollbackRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoRollbackRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoRollbackRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoRollbackRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoRollbackRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoRollbackRequestProto::MergeFrom(const DoRollbackRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoRollbackRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoRollbackRequestProto::CopyFrom(const DoRollbackRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoRollbackRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void DoRollbackRequestProto::Swap(DoRollbackRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoRollbackRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoRollbackRequestProto_descriptor_;
  metadata.reflection = DoRollbackRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

DoRollbackResponseProto::DoRollbackResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void DoRollbackResponseProto::InitAsDefaultInstance() {
}

DoRollbackResponseProto::DoRollbackResponseProto(const DoRollbackResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void DoRollbackResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

DoRollbackResponseProto::~DoRollbackResponseProto() {
  SharedDtor();
}

void DoRollbackResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void DoRollbackResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoRollbackResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoRollbackResponseProto_descriptor_;
}

const DoRollbackResponseProto& DoRollbackResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

DoRollbackResponseProto* DoRollbackResponseProto::default_instance_ = NULL;

DoRollbackResponseProto* DoRollbackResponseProto::New() const {
  return new DoRollbackResponseProto;
}

void DoRollbackResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool DoRollbackResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void DoRollbackResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* DoRollbackResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int DoRollbackResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoRollbackResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const DoRollbackResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const DoRollbackResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void DoRollbackResponseProto::MergeFrom(const DoRollbackResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void DoRollbackResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoRollbackResponseProto::CopyFrom(const DoRollbackResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoRollbackResponseProto::IsInitialized() const {

  return true;
}

void DoRollbackResponseProto::Swap(DoRollbackResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata DoRollbackResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoRollbackResponseProto_descriptor_;
  metadata.reflection = DoRollbackResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetJournalStateRequestProto::kJidFieldNumber;
#endif  // !_MSC_VER

GetJournalStateRequestProto::GetJournalStateRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetJournalStateRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

GetJournalStateRequestProto::GetJournalStateRequestProto(const GetJournalStateRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetJournalStateRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetJournalStateRequestProto::~GetJournalStateRequestProto() {
  SharedDtor();
}

void GetJournalStateRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void GetJournalStateRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetJournalStateRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetJournalStateRequestProto_descriptor_;
}

const GetJournalStateRequestProto& GetJournalStateRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

GetJournalStateRequestProto* GetJournalStateRequestProto::default_instance_ = NULL;

GetJournalStateRequestProto* GetJournalStateRequestProto::New() const {
  return new GetJournalStateRequestProto;
}

void GetJournalStateRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetJournalStateRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetJournalStateRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetJournalStateRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetJournalStateRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetJournalStateRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetJournalStateRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetJournalStateRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetJournalStateRequestProto::MergeFrom(const GetJournalStateRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetJournalStateRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetJournalStateRequestProto::CopyFrom(const GetJournalStateRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJournalStateRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void GetJournalStateRequestProto::Swap(GetJournalStateRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetJournalStateRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetJournalStateRequestProto_descriptor_;
  metadata.reflection = GetJournalStateRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetJournalStateResponseProto::kLastPromisedEpochFieldNumber;
const int GetJournalStateResponseProto::kHttpPortFieldNumber;
const int GetJournalStateResponseProto::kFromURLFieldNumber;
#endif  // !_MSC_VER

GetJournalStateResponseProto::GetJournalStateResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetJournalStateResponseProto::InitAsDefaultInstance() {
}

GetJournalStateResponseProto::GetJournalStateResponseProto(const GetJournalStateResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetJournalStateResponseProto::SharedCtor() {
  _cached_size_ = 0;
  lastpromisedepoch_ = GOOGLE_ULONGLONG(0);
  httpport_ = 0u;
  fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetJournalStateResponseProto::~GetJournalStateResponseProto() {
  SharedDtor();
}

void GetJournalStateResponseProto::SharedDtor() {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    delete fromurl_;
  }
  if (this != default_instance_) {
  }
}

void GetJournalStateResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetJournalStateResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetJournalStateResponseProto_descriptor_;
}

const GetJournalStateResponseProto& GetJournalStateResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

GetJournalStateResponseProto* GetJournalStateResponseProto::default_instance_ = NULL;

GetJournalStateResponseProto* GetJournalStateResponseProto::New() const {
  return new GetJournalStateResponseProto;
}

void GetJournalStateResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    lastpromisedepoch_ = GOOGLE_ULONGLONG(0);
    httpport_ = 0u;
    if (has_fromurl()) {
      if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
        fromurl_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetJournalStateResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 lastPromisedEpoch = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &lastpromisedepoch_)));
          set_has_lastpromisedepoch();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_httpPort;
        break;
      }

      // required uint32 httpPort = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_httpPort:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &httpport_)));
          set_has_httpport();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_fromURL;
        break;
      }

      // optional string fromURL = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_fromURL:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fromurl()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->fromurl().data(), this->fromurl().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetJournalStateResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 lastPromisedEpoch = 1;
  if (has_lastpromisedepoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->lastpromisedepoch(), output);
  }

  // required uint32 httpPort = 2;
  if (has_httpport()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->httpport(), output);
  }

  // optional string fromURL = 3;
  if (has_fromurl()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fromurl().data(), this->fromurl().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->fromurl(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetJournalStateResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 lastPromisedEpoch = 1;
  if (has_lastpromisedepoch()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->lastpromisedepoch(), target);
  }

  // required uint32 httpPort = 2;
  if (has_httpport()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->httpport(), target);
  }

  // optional string fromURL = 3;
  if (has_fromurl()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fromurl().data(), this->fromurl().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->fromurl(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetJournalStateResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 lastPromisedEpoch = 1;
    if (has_lastpromisedepoch()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->lastpromisedepoch());
    }

    // required uint32 httpPort = 2;
    if (has_httpport()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->httpport());
    }

    // optional string fromURL = 3;
    if (has_fromurl()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->fromurl());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetJournalStateResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetJournalStateResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetJournalStateResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetJournalStateResponseProto::MergeFrom(const GetJournalStateResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_lastpromisedepoch()) {
      set_lastpromisedepoch(from.lastpromisedepoch());
    }
    if (from.has_httpport()) {
      set_httpport(from.httpport());
    }
    if (from.has_fromurl()) {
      set_fromurl(from.fromurl());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetJournalStateResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetJournalStateResponseProto::CopyFrom(const GetJournalStateResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJournalStateResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void GetJournalStateResponseProto::Swap(GetJournalStateResponseProto* other) {
  if (other != this) {
    std::swap(lastpromisedepoch_, other->lastpromisedepoch_);
    std::swap(httpport_, other->httpport_);
    std::swap(fromurl_, other->fromurl_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetJournalStateResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetJournalStateResponseProto_descriptor_;
  metadata.reflection = GetJournalStateResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int FormatRequestProto::kJidFieldNumber;
const int FormatRequestProto::kNsInfoFieldNumber;
#endif  // !_MSC_VER

FormatRequestProto::FormatRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FormatRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
  nsinfo_ = const_cast< ::hadoop::hdfs::NamespaceInfoProto*>(&::hadoop::hdfs::NamespaceInfoProto::default_instance());
}

FormatRequestProto::FormatRequestProto(const FormatRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FormatRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  nsinfo_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FormatRequestProto::~FormatRequestProto() {
  SharedDtor();
}

void FormatRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
    delete nsinfo_;
  }
}

void FormatRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FormatRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FormatRequestProto_descriptor_;
}

const FormatRequestProto& FormatRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

FormatRequestProto* FormatRequestProto::default_instance_ = NULL;

FormatRequestProto* FormatRequestProto::New() const {
  return new FormatRequestProto;
}

void FormatRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    if (has_nsinfo()) {
      if (nsinfo_ != NULL) nsinfo_->::hadoop::hdfs::NamespaceInfoProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FormatRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_nsInfo;
        break;
      }

      // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_nsInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nsinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void FormatRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
  if (has_nsinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->nsinfo(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FormatRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
  if (has_nsinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->nsinfo(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FormatRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

    // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
    if (has_nsinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->nsinfo());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FormatRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FormatRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FormatRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FormatRequestProto::MergeFrom(const FormatRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
    if (from.has_nsinfo()) {
      mutable_nsinfo()->::hadoop::hdfs::NamespaceInfoProto::MergeFrom(from.nsinfo());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FormatRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FormatRequestProto::CopyFrom(const FormatRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FormatRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  if (has_nsinfo()) {
    if (!this->nsinfo().IsInitialized()) return false;
  }
  return true;
}

void FormatRequestProto::Swap(FormatRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(nsinfo_, other->nsinfo_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FormatRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FormatRequestProto_descriptor_;
  metadata.reflection = FormatRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

FormatResponseProto::FormatResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void FormatResponseProto::InitAsDefaultInstance() {
}

FormatResponseProto::FormatResponseProto(const FormatResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void FormatResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

FormatResponseProto::~FormatResponseProto() {
  SharedDtor();
}

void FormatResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void FormatResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FormatResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FormatResponseProto_descriptor_;
}

const FormatResponseProto& FormatResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

FormatResponseProto* FormatResponseProto::default_instance_ = NULL;

FormatResponseProto* FormatResponseProto::New() const {
  return new FormatResponseProto;
}

void FormatResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool FormatResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void FormatResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* FormatResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int FormatResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FormatResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const FormatResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const FormatResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void FormatResponseProto::MergeFrom(const FormatResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void FormatResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FormatResponseProto::CopyFrom(const FormatResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FormatResponseProto::IsInitialized() const {

  return true;
}

void FormatResponseProto::Swap(FormatResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata FormatResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FormatResponseProto_descriptor_;
  metadata.reflection = FormatResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int NewEpochRequestProto::kJidFieldNumber;
const int NewEpochRequestProto::kNsInfoFieldNumber;
const int NewEpochRequestProto::kEpochFieldNumber;
#endif  // !_MSC_VER

NewEpochRequestProto::NewEpochRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void NewEpochRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
  nsinfo_ = const_cast< ::hadoop::hdfs::NamespaceInfoProto*>(&::hadoop::hdfs::NamespaceInfoProto::default_instance());
}

NewEpochRequestProto::NewEpochRequestProto(const NewEpochRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void NewEpochRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  nsinfo_ = NULL;
  epoch_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

NewEpochRequestProto::~NewEpochRequestProto() {
  SharedDtor();
}

void NewEpochRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
    delete nsinfo_;
  }
}

void NewEpochRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NewEpochRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NewEpochRequestProto_descriptor_;
}

const NewEpochRequestProto& NewEpochRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

NewEpochRequestProto* NewEpochRequestProto::default_instance_ = NULL;

NewEpochRequestProto* NewEpochRequestProto::New() const {
  return new NewEpochRequestProto;
}

void NewEpochRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    if (has_nsinfo()) {
      if (nsinfo_ != NULL) nsinfo_->::hadoop::hdfs::NamespaceInfoProto::Clear();
    }
    epoch_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool NewEpochRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_nsInfo;
        break;
      }

      // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_nsInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nsinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_epoch;
        break;
      }

      // required uint64 epoch = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_epoch:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &epoch_)));
          set_has_epoch();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void NewEpochRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
  if (has_nsinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->nsinfo(), output);
  }

  // required uint64 epoch = 3;
  if (has_epoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->epoch(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* NewEpochRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
  if (has_nsinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->nsinfo(), target);
  }

  // required uint64 epoch = 3;
  if (has_epoch()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->epoch(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int NewEpochRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

    // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
    if (has_nsinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->nsinfo());
    }

    // required uint64 epoch = 3;
    if (has_epoch()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->epoch());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NewEpochRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const NewEpochRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const NewEpochRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void NewEpochRequestProto::MergeFrom(const NewEpochRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
    if (from.has_nsinfo()) {
      mutable_nsinfo()->::hadoop::hdfs::NamespaceInfoProto::MergeFrom(from.nsinfo());
    }
    if (from.has_epoch()) {
      set_epoch(from.epoch());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void NewEpochRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NewEpochRequestProto::CopyFrom(const NewEpochRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NewEpochRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  if (has_nsinfo()) {
    if (!this->nsinfo().IsInitialized()) return false;
  }
  return true;
}

void NewEpochRequestProto::Swap(NewEpochRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(nsinfo_, other->nsinfo_);
    std::swap(epoch_, other->epoch_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata NewEpochRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NewEpochRequestProto_descriptor_;
  metadata.reflection = NewEpochRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int NewEpochResponseProto::kLastSegmentTxIdFieldNumber;
#endif  // !_MSC_VER

NewEpochResponseProto::NewEpochResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void NewEpochResponseProto::InitAsDefaultInstance() {
}

NewEpochResponseProto::NewEpochResponseProto(const NewEpochResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void NewEpochResponseProto::SharedCtor() {
  _cached_size_ = 0;
  lastsegmenttxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

NewEpochResponseProto::~NewEpochResponseProto() {
  SharedDtor();
}

void NewEpochResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void NewEpochResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NewEpochResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NewEpochResponseProto_descriptor_;
}

const NewEpochResponseProto& NewEpochResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

NewEpochResponseProto* NewEpochResponseProto::default_instance_ = NULL;

NewEpochResponseProto* NewEpochResponseProto::New() const {
  return new NewEpochResponseProto;
}

void NewEpochResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    lastsegmenttxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool NewEpochResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 lastSegmentTxId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &lastsegmenttxid_)));
          set_has_lastsegmenttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void NewEpochResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional uint64 lastSegmentTxId = 1;
  if (has_lastsegmenttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->lastsegmenttxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* NewEpochResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional uint64 lastSegmentTxId = 1;
  if (has_lastsegmenttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->lastsegmenttxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int NewEpochResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional uint64 lastSegmentTxId = 1;
    if (has_lastsegmenttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->lastsegmenttxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NewEpochResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const NewEpochResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const NewEpochResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void NewEpochResponseProto::MergeFrom(const NewEpochResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_lastsegmenttxid()) {
      set_lastsegmenttxid(from.lastsegmenttxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void NewEpochResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NewEpochResponseProto::CopyFrom(const NewEpochResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NewEpochResponseProto::IsInitialized() const {

  return true;
}

void NewEpochResponseProto::Swap(NewEpochResponseProto* other) {
  if (other != this) {
    std::swap(lastsegmenttxid_, other->lastsegmenttxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata NewEpochResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NewEpochResponseProto_descriptor_;
  metadata.reflection = NewEpochResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetEditLogManifestRequestProto::kJidFieldNumber;
const int GetEditLogManifestRequestProto::kSinceTxIdFieldNumber;
const int GetEditLogManifestRequestProto::kInProgressOkFieldNumber;
#endif  // !_MSC_VER

GetEditLogManifestRequestProto::GetEditLogManifestRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetEditLogManifestRequestProto::InitAsDefaultInstance() {
  jid_ = const_cast< ::hadoop::hdfs::qjournal::JournalIdProto*>(&::hadoop::hdfs::qjournal::JournalIdProto::default_instance());
}

GetEditLogManifestRequestProto::GetEditLogManifestRequestProto(const GetEditLogManifestRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetEditLogManifestRequestProto::SharedCtor() {
  _cached_size_ = 0;
  jid_ = NULL;
  sincetxid_ = GOOGLE_ULONGLONG(0);
  inprogressok_ = false;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetEditLogManifestRequestProto::~GetEditLogManifestRequestProto() {
  SharedDtor();
}

void GetEditLogManifestRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete jid_;
  }
}

void GetEditLogManifestRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetEditLogManifestRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetEditLogManifestRequestProto_descriptor_;
}

const GetEditLogManifestRequestProto& GetEditLogManifestRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

GetEditLogManifestRequestProto* GetEditLogManifestRequestProto::default_instance_ = NULL;

GetEditLogManifestRequestProto* GetEditLogManifestRequestProto::New() const {
  return new GetEditLogManifestRequestProto;
}

void GetEditLogManifestRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_jid()) {
      if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
    }
    sincetxid_ = GOOGLE_ULONGLONG(0);
    inprogressok_ = false;
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetEditLogManifestRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_jid()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_sinceTxId;
        break;
      }

      // required uint64 sinceTxId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_sinceTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &sincetxid_)));
          set_has_sincetxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_inProgressOk;
        break;
      }

      // optional bool inProgressOk = 4 [default = false];
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_inProgressOk:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &inprogressok_)));
          set_has_inprogressok();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetEditLogManifestRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->jid(), output);
  }

  // required uint64 sinceTxId = 2;
  if (has_sincetxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->sincetxid(), output);
  }

  // optional bool inProgressOk = 4 [default = false];
  if (has_inprogressok()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->inprogressok(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetEditLogManifestRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  if (has_jid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->jid(), target);
  }

  // required uint64 sinceTxId = 2;
  if (has_sincetxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->sincetxid(), target);
  }

  // optional bool inProgressOk = 4 [default = false];
  if (has_inprogressok()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->inprogressok(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetEditLogManifestRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
    if (has_jid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->jid());
    }

    // required uint64 sinceTxId = 2;
    if (has_sincetxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->sincetxid());
    }

    // optional bool inProgressOk = 4 [default = false];
    if (has_inprogressok()) {
      total_size += 1 + 1;
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetEditLogManifestRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetEditLogManifestRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetEditLogManifestRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetEditLogManifestRequestProto::MergeFrom(const GetEditLogManifestRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_jid()) {
      mutable_jid()->::hadoop::hdfs::qjournal::JournalIdProto::MergeFrom(from.jid());
    }
    if (from.has_sincetxid()) {
      set_sincetxid(from.sincetxid());
    }
    if (from.has_inprogressok()) {
      set_inprogressok(from.inprogressok());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetEditLogManifestRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetEditLogManifestRequestProto::CopyFrom(const GetEditLogManifestRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetEditLogManifestRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_jid()) {
    if (!this->jid().IsInitialized()) return false;
  }
  return true;
}

void GetEditLogManifestRequestProto::Swap(GetEditLogManifestRequestProto* other) {
  if (other != this) {
    std::swap(jid_, other->jid_);
    std::swap(sincetxid_, other->sincetxid_);
    std::swap(inprogressok_, other->inprogressok_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetEditLogManifestRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetEditLogManifestRequestProto_descriptor_;
  metadata.reflection = GetEditLogManifestRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetEditLogManifestResponseProto::kManifestFieldNumber;
const int GetEditLogManifestResponseProto::kHttpPortFieldNumber;
const int GetEditLogManifestResponseProto::kFromURLFieldNumber;
#endif  // !_MSC_VER

GetEditLogManifestResponseProto::GetEditLogManifestResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetEditLogManifestResponseProto::InitAsDefaultInstance() {
  manifest_ = const_cast< ::hadoop::hdfs::RemoteEditLogManifestProto*>(&::hadoop::hdfs::RemoteEditLogManifestProto::default_instance());
}

GetEditLogManifestResponseProto::GetEditLogManifestResponseProto(const GetEditLogManifestResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetEditLogManifestResponseProto::SharedCtor() {
  _cached_size_ = 0;
  manifest_ = NULL;
  httpport_ = 0u;
  fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetEditLogManifestResponseProto::~GetEditLogManifestResponseProto() {
  SharedDtor();
}

void GetEditLogManifestResponseProto::SharedDtor() {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    delete fromurl_;
  }
  if (this != default_instance_) {
    delete manifest_;
  }
}

void GetEditLogManifestResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetEditLogManifestResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetEditLogManifestResponseProto_descriptor_;
}

const GetEditLogManifestResponseProto& GetEditLogManifestResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

GetEditLogManifestResponseProto* GetEditLogManifestResponseProto::default_instance_ = NULL;

GetEditLogManifestResponseProto* GetEditLogManifestResponseProto::New() const {
  return new GetEditLogManifestResponseProto;
}

void GetEditLogManifestResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_manifest()) {
      if (manifest_ != NULL) manifest_->::hadoop::hdfs::RemoteEditLogManifestProto::Clear();
    }
    httpport_ = 0u;
    if (has_fromurl()) {
      if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
        fromurl_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetEditLogManifestResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_manifest()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_httpPort;
        break;
      }

      // required uint32 httpPort = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_httpPort:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &httpport_)));
          set_has_httpport();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_fromURL;
        break;
      }

      // optional string fromURL = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_fromURL:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fromurl()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->fromurl().data(), this->fromurl().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetEditLogManifestResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
  if (has_manifest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->manifest(), output);
  }

  // required uint32 httpPort = 2;
  if (has_httpport()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->httpport(), output);
  }

  // optional string fromURL = 3;
  if (has_fromurl()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fromurl().data(), this->fromurl().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->fromurl(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetEditLogManifestResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
  if (has_manifest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->manifest(), target);
  }

  // required uint32 httpPort = 2;
  if (has_httpport()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->httpport(), target);
  }

  // optional string fromURL = 3;
  if (has_fromurl()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fromurl().data(), this->fromurl().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->fromurl(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetEditLogManifestResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
    if (has_manifest()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->manifest());
    }

    // required uint32 httpPort = 2;
    if (has_httpport()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->httpport());
    }

    // optional string fromURL = 3;
    if (has_fromurl()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->fromurl());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetEditLogManifestResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetEditLogManifestResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetEditLogManifestResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetEditLogManifestResponseProto::MergeFrom(const GetEditLogManifestResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_manifest()) {
      mutable_manifest()->::hadoop::hdfs::RemoteEditLogManifestProto::MergeFrom(from.manifest());
    }
    if (from.has_httpport()) {
      set_httpport(from.httpport());
    }
    if (from.has_fromurl()) {
      set_fromurl(from.fromurl());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetEditLogManifestResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetEditLogManifestResponseProto::CopyFrom(const GetEditLogManifestResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetEditLogManifestResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_manifest()) {
    if (!this->manifest().IsInitialized()) return false;
  }
  return true;
}

void GetEditLogManifestResponseProto::Swap(GetEditLogManifestResponseProto* other) {
  if (other != this) {
    std::swap(manifest_, other->manifest_);
    std::swap(httpport_, other->httpport_);
    std::swap(fromurl_, other->fromurl_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetEditLogManifestResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetEditLogManifestResponseProto_descriptor_;
  metadata.reflection = GetEditLogManifestResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int PrepareRecoveryRequestProto::kReqInfoFieldNumber;
const int PrepareRecoveryRequestProto::kSegmentTxIdFieldNumber;
#endif  // !_MSC_VER

PrepareRecoveryRequestProto::PrepareRecoveryRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PrepareRecoveryRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
}

PrepareRecoveryRequestProto::PrepareRecoveryRequestProto(const PrepareRecoveryRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PrepareRecoveryRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  segmenttxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PrepareRecoveryRequestProto::~PrepareRecoveryRequestProto() {
  SharedDtor();
}

void PrepareRecoveryRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete reqinfo_;
  }
}

void PrepareRecoveryRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PrepareRecoveryRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PrepareRecoveryRequestProto_descriptor_;
}

const PrepareRecoveryRequestProto& PrepareRecoveryRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

PrepareRecoveryRequestProto* PrepareRecoveryRequestProto::default_instance_ = NULL;

PrepareRecoveryRequestProto* PrepareRecoveryRequestProto::New() const {
  return new PrepareRecoveryRequestProto;
}

void PrepareRecoveryRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
    segmenttxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PrepareRecoveryRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_segmentTxId;
        break;
      }

      // required uint64 segmentTxId = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_segmentTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &segmenttxid_)));
          set_has_segmenttxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void PrepareRecoveryRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  // required uint64 segmentTxId = 2;
  if (has_segmenttxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->segmenttxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PrepareRecoveryRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  // required uint64 segmentTxId = 2;
  if (has_segmenttxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->segmenttxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PrepareRecoveryRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

    // required uint64 segmentTxId = 2;
    if (has_segmenttxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->segmenttxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PrepareRecoveryRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PrepareRecoveryRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PrepareRecoveryRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PrepareRecoveryRequestProto::MergeFrom(const PrepareRecoveryRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
    if (from.has_segmenttxid()) {
      set_segmenttxid(from.segmenttxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PrepareRecoveryRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PrepareRecoveryRequestProto::CopyFrom(const PrepareRecoveryRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PrepareRecoveryRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  return true;
}

void PrepareRecoveryRequestProto::Swap(PrepareRecoveryRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(segmenttxid_, other->segmenttxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PrepareRecoveryRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PrepareRecoveryRequestProto_descriptor_;
  metadata.reflection = PrepareRecoveryRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int PrepareRecoveryResponseProto::kSegmentStateFieldNumber;
const int PrepareRecoveryResponseProto::kAcceptedInEpochFieldNumber;
const int PrepareRecoveryResponseProto::kLastWriterEpochFieldNumber;
const int PrepareRecoveryResponseProto::kLastCommittedTxIdFieldNumber;
#endif  // !_MSC_VER

PrepareRecoveryResponseProto::PrepareRecoveryResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void PrepareRecoveryResponseProto::InitAsDefaultInstance() {
  segmentstate_ = const_cast< ::hadoop::hdfs::qjournal::SegmentStateProto*>(&::hadoop::hdfs::qjournal::SegmentStateProto::default_instance());
}

PrepareRecoveryResponseProto::PrepareRecoveryResponseProto(const PrepareRecoveryResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void PrepareRecoveryResponseProto::SharedCtor() {
  _cached_size_ = 0;
  segmentstate_ = NULL;
  acceptedinepoch_ = GOOGLE_ULONGLONG(0);
  lastwriterepoch_ = GOOGLE_ULONGLONG(0);
  lastcommittedtxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

PrepareRecoveryResponseProto::~PrepareRecoveryResponseProto() {
  SharedDtor();
}

void PrepareRecoveryResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete segmentstate_;
  }
}

void PrepareRecoveryResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PrepareRecoveryResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PrepareRecoveryResponseProto_descriptor_;
}

const PrepareRecoveryResponseProto& PrepareRecoveryResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

PrepareRecoveryResponseProto* PrepareRecoveryResponseProto::default_instance_ = NULL;

PrepareRecoveryResponseProto* PrepareRecoveryResponseProto::New() const {
  return new PrepareRecoveryResponseProto;
}

void PrepareRecoveryResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_segmentstate()) {
      if (segmentstate_ != NULL) segmentstate_->::hadoop::hdfs::qjournal::SegmentStateProto::Clear();
    }
    acceptedinepoch_ = GOOGLE_ULONGLONG(0);
    lastwriterepoch_ = GOOGLE_ULONGLONG(0);
    lastcommittedtxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool PrepareRecoveryResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_segmentstate()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_acceptedInEpoch;
        break;
      }

      // optional uint64 acceptedInEpoch = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_acceptedInEpoch:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &acceptedinepoch_)));
          set_has_acceptedinepoch();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(24)) goto parse_lastWriterEpoch;
        break;
      }

      // required uint64 lastWriterEpoch = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lastWriterEpoch:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &lastwriterepoch_)));
          set_has_lastwriterepoch();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(32)) goto parse_lastCommittedTxId;
        break;
      }

      // optional uint64 lastCommittedTxId = 4;
      case 4: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_lastCommittedTxId:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &lastcommittedtxid_)));
          set_has_lastcommittedtxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void PrepareRecoveryResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
  if (has_segmentstate()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->segmentstate(), output);
  }

  // optional uint64 acceptedInEpoch = 2;
  if (has_acceptedinepoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->acceptedinepoch(), output);
  }

  // required uint64 lastWriterEpoch = 3;
  if (has_lastwriterepoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->lastwriterepoch(), output);
  }

  // optional uint64 lastCommittedTxId = 4;
  if (has_lastcommittedtxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->lastcommittedtxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* PrepareRecoveryResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
  if (has_segmentstate()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->segmentstate(), target);
  }

  // optional uint64 acceptedInEpoch = 2;
  if (has_acceptedinepoch()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->acceptedinepoch(), target);
  }

  // required uint64 lastWriterEpoch = 3;
  if (has_lastwriterepoch()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->lastwriterepoch(), target);
  }

  // optional uint64 lastCommittedTxId = 4;
  if (has_lastcommittedtxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->lastcommittedtxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int PrepareRecoveryResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
    if (has_segmentstate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->segmentstate());
    }

    // optional uint64 acceptedInEpoch = 2;
    if (has_acceptedinepoch()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->acceptedinepoch());
    }

    // required uint64 lastWriterEpoch = 3;
    if (has_lastwriterepoch()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->lastwriterepoch());
    }

    // optional uint64 lastCommittedTxId = 4;
    if (has_lastcommittedtxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->lastcommittedtxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PrepareRecoveryResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const PrepareRecoveryResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const PrepareRecoveryResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void PrepareRecoveryResponseProto::MergeFrom(const PrepareRecoveryResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_segmentstate()) {
      mutable_segmentstate()->::hadoop::hdfs::qjournal::SegmentStateProto::MergeFrom(from.segmentstate());
    }
    if (from.has_acceptedinepoch()) {
      set_acceptedinepoch(from.acceptedinepoch());
    }
    if (from.has_lastwriterepoch()) {
      set_lastwriterepoch(from.lastwriterepoch());
    }
    if (from.has_lastcommittedtxid()) {
      set_lastcommittedtxid(from.lastcommittedtxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void PrepareRecoveryResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PrepareRecoveryResponseProto::CopyFrom(const PrepareRecoveryResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PrepareRecoveryResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000004) != 0x00000004) return false;

  if (has_segmentstate()) {
    if (!this->segmentstate().IsInitialized()) return false;
  }
  return true;
}

void PrepareRecoveryResponseProto::Swap(PrepareRecoveryResponseProto* other) {
  if (other != this) {
    std::swap(segmentstate_, other->segmentstate_);
    std::swap(acceptedinepoch_, other->acceptedinepoch_);
    std::swap(lastwriterepoch_, other->lastwriterepoch_);
    std::swap(lastcommittedtxid_, other->lastcommittedtxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata PrepareRecoveryResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PrepareRecoveryResponseProto_descriptor_;
  metadata.reflection = PrepareRecoveryResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int AcceptRecoveryRequestProto::kReqInfoFieldNumber;
const int AcceptRecoveryRequestProto::kStateToAcceptFieldNumber;
const int AcceptRecoveryRequestProto::kFromURLFieldNumber;
#endif  // !_MSC_VER

AcceptRecoveryRequestProto::AcceptRecoveryRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AcceptRecoveryRequestProto::InitAsDefaultInstance() {
  reqinfo_ = const_cast< ::hadoop::hdfs::qjournal::RequestInfoProto*>(&::hadoop::hdfs::qjournal::RequestInfoProto::default_instance());
  statetoaccept_ = const_cast< ::hadoop::hdfs::qjournal::SegmentStateProto*>(&::hadoop::hdfs::qjournal::SegmentStateProto::default_instance());
}

AcceptRecoveryRequestProto::AcceptRecoveryRequestProto(const AcceptRecoveryRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AcceptRecoveryRequestProto::SharedCtor() {
  _cached_size_ = 0;
  reqinfo_ = NULL;
  statetoaccept_ = NULL;
  fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AcceptRecoveryRequestProto::~AcceptRecoveryRequestProto() {
  SharedDtor();
}

void AcceptRecoveryRequestProto::SharedDtor() {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    delete fromurl_;
  }
  if (this != default_instance_) {
    delete reqinfo_;
    delete statetoaccept_;
  }
}

void AcceptRecoveryRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AcceptRecoveryRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AcceptRecoveryRequestProto_descriptor_;
}

const AcceptRecoveryRequestProto& AcceptRecoveryRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

AcceptRecoveryRequestProto* AcceptRecoveryRequestProto::default_instance_ = NULL;

AcceptRecoveryRequestProto* AcceptRecoveryRequestProto::New() const {
  return new AcceptRecoveryRequestProto;
}

void AcceptRecoveryRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_reqinfo()) {
      if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
    }
    if (has_statetoaccept()) {
      if (statetoaccept_ != NULL) statetoaccept_->::hadoop::hdfs::qjournal::SegmentStateProto::Clear();
    }
    if (has_fromurl()) {
      if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
        fromurl_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AcceptRecoveryRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_reqinfo()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_stateToAccept;
        break;
      }

      // required .hadoop.hdfs.qjournal.SegmentStateProto stateToAccept = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_stateToAccept:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_statetoaccept()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_fromURL;
        break;
      }

      // required string fromURL = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_fromURL:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fromurl()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->fromurl().data(), this->fromurl().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void AcceptRecoveryRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->reqinfo(), output);
  }

  // required .hadoop.hdfs.qjournal.SegmentStateProto stateToAccept = 2;
  if (has_statetoaccept()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->statetoaccept(), output);
  }

  // required string fromURL = 3;
  if (has_fromurl()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fromurl().data(), this->fromurl().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->fromurl(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AcceptRecoveryRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  if (has_reqinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->reqinfo(), target);
  }

  // required .hadoop.hdfs.qjournal.SegmentStateProto stateToAccept = 2;
  if (has_statetoaccept()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->statetoaccept(), target);
  }

  // required string fromURL = 3;
  if (has_fromurl()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->fromurl().data(), this->fromurl().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->fromurl(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AcceptRecoveryRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
    if (has_reqinfo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reqinfo());
    }

    // required .hadoop.hdfs.qjournal.SegmentStateProto stateToAccept = 2;
    if (has_statetoaccept()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->statetoaccept());
    }

    // required string fromURL = 3;
    if (has_fromurl()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->fromurl());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AcceptRecoveryRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AcceptRecoveryRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AcceptRecoveryRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AcceptRecoveryRequestProto::MergeFrom(const AcceptRecoveryRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_reqinfo()) {
      mutable_reqinfo()->::hadoop::hdfs::qjournal::RequestInfoProto::MergeFrom(from.reqinfo());
    }
    if (from.has_statetoaccept()) {
      mutable_statetoaccept()->::hadoop::hdfs::qjournal::SegmentStateProto::MergeFrom(from.statetoaccept());
    }
    if (from.has_fromurl()) {
      set_fromurl(from.fromurl());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AcceptRecoveryRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AcceptRecoveryRequestProto::CopyFrom(const AcceptRecoveryRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AcceptRecoveryRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_reqinfo()) {
    if (!this->reqinfo().IsInitialized()) return false;
  }
  if (has_statetoaccept()) {
    if (!this->statetoaccept().IsInitialized()) return false;
  }
  return true;
}

void AcceptRecoveryRequestProto::Swap(AcceptRecoveryRequestProto* other) {
  if (other != this) {
    std::swap(reqinfo_, other->reqinfo_);
    std::swap(statetoaccept_, other->statetoaccept_);
    std::swap(fromurl_, other->fromurl_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AcceptRecoveryRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AcceptRecoveryRequestProto_descriptor_;
  metadata.reflection = AcceptRecoveryRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

AcceptRecoveryResponseProto::AcceptRecoveryResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void AcceptRecoveryResponseProto::InitAsDefaultInstance() {
}

AcceptRecoveryResponseProto::AcceptRecoveryResponseProto(const AcceptRecoveryResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void AcceptRecoveryResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

AcceptRecoveryResponseProto::~AcceptRecoveryResponseProto() {
  SharedDtor();
}

void AcceptRecoveryResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void AcceptRecoveryResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AcceptRecoveryResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AcceptRecoveryResponseProto_descriptor_;
}

const AcceptRecoveryResponseProto& AcceptRecoveryResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_QJournalProtocol_2eproto();
  return *default_instance_;
}

AcceptRecoveryResponseProto* AcceptRecoveryResponseProto::default_instance_ = NULL;

AcceptRecoveryResponseProto* AcceptRecoveryResponseProto::New() const {
  return new AcceptRecoveryResponseProto;
}

void AcceptRecoveryResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool AcceptRecoveryResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void AcceptRecoveryResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* AcceptRecoveryResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int AcceptRecoveryResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AcceptRecoveryResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const AcceptRecoveryResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const AcceptRecoveryResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void AcceptRecoveryResponseProto::MergeFrom(const AcceptRecoveryResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void AcceptRecoveryResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AcceptRecoveryResponseProto::CopyFrom(const AcceptRecoveryResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AcceptRecoveryResponseProto::IsInitialized() const {

  return true;
}

void AcceptRecoveryResponseProto::Swap(AcceptRecoveryResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata AcceptRecoveryResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AcceptRecoveryResponseProto_descriptor_;
  metadata.reflection = AcceptRecoveryResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace qjournal
}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
