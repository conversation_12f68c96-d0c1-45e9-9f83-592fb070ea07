// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: QJournalProtocol.proto

#ifndef PROTOBUF_QJournalProtocol_2eproto__INCLUDED
#define PROTOBUF_QJournalProtocol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "hdfs.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {
namespace qjournal {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_QJournalProtocol_2eproto();
void protobuf_AssignDesc_QJournalProtocol_2eproto();
void protobuf_ShutdownFile_QJournalProtocol_2eproto();

class JournalIdProto;
class RequestInfoProto;
class SegmentStateProto;
class PersistedRecoveryPaxosData;
class JournalRequestProto;
class JournalResponseProto;
class HeartbeatRequestProto;
class HeartbeatResponseProto;
class StartLogSegmentRequestProto;
class StartLogSegmentResponseProto;
class FinalizeLogSegmentRequestProto;
class FinalizeLogSegmentResponseProto;
class PurgeLogsRequestProto;
class PurgeLogsResponseProto;
class IsFormattedRequestProto;
class IsFormattedResponseProto;
class DiscardSegmentsRequestProto;
class DiscardSegmentsResponseProto;
class GetJournalCTimeRequestProto;
class GetJournalCTimeResponseProto;
class DoPreUpgradeRequestProto;
class DoPreUpgradeResponseProto;
class DoUpgradeRequestProto;
class DoUpgradeResponseProto;
class DoFinalizeRequestProto;
class DoFinalizeResponseProto;
class CanRollBackRequestProto;
class CanRollBackResponseProto;
class DoRollbackRequestProto;
class DoRollbackResponseProto;
class GetJournalStateRequestProto;
class GetJournalStateResponseProto;
class FormatRequestProto;
class FormatResponseProto;
class NewEpochRequestProto;
class NewEpochResponseProto;
class GetEditLogManifestRequestProto;
class GetEditLogManifestResponseProto;
class PrepareRecoveryRequestProto;
class PrepareRecoveryResponseProto;
class AcceptRecoveryRequestProto;
class AcceptRecoveryResponseProto;

// ===================================================================

class JournalIdProto : public ::google::protobuf::Message {
 public:
  JournalIdProto();
  virtual ~JournalIdProto();

  JournalIdProto(const JournalIdProto& from);

  inline JournalIdProto& operator=(const JournalIdProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JournalIdProto& default_instance();

  void Swap(JournalIdProto* other);

  // implements Message ----------------------------------------------

  JournalIdProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JournalIdProto& from);
  void MergeFrom(const JournalIdProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string identifier = 1;
  inline bool has_identifier() const;
  inline void clear_identifier();
  static const int kIdentifierFieldNumber = 1;
  inline const ::std::string& identifier() const;
  inline void set_identifier(const ::std::string& value);
  inline void set_identifier(const char* value);
  inline void set_identifier(const char* value, size_t size);
  inline ::std::string* mutable_identifier();
  inline ::std::string* release_identifier();
  inline void set_allocated_identifier(::std::string* identifier);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.JournalIdProto)
 private:
  inline void set_has_identifier();
  inline void clear_has_identifier();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* identifier_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static JournalIdProto* default_instance_;
};
// -------------------------------------------------------------------

class RequestInfoProto : public ::google::protobuf::Message {
 public:
  RequestInfoProto();
  virtual ~RequestInfoProto();

  RequestInfoProto(const RequestInfoProto& from);

  inline RequestInfoProto& operator=(const RequestInfoProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RequestInfoProto& default_instance();

  void Swap(RequestInfoProto* other);

  // implements Message ----------------------------------------------

  RequestInfoProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RequestInfoProto& from);
  void MergeFrom(const RequestInfoProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto journalId = 1;
  inline bool has_journalid() const;
  inline void clear_journalid();
  static const int kJournalIdFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& journalid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_journalid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_journalid();
  inline void set_allocated_journalid(::hadoop::hdfs::qjournal::JournalIdProto* journalid);

  // required uint64 epoch = 2;
  inline bool has_epoch() const;
  inline void clear_epoch();
  static const int kEpochFieldNumber = 2;
  inline ::google::protobuf::uint64 epoch() const;
  inline void set_epoch(::google::protobuf::uint64 value);

  // required uint64 ipcSerialNumber = 3;
  inline bool has_ipcserialnumber() const;
  inline void clear_ipcserialnumber();
  static const int kIpcSerialNumberFieldNumber = 3;
  inline ::google::protobuf::uint64 ipcserialnumber() const;
  inline void set_ipcserialnumber(::google::protobuf::uint64 value);

  // optional uint64 committedTxId = 4;
  inline bool has_committedtxid() const;
  inline void clear_committedtxid();
  static const int kCommittedTxIdFieldNumber = 4;
  inline ::google::protobuf::uint64 committedtxid() const;
  inline void set_committedtxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.RequestInfoProto)
 private:
  inline void set_has_journalid();
  inline void clear_has_journalid();
  inline void set_has_epoch();
  inline void clear_has_epoch();
  inline void set_has_ipcserialnumber();
  inline void clear_has_ipcserialnumber();
  inline void set_has_committedtxid();
  inline void clear_has_committedtxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* journalid_;
  ::google::protobuf::uint64 epoch_;
  ::google::protobuf::uint64 ipcserialnumber_;
  ::google::protobuf::uint64 committedtxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static RequestInfoProto* default_instance_;
};
// -------------------------------------------------------------------

class SegmentStateProto : public ::google::protobuf::Message {
 public:
  SegmentStateProto();
  virtual ~SegmentStateProto();

  SegmentStateProto(const SegmentStateProto& from);

  inline SegmentStateProto& operator=(const SegmentStateProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SegmentStateProto& default_instance();

  void Swap(SegmentStateProto* other);

  // implements Message ----------------------------------------------

  SegmentStateProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SegmentStateProto& from);
  void MergeFrom(const SegmentStateProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 startTxId = 1;
  inline bool has_starttxid() const;
  inline void clear_starttxid();
  static const int kStartTxIdFieldNumber = 1;
  inline ::google::protobuf::uint64 starttxid() const;
  inline void set_starttxid(::google::protobuf::uint64 value);

  // required uint64 endTxId = 2;
  inline bool has_endtxid() const;
  inline void clear_endtxid();
  static const int kEndTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 endtxid() const;
  inline void set_endtxid(::google::protobuf::uint64 value);

  // required bool isInProgress = 3;
  inline bool has_isinprogress() const;
  inline void clear_isinprogress();
  static const int kIsInProgressFieldNumber = 3;
  inline bool isinprogress() const;
  inline void set_isinprogress(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.SegmentStateProto)
 private:
  inline void set_has_starttxid();
  inline void clear_has_starttxid();
  inline void set_has_endtxid();
  inline void clear_has_endtxid();
  inline void set_has_isinprogress();
  inline void clear_has_isinprogress();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 starttxid_;
  ::google::protobuf::uint64 endtxid_;
  bool isinprogress_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static SegmentStateProto* default_instance_;
};
// -------------------------------------------------------------------

class PersistedRecoveryPaxosData : public ::google::protobuf::Message {
 public:
  PersistedRecoveryPaxosData();
  virtual ~PersistedRecoveryPaxosData();

  PersistedRecoveryPaxosData(const PersistedRecoveryPaxosData& from);

  inline PersistedRecoveryPaxosData& operator=(const PersistedRecoveryPaxosData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PersistedRecoveryPaxosData& default_instance();

  void Swap(PersistedRecoveryPaxosData* other);

  // implements Message ----------------------------------------------

  PersistedRecoveryPaxosData* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PersistedRecoveryPaxosData& from);
  void MergeFrom(const PersistedRecoveryPaxosData& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
  inline bool has_segmentstate() const;
  inline void clear_segmentstate();
  static const int kSegmentStateFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::SegmentStateProto& segmentstate() const;
  inline ::hadoop::hdfs::qjournal::SegmentStateProto* mutable_segmentstate();
  inline ::hadoop::hdfs::qjournal::SegmentStateProto* release_segmentstate();
  inline void set_allocated_segmentstate(::hadoop::hdfs::qjournal::SegmentStateProto* segmentstate);

  // required uint64 acceptedInEpoch = 2;
  inline bool has_acceptedinepoch() const;
  inline void clear_acceptedinepoch();
  static const int kAcceptedInEpochFieldNumber = 2;
  inline ::google::protobuf::uint64 acceptedinepoch() const;
  inline void set_acceptedinepoch(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.PersistedRecoveryPaxosData)
 private:
  inline void set_has_segmentstate();
  inline void clear_has_segmentstate();
  inline void set_has_acceptedinepoch();
  inline void clear_has_acceptedinepoch();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::SegmentStateProto* segmentstate_;
  ::google::protobuf::uint64 acceptedinepoch_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static PersistedRecoveryPaxosData* default_instance_;
};
// -------------------------------------------------------------------

class JournalRequestProto : public ::google::protobuf::Message {
 public:
  JournalRequestProto();
  virtual ~JournalRequestProto();

  JournalRequestProto(const JournalRequestProto& from);

  inline JournalRequestProto& operator=(const JournalRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JournalRequestProto& default_instance();

  void Swap(JournalRequestProto* other);

  // implements Message ----------------------------------------------

  JournalRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JournalRequestProto& from);
  void MergeFrom(const JournalRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // required uint64 firstTxnId = 2;
  inline bool has_firsttxnid() const;
  inline void clear_firsttxnid();
  static const int kFirstTxnIdFieldNumber = 2;
  inline ::google::protobuf::uint64 firsttxnid() const;
  inline void set_firsttxnid(::google::protobuf::uint64 value);

  // required uint32 numTxns = 3;
  inline bool has_numtxns() const;
  inline void clear_numtxns();
  static const int kNumTxnsFieldNumber = 3;
  inline ::google::protobuf::uint32 numtxns() const;
  inline void set_numtxns(::google::protobuf::uint32 value);

  // required bytes records = 4;
  inline bool has_records() const;
  inline void clear_records();
  static const int kRecordsFieldNumber = 4;
  inline const ::std::string& records() const;
  inline void set_records(const ::std::string& value);
  inline void set_records(const char* value);
  inline void set_records(const void* value, size_t size);
  inline ::std::string* mutable_records();
  inline ::std::string* release_records();
  inline void set_allocated_records(::std::string* records);

  // required uint64 segmentTxnId = 5;
  inline bool has_segmenttxnid() const;
  inline void clear_segmenttxnid();
  static const int kSegmentTxnIdFieldNumber = 5;
  inline ::google::protobuf::uint64 segmenttxnid() const;
  inline void set_segmenttxnid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.JournalRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();
  inline void set_has_firsttxnid();
  inline void clear_has_firsttxnid();
  inline void set_has_numtxns();
  inline void clear_has_numtxns();
  inline void set_has_records();
  inline void clear_has_records();
  inline void set_has_segmenttxnid();
  inline void clear_has_segmenttxnid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;
  ::google::protobuf::uint64 firsttxnid_;
  ::std::string* records_;
  ::google::protobuf::uint64 segmenttxnid_;
  ::google::protobuf::uint32 numtxns_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(5 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static JournalRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class JournalResponseProto : public ::google::protobuf::Message {
 public:
  JournalResponseProto();
  virtual ~JournalResponseProto();

  JournalResponseProto(const JournalResponseProto& from);

  inline JournalResponseProto& operator=(const JournalResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JournalResponseProto& default_instance();

  void Swap(JournalResponseProto* other);

  // implements Message ----------------------------------------------

  JournalResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JournalResponseProto& from);
  void MergeFrom(const JournalResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.JournalResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static JournalResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class HeartbeatRequestProto : public ::google::protobuf::Message {
 public:
  HeartbeatRequestProto();
  virtual ~HeartbeatRequestProto();

  HeartbeatRequestProto(const HeartbeatRequestProto& from);

  inline HeartbeatRequestProto& operator=(const HeartbeatRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const HeartbeatRequestProto& default_instance();

  void Swap(HeartbeatRequestProto* other);

  // implements Message ----------------------------------------------

  HeartbeatRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const HeartbeatRequestProto& from);
  void MergeFrom(const HeartbeatRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.HeartbeatRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static HeartbeatRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class HeartbeatResponseProto : public ::google::protobuf::Message {
 public:
  HeartbeatResponseProto();
  virtual ~HeartbeatResponseProto();

  HeartbeatResponseProto(const HeartbeatResponseProto& from);

  inline HeartbeatResponseProto& operator=(const HeartbeatResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const HeartbeatResponseProto& default_instance();

  void Swap(HeartbeatResponseProto* other);

  // implements Message ----------------------------------------------

  HeartbeatResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const HeartbeatResponseProto& from);
  void MergeFrom(const HeartbeatResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.HeartbeatResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static HeartbeatResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class StartLogSegmentRequestProto : public ::google::protobuf::Message {
 public:
  StartLogSegmentRequestProto();
  virtual ~StartLogSegmentRequestProto();

  StartLogSegmentRequestProto(const StartLogSegmentRequestProto& from);

  inline StartLogSegmentRequestProto& operator=(const StartLogSegmentRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StartLogSegmentRequestProto& default_instance();

  void Swap(StartLogSegmentRequestProto* other);

  // implements Message ----------------------------------------------

  StartLogSegmentRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StartLogSegmentRequestProto& from);
  void MergeFrom(const StartLogSegmentRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // required uint64 txid = 2;
  inline bool has_txid() const;
  inline void clear_txid();
  static const int kTxidFieldNumber = 2;
  inline ::google::protobuf::uint64 txid() const;
  inline void set_txid(::google::protobuf::uint64 value);

  // optional sint32 layoutVersion = 3;
  inline bool has_layoutversion() const;
  inline void clear_layoutversion();
  static const int kLayoutVersionFieldNumber = 3;
  inline ::google::protobuf::int32 layoutversion() const;
  inline void set_layoutversion(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.StartLogSegmentRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();
  inline void set_has_txid();
  inline void clear_has_txid();
  inline void set_has_layoutversion();
  inline void clear_has_layoutversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;
  ::google::protobuf::uint64 txid_;
  ::google::protobuf::int32 layoutversion_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static StartLogSegmentRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class StartLogSegmentResponseProto : public ::google::protobuf::Message {
 public:
  StartLogSegmentResponseProto();
  virtual ~StartLogSegmentResponseProto();

  StartLogSegmentResponseProto(const StartLogSegmentResponseProto& from);

  inline StartLogSegmentResponseProto& operator=(const StartLogSegmentResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StartLogSegmentResponseProto& default_instance();

  void Swap(StartLogSegmentResponseProto* other);

  // implements Message ----------------------------------------------

  StartLogSegmentResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StartLogSegmentResponseProto& from);
  void MergeFrom(const StartLogSegmentResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.StartLogSegmentResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static StartLogSegmentResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class FinalizeLogSegmentRequestProto : public ::google::protobuf::Message {
 public:
  FinalizeLogSegmentRequestProto();
  virtual ~FinalizeLogSegmentRequestProto();

  FinalizeLogSegmentRequestProto(const FinalizeLogSegmentRequestProto& from);

  inline FinalizeLogSegmentRequestProto& operator=(const FinalizeLogSegmentRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FinalizeLogSegmentRequestProto& default_instance();

  void Swap(FinalizeLogSegmentRequestProto* other);

  // implements Message ----------------------------------------------

  FinalizeLogSegmentRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FinalizeLogSegmentRequestProto& from);
  void MergeFrom(const FinalizeLogSegmentRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // required uint64 startTxId = 2;
  inline bool has_starttxid() const;
  inline void clear_starttxid();
  static const int kStartTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 starttxid() const;
  inline void set_starttxid(::google::protobuf::uint64 value);

  // required uint64 endTxId = 3;
  inline bool has_endtxid() const;
  inline void clear_endtxid();
  static const int kEndTxIdFieldNumber = 3;
  inline ::google::protobuf::uint64 endtxid() const;
  inline void set_endtxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.FinalizeLogSegmentRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();
  inline void set_has_starttxid();
  inline void clear_has_starttxid();
  inline void set_has_endtxid();
  inline void clear_has_endtxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;
  ::google::protobuf::uint64 starttxid_;
  ::google::protobuf::uint64 endtxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static FinalizeLogSegmentRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class FinalizeLogSegmentResponseProto : public ::google::protobuf::Message {
 public:
  FinalizeLogSegmentResponseProto();
  virtual ~FinalizeLogSegmentResponseProto();

  FinalizeLogSegmentResponseProto(const FinalizeLogSegmentResponseProto& from);

  inline FinalizeLogSegmentResponseProto& operator=(const FinalizeLogSegmentResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FinalizeLogSegmentResponseProto& default_instance();

  void Swap(FinalizeLogSegmentResponseProto* other);

  // implements Message ----------------------------------------------

  FinalizeLogSegmentResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FinalizeLogSegmentResponseProto& from);
  void MergeFrom(const FinalizeLogSegmentResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.FinalizeLogSegmentResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static FinalizeLogSegmentResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class PurgeLogsRequestProto : public ::google::protobuf::Message {
 public:
  PurgeLogsRequestProto();
  virtual ~PurgeLogsRequestProto();

  PurgeLogsRequestProto(const PurgeLogsRequestProto& from);

  inline PurgeLogsRequestProto& operator=(const PurgeLogsRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PurgeLogsRequestProto& default_instance();

  void Swap(PurgeLogsRequestProto* other);

  // implements Message ----------------------------------------------

  PurgeLogsRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PurgeLogsRequestProto& from);
  void MergeFrom(const PurgeLogsRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // required uint64 minTxIdToKeep = 2;
  inline bool has_mintxidtokeep() const;
  inline void clear_mintxidtokeep();
  static const int kMinTxIdToKeepFieldNumber = 2;
  inline ::google::protobuf::uint64 mintxidtokeep() const;
  inline void set_mintxidtokeep(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.PurgeLogsRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();
  inline void set_has_mintxidtokeep();
  inline void clear_has_mintxidtokeep();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;
  ::google::protobuf::uint64 mintxidtokeep_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static PurgeLogsRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class PurgeLogsResponseProto : public ::google::protobuf::Message {
 public:
  PurgeLogsResponseProto();
  virtual ~PurgeLogsResponseProto();

  PurgeLogsResponseProto(const PurgeLogsResponseProto& from);

  inline PurgeLogsResponseProto& operator=(const PurgeLogsResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PurgeLogsResponseProto& default_instance();

  void Swap(PurgeLogsResponseProto* other);

  // implements Message ----------------------------------------------

  PurgeLogsResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PurgeLogsResponseProto& from);
  void MergeFrom(const PurgeLogsResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.PurgeLogsResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static PurgeLogsResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class IsFormattedRequestProto : public ::google::protobuf::Message {
 public:
  IsFormattedRequestProto();
  virtual ~IsFormattedRequestProto();

  IsFormattedRequestProto(const IsFormattedRequestProto& from);

  inline IsFormattedRequestProto& operator=(const IsFormattedRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const IsFormattedRequestProto& default_instance();

  void Swap(IsFormattedRequestProto* other);

  // implements Message ----------------------------------------------

  IsFormattedRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const IsFormattedRequestProto& from);
  void MergeFrom(const IsFormattedRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.IsFormattedRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static IsFormattedRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class IsFormattedResponseProto : public ::google::protobuf::Message {
 public:
  IsFormattedResponseProto();
  virtual ~IsFormattedResponseProto();

  IsFormattedResponseProto(const IsFormattedResponseProto& from);

  inline IsFormattedResponseProto& operator=(const IsFormattedResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const IsFormattedResponseProto& default_instance();

  void Swap(IsFormattedResponseProto* other);

  // implements Message ----------------------------------------------

  IsFormattedResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const IsFormattedResponseProto& from);
  void MergeFrom(const IsFormattedResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bool isFormatted = 1;
  inline bool has_isformatted() const;
  inline void clear_isformatted();
  static const int kIsFormattedFieldNumber = 1;
  inline bool isformatted() const;
  inline void set_isformatted(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.IsFormattedResponseProto)
 private:
  inline void set_has_isformatted();
  inline void clear_has_isformatted();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  bool isformatted_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static IsFormattedResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class DiscardSegmentsRequestProto : public ::google::protobuf::Message {
 public:
  DiscardSegmentsRequestProto();
  virtual ~DiscardSegmentsRequestProto();

  DiscardSegmentsRequestProto(const DiscardSegmentsRequestProto& from);

  inline DiscardSegmentsRequestProto& operator=(const DiscardSegmentsRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DiscardSegmentsRequestProto& default_instance();

  void Swap(DiscardSegmentsRequestProto* other);

  // implements Message ----------------------------------------------

  DiscardSegmentsRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DiscardSegmentsRequestProto& from);
  void MergeFrom(const DiscardSegmentsRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // required uint64 startTxId = 2;
  inline bool has_starttxid() const;
  inline void clear_starttxid();
  static const int kStartTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 starttxid() const;
  inline void set_starttxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DiscardSegmentsRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();
  inline void set_has_starttxid();
  inline void clear_has_starttxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;
  ::google::protobuf::uint64 starttxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DiscardSegmentsRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class DiscardSegmentsResponseProto : public ::google::protobuf::Message {
 public:
  DiscardSegmentsResponseProto();
  virtual ~DiscardSegmentsResponseProto();

  DiscardSegmentsResponseProto(const DiscardSegmentsResponseProto& from);

  inline DiscardSegmentsResponseProto& operator=(const DiscardSegmentsResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DiscardSegmentsResponseProto& default_instance();

  void Swap(DiscardSegmentsResponseProto* other);

  // implements Message ----------------------------------------------

  DiscardSegmentsResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DiscardSegmentsResponseProto& from);
  void MergeFrom(const DiscardSegmentsResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DiscardSegmentsResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DiscardSegmentsResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetJournalCTimeRequestProto : public ::google::protobuf::Message {
 public:
  GetJournalCTimeRequestProto();
  virtual ~GetJournalCTimeRequestProto();

  GetJournalCTimeRequestProto(const GetJournalCTimeRequestProto& from);

  inline GetJournalCTimeRequestProto& operator=(const GetJournalCTimeRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetJournalCTimeRequestProto& default_instance();

  void Swap(GetJournalCTimeRequestProto* other);

  // implements Message ----------------------------------------------

  GetJournalCTimeRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetJournalCTimeRequestProto& from);
  void MergeFrom(const GetJournalCTimeRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.GetJournalCTimeRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetJournalCTimeRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetJournalCTimeResponseProto : public ::google::protobuf::Message {
 public:
  GetJournalCTimeResponseProto();
  virtual ~GetJournalCTimeResponseProto();

  GetJournalCTimeResponseProto(const GetJournalCTimeResponseProto& from);

  inline GetJournalCTimeResponseProto& operator=(const GetJournalCTimeResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetJournalCTimeResponseProto& default_instance();

  void Swap(GetJournalCTimeResponseProto* other);

  // implements Message ----------------------------------------------

  GetJournalCTimeResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetJournalCTimeResponseProto& from);
  void MergeFrom(const GetJournalCTimeResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int64 resultCTime = 1;
  inline bool has_resultctime() const;
  inline void clear_resultctime();
  static const int kResultCTimeFieldNumber = 1;
  inline ::google::protobuf::int64 resultctime() const;
  inline void set_resultctime(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.GetJournalCTimeResponseProto)
 private:
  inline void set_has_resultctime();
  inline void clear_has_resultctime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::int64 resultctime_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetJournalCTimeResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class DoPreUpgradeRequestProto : public ::google::protobuf::Message {
 public:
  DoPreUpgradeRequestProto();
  virtual ~DoPreUpgradeRequestProto();

  DoPreUpgradeRequestProto(const DoPreUpgradeRequestProto& from);

  inline DoPreUpgradeRequestProto& operator=(const DoPreUpgradeRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoPreUpgradeRequestProto& default_instance();

  void Swap(DoPreUpgradeRequestProto* other);

  // implements Message ----------------------------------------------

  DoPreUpgradeRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoPreUpgradeRequestProto& from);
  void MergeFrom(const DoPreUpgradeRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoPreUpgradeRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoPreUpgradeRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class DoPreUpgradeResponseProto : public ::google::protobuf::Message {
 public:
  DoPreUpgradeResponseProto();
  virtual ~DoPreUpgradeResponseProto();

  DoPreUpgradeResponseProto(const DoPreUpgradeResponseProto& from);

  inline DoPreUpgradeResponseProto& operator=(const DoPreUpgradeResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoPreUpgradeResponseProto& default_instance();

  void Swap(DoPreUpgradeResponseProto* other);

  // implements Message ----------------------------------------------

  DoPreUpgradeResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoPreUpgradeResponseProto& from);
  void MergeFrom(const DoPreUpgradeResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoPreUpgradeResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoPreUpgradeResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class DoUpgradeRequestProto : public ::google::protobuf::Message {
 public:
  DoUpgradeRequestProto();
  virtual ~DoUpgradeRequestProto();

  DoUpgradeRequestProto(const DoUpgradeRequestProto& from);

  inline DoUpgradeRequestProto& operator=(const DoUpgradeRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoUpgradeRequestProto& default_instance();

  void Swap(DoUpgradeRequestProto* other);

  // implements Message ----------------------------------------------

  DoUpgradeRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoUpgradeRequestProto& from);
  void MergeFrom(const DoUpgradeRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // required .hadoop.hdfs.StorageInfoProto sInfo = 2;
  inline bool has_sinfo() const;
  inline void clear_sinfo();
  static const int kSInfoFieldNumber = 2;
  inline const ::hadoop::hdfs::StorageInfoProto& sinfo() const;
  inline ::hadoop::hdfs::StorageInfoProto* mutable_sinfo();
  inline ::hadoop::hdfs::StorageInfoProto* release_sinfo();
  inline void set_allocated_sinfo(::hadoop::hdfs::StorageInfoProto* sinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoUpgradeRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();
  inline void set_has_sinfo();
  inline void clear_has_sinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;
  ::hadoop::hdfs::StorageInfoProto* sinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoUpgradeRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class DoUpgradeResponseProto : public ::google::protobuf::Message {
 public:
  DoUpgradeResponseProto();
  virtual ~DoUpgradeResponseProto();

  DoUpgradeResponseProto(const DoUpgradeResponseProto& from);

  inline DoUpgradeResponseProto& operator=(const DoUpgradeResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoUpgradeResponseProto& default_instance();

  void Swap(DoUpgradeResponseProto* other);

  // implements Message ----------------------------------------------

  DoUpgradeResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoUpgradeResponseProto& from);
  void MergeFrom(const DoUpgradeResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoUpgradeResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoUpgradeResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class DoFinalizeRequestProto : public ::google::protobuf::Message {
 public:
  DoFinalizeRequestProto();
  virtual ~DoFinalizeRequestProto();

  DoFinalizeRequestProto(const DoFinalizeRequestProto& from);

  inline DoFinalizeRequestProto& operator=(const DoFinalizeRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoFinalizeRequestProto& default_instance();

  void Swap(DoFinalizeRequestProto* other);

  // implements Message ----------------------------------------------

  DoFinalizeRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoFinalizeRequestProto& from);
  void MergeFrom(const DoFinalizeRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoFinalizeRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoFinalizeRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class DoFinalizeResponseProto : public ::google::protobuf::Message {
 public:
  DoFinalizeResponseProto();
  virtual ~DoFinalizeResponseProto();

  DoFinalizeResponseProto(const DoFinalizeResponseProto& from);

  inline DoFinalizeResponseProto& operator=(const DoFinalizeResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoFinalizeResponseProto& default_instance();

  void Swap(DoFinalizeResponseProto* other);

  // implements Message ----------------------------------------------

  DoFinalizeResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoFinalizeResponseProto& from);
  void MergeFrom(const DoFinalizeResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoFinalizeResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoFinalizeResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class CanRollBackRequestProto : public ::google::protobuf::Message {
 public:
  CanRollBackRequestProto();
  virtual ~CanRollBackRequestProto();

  CanRollBackRequestProto(const CanRollBackRequestProto& from);

  inline CanRollBackRequestProto& operator=(const CanRollBackRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CanRollBackRequestProto& default_instance();

  void Swap(CanRollBackRequestProto* other);

  // implements Message ----------------------------------------------

  CanRollBackRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CanRollBackRequestProto& from);
  void MergeFrom(const CanRollBackRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // required .hadoop.hdfs.StorageInfoProto storage = 2;
  inline bool has_storage() const;
  inline void clear_storage();
  static const int kStorageFieldNumber = 2;
  inline const ::hadoop::hdfs::StorageInfoProto& storage() const;
  inline ::hadoop::hdfs::StorageInfoProto* mutable_storage();
  inline ::hadoop::hdfs::StorageInfoProto* release_storage();
  inline void set_allocated_storage(::hadoop::hdfs::StorageInfoProto* storage);

  // required .hadoop.hdfs.StorageInfoProto prevStorage = 3;
  inline bool has_prevstorage() const;
  inline void clear_prevstorage();
  static const int kPrevStorageFieldNumber = 3;
  inline const ::hadoop::hdfs::StorageInfoProto& prevstorage() const;
  inline ::hadoop::hdfs::StorageInfoProto* mutable_prevstorage();
  inline ::hadoop::hdfs::StorageInfoProto* release_prevstorage();
  inline void set_allocated_prevstorage(::hadoop::hdfs::StorageInfoProto* prevstorage);

  // required int32 targetLayoutVersion = 4;
  inline bool has_targetlayoutversion() const;
  inline void clear_targetlayoutversion();
  static const int kTargetLayoutVersionFieldNumber = 4;
  inline ::google::protobuf::int32 targetlayoutversion() const;
  inline void set_targetlayoutversion(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.CanRollBackRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();
  inline void set_has_storage();
  inline void clear_has_storage();
  inline void set_has_prevstorage();
  inline void clear_has_prevstorage();
  inline void set_has_targetlayoutversion();
  inline void clear_has_targetlayoutversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;
  ::hadoop::hdfs::StorageInfoProto* storage_;
  ::hadoop::hdfs::StorageInfoProto* prevstorage_;
  ::google::protobuf::int32 targetlayoutversion_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static CanRollBackRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class CanRollBackResponseProto : public ::google::protobuf::Message {
 public:
  CanRollBackResponseProto();
  virtual ~CanRollBackResponseProto();

  CanRollBackResponseProto(const CanRollBackResponseProto& from);

  inline CanRollBackResponseProto& operator=(const CanRollBackResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CanRollBackResponseProto& default_instance();

  void Swap(CanRollBackResponseProto* other);

  // implements Message ----------------------------------------------

  CanRollBackResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CanRollBackResponseProto& from);
  void MergeFrom(const CanRollBackResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bool canRollBack = 1;
  inline bool has_canrollback() const;
  inline void clear_canrollback();
  static const int kCanRollBackFieldNumber = 1;
  inline bool canrollback() const;
  inline void set_canrollback(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.CanRollBackResponseProto)
 private:
  inline void set_has_canrollback();
  inline void clear_has_canrollback();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  bool canrollback_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static CanRollBackResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class DoRollbackRequestProto : public ::google::protobuf::Message {
 public:
  DoRollbackRequestProto();
  virtual ~DoRollbackRequestProto();

  DoRollbackRequestProto(const DoRollbackRequestProto& from);

  inline DoRollbackRequestProto& operator=(const DoRollbackRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoRollbackRequestProto& default_instance();

  void Swap(DoRollbackRequestProto* other);

  // implements Message ----------------------------------------------

  DoRollbackRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoRollbackRequestProto& from);
  void MergeFrom(const DoRollbackRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoRollbackRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoRollbackRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class DoRollbackResponseProto : public ::google::protobuf::Message {
 public:
  DoRollbackResponseProto();
  virtual ~DoRollbackResponseProto();

  DoRollbackResponseProto(const DoRollbackResponseProto& from);

  inline DoRollbackResponseProto& operator=(const DoRollbackResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoRollbackResponseProto& default_instance();

  void Swap(DoRollbackResponseProto* other);

  // implements Message ----------------------------------------------

  DoRollbackResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoRollbackResponseProto& from);
  void MergeFrom(const DoRollbackResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.DoRollbackResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static DoRollbackResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetJournalStateRequestProto : public ::google::protobuf::Message {
 public:
  GetJournalStateRequestProto();
  virtual ~GetJournalStateRequestProto();

  GetJournalStateRequestProto(const GetJournalStateRequestProto& from);

  inline GetJournalStateRequestProto& operator=(const GetJournalStateRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetJournalStateRequestProto& default_instance();

  void Swap(GetJournalStateRequestProto* other);

  // implements Message ----------------------------------------------

  GetJournalStateRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetJournalStateRequestProto& from);
  void MergeFrom(const GetJournalStateRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.GetJournalStateRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetJournalStateRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetJournalStateResponseProto : public ::google::protobuf::Message {
 public:
  GetJournalStateResponseProto();
  virtual ~GetJournalStateResponseProto();

  GetJournalStateResponseProto(const GetJournalStateResponseProto& from);

  inline GetJournalStateResponseProto& operator=(const GetJournalStateResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetJournalStateResponseProto& default_instance();

  void Swap(GetJournalStateResponseProto* other);

  // implements Message ----------------------------------------------

  GetJournalStateResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetJournalStateResponseProto& from);
  void MergeFrom(const GetJournalStateResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 lastPromisedEpoch = 1;
  inline bool has_lastpromisedepoch() const;
  inline void clear_lastpromisedepoch();
  static const int kLastPromisedEpochFieldNumber = 1;
  inline ::google::protobuf::uint64 lastpromisedepoch() const;
  inline void set_lastpromisedepoch(::google::protobuf::uint64 value);

  // required uint32 httpPort = 2;
  inline bool has_httpport() const;
  inline void clear_httpport();
  static const int kHttpPortFieldNumber = 2;
  inline ::google::protobuf::uint32 httpport() const;
  inline void set_httpport(::google::protobuf::uint32 value);

  // optional string fromURL = 3;
  inline bool has_fromurl() const;
  inline void clear_fromurl();
  static const int kFromURLFieldNumber = 3;
  inline const ::std::string& fromurl() const;
  inline void set_fromurl(const ::std::string& value);
  inline void set_fromurl(const char* value);
  inline void set_fromurl(const char* value, size_t size);
  inline ::std::string* mutable_fromurl();
  inline ::std::string* release_fromurl();
  inline void set_allocated_fromurl(::std::string* fromurl);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.GetJournalStateResponseProto)
 private:
  inline void set_has_lastpromisedepoch();
  inline void clear_has_lastpromisedepoch();
  inline void set_has_httpport();
  inline void clear_has_httpport();
  inline void set_has_fromurl();
  inline void clear_has_fromurl();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 lastpromisedepoch_;
  ::std::string* fromurl_;
  ::google::protobuf::uint32 httpport_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetJournalStateResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class FormatRequestProto : public ::google::protobuf::Message {
 public:
  FormatRequestProto();
  virtual ~FormatRequestProto();

  FormatRequestProto(const FormatRequestProto& from);

  inline FormatRequestProto& operator=(const FormatRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FormatRequestProto& default_instance();

  void Swap(FormatRequestProto* other);

  // implements Message ----------------------------------------------

  FormatRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FormatRequestProto& from);
  void MergeFrom(const FormatRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
  inline bool has_nsinfo() const;
  inline void clear_nsinfo();
  static const int kNsInfoFieldNumber = 2;
  inline const ::hadoop::hdfs::NamespaceInfoProto& nsinfo() const;
  inline ::hadoop::hdfs::NamespaceInfoProto* mutable_nsinfo();
  inline ::hadoop::hdfs::NamespaceInfoProto* release_nsinfo();
  inline void set_allocated_nsinfo(::hadoop::hdfs::NamespaceInfoProto* nsinfo);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.FormatRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();
  inline void set_has_nsinfo();
  inline void clear_has_nsinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;
  ::hadoop::hdfs::NamespaceInfoProto* nsinfo_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static FormatRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class FormatResponseProto : public ::google::protobuf::Message {
 public:
  FormatResponseProto();
  virtual ~FormatResponseProto();

  FormatResponseProto(const FormatResponseProto& from);

  inline FormatResponseProto& operator=(const FormatResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FormatResponseProto& default_instance();

  void Swap(FormatResponseProto* other);

  // implements Message ----------------------------------------------

  FormatResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FormatResponseProto& from);
  void MergeFrom(const FormatResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.FormatResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static FormatResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class NewEpochRequestProto : public ::google::protobuf::Message {
 public:
  NewEpochRequestProto();
  virtual ~NewEpochRequestProto();

  NewEpochRequestProto(const NewEpochRequestProto& from);

  inline NewEpochRequestProto& operator=(const NewEpochRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NewEpochRequestProto& default_instance();

  void Swap(NewEpochRequestProto* other);

  // implements Message ----------------------------------------------

  NewEpochRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NewEpochRequestProto& from);
  void MergeFrom(const NewEpochRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
  inline bool has_nsinfo() const;
  inline void clear_nsinfo();
  static const int kNsInfoFieldNumber = 2;
  inline const ::hadoop::hdfs::NamespaceInfoProto& nsinfo() const;
  inline ::hadoop::hdfs::NamespaceInfoProto* mutable_nsinfo();
  inline ::hadoop::hdfs::NamespaceInfoProto* release_nsinfo();
  inline void set_allocated_nsinfo(::hadoop::hdfs::NamespaceInfoProto* nsinfo);

  // required uint64 epoch = 3;
  inline bool has_epoch() const;
  inline void clear_epoch();
  static const int kEpochFieldNumber = 3;
  inline ::google::protobuf::uint64 epoch() const;
  inline void set_epoch(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.NewEpochRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();
  inline void set_has_nsinfo();
  inline void clear_has_nsinfo();
  inline void set_has_epoch();
  inline void clear_has_epoch();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;
  ::hadoop::hdfs::NamespaceInfoProto* nsinfo_;
  ::google::protobuf::uint64 epoch_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static NewEpochRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class NewEpochResponseProto : public ::google::protobuf::Message {
 public:
  NewEpochResponseProto();
  virtual ~NewEpochResponseProto();

  NewEpochResponseProto(const NewEpochResponseProto& from);

  inline NewEpochResponseProto& operator=(const NewEpochResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NewEpochResponseProto& default_instance();

  void Swap(NewEpochResponseProto* other);

  // implements Message ----------------------------------------------

  NewEpochResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NewEpochResponseProto& from);
  void MergeFrom(const NewEpochResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 lastSegmentTxId = 1;
  inline bool has_lastsegmenttxid() const;
  inline void clear_lastsegmenttxid();
  static const int kLastSegmentTxIdFieldNumber = 1;
  inline ::google::protobuf::uint64 lastsegmenttxid() const;
  inline void set_lastsegmenttxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.NewEpochResponseProto)
 private:
  inline void set_has_lastsegmenttxid();
  inline void clear_has_lastsegmenttxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 lastsegmenttxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static NewEpochResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class GetEditLogManifestRequestProto : public ::google::protobuf::Message {
 public:
  GetEditLogManifestRequestProto();
  virtual ~GetEditLogManifestRequestProto();

  GetEditLogManifestRequestProto(const GetEditLogManifestRequestProto& from);

  inline GetEditLogManifestRequestProto& operator=(const GetEditLogManifestRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetEditLogManifestRequestProto& default_instance();

  void Swap(GetEditLogManifestRequestProto* other);

  // implements Message ----------------------------------------------

  GetEditLogManifestRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetEditLogManifestRequestProto& from);
  void MergeFrom(const GetEditLogManifestRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
  inline bool has_jid() const;
  inline void clear_jid();
  static const int kJidFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::JournalIdProto& jid() const;
  inline ::hadoop::hdfs::qjournal::JournalIdProto* mutable_jid();
  inline ::hadoop::hdfs::qjournal::JournalIdProto* release_jid();
  inline void set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid);

  // required uint64 sinceTxId = 2;
  inline bool has_sincetxid() const;
  inline void clear_sincetxid();
  static const int kSinceTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 sincetxid() const;
  inline void set_sincetxid(::google::protobuf::uint64 value);

  // optional bool inProgressOk = 4 [default = false];
  inline bool has_inprogressok() const;
  inline void clear_inprogressok();
  static const int kInProgressOkFieldNumber = 4;
  inline bool inprogressok() const;
  inline void set_inprogressok(bool value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.GetEditLogManifestRequestProto)
 private:
  inline void set_has_jid();
  inline void clear_has_jid();
  inline void set_has_sincetxid();
  inline void clear_has_sincetxid();
  inline void set_has_inprogressok();
  inline void clear_has_inprogressok();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::JournalIdProto* jid_;
  ::google::protobuf::uint64 sincetxid_;
  bool inprogressok_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetEditLogManifestRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class GetEditLogManifestResponseProto : public ::google::protobuf::Message {
 public:
  GetEditLogManifestResponseProto();
  virtual ~GetEditLogManifestResponseProto();

  GetEditLogManifestResponseProto(const GetEditLogManifestResponseProto& from);

  inline GetEditLogManifestResponseProto& operator=(const GetEditLogManifestResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetEditLogManifestResponseProto& default_instance();

  void Swap(GetEditLogManifestResponseProto* other);

  // implements Message ----------------------------------------------

  GetEditLogManifestResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const GetEditLogManifestResponseProto& from);
  void MergeFrom(const GetEditLogManifestResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
  inline bool has_manifest() const;
  inline void clear_manifest();
  static const int kManifestFieldNumber = 1;
  inline const ::hadoop::hdfs::RemoteEditLogManifestProto& manifest() const;
  inline ::hadoop::hdfs::RemoteEditLogManifestProto* mutable_manifest();
  inline ::hadoop::hdfs::RemoteEditLogManifestProto* release_manifest();
  inline void set_allocated_manifest(::hadoop::hdfs::RemoteEditLogManifestProto* manifest);

  // required uint32 httpPort = 2;
  inline bool has_httpport() const;
  inline void clear_httpport();
  static const int kHttpPortFieldNumber = 2;
  inline ::google::protobuf::uint32 httpport() const;
  inline void set_httpport(::google::protobuf::uint32 value);

  // optional string fromURL = 3;
  inline bool has_fromurl() const;
  inline void clear_fromurl();
  static const int kFromURLFieldNumber = 3;
  inline const ::std::string& fromurl() const;
  inline void set_fromurl(const ::std::string& value);
  inline void set_fromurl(const char* value);
  inline void set_fromurl(const char* value, size_t size);
  inline ::std::string* mutable_fromurl();
  inline ::std::string* release_fromurl();
  inline void set_allocated_fromurl(::std::string* fromurl);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.GetEditLogManifestResponseProto)
 private:
  inline void set_has_manifest();
  inline void clear_has_manifest();
  inline void set_has_httpport();
  inline void clear_has_httpport();
  inline void set_has_fromurl();
  inline void clear_has_fromurl();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::RemoteEditLogManifestProto* manifest_;
  ::std::string* fromurl_;
  ::google::protobuf::uint32 httpport_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static GetEditLogManifestResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class PrepareRecoveryRequestProto : public ::google::protobuf::Message {
 public:
  PrepareRecoveryRequestProto();
  virtual ~PrepareRecoveryRequestProto();

  PrepareRecoveryRequestProto(const PrepareRecoveryRequestProto& from);

  inline PrepareRecoveryRequestProto& operator=(const PrepareRecoveryRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PrepareRecoveryRequestProto& default_instance();

  void Swap(PrepareRecoveryRequestProto* other);

  // implements Message ----------------------------------------------

  PrepareRecoveryRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PrepareRecoveryRequestProto& from);
  void MergeFrom(const PrepareRecoveryRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // required uint64 segmentTxId = 2;
  inline bool has_segmenttxid() const;
  inline void clear_segmenttxid();
  static const int kSegmentTxIdFieldNumber = 2;
  inline ::google::protobuf::uint64 segmenttxid() const;
  inline void set_segmenttxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.PrepareRecoveryRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();
  inline void set_has_segmenttxid();
  inline void clear_has_segmenttxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;
  ::google::protobuf::uint64 segmenttxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static PrepareRecoveryRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class PrepareRecoveryResponseProto : public ::google::protobuf::Message {
 public:
  PrepareRecoveryResponseProto();
  virtual ~PrepareRecoveryResponseProto();

  PrepareRecoveryResponseProto(const PrepareRecoveryResponseProto& from);

  inline PrepareRecoveryResponseProto& operator=(const PrepareRecoveryResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PrepareRecoveryResponseProto& default_instance();

  void Swap(PrepareRecoveryResponseProto* other);

  // implements Message ----------------------------------------------

  PrepareRecoveryResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PrepareRecoveryResponseProto& from);
  void MergeFrom(const PrepareRecoveryResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
  inline bool has_segmentstate() const;
  inline void clear_segmentstate();
  static const int kSegmentStateFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::SegmentStateProto& segmentstate() const;
  inline ::hadoop::hdfs::qjournal::SegmentStateProto* mutable_segmentstate();
  inline ::hadoop::hdfs::qjournal::SegmentStateProto* release_segmentstate();
  inline void set_allocated_segmentstate(::hadoop::hdfs::qjournal::SegmentStateProto* segmentstate);

  // optional uint64 acceptedInEpoch = 2;
  inline bool has_acceptedinepoch() const;
  inline void clear_acceptedinepoch();
  static const int kAcceptedInEpochFieldNumber = 2;
  inline ::google::protobuf::uint64 acceptedinepoch() const;
  inline void set_acceptedinepoch(::google::protobuf::uint64 value);

  // required uint64 lastWriterEpoch = 3;
  inline bool has_lastwriterepoch() const;
  inline void clear_lastwriterepoch();
  static const int kLastWriterEpochFieldNumber = 3;
  inline ::google::protobuf::uint64 lastwriterepoch() const;
  inline void set_lastwriterepoch(::google::protobuf::uint64 value);

  // optional uint64 lastCommittedTxId = 4;
  inline bool has_lastcommittedtxid() const;
  inline void clear_lastcommittedtxid();
  static const int kLastCommittedTxIdFieldNumber = 4;
  inline ::google::protobuf::uint64 lastcommittedtxid() const;
  inline void set_lastcommittedtxid(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.PrepareRecoveryResponseProto)
 private:
  inline void set_has_segmentstate();
  inline void clear_has_segmentstate();
  inline void set_has_acceptedinepoch();
  inline void clear_has_acceptedinepoch();
  inline void set_has_lastwriterepoch();
  inline void clear_has_lastwriterepoch();
  inline void set_has_lastcommittedtxid();
  inline void clear_has_lastcommittedtxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::SegmentStateProto* segmentstate_;
  ::google::protobuf::uint64 acceptedinepoch_;
  ::google::protobuf::uint64 lastwriterepoch_;
  ::google::protobuf::uint64 lastcommittedtxid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static PrepareRecoveryResponseProto* default_instance_;
};
// -------------------------------------------------------------------

class AcceptRecoveryRequestProto : public ::google::protobuf::Message {
 public:
  AcceptRecoveryRequestProto();
  virtual ~AcceptRecoveryRequestProto();

  AcceptRecoveryRequestProto(const AcceptRecoveryRequestProto& from);

  inline AcceptRecoveryRequestProto& operator=(const AcceptRecoveryRequestProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AcceptRecoveryRequestProto& default_instance();

  void Swap(AcceptRecoveryRequestProto* other);

  // implements Message ----------------------------------------------

  AcceptRecoveryRequestProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AcceptRecoveryRequestProto& from);
  void MergeFrom(const AcceptRecoveryRequestProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
  inline bool has_reqinfo() const;
  inline void clear_reqinfo();
  static const int kReqInfoFieldNumber = 1;
  inline const ::hadoop::hdfs::qjournal::RequestInfoProto& reqinfo() const;
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* mutable_reqinfo();
  inline ::hadoop::hdfs::qjournal::RequestInfoProto* release_reqinfo();
  inline void set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo);

  // required .hadoop.hdfs.qjournal.SegmentStateProto stateToAccept = 2;
  inline bool has_statetoaccept() const;
  inline void clear_statetoaccept();
  static const int kStateToAcceptFieldNumber = 2;
  inline const ::hadoop::hdfs::qjournal::SegmentStateProto& statetoaccept() const;
  inline ::hadoop::hdfs::qjournal::SegmentStateProto* mutable_statetoaccept();
  inline ::hadoop::hdfs::qjournal::SegmentStateProto* release_statetoaccept();
  inline void set_allocated_statetoaccept(::hadoop::hdfs::qjournal::SegmentStateProto* statetoaccept);

  // required string fromURL = 3;
  inline bool has_fromurl() const;
  inline void clear_fromurl();
  static const int kFromURLFieldNumber = 3;
  inline const ::std::string& fromurl() const;
  inline void set_fromurl(const ::std::string& value);
  inline void set_fromurl(const char* value);
  inline void set_fromurl(const char* value, size_t size);
  inline ::std::string* mutable_fromurl();
  inline ::std::string* release_fromurl();
  inline void set_allocated_fromurl(::std::string* fromurl);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.AcceptRecoveryRequestProto)
 private:
  inline void set_has_reqinfo();
  inline void clear_has_reqinfo();
  inline void set_has_statetoaccept();
  inline void clear_has_statetoaccept();
  inline void set_has_fromurl();
  inline void clear_has_fromurl();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo_;
  ::hadoop::hdfs::qjournal::SegmentStateProto* statetoaccept_;
  ::std::string* fromurl_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static AcceptRecoveryRequestProto* default_instance_;
};
// -------------------------------------------------------------------

class AcceptRecoveryResponseProto : public ::google::protobuf::Message {
 public:
  AcceptRecoveryResponseProto();
  virtual ~AcceptRecoveryResponseProto();

  AcceptRecoveryResponseProto(const AcceptRecoveryResponseProto& from);

  inline AcceptRecoveryResponseProto& operator=(const AcceptRecoveryResponseProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AcceptRecoveryResponseProto& default_instance();

  void Swap(AcceptRecoveryResponseProto* other);

  // implements Message ----------------------------------------------

  AcceptRecoveryResponseProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AcceptRecoveryResponseProto& from);
  void MergeFrom(const AcceptRecoveryResponseProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.qjournal.AcceptRecoveryResponseProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_QJournalProtocol_2eproto();
  friend void protobuf_AssignDesc_QJournalProtocol_2eproto();
  friend void protobuf_ShutdownFile_QJournalProtocol_2eproto();

  void InitAsDefaultInstance();
  static AcceptRecoveryResponseProto* default_instance_;
};
// ===================================================================


// ===================================================================

// JournalIdProto

// required string identifier = 1;
inline bool JournalIdProto::has_identifier() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void JournalIdProto::set_has_identifier() {
  _has_bits_[0] |= 0x00000001u;
}
inline void JournalIdProto::clear_has_identifier() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void JournalIdProto::clear_identifier() {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    identifier_->clear();
  }
  clear_has_identifier();
}
inline const ::std::string& JournalIdProto::identifier() const {
  return *identifier_;
}
inline void JournalIdProto::set_identifier(const ::std::string& value) {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  identifier_->assign(value);
}
inline void JournalIdProto::set_identifier(const char* value) {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  identifier_->assign(value);
}
inline void JournalIdProto::set_identifier(const char* value, size_t size) {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  identifier_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* JournalIdProto::mutable_identifier() {
  set_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    identifier_ = new ::std::string;
  }
  return identifier_;
}
inline ::std::string* JournalIdProto::release_identifier() {
  clear_has_identifier();
  if (identifier_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = identifier_;
    identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void JournalIdProto::set_allocated_identifier(::std::string* identifier) {
  if (identifier_ != &::google::protobuf::internal::kEmptyString) {
    delete identifier_;
  }
  if (identifier) {
    set_has_identifier();
    identifier_ = identifier;
  } else {
    clear_has_identifier();
    identifier_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// RequestInfoProto

// required .hadoop.hdfs.qjournal.JournalIdProto journalId = 1;
inline bool RequestInfoProto::has_journalid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RequestInfoProto::set_has_journalid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RequestInfoProto::clear_has_journalid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RequestInfoProto::clear_journalid() {
  if (journalid_ != NULL) journalid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_journalid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& RequestInfoProto::journalid() const {
  return journalid_ != NULL ? *journalid_ : *default_instance_->journalid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* RequestInfoProto::mutable_journalid() {
  set_has_journalid();
  if (journalid_ == NULL) journalid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return journalid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* RequestInfoProto::release_journalid() {
  clear_has_journalid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = journalid_;
  journalid_ = NULL;
  return temp;
}
inline void RequestInfoProto::set_allocated_journalid(::hadoop::hdfs::qjournal::JournalIdProto* journalid) {
  delete journalid_;
  journalid_ = journalid;
  if (journalid) {
    set_has_journalid();
  } else {
    clear_has_journalid();
  }
}

// required uint64 epoch = 2;
inline bool RequestInfoProto::has_epoch() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RequestInfoProto::set_has_epoch() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RequestInfoProto::clear_has_epoch() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RequestInfoProto::clear_epoch() {
  epoch_ = GOOGLE_ULONGLONG(0);
  clear_has_epoch();
}
inline ::google::protobuf::uint64 RequestInfoProto::epoch() const {
  return epoch_;
}
inline void RequestInfoProto::set_epoch(::google::protobuf::uint64 value) {
  set_has_epoch();
  epoch_ = value;
}

// required uint64 ipcSerialNumber = 3;
inline bool RequestInfoProto::has_ipcserialnumber() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RequestInfoProto::set_has_ipcserialnumber() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RequestInfoProto::clear_has_ipcserialnumber() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RequestInfoProto::clear_ipcserialnumber() {
  ipcserialnumber_ = GOOGLE_ULONGLONG(0);
  clear_has_ipcserialnumber();
}
inline ::google::protobuf::uint64 RequestInfoProto::ipcserialnumber() const {
  return ipcserialnumber_;
}
inline void RequestInfoProto::set_ipcserialnumber(::google::protobuf::uint64 value) {
  set_has_ipcserialnumber();
  ipcserialnumber_ = value;
}

// optional uint64 committedTxId = 4;
inline bool RequestInfoProto::has_committedtxid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RequestInfoProto::set_has_committedtxid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RequestInfoProto::clear_has_committedtxid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RequestInfoProto::clear_committedtxid() {
  committedtxid_ = GOOGLE_ULONGLONG(0);
  clear_has_committedtxid();
}
inline ::google::protobuf::uint64 RequestInfoProto::committedtxid() const {
  return committedtxid_;
}
inline void RequestInfoProto::set_committedtxid(::google::protobuf::uint64 value) {
  set_has_committedtxid();
  committedtxid_ = value;
}

// -------------------------------------------------------------------

// SegmentStateProto

// required uint64 startTxId = 1;
inline bool SegmentStateProto::has_starttxid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SegmentStateProto::set_has_starttxid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SegmentStateProto::clear_has_starttxid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SegmentStateProto::clear_starttxid() {
  starttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_starttxid();
}
inline ::google::protobuf::uint64 SegmentStateProto::starttxid() const {
  return starttxid_;
}
inline void SegmentStateProto::set_starttxid(::google::protobuf::uint64 value) {
  set_has_starttxid();
  starttxid_ = value;
}

// required uint64 endTxId = 2;
inline bool SegmentStateProto::has_endtxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SegmentStateProto::set_has_endtxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SegmentStateProto::clear_has_endtxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SegmentStateProto::clear_endtxid() {
  endtxid_ = GOOGLE_ULONGLONG(0);
  clear_has_endtxid();
}
inline ::google::protobuf::uint64 SegmentStateProto::endtxid() const {
  return endtxid_;
}
inline void SegmentStateProto::set_endtxid(::google::protobuf::uint64 value) {
  set_has_endtxid();
  endtxid_ = value;
}

// required bool isInProgress = 3;
inline bool SegmentStateProto::has_isinprogress() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SegmentStateProto::set_has_isinprogress() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SegmentStateProto::clear_has_isinprogress() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SegmentStateProto::clear_isinprogress() {
  isinprogress_ = false;
  clear_has_isinprogress();
}
inline bool SegmentStateProto::isinprogress() const {
  return isinprogress_;
}
inline void SegmentStateProto::set_isinprogress(bool value) {
  set_has_isinprogress();
  isinprogress_ = value;
}

// -------------------------------------------------------------------

// PersistedRecoveryPaxosData

// required .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
inline bool PersistedRecoveryPaxosData::has_segmentstate() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PersistedRecoveryPaxosData::set_has_segmentstate() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PersistedRecoveryPaxosData::clear_has_segmentstate() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PersistedRecoveryPaxosData::clear_segmentstate() {
  if (segmentstate_ != NULL) segmentstate_->::hadoop::hdfs::qjournal::SegmentStateProto::Clear();
  clear_has_segmentstate();
}
inline const ::hadoop::hdfs::qjournal::SegmentStateProto& PersistedRecoveryPaxosData::segmentstate() const {
  return segmentstate_ != NULL ? *segmentstate_ : *default_instance_->segmentstate_;
}
inline ::hadoop::hdfs::qjournal::SegmentStateProto* PersistedRecoveryPaxosData::mutable_segmentstate() {
  set_has_segmentstate();
  if (segmentstate_ == NULL) segmentstate_ = new ::hadoop::hdfs::qjournal::SegmentStateProto;
  return segmentstate_;
}
inline ::hadoop::hdfs::qjournal::SegmentStateProto* PersistedRecoveryPaxosData::release_segmentstate() {
  clear_has_segmentstate();
  ::hadoop::hdfs::qjournal::SegmentStateProto* temp = segmentstate_;
  segmentstate_ = NULL;
  return temp;
}
inline void PersistedRecoveryPaxosData::set_allocated_segmentstate(::hadoop::hdfs::qjournal::SegmentStateProto* segmentstate) {
  delete segmentstate_;
  segmentstate_ = segmentstate;
  if (segmentstate) {
    set_has_segmentstate();
  } else {
    clear_has_segmentstate();
  }
}

// required uint64 acceptedInEpoch = 2;
inline bool PersistedRecoveryPaxosData::has_acceptedinepoch() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void PersistedRecoveryPaxosData::set_has_acceptedinepoch() {
  _has_bits_[0] |= 0x00000002u;
}
inline void PersistedRecoveryPaxosData::clear_has_acceptedinepoch() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void PersistedRecoveryPaxosData::clear_acceptedinepoch() {
  acceptedinepoch_ = GOOGLE_ULONGLONG(0);
  clear_has_acceptedinepoch();
}
inline ::google::protobuf::uint64 PersistedRecoveryPaxosData::acceptedinepoch() const {
  return acceptedinepoch_;
}
inline void PersistedRecoveryPaxosData::set_acceptedinepoch(::google::protobuf::uint64 value) {
  set_has_acceptedinepoch();
  acceptedinepoch_ = value;
}

// -------------------------------------------------------------------

// JournalRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool JournalRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void JournalRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void JournalRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void JournalRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& JournalRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* JournalRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* JournalRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void JournalRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// required uint64 firstTxnId = 2;
inline bool JournalRequestProto::has_firsttxnid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void JournalRequestProto::set_has_firsttxnid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void JournalRequestProto::clear_has_firsttxnid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void JournalRequestProto::clear_firsttxnid() {
  firsttxnid_ = GOOGLE_ULONGLONG(0);
  clear_has_firsttxnid();
}
inline ::google::protobuf::uint64 JournalRequestProto::firsttxnid() const {
  return firsttxnid_;
}
inline void JournalRequestProto::set_firsttxnid(::google::protobuf::uint64 value) {
  set_has_firsttxnid();
  firsttxnid_ = value;
}

// required uint32 numTxns = 3;
inline bool JournalRequestProto::has_numtxns() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void JournalRequestProto::set_has_numtxns() {
  _has_bits_[0] |= 0x00000004u;
}
inline void JournalRequestProto::clear_has_numtxns() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void JournalRequestProto::clear_numtxns() {
  numtxns_ = 0u;
  clear_has_numtxns();
}
inline ::google::protobuf::uint32 JournalRequestProto::numtxns() const {
  return numtxns_;
}
inline void JournalRequestProto::set_numtxns(::google::protobuf::uint32 value) {
  set_has_numtxns();
  numtxns_ = value;
}

// required bytes records = 4;
inline bool JournalRequestProto::has_records() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void JournalRequestProto::set_has_records() {
  _has_bits_[0] |= 0x00000008u;
}
inline void JournalRequestProto::clear_has_records() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void JournalRequestProto::clear_records() {
  if (records_ != &::google::protobuf::internal::kEmptyString) {
    records_->clear();
  }
  clear_has_records();
}
inline const ::std::string& JournalRequestProto::records() const {
  return *records_;
}
inline void JournalRequestProto::set_records(const ::std::string& value) {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  records_->assign(value);
}
inline void JournalRequestProto::set_records(const char* value) {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  records_->assign(value);
}
inline void JournalRequestProto::set_records(const void* value, size_t size) {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  records_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* JournalRequestProto::mutable_records() {
  set_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    records_ = new ::std::string;
  }
  return records_;
}
inline ::std::string* JournalRequestProto::release_records() {
  clear_has_records();
  if (records_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = records_;
    records_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void JournalRequestProto::set_allocated_records(::std::string* records) {
  if (records_ != &::google::protobuf::internal::kEmptyString) {
    delete records_;
  }
  if (records) {
    set_has_records();
    records_ = records;
  } else {
    clear_has_records();
    records_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// required uint64 segmentTxnId = 5;
inline bool JournalRequestProto::has_segmenttxnid() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void JournalRequestProto::set_has_segmenttxnid() {
  _has_bits_[0] |= 0x00000010u;
}
inline void JournalRequestProto::clear_has_segmenttxnid() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void JournalRequestProto::clear_segmenttxnid() {
  segmenttxnid_ = GOOGLE_ULONGLONG(0);
  clear_has_segmenttxnid();
}
inline ::google::protobuf::uint64 JournalRequestProto::segmenttxnid() const {
  return segmenttxnid_;
}
inline void JournalRequestProto::set_segmenttxnid(::google::protobuf::uint64 value) {
  set_has_segmenttxnid();
  segmenttxnid_ = value;
}

// -------------------------------------------------------------------

// JournalResponseProto

// -------------------------------------------------------------------

// HeartbeatRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool HeartbeatRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void HeartbeatRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void HeartbeatRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void HeartbeatRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& HeartbeatRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* HeartbeatRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* HeartbeatRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void HeartbeatRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// -------------------------------------------------------------------

// HeartbeatResponseProto

// -------------------------------------------------------------------

// StartLogSegmentRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool StartLogSegmentRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StartLogSegmentRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StartLogSegmentRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StartLogSegmentRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& StartLogSegmentRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* StartLogSegmentRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* StartLogSegmentRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void StartLogSegmentRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// required uint64 txid = 2;
inline bool StartLogSegmentRequestProto::has_txid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void StartLogSegmentRequestProto::set_has_txid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void StartLogSegmentRequestProto::clear_has_txid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void StartLogSegmentRequestProto::clear_txid() {
  txid_ = GOOGLE_ULONGLONG(0);
  clear_has_txid();
}
inline ::google::protobuf::uint64 StartLogSegmentRequestProto::txid() const {
  return txid_;
}
inline void StartLogSegmentRequestProto::set_txid(::google::protobuf::uint64 value) {
  set_has_txid();
  txid_ = value;
}

// optional sint32 layoutVersion = 3;
inline bool StartLogSegmentRequestProto::has_layoutversion() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void StartLogSegmentRequestProto::set_has_layoutversion() {
  _has_bits_[0] |= 0x00000004u;
}
inline void StartLogSegmentRequestProto::clear_has_layoutversion() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void StartLogSegmentRequestProto::clear_layoutversion() {
  layoutversion_ = 0;
  clear_has_layoutversion();
}
inline ::google::protobuf::int32 StartLogSegmentRequestProto::layoutversion() const {
  return layoutversion_;
}
inline void StartLogSegmentRequestProto::set_layoutversion(::google::protobuf::int32 value) {
  set_has_layoutversion();
  layoutversion_ = value;
}

// -------------------------------------------------------------------

// StartLogSegmentResponseProto

// -------------------------------------------------------------------

// FinalizeLogSegmentRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool FinalizeLogSegmentRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FinalizeLogSegmentRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FinalizeLogSegmentRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FinalizeLogSegmentRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& FinalizeLogSegmentRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* FinalizeLogSegmentRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* FinalizeLogSegmentRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void FinalizeLogSegmentRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// required uint64 startTxId = 2;
inline bool FinalizeLogSegmentRequestProto::has_starttxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FinalizeLogSegmentRequestProto::set_has_starttxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FinalizeLogSegmentRequestProto::clear_has_starttxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FinalizeLogSegmentRequestProto::clear_starttxid() {
  starttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_starttxid();
}
inline ::google::protobuf::uint64 FinalizeLogSegmentRequestProto::starttxid() const {
  return starttxid_;
}
inline void FinalizeLogSegmentRequestProto::set_starttxid(::google::protobuf::uint64 value) {
  set_has_starttxid();
  starttxid_ = value;
}

// required uint64 endTxId = 3;
inline bool FinalizeLogSegmentRequestProto::has_endtxid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FinalizeLogSegmentRequestProto::set_has_endtxid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FinalizeLogSegmentRequestProto::clear_has_endtxid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FinalizeLogSegmentRequestProto::clear_endtxid() {
  endtxid_ = GOOGLE_ULONGLONG(0);
  clear_has_endtxid();
}
inline ::google::protobuf::uint64 FinalizeLogSegmentRequestProto::endtxid() const {
  return endtxid_;
}
inline void FinalizeLogSegmentRequestProto::set_endtxid(::google::protobuf::uint64 value) {
  set_has_endtxid();
  endtxid_ = value;
}

// -------------------------------------------------------------------

// FinalizeLogSegmentResponseProto

// -------------------------------------------------------------------

// PurgeLogsRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool PurgeLogsRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PurgeLogsRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PurgeLogsRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PurgeLogsRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& PurgeLogsRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* PurgeLogsRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* PurgeLogsRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void PurgeLogsRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// required uint64 minTxIdToKeep = 2;
inline bool PurgeLogsRequestProto::has_mintxidtokeep() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void PurgeLogsRequestProto::set_has_mintxidtokeep() {
  _has_bits_[0] |= 0x00000002u;
}
inline void PurgeLogsRequestProto::clear_has_mintxidtokeep() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void PurgeLogsRequestProto::clear_mintxidtokeep() {
  mintxidtokeep_ = GOOGLE_ULONGLONG(0);
  clear_has_mintxidtokeep();
}
inline ::google::protobuf::uint64 PurgeLogsRequestProto::mintxidtokeep() const {
  return mintxidtokeep_;
}
inline void PurgeLogsRequestProto::set_mintxidtokeep(::google::protobuf::uint64 value) {
  set_has_mintxidtokeep();
  mintxidtokeep_ = value;
}

// -------------------------------------------------------------------

// PurgeLogsResponseProto

// -------------------------------------------------------------------

// IsFormattedRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool IsFormattedRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void IsFormattedRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void IsFormattedRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void IsFormattedRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& IsFormattedRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* IsFormattedRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* IsFormattedRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void IsFormattedRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// -------------------------------------------------------------------

// IsFormattedResponseProto

// required bool isFormatted = 1;
inline bool IsFormattedResponseProto::has_isformatted() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void IsFormattedResponseProto::set_has_isformatted() {
  _has_bits_[0] |= 0x00000001u;
}
inline void IsFormattedResponseProto::clear_has_isformatted() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void IsFormattedResponseProto::clear_isformatted() {
  isformatted_ = false;
  clear_has_isformatted();
}
inline bool IsFormattedResponseProto::isformatted() const {
  return isformatted_;
}
inline void IsFormattedResponseProto::set_isformatted(bool value) {
  set_has_isformatted();
  isformatted_ = value;
}

// -------------------------------------------------------------------

// DiscardSegmentsRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool DiscardSegmentsRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DiscardSegmentsRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DiscardSegmentsRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DiscardSegmentsRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& DiscardSegmentsRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DiscardSegmentsRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DiscardSegmentsRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void DiscardSegmentsRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// required uint64 startTxId = 2;
inline bool DiscardSegmentsRequestProto::has_starttxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DiscardSegmentsRequestProto::set_has_starttxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DiscardSegmentsRequestProto::clear_has_starttxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DiscardSegmentsRequestProto::clear_starttxid() {
  starttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_starttxid();
}
inline ::google::protobuf::uint64 DiscardSegmentsRequestProto::starttxid() const {
  return starttxid_;
}
inline void DiscardSegmentsRequestProto::set_starttxid(::google::protobuf::uint64 value) {
  set_has_starttxid();
  starttxid_ = value;
}

// -------------------------------------------------------------------

// DiscardSegmentsResponseProto

// -------------------------------------------------------------------

// GetJournalCTimeRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool GetJournalCTimeRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetJournalCTimeRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetJournalCTimeRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetJournalCTimeRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& GetJournalCTimeRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* GetJournalCTimeRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* GetJournalCTimeRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void GetJournalCTimeRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// -------------------------------------------------------------------

// GetJournalCTimeResponseProto

// required int64 resultCTime = 1;
inline bool GetJournalCTimeResponseProto::has_resultctime() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetJournalCTimeResponseProto::set_has_resultctime() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetJournalCTimeResponseProto::clear_has_resultctime() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetJournalCTimeResponseProto::clear_resultctime() {
  resultctime_ = GOOGLE_LONGLONG(0);
  clear_has_resultctime();
}
inline ::google::protobuf::int64 GetJournalCTimeResponseProto::resultctime() const {
  return resultctime_;
}
inline void GetJournalCTimeResponseProto::set_resultctime(::google::protobuf::int64 value) {
  set_has_resultctime();
  resultctime_ = value;
}

// -------------------------------------------------------------------

// DoPreUpgradeRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool DoPreUpgradeRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoPreUpgradeRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoPreUpgradeRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoPreUpgradeRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& DoPreUpgradeRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoPreUpgradeRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoPreUpgradeRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void DoPreUpgradeRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// -------------------------------------------------------------------

// DoPreUpgradeResponseProto

// -------------------------------------------------------------------

// DoUpgradeRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool DoUpgradeRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoUpgradeRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoUpgradeRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoUpgradeRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& DoUpgradeRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoUpgradeRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoUpgradeRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void DoUpgradeRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// required .hadoop.hdfs.StorageInfoProto sInfo = 2;
inline bool DoUpgradeRequestProto::has_sinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DoUpgradeRequestProto::set_has_sinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DoUpgradeRequestProto::clear_has_sinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DoUpgradeRequestProto::clear_sinfo() {
  if (sinfo_ != NULL) sinfo_->::hadoop::hdfs::StorageInfoProto::Clear();
  clear_has_sinfo();
}
inline const ::hadoop::hdfs::StorageInfoProto& DoUpgradeRequestProto::sinfo() const {
  return sinfo_ != NULL ? *sinfo_ : *default_instance_->sinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* DoUpgradeRequestProto::mutable_sinfo() {
  set_has_sinfo();
  if (sinfo_ == NULL) sinfo_ = new ::hadoop::hdfs::StorageInfoProto;
  return sinfo_;
}
inline ::hadoop::hdfs::StorageInfoProto* DoUpgradeRequestProto::release_sinfo() {
  clear_has_sinfo();
  ::hadoop::hdfs::StorageInfoProto* temp = sinfo_;
  sinfo_ = NULL;
  return temp;
}
inline void DoUpgradeRequestProto::set_allocated_sinfo(::hadoop::hdfs::StorageInfoProto* sinfo) {
  delete sinfo_;
  sinfo_ = sinfo;
  if (sinfo) {
    set_has_sinfo();
  } else {
    clear_has_sinfo();
  }
}

// -------------------------------------------------------------------

// DoUpgradeResponseProto

// -------------------------------------------------------------------

// DoFinalizeRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool DoFinalizeRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoFinalizeRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoFinalizeRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoFinalizeRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& DoFinalizeRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoFinalizeRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoFinalizeRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void DoFinalizeRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// -------------------------------------------------------------------

// DoFinalizeResponseProto

// -------------------------------------------------------------------

// CanRollBackRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool CanRollBackRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CanRollBackRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CanRollBackRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CanRollBackRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& CanRollBackRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* CanRollBackRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* CanRollBackRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void CanRollBackRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// required .hadoop.hdfs.StorageInfoProto storage = 2;
inline bool CanRollBackRequestProto::has_storage() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CanRollBackRequestProto::set_has_storage() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CanRollBackRequestProto::clear_has_storage() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CanRollBackRequestProto::clear_storage() {
  if (storage_ != NULL) storage_->::hadoop::hdfs::StorageInfoProto::Clear();
  clear_has_storage();
}
inline const ::hadoop::hdfs::StorageInfoProto& CanRollBackRequestProto::storage() const {
  return storage_ != NULL ? *storage_ : *default_instance_->storage_;
}
inline ::hadoop::hdfs::StorageInfoProto* CanRollBackRequestProto::mutable_storage() {
  set_has_storage();
  if (storage_ == NULL) storage_ = new ::hadoop::hdfs::StorageInfoProto;
  return storage_;
}
inline ::hadoop::hdfs::StorageInfoProto* CanRollBackRequestProto::release_storage() {
  clear_has_storage();
  ::hadoop::hdfs::StorageInfoProto* temp = storage_;
  storage_ = NULL;
  return temp;
}
inline void CanRollBackRequestProto::set_allocated_storage(::hadoop::hdfs::StorageInfoProto* storage) {
  delete storage_;
  storage_ = storage;
  if (storage) {
    set_has_storage();
  } else {
    clear_has_storage();
  }
}

// required .hadoop.hdfs.StorageInfoProto prevStorage = 3;
inline bool CanRollBackRequestProto::has_prevstorage() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void CanRollBackRequestProto::set_has_prevstorage() {
  _has_bits_[0] |= 0x00000004u;
}
inline void CanRollBackRequestProto::clear_has_prevstorage() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void CanRollBackRequestProto::clear_prevstorage() {
  if (prevstorage_ != NULL) prevstorage_->::hadoop::hdfs::StorageInfoProto::Clear();
  clear_has_prevstorage();
}
inline const ::hadoop::hdfs::StorageInfoProto& CanRollBackRequestProto::prevstorage() const {
  return prevstorage_ != NULL ? *prevstorage_ : *default_instance_->prevstorage_;
}
inline ::hadoop::hdfs::StorageInfoProto* CanRollBackRequestProto::mutable_prevstorage() {
  set_has_prevstorage();
  if (prevstorage_ == NULL) prevstorage_ = new ::hadoop::hdfs::StorageInfoProto;
  return prevstorage_;
}
inline ::hadoop::hdfs::StorageInfoProto* CanRollBackRequestProto::release_prevstorage() {
  clear_has_prevstorage();
  ::hadoop::hdfs::StorageInfoProto* temp = prevstorage_;
  prevstorage_ = NULL;
  return temp;
}
inline void CanRollBackRequestProto::set_allocated_prevstorage(::hadoop::hdfs::StorageInfoProto* prevstorage) {
  delete prevstorage_;
  prevstorage_ = prevstorage;
  if (prevstorage) {
    set_has_prevstorage();
  } else {
    clear_has_prevstorage();
  }
}

// required int32 targetLayoutVersion = 4;
inline bool CanRollBackRequestProto::has_targetlayoutversion() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void CanRollBackRequestProto::set_has_targetlayoutversion() {
  _has_bits_[0] |= 0x00000008u;
}
inline void CanRollBackRequestProto::clear_has_targetlayoutversion() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void CanRollBackRequestProto::clear_targetlayoutversion() {
  targetlayoutversion_ = 0;
  clear_has_targetlayoutversion();
}
inline ::google::protobuf::int32 CanRollBackRequestProto::targetlayoutversion() const {
  return targetlayoutversion_;
}
inline void CanRollBackRequestProto::set_targetlayoutversion(::google::protobuf::int32 value) {
  set_has_targetlayoutversion();
  targetlayoutversion_ = value;
}

// -------------------------------------------------------------------

// CanRollBackResponseProto

// required bool canRollBack = 1;
inline bool CanRollBackResponseProto::has_canrollback() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CanRollBackResponseProto::set_has_canrollback() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CanRollBackResponseProto::clear_has_canrollback() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CanRollBackResponseProto::clear_canrollback() {
  canrollback_ = false;
  clear_has_canrollback();
}
inline bool CanRollBackResponseProto::canrollback() const {
  return canrollback_;
}
inline void CanRollBackResponseProto::set_canrollback(bool value) {
  set_has_canrollback();
  canrollback_ = value;
}

// -------------------------------------------------------------------

// DoRollbackRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool DoRollbackRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoRollbackRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoRollbackRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoRollbackRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& DoRollbackRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoRollbackRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* DoRollbackRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void DoRollbackRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// -------------------------------------------------------------------

// DoRollbackResponseProto

// -------------------------------------------------------------------

// GetJournalStateRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool GetJournalStateRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetJournalStateRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetJournalStateRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetJournalStateRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& GetJournalStateRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* GetJournalStateRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* GetJournalStateRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void GetJournalStateRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// -------------------------------------------------------------------

// GetJournalStateResponseProto

// required uint64 lastPromisedEpoch = 1;
inline bool GetJournalStateResponseProto::has_lastpromisedepoch() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetJournalStateResponseProto::set_has_lastpromisedepoch() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetJournalStateResponseProto::clear_has_lastpromisedepoch() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetJournalStateResponseProto::clear_lastpromisedepoch() {
  lastpromisedepoch_ = GOOGLE_ULONGLONG(0);
  clear_has_lastpromisedepoch();
}
inline ::google::protobuf::uint64 GetJournalStateResponseProto::lastpromisedepoch() const {
  return lastpromisedepoch_;
}
inline void GetJournalStateResponseProto::set_lastpromisedepoch(::google::protobuf::uint64 value) {
  set_has_lastpromisedepoch();
  lastpromisedepoch_ = value;
}

// required uint32 httpPort = 2;
inline bool GetJournalStateResponseProto::has_httpport() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GetJournalStateResponseProto::set_has_httpport() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GetJournalStateResponseProto::clear_has_httpport() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GetJournalStateResponseProto::clear_httpport() {
  httpport_ = 0u;
  clear_has_httpport();
}
inline ::google::protobuf::uint32 GetJournalStateResponseProto::httpport() const {
  return httpport_;
}
inline void GetJournalStateResponseProto::set_httpport(::google::protobuf::uint32 value) {
  set_has_httpport();
  httpport_ = value;
}

// optional string fromURL = 3;
inline bool GetJournalStateResponseProto::has_fromurl() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void GetJournalStateResponseProto::set_has_fromurl() {
  _has_bits_[0] |= 0x00000004u;
}
inline void GetJournalStateResponseProto::clear_has_fromurl() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void GetJournalStateResponseProto::clear_fromurl() {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    fromurl_->clear();
  }
  clear_has_fromurl();
}
inline const ::std::string& GetJournalStateResponseProto::fromurl() const {
  return *fromurl_;
}
inline void GetJournalStateResponseProto::set_fromurl(const ::std::string& value) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(value);
}
inline void GetJournalStateResponseProto::set_fromurl(const char* value) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(value);
}
inline void GetJournalStateResponseProto::set_fromurl(const char* value, size_t size) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetJournalStateResponseProto::mutable_fromurl() {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  return fromurl_;
}
inline ::std::string* GetJournalStateResponseProto::release_fromurl() {
  clear_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fromurl_;
    fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetJournalStateResponseProto::set_allocated_fromurl(::std::string* fromurl) {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    delete fromurl_;
  }
  if (fromurl) {
    set_has_fromurl();
    fromurl_ = fromurl;
  } else {
    clear_has_fromurl();
    fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// FormatRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool FormatRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FormatRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FormatRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FormatRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& FormatRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* FormatRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* FormatRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void FormatRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
inline bool FormatRequestProto::has_nsinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FormatRequestProto::set_has_nsinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FormatRequestProto::clear_has_nsinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FormatRequestProto::clear_nsinfo() {
  if (nsinfo_ != NULL) nsinfo_->::hadoop::hdfs::NamespaceInfoProto::Clear();
  clear_has_nsinfo();
}
inline const ::hadoop::hdfs::NamespaceInfoProto& FormatRequestProto::nsinfo() const {
  return nsinfo_ != NULL ? *nsinfo_ : *default_instance_->nsinfo_;
}
inline ::hadoop::hdfs::NamespaceInfoProto* FormatRequestProto::mutable_nsinfo() {
  set_has_nsinfo();
  if (nsinfo_ == NULL) nsinfo_ = new ::hadoop::hdfs::NamespaceInfoProto;
  return nsinfo_;
}
inline ::hadoop::hdfs::NamespaceInfoProto* FormatRequestProto::release_nsinfo() {
  clear_has_nsinfo();
  ::hadoop::hdfs::NamespaceInfoProto* temp = nsinfo_;
  nsinfo_ = NULL;
  return temp;
}
inline void FormatRequestProto::set_allocated_nsinfo(::hadoop::hdfs::NamespaceInfoProto* nsinfo) {
  delete nsinfo_;
  nsinfo_ = nsinfo;
  if (nsinfo) {
    set_has_nsinfo();
  } else {
    clear_has_nsinfo();
  }
}

// -------------------------------------------------------------------

// FormatResponseProto

// -------------------------------------------------------------------

// NewEpochRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool NewEpochRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NewEpochRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NewEpochRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NewEpochRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& NewEpochRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* NewEpochRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* NewEpochRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void NewEpochRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// required .hadoop.hdfs.NamespaceInfoProto nsInfo = 2;
inline bool NewEpochRequestProto::has_nsinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NewEpochRequestProto::set_has_nsinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NewEpochRequestProto::clear_has_nsinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NewEpochRequestProto::clear_nsinfo() {
  if (nsinfo_ != NULL) nsinfo_->::hadoop::hdfs::NamespaceInfoProto::Clear();
  clear_has_nsinfo();
}
inline const ::hadoop::hdfs::NamespaceInfoProto& NewEpochRequestProto::nsinfo() const {
  return nsinfo_ != NULL ? *nsinfo_ : *default_instance_->nsinfo_;
}
inline ::hadoop::hdfs::NamespaceInfoProto* NewEpochRequestProto::mutable_nsinfo() {
  set_has_nsinfo();
  if (nsinfo_ == NULL) nsinfo_ = new ::hadoop::hdfs::NamespaceInfoProto;
  return nsinfo_;
}
inline ::hadoop::hdfs::NamespaceInfoProto* NewEpochRequestProto::release_nsinfo() {
  clear_has_nsinfo();
  ::hadoop::hdfs::NamespaceInfoProto* temp = nsinfo_;
  nsinfo_ = NULL;
  return temp;
}
inline void NewEpochRequestProto::set_allocated_nsinfo(::hadoop::hdfs::NamespaceInfoProto* nsinfo) {
  delete nsinfo_;
  nsinfo_ = nsinfo;
  if (nsinfo) {
    set_has_nsinfo();
  } else {
    clear_has_nsinfo();
  }
}

// required uint64 epoch = 3;
inline bool NewEpochRequestProto::has_epoch() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NewEpochRequestProto::set_has_epoch() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NewEpochRequestProto::clear_has_epoch() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NewEpochRequestProto::clear_epoch() {
  epoch_ = GOOGLE_ULONGLONG(0);
  clear_has_epoch();
}
inline ::google::protobuf::uint64 NewEpochRequestProto::epoch() const {
  return epoch_;
}
inline void NewEpochRequestProto::set_epoch(::google::protobuf::uint64 value) {
  set_has_epoch();
  epoch_ = value;
}

// -------------------------------------------------------------------

// NewEpochResponseProto

// optional uint64 lastSegmentTxId = 1;
inline bool NewEpochResponseProto::has_lastsegmenttxid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NewEpochResponseProto::set_has_lastsegmenttxid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NewEpochResponseProto::clear_has_lastsegmenttxid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NewEpochResponseProto::clear_lastsegmenttxid() {
  lastsegmenttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_lastsegmenttxid();
}
inline ::google::protobuf::uint64 NewEpochResponseProto::lastsegmenttxid() const {
  return lastsegmenttxid_;
}
inline void NewEpochResponseProto::set_lastsegmenttxid(::google::protobuf::uint64 value) {
  set_has_lastsegmenttxid();
  lastsegmenttxid_ = value;
}

// -------------------------------------------------------------------

// GetEditLogManifestRequestProto

// required .hadoop.hdfs.qjournal.JournalIdProto jid = 1;
inline bool GetEditLogManifestRequestProto::has_jid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetEditLogManifestRequestProto::set_has_jid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetEditLogManifestRequestProto::clear_has_jid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetEditLogManifestRequestProto::clear_jid() {
  if (jid_ != NULL) jid_->::hadoop::hdfs::qjournal::JournalIdProto::Clear();
  clear_has_jid();
}
inline const ::hadoop::hdfs::qjournal::JournalIdProto& GetEditLogManifestRequestProto::jid() const {
  return jid_ != NULL ? *jid_ : *default_instance_->jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* GetEditLogManifestRequestProto::mutable_jid() {
  set_has_jid();
  if (jid_ == NULL) jid_ = new ::hadoop::hdfs::qjournal::JournalIdProto;
  return jid_;
}
inline ::hadoop::hdfs::qjournal::JournalIdProto* GetEditLogManifestRequestProto::release_jid() {
  clear_has_jid();
  ::hadoop::hdfs::qjournal::JournalIdProto* temp = jid_;
  jid_ = NULL;
  return temp;
}
inline void GetEditLogManifestRequestProto::set_allocated_jid(::hadoop::hdfs::qjournal::JournalIdProto* jid) {
  delete jid_;
  jid_ = jid;
  if (jid) {
    set_has_jid();
  } else {
    clear_has_jid();
  }
}

// required uint64 sinceTxId = 2;
inline bool GetEditLogManifestRequestProto::has_sincetxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GetEditLogManifestRequestProto::set_has_sincetxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GetEditLogManifestRequestProto::clear_has_sincetxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GetEditLogManifestRequestProto::clear_sincetxid() {
  sincetxid_ = GOOGLE_ULONGLONG(0);
  clear_has_sincetxid();
}
inline ::google::protobuf::uint64 GetEditLogManifestRequestProto::sincetxid() const {
  return sincetxid_;
}
inline void GetEditLogManifestRequestProto::set_sincetxid(::google::protobuf::uint64 value) {
  set_has_sincetxid();
  sincetxid_ = value;
}

// optional bool inProgressOk = 4 [default = false];
inline bool GetEditLogManifestRequestProto::has_inprogressok() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void GetEditLogManifestRequestProto::set_has_inprogressok() {
  _has_bits_[0] |= 0x00000004u;
}
inline void GetEditLogManifestRequestProto::clear_has_inprogressok() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void GetEditLogManifestRequestProto::clear_inprogressok() {
  inprogressok_ = false;
  clear_has_inprogressok();
}
inline bool GetEditLogManifestRequestProto::inprogressok() const {
  return inprogressok_;
}
inline void GetEditLogManifestRequestProto::set_inprogressok(bool value) {
  set_has_inprogressok();
  inprogressok_ = value;
}

// -------------------------------------------------------------------

// GetEditLogManifestResponseProto

// required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
inline bool GetEditLogManifestResponseProto::has_manifest() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void GetEditLogManifestResponseProto::set_has_manifest() {
  _has_bits_[0] |= 0x00000001u;
}
inline void GetEditLogManifestResponseProto::clear_has_manifest() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void GetEditLogManifestResponseProto::clear_manifest() {
  if (manifest_ != NULL) manifest_->::hadoop::hdfs::RemoteEditLogManifestProto::Clear();
  clear_has_manifest();
}
inline const ::hadoop::hdfs::RemoteEditLogManifestProto& GetEditLogManifestResponseProto::manifest() const {
  return manifest_ != NULL ? *manifest_ : *default_instance_->manifest_;
}
inline ::hadoop::hdfs::RemoteEditLogManifestProto* GetEditLogManifestResponseProto::mutable_manifest() {
  set_has_manifest();
  if (manifest_ == NULL) manifest_ = new ::hadoop::hdfs::RemoteEditLogManifestProto;
  return manifest_;
}
inline ::hadoop::hdfs::RemoteEditLogManifestProto* GetEditLogManifestResponseProto::release_manifest() {
  clear_has_manifest();
  ::hadoop::hdfs::RemoteEditLogManifestProto* temp = manifest_;
  manifest_ = NULL;
  return temp;
}
inline void GetEditLogManifestResponseProto::set_allocated_manifest(::hadoop::hdfs::RemoteEditLogManifestProto* manifest) {
  delete manifest_;
  manifest_ = manifest;
  if (manifest) {
    set_has_manifest();
  } else {
    clear_has_manifest();
  }
}

// required uint32 httpPort = 2;
inline bool GetEditLogManifestResponseProto::has_httpport() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void GetEditLogManifestResponseProto::set_has_httpport() {
  _has_bits_[0] |= 0x00000002u;
}
inline void GetEditLogManifestResponseProto::clear_has_httpport() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void GetEditLogManifestResponseProto::clear_httpport() {
  httpport_ = 0u;
  clear_has_httpport();
}
inline ::google::protobuf::uint32 GetEditLogManifestResponseProto::httpport() const {
  return httpport_;
}
inline void GetEditLogManifestResponseProto::set_httpport(::google::protobuf::uint32 value) {
  set_has_httpport();
  httpport_ = value;
}

// optional string fromURL = 3;
inline bool GetEditLogManifestResponseProto::has_fromurl() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void GetEditLogManifestResponseProto::set_has_fromurl() {
  _has_bits_[0] |= 0x00000004u;
}
inline void GetEditLogManifestResponseProto::clear_has_fromurl() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void GetEditLogManifestResponseProto::clear_fromurl() {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    fromurl_->clear();
  }
  clear_has_fromurl();
}
inline const ::std::string& GetEditLogManifestResponseProto::fromurl() const {
  return *fromurl_;
}
inline void GetEditLogManifestResponseProto::set_fromurl(const ::std::string& value) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(value);
}
inline void GetEditLogManifestResponseProto::set_fromurl(const char* value) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(value);
}
inline void GetEditLogManifestResponseProto::set_fromurl(const char* value, size_t size) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* GetEditLogManifestResponseProto::mutable_fromurl() {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  return fromurl_;
}
inline ::std::string* GetEditLogManifestResponseProto::release_fromurl() {
  clear_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fromurl_;
    fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void GetEditLogManifestResponseProto::set_allocated_fromurl(::std::string* fromurl) {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    delete fromurl_;
  }
  if (fromurl) {
    set_has_fromurl();
    fromurl_ = fromurl;
  } else {
    clear_has_fromurl();
    fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// PrepareRecoveryRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool PrepareRecoveryRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PrepareRecoveryRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PrepareRecoveryRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PrepareRecoveryRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& PrepareRecoveryRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* PrepareRecoveryRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* PrepareRecoveryRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void PrepareRecoveryRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// required uint64 segmentTxId = 2;
inline bool PrepareRecoveryRequestProto::has_segmenttxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void PrepareRecoveryRequestProto::set_has_segmenttxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void PrepareRecoveryRequestProto::clear_has_segmenttxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void PrepareRecoveryRequestProto::clear_segmenttxid() {
  segmenttxid_ = GOOGLE_ULONGLONG(0);
  clear_has_segmenttxid();
}
inline ::google::protobuf::uint64 PrepareRecoveryRequestProto::segmenttxid() const {
  return segmenttxid_;
}
inline void PrepareRecoveryRequestProto::set_segmenttxid(::google::protobuf::uint64 value) {
  set_has_segmenttxid();
  segmenttxid_ = value;
}

// -------------------------------------------------------------------

// PrepareRecoveryResponseProto

// optional .hadoop.hdfs.qjournal.SegmentStateProto segmentState = 1;
inline bool PrepareRecoveryResponseProto::has_segmentstate() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PrepareRecoveryResponseProto::set_has_segmentstate() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PrepareRecoveryResponseProto::clear_has_segmentstate() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PrepareRecoveryResponseProto::clear_segmentstate() {
  if (segmentstate_ != NULL) segmentstate_->::hadoop::hdfs::qjournal::SegmentStateProto::Clear();
  clear_has_segmentstate();
}
inline const ::hadoop::hdfs::qjournal::SegmentStateProto& PrepareRecoveryResponseProto::segmentstate() const {
  return segmentstate_ != NULL ? *segmentstate_ : *default_instance_->segmentstate_;
}
inline ::hadoop::hdfs::qjournal::SegmentStateProto* PrepareRecoveryResponseProto::mutable_segmentstate() {
  set_has_segmentstate();
  if (segmentstate_ == NULL) segmentstate_ = new ::hadoop::hdfs::qjournal::SegmentStateProto;
  return segmentstate_;
}
inline ::hadoop::hdfs::qjournal::SegmentStateProto* PrepareRecoveryResponseProto::release_segmentstate() {
  clear_has_segmentstate();
  ::hadoop::hdfs::qjournal::SegmentStateProto* temp = segmentstate_;
  segmentstate_ = NULL;
  return temp;
}
inline void PrepareRecoveryResponseProto::set_allocated_segmentstate(::hadoop::hdfs::qjournal::SegmentStateProto* segmentstate) {
  delete segmentstate_;
  segmentstate_ = segmentstate;
  if (segmentstate) {
    set_has_segmentstate();
  } else {
    clear_has_segmentstate();
  }
}

// optional uint64 acceptedInEpoch = 2;
inline bool PrepareRecoveryResponseProto::has_acceptedinepoch() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void PrepareRecoveryResponseProto::set_has_acceptedinepoch() {
  _has_bits_[0] |= 0x00000002u;
}
inline void PrepareRecoveryResponseProto::clear_has_acceptedinepoch() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void PrepareRecoveryResponseProto::clear_acceptedinepoch() {
  acceptedinepoch_ = GOOGLE_ULONGLONG(0);
  clear_has_acceptedinepoch();
}
inline ::google::protobuf::uint64 PrepareRecoveryResponseProto::acceptedinepoch() const {
  return acceptedinepoch_;
}
inline void PrepareRecoveryResponseProto::set_acceptedinepoch(::google::protobuf::uint64 value) {
  set_has_acceptedinepoch();
  acceptedinepoch_ = value;
}

// required uint64 lastWriterEpoch = 3;
inline bool PrepareRecoveryResponseProto::has_lastwriterepoch() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void PrepareRecoveryResponseProto::set_has_lastwriterepoch() {
  _has_bits_[0] |= 0x00000004u;
}
inline void PrepareRecoveryResponseProto::clear_has_lastwriterepoch() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void PrepareRecoveryResponseProto::clear_lastwriterepoch() {
  lastwriterepoch_ = GOOGLE_ULONGLONG(0);
  clear_has_lastwriterepoch();
}
inline ::google::protobuf::uint64 PrepareRecoveryResponseProto::lastwriterepoch() const {
  return lastwriterepoch_;
}
inline void PrepareRecoveryResponseProto::set_lastwriterepoch(::google::protobuf::uint64 value) {
  set_has_lastwriterepoch();
  lastwriterepoch_ = value;
}

// optional uint64 lastCommittedTxId = 4;
inline bool PrepareRecoveryResponseProto::has_lastcommittedtxid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void PrepareRecoveryResponseProto::set_has_lastcommittedtxid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void PrepareRecoveryResponseProto::clear_has_lastcommittedtxid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void PrepareRecoveryResponseProto::clear_lastcommittedtxid() {
  lastcommittedtxid_ = GOOGLE_ULONGLONG(0);
  clear_has_lastcommittedtxid();
}
inline ::google::protobuf::uint64 PrepareRecoveryResponseProto::lastcommittedtxid() const {
  return lastcommittedtxid_;
}
inline void PrepareRecoveryResponseProto::set_lastcommittedtxid(::google::protobuf::uint64 value) {
  set_has_lastcommittedtxid();
  lastcommittedtxid_ = value;
}

// -------------------------------------------------------------------

// AcceptRecoveryRequestProto

// required .hadoop.hdfs.qjournal.RequestInfoProto reqInfo = 1;
inline bool AcceptRecoveryRequestProto::has_reqinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AcceptRecoveryRequestProto::set_has_reqinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AcceptRecoveryRequestProto::clear_has_reqinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AcceptRecoveryRequestProto::clear_reqinfo() {
  if (reqinfo_ != NULL) reqinfo_->::hadoop::hdfs::qjournal::RequestInfoProto::Clear();
  clear_has_reqinfo();
}
inline const ::hadoop::hdfs::qjournal::RequestInfoProto& AcceptRecoveryRequestProto::reqinfo() const {
  return reqinfo_ != NULL ? *reqinfo_ : *default_instance_->reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* AcceptRecoveryRequestProto::mutable_reqinfo() {
  set_has_reqinfo();
  if (reqinfo_ == NULL) reqinfo_ = new ::hadoop::hdfs::qjournal::RequestInfoProto;
  return reqinfo_;
}
inline ::hadoop::hdfs::qjournal::RequestInfoProto* AcceptRecoveryRequestProto::release_reqinfo() {
  clear_has_reqinfo();
  ::hadoop::hdfs::qjournal::RequestInfoProto* temp = reqinfo_;
  reqinfo_ = NULL;
  return temp;
}
inline void AcceptRecoveryRequestProto::set_allocated_reqinfo(::hadoop::hdfs::qjournal::RequestInfoProto* reqinfo) {
  delete reqinfo_;
  reqinfo_ = reqinfo;
  if (reqinfo) {
    set_has_reqinfo();
  } else {
    clear_has_reqinfo();
  }
}

// required .hadoop.hdfs.qjournal.SegmentStateProto stateToAccept = 2;
inline bool AcceptRecoveryRequestProto::has_statetoaccept() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void AcceptRecoveryRequestProto::set_has_statetoaccept() {
  _has_bits_[0] |= 0x00000002u;
}
inline void AcceptRecoveryRequestProto::clear_has_statetoaccept() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void AcceptRecoveryRequestProto::clear_statetoaccept() {
  if (statetoaccept_ != NULL) statetoaccept_->::hadoop::hdfs::qjournal::SegmentStateProto::Clear();
  clear_has_statetoaccept();
}
inline const ::hadoop::hdfs::qjournal::SegmentStateProto& AcceptRecoveryRequestProto::statetoaccept() const {
  return statetoaccept_ != NULL ? *statetoaccept_ : *default_instance_->statetoaccept_;
}
inline ::hadoop::hdfs::qjournal::SegmentStateProto* AcceptRecoveryRequestProto::mutable_statetoaccept() {
  set_has_statetoaccept();
  if (statetoaccept_ == NULL) statetoaccept_ = new ::hadoop::hdfs::qjournal::SegmentStateProto;
  return statetoaccept_;
}
inline ::hadoop::hdfs::qjournal::SegmentStateProto* AcceptRecoveryRequestProto::release_statetoaccept() {
  clear_has_statetoaccept();
  ::hadoop::hdfs::qjournal::SegmentStateProto* temp = statetoaccept_;
  statetoaccept_ = NULL;
  return temp;
}
inline void AcceptRecoveryRequestProto::set_allocated_statetoaccept(::hadoop::hdfs::qjournal::SegmentStateProto* statetoaccept) {
  delete statetoaccept_;
  statetoaccept_ = statetoaccept;
  if (statetoaccept) {
    set_has_statetoaccept();
  } else {
    clear_has_statetoaccept();
  }
}

// required string fromURL = 3;
inline bool AcceptRecoveryRequestProto::has_fromurl() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void AcceptRecoveryRequestProto::set_has_fromurl() {
  _has_bits_[0] |= 0x00000004u;
}
inline void AcceptRecoveryRequestProto::clear_has_fromurl() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void AcceptRecoveryRequestProto::clear_fromurl() {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    fromurl_->clear();
  }
  clear_has_fromurl();
}
inline const ::std::string& AcceptRecoveryRequestProto::fromurl() const {
  return *fromurl_;
}
inline void AcceptRecoveryRequestProto::set_fromurl(const ::std::string& value) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(value);
}
inline void AcceptRecoveryRequestProto::set_fromurl(const char* value) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(value);
}
inline void AcceptRecoveryRequestProto::set_fromurl(const char* value, size_t size) {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  fromurl_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* AcceptRecoveryRequestProto::mutable_fromurl() {
  set_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    fromurl_ = new ::std::string;
  }
  return fromurl_;
}
inline ::std::string* AcceptRecoveryRequestProto::release_fromurl() {
  clear_has_fromurl();
  if (fromurl_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fromurl_;
    fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void AcceptRecoveryRequestProto::set_allocated_fromurl(::std::string* fromurl) {
  if (fromurl_ != &::google::protobuf::internal::kEmptyString) {
    delete fromurl_;
  }
  if (fromurl) {
    set_has_fromurl();
    fromurl_ = fromurl;
  } else {
    clear_has_fromurl();
    fromurl_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// AcceptRecoveryResponseProto


// @@protoc_insertion_point(namespace_scope)

}  // namespace qjournal
}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_QJournalProtocol_2eproto__INCLUDED
