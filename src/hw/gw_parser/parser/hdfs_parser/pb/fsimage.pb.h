// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: fsimage.proto

#ifndef PROTOBUF_fsimage_2eproto__INCLUDED
#define PROTOBUF_fsimage_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2005000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "hdfs.pb.h"
#include "acl.pb.h"
#include "xattr.pb.h"
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {
namespace fsimage {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_fsimage_2eproto();
void protobuf_AssignDesc_fsimage_2eproto();
void protobuf_ShutdownFile_fsimage_2eproto();

class FileSummary;
class FileSummary_Section;
class NameSystemSection;
class INodeSection;
class INodeSection_FileUnderConstructionFeature;
class INodeSection_AclFeatureProto;
class INodeSection_XAttrCompactProto;
class INodeSection_XAttrFeatureProto;
class INodeSection_INodeFile;
class INodeSection_INodeDirectory;
class INodeSection_INodeSymlink;
class INodeSection_INode;
class FilesUnderConstructionSection;
class FilesUnderConstructionSection_FileUnderConstructionEntry;
class INodeDirectorySection;
class INodeDirectorySection_DirEntry;
class INodeReferenceSection;
class INodeReferenceSection_INodeReference;
class SnapshotSection;
class SnapshotSection_Snapshot;
class SnapshotDiffSection;
class SnapshotDiffSection_CreatedListEntry;
class SnapshotDiffSection_DirectoryDiff;
class SnapshotDiffSection_FileDiff;
class SnapshotDiffSection_DiffEntry;
class StringTableSection;
class StringTableSection_Entry;
class SecretManagerSection;
class SecretManagerSection_DelegationKey;
class SecretManagerSection_PersistToken;
class CacheManagerSection;

enum INodeSection_INode_Type {
  INodeSection_INode_Type_FILE = 1,
  INodeSection_INode_Type_DIRECTORY = 2,
  INodeSection_INode_Type_SYMLINK = 3
};
bool INodeSection_INode_Type_IsValid(int value);
const INodeSection_INode_Type INodeSection_INode_Type_Type_MIN = INodeSection_INode_Type_FILE;
const INodeSection_INode_Type INodeSection_INode_Type_Type_MAX = INodeSection_INode_Type_SYMLINK;
const int INodeSection_INode_Type_Type_ARRAYSIZE = INodeSection_INode_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* INodeSection_INode_Type_descriptor();
inline const ::std::string& INodeSection_INode_Type_Name(INodeSection_INode_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    INodeSection_INode_Type_descriptor(), value);
}
inline bool INodeSection_INode_Type_Parse(
    const ::std::string& name, INodeSection_INode_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<INodeSection_INode_Type>(
    INodeSection_INode_Type_descriptor(), name, value);
}
enum SnapshotDiffSection_DiffEntry_Type {
  SnapshotDiffSection_DiffEntry_Type_FILEDIFF = 1,
  SnapshotDiffSection_DiffEntry_Type_DIRECTORYDIFF = 2
};
bool SnapshotDiffSection_DiffEntry_Type_IsValid(int value);
const SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry_Type_Type_MIN = SnapshotDiffSection_DiffEntry_Type_FILEDIFF;
const SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry_Type_Type_MAX = SnapshotDiffSection_DiffEntry_Type_DIRECTORYDIFF;
const int SnapshotDiffSection_DiffEntry_Type_Type_ARRAYSIZE = SnapshotDiffSection_DiffEntry_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* SnapshotDiffSection_DiffEntry_Type_descriptor();
inline const ::std::string& SnapshotDiffSection_DiffEntry_Type_Name(SnapshotDiffSection_DiffEntry_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    SnapshotDiffSection_DiffEntry_Type_descriptor(), value);
}
inline bool SnapshotDiffSection_DiffEntry_Type_Parse(
    const ::std::string& name, SnapshotDiffSection_DiffEntry_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<SnapshotDiffSection_DiffEntry_Type>(
    SnapshotDiffSection_DiffEntry_Type_descriptor(), name, value);
}
// ===================================================================

class FileSummary_Section : public ::google::protobuf::Message {
 public:
  FileSummary_Section();
  virtual ~FileSummary_Section();

  FileSummary_Section(const FileSummary_Section& from);

  inline FileSummary_Section& operator=(const FileSummary_Section& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FileSummary_Section& default_instance();

  void Swap(FileSummary_Section* other);

  // implements Message ----------------------------------------------

  FileSummary_Section* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FileSummary_Section& from);
  void MergeFrom(const FileSummary_Section& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string name = 1;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 1;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional uint64 length = 2;
  inline bool has_length() const;
  inline void clear_length();
  static const int kLengthFieldNumber = 2;
  inline ::google::protobuf::uint64 length() const;
  inline void set_length(::google::protobuf::uint64 value);

  // optional uint64 offset = 3;
  inline bool has_offset() const;
  inline void clear_offset();
  static const int kOffsetFieldNumber = 3;
  inline ::google::protobuf::uint64 offset() const;
  inline void set_offset(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.FileSummary.Section)
 private:
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_length();
  inline void clear_has_length();
  inline void set_has_offset();
  inline void clear_has_offset();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* name_;
  ::google::protobuf::uint64 length_;
  ::google::protobuf::uint64 offset_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static FileSummary_Section* default_instance_;
};
// -------------------------------------------------------------------

class FileSummary : public ::google::protobuf::Message {
 public:
  FileSummary();
  virtual ~FileSummary();

  FileSummary(const FileSummary& from);

  inline FileSummary& operator=(const FileSummary& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FileSummary& default_instance();

  void Swap(FileSummary* other);

  // implements Message ----------------------------------------------

  FileSummary* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FileSummary& from);
  void MergeFrom(const FileSummary& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef FileSummary_Section Section;

  // accessors -------------------------------------------------------

  // required uint32 ondiskVersion = 1;
  inline bool has_ondiskversion() const;
  inline void clear_ondiskversion();
  static const int kOndiskVersionFieldNumber = 1;
  inline ::google::protobuf::uint32 ondiskversion() const;
  inline void set_ondiskversion(::google::protobuf::uint32 value);

  // required uint32 layoutVersion = 2;
  inline bool has_layoutversion() const;
  inline void clear_layoutversion();
  static const int kLayoutVersionFieldNumber = 2;
  inline ::google::protobuf::uint32 layoutversion() const;
  inline void set_layoutversion(::google::protobuf::uint32 value);

  // optional string codec = 3;
  inline bool has_codec() const;
  inline void clear_codec();
  static const int kCodecFieldNumber = 3;
  inline const ::std::string& codec() const;
  inline void set_codec(const ::std::string& value);
  inline void set_codec(const char* value);
  inline void set_codec(const char* value, size_t size);
  inline ::std::string* mutable_codec();
  inline ::std::string* release_codec();
  inline void set_allocated_codec(::std::string* codec);

  // repeated .hadoop.hdfs.fsimage.FileSummary.Section sections = 4;
  inline int sections_size() const;
  inline void clear_sections();
  static const int kSectionsFieldNumber = 4;
  inline const ::hadoop::hdfs::fsimage::FileSummary_Section& sections(int index) const;
  inline ::hadoop::hdfs::fsimage::FileSummary_Section* mutable_sections(int index);
  inline ::hadoop::hdfs::fsimage::FileSummary_Section* add_sections();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::FileSummary_Section >&
      sections() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::FileSummary_Section >*
      mutable_sections();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.FileSummary)
 private:
  inline void set_has_ondiskversion();
  inline void clear_has_ondiskversion();
  inline void set_has_layoutversion();
  inline void clear_has_layoutversion();
  inline void set_has_codec();
  inline void clear_has_codec();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 ondiskversion_;
  ::google::protobuf::uint32 layoutversion_;
  ::std::string* codec_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::FileSummary_Section > sections_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static FileSummary* default_instance_;
};
// -------------------------------------------------------------------

class NameSystemSection : public ::google::protobuf::Message {
 public:
  NameSystemSection();
  virtual ~NameSystemSection();

  NameSystemSection(const NameSystemSection& from);

  inline NameSystemSection& operator=(const NameSystemSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NameSystemSection& default_instance();

  void Swap(NameSystemSection* other);

  // implements Message ----------------------------------------------

  NameSystemSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NameSystemSection& from);
  void MergeFrom(const NameSystemSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 namespaceId = 1;
  inline bool has_namespaceid() const;
  inline void clear_namespaceid();
  static const int kNamespaceIdFieldNumber = 1;
  inline ::google::protobuf::uint32 namespaceid() const;
  inline void set_namespaceid(::google::protobuf::uint32 value);

  // optional uint64 genstampV1 = 2;
  inline bool has_genstampv1() const;
  inline void clear_genstampv1();
  static const int kGenstampV1FieldNumber = 2;
  inline ::google::protobuf::uint64 genstampv1() const;
  inline void set_genstampv1(::google::protobuf::uint64 value);

  // optional uint64 genstampV2 = 3;
  inline bool has_genstampv2() const;
  inline void clear_genstampv2();
  static const int kGenstampV2FieldNumber = 3;
  inline ::google::protobuf::uint64 genstampv2() const;
  inline void set_genstampv2(::google::protobuf::uint64 value);

  // optional uint64 genstampV1Limit = 4;
  inline bool has_genstampv1limit() const;
  inline void clear_genstampv1limit();
  static const int kGenstampV1LimitFieldNumber = 4;
  inline ::google::protobuf::uint64 genstampv1limit() const;
  inline void set_genstampv1limit(::google::protobuf::uint64 value);

  // optional uint64 lastAllocatedBlockId = 5;
  inline bool has_lastallocatedblockid() const;
  inline void clear_lastallocatedblockid();
  static const int kLastAllocatedBlockIdFieldNumber = 5;
  inline ::google::protobuf::uint64 lastallocatedblockid() const;
  inline void set_lastallocatedblockid(::google::protobuf::uint64 value);

  // optional uint64 transactionId = 6;
  inline bool has_transactionid() const;
  inline void clear_transactionid();
  static const int kTransactionIdFieldNumber = 6;
  inline ::google::protobuf::uint64 transactionid() const;
  inline void set_transactionid(::google::protobuf::uint64 value);

  // optional uint64 rollingUpgradeStartTime = 7;
  inline bool has_rollingupgradestarttime() const;
  inline void clear_rollingupgradestarttime();
  static const int kRollingUpgradeStartTimeFieldNumber = 7;
  inline ::google::protobuf::uint64 rollingupgradestarttime() const;
  inline void set_rollingupgradestarttime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.NameSystemSection)
 private:
  inline void set_has_namespaceid();
  inline void clear_has_namespaceid();
  inline void set_has_genstampv1();
  inline void clear_has_genstampv1();
  inline void set_has_genstampv2();
  inline void clear_has_genstampv2();
  inline void set_has_genstampv1limit();
  inline void clear_has_genstampv1limit();
  inline void set_has_lastallocatedblockid();
  inline void clear_has_lastallocatedblockid();
  inline void set_has_transactionid();
  inline void clear_has_transactionid();
  inline void set_has_rollingupgradestarttime();
  inline void clear_has_rollingupgradestarttime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 genstampv1_;
  ::google::protobuf::uint64 genstampv2_;
  ::google::protobuf::uint64 genstampv1limit_;
  ::google::protobuf::uint64 lastallocatedblockid_;
  ::google::protobuf::uint64 transactionid_;
  ::google::protobuf::uint64 rollingupgradestarttime_;
  ::google::protobuf::uint32 namespaceid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(7 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static NameSystemSection* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_FileUnderConstructionFeature : public ::google::protobuf::Message {
 public:
  INodeSection_FileUnderConstructionFeature();
  virtual ~INodeSection_FileUnderConstructionFeature();

  INodeSection_FileUnderConstructionFeature(const INodeSection_FileUnderConstructionFeature& from);

  inline INodeSection_FileUnderConstructionFeature& operator=(const INodeSection_FileUnderConstructionFeature& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_FileUnderConstructionFeature& default_instance();

  void Swap(INodeSection_FileUnderConstructionFeature* other);

  // implements Message ----------------------------------------------

  INodeSection_FileUnderConstructionFeature* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_FileUnderConstructionFeature& from);
  void MergeFrom(const INodeSection_FileUnderConstructionFeature& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string clientName = 1;
  inline bool has_clientname() const;
  inline void clear_clientname();
  static const int kClientNameFieldNumber = 1;
  inline const ::std::string& clientname() const;
  inline void set_clientname(const ::std::string& value);
  inline void set_clientname(const char* value);
  inline void set_clientname(const char* value, size_t size);
  inline ::std::string* mutable_clientname();
  inline ::std::string* release_clientname();
  inline void set_allocated_clientname(::std::string* clientname);

  // optional string clientMachine = 2;
  inline bool has_clientmachine() const;
  inline void clear_clientmachine();
  static const int kClientMachineFieldNumber = 2;
  inline const ::std::string& clientmachine() const;
  inline void set_clientmachine(const ::std::string& value);
  inline void set_clientmachine(const char* value);
  inline void set_clientmachine(const char* value, size_t size);
  inline ::std::string* mutable_clientmachine();
  inline ::std::string* release_clientmachine();
  inline void set_allocated_clientmachine(::std::string* clientmachine);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature)
 private:
  inline void set_has_clientname();
  inline void clear_has_clientname();
  inline void set_has_clientmachine();
  inline void clear_has_clientmachine();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* clientname_;
  ::std::string* clientmachine_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_FileUnderConstructionFeature* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_AclFeatureProto : public ::google::protobuf::Message {
 public:
  INodeSection_AclFeatureProto();
  virtual ~INodeSection_AclFeatureProto();

  INodeSection_AclFeatureProto(const INodeSection_AclFeatureProto& from);

  inline INodeSection_AclFeatureProto& operator=(const INodeSection_AclFeatureProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_AclFeatureProto& default_instance();

  void Swap(INodeSection_AclFeatureProto* other);

  // implements Message ----------------------------------------------

  INodeSection_AclFeatureProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_AclFeatureProto& from);
  void MergeFrom(const INodeSection_AclFeatureProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated fixed32 entries = 2 [packed = true];
  inline int entries_size() const;
  inline void clear_entries();
  static const int kEntriesFieldNumber = 2;
  inline ::google::protobuf::uint32 entries(int index) const;
  inline void set_entries(int index, ::google::protobuf::uint32 value);
  inline void add_entries(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      entries() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_entries();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.AclFeatureProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > entries_;
  mutable int _entries_cached_byte_size_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_AclFeatureProto* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_XAttrCompactProto : public ::google::protobuf::Message {
 public:
  INodeSection_XAttrCompactProto();
  virtual ~INodeSection_XAttrCompactProto();

  INodeSection_XAttrCompactProto(const INodeSection_XAttrCompactProto& from);

  inline INodeSection_XAttrCompactProto& operator=(const INodeSection_XAttrCompactProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_XAttrCompactProto& default_instance();

  void Swap(INodeSection_XAttrCompactProto* other);

  // implements Message ----------------------------------------------

  INodeSection_XAttrCompactProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_XAttrCompactProto& from);
  void MergeFrom(const INodeSection_XAttrCompactProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required fixed32 name = 1;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 1;
  inline ::google::protobuf::uint32 name() const;
  inline void set_name(::google::protobuf::uint32 value);

  // optional bytes value = 2;
  inline bool has_value() const;
  inline void clear_value();
  static const int kValueFieldNumber = 2;
  inline const ::std::string& value() const;
  inline void set_value(const ::std::string& value);
  inline void set_value(const char* value);
  inline void set_value(const void* value, size_t size);
  inline ::std::string* mutable_value();
  inline ::std::string* release_value();
  inline void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto)
 private:
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* value_;
  ::google::protobuf::uint32 name_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_XAttrCompactProto* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_XAttrFeatureProto : public ::google::protobuf::Message {
 public:
  INodeSection_XAttrFeatureProto();
  virtual ~INodeSection_XAttrFeatureProto();

  INodeSection_XAttrFeatureProto(const INodeSection_XAttrFeatureProto& from);

  inline INodeSection_XAttrFeatureProto& operator=(const INodeSection_XAttrFeatureProto& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_XAttrFeatureProto& default_instance();

  void Swap(INodeSection_XAttrFeatureProto* other);

  // implements Message ----------------------------------------------

  INodeSection_XAttrFeatureProto* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_XAttrFeatureProto& from);
  void MergeFrom(const INodeSection_XAttrFeatureProto& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto xAttrs = 1;
  inline int xattrs_size() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 1;
  inline const ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto& xattrs(int index) const;
  inline ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto* mutable_xattrs(int index);
  inline ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto* add_xattrs();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto >&
      xattrs() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto >*
      mutable_xattrs();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto > xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_XAttrFeatureProto* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_INodeFile : public ::google::protobuf::Message {
 public:
  INodeSection_INodeFile();
  virtual ~INodeSection_INodeFile();

  INodeSection_INodeFile(const INodeSection_INodeFile& from);

  inline INodeSection_INodeFile& operator=(const INodeSection_INodeFile& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_INodeFile& default_instance();

  void Swap(INodeSection_INodeFile* other);

  // implements Message ----------------------------------------------

  INodeSection_INodeFile* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_INodeFile& from);
  void MergeFrom(const INodeSection_INodeFile& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 replication = 1;
  inline bool has_replication() const;
  inline void clear_replication();
  static const int kReplicationFieldNumber = 1;
  inline ::google::protobuf::uint32 replication() const;
  inline void set_replication(::google::protobuf::uint32 value);

  // optional uint64 modificationTime = 2;
  inline bool has_modificationtime() const;
  inline void clear_modificationtime();
  static const int kModificationTimeFieldNumber = 2;
  inline ::google::protobuf::uint64 modificationtime() const;
  inline void set_modificationtime(::google::protobuf::uint64 value);

  // optional uint64 accessTime = 3;
  inline bool has_accesstime() const;
  inline void clear_accesstime();
  static const int kAccessTimeFieldNumber = 3;
  inline ::google::protobuf::uint64 accesstime() const;
  inline void set_accesstime(::google::protobuf::uint64 value);

  // optional uint64 preferredBlockSize = 4;
  inline bool has_preferredblocksize() const;
  inline void clear_preferredblocksize();
  static const int kPreferredBlockSizeFieldNumber = 4;
  inline ::google::protobuf::uint64 preferredblocksize() const;
  inline void set_preferredblocksize(::google::protobuf::uint64 value);

  // optional fixed64 permission = 5;
  inline bool has_permission() const;
  inline void clear_permission();
  static const int kPermissionFieldNumber = 5;
  inline ::google::protobuf::uint64 permission() const;
  inline void set_permission(::google::protobuf::uint64 value);

  // repeated .hadoop.hdfs.BlockProto blocks = 6;
  inline int blocks_size() const;
  inline void clear_blocks();
  static const int kBlocksFieldNumber = 6;
  inline const ::hadoop::hdfs::BlockProto& blocks(int index) const;
  inline ::hadoop::hdfs::BlockProto* mutable_blocks(int index);
  inline ::hadoop::hdfs::BlockProto* add_blocks();
  inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockProto >&
      blocks() const;
  inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockProto >*
      mutable_blocks();

  // optional .hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature fileUC = 7;
  inline bool has_fileuc() const;
  inline void clear_fileuc();
  static const int kFileUCFieldNumber = 7;
  inline const ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature& fileuc() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* mutable_fileuc();
  inline ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* release_fileuc();
  inline void set_allocated_fileuc(::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* fileuc);

  // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 8;
  inline bool has_acl() const;
  inline void clear_acl();
  static const int kAclFieldNumber = 8;
  inline const ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto& acl() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* mutable_acl();
  inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* release_acl();
  inline void set_allocated_acl(::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* acl);

  // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 9;
  inline bool has_xattrs() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 9;
  inline const ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto& xattrs() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* mutable_xattrs();
  inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* release_xattrs();
  inline void set_allocated_xattrs(::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* xattrs);

  // optional uint32 storagePolicyID = 10;
  inline bool has_storagepolicyid() const;
  inline void clear_storagepolicyid();
  static const int kStoragePolicyIDFieldNumber = 10;
  inline ::google::protobuf::uint32 storagepolicyid() const;
  inline void set_storagepolicyid(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.INodeFile)
 private:
  inline void set_has_replication();
  inline void clear_has_replication();
  inline void set_has_modificationtime();
  inline void clear_has_modificationtime();
  inline void set_has_accesstime();
  inline void clear_has_accesstime();
  inline void set_has_preferredblocksize();
  inline void clear_has_preferredblocksize();
  inline void set_has_permission();
  inline void clear_has_permission();
  inline void set_has_fileuc();
  inline void clear_has_fileuc();
  inline void set_has_acl();
  inline void clear_has_acl();
  inline void set_has_xattrs();
  inline void clear_has_xattrs();
  inline void set_has_storagepolicyid();
  inline void clear_has_storagepolicyid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 modificationtime_;
  ::google::protobuf::uint64 accesstime_;
  ::google::protobuf::uint64 preferredblocksize_;
  ::google::protobuf::uint64 permission_;
  ::google::protobuf::uint32 replication_;
  ::google::protobuf::uint32 storagepolicyid_;
  ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockProto > blocks_;
  ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* fileuc_;
  ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* acl_;
  ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(10 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_INodeFile* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_INodeDirectory : public ::google::protobuf::Message {
 public:
  INodeSection_INodeDirectory();
  virtual ~INodeSection_INodeDirectory();

  INodeSection_INodeDirectory(const INodeSection_INodeDirectory& from);

  inline INodeSection_INodeDirectory& operator=(const INodeSection_INodeDirectory& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_INodeDirectory& default_instance();

  void Swap(INodeSection_INodeDirectory* other);

  // implements Message ----------------------------------------------

  INodeSection_INodeDirectory* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_INodeDirectory& from);
  void MergeFrom(const INodeSection_INodeDirectory& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 modificationTime = 1;
  inline bool has_modificationtime() const;
  inline void clear_modificationtime();
  static const int kModificationTimeFieldNumber = 1;
  inline ::google::protobuf::uint64 modificationtime() const;
  inline void set_modificationtime(::google::protobuf::uint64 value);

  // optional uint64 nsQuota = 2;
  inline bool has_nsquota() const;
  inline void clear_nsquota();
  static const int kNsQuotaFieldNumber = 2;
  inline ::google::protobuf::uint64 nsquota() const;
  inline void set_nsquota(::google::protobuf::uint64 value);

  // optional uint64 dsQuota = 3;
  inline bool has_dsquota() const;
  inline void clear_dsquota();
  static const int kDsQuotaFieldNumber = 3;
  inline ::google::protobuf::uint64 dsquota() const;
  inline void set_dsquota(::google::protobuf::uint64 value);

  // optional fixed64 permission = 4;
  inline bool has_permission() const;
  inline void clear_permission();
  static const int kPermissionFieldNumber = 4;
  inline ::google::protobuf::uint64 permission() const;
  inline void set_permission(::google::protobuf::uint64 value);

  // optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 5;
  inline bool has_acl() const;
  inline void clear_acl();
  static const int kAclFieldNumber = 5;
  inline const ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto& acl() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* mutable_acl();
  inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* release_acl();
  inline void set_allocated_acl(::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* acl);

  // optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 6;
  inline bool has_xattrs() const;
  inline void clear_xattrs();
  static const int kXAttrsFieldNumber = 6;
  inline const ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto& xattrs() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* mutable_xattrs();
  inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* release_xattrs();
  inline void set_allocated_xattrs(::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* xattrs);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.INodeDirectory)
 private:
  inline void set_has_modificationtime();
  inline void clear_has_modificationtime();
  inline void set_has_nsquota();
  inline void clear_has_nsquota();
  inline void set_has_dsquota();
  inline void clear_has_dsquota();
  inline void set_has_permission();
  inline void clear_has_permission();
  inline void set_has_acl();
  inline void clear_has_acl();
  inline void set_has_xattrs();
  inline void clear_has_xattrs();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 modificationtime_;
  ::google::protobuf::uint64 nsquota_;
  ::google::protobuf::uint64 dsquota_;
  ::google::protobuf::uint64 permission_;
  ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* acl_;
  ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* xattrs_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_INodeDirectory* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_INodeSymlink : public ::google::protobuf::Message {
 public:
  INodeSection_INodeSymlink();
  virtual ~INodeSection_INodeSymlink();

  INodeSection_INodeSymlink(const INodeSection_INodeSymlink& from);

  inline INodeSection_INodeSymlink& operator=(const INodeSection_INodeSymlink& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_INodeSymlink& default_instance();

  void Swap(INodeSection_INodeSymlink* other);

  // implements Message ----------------------------------------------

  INodeSection_INodeSymlink* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_INodeSymlink& from);
  void MergeFrom(const INodeSection_INodeSymlink& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional fixed64 permission = 1;
  inline bool has_permission() const;
  inline void clear_permission();
  static const int kPermissionFieldNumber = 1;
  inline ::google::protobuf::uint64 permission() const;
  inline void set_permission(::google::protobuf::uint64 value);

  // optional bytes target = 2;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 2;
  inline const ::std::string& target() const;
  inline void set_target(const ::std::string& value);
  inline void set_target(const char* value);
  inline void set_target(const void* value, size_t size);
  inline ::std::string* mutable_target();
  inline ::std::string* release_target();
  inline void set_allocated_target(::std::string* target);

  // optional uint64 modificationTime = 3;
  inline bool has_modificationtime() const;
  inline void clear_modificationtime();
  static const int kModificationTimeFieldNumber = 3;
  inline ::google::protobuf::uint64 modificationtime() const;
  inline void set_modificationtime(::google::protobuf::uint64 value);

  // optional uint64 accessTime = 4;
  inline bool has_accesstime() const;
  inline void clear_accesstime();
  static const int kAccessTimeFieldNumber = 4;
  inline ::google::protobuf::uint64 accesstime() const;
  inline void set_accesstime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.INodeSymlink)
 private:
  inline void set_has_permission();
  inline void clear_has_permission();
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_modificationtime();
  inline void clear_has_modificationtime();
  inline void set_has_accesstime();
  inline void clear_has_accesstime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 permission_;
  ::std::string* target_;
  ::google::protobuf::uint64 modificationtime_;
  ::google::protobuf::uint64 accesstime_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_INodeSymlink* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection_INode : public ::google::protobuf::Message {
 public:
  INodeSection_INode();
  virtual ~INodeSection_INode();

  INodeSection_INode(const INodeSection_INode& from);

  inline INodeSection_INode& operator=(const INodeSection_INode& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection_INode& default_instance();

  void Swap(INodeSection_INode* other);

  // implements Message ----------------------------------------------

  INodeSection_INode* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection_INode& from);
  void MergeFrom(const INodeSection_INode& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef INodeSection_INode_Type Type;
  static const Type FILE = INodeSection_INode_Type_FILE;
  static const Type DIRECTORY = INodeSection_INode_Type_DIRECTORY;
  static const Type SYMLINK = INodeSection_INode_Type_SYMLINK;
  static inline bool Type_IsValid(int value) {
    return INodeSection_INode_Type_IsValid(value);
  }
  static const Type Type_MIN =
    INodeSection_INode_Type_Type_MIN;
  static const Type Type_MAX =
    INodeSection_INode_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    INodeSection_INode_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return INodeSection_INode_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return INodeSection_INode_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return INodeSection_INode_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.fsimage.INodeSection.INode.Type type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::hadoop::hdfs::fsimage::INodeSection_INode_Type type() const;
  inline void set_type(::hadoop::hdfs::fsimage::INodeSection_INode_Type value);

  // required uint64 id = 2;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 2;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes name = 3;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 3;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const void* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile file = 4;
  inline bool has_file() const;
  inline void clear_file();
  static const int kFileFieldNumber = 4;
  inline const ::hadoop::hdfs::fsimage::INodeSection_INodeFile& file() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* mutable_file();
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* release_file();
  inline void set_allocated_file(::hadoop::hdfs::fsimage::INodeSection_INodeFile* file);

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory directory = 5;
  inline bool has_directory() const;
  inline void clear_directory();
  static const int kDirectoryFieldNumber = 5;
  inline const ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory& directory() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* mutable_directory();
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* release_directory();
  inline void set_allocated_directory(::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* directory);

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeSymlink symlink = 6;
  inline bool has_symlink() const;
  inline void clear_symlink();
  static const int kSymlinkFieldNumber = 6;
  inline const ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink& symlink() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* mutable_symlink();
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* release_symlink();
  inline void set_allocated_symlink(::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* symlink);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection.INode)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_file();
  inline void clear_has_file();
  inline void set_has_directory();
  inline void clear_has_directory();
  inline void set_has_symlink();
  inline void clear_has_symlink();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 id_;
  ::std::string* name_;
  ::hadoop::hdfs::fsimage::INodeSection_INodeFile* file_;
  ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* directory_;
  ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* symlink_;
  int type_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(6 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection_INode* default_instance_;
};
// -------------------------------------------------------------------

class INodeSection : public ::google::protobuf::Message {
 public:
  INodeSection();
  virtual ~INodeSection();

  INodeSection(const INodeSection& from);

  inline INodeSection& operator=(const INodeSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeSection& default_instance();

  void Swap(INodeSection* other);

  // implements Message ----------------------------------------------

  INodeSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeSection& from);
  void MergeFrom(const INodeSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef INodeSection_FileUnderConstructionFeature FileUnderConstructionFeature;
  typedef INodeSection_AclFeatureProto AclFeatureProto;
  typedef INodeSection_XAttrCompactProto XAttrCompactProto;
  typedef INodeSection_XAttrFeatureProto XAttrFeatureProto;
  typedef INodeSection_INodeFile INodeFile;
  typedef INodeSection_INodeDirectory INodeDirectory;
  typedef INodeSection_INodeSymlink INodeSymlink;
  typedef INodeSection_INode INode;

  // accessors -------------------------------------------------------

  // optional uint64 lastInodeId = 1;
  inline bool has_lastinodeid() const;
  inline void clear_lastinodeid();
  static const int kLastInodeIdFieldNumber = 1;
  inline ::google::protobuf::uint64 lastinodeid() const;
  inline void set_lastinodeid(::google::protobuf::uint64 value);

  // optional uint64 numInodes = 2;
  inline bool has_numinodes() const;
  inline void clear_numinodes();
  static const int kNumInodesFieldNumber = 2;
  inline ::google::protobuf::uint64 numinodes() const;
  inline void set_numinodes(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeSection)
 private:
  inline void set_has_lastinodeid();
  inline void clear_has_lastinodeid();
  inline void set_has_numinodes();
  inline void clear_has_numinodes();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 lastinodeid_;
  ::google::protobuf::uint64 numinodes_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeSection* default_instance_;
};
// -------------------------------------------------------------------

class FilesUnderConstructionSection_FileUnderConstructionEntry : public ::google::protobuf::Message {
 public:
  FilesUnderConstructionSection_FileUnderConstructionEntry();
  virtual ~FilesUnderConstructionSection_FileUnderConstructionEntry();

  FilesUnderConstructionSection_FileUnderConstructionEntry(const FilesUnderConstructionSection_FileUnderConstructionEntry& from);

  inline FilesUnderConstructionSection_FileUnderConstructionEntry& operator=(const FilesUnderConstructionSection_FileUnderConstructionEntry& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FilesUnderConstructionSection_FileUnderConstructionEntry& default_instance();

  void Swap(FilesUnderConstructionSection_FileUnderConstructionEntry* other);

  // implements Message ----------------------------------------------

  FilesUnderConstructionSection_FileUnderConstructionEntry* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FilesUnderConstructionSection_FileUnderConstructionEntry& from);
  void MergeFrom(const FilesUnderConstructionSection_FileUnderConstructionEntry& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 inodeId = 1;
  inline bool has_inodeid() const;
  inline void clear_inodeid();
  static const int kInodeIdFieldNumber = 1;
  inline ::google::protobuf::uint64 inodeid() const;
  inline void set_inodeid(::google::protobuf::uint64 value);

  // optional string fullPath = 2;
  inline bool has_fullpath() const;
  inline void clear_fullpath();
  static const int kFullPathFieldNumber = 2;
  inline const ::std::string& fullpath() const;
  inline void set_fullpath(const ::std::string& value);
  inline void set_fullpath(const char* value);
  inline void set_fullpath(const char* value, size_t size);
  inline ::std::string* mutable_fullpath();
  inline ::std::string* release_fullpath();
  inline void set_allocated_fullpath(::std::string* fullpath);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.FilesUnderConstructionSection.FileUnderConstructionEntry)
 private:
  inline void set_has_inodeid();
  inline void clear_has_inodeid();
  inline void set_has_fullpath();
  inline void clear_has_fullpath();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 inodeid_;
  ::std::string* fullpath_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static FilesUnderConstructionSection_FileUnderConstructionEntry* default_instance_;
};
// -------------------------------------------------------------------

class FilesUnderConstructionSection : public ::google::protobuf::Message {
 public:
  FilesUnderConstructionSection();
  virtual ~FilesUnderConstructionSection();

  FilesUnderConstructionSection(const FilesUnderConstructionSection& from);

  inline FilesUnderConstructionSection& operator=(const FilesUnderConstructionSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FilesUnderConstructionSection& default_instance();

  void Swap(FilesUnderConstructionSection* other);

  // implements Message ----------------------------------------------

  FilesUnderConstructionSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FilesUnderConstructionSection& from);
  void MergeFrom(const FilesUnderConstructionSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef FilesUnderConstructionSection_FileUnderConstructionEntry FileUnderConstructionEntry;

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.FilesUnderConstructionSection)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static FilesUnderConstructionSection* default_instance_;
};
// -------------------------------------------------------------------

class INodeDirectorySection_DirEntry : public ::google::protobuf::Message {
 public:
  INodeDirectorySection_DirEntry();
  virtual ~INodeDirectorySection_DirEntry();

  INodeDirectorySection_DirEntry(const INodeDirectorySection_DirEntry& from);

  inline INodeDirectorySection_DirEntry& operator=(const INodeDirectorySection_DirEntry& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeDirectorySection_DirEntry& default_instance();

  void Swap(INodeDirectorySection_DirEntry* other);

  // implements Message ----------------------------------------------

  INodeDirectorySection_DirEntry* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeDirectorySection_DirEntry& from);
  void MergeFrom(const INodeDirectorySection_DirEntry& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 parent = 1;
  inline bool has_parent() const;
  inline void clear_parent();
  static const int kParentFieldNumber = 1;
  inline ::google::protobuf::uint64 parent() const;
  inline void set_parent(::google::protobuf::uint64 value);

  // repeated uint64 children = 2 [packed = true];
  inline int children_size() const;
  inline void clear_children();
  static const int kChildrenFieldNumber = 2;
  inline ::google::protobuf::uint64 children(int index) const;
  inline void set_children(int index, ::google::protobuf::uint64 value);
  inline void add_children(::google::protobuf::uint64 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      children() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_children();

  // repeated uint32 refChildren = 3 [packed = true];
  inline int refchildren_size() const;
  inline void clear_refchildren();
  static const int kRefChildrenFieldNumber = 3;
  inline ::google::protobuf::uint32 refchildren(int index) const;
  inline void set_refchildren(int index, ::google::protobuf::uint32 value);
  inline void add_refchildren(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      refchildren() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_refchildren();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeDirectorySection.DirEntry)
 private:
  inline void set_has_parent();
  inline void clear_has_parent();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 parent_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > children_;
  mutable int _children_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > refchildren_;
  mutable int _refchildren_cached_byte_size_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeDirectorySection_DirEntry* default_instance_;
};
// -------------------------------------------------------------------

class INodeDirectorySection : public ::google::protobuf::Message {
 public:
  INodeDirectorySection();
  virtual ~INodeDirectorySection();

  INodeDirectorySection(const INodeDirectorySection& from);

  inline INodeDirectorySection& operator=(const INodeDirectorySection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeDirectorySection& default_instance();

  void Swap(INodeDirectorySection* other);

  // implements Message ----------------------------------------------

  INodeDirectorySection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeDirectorySection& from);
  void MergeFrom(const INodeDirectorySection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef INodeDirectorySection_DirEntry DirEntry;

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeDirectorySection)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeDirectorySection* default_instance_;
};
// -------------------------------------------------------------------

class INodeReferenceSection_INodeReference : public ::google::protobuf::Message {
 public:
  INodeReferenceSection_INodeReference();
  virtual ~INodeReferenceSection_INodeReference();

  INodeReferenceSection_INodeReference(const INodeReferenceSection_INodeReference& from);

  inline INodeReferenceSection_INodeReference& operator=(const INodeReferenceSection_INodeReference& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeReferenceSection_INodeReference& default_instance();

  void Swap(INodeReferenceSection_INodeReference* other);

  // implements Message ----------------------------------------------

  INodeReferenceSection_INodeReference* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeReferenceSection_INodeReference& from);
  void MergeFrom(const INodeReferenceSection_INodeReference& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 referredId = 1;
  inline bool has_referredid() const;
  inline void clear_referredid();
  static const int kReferredIdFieldNumber = 1;
  inline ::google::protobuf::uint64 referredid() const;
  inline void set_referredid(::google::protobuf::uint64 value);

  // optional bytes name = 2;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 2;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const void* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional uint32 dstSnapshotId = 3;
  inline bool has_dstsnapshotid() const;
  inline void clear_dstsnapshotid();
  static const int kDstSnapshotIdFieldNumber = 3;
  inline ::google::protobuf::uint32 dstsnapshotid() const;
  inline void set_dstsnapshotid(::google::protobuf::uint32 value);

  // optional uint32 lastSnapshotId = 4;
  inline bool has_lastsnapshotid() const;
  inline void clear_lastsnapshotid();
  static const int kLastSnapshotIdFieldNumber = 4;
  inline ::google::protobuf::uint32 lastsnapshotid() const;
  inline void set_lastsnapshotid(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeReferenceSection.INodeReference)
 private:
  inline void set_has_referredid();
  inline void clear_has_referredid();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_dstsnapshotid();
  inline void clear_has_dstsnapshotid();
  inline void set_has_lastsnapshotid();
  inline void clear_has_lastsnapshotid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 referredid_;
  ::std::string* name_;
  ::google::protobuf::uint32 dstsnapshotid_;
  ::google::protobuf::uint32 lastsnapshotid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeReferenceSection_INodeReference* default_instance_;
};
// -------------------------------------------------------------------

class INodeReferenceSection : public ::google::protobuf::Message {
 public:
  INodeReferenceSection();
  virtual ~INodeReferenceSection();

  INodeReferenceSection(const INodeReferenceSection& from);

  inline INodeReferenceSection& operator=(const INodeReferenceSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const INodeReferenceSection& default_instance();

  void Swap(INodeReferenceSection* other);

  // implements Message ----------------------------------------------

  INodeReferenceSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const INodeReferenceSection& from);
  void MergeFrom(const INodeReferenceSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef INodeReferenceSection_INodeReference INodeReference;

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.INodeReferenceSection)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static INodeReferenceSection* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotSection_Snapshot : public ::google::protobuf::Message {
 public:
  SnapshotSection_Snapshot();
  virtual ~SnapshotSection_Snapshot();

  SnapshotSection_Snapshot(const SnapshotSection_Snapshot& from);

  inline SnapshotSection_Snapshot& operator=(const SnapshotSection_Snapshot& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotSection_Snapshot& default_instance();

  void Swap(SnapshotSection_Snapshot* other);

  // implements Message ----------------------------------------------

  SnapshotSection_Snapshot* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotSection_Snapshot& from);
  void MergeFrom(const SnapshotSection_Snapshot& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 snapshotId = 1;
  inline bool has_snapshotid() const;
  inline void clear_snapshotid();
  static const int kSnapshotIdFieldNumber = 1;
  inline ::google::protobuf::uint32 snapshotid() const;
  inline void set_snapshotid(::google::protobuf::uint32 value);

  // optional .hadoop.hdfs.fsimage.INodeSection.INode root = 2;
  inline bool has_root() const;
  inline void clear_root();
  static const int kRootFieldNumber = 2;
  inline const ::hadoop::hdfs::fsimage::INodeSection_INode& root() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_INode* mutable_root();
  inline ::hadoop::hdfs::fsimage::INodeSection_INode* release_root();
  inline void set_allocated_root(::hadoop::hdfs::fsimage::INodeSection_INode* root);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotSection.Snapshot)
 private:
  inline void set_has_snapshotid();
  inline void clear_has_snapshotid();
  inline void set_has_root();
  inline void clear_has_root();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::hadoop::hdfs::fsimage::INodeSection_INode* root_;
  ::google::protobuf::uint32 snapshotid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotSection_Snapshot* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotSection : public ::google::protobuf::Message {
 public:
  SnapshotSection();
  virtual ~SnapshotSection();

  SnapshotSection(const SnapshotSection& from);

  inline SnapshotSection& operator=(const SnapshotSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotSection& default_instance();

  void Swap(SnapshotSection* other);

  // implements Message ----------------------------------------------

  SnapshotSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotSection& from);
  void MergeFrom(const SnapshotSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef SnapshotSection_Snapshot Snapshot;

  // accessors -------------------------------------------------------

  // optional uint32 snapshotCounter = 1;
  inline bool has_snapshotcounter() const;
  inline void clear_snapshotcounter();
  static const int kSnapshotCounterFieldNumber = 1;
  inline ::google::protobuf::uint32 snapshotcounter() const;
  inline void set_snapshotcounter(::google::protobuf::uint32 value);

  // repeated uint64 snapshottableDir = 2 [packed = true];
  inline int snapshottabledir_size() const;
  inline void clear_snapshottabledir();
  static const int kSnapshottableDirFieldNumber = 2;
  inline ::google::protobuf::uint64 snapshottabledir(int index) const;
  inline void set_snapshottabledir(int index, ::google::protobuf::uint64 value);
  inline void add_snapshottabledir(::google::protobuf::uint64 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      snapshottabledir() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_snapshottabledir();

  // optional uint32 numSnapshots = 3;
  inline bool has_numsnapshots() const;
  inline void clear_numsnapshots();
  static const int kNumSnapshotsFieldNumber = 3;
  inline ::google::protobuf::uint32 numsnapshots() const;
  inline void set_numsnapshots(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotSection)
 private:
  inline void set_has_snapshotcounter();
  inline void clear_has_snapshotcounter();
  inline void set_has_numsnapshots();
  inline void clear_has_numsnapshots();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > snapshottabledir_;
  mutable int _snapshottabledir_cached_byte_size_;
  ::google::protobuf::uint32 snapshotcounter_;
  ::google::protobuf::uint32 numsnapshots_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotSection* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffSection_CreatedListEntry : public ::google::protobuf::Message {
 public:
  SnapshotDiffSection_CreatedListEntry();
  virtual ~SnapshotDiffSection_CreatedListEntry();

  SnapshotDiffSection_CreatedListEntry(const SnapshotDiffSection_CreatedListEntry& from);

  inline SnapshotDiffSection_CreatedListEntry& operator=(const SnapshotDiffSection_CreatedListEntry& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffSection_CreatedListEntry& default_instance();

  void Swap(SnapshotDiffSection_CreatedListEntry* other);

  // implements Message ----------------------------------------------

  SnapshotDiffSection_CreatedListEntry* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffSection_CreatedListEntry& from);
  void MergeFrom(const SnapshotDiffSection_CreatedListEntry& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bytes name = 1;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 1;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const void* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotDiffSection.CreatedListEntry)
 private:
  inline void set_has_name();
  inline void clear_has_name();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* name_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffSection_CreatedListEntry* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffSection_DirectoryDiff : public ::google::protobuf::Message {
 public:
  SnapshotDiffSection_DirectoryDiff();
  virtual ~SnapshotDiffSection_DirectoryDiff();

  SnapshotDiffSection_DirectoryDiff(const SnapshotDiffSection_DirectoryDiff& from);

  inline SnapshotDiffSection_DirectoryDiff& operator=(const SnapshotDiffSection_DirectoryDiff& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffSection_DirectoryDiff& default_instance();

  void Swap(SnapshotDiffSection_DirectoryDiff* other);

  // implements Message ----------------------------------------------

  SnapshotDiffSection_DirectoryDiff* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffSection_DirectoryDiff& from);
  void MergeFrom(const SnapshotDiffSection_DirectoryDiff& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 snapshotId = 1;
  inline bool has_snapshotid() const;
  inline void clear_snapshotid();
  static const int kSnapshotIdFieldNumber = 1;
  inline ::google::protobuf::uint32 snapshotid() const;
  inline void set_snapshotid(::google::protobuf::uint32 value);

  // optional uint32 childrenSize = 2;
  inline bool has_childrensize() const;
  inline void clear_childrensize();
  static const int kChildrenSizeFieldNumber = 2;
  inline ::google::protobuf::uint32 childrensize() const;
  inline void set_childrensize(::google::protobuf::uint32 value);

  // optional bool isSnapshotRoot = 3;
  inline bool has_issnapshotroot() const;
  inline void clear_issnapshotroot();
  static const int kIsSnapshotRootFieldNumber = 3;
  inline bool issnapshotroot() const;
  inline void set_issnapshotroot(bool value);

  // optional bytes name = 4;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 4;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const void* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory snapshotCopy = 5;
  inline bool has_snapshotcopy() const;
  inline void clear_snapshotcopy();
  static const int kSnapshotCopyFieldNumber = 5;
  inline const ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory& snapshotcopy() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* mutable_snapshotcopy();
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* release_snapshotcopy();
  inline void set_allocated_snapshotcopy(::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* snapshotcopy);

  // optional uint32 createdListSize = 6;
  inline bool has_createdlistsize() const;
  inline void clear_createdlistsize();
  static const int kCreatedListSizeFieldNumber = 6;
  inline ::google::protobuf::uint32 createdlistsize() const;
  inline void set_createdlistsize(::google::protobuf::uint32 value);

  // repeated uint64 deletedINode = 7 [packed = true];
  inline int deletedinode_size() const;
  inline void clear_deletedinode();
  static const int kDeletedINodeFieldNumber = 7;
  inline ::google::protobuf::uint64 deletedinode(int index) const;
  inline void set_deletedinode(int index, ::google::protobuf::uint64 value);
  inline void add_deletedinode(::google::protobuf::uint64 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      deletedinode() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_deletedinode();

  // repeated uint32 deletedINodeRef = 8 [packed = true];
  inline int deletedinoderef_size() const;
  inline void clear_deletedinoderef();
  static const int kDeletedINodeRefFieldNumber = 8;
  inline ::google::protobuf::uint32 deletedinoderef(int index) const;
  inline void set_deletedinoderef(int index, ::google::protobuf::uint32 value);
  inline void add_deletedinoderef(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      deletedinoderef() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_deletedinoderef();

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotDiffSection.DirectoryDiff)
 private:
  inline void set_has_snapshotid();
  inline void clear_has_snapshotid();
  inline void set_has_childrensize();
  inline void clear_has_childrensize();
  inline void set_has_issnapshotroot();
  inline void clear_has_issnapshotroot();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_snapshotcopy();
  inline void clear_has_snapshotcopy();
  inline void set_has_createdlistsize();
  inline void clear_has_createdlistsize();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 snapshotid_;
  ::google::protobuf::uint32 childrensize_;
  ::std::string* name_;
  bool issnapshotroot_;
  ::google::protobuf::uint32 createdlistsize_;
  ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* snapshotcopy_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > deletedinode_;
  mutable int _deletedinode_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > deletedinoderef_;
  mutable int _deletedinoderef_cached_byte_size_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(8 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffSection_DirectoryDiff* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffSection_FileDiff : public ::google::protobuf::Message {
 public:
  SnapshotDiffSection_FileDiff();
  virtual ~SnapshotDiffSection_FileDiff();

  SnapshotDiffSection_FileDiff(const SnapshotDiffSection_FileDiff& from);

  inline SnapshotDiffSection_FileDiff& operator=(const SnapshotDiffSection_FileDiff& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffSection_FileDiff& default_instance();

  void Swap(SnapshotDiffSection_FileDiff* other);

  // implements Message ----------------------------------------------

  SnapshotDiffSection_FileDiff* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffSection_FileDiff& from);
  void MergeFrom(const SnapshotDiffSection_FileDiff& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 snapshotId = 1;
  inline bool has_snapshotid() const;
  inline void clear_snapshotid();
  static const int kSnapshotIdFieldNumber = 1;
  inline ::google::protobuf::uint32 snapshotid() const;
  inline void set_snapshotid(::google::protobuf::uint32 value);

  // optional uint64 fileSize = 2;
  inline bool has_filesize() const;
  inline void clear_filesize();
  static const int kFileSizeFieldNumber = 2;
  inline ::google::protobuf::uint64 filesize() const;
  inline void set_filesize(::google::protobuf::uint64 value);

  // optional bytes name = 3;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 3;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const void* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional .hadoop.hdfs.fsimage.INodeSection.INodeFile snapshotCopy = 4;
  inline bool has_snapshotcopy() const;
  inline void clear_snapshotcopy();
  static const int kSnapshotCopyFieldNumber = 4;
  inline const ::hadoop::hdfs::fsimage::INodeSection_INodeFile& snapshotcopy() const;
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* mutable_snapshotcopy();
  inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* release_snapshotcopy();
  inline void set_allocated_snapshotcopy(::hadoop::hdfs::fsimage::INodeSection_INodeFile* snapshotcopy);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotDiffSection.FileDiff)
 private:
  inline void set_has_snapshotid();
  inline void clear_has_snapshotid();
  inline void set_has_filesize();
  inline void clear_has_filesize();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_snapshotcopy();
  inline void clear_has_snapshotcopy();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 filesize_;
  ::std::string* name_;
  ::hadoop::hdfs::fsimage::INodeSection_INodeFile* snapshotcopy_;
  ::google::protobuf::uint32 snapshotid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffSection_FileDiff* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffSection_DiffEntry : public ::google::protobuf::Message {
 public:
  SnapshotDiffSection_DiffEntry();
  virtual ~SnapshotDiffSection_DiffEntry();

  SnapshotDiffSection_DiffEntry(const SnapshotDiffSection_DiffEntry& from);

  inline SnapshotDiffSection_DiffEntry& operator=(const SnapshotDiffSection_DiffEntry& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffSection_DiffEntry& default_instance();

  void Swap(SnapshotDiffSection_DiffEntry* other);

  // implements Message ----------------------------------------------

  SnapshotDiffSection_DiffEntry* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffSection_DiffEntry& from);
  void MergeFrom(const SnapshotDiffSection_DiffEntry& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef SnapshotDiffSection_DiffEntry_Type Type;
  static const Type FILEDIFF = SnapshotDiffSection_DiffEntry_Type_FILEDIFF;
  static const Type DIRECTORYDIFF = SnapshotDiffSection_DiffEntry_Type_DIRECTORYDIFF;
  static inline bool Type_IsValid(int value) {
    return SnapshotDiffSection_DiffEntry_Type_IsValid(value);
  }
  static const Type Type_MIN =
    SnapshotDiffSection_DiffEntry_Type_Type_MIN;
  static const Type Type_MAX =
    SnapshotDiffSection_DiffEntry_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    SnapshotDiffSection_DiffEntry_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return SnapshotDiffSection_DiffEntry_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return SnapshotDiffSection_DiffEntry_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return SnapshotDiffSection_DiffEntry_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry.Type type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type type() const;
  inline void set_type(::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type value);

  // optional uint64 inodeId = 2;
  inline bool has_inodeid() const;
  inline void clear_inodeid();
  static const int kInodeIdFieldNumber = 2;
  inline ::google::protobuf::uint64 inodeid() const;
  inline void set_inodeid(::google::protobuf::uint64 value);

  // optional uint32 numOfDiff = 3;
  inline bool has_numofdiff() const;
  inline void clear_numofdiff();
  static const int kNumOfDiffFieldNumber = 3;
  inline ::google::protobuf::uint32 numofdiff() const;
  inline void set_numofdiff(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_inodeid();
  inline void clear_has_inodeid();
  inline void set_has_numofdiff();
  inline void clear_has_numofdiff();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 inodeid_;
  int type_;
  ::google::protobuf::uint32 numofdiff_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffSection_DiffEntry* default_instance_;
};
// -------------------------------------------------------------------

class SnapshotDiffSection : public ::google::protobuf::Message {
 public:
  SnapshotDiffSection();
  virtual ~SnapshotDiffSection();

  SnapshotDiffSection(const SnapshotDiffSection& from);

  inline SnapshotDiffSection& operator=(const SnapshotDiffSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SnapshotDiffSection& default_instance();

  void Swap(SnapshotDiffSection* other);

  // implements Message ----------------------------------------------

  SnapshotDiffSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SnapshotDiffSection& from);
  void MergeFrom(const SnapshotDiffSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef SnapshotDiffSection_CreatedListEntry CreatedListEntry;
  typedef SnapshotDiffSection_DirectoryDiff DirectoryDiff;
  typedef SnapshotDiffSection_FileDiff FileDiff;
  typedef SnapshotDiffSection_DiffEntry DiffEntry;

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SnapshotDiffSection)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;


  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[1];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SnapshotDiffSection* default_instance_;
};
// -------------------------------------------------------------------

class StringTableSection_Entry : public ::google::protobuf::Message {
 public:
  StringTableSection_Entry();
  virtual ~StringTableSection_Entry();

  StringTableSection_Entry(const StringTableSection_Entry& from);

  inline StringTableSection_Entry& operator=(const StringTableSection_Entry& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StringTableSection_Entry& default_instance();

  void Swap(StringTableSection_Entry* other);

  // implements Message ----------------------------------------------

  StringTableSection_Entry* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StringTableSection_Entry& from);
  void MergeFrom(const StringTableSection_Entry& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint32 id() const;
  inline void set_id(::google::protobuf::uint32 value);

  // optional string str = 2;
  inline bool has_str() const;
  inline void clear_str();
  static const int kStrFieldNumber = 2;
  inline const ::std::string& str() const;
  inline void set_str(const ::std::string& value);
  inline void set_str(const char* value);
  inline void set_str(const char* value, size_t size);
  inline ::std::string* mutable_str();
  inline ::std::string* release_str();
  inline void set_allocated_str(::std::string* str);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.StringTableSection.Entry)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_str();
  inline void clear_has_str();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* str_;
  ::google::protobuf::uint32 id_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(2 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static StringTableSection_Entry* default_instance_;
};
// -------------------------------------------------------------------

class StringTableSection : public ::google::protobuf::Message {
 public:
  StringTableSection();
  virtual ~StringTableSection();

  StringTableSection(const StringTableSection& from);

  inline StringTableSection& operator=(const StringTableSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StringTableSection& default_instance();

  void Swap(StringTableSection* other);

  // implements Message ----------------------------------------------

  StringTableSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StringTableSection& from);
  void MergeFrom(const StringTableSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef StringTableSection_Entry Entry;

  // accessors -------------------------------------------------------

  // optional uint32 numEntry = 1;
  inline bool has_numentry() const;
  inline void clear_numentry();
  static const int kNumEntryFieldNumber = 1;
  inline ::google::protobuf::uint32 numentry() const;
  inline void set_numentry(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.StringTableSection)
 private:
  inline void set_has_numentry();
  inline void clear_has_numentry();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 numentry_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(1 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static StringTableSection* default_instance_;
};
// -------------------------------------------------------------------

class SecretManagerSection_DelegationKey : public ::google::protobuf::Message {
 public:
  SecretManagerSection_DelegationKey();
  virtual ~SecretManagerSection_DelegationKey();

  SecretManagerSection_DelegationKey(const SecretManagerSection_DelegationKey& from);

  inline SecretManagerSection_DelegationKey& operator=(const SecretManagerSection_DelegationKey& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SecretManagerSection_DelegationKey& default_instance();

  void Swap(SecretManagerSection_DelegationKey* other);

  // implements Message ----------------------------------------------

  SecretManagerSection_DelegationKey* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SecretManagerSection_DelegationKey& from);
  void MergeFrom(const SecretManagerSection_DelegationKey& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint32 id() const;
  inline void set_id(::google::protobuf::uint32 value);

  // optional uint64 expiryDate = 2;
  inline bool has_expirydate() const;
  inline void clear_expirydate();
  static const int kExpiryDateFieldNumber = 2;
  inline ::google::protobuf::uint64 expirydate() const;
  inline void set_expirydate(::google::protobuf::uint64 value);

  // optional bytes key = 3;
  inline bool has_key() const;
  inline void clear_key();
  static const int kKeyFieldNumber = 3;
  inline const ::std::string& key() const;
  inline void set_key(const ::std::string& value);
  inline void set_key(const char* value);
  inline void set_key(const void* value, size_t size);
  inline ::std::string* mutable_key();
  inline ::std::string* release_key();
  inline void set_allocated_key(::std::string* key);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SecretManagerSection.DelegationKey)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_expirydate();
  inline void clear_has_expirydate();
  inline void set_has_key();
  inline void clear_has_key();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 expirydate_;
  ::std::string* key_;
  ::google::protobuf::uint32 id_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SecretManagerSection_DelegationKey* default_instance_;
};
// -------------------------------------------------------------------

class SecretManagerSection_PersistToken : public ::google::protobuf::Message {
 public:
  SecretManagerSection_PersistToken();
  virtual ~SecretManagerSection_PersistToken();

  SecretManagerSection_PersistToken(const SecretManagerSection_PersistToken& from);

  inline SecretManagerSection_PersistToken& operator=(const SecretManagerSection_PersistToken& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SecretManagerSection_PersistToken& default_instance();

  void Swap(SecretManagerSection_PersistToken* other);

  // implements Message ----------------------------------------------

  SecretManagerSection_PersistToken* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SecretManagerSection_PersistToken& from);
  void MergeFrom(const SecretManagerSection_PersistToken& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 version = 1;
  inline bool has_version() const;
  inline void clear_version();
  static const int kVersionFieldNumber = 1;
  inline ::google::protobuf::uint32 version() const;
  inline void set_version(::google::protobuf::uint32 value);

  // optional string owner = 2;
  inline bool has_owner() const;
  inline void clear_owner();
  static const int kOwnerFieldNumber = 2;
  inline const ::std::string& owner() const;
  inline void set_owner(const ::std::string& value);
  inline void set_owner(const char* value);
  inline void set_owner(const char* value, size_t size);
  inline ::std::string* mutable_owner();
  inline ::std::string* release_owner();
  inline void set_allocated_owner(::std::string* owner);

  // optional string renewer = 3;
  inline bool has_renewer() const;
  inline void clear_renewer();
  static const int kRenewerFieldNumber = 3;
  inline const ::std::string& renewer() const;
  inline void set_renewer(const ::std::string& value);
  inline void set_renewer(const char* value);
  inline void set_renewer(const char* value, size_t size);
  inline ::std::string* mutable_renewer();
  inline ::std::string* release_renewer();
  inline void set_allocated_renewer(::std::string* renewer);

  // optional string realUser = 4;
  inline bool has_realuser() const;
  inline void clear_realuser();
  static const int kRealUserFieldNumber = 4;
  inline const ::std::string& realuser() const;
  inline void set_realuser(const ::std::string& value);
  inline void set_realuser(const char* value);
  inline void set_realuser(const char* value, size_t size);
  inline ::std::string* mutable_realuser();
  inline ::std::string* release_realuser();
  inline void set_allocated_realuser(::std::string* realuser);

  // optional uint64 issueDate = 5;
  inline bool has_issuedate() const;
  inline void clear_issuedate();
  static const int kIssueDateFieldNumber = 5;
  inline ::google::protobuf::uint64 issuedate() const;
  inline void set_issuedate(::google::protobuf::uint64 value);

  // optional uint64 maxDate = 6;
  inline bool has_maxdate() const;
  inline void clear_maxdate();
  static const int kMaxDateFieldNumber = 6;
  inline ::google::protobuf::uint64 maxdate() const;
  inline void set_maxdate(::google::protobuf::uint64 value);

  // optional uint32 sequenceNumber = 7;
  inline bool has_sequencenumber() const;
  inline void clear_sequencenumber();
  static const int kSequenceNumberFieldNumber = 7;
  inline ::google::protobuf::uint32 sequencenumber() const;
  inline void set_sequencenumber(::google::protobuf::uint32 value);

  // optional uint32 masterKeyId = 8;
  inline bool has_masterkeyid() const;
  inline void clear_masterkeyid();
  static const int kMasterKeyIdFieldNumber = 8;
  inline ::google::protobuf::uint32 masterkeyid() const;
  inline void set_masterkeyid(::google::protobuf::uint32 value);

  // optional uint64 expiryDate = 9;
  inline bool has_expirydate() const;
  inline void clear_expirydate();
  static const int kExpiryDateFieldNumber = 9;
  inline ::google::protobuf::uint64 expirydate() const;
  inline void set_expirydate(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SecretManagerSection.PersistToken)
 private:
  inline void set_has_version();
  inline void clear_has_version();
  inline void set_has_owner();
  inline void clear_has_owner();
  inline void set_has_renewer();
  inline void clear_has_renewer();
  inline void set_has_realuser();
  inline void clear_has_realuser();
  inline void set_has_issuedate();
  inline void clear_has_issuedate();
  inline void set_has_maxdate();
  inline void clear_has_maxdate();
  inline void set_has_sequencenumber();
  inline void clear_has_sequencenumber();
  inline void set_has_masterkeyid();
  inline void clear_has_masterkeyid();
  inline void set_has_expirydate();
  inline void clear_has_expirydate();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::std::string* owner_;
  ::std::string* renewer_;
  ::std::string* realuser_;
  ::google::protobuf::uint32 version_;
  ::google::protobuf::uint32 sequencenumber_;
  ::google::protobuf::uint64 issuedate_;
  ::google::protobuf::uint64 maxdate_;
  ::google::protobuf::uint64 expirydate_;
  ::google::protobuf::uint32 masterkeyid_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(9 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SecretManagerSection_PersistToken* default_instance_;
};
// -------------------------------------------------------------------

class SecretManagerSection : public ::google::protobuf::Message {
 public:
  SecretManagerSection();
  virtual ~SecretManagerSection();

  SecretManagerSection(const SecretManagerSection& from);

  inline SecretManagerSection& operator=(const SecretManagerSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SecretManagerSection& default_instance();

  void Swap(SecretManagerSection* other);

  // implements Message ----------------------------------------------

  SecretManagerSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SecretManagerSection& from);
  void MergeFrom(const SecretManagerSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef SecretManagerSection_DelegationKey DelegationKey;
  typedef SecretManagerSection_PersistToken PersistToken;

  // accessors -------------------------------------------------------

  // optional uint32 currentId = 1;
  inline bool has_currentid() const;
  inline void clear_currentid();
  static const int kCurrentIdFieldNumber = 1;
  inline ::google::protobuf::uint32 currentid() const;
  inline void set_currentid(::google::protobuf::uint32 value);

  // optional uint32 tokenSequenceNumber = 2;
  inline bool has_tokensequencenumber() const;
  inline void clear_tokensequencenumber();
  static const int kTokenSequenceNumberFieldNumber = 2;
  inline ::google::protobuf::uint32 tokensequencenumber() const;
  inline void set_tokensequencenumber(::google::protobuf::uint32 value);

  // optional uint32 numKeys = 3;
  inline bool has_numkeys() const;
  inline void clear_numkeys();
  static const int kNumKeysFieldNumber = 3;
  inline ::google::protobuf::uint32 numkeys() const;
  inline void set_numkeys(::google::protobuf::uint32 value);

  // optional uint32 numTokens = 4;
  inline bool has_numtokens() const;
  inline void clear_numtokens();
  static const int kNumTokensFieldNumber = 4;
  inline ::google::protobuf::uint32 numtokens() const;
  inline void set_numtokens(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.SecretManagerSection)
 private:
  inline void set_has_currentid();
  inline void clear_has_currentid();
  inline void set_has_tokensequencenumber();
  inline void clear_has_tokensequencenumber();
  inline void set_has_numkeys();
  inline void clear_has_numkeys();
  inline void set_has_numtokens();
  inline void clear_has_numtokens();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 currentid_;
  ::google::protobuf::uint32 tokensequencenumber_;
  ::google::protobuf::uint32 numkeys_;
  ::google::protobuf::uint32 numtokens_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(4 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static SecretManagerSection* default_instance_;
};
// -------------------------------------------------------------------

class CacheManagerSection : public ::google::protobuf::Message {
 public:
  CacheManagerSection();
  virtual ~CacheManagerSection();

  CacheManagerSection(const CacheManagerSection& from);

  inline CacheManagerSection& operator=(const CacheManagerSection& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CacheManagerSection& default_instance();

  void Swap(CacheManagerSection* other);

  // implements Message ----------------------------------------------

  CacheManagerSection* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CacheManagerSection& from);
  void MergeFrom(const CacheManagerSection& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint64 nextDirectiveId = 1;
  inline bool has_nextdirectiveid() const;
  inline void clear_nextdirectiveid();
  static const int kNextDirectiveIdFieldNumber = 1;
  inline ::google::protobuf::uint64 nextdirectiveid() const;
  inline void set_nextdirectiveid(::google::protobuf::uint64 value);

  // required uint32 numPools = 2;
  inline bool has_numpools() const;
  inline void clear_numpools();
  static const int kNumPoolsFieldNumber = 2;
  inline ::google::protobuf::uint32 numpools() const;
  inline void set_numpools(::google::protobuf::uint32 value);

  // required uint32 numDirectives = 3;
  inline bool has_numdirectives() const;
  inline void clear_numdirectives();
  static const int kNumDirectivesFieldNumber = 3;
  inline ::google::protobuf::uint32 numdirectives() const;
  inline void set_numdirectives(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:hadoop.hdfs.fsimage.CacheManagerSection)
 private:
  inline void set_has_nextdirectiveid();
  inline void clear_has_nextdirectiveid();
  inline void set_has_numpools();
  inline void clear_has_numpools();
  inline void set_has_numdirectives();
  inline void clear_has_numdirectives();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint64 nextdirectiveid_;
  ::google::protobuf::uint32 numpools_;
  ::google::protobuf::uint32 numdirectives_;

  mutable int _cached_size_;
  ::google::protobuf::uint32 _has_bits_[(3 + 31) / 32];

  friend void  protobuf_AddDesc_fsimage_2eproto();
  friend void protobuf_AssignDesc_fsimage_2eproto();
  friend void protobuf_ShutdownFile_fsimage_2eproto();

  void InitAsDefaultInstance();
  static CacheManagerSection* default_instance_;
};
// ===================================================================


// ===================================================================

// FileSummary_Section

// optional string name = 1;
inline bool FileSummary_Section::has_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FileSummary_Section::set_has_name() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FileSummary_Section::clear_has_name() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FileSummary_Section::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& FileSummary_Section::name() const {
  return *name_;
}
inline void FileSummary_Section::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void FileSummary_Section::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void FileSummary_Section::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FileSummary_Section::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* FileSummary_Section::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FileSummary_Section::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint64 length = 2;
inline bool FileSummary_Section::has_length() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FileSummary_Section::set_has_length() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FileSummary_Section::clear_has_length() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FileSummary_Section::clear_length() {
  length_ = GOOGLE_ULONGLONG(0);
  clear_has_length();
}
inline ::google::protobuf::uint64 FileSummary_Section::length() const {
  return length_;
}
inline void FileSummary_Section::set_length(::google::protobuf::uint64 value) {
  set_has_length();
  length_ = value;
}

// optional uint64 offset = 3;
inline bool FileSummary_Section::has_offset() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FileSummary_Section::set_has_offset() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FileSummary_Section::clear_has_offset() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FileSummary_Section::clear_offset() {
  offset_ = GOOGLE_ULONGLONG(0);
  clear_has_offset();
}
inline ::google::protobuf::uint64 FileSummary_Section::offset() const {
  return offset_;
}
inline void FileSummary_Section::set_offset(::google::protobuf::uint64 value) {
  set_has_offset();
  offset_ = value;
}

// -------------------------------------------------------------------

// FileSummary

// required uint32 ondiskVersion = 1;
inline bool FileSummary::has_ondiskversion() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FileSummary::set_has_ondiskversion() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FileSummary::clear_has_ondiskversion() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FileSummary::clear_ondiskversion() {
  ondiskversion_ = 0u;
  clear_has_ondiskversion();
}
inline ::google::protobuf::uint32 FileSummary::ondiskversion() const {
  return ondiskversion_;
}
inline void FileSummary::set_ondiskversion(::google::protobuf::uint32 value) {
  set_has_ondiskversion();
  ondiskversion_ = value;
}

// required uint32 layoutVersion = 2;
inline bool FileSummary::has_layoutversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FileSummary::set_has_layoutversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FileSummary::clear_has_layoutversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FileSummary::clear_layoutversion() {
  layoutversion_ = 0u;
  clear_has_layoutversion();
}
inline ::google::protobuf::uint32 FileSummary::layoutversion() const {
  return layoutversion_;
}
inline void FileSummary::set_layoutversion(::google::protobuf::uint32 value) {
  set_has_layoutversion();
  layoutversion_ = value;
}

// optional string codec = 3;
inline bool FileSummary::has_codec() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FileSummary::set_has_codec() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FileSummary::clear_has_codec() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FileSummary::clear_codec() {
  if (codec_ != &::google::protobuf::internal::kEmptyString) {
    codec_->clear();
  }
  clear_has_codec();
}
inline const ::std::string& FileSummary::codec() const {
  return *codec_;
}
inline void FileSummary::set_codec(const ::std::string& value) {
  set_has_codec();
  if (codec_ == &::google::protobuf::internal::kEmptyString) {
    codec_ = new ::std::string;
  }
  codec_->assign(value);
}
inline void FileSummary::set_codec(const char* value) {
  set_has_codec();
  if (codec_ == &::google::protobuf::internal::kEmptyString) {
    codec_ = new ::std::string;
  }
  codec_->assign(value);
}
inline void FileSummary::set_codec(const char* value, size_t size) {
  set_has_codec();
  if (codec_ == &::google::protobuf::internal::kEmptyString) {
    codec_ = new ::std::string;
  }
  codec_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FileSummary::mutable_codec() {
  set_has_codec();
  if (codec_ == &::google::protobuf::internal::kEmptyString) {
    codec_ = new ::std::string;
  }
  return codec_;
}
inline ::std::string* FileSummary::release_codec() {
  clear_has_codec();
  if (codec_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = codec_;
    codec_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FileSummary::set_allocated_codec(::std::string* codec) {
  if (codec_ != &::google::protobuf::internal::kEmptyString) {
    delete codec_;
  }
  if (codec) {
    set_has_codec();
    codec_ = codec;
  } else {
    clear_has_codec();
    codec_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// repeated .hadoop.hdfs.fsimage.FileSummary.Section sections = 4;
inline int FileSummary::sections_size() const {
  return sections_.size();
}
inline void FileSummary::clear_sections() {
  sections_.Clear();
}
inline const ::hadoop::hdfs::fsimage::FileSummary_Section& FileSummary::sections(int index) const {
  return sections_.Get(index);
}
inline ::hadoop::hdfs::fsimage::FileSummary_Section* FileSummary::mutable_sections(int index) {
  return sections_.Mutable(index);
}
inline ::hadoop::hdfs::fsimage::FileSummary_Section* FileSummary::add_sections() {
  return sections_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::FileSummary_Section >&
FileSummary::sections() const {
  return sections_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::FileSummary_Section >*
FileSummary::mutable_sections() {
  return &sections_;
}

// -------------------------------------------------------------------

// NameSystemSection

// optional uint32 namespaceId = 1;
inline bool NameSystemSection::has_namespaceid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NameSystemSection::set_has_namespaceid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NameSystemSection::clear_has_namespaceid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NameSystemSection::clear_namespaceid() {
  namespaceid_ = 0u;
  clear_has_namespaceid();
}
inline ::google::protobuf::uint32 NameSystemSection::namespaceid() const {
  return namespaceid_;
}
inline void NameSystemSection::set_namespaceid(::google::protobuf::uint32 value) {
  set_has_namespaceid();
  namespaceid_ = value;
}

// optional uint64 genstampV1 = 2;
inline bool NameSystemSection::has_genstampv1() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NameSystemSection::set_has_genstampv1() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NameSystemSection::clear_has_genstampv1() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NameSystemSection::clear_genstampv1() {
  genstampv1_ = GOOGLE_ULONGLONG(0);
  clear_has_genstampv1();
}
inline ::google::protobuf::uint64 NameSystemSection::genstampv1() const {
  return genstampv1_;
}
inline void NameSystemSection::set_genstampv1(::google::protobuf::uint64 value) {
  set_has_genstampv1();
  genstampv1_ = value;
}

// optional uint64 genstampV2 = 3;
inline bool NameSystemSection::has_genstampv2() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NameSystemSection::set_has_genstampv2() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NameSystemSection::clear_has_genstampv2() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NameSystemSection::clear_genstampv2() {
  genstampv2_ = GOOGLE_ULONGLONG(0);
  clear_has_genstampv2();
}
inline ::google::protobuf::uint64 NameSystemSection::genstampv2() const {
  return genstampv2_;
}
inline void NameSystemSection::set_genstampv2(::google::protobuf::uint64 value) {
  set_has_genstampv2();
  genstampv2_ = value;
}

// optional uint64 genstampV1Limit = 4;
inline bool NameSystemSection::has_genstampv1limit() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void NameSystemSection::set_has_genstampv1limit() {
  _has_bits_[0] |= 0x00000008u;
}
inline void NameSystemSection::clear_has_genstampv1limit() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void NameSystemSection::clear_genstampv1limit() {
  genstampv1limit_ = GOOGLE_ULONGLONG(0);
  clear_has_genstampv1limit();
}
inline ::google::protobuf::uint64 NameSystemSection::genstampv1limit() const {
  return genstampv1limit_;
}
inline void NameSystemSection::set_genstampv1limit(::google::protobuf::uint64 value) {
  set_has_genstampv1limit();
  genstampv1limit_ = value;
}

// optional uint64 lastAllocatedBlockId = 5;
inline bool NameSystemSection::has_lastallocatedblockid() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void NameSystemSection::set_has_lastallocatedblockid() {
  _has_bits_[0] |= 0x00000010u;
}
inline void NameSystemSection::clear_has_lastallocatedblockid() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void NameSystemSection::clear_lastallocatedblockid() {
  lastallocatedblockid_ = GOOGLE_ULONGLONG(0);
  clear_has_lastallocatedblockid();
}
inline ::google::protobuf::uint64 NameSystemSection::lastallocatedblockid() const {
  return lastallocatedblockid_;
}
inline void NameSystemSection::set_lastallocatedblockid(::google::protobuf::uint64 value) {
  set_has_lastallocatedblockid();
  lastallocatedblockid_ = value;
}

// optional uint64 transactionId = 6;
inline bool NameSystemSection::has_transactionid() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void NameSystemSection::set_has_transactionid() {
  _has_bits_[0] |= 0x00000020u;
}
inline void NameSystemSection::clear_has_transactionid() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void NameSystemSection::clear_transactionid() {
  transactionid_ = GOOGLE_ULONGLONG(0);
  clear_has_transactionid();
}
inline ::google::protobuf::uint64 NameSystemSection::transactionid() const {
  return transactionid_;
}
inline void NameSystemSection::set_transactionid(::google::protobuf::uint64 value) {
  set_has_transactionid();
  transactionid_ = value;
}

// optional uint64 rollingUpgradeStartTime = 7;
inline bool NameSystemSection::has_rollingupgradestarttime() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void NameSystemSection::set_has_rollingupgradestarttime() {
  _has_bits_[0] |= 0x00000040u;
}
inline void NameSystemSection::clear_has_rollingupgradestarttime() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void NameSystemSection::clear_rollingupgradestarttime() {
  rollingupgradestarttime_ = GOOGLE_ULONGLONG(0);
  clear_has_rollingupgradestarttime();
}
inline ::google::protobuf::uint64 NameSystemSection::rollingupgradestarttime() const {
  return rollingupgradestarttime_;
}
inline void NameSystemSection::set_rollingupgradestarttime(::google::protobuf::uint64 value) {
  set_has_rollingupgradestarttime();
  rollingupgradestarttime_ = value;
}

// -------------------------------------------------------------------

// INodeSection_FileUnderConstructionFeature

// optional string clientName = 1;
inline bool INodeSection_FileUnderConstructionFeature::has_clientname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection_FileUnderConstructionFeature::set_has_clientname() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection_FileUnderConstructionFeature::clear_has_clientname() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection_FileUnderConstructionFeature::clear_clientname() {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    clientname_->clear();
  }
  clear_has_clientname();
}
inline const ::std::string& INodeSection_FileUnderConstructionFeature::clientname() const {
  return *clientname_;
}
inline void INodeSection_FileUnderConstructionFeature::set_clientname(const ::std::string& value) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(value);
}
inline void INodeSection_FileUnderConstructionFeature::set_clientname(const char* value) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(value);
}
inline void INodeSection_FileUnderConstructionFeature::set_clientname(const char* value, size_t size) {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  clientname_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* INodeSection_FileUnderConstructionFeature::mutable_clientname() {
  set_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    clientname_ = new ::std::string;
  }
  return clientname_;
}
inline ::std::string* INodeSection_FileUnderConstructionFeature::release_clientname() {
  clear_has_clientname();
  if (clientname_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clientname_;
    clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void INodeSection_FileUnderConstructionFeature::set_allocated_clientname(::std::string* clientname) {
  if (clientname_ != &::google::protobuf::internal::kEmptyString) {
    delete clientname_;
  }
  if (clientname) {
    set_has_clientname();
    clientname_ = clientname;
  } else {
    clear_has_clientname();
    clientname_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string clientMachine = 2;
inline bool INodeSection_FileUnderConstructionFeature::has_clientmachine() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection_FileUnderConstructionFeature::set_has_clientmachine() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection_FileUnderConstructionFeature::clear_has_clientmachine() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection_FileUnderConstructionFeature::clear_clientmachine() {
  if (clientmachine_ != &::google::protobuf::internal::kEmptyString) {
    clientmachine_->clear();
  }
  clear_has_clientmachine();
}
inline const ::std::string& INodeSection_FileUnderConstructionFeature::clientmachine() const {
  return *clientmachine_;
}
inline void INodeSection_FileUnderConstructionFeature::set_clientmachine(const ::std::string& value) {
  set_has_clientmachine();
  if (clientmachine_ == &::google::protobuf::internal::kEmptyString) {
    clientmachine_ = new ::std::string;
  }
  clientmachine_->assign(value);
}
inline void INodeSection_FileUnderConstructionFeature::set_clientmachine(const char* value) {
  set_has_clientmachine();
  if (clientmachine_ == &::google::protobuf::internal::kEmptyString) {
    clientmachine_ = new ::std::string;
  }
  clientmachine_->assign(value);
}
inline void INodeSection_FileUnderConstructionFeature::set_clientmachine(const char* value, size_t size) {
  set_has_clientmachine();
  if (clientmachine_ == &::google::protobuf::internal::kEmptyString) {
    clientmachine_ = new ::std::string;
  }
  clientmachine_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* INodeSection_FileUnderConstructionFeature::mutable_clientmachine() {
  set_has_clientmachine();
  if (clientmachine_ == &::google::protobuf::internal::kEmptyString) {
    clientmachine_ = new ::std::string;
  }
  return clientmachine_;
}
inline ::std::string* INodeSection_FileUnderConstructionFeature::release_clientmachine() {
  clear_has_clientmachine();
  if (clientmachine_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = clientmachine_;
    clientmachine_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void INodeSection_FileUnderConstructionFeature::set_allocated_clientmachine(::std::string* clientmachine) {
  if (clientmachine_ != &::google::protobuf::internal::kEmptyString) {
    delete clientmachine_;
  }
  if (clientmachine) {
    set_has_clientmachine();
    clientmachine_ = clientmachine;
  } else {
    clear_has_clientmachine();
    clientmachine_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// INodeSection_AclFeatureProto

// repeated fixed32 entries = 2 [packed = true];
inline int INodeSection_AclFeatureProto::entries_size() const {
  return entries_.size();
}
inline void INodeSection_AclFeatureProto::clear_entries() {
  entries_.Clear();
}
inline ::google::protobuf::uint32 INodeSection_AclFeatureProto::entries(int index) const {
  return entries_.Get(index);
}
inline void INodeSection_AclFeatureProto::set_entries(int index, ::google::protobuf::uint32 value) {
  entries_.Set(index, value);
}
inline void INodeSection_AclFeatureProto::add_entries(::google::protobuf::uint32 value) {
  entries_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
INodeSection_AclFeatureProto::entries() const {
  return entries_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
INodeSection_AclFeatureProto::mutable_entries() {
  return &entries_;
}

// -------------------------------------------------------------------

// INodeSection_XAttrCompactProto

// required fixed32 name = 1;
inline bool INodeSection_XAttrCompactProto::has_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection_XAttrCompactProto::set_has_name() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection_XAttrCompactProto::clear_has_name() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection_XAttrCompactProto::clear_name() {
  name_ = 0u;
  clear_has_name();
}
inline ::google::protobuf::uint32 INodeSection_XAttrCompactProto::name() const {
  return name_;
}
inline void INodeSection_XAttrCompactProto::set_name(::google::protobuf::uint32 value) {
  set_has_name();
  name_ = value;
}

// optional bytes value = 2;
inline bool INodeSection_XAttrCompactProto::has_value() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection_XAttrCompactProto::set_has_value() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection_XAttrCompactProto::clear_has_value() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection_XAttrCompactProto::clear_value() {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    value_->clear();
  }
  clear_has_value();
}
inline const ::std::string& INodeSection_XAttrCompactProto::value() const {
  return *value_;
}
inline void INodeSection_XAttrCompactProto::set_value(const ::std::string& value) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(value);
}
inline void INodeSection_XAttrCompactProto::set_value(const char* value) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(value);
}
inline void INodeSection_XAttrCompactProto::set_value(const void* value, size_t size) {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  value_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* INodeSection_XAttrCompactProto::mutable_value() {
  set_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    value_ = new ::std::string;
  }
  return value_;
}
inline ::std::string* INodeSection_XAttrCompactProto::release_value() {
  clear_has_value();
  if (value_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = value_;
    value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void INodeSection_XAttrCompactProto::set_allocated_value(::std::string* value) {
  if (value_ != &::google::protobuf::internal::kEmptyString) {
    delete value_;
  }
  if (value) {
    set_has_value();
    value_ = value;
  } else {
    clear_has_value();
    value_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// INodeSection_XAttrFeatureProto

// repeated .hadoop.hdfs.fsimage.INodeSection.XAttrCompactProto xAttrs = 1;
inline int INodeSection_XAttrFeatureProto::xattrs_size() const {
  return xattrs_.size();
}
inline void INodeSection_XAttrFeatureProto::clear_xattrs() {
  xattrs_.Clear();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto& INodeSection_XAttrFeatureProto::xattrs(int index) const {
  return xattrs_.Get(index);
}
inline ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto* INodeSection_XAttrFeatureProto::mutable_xattrs(int index) {
  return xattrs_.Mutable(index);
}
inline ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto* INodeSection_XAttrFeatureProto::add_xattrs() {
  return xattrs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto >&
INodeSection_XAttrFeatureProto::xattrs() const {
  return xattrs_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::fsimage::INodeSection_XAttrCompactProto >*
INodeSection_XAttrFeatureProto::mutable_xattrs() {
  return &xattrs_;
}

// -------------------------------------------------------------------

// INodeSection_INodeFile

// optional uint32 replication = 1;
inline bool INodeSection_INodeFile::has_replication() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection_INodeFile::set_has_replication() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection_INodeFile::clear_has_replication() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection_INodeFile::clear_replication() {
  replication_ = 0u;
  clear_has_replication();
}
inline ::google::protobuf::uint32 INodeSection_INodeFile::replication() const {
  return replication_;
}
inline void INodeSection_INodeFile::set_replication(::google::protobuf::uint32 value) {
  set_has_replication();
  replication_ = value;
}

// optional uint64 modificationTime = 2;
inline bool INodeSection_INodeFile::has_modificationtime() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection_INodeFile::set_has_modificationtime() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection_INodeFile::clear_has_modificationtime() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection_INodeFile::clear_modificationtime() {
  modificationtime_ = GOOGLE_ULONGLONG(0);
  clear_has_modificationtime();
}
inline ::google::protobuf::uint64 INodeSection_INodeFile::modificationtime() const {
  return modificationtime_;
}
inline void INodeSection_INodeFile::set_modificationtime(::google::protobuf::uint64 value) {
  set_has_modificationtime();
  modificationtime_ = value;
}

// optional uint64 accessTime = 3;
inline bool INodeSection_INodeFile::has_accesstime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void INodeSection_INodeFile::set_has_accesstime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void INodeSection_INodeFile::clear_has_accesstime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void INodeSection_INodeFile::clear_accesstime() {
  accesstime_ = GOOGLE_ULONGLONG(0);
  clear_has_accesstime();
}
inline ::google::protobuf::uint64 INodeSection_INodeFile::accesstime() const {
  return accesstime_;
}
inline void INodeSection_INodeFile::set_accesstime(::google::protobuf::uint64 value) {
  set_has_accesstime();
  accesstime_ = value;
}

// optional uint64 preferredBlockSize = 4;
inline bool INodeSection_INodeFile::has_preferredblocksize() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void INodeSection_INodeFile::set_has_preferredblocksize() {
  _has_bits_[0] |= 0x00000008u;
}
inline void INodeSection_INodeFile::clear_has_preferredblocksize() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void INodeSection_INodeFile::clear_preferredblocksize() {
  preferredblocksize_ = GOOGLE_ULONGLONG(0);
  clear_has_preferredblocksize();
}
inline ::google::protobuf::uint64 INodeSection_INodeFile::preferredblocksize() const {
  return preferredblocksize_;
}
inline void INodeSection_INodeFile::set_preferredblocksize(::google::protobuf::uint64 value) {
  set_has_preferredblocksize();
  preferredblocksize_ = value;
}

// optional fixed64 permission = 5;
inline bool INodeSection_INodeFile::has_permission() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void INodeSection_INodeFile::set_has_permission() {
  _has_bits_[0] |= 0x00000010u;
}
inline void INodeSection_INodeFile::clear_has_permission() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void INodeSection_INodeFile::clear_permission() {
  permission_ = GOOGLE_ULONGLONG(0);
  clear_has_permission();
}
inline ::google::protobuf::uint64 INodeSection_INodeFile::permission() const {
  return permission_;
}
inline void INodeSection_INodeFile::set_permission(::google::protobuf::uint64 value) {
  set_has_permission();
  permission_ = value;
}

// repeated .hadoop.hdfs.BlockProto blocks = 6;
inline int INodeSection_INodeFile::blocks_size() const {
  return blocks_.size();
}
inline void INodeSection_INodeFile::clear_blocks() {
  blocks_.Clear();
}
inline const ::hadoop::hdfs::BlockProto& INodeSection_INodeFile::blocks(int index) const {
  return blocks_.Get(index);
}
inline ::hadoop::hdfs::BlockProto* INodeSection_INodeFile::mutable_blocks(int index) {
  return blocks_.Mutable(index);
}
inline ::hadoop::hdfs::BlockProto* INodeSection_INodeFile::add_blocks() {
  return blocks_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockProto >&
INodeSection_INodeFile::blocks() const {
  return blocks_;
}
inline ::google::protobuf::RepeatedPtrField< ::hadoop::hdfs::BlockProto >*
INodeSection_INodeFile::mutable_blocks() {
  return &blocks_;
}

// optional .hadoop.hdfs.fsimage.INodeSection.FileUnderConstructionFeature fileUC = 7;
inline bool INodeSection_INodeFile::has_fileuc() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void INodeSection_INodeFile::set_has_fileuc() {
  _has_bits_[0] |= 0x00000040u;
}
inline void INodeSection_INodeFile::clear_has_fileuc() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void INodeSection_INodeFile::clear_fileuc() {
  if (fileuc_ != NULL) fileuc_->::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature::Clear();
  clear_has_fileuc();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature& INodeSection_INodeFile::fileuc() const {
  return fileuc_ != NULL ? *fileuc_ : *default_instance_->fileuc_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* INodeSection_INodeFile::mutable_fileuc() {
  set_has_fileuc();
  if (fileuc_ == NULL) fileuc_ = new ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature;
  return fileuc_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* INodeSection_INodeFile::release_fileuc() {
  clear_has_fileuc();
  ::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* temp = fileuc_;
  fileuc_ = NULL;
  return temp;
}
inline void INodeSection_INodeFile::set_allocated_fileuc(::hadoop::hdfs::fsimage::INodeSection_FileUnderConstructionFeature* fileuc) {
  delete fileuc_;
  fileuc_ = fileuc;
  if (fileuc) {
    set_has_fileuc();
  } else {
    clear_has_fileuc();
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 8;
inline bool INodeSection_INodeFile::has_acl() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void INodeSection_INodeFile::set_has_acl() {
  _has_bits_[0] |= 0x00000080u;
}
inline void INodeSection_INodeFile::clear_has_acl() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void INodeSection_INodeFile::clear_acl() {
  if (acl_ != NULL) acl_->::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::Clear();
  clear_has_acl();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto& INodeSection_INodeFile::acl() const {
  return acl_ != NULL ? *acl_ : *default_instance_->acl_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* INodeSection_INodeFile::mutable_acl() {
  set_has_acl();
  if (acl_ == NULL) acl_ = new ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto;
  return acl_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* INodeSection_INodeFile::release_acl() {
  clear_has_acl();
  ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* temp = acl_;
  acl_ = NULL;
  return temp;
}
inline void INodeSection_INodeFile::set_allocated_acl(::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* acl) {
  delete acl_;
  acl_ = acl;
  if (acl) {
    set_has_acl();
  } else {
    clear_has_acl();
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 9;
inline bool INodeSection_INodeFile::has_xattrs() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void INodeSection_INodeFile::set_has_xattrs() {
  _has_bits_[0] |= 0x00000100u;
}
inline void INodeSection_INodeFile::clear_has_xattrs() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void INodeSection_INodeFile::clear_xattrs() {
  if (xattrs_ != NULL) xattrs_->::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::Clear();
  clear_has_xattrs();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto& INodeSection_INodeFile::xattrs() const {
  return xattrs_ != NULL ? *xattrs_ : *default_instance_->xattrs_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* INodeSection_INodeFile::mutable_xattrs() {
  set_has_xattrs();
  if (xattrs_ == NULL) xattrs_ = new ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto;
  return xattrs_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* INodeSection_INodeFile::release_xattrs() {
  clear_has_xattrs();
  ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* temp = xattrs_;
  xattrs_ = NULL;
  return temp;
}
inline void INodeSection_INodeFile::set_allocated_xattrs(::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* xattrs) {
  delete xattrs_;
  xattrs_ = xattrs;
  if (xattrs) {
    set_has_xattrs();
  } else {
    clear_has_xattrs();
  }
}

// optional uint32 storagePolicyID = 10;
inline bool INodeSection_INodeFile::has_storagepolicyid() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void INodeSection_INodeFile::set_has_storagepolicyid() {
  _has_bits_[0] |= 0x00000200u;
}
inline void INodeSection_INodeFile::clear_has_storagepolicyid() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void INodeSection_INodeFile::clear_storagepolicyid() {
  storagepolicyid_ = 0u;
  clear_has_storagepolicyid();
}
inline ::google::protobuf::uint32 INodeSection_INodeFile::storagepolicyid() const {
  return storagepolicyid_;
}
inline void INodeSection_INodeFile::set_storagepolicyid(::google::protobuf::uint32 value) {
  set_has_storagepolicyid();
  storagepolicyid_ = value;
}

// -------------------------------------------------------------------

// INodeSection_INodeDirectory

// optional uint64 modificationTime = 1;
inline bool INodeSection_INodeDirectory::has_modificationtime() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection_INodeDirectory::set_has_modificationtime() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection_INodeDirectory::clear_has_modificationtime() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection_INodeDirectory::clear_modificationtime() {
  modificationtime_ = GOOGLE_ULONGLONG(0);
  clear_has_modificationtime();
}
inline ::google::protobuf::uint64 INodeSection_INodeDirectory::modificationtime() const {
  return modificationtime_;
}
inline void INodeSection_INodeDirectory::set_modificationtime(::google::protobuf::uint64 value) {
  set_has_modificationtime();
  modificationtime_ = value;
}

// optional uint64 nsQuota = 2;
inline bool INodeSection_INodeDirectory::has_nsquota() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection_INodeDirectory::set_has_nsquota() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection_INodeDirectory::clear_has_nsquota() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection_INodeDirectory::clear_nsquota() {
  nsquota_ = GOOGLE_ULONGLONG(0);
  clear_has_nsquota();
}
inline ::google::protobuf::uint64 INodeSection_INodeDirectory::nsquota() const {
  return nsquota_;
}
inline void INodeSection_INodeDirectory::set_nsquota(::google::protobuf::uint64 value) {
  set_has_nsquota();
  nsquota_ = value;
}

// optional uint64 dsQuota = 3;
inline bool INodeSection_INodeDirectory::has_dsquota() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void INodeSection_INodeDirectory::set_has_dsquota() {
  _has_bits_[0] |= 0x00000004u;
}
inline void INodeSection_INodeDirectory::clear_has_dsquota() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void INodeSection_INodeDirectory::clear_dsquota() {
  dsquota_ = GOOGLE_ULONGLONG(0);
  clear_has_dsquota();
}
inline ::google::protobuf::uint64 INodeSection_INodeDirectory::dsquota() const {
  return dsquota_;
}
inline void INodeSection_INodeDirectory::set_dsquota(::google::protobuf::uint64 value) {
  set_has_dsquota();
  dsquota_ = value;
}

// optional fixed64 permission = 4;
inline bool INodeSection_INodeDirectory::has_permission() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void INodeSection_INodeDirectory::set_has_permission() {
  _has_bits_[0] |= 0x00000008u;
}
inline void INodeSection_INodeDirectory::clear_has_permission() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void INodeSection_INodeDirectory::clear_permission() {
  permission_ = GOOGLE_ULONGLONG(0);
  clear_has_permission();
}
inline ::google::protobuf::uint64 INodeSection_INodeDirectory::permission() const {
  return permission_;
}
inline void INodeSection_INodeDirectory::set_permission(::google::protobuf::uint64 value) {
  set_has_permission();
  permission_ = value;
}

// optional .hadoop.hdfs.fsimage.INodeSection.AclFeatureProto acl = 5;
inline bool INodeSection_INodeDirectory::has_acl() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void INodeSection_INodeDirectory::set_has_acl() {
  _has_bits_[0] |= 0x00000010u;
}
inline void INodeSection_INodeDirectory::clear_has_acl() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void INodeSection_INodeDirectory::clear_acl() {
  if (acl_ != NULL) acl_->::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto::Clear();
  clear_has_acl();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto& INodeSection_INodeDirectory::acl() const {
  return acl_ != NULL ? *acl_ : *default_instance_->acl_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* INodeSection_INodeDirectory::mutable_acl() {
  set_has_acl();
  if (acl_ == NULL) acl_ = new ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto;
  return acl_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* INodeSection_INodeDirectory::release_acl() {
  clear_has_acl();
  ::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* temp = acl_;
  acl_ = NULL;
  return temp;
}
inline void INodeSection_INodeDirectory::set_allocated_acl(::hadoop::hdfs::fsimage::INodeSection_AclFeatureProto* acl) {
  delete acl_;
  acl_ = acl;
  if (acl) {
    set_has_acl();
  } else {
    clear_has_acl();
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.XAttrFeatureProto xAttrs = 6;
inline bool INodeSection_INodeDirectory::has_xattrs() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void INodeSection_INodeDirectory::set_has_xattrs() {
  _has_bits_[0] |= 0x00000020u;
}
inline void INodeSection_INodeDirectory::clear_has_xattrs() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void INodeSection_INodeDirectory::clear_xattrs() {
  if (xattrs_ != NULL) xattrs_->::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto::Clear();
  clear_has_xattrs();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto& INodeSection_INodeDirectory::xattrs() const {
  return xattrs_ != NULL ? *xattrs_ : *default_instance_->xattrs_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* INodeSection_INodeDirectory::mutable_xattrs() {
  set_has_xattrs();
  if (xattrs_ == NULL) xattrs_ = new ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto;
  return xattrs_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* INodeSection_INodeDirectory::release_xattrs() {
  clear_has_xattrs();
  ::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* temp = xattrs_;
  xattrs_ = NULL;
  return temp;
}
inline void INodeSection_INodeDirectory::set_allocated_xattrs(::hadoop::hdfs::fsimage::INodeSection_XAttrFeatureProto* xattrs) {
  delete xattrs_;
  xattrs_ = xattrs;
  if (xattrs) {
    set_has_xattrs();
  } else {
    clear_has_xattrs();
  }
}

// -------------------------------------------------------------------

// INodeSection_INodeSymlink

// optional fixed64 permission = 1;
inline bool INodeSection_INodeSymlink::has_permission() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection_INodeSymlink::set_has_permission() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection_INodeSymlink::clear_has_permission() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection_INodeSymlink::clear_permission() {
  permission_ = GOOGLE_ULONGLONG(0);
  clear_has_permission();
}
inline ::google::protobuf::uint64 INodeSection_INodeSymlink::permission() const {
  return permission_;
}
inline void INodeSection_INodeSymlink::set_permission(::google::protobuf::uint64 value) {
  set_has_permission();
  permission_ = value;
}

// optional bytes target = 2;
inline bool INodeSection_INodeSymlink::has_target() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection_INodeSymlink::set_has_target() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection_INodeSymlink::clear_has_target() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection_INodeSymlink::clear_target() {
  if (target_ != &::google::protobuf::internal::kEmptyString) {
    target_->clear();
  }
  clear_has_target();
}
inline const ::std::string& INodeSection_INodeSymlink::target() const {
  return *target_;
}
inline void INodeSection_INodeSymlink::set_target(const ::std::string& value) {
  set_has_target();
  if (target_ == &::google::protobuf::internal::kEmptyString) {
    target_ = new ::std::string;
  }
  target_->assign(value);
}
inline void INodeSection_INodeSymlink::set_target(const char* value) {
  set_has_target();
  if (target_ == &::google::protobuf::internal::kEmptyString) {
    target_ = new ::std::string;
  }
  target_->assign(value);
}
inline void INodeSection_INodeSymlink::set_target(const void* value, size_t size) {
  set_has_target();
  if (target_ == &::google::protobuf::internal::kEmptyString) {
    target_ = new ::std::string;
  }
  target_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* INodeSection_INodeSymlink::mutable_target() {
  set_has_target();
  if (target_ == &::google::protobuf::internal::kEmptyString) {
    target_ = new ::std::string;
  }
  return target_;
}
inline ::std::string* INodeSection_INodeSymlink::release_target() {
  clear_has_target();
  if (target_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = target_;
    target_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void INodeSection_INodeSymlink::set_allocated_target(::std::string* target) {
  if (target_ != &::google::protobuf::internal::kEmptyString) {
    delete target_;
  }
  if (target) {
    set_has_target();
    target_ = target;
  } else {
    clear_has_target();
    target_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint64 modificationTime = 3;
inline bool INodeSection_INodeSymlink::has_modificationtime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void INodeSection_INodeSymlink::set_has_modificationtime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void INodeSection_INodeSymlink::clear_has_modificationtime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void INodeSection_INodeSymlink::clear_modificationtime() {
  modificationtime_ = GOOGLE_ULONGLONG(0);
  clear_has_modificationtime();
}
inline ::google::protobuf::uint64 INodeSection_INodeSymlink::modificationtime() const {
  return modificationtime_;
}
inline void INodeSection_INodeSymlink::set_modificationtime(::google::protobuf::uint64 value) {
  set_has_modificationtime();
  modificationtime_ = value;
}

// optional uint64 accessTime = 4;
inline bool INodeSection_INodeSymlink::has_accesstime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void INodeSection_INodeSymlink::set_has_accesstime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void INodeSection_INodeSymlink::clear_has_accesstime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void INodeSection_INodeSymlink::clear_accesstime() {
  accesstime_ = GOOGLE_ULONGLONG(0);
  clear_has_accesstime();
}
inline ::google::protobuf::uint64 INodeSection_INodeSymlink::accesstime() const {
  return accesstime_;
}
inline void INodeSection_INodeSymlink::set_accesstime(::google::protobuf::uint64 value) {
  set_has_accesstime();
  accesstime_ = value;
}

// -------------------------------------------------------------------

// INodeSection_INode

// required .hadoop.hdfs.fsimage.INodeSection.INode.Type type = 1;
inline bool INodeSection_INode::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection_INode::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection_INode::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection_INode::clear_type() {
  type_ = 1;
  clear_has_type();
}
inline ::hadoop::hdfs::fsimage::INodeSection_INode_Type INodeSection_INode::type() const {
  return static_cast< ::hadoop::hdfs::fsimage::INodeSection_INode_Type >(type_);
}
inline void INodeSection_INode::set_type(::hadoop::hdfs::fsimage::INodeSection_INode_Type value) {
  assert(::hadoop::hdfs::fsimage::INodeSection_INode_Type_IsValid(value));
  set_has_type();
  type_ = value;
}

// required uint64 id = 2;
inline bool INodeSection_INode::has_id() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection_INode::set_has_id() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection_INode::clear_has_id() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection_INode::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 INodeSection_INode::id() const {
  return id_;
}
inline void INodeSection_INode::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
}

// optional bytes name = 3;
inline bool INodeSection_INode::has_name() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void INodeSection_INode::set_has_name() {
  _has_bits_[0] |= 0x00000004u;
}
inline void INodeSection_INode::clear_has_name() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void INodeSection_INode::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& INodeSection_INode::name() const {
  return *name_;
}
inline void INodeSection_INode::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void INodeSection_INode::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void INodeSection_INode::set_name(const void* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* INodeSection_INode::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* INodeSection_INode::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void INodeSection_INode::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.INodeFile file = 4;
inline bool INodeSection_INode::has_file() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void INodeSection_INode::set_has_file() {
  _has_bits_[0] |= 0x00000008u;
}
inline void INodeSection_INode::clear_has_file() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void INodeSection_INode::clear_file() {
  if (file_ != NULL) file_->::hadoop::hdfs::fsimage::INodeSection_INodeFile::Clear();
  clear_has_file();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_INodeFile& INodeSection_INode::file() const {
  return file_ != NULL ? *file_ : *default_instance_->file_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* INodeSection_INode::mutable_file() {
  set_has_file();
  if (file_ == NULL) file_ = new ::hadoop::hdfs::fsimage::INodeSection_INodeFile;
  return file_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* INodeSection_INode::release_file() {
  clear_has_file();
  ::hadoop::hdfs::fsimage::INodeSection_INodeFile* temp = file_;
  file_ = NULL;
  return temp;
}
inline void INodeSection_INode::set_allocated_file(::hadoop::hdfs::fsimage::INodeSection_INodeFile* file) {
  delete file_;
  file_ = file;
  if (file) {
    set_has_file();
  } else {
    clear_has_file();
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory directory = 5;
inline bool INodeSection_INode::has_directory() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void INodeSection_INode::set_has_directory() {
  _has_bits_[0] |= 0x00000010u;
}
inline void INodeSection_INode::clear_has_directory() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void INodeSection_INode::clear_directory() {
  if (directory_ != NULL) directory_->::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::Clear();
  clear_has_directory();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory& INodeSection_INode::directory() const {
  return directory_ != NULL ? *directory_ : *default_instance_->directory_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* INodeSection_INode::mutable_directory() {
  set_has_directory();
  if (directory_ == NULL) directory_ = new ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory;
  return directory_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* INodeSection_INode::release_directory() {
  clear_has_directory();
  ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* temp = directory_;
  directory_ = NULL;
  return temp;
}
inline void INodeSection_INode::set_allocated_directory(::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* directory) {
  delete directory_;
  directory_ = directory;
  if (directory) {
    set_has_directory();
  } else {
    clear_has_directory();
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.INodeSymlink symlink = 6;
inline bool INodeSection_INode::has_symlink() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void INodeSection_INode::set_has_symlink() {
  _has_bits_[0] |= 0x00000020u;
}
inline void INodeSection_INode::clear_has_symlink() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void INodeSection_INode::clear_symlink() {
  if (symlink_ != NULL) symlink_->::hadoop::hdfs::fsimage::INodeSection_INodeSymlink::Clear();
  clear_has_symlink();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink& INodeSection_INode::symlink() const {
  return symlink_ != NULL ? *symlink_ : *default_instance_->symlink_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* INodeSection_INode::mutable_symlink() {
  set_has_symlink();
  if (symlink_ == NULL) symlink_ = new ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink;
  return symlink_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* INodeSection_INode::release_symlink() {
  clear_has_symlink();
  ::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* temp = symlink_;
  symlink_ = NULL;
  return temp;
}
inline void INodeSection_INode::set_allocated_symlink(::hadoop::hdfs::fsimage::INodeSection_INodeSymlink* symlink) {
  delete symlink_;
  symlink_ = symlink;
  if (symlink) {
    set_has_symlink();
  } else {
    clear_has_symlink();
  }
}

// -------------------------------------------------------------------

// INodeSection

// optional uint64 lastInodeId = 1;
inline bool INodeSection::has_lastinodeid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeSection::set_has_lastinodeid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeSection::clear_has_lastinodeid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeSection::clear_lastinodeid() {
  lastinodeid_ = GOOGLE_ULONGLONG(0);
  clear_has_lastinodeid();
}
inline ::google::protobuf::uint64 INodeSection::lastinodeid() const {
  return lastinodeid_;
}
inline void INodeSection::set_lastinodeid(::google::protobuf::uint64 value) {
  set_has_lastinodeid();
  lastinodeid_ = value;
}

// optional uint64 numInodes = 2;
inline bool INodeSection::has_numinodes() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeSection::set_has_numinodes() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeSection::clear_has_numinodes() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeSection::clear_numinodes() {
  numinodes_ = GOOGLE_ULONGLONG(0);
  clear_has_numinodes();
}
inline ::google::protobuf::uint64 INodeSection::numinodes() const {
  return numinodes_;
}
inline void INodeSection::set_numinodes(::google::protobuf::uint64 value) {
  set_has_numinodes();
  numinodes_ = value;
}

// -------------------------------------------------------------------

// FilesUnderConstructionSection_FileUnderConstructionEntry

// optional uint64 inodeId = 1;
inline bool FilesUnderConstructionSection_FileUnderConstructionEntry::has_inodeid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_has_inodeid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::clear_has_inodeid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::clear_inodeid() {
  inodeid_ = GOOGLE_ULONGLONG(0);
  clear_has_inodeid();
}
inline ::google::protobuf::uint64 FilesUnderConstructionSection_FileUnderConstructionEntry::inodeid() const {
  return inodeid_;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_inodeid(::google::protobuf::uint64 value) {
  set_has_inodeid();
  inodeid_ = value;
}

// optional string fullPath = 2;
inline bool FilesUnderConstructionSection_FileUnderConstructionEntry::has_fullpath() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_has_fullpath() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::clear_has_fullpath() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::clear_fullpath() {
  if (fullpath_ != &::google::protobuf::internal::kEmptyString) {
    fullpath_->clear();
  }
  clear_has_fullpath();
}
inline const ::std::string& FilesUnderConstructionSection_FileUnderConstructionEntry::fullpath() const {
  return *fullpath_;
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_fullpath(const ::std::string& value) {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  fullpath_->assign(value);
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_fullpath(const char* value) {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  fullpath_->assign(value);
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_fullpath(const char* value, size_t size) {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  fullpath_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* FilesUnderConstructionSection_FileUnderConstructionEntry::mutable_fullpath() {
  set_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    fullpath_ = new ::std::string;
  }
  return fullpath_;
}
inline ::std::string* FilesUnderConstructionSection_FileUnderConstructionEntry::release_fullpath() {
  clear_has_fullpath();
  if (fullpath_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = fullpath_;
    fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void FilesUnderConstructionSection_FileUnderConstructionEntry::set_allocated_fullpath(::std::string* fullpath) {
  if (fullpath_ != &::google::protobuf::internal::kEmptyString) {
    delete fullpath_;
  }
  if (fullpath) {
    set_has_fullpath();
    fullpath_ = fullpath;
  } else {
    clear_has_fullpath();
    fullpath_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// FilesUnderConstructionSection

// -------------------------------------------------------------------

// INodeDirectorySection_DirEntry

// optional uint64 parent = 1;
inline bool INodeDirectorySection_DirEntry::has_parent() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeDirectorySection_DirEntry::set_has_parent() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeDirectorySection_DirEntry::clear_has_parent() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeDirectorySection_DirEntry::clear_parent() {
  parent_ = GOOGLE_ULONGLONG(0);
  clear_has_parent();
}
inline ::google::protobuf::uint64 INodeDirectorySection_DirEntry::parent() const {
  return parent_;
}
inline void INodeDirectorySection_DirEntry::set_parent(::google::protobuf::uint64 value) {
  set_has_parent();
  parent_ = value;
}

// repeated uint64 children = 2 [packed = true];
inline int INodeDirectorySection_DirEntry::children_size() const {
  return children_.size();
}
inline void INodeDirectorySection_DirEntry::clear_children() {
  children_.Clear();
}
inline ::google::protobuf::uint64 INodeDirectorySection_DirEntry::children(int index) const {
  return children_.Get(index);
}
inline void INodeDirectorySection_DirEntry::set_children(int index, ::google::protobuf::uint64 value) {
  children_.Set(index, value);
}
inline void INodeDirectorySection_DirEntry::add_children(::google::protobuf::uint64 value) {
  children_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
INodeDirectorySection_DirEntry::children() const {
  return children_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
INodeDirectorySection_DirEntry::mutable_children() {
  return &children_;
}

// repeated uint32 refChildren = 3 [packed = true];
inline int INodeDirectorySection_DirEntry::refchildren_size() const {
  return refchildren_.size();
}
inline void INodeDirectorySection_DirEntry::clear_refchildren() {
  refchildren_.Clear();
}
inline ::google::protobuf::uint32 INodeDirectorySection_DirEntry::refchildren(int index) const {
  return refchildren_.Get(index);
}
inline void INodeDirectorySection_DirEntry::set_refchildren(int index, ::google::protobuf::uint32 value) {
  refchildren_.Set(index, value);
}
inline void INodeDirectorySection_DirEntry::add_refchildren(::google::protobuf::uint32 value) {
  refchildren_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
INodeDirectorySection_DirEntry::refchildren() const {
  return refchildren_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
INodeDirectorySection_DirEntry::mutable_refchildren() {
  return &refchildren_;
}

// -------------------------------------------------------------------

// INodeDirectorySection

// -------------------------------------------------------------------

// INodeReferenceSection_INodeReference

// optional uint64 referredId = 1;
inline bool INodeReferenceSection_INodeReference::has_referredid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void INodeReferenceSection_INodeReference::set_has_referredid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void INodeReferenceSection_INodeReference::clear_has_referredid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void INodeReferenceSection_INodeReference::clear_referredid() {
  referredid_ = GOOGLE_ULONGLONG(0);
  clear_has_referredid();
}
inline ::google::protobuf::uint64 INodeReferenceSection_INodeReference::referredid() const {
  return referredid_;
}
inline void INodeReferenceSection_INodeReference::set_referredid(::google::protobuf::uint64 value) {
  set_has_referredid();
  referredid_ = value;
}

// optional bytes name = 2;
inline bool INodeReferenceSection_INodeReference::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void INodeReferenceSection_INodeReference::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void INodeReferenceSection_INodeReference::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void INodeReferenceSection_INodeReference::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& INodeReferenceSection_INodeReference::name() const {
  return *name_;
}
inline void INodeReferenceSection_INodeReference::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void INodeReferenceSection_INodeReference::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void INodeReferenceSection_INodeReference::set_name(const void* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* INodeReferenceSection_INodeReference::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* INodeReferenceSection_INodeReference::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void INodeReferenceSection_INodeReference::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint32 dstSnapshotId = 3;
inline bool INodeReferenceSection_INodeReference::has_dstsnapshotid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void INodeReferenceSection_INodeReference::set_has_dstsnapshotid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void INodeReferenceSection_INodeReference::clear_has_dstsnapshotid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void INodeReferenceSection_INodeReference::clear_dstsnapshotid() {
  dstsnapshotid_ = 0u;
  clear_has_dstsnapshotid();
}
inline ::google::protobuf::uint32 INodeReferenceSection_INodeReference::dstsnapshotid() const {
  return dstsnapshotid_;
}
inline void INodeReferenceSection_INodeReference::set_dstsnapshotid(::google::protobuf::uint32 value) {
  set_has_dstsnapshotid();
  dstsnapshotid_ = value;
}

// optional uint32 lastSnapshotId = 4;
inline bool INodeReferenceSection_INodeReference::has_lastsnapshotid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void INodeReferenceSection_INodeReference::set_has_lastsnapshotid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void INodeReferenceSection_INodeReference::clear_has_lastsnapshotid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void INodeReferenceSection_INodeReference::clear_lastsnapshotid() {
  lastsnapshotid_ = 0u;
  clear_has_lastsnapshotid();
}
inline ::google::protobuf::uint32 INodeReferenceSection_INodeReference::lastsnapshotid() const {
  return lastsnapshotid_;
}
inline void INodeReferenceSection_INodeReference::set_lastsnapshotid(::google::protobuf::uint32 value) {
  set_has_lastsnapshotid();
  lastsnapshotid_ = value;
}

// -------------------------------------------------------------------

// INodeReferenceSection

// -------------------------------------------------------------------

// SnapshotSection_Snapshot

// optional uint32 snapshotId = 1;
inline bool SnapshotSection_Snapshot::has_snapshotid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotSection_Snapshot::set_has_snapshotid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotSection_Snapshot::clear_has_snapshotid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotSection_Snapshot::clear_snapshotid() {
  snapshotid_ = 0u;
  clear_has_snapshotid();
}
inline ::google::protobuf::uint32 SnapshotSection_Snapshot::snapshotid() const {
  return snapshotid_;
}
inline void SnapshotSection_Snapshot::set_snapshotid(::google::protobuf::uint32 value) {
  set_has_snapshotid();
  snapshotid_ = value;
}

// optional .hadoop.hdfs.fsimage.INodeSection.INode root = 2;
inline bool SnapshotSection_Snapshot::has_root() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotSection_Snapshot::set_has_root() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotSection_Snapshot::clear_has_root() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotSection_Snapshot::clear_root() {
  if (root_ != NULL) root_->::hadoop::hdfs::fsimage::INodeSection_INode::Clear();
  clear_has_root();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_INode& SnapshotSection_Snapshot::root() const {
  return root_ != NULL ? *root_ : *default_instance_->root_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INode* SnapshotSection_Snapshot::mutable_root() {
  set_has_root();
  if (root_ == NULL) root_ = new ::hadoop::hdfs::fsimage::INodeSection_INode;
  return root_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INode* SnapshotSection_Snapshot::release_root() {
  clear_has_root();
  ::hadoop::hdfs::fsimage::INodeSection_INode* temp = root_;
  root_ = NULL;
  return temp;
}
inline void SnapshotSection_Snapshot::set_allocated_root(::hadoop::hdfs::fsimage::INodeSection_INode* root) {
  delete root_;
  root_ = root;
  if (root) {
    set_has_root();
  } else {
    clear_has_root();
  }
}

// -------------------------------------------------------------------

// SnapshotSection

// optional uint32 snapshotCounter = 1;
inline bool SnapshotSection::has_snapshotcounter() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotSection::set_has_snapshotcounter() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotSection::clear_has_snapshotcounter() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotSection::clear_snapshotcounter() {
  snapshotcounter_ = 0u;
  clear_has_snapshotcounter();
}
inline ::google::protobuf::uint32 SnapshotSection::snapshotcounter() const {
  return snapshotcounter_;
}
inline void SnapshotSection::set_snapshotcounter(::google::protobuf::uint32 value) {
  set_has_snapshotcounter();
  snapshotcounter_ = value;
}

// repeated uint64 snapshottableDir = 2 [packed = true];
inline int SnapshotSection::snapshottabledir_size() const {
  return snapshottabledir_.size();
}
inline void SnapshotSection::clear_snapshottabledir() {
  snapshottabledir_.Clear();
}
inline ::google::protobuf::uint64 SnapshotSection::snapshottabledir(int index) const {
  return snapshottabledir_.Get(index);
}
inline void SnapshotSection::set_snapshottabledir(int index, ::google::protobuf::uint64 value) {
  snapshottabledir_.Set(index, value);
}
inline void SnapshotSection::add_snapshottabledir(::google::protobuf::uint64 value) {
  snapshottabledir_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
SnapshotSection::snapshottabledir() const {
  return snapshottabledir_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
SnapshotSection::mutable_snapshottabledir() {
  return &snapshottabledir_;
}

// optional uint32 numSnapshots = 3;
inline bool SnapshotSection::has_numsnapshots() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotSection::set_has_numsnapshots() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotSection::clear_has_numsnapshots() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotSection::clear_numsnapshots() {
  numsnapshots_ = 0u;
  clear_has_numsnapshots();
}
inline ::google::protobuf::uint32 SnapshotSection::numsnapshots() const {
  return numsnapshots_;
}
inline void SnapshotSection::set_numsnapshots(::google::protobuf::uint32 value) {
  set_has_numsnapshots();
  numsnapshots_ = value;
}

// -------------------------------------------------------------------

// SnapshotDiffSection_CreatedListEntry

// optional bytes name = 1;
inline bool SnapshotDiffSection_CreatedListEntry::has_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotDiffSection_CreatedListEntry::set_has_name() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotDiffSection_CreatedListEntry::clear_has_name() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotDiffSection_CreatedListEntry::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& SnapshotDiffSection_CreatedListEntry::name() const {
  return *name_;
}
inline void SnapshotDiffSection_CreatedListEntry::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void SnapshotDiffSection_CreatedListEntry::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void SnapshotDiffSection_CreatedListEntry::set_name(const void* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffSection_CreatedListEntry::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* SnapshotDiffSection_CreatedListEntry::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffSection_CreatedListEntry::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// SnapshotDiffSection_DirectoryDiff

// optional uint32 snapshotId = 1;
inline bool SnapshotDiffSection_DirectoryDiff::has_snapshotid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotDiffSection_DirectoryDiff::set_has_snapshotid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_has_snapshotid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_snapshotid() {
  snapshotid_ = 0u;
  clear_has_snapshotid();
}
inline ::google::protobuf::uint32 SnapshotDiffSection_DirectoryDiff::snapshotid() const {
  return snapshotid_;
}
inline void SnapshotDiffSection_DirectoryDiff::set_snapshotid(::google::protobuf::uint32 value) {
  set_has_snapshotid();
  snapshotid_ = value;
}

// optional uint32 childrenSize = 2;
inline bool SnapshotDiffSection_DirectoryDiff::has_childrensize() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotDiffSection_DirectoryDiff::set_has_childrensize() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_has_childrensize() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_childrensize() {
  childrensize_ = 0u;
  clear_has_childrensize();
}
inline ::google::protobuf::uint32 SnapshotDiffSection_DirectoryDiff::childrensize() const {
  return childrensize_;
}
inline void SnapshotDiffSection_DirectoryDiff::set_childrensize(::google::protobuf::uint32 value) {
  set_has_childrensize();
  childrensize_ = value;
}

// optional bool isSnapshotRoot = 3;
inline bool SnapshotDiffSection_DirectoryDiff::has_issnapshotroot() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotDiffSection_DirectoryDiff::set_has_issnapshotroot() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_has_issnapshotroot() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_issnapshotroot() {
  issnapshotroot_ = false;
  clear_has_issnapshotroot();
}
inline bool SnapshotDiffSection_DirectoryDiff::issnapshotroot() const {
  return issnapshotroot_;
}
inline void SnapshotDiffSection_DirectoryDiff::set_issnapshotroot(bool value) {
  set_has_issnapshotroot();
  issnapshotroot_ = value;
}

// optional bytes name = 4;
inline bool SnapshotDiffSection_DirectoryDiff::has_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void SnapshotDiffSection_DirectoryDiff::set_has_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_has_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& SnapshotDiffSection_DirectoryDiff::name() const {
  return *name_;
}
inline void SnapshotDiffSection_DirectoryDiff::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void SnapshotDiffSection_DirectoryDiff::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void SnapshotDiffSection_DirectoryDiff::set_name(const void* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffSection_DirectoryDiff::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* SnapshotDiffSection_DirectoryDiff::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffSection_DirectoryDiff::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.INodeDirectory snapshotCopy = 5;
inline bool SnapshotDiffSection_DirectoryDiff::has_snapshotcopy() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void SnapshotDiffSection_DirectoryDiff::set_has_snapshotcopy() {
  _has_bits_[0] |= 0x00000010u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_has_snapshotcopy() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_snapshotcopy() {
  if (snapshotcopy_ != NULL) snapshotcopy_->::hadoop::hdfs::fsimage::INodeSection_INodeDirectory::Clear();
  clear_has_snapshotcopy();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory& SnapshotDiffSection_DirectoryDiff::snapshotcopy() const {
  return snapshotcopy_ != NULL ? *snapshotcopy_ : *default_instance_->snapshotcopy_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* SnapshotDiffSection_DirectoryDiff::mutable_snapshotcopy() {
  set_has_snapshotcopy();
  if (snapshotcopy_ == NULL) snapshotcopy_ = new ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory;
  return snapshotcopy_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* SnapshotDiffSection_DirectoryDiff::release_snapshotcopy() {
  clear_has_snapshotcopy();
  ::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* temp = snapshotcopy_;
  snapshotcopy_ = NULL;
  return temp;
}
inline void SnapshotDiffSection_DirectoryDiff::set_allocated_snapshotcopy(::hadoop::hdfs::fsimage::INodeSection_INodeDirectory* snapshotcopy) {
  delete snapshotcopy_;
  snapshotcopy_ = snapshotcopy;
  if (snapshotcopy) {
    set_has_snapshotcopy();
  } else {
    clear_has_snapshotcopy();
  }
}

// optional uint32 createdListSize = 6;
inline bool SnapshotDiffSection_DirectoryDiff::has_createdlistsize() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void SnapshotDiffSection_DirectoryDiff::set_has_createdlistsize() {
  _has_bits_[0] |= 0x00000020u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_has_createdlistsize() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void SnapshotDiffSection_DirectoryDiff::clear_createdlistsize() {
  createdlistsize_ = 0u;
  clear_has_createdlistsize();
}
inline ::google::protobuf::uint32 SnapshotDiffSection_DirectoryDiff::createdlistsize() const {
  return createdlistsize_;
}
inline void SnapshotDiffSection_DirectoryDiff::set_createdlistsize(::google::protobuf::uint32 value) {
  set_has_createdlistsize();
  createdlistsize_ = value;
}

// repeated uint64 deletedINode = 7 [packed = true];
inline int SnapshotDiffSection_DirectoryDiff::deletedinode_size() const {
  return deletedinode_.size();
}
inline void SnapshotDiffSection_DirectoryDiff::clear_deletedinode() {
  deletedinode_.Clear();
}
inline ::google::protobuf::uint64 SnapshotDiffSection_DirectoryDiff::deletedinode(int index) const {
  return deletedinode_.Get(index);
}
inline void SnapshotDiffSection_DirectoryDiff::set_deletedinode(int index, ::google::protobuf::uint64 value) {
  deletedinode_.Set(index, value);
}
inline void SnapshotDiffSection_DirectoryDiff::add_deletedinode(::google::protobuf::uint64 value) {
  deletedinode_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
SnapshotDiffSection_DirectoryDiff::deletedinode() const {
  return deletedinode_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
SnapshotDiffSection_DirectoryDiff::mutable_deletedinode() {
  return &deletedinode_;
}

// repeated uint32 deletedINodeRef = 8 [packed = true];
inline int SnapshotDiffSection_DirectoryDiff::deletedinoderef_size() const {
  return deletedinoderef_.size();
}
inline void SnapshotDiffSection_DirectoryDiff::clear_deletedinoderef() {
  deletedinoderef_.Clear();
}
inline ::google::protobuf::uint32 SnapshotDiffSection_DirectoryDiff::deletedinoderef(int index) const {
  return deletedinoderef_.Get(index);
}
inline void SnapshotDiffSection_DirectoryDiff::set_deletedinoderef(int index, ::google::protobuf::uint32 value) {
  deletedinoderef_.Set(index, value);
}
inline void SnapshotDiffSection_DirectoryDiff::add_deletedinoderef(::google::protobuf::uint32 value) {
  deletedinoderef_.Add(value);
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
SnapshotDiffSection_DirectoryDiff::deletedinoderef() const {
  return deletedinoderef_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
SnapshotDiffSection_DirectoryDiff::mutable_deletedinoderef() {
  return &deletedinoderef_;
}

// -------------------------------------------------------------------

// SnapshotDiffSection_FileDiff

// optional uint32 snapshotId = 1;
inline bool SnapshotDiffSection_FileDiff::has_snapshotid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotDiffSection_FileDiff::set_has_snapshotid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotDiffSection_FileDiff::clear_has_snapshotid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotDiffSection_FileDiff::clear_snapshotid() {
  snapshotid_ = 0u;
  clear_has_snapshotid();
}
inline ::google::protobuf::uint32 SnapshotDiffSection_FileDiff::snapshotid() const {
  return snapshotid_;
}
inline void SnapshotDiffSection_FileDiff::set_snapshotid(::google::protobuf::uint32 value) {
  set_has_snapshotid();
  snapshotid_ = value;
}

// optional uint64 fileSize = 2;
inline bool SnapshotDiffSection_FileDiff::has_filesize() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotDiffSection_FileDiff::set_has_filesize() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotDiffSection_FileDiff::clear_has_filesize() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotDiffSection_FileDiff::clear_filesize() {
  filesize_ = GOOGLE_ULONGLONG(0);
  clear_has_filesize();
}
inline ::google::protobuf::uint64 SnapshotDiffSection_FileDiff::filesize() const {
  return filesize_;
}
inline void SnapshotDiffSection_FileDiff::set_filesize(::google::protobuf::uint64 value) {
  set_has_filesize();
  filesize_ = value;
}

// optional bytes name = 3;
inline bool SnapshotDiffSection_FileDiff::has_name() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotDiffSection_FileDiff::set_has_name() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotDiffSection_FileDiff::clear_has_name() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotDiffSection_FileDiff::clear_name() {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& SnapshotDiffSection_FileDiff::name() const {
  return *name_;
}
inline void SnapshotDiffSection_FileDiff::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void SnapshotDiffSection_FileDiff::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(value);
}
inline void SnapshotDiffSection_FileDiff::set_name(const void* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SnapshotDiffSection_FileDiff::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    name_ = new ::std::string;
  }
  return name_;
}
inline ::std::string* SnapshotDiffSection_FileDiff::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SnapshotDiffSection_FileDiff::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::kEmptyString) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional .hadoop.hdfs.fsimage.INodeSection.INodeFile snapshotCopy = 4;
inline bool SnapshotDiffSection_FileDiff::has_snapshotcopy() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void SnapshotDiffSection_FileDiff::set_has_snapshotcopy() {
  _has_bits_[0] |= 0x00000008u;
}
inline void SnapshotDiffSection_FileDiff::clear_has_snapshotcopy() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void SnapshotDiffSection_FileDiff::clear_snapshotcopy() {
  if (snapshotcopy_ != NULL) snapshotcopy_->::hadoop::hdfs::fsimage::INodeSection_INodeFile::Clear();
  clear_has_snapshotcopy();
}
inline const ::hadoop::hdfs::fsimage::INodeSection_INodeFile& SnapshotDiffSection_FileDiff::snapshotcopy() const {
  return snapshotcopy_ != NULL ? *snapshotcopy_ : *default_instance_->snapshotcopy_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* SnapshotDiffSection_FileDiff::mutable_snapshotcopy() {
  set_has_snapshotcopy();
  if (snapshotcopy_ == NULL) snapshotcopy_ = new ::hadoop::hdfs::fsimage::INodeSection_INodeFile;
  return snapshotcopy_;
}
inline ::hadoop::hdfs::fsimage::INodeSection_INodeFile* SnapshotDiffSection_FileDiff::release_snapshotcopy() {
  clear_has_snapshotcopy();
  ::hadoop::hdfs::fsimage::INodeSection_INodeFile* temp = snapshotcopy_;
  snapshotcopy_ = NULL;
  return temp;
}
inline void SnapshotDiffSection_FileDiff::set_allocated_snapshotcopy(::hadoop::hdfs::fsimage::INodeSection_INodeFile* snapshotcopy) {
  delete snapshotcopy_;
  snapshotcopy_ = snapshotcopy;
  if (snapshotcopy) {
    set_has_snapshotcopy();
  } else {
    clear_has_snapshotcopy();
  }
}

// -------------------------------------------------------------------

// SnapshotDiffSection_DiffEntry

// required .hadoop.hdfs.fsimage.SnapshotDiffSection.DiffEntry.Type type = 1;
inline bool SnapshotDiffSection_DiffEntry::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SnapshotDiffSection_DiffEntry::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SnapshotDiffSection_DiffEntry::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SnapshotDiffSection_DiffEntry::clear_type() {
  type_ = 1;
  clear_has_type();
}
inline ::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type SnapshotDiffSection_DiffEntry::type() const {
  return static_cast< ::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type >(type_);
}
inline void SnapshotDiffSection_DiffEntry::set_type(::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type value) {
  assert(::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type_IsValid(value));
  set_has_type();
  type_ = value;
}

// optional uint64 inodeId = 2;
inline bool SnapshotDiffSection_DiffEntry::has_inodeid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SnapshotDiffSection_DiffEntry::set_has_inodeid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SnapshotDiffSection_DiffEntry::clear_has_inodeid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SnapshotDiffSection_DiffEntry::clear_inodeid() {
  inodeid_ = GOOGLE_ULONGLONG(0);
  clear_has_inodeid();
}
inline ::google::protobuf::uint64 SnapshotDiffSection_DiffEntry::inodeid() const {
  return inodeid_;
}
inline void SnapshotDiffSection_DiffEntry::set_inodeid(::google::protobuf::uint64 value) {
  set_has_inodeid();
  inodeid_ = value;
}

// optional uint32 numOfDiff = 3;
inline bool SnapshotDiffSection_DiffEntry::has_numofdiff() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SnapshotDiffSection_DiffEntry::set_has_numofdiff() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SnapshotDiffSection_DiffEntry::clear_has_numofdiff() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SnapshotDiffSection_DiffEntry::clear_numofdiff() {
  numofdiff_ = 0u;
  clear_has_numofdiff();
}
inline ::google::protobuf::uint32 SnapshotDiffSection_DiffEntry::numofdiff() const {
  return numofdiff_;
}
inline void SnapshotDiffSection_DiffEntry::set_numofdiff(::google::protobuf::uint32 value) {
  set_has_numofdiff();
  numofdiff_ = value;
}

// -------------------------------------------------------------------

// SnapshotDiffSection

// -------------------------------------------------------------------

// StringTableSection_Entry

// optional uint32 id = 1;
inline bool StringTableSection_Entry::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StringTableSection_Entry::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StringTableSection_Entry::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StringTableSection_Entry::clear_id() {
  id_ = 0u;
  clear_has_id();
}
inline ::google::protobuf::uint32 StringTableSection_Entry::id() const {
  return id_;
}
inline void StringTableSection_Entry::set_id(::google::protobuf::uint32 value) {
  set_has_id();
  id_ = value;
}

// optional string str = 2;
inline bool StringTableSection_Entry::has_str() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void StringTableSection_Entry::set_has_str() {
  _has_bits_[0] |= 0x00000002u;
}
inline void StringTableSection_Entry::clear_has_str() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void StringTableSection_Entry::clear_str() {
  if (str_ != &::google::protobuf::internal::kEmptyString) {
    str_->clear();
  }
  clear_has_str();
}
inline const ::std::string& StringTableSection_Entry::str() const {
  return *str_;
}
inline void StringTableSection_Entry::set_str(const ::std::string& value) {
  set_has_str();
  if (str_ == &::google::protobuf::internal::kEmptyString) {
    str_ = new ::std::string;
  }
  str_->assign(value);
}
inline void StringTableSection_Entry::set_str(const char* value) {
  set_has_str();
  if (str_ == &::google::protobuf::internal::kEmptyString) {
    str_ = new ::std::string;
  }
  str_->assign(value);
}
inline void StringTableSection_Entry::set_str(const char* value, size_t size) {
  set_has_str();
  if (str_ == &::google::protobuf::internal::kEmptyString) {
    str_ = new ::std::string;
  }
  str_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* StringTableSection_Entry::mutable_str() {
  set_has_str();
  if (str_ == &::google::protobuf::internal::kEmptyString) {
    str_ = new ::std::string;
  }
  return str_;
}
inline ::std::string* StringTableSection_Entry::release_str() {
  clear_has_str();
  if (str_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = str_;
    str_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void StringTableSection_Entry::set_allocated_str(::std::string* str) {
  if (str_ != &::google::protobuf::internal::kEmptyString) {
    delete str_;
  }
  if (str) {
    set_has_str();
    str_ = str;
  } else {
    clear_has_str();
    str_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// StringTableSection

// optional uint32 numEntry = 1;
inline bool StringTableSection::has_numentry() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void StringTableSection::set_has_numentry() {
  _has_bits_[0] |= 0x00000001u;
}
inline void StringTableSection::clear_has_numentry() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void StringTableSection::clear_numentry() {
  numentry_ = 0u;
  clear_has_numentry();
}
inline ::google::protobuf::uint32 StringTableSection::numentry() const {
  return numentry_;
}
inline void StringTableSection::set_numentry(::google::protobuf::uint32 value) {
  set_has_numentry();
  numentry_ = value;
}

// -------------------------------------------------------------------

// SecretManagerSection_DelegationKey

// optional uint32 id = 1;
inline bool SecretManagerSection_DelegationKey::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SecretManagerSection_DelegationKey::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SecretManagerSection_DelegationKey::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SecretManagerSection_DelegationKey::clear_id() {
  id_ = 0u;
  clear_has_id();
}
inline ::google::protobuf::uint32 SecretManagerSection_DelegationKey::id() const {
  return id_;
}
inline void SecretManagerSection_DelegationKey::set_id(::google::protobuf::uint32 value) {
  set_has_id();
  id_ = value;
}

// optional uint64 expiryDate = 2;
inline bool SecretManagerSection_DelegationKey::has_expirydate() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SecretManagerSection_DelegationKey::set_has_expirydate() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SecretManagerSection_DelegationKey::clear_has_expirydate() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SecretManagerSection_DelegationKey::clear_expirydate() {
  expirydate_ = GOOGLE_ULONGLONG(0);
  clear_has_expirydate();
}
inline ::google::protobuf::uint64 SecretManagerSection_DelegationKey::expirydate() const {
  return expirydate_;
}
inline void SecretManagerSection_DelegationKey::set_expirydate(::google::protobuf::uint64 value) {
  set_has_expirydate();
  expirydate_ = value;
}

// optional bytes key = 3;
inline bool SecretManagerSection_DelegationKey::has_key() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SecretManagerSection_DelegationKey::set_has_key() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SecretManagerSection_DelegationKey::clear_has_key() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SecretManagerSection_DelegationKey::clear_key() {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    key_->clear();
  }
  clear_has_key();
}
inline const ::std::string& SecretManagerSection_DelegationKey::key() const {
  return *key_;
}
inline void SecretManagerSection_DelegationKey::set_key(const ::std::string& value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void SecretManagerSection_DelegationKey::set_key(const char* value) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(value);
}
inline void SecretManagerSection_DelegationKey::set_key(const void* value, size_t size) {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  key_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SecretManagerSection_DelegationKey::mutable_key() {
  set_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    key_ = new ::std::string;
  }
  return key_;
}
inline ::std::string* SecretManagerSection_DelegationKey::release_key() {
  clear_has_key();
  if (key_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = key_;
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SecretManagerSection_DelegationKey::set_allocated_key(::std::string* key) {
  if (key_ != &::google::protobuf::internal::kEmptyString) {
    delete key_;
  }
  if (key) {
    set_has_key();
    key_ = key;
  } else {
    clear_has_key();
    key_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// -------------------------------------------------------------------

// SecretManagerSection_PersistToken

// optional uint32 version = 1;
inline bool SecretManagerSection_PersistToken::has_version() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_version() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SecretManagerSection_PersistToken::clear_has_version() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SecretManagerSection_PersistToken::clear_version() {
  version_ = 0u;
  clear_has_version();
}
inline ::google::protobuf::uint32 SecretManagerSection_PersistToken::version() const {
  return version_;
}
inline void SecretManagerSection_PersistToken::set_version(::google::protobuf::uint32 value) {
  set_has_version();
  version_ = value;
}

// optional string owner = 2;
inline bool SecretManagerSection_PersistToken::has_owner() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_owner() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SecretManagerSection_PersistToken::clear_has_owner() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SecretManagerSection_PersistToken::clear_owner() {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    owner_->clear();
  }
  clear_has_owner();
}
inline const ::std::string& SecretManagerSection_PersistToken::owner() const {
  return *owner_;
}
inline void SecretManagerSection_PersistToken::set_owner(const ::std::string& value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void SecretManagerSection_PersistToken::set_owner(const char* value) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(value);
}
inline void SecretManagerSection_PersistToken::set_owner(const char* value, size_t size) {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  owner_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SecretManagerSection_PersistToken::mutable_owner() {
  set_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    owner_ = new ::std::string;
  }
  return owner_;
}
inline ::std::string* SecretManagerSection_PersistToken::release_owner() {
  clear_has_owner();
  if (owner_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = owner_;
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SecretManagerSection_PersistToken::set_allocated_owner(::std::string* owner) {
  if (owner_ != &::google::protobuf::internal::kEmptyString) {
    delete owner_;
  }
  if (owner) {
    set_has_owner();
    owner_ = owner;
  } else {
    clear_has_owner();
    owner_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string renewer = 3;
inline bool SecretManagerSection_PersistToken::has_renewer() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_renewer() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SecretManagerSection_PersistToken::clear_has_renewer() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SecretManagerSection_PersistToken::clear_renewer() {
  if (renewer_ != &::google::protobuf::internal::kEmptyString) {
    renewer_->clear();
  }
  clear_has_renewer();
}
inline const ::std::string& SecretManagerSection_PersistToken::renewer() const {
  return *renewer_;
}
inline void SecretManagerSection_PersistToken::set_renewer(const ::std::string& value) {
  set_has_renewer();
  if (renewer_ == &::google::protobuf::internal::kEmptyString) {
    renewer_ = new ::std::string;
  }
  renewer_->assign(value);
}
inline void SecretManagerSection_PersistToken::set_renewer(const char* value) {
  set_has_renewer();
  if (renewer_ == &::google::protobuf::internal::kEmptyString) {
    renewer_ = new ::std::string;
  }
  renewer_->assign(value);
}
inline void SecretManagerSection_PersistToken::set_renewer(const char* value, size_t size) {
  set_has_renewer();
  if (renewer_ == &::google::protobuf::internal::kEmptyString) {
    renewer_ = new ::std::string;
  }
  renewer_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SecretManagerSection_PersistToken::mutable_renewer() {
  set_has_renewer();
  if (renewer_ == &::google::protobuf::internal::kEmptyString) {
    renewer_ = new ::std::string;
  }
  return renewer_;
}
inline ::std::string* SecretManagerSection_PersistToken::release_renewer() {
  clear_has_renewer();
  if (renewer_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = renewer_;
    renewer_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SecretManagerSection_PersistToken::set_allocated_renewer(::std::string* renewer) {
  if (renewer_ != &::google::protobuf::internal::kEmptyString) {
    delete renewer_;
  }
  if (renewer) {
    set_has_renewer();
    renewer_ = renewer;
  } else {
    clear_has_renewer();
    renewer_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional string realUser = 4;
inline bool SecretManagerSection_PersistToken::has_realuser() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_realuser() {
  _has_bits_[0] |= 0x00000008u;
}
inline void SecretManagerSection_PersistToken::clear_has_realuser() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void SecretManagerSection_PersistToken::clear_realuser() {
  if (realuser_ != &::google::protobuf::internal::kEmptyString) {
    realuser_->clear();
  }
  clear_has_realuser();
}
inline const ::std::string& SecretManagerSection_PersistToken::realuser() const {
  return *realuser_;
}
inline void SecretManagerSection_PersistToken::set_realuser(const ::std::string& value) {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  realuser_->assign(value);
}
inline void SecretManagerSection_PersistToken::set_realuser(const char* value) {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  realuser_->assign(value);
}
inline void SecretManagerSection_PersistToken::set_realuser(const char* value, size_t size) {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  realuser_->assign(reinterpret_cast<const char*>(value), size);
}
inline ::std::string* SecretManagerSection_PersistToken::mutable_realuser() {
  set_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    realuser_ = new ::std::string;
  }
  return realuser_;
}
inline ::std::string* SecretManagerSection_PersistToken::release_realuser() {
  clear_has_realuser();
  if (realuser_ == &::google::protobuf::internal::kEmptyString) {
    return NULL;
  } else {
    ::std::string* temp = realuser_;
    realuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
    return temp;
  }
}
inline void SecretManagerSection_PersistToken::set_allocated_realuser(::std::string* realuser) {
  if (realuser_ != &::google::protobuf::internal::kEmptyString) {
    delete realuser_;
  }
  if (realuser) {
    set_has_realuser();
    realuser_ = realuser;
  } else {
    clear_has_realuser();
    realuser_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  }
}

// optional uint64 issueDate = 5;
inline bool SecretManagerSection_PersistToken::has_issuedate() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_issuedate() {
  _has_bits_[0] |= 0x00000010u;
}
inline void SecretManagerSection_PersistToken::clear_has_issuedate() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void SecretManagerSection_PersistToken::clear_issuedate() {
  issuedate_ = GOOGLE_ULONGLONG(0);
  clear_has_issuedate();
}
inline ::google::protobuf::uint64 SecretManagerSection_PersistToken::issuedate() const {
  return issuedate_;
}
inline void SecretManagerSection_PersistToken::set_issuedate(::google::protobuf::uint64 value) {
  set_has_issuedate();
  issuedate_ = value;
}

// optional uint64 maxDate = 6;
inline bool SecretManagerSection_PersistToken::has_maxdate() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_maxdate() {
  _has_bits_[0] |= 0x00000020u;
}
inline void SecretManagerSection_PersistToken::clear_has_maxdate() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void SecretManagerSection_PersistToken::clear_maxdate() {
  maxdate_ = GOOGLE_ULONGLONG(0);
  clear_has_maxdate();
}
inline ::google::protobuf::uint64 SecretManagerSection_PersistToken::maxdate() const {
  return maxdate_;
}
inline void SecretManagerSection_PersistToken::set_maxdate(::google::protobuf::uint64 value) {
  set_has_maxdate();
  maxdate_ = value;
}

// optional uint32 sequenceNumber = 7;
inline bool SecretManagerSection_PersistToken::has_sequencenumber() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_sequencenumber() {
  _has_bits_[0] |= 0x00000040u;
}
inline void SecretManagerSection_PersistToken::clear_has_sequencenumber() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void SecretManagerSection_PersistToken::clear_sequencenumber() {
  sequencenumber_ = 0u;
  clear_has_sequencenumber();
}
inline ::google::protobuf::uint32 SecretManagerSection_PersistToken::sequencenumber() const {
  return sequencenumber_;
}
inline void SecretManagerSection_PersistToken::set_sequencenumber(::google::protobuf::uint32 value) {
  set_has_sequencenumber();
  sequencenumber_ = value;
}

// optional uint32 masterKeyId = 8;
inline bool SecretManagerSection_PersistToken::has_masterkeyid() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_masterkeyid() {
  _has_bits_[0] |= 0x00000080u;
}
inline void SecretManagerSection_PersistToken::clear_has_masterkeyid() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void SecretManagerSection_PersistToken::clear_masterkeyid() {
  masterkeyid_ = 0u;
  clear_has_masterkeyid();
}
inline ::google::protobuf::uint32 SecretManagerSection_PersistToken::masterkeyid() const {
  return masterkeyid_;
}
inline void SecretManagerSection_PersistToken::set_masterkeyid(::google::protobuf::uint32 value) {
  set_has_masterkeyid();
  masterkeyid_ = value;
}

// optional uint64 expiryDate = 9;
inline bool SecretManagerSection_PersistToken::has_expirydate() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void SecretManagerSection_PersistToken::set_has_expirydate() {
  _has_bits_[0] |= 0x00000100u;
}
inline void SecretManagerSection_PersistToken::clear_has_expirydate() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void SecretManagerSection_PersistToken::clear_expirydate() {
  expirydate_ = GOOGLE_ULONGLONG(0);
  clear_has_expirydate();
}
inline ::google::protobuf::uint64 SecretManagerSection_PersistToken::expirydate() const {
  return expirydate_;
}
inline void SecretManagerSection_PersistToken::set_expirydate(::google::protobuf::uint64 value) {
  set_has_expirydate();
  expirydate_ = value;
}

// -------------------------------------------------------------------

// SecretManagerSection

// optional uint32 currentId = 1;
inline bool SecretManagerSection::has_currentid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void SecretManagerSection::set_has_currentid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void SecretManagerSection::clear_has_currentid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void SecretManagerSection::clear_currentid() {
  currentid_ = 0u;
  clear_has_currentid();
}
inline ::google::protobuf::uint32 SecretManagerSection::currentid() const {
  return currentid_;
}
inline void SecretManagerSection::set_currentid(::google::protobuf::uint32 value) {
  set_has_currentid();
  currentid_ = value;
}

// optional uint32 tokenSequenceNumber = 2;
inline bool SecretManagerSection::has_tokensequencenumber() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void SecretManagerSection::set_has_tokensequencenumber() {
  _has_bits_[0] |= 0x00000002u;
}
inline void SecretManagerSection::clear_has_tokensequencenumber() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void SecretManagerSection::clear_tokensequencenumber() {
  tokensequencenumber_ = 0u;
  clear_has_tokensequencenumber();
}
inline ::google::protobuf::uint32 SecretManagerSection::tokensequencenumber() const {
  return tokensequencenumber_;
}
inline void SecretManagerSection::set_tokensequencenumber(::google::protobuf::uint32 value) {
  set_has_tokensequencenumber();
  tokensequencenumber_ = value;
}

// optional uint32 numKeys = 3;
inline bool SecretManagerSection::has_numkeys() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void SecretManagerSection::set_has_numkeys() {
  _has_bits_[0] |= 0x00000004u;
}
inline void SecretManagerSection::clear_has_numkeys() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void SecretManagerSection::clear_numkeys() {
  numkeys_ = 0u;
  clear_has_numkeys();
}
inline ::google::protobuf::uint32 SecretManagerSection::numkeys() const {
  return numkeys_;
}
inline void SecretManagerSection::set_numkeys(::google::protobuf::uint32 value) {
  set_has_numkeys();
  numkeys_ = value;
}

// optional uint32 numTokens = 4;
inline bool SecretManagerSection::has_numtokens() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void SecretManagerSection::set_has_numtokens() {
  _has_bits_[0] |= 0x00000008u;
}
inline void SecretManagerSection::clear_has_numtokens() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void SecretManagerSection::clear_numtokens() {
  numtokens_ = 0u;
  clear_has_numtokens();
}
inline ::google::protobuf::uint32 SecretManagerSection::numtokens() const {
  return numtokens_;
}
inline void SecretManagerSection::set_numtokens(::google::protobuf::uint32 value) {
  set_has_numtokens();
  numtokens_ = value;
}

// -------------------------------------------------------------------

// CacheManagerSection

// required uint64 nextDirectiveId = 1;
inline bool CacheManagerSection::has_nextdirectiveid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void CacheManagerSection::set_has_nextdirectiveid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void CacheManagerSection::clear_has_nextdirectiveid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void CacheManagerSection::clear_nextdirectiveid() {
  nextdirectiveid_ = GOOGLE_ULONGLONG(0);
  clear_has_nextdirectiveid();
}
inline ::google::protobuf::uint64 CacheManagerSection::nextdirectiveid() const {
  return nextdirectiveid_;
}
inline void CacheManagerSection::set_nextdirectiveid(::google::protobuf::uint64 value) {
  set_has_nextdirectiveid();
  nextdirectiveid_ = value;
}

// required uint32 numPools = 2;
inline bool CacheManagerSection::has_numpools() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void CacheManagerSection::set_has_numpools() {
  _has_bits_[0] |= 0x00000002u;
}
inline void CacheManagerSection::clear_has_numpools() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void CacheManagerSection::clear_numpools() {
  numpools_ = 0u;
  clear_has_numpools();
}
inline ::google::protobuf::uint32 CacheManagerSection::numpools() const {
  return numpools_;
}
inline void CacheManagerSection::set_numpools(::google::protobuf::uint32 value) {
  set_has_numpools();
  numpools_ = value;
}

// required uint32 numDirectives = 3;
inline bool CacheManagerSection::has_numdirectives() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void CacheManagerSection::set_has_numdirectives() {
  _has_bits_[0] |= 0x00000004u;
}
inline void CacheManagerSection::clear_has_numdirectives() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void CacheManagerSection::clear_numdirectives() {
  numdirectives_ = 0u;
  clear_has_numdirectives();
}
inline ::google::protobuf::uint32 CacheManagerSection::numdirectives() const {
  return numdirectives_;
}
inline void CacheManagerSection::set_numdirectives(::google::protobuf::uint32 value) {
  set_has_numdirectives();
  numdirectives_ = value;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace fsimage
}  // namespace hdfs
}  // namespace hadoop

#ifndef SWIG
namespace google {
namespace protobuf {

template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::fsimage::INodeSection_INode_Type>() {
  return ::hadoop::hdfs::fsimage::INodeSection_INode_Type_descriptor();
}
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type>() {
  return ::hadoop::hdfs::fsimage::SnapshotDiffSection_DiffEntry_Type_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_fsimage_2eproto__INCLUDED
