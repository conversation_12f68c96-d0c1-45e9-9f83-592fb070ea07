// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: NamenodeProtocol.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "NamenodeProtocol.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace hadoop {
namespace hdfs {
namespace namenode {

namespace {

const ::google::protobuf::Descriptor* GetBlocksRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetBlocksRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetBlocksResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetBlocksResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetBlockKeysRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetBlockKeysRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetBlockKeysResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetBlockKeysResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetTransactionIdRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetTransactionIdRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetTransactionIdResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetTransactionIdResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RollEditLogRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RollEditLogRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RollEditLogResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RollEditLogResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetMostRecentCheckpointTxIdRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetMostRecentCheckpointTxIdRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetMostRecentCheckpointTxIdResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetMostRecentCheckpointTxIdResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ErrorReportRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ErrorReportRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* ErrorReportResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ErrorReportResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RegisterRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RegisterRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* RegisterResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RegisterResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* StartCheckpointRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StartCheckpointRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* StartCheckpointResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StartCheckpointResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* EndCheckpointRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  EndCheckpointRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* EndCheckpointResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  EndCheckpointResponseProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetEditLogManifestRequestProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetEditLogManifestRequestProto_reflection_ = NULL;
const ::google::protobuf::Descriptor* GetEditLogManifestResponseProto_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  GetEditLogManifestResponseProto_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_NamenodeProtocol_2eproto() {
  protobuf_AddDesc_NamenodeProtocol_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "NamenodeProtocol.proto");
  GOOGLE_CHECK(file != NULL);
  GetBlocksRequestProto_descriptor_ = file->message_type(0);
  static const int GetBlocksRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksRequestProto, datanode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksRequestProto, size_),
  };
  GetBlocksRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetBlocksRequestProto_descriptor_,
      GetBlocksRequestProto::default_instance_,
      GetBlocksRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetBlocksRequestProto));
  GetBlocksResponseProto_descriptor_ = file->message_type(1);
  static const int GetBlocksResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksResponseProto, blocks_),
  };
  GetBlocksResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetBlocksResponseProto_descriptor_,
      GetBlocksResponseProto::default_instance_,
      GetBlocksResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlocksResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetBlocksResponseProto));
  GetBlockKeysRequestProto_descriptor_ = file->message_type(2);
  static const int GetBlockKeysRequestProto_offsets_[1] = {
  };
  GetBlockKeysRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetBlockKeysRequestProto_descriptor_,
      GetBlockKeysRequestProto::default_instance_,
      GetBlockKeysRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlockKeysRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlockKeysRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetBlockKeysRequestProto));
  GetBlockKeysResponseProto_descriptor_ = file->message_type(3);
  static const int GetBlockKeysResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlockKeysResponseProto, keys_),
  };
  GetBlockKeysResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetBlockKeysResponseProto_descriptor_,
      GetBlockKeysResponseProto::default_instance_,
      GetBlockKeysResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlockKeysResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBlockKeysResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetBlockKeysResponseProto));
  GetTransactionIdRequestProto_descriptor_ = file->message_type(4);
  static const int GetTransactionIdRequestProto_offsets_[1] = {
  };
  GetTransactionIdRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetTransactionIdRequestProto_descriptor_,
      GetTransactionIdRequestProto::default_instance_,
      GetTransactionIdRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetTransactionIdRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetTransactionIdRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetTransactionIdRequestProto));
  GetTransactionIdResponseProto_descriptor_ = file->message_type(5);
  static const int GetTransactionIdResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetTransactionIdResponseProto, txid_),
  };
  GetTransactionIdResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetTransactionIdResponseProto_descriptor_,
      GetTransactionIdResponseProto::default_instance_,
      GetTransactionIdResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetTransactionIdResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetTransactionIdResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetTransactionIdResponseProto));
  RollEditLogRequestProto_descriptor_ = file->message_type(6);
  static const int RollEditLogRequestProto_offsets_[1] = {
  };
  RollEditLogRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RollEditLogRequestProto_descriptor_,
      RollEditLogRequestProto::default_instance_,
      RollEditLogRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RollEditLogRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RollEditLogRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RollEditLogRequestProto));
  RollEditLogResponseProto_descriptor_ = file->message_type(7);
  static const int RollEditLogResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RollEditLogResponseProto, signature_),
  };
  RollEditLogResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RollEditLogResponseProto_descriptor_,
      RollEditLogResponseProto::default_instance_,
      RollEditLogResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RollEditLogResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RollEditLogResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RollEditLogResponseProto));
  GetMostRecentCheckpointTxIdRequestProto_descriptor_ = file->message_type(8);
  static const int GetMostRecentCheckpointTxIdRequestProto_offsets_[1] = {
  };
  GetMostRecentCheckpointTxIdRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetMostRecentCheckpointTxIdRequestProto_descriptor_,
      GetMostRecentCheckpointTxIdRequestProto::default_instance_,
      GetMostRecentCheckpointTxIdRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetMostRecentCheckpointTxIdRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetMostRecentCheckpointTxIdRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetMostRecentCheckpointTxIdRequestProto));
  GetMostRecentCheckpointTxIdResponseProto_descriptor_ = file->message_type(9);
  static const int GetMostRecentCheckpointTxIdResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetMostRecentCheckpointTxIdResponseProto, txid_),
  };
  GetMostRecentCheckpointTxIdResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetMostRecentCheckpointTxIdResponseProto_descriptor_,
      GetMostRecentCheckpointTxIdResponseProto::default_instance_,
      GetMostRecentCheckpointTxIdResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetMostRecentCheckpointTxIdResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetMostRecentCheckpointTxIdResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetMostRecentCheckpointTxIdResponseProto));
  ErrorReportRequestProto_descriptor_ = file->message_type(10);
  static const int ErrorReportRequestProto_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportRequestProto, registration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportRequestProto, errorcode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportRequestProto, msg_),
  };
  ErrorReportRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ErrorReportRequestProto_descriptor_,
      ErrorReportRequestProto::default_instance_,
      ErrorReportRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ErrorReportRequestProto));
  ErrorReportResponseProto_descriptor_ = file->message_type(11);
  static const int ErrorReportResponseProto_offsets_[1] = {
  };
  ErrorReportResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      ErrorReportResponseProto_descriptor_,
      ErrorReportResponseProto::default_instance_,
      ErrorReportResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ErrorReportResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(ErrorReportResponseProto));
  RegisterRequestProto_descriptor_ = file->message_type(12);
  static const int RegisterRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RegisterRequestProto, registration_),
  };
  RegisterRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RegisterRequestProto_descriptor_,
      RegisterRequestProto::default_instance_,
      RegisterRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RegisterRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RegisterRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RegisterRequestProto));
  RegisterResponseProto_descriptor_ = file->message_type(13);
  static const int RegisterResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RegisterResponseProto, registration_),
  };
  RegisterResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      RegisterResponseProto_descriptor_,
      RegisterResponseProto::default_instance_,
      RegisterResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RegisterResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RegisterResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(RegisterResponseProto));
  StartCheckpointRequestProto_descriptor_ = file->message_type(14);
  static const int StartCheckpointRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartCheckpointRequestProto, registration_),
  };
  StartCheckpointRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      StartCheckpointRequestProto_descriptor_,
      StartCheckpointRequestProto::default_instance_,
      StartCheckpointRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartCheckpointRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartCheckpointRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(StartCheckpointRequestProto));
  StartCheckpointResponseProto_descriptor_ = file->message_type(15);
  static const int StartCheckpointResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartCheckpointResponseProto, command_),
  };
  StartCheckpointResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      StartCheckpointResponseProto_descriptor_,
      StartCheckpointResponseProto::default_instance_,
      StartCheckpointResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartCheckpointResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StartCheckpointResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(StartCheckpointResponseProto));
  EndCheckpointRequestProto_descriptor_ = file->message_type(16);
  static const int EndCheckpointRequestProto_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EndCheckpointRequestProto, registration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EndCheckpointRequestProto, signature_),
  };
  EndCheckpointRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      EndCheckpointRequestProto_descriptor_,
      EndCheckpointRequestProto::default_instance_,
      EndCheckpointRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EndCheckpointRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EndCheckpointRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(EndCheckpointRequestProto));
  EndCheckpointResponseProto_descriptor_ = file->message_type(17);
  static const int EndCheckpointResponseProto_offsets_[1] = {
  };
  EndCheckpointResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      EndCheckpointResponseProto_descriptor_,
      EndCheckpointResponseProto::default_instance_,
      EndCheckpointResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EndCheckpointResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EndCheckpointResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(EndCheckpointResponseProto));
  GetEditLogManifestRequestProto_descriptor_ = file->message_type(18);
  static const int GetEditLogManifestRequestProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, sincetxid_),
  };
  GetEditLogManifestRequestProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetEditLogManifestRequestProto_descriptor_,
      GetEditLogManifestRequestProto::default_instance_,
      GetEditLogManifestRequestProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestRequestProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetEditLogManifestRequestProto));
  GetEditLogManifestResponseProto_descriptor_ = file->message_type(19);
  static const int GetEditLogManifestResponseProto_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, manifest_),
  };
  GetEditLogManifestResponseProto_reflection_ =
    new ::google::protobuf::internal::GeneratedMessageReflection(
      GetEditLogManifestResponseProto_descriptor_,
      GetEditLogManifestResponseProto::default_instance_,
      GetEditLogManifestResponseProto_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, _has_bits_[0]),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetEditLogManifestResponseProto, _unknown_fields_),
      -1,
      ::google::protobuf::DescriptorPool::generated_pool(),
      ::google::protobuf::MessageFactory::generated_factory(),
      sizeof(GetEditLogManifestResponseProto));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
inline void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_NamenodeProtocol_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetBlocksRequestProto_descriptor_, &GetBlocksRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetBlocksResponseProto_descriptor_, &GetBlocksResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetBlockKeysRequestProto_descriptor_, &GetBlockKeysRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetBlockKeysResponseProto_descriptor_, &GetBlockKeysResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetTransactionIdRequestProto_descriptor_, &GetTransactionIdRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetTransactionIdResponseProto_descriptor_, &GetTransactionIdResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RollEditLogRequestProto_descriptor_, &RollEditLogRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RollEditLogResponseProto_descriptor_, &RollEditLogResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetMostRecentCheckpointTxIdRequestProto_descriptor_, &GetMostRecentCheckpointTxIdRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetMostRecentCheckpointTxIdResponseProto_descriptor_, &GetMostRecentCheckpointTxIdResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ErrorReportRequestProto_descriptor_, &ErrorReportRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    ErrorReportResponseProto_descriptor_, &ErrorReportResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RegisterRequestProto_descriptor_, &RegisterRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    RegisterResponseProto_descriptor_, &RegisterResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    StartCheckpointRequestProto_descriptor_, &StartCheckpointRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    StartCheckpointResponseProto_descriptor_, &StartCheckpointResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    EndCheckpointRequestProto_descriptor_, &EndCheckpointRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    EndCheckpointResponseProto_descriptor_, &EndCheckpointResponseProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetEditLogManifestRequestProto_descriptor_, &GetEditLogManifestRequestProto::default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
    GetEditLogManifestResponseProto_descriptor_, &GetEditLogManifestResponseProto::default_instance());
}

}  // namespace

void protobuf_ShutdownFile_NamenodeProtocol_2eproto() {
  delete GetBlocksRequestProto::default_instance_;
  delete GetBlocksRequestProto_reflection_;
  delete GetBlocksResponseProto::default_instance_;
  delete GetBlocksResponseProto_reflection_;
  delete GetBlockKeysRequestProto::default_instance_;
  delete GetBlockKeysRequestProto_reflection_;
  delete GetBlockKeysResponseProto::default_instance_;
  delete GetBlockKeysResponseProto_reflection_;
  delete GetTransactionIdRequestProto::default_instance_;
  delete GetTransactionIdRequestProto_reflection_;
  delete GetTransactionIdResponseProto::default_instance_;
  delete GetTransactionIdResponseProto_reflection_;
  delete RollEditLogRequestProto::default_instance_;
  delete RollEditLogRequestProto_reflection_;
  delete RollEditLogResponseProto::default_instance_;
  delete RollEditLogResponseProto_reflection_;
  delete GetMostRecentCheckpointTxIdRequestProto::default_instance_;
  delete GetMostRecentCheckpointTxIdRequestProto_reflection_;
  delete GetMostRecentCheckpointTxIdResponseProto::default_instance_;
  delete GetMostRecentCheckpointTxIdResponseProto_reflection_;
  delete ErrorReportRequestProto::default_instance_;
  delete ErrorReportRequestProto_reflection_;
  delete ErrorReportResponseProto::default_instance_;
  delete ErrorReportResponseProto_reflection_;
  delete RegisterRequestProto::default_instance_;
  delete RegisterRequestProto_reflection_;
  delete RegisterResponseProto::default_instance_;
  delete RegisterResponseProto_reflection_;
  delete StartCheckpointRequestProto::default_instance_;
  delete StartCheckpointRequestProto_reflection_;
  delete StartCheckpointResponseProto::default_instance_;
  delete StartCheckpointResponseProto_reflection_;
  delete EndCheckpointRequestProto::default_instance_;
  delete EndCheckpointRequestProto_reflection_;
  delete EndCheckpointResponseProto::default_instance_;
  delete EndCheckpointResponseProto_reflection_;
  delete GetEditLogManifestRequestProto::default_instance_;
  delete GetEditLogManifestRequestProto_reflection_;
  delete GetEditLogManifestResponseProto::default_instance_;
  delete GetEditLogManifestResponseProto_reflection_;
}

void protobuf_AddDesc_NamenodeProtocol_2eproto() {
  static bool already_here = false;
  if (already_here) return;
  already_here = true;
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::hadoop::hdfs::protobuf_AddDesc_hdfs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\026NamenodeProtocol.proto\022\024hadoop.hdfs.na"
    "menode\032\nhdfs.proto\"U\n\025GetBlocksRequestPr"
    "oto\022.\n\010datanode\030\001 \002(\0132\034.hadoop.hdfs.Data"
    "nodeIDProto\022\014\n\004size\030\002 \002(\004\"O\n\026GetBlocksRe"
    "sponseProto\0225\n\006blocks\030\001 \002(\0132%.hadoop.hdf"
    "s.BlocksWithLocationsProto\"\032\n\030GetBlockKe"
    "ysRequestProto\"N\n\031GetBlockKeysResponsePr"
    "oto\0221\n\004keys\030\001 \001(\0132#.hadoop.hdfs.Exported"
    "BlockKeysProto\"\036\n\034GetTransactionIdReques"
    "tProto\"-\n\035GetTransactionIdResponseProto\022"
    "\014\n\004txId\030\001 \002(\004\"\031\n\027RollEditLogRequestProto"
    "\"T\n\030RollEditLogResponseProto\0228\n\tsignatur"
    "e\030\001 \002(\0132%.hadoop.hdfs.CheckpointSignatur"
    "eProto\")\n\'GetMostRecentCheckpointTxIdReq"
    "uestProto\"8\n(GetMostRecentCheckpointTxId"
    "ResponseProto\022\014\n\004txId\030\001 \002(\004\"w\n\027ErrorRepo"
    "rtRequestProto\022<\n\014registration\030\001 \002(\0132&.h"
    "adoop.hdfs.NamenodeRegistrationProto\022\021\n\t"
    "errorCode\030\002 \002(\r\022\013\n\003msg\030\003 \002(\t\"\032\n\030ErrorRep"
    "ortResponseProto\"T\n\024RegisterRequestProto"
    "\022<\n\014registration\030\001 \002(\0132&.hadoop.hdfs.Nam"
    "enodeRegistrationProto\"U\n\025RegisterRespon"
    "seProto\022<\n\014registration\030\001 \002(\0132&.hadoop.h"
    "dfs.NamenodeRegistrationProto\"[\n\033StartCh"
    "eckpointRequestProto\022<\n\014registration\030\001 \002"
    "(\0132&.hadoop.hdfs.NamenodeRegistrationPro"
    "to\"R\n\034StartCheckpointResponseProto\0222\n\007co"
    "mmand\030\001 \002(\0132!.hadoop.hdfs.NamenodeComman"
    "dProto\"\223\001\n\031EndCheckpointRequestProto\022<\n\014"
    "registration\030\001 \002(\0132&.hadoop.hdfs.Namenod"
    "eRegistrationProto\0228\n\tsignature\030\002 \002(\0132%."
    "hadoop.hdfs.CheckpointSignatureProto\"\034\n\032"
    "EndCheckpointResponseProto\"3\n\036GetEditLog"
    "ManifestRequestProto\022\021\n\tsinceTxId\030\001 \002(\004\""
    "\\\n\037GetEditLogManifestResponseProto\0229\n\010ma"
    "nifest\030\001 \002(\0132\'.hadoop.hdfs.RemoteEditLog"
    "ManifestProto2\253\n\n\027NamenodeProtocolServic"
    "e\022f\n\tgetBlocks\022+.hadoop.hdfs.namenode.Ge"
    "tBlocksRequestProto\032,.hadoop.hdfs.nameno"
    "de.GetBlocksResponseProto\022o\n\014getBlockKey"
    "s\022..hadoop.hdfs.namenode.GetBlockKeysReq"
    "uestProto\032/.hadoop.hdfs.namenode.GetBloc"
    "kKeysResponseProto\022{\n\020getTransactionId\0222"
    ".hadoop.hdfs.namenode.GetTransactionIdRe"
    "questProto\0323.hadoop.hdfs.namenode.GetTra"
    "nsactionIdResponseProto\022\234\001\n\033getMostRecen"
    "tCheckpointTxId\022=.hadoop.hdfs.namenode.G"
    "etMostRecentCheckpointTxIdRequestProto\032>"
    ".hadoop.hdfs.namenode.GetMostRecentCheck"
    "pointTxIdResponseProto\022l\n\013rollEditLog\022-."
    "hadoop.hdfs.namenode.RollEditLogRequestP"
    "roto\032..hadoop.hdfs.namenode.RollEditLogR"
    "esponseProto\022U\n\016versionRequest\022 .hadoop."
    "hdfs.VersionRequestProto\032!.hadoop.hdfs.V"
    "ersionResponseProto\022l\n\013errorReport\022-.had"
    "oop.hdfs.namenode.ErrorReportRequestProt"
    "o\032..hadoop.hdfs.namenode.ErrorReportResp"
    "onseProto\022v\n\033registerSubordinateNamenode"
    "\022*.hadoop.hdfs.namenode.RegisterRequestP"
    "roto\032+.hadoop.hdfs.namenode.RegisterResp"
    "onseProto\022x\n\017startCheckpoint\0221.hadoop.hd"
    "fs.namenode.StartCheckpointRequestProto\032"
    "2.hadoop.hdfs.namenode.StartCheckpointRe"
    "sponseProto\022r\n\rendCheckpoint\022/.hadoop.hd"
    "fs.namenode.EndCheckpointRequestProto\0320."
    "hadoop.hdfs.namenode.EndCheckpointRespon"
    "seProto\022\201\001\n\022getEditLogManifest\0224.hadoop."
    "hdfs.namenode.GetEditLogManifestRequestP"
    "roto\0325.hadoop.hdfs.namenode.GetEditLogMa"
    "nifestResponseProtoBE\n%org.apache.hadoop"
    ".hdfs.protocol.protoB\026NamenodeProtocolPr"
    "otos\210\001\001\240\001\001", 2850);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "NamenodeProtocol.proto", &protobuf_RegisterTypes);
  GetBlocksRequestProto::default_instance_ = new GetBlocksRequestProto();
  GetBlocksResponseProto::default_instance_ = new GetBlocksResponseProto();
  GetBlockKeysRequestProto::default_instance_ = new GetBlockKeysRequestProto();
  GetBlockKeysResponseProto::default_instance_ = new GetBlockKeysResponseProto();
  GetTransactionIdRequestProto::default_instance_ = new GetTransactionIdRequestProto();
  GetTransactionIdResponseProto::default_instance_ = new GetTransactionIdResponseProto();
  RollEditLogRequestProto::default_instance_ = new RollEditLogRequestProto();
  RollEditLogResponseProto::default_instance_ = new RollEditLogResponseProto();
  GetMostRecentCheckpointTxIdRequestProto::default_instance_ = new GetMostRecentCheckpointTxIdRequestProto();
  GetMostRecentCheckpointTxIdResponseProto::default_instance_ = new GetMostRecentCheckpointTxIdResponseProto();
  ErrorReportRequestProto::default_instance_ = new ErrorReportRequestProto();
  ErrorReportResponseProto::default_instance_ = new ErrorReportResponseProto();
  RegisterRequestProto::default_instance_ = new RegisterRequestProto();
  RegisterResponseProto::default_instance_ = new RegisterResponseProto();
  StartCheckpointRequestProto::default_instance_ = new StartCheckpointRequestProto();
  StartCheckpointResponseProto::default_instance_ = new StartCheckpointResponseProto();
  EndCheckpointRequestProto::default_instance_ = new EndCheckpointRequestProto();
  EndCheckpointResponseProto::default_instance_ = new EndCheckpointResponseProto();
  GetEditLogManifestRequestProto::default_instance_ = new GetEditLogManifestRequestProto();
  GetEditLogManifestResponseProto::default_instance_ = new GetEditLogManifestResponseProto();
  GetBlocksRequestProto::default_instance_->InitAsDefaultInstance();
  GetBlocksResponseProto::default_instance_->InitAsDefaultInstance();
  GetBlockKeysRequestProto::default_instance_->InitAsDefaultInstance();
  GetBlockKeysResponseProto::default_instance_->InitAsDefaultInstance();
  GetTransactionIdRequestProto::default_instance_->InitAsDefaultInstance();
  GetTransactionIdResponseProto::default_instance_->InitAsDefaultInstance();
  RollEditLogRequestProto::default_instance_->InitAsDefaultInstance();
  RollEditLogResponseProto::default_instance_->InitAsDefaultInstance();
  GetMostRecentCheckpointTxIdRequestProto::default_instance_->InitAsDefaultInstance();
  GetMostRecentCheckpointTxIdResponseProto::default_instance_->InitAsDefaultInstance();
  ErrorReportRequestProto::default_instance_->InitAsDefaultInstance();
  ErrorReportResponseProto::default_instance_->InitAsDefaultInstance();
  RegisterRequestProto::default_instance_->InitAsDefaultInstance();
  RegisterResponseProto::default_instance_->InitAsDefaultInstance();
  StartCheckpointRequestProto::default_instance_->InitAsDefaultInstance();
  StartCheckpointResponseProto::default_instance_->InitAsDefaultInstance();
  EndCheckpointRequestProto::default_instance_->InitAsDefaultInstance();
  EndCheckpointResponseProto::default_instance_->InitAsDefaultInstance();
  GetEditLogManifestRequestProto::default_instance_->InitAsDefaultInstance();
  GetEditLogManifestResponseProto::default_instance_->InitAsDefaultInstance();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_NamenodeProtocol_2eproto);
}

// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_NamenodeProtocol_2eproto {
  StaticDescriptorInitializer_NamenodeProtocol_2eproto() {
    protobuf_AddDesc_NamenodeProtocol_2eproto();
  }
} static_descriptor_initializer_NamenodeProtocol_2eproto_;

// ===================================================================

#ifndef _MSC_VER
const int GetBlocksRequestProto::kDatanodeFieldNumber;
const int GetBlocksRequestProto::kSizeFieldNumber;
#endif  // !_MSC_VER

GetBlocksRequestProto::GetBlocksRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetBlocksRequestProto::InitAsDefaultInstance() {
  datanode_ = const_cast< ::hadoop::hdfs::DatanodeIDProto*>(&::hadoop::hdfs::DatanodeIDProto::default_instance());
}

GetBlocksRequestProto::GetBlocksRequestProto(const GetBlocksRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetBlocksRequestProto::SharedCtor() {
  _cached_size_ = 0;
  datanode_ = NULL;
  size_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetBlocksRequestProto::~GetBlocksRequestProto() {
  SharedDtor();
}

void GetBlocksRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete datanode_;
  }
}

void GetBlocksRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetBlocksRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetBlocksRequestProto_descriptor_;
}

const GetBlocksRequestProto& GetBlocksRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetBlocksRequestProto* GetBlocksRequestProto::default_instance_ = NULL;

GetBlocksRequestProto* GetBlocksRequestProto::New() const {
  return new GetBlocksRequestProto;
}

void GetBlocksRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_datanode()) {
      if (datanode_ != NULL) datanode_->::hadoop::hdfs::DatanodeIDProto::Clear();
    }
    size_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetBlocksRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.DatanodeIDProto datanode = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_datanode()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_size;
        break;
      }

      // required uint64 size = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_size:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &size_)));
          set_has_size();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetBlocksRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.DatanodeIDProto datanode = 1;
  if (has_datanode()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->datanode(), output);
  }

  // required uint64 size = 2;
  if (has_size()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->size(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetBlocksRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.DatanodeIDProto datanode = 1;
  if (has_datanode()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->datanode(), target);
  }

  // required uint64 size = 2;
  if (has_size()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->size(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetBlocksRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.DatanodeIDProto datanode = 1;
    if (has_datanode()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->datanode());
    }

    // required uint64 size = 2;
    if (has_size()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->size());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetBlocksRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetBlocksRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetBlocksRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetBlocksRequestProto::MergeFrom(const GetBlocksRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_datanode()) {
      mutable_datanode()->::hadoop::hdfs::DatanodeIDProto::MergeFrom(from.datanode());
    }
    if (from.has_size()) {
      set_size(from.size());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetBlocksRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetBlocksRequestProto::CopyFrom(const GetBlocksRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetBlocksRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_datanode()) {
    if (!this->datanode().IsInitialized()) return false;
  }
  return true;
}

void GetBlocksRequestProto::Swap(GetBlocksRequestProto* other) {
  if (other != this) {
    std::swap(datanode_, other->datanode_);
    std::swap(size_, other->size_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetBlocksRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetBlocksRequestProto_descriptor_;
  metadata.reflection = GetBlocksRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetBlocksResponseProto::kBlocksFieldNumber;
#endif  // !_MSC_VER

GetBlocksResponseProto::GetBlocksResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetBlocksResponseProto::InitAsDefaultInstance() {
  blocks_ = const_cast< ::hadoop::hdfs::BlocksWithLocationsProto*>(&::hadoop::hdfs::BlocksWithLocationsProto::default_instance());
}

GetBlocksResponseProto::GetBlocksResponseProto(const GetBlocksResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetBlocksResponseProto::SharedCtor() {
  _cached_size_ = 0;
  blocks_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetBlocksResponseProto::~GetBlocksResponseProto() {
  SharedDtor();
}

void GetBlocksResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete blocks_;
  }
}

void GetBlocksResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetBlocksResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetBlocksResponseProto_descriptor_;
}

const GetBlocksResponseProto& GetBlocksResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetBlocksResponseProto* GetBlocksResponseProto::default_instance_ = NULL;

GetBlocksResponseProto* GetBlocksResponseProto::New() const {
  return new GetBlocksResponseProto;
}

void GetBlocksResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_blocks()) {
      if (blocks_ != NULL) blocks_->::hadoop::hdfs::BlocksWithLocationsProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetBlocksResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.BlocksWithLocationsProto blocks = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_blocks()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetBlocksResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.BlocksWithLocationsProto blocks = 1;
  if (has_blocks()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->blocks(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetBlocksResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.BlocksWithLocationsProto blocks = 1;
  if (has_blocks()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->blocks(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetBlocksResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.BlocksWithLocationsProto blocks = 1;
    if (has_blocks()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->blocks());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetBlocksResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetBlocksResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetBlocksResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetBlocksResponseProto::MergeFrom(const GetBlocksResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_blocks()) {
      mutable_blocks()->::hadoop::hdfs::BlocksWithLocationsProto::MergeFrom(from.blocks());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetBlocksResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetBlocksResponseProto::CopyFrom(const GetBlocksResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetBlocksResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_blocks()) {
    if (!this->blocks().IsInitialized()) return false;
  }
  return true;
}

void GetBlocksResponseProto::Swap(GetBlocksResponseProto* other) {
  if (other != this) {
    std::swap(blocks_, other->blocks_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetBlocksResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetBlocksResponseProto_descriptor_;
  metadata.reflection = GetBlocksResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

GetBlockKeysRequestProto::GetBlockKeysRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetBlockKeysRequestProto::InitAsDefaultInstance() {
}

GetBlockKeysRequestProto::GetBlockKeysRequestProto(const GetBlockKeysRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetBlockKeysRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetBlockKeysRequestProto::~GetBlockKeysRequestProto() {
  SharedDtor();
}

void GetBlockKeysRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetBlockKeysRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetBlockKeysRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetBlockKeysRequestProto_descriptor_;
}

const GetBlockKeysRequestProto& GetBlockKeysRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetBlockKeysRequestProto* GetBlockKeysRequestProto::default_instance_ = NULL;

GetBlockKeysRequestProto* GetBlockKeysRequestProto::New() const {
  return new GetBlockKeysRequestProto;
}

void GetBlockKeysRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetBlockKeysRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void GetBlockKeysRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetBlockKeysRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetBlockKeysRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetBlockKeysRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetBlockKeysRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetBlockKeysRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetBlockKeysRequestProto::MergeFrom(const GetBlockKeysRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetBlockKeysRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetBlockKeysRequestProto::CopyFrom(const GetBlockKeysRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetBlockKeysRequestProto::IsInitialized() const {

  return true;
}

void GetBlockKeysRequestProto::Swap(GetBlockKeysRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetBlockKeysRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetBlockKeysRequestProto_descriptor_;
  metadata.reflection = GetBlockKeysRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetBlockKeysResponseProto::kKeysFieldNumber;
#endif  // !_MSC_VER

GetBlockKeysResponseProto::GetBlockKeysResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetBlockKeysResponseProto::InitAsDefaultInstance() {
  keys_ = const_cast< ::hadoop::hdfs::ExportedBlockKeysProto*>(&::hadoop::hdfs::ExportedBlockKeysProto::default_instance());
}

GetBlockKeysResponseProto::GetBlockKeysResponseProto(const GetBlockKeysResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetBlockKeysResponseProto::SharedCtor() {
  _cached_size_ = 0;
  keys_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetBlockKeysResponseProto::~GetBlockKeysResponseProto() {
  SharedDtor();
}

void GetBlockKeysResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete keys_;
  }
}

void GetBlockKeysResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetBlockKeysResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetBlockKeysResponseProto_descriptor_;
}

const GetBlockKeysResponseProto& GetBlockKeysResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetBlockKeysResponseProto* GetBlockKeysResponseProto::default_instance_ = NULL;

GetBlockKeysResponseProto* GetBlockKeysResponseProto::New() const {
  return new GetBlockKeysResponseProto;
}

void GetBlockKeysResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_keys()) {
      if (keys_ != NULL) keys_->::hadoop::hdfs::ExportedBlockKeysProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetBlockKeysResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .hadoop.hdfs.ExportedBlockKeysProto keys = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_keys()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetBlockKeysResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // optional .hadoop.hdfs.ExportedBlockKeysProto keys = 1;
  if (has_keys()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->keys(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetBlockKeysResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // optional .hadoop.hdfs.ExportedBlockKeysProto keys = 1;
  if (has_keys()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->keys(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetBlockKeysResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // optional .hadoop.hdfs.ExportedBlockKeysProto keys = 1;
    if (has_keys()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->keys());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetBlockKeysResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetBlockKeysResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetBlockKeysResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetBlockKeysResponseProto::MergeFrom(const GetBlockKeysResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_keys()) {
      mutable_keys()->::hadoop::hdfs::ExportedBlockKeysProto::MergeFrom(from.keys());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetBlockKeysResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetBlockKeysResponseProto::CopyFrom(const GetBlockKeysResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetBlockKeysResponseProto::IsInitialized() const {

  if (has_keys()) {
    if (!this->keys().IsInitialized()) return false;
  }
  return true;
}

void GetBlockKeysResponseProto::Swap(GetBlockKeysResponseProto* other) {
  if (other != this) {
    std::swap(keys_, other->keys_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetBlockKeysResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetBlockKeysResponseProto_descriptor_;
  metadata.reflection = GetBlockKeysResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

GetTransactionIdRequestProto::GetTransactionIdRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetTransactionIdRequestProto::InitAsDefaultInstance() {
}

GetTransactionIdRequestProto::GetTransactionIdRequestProto(const GetTransactionIdRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetTransactionIdRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetTransactionIdRequestProto::~GetTransactionIdRequestProto() {
  SharedDtor();
}

void GetTransactionIdRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetTransactionIdRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetTransactionIdRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetTransactionIdRequestProto_descriptor_;
}

const GetTransactionIdRequestProto& GetTransactionIdRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetTransactionIdRequestProto* GetTransactionIdRequestProto::default_instance_ = NULL;

GetTransactionIdRequestProto* GetTransactionIdRequestProto::New() const {
  return new GetTransactionIdRequestProto;
}

void GetTransactionIdRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetTransactionIdRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void GetTransactionIdRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetTransactionIdRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetTransactionIdRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetTransactionIdRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetTransactionIdRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetTransactionIdRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetTransactionIdRequestProto::MergeFrom(const GetTransactionIdRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetTransactionIdRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetTransactionIdRequestProto::CopyFrom(const GetTransactionIdRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTransactionIdRequestProto::IsInitialized() const {

  return true;
}

void GetTransactionIdRequestProto::Swap(GetTransactionIdRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetTransactionIdRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetTransactionIdRequestProto_descriptor_;
  metadata.reflection = GetTransactionIdRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetTransactionIdResponseProto::kTxIdFieldNumber;
#endif  // !_MSC_VER

GetTransactionIdResponseProto::GetTransactionIdResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetTransactionIdResponseProto::InitAsDefaultInstance() {
}

GetTransactionIdResponseProto::GetTransactionIdResponseProto(const GetTransactionIdResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetTransactionIdResponseProto::SharedCtor() {
  _cached_size_ = 0;
  txid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetTransactionIdResponseProto::~GetTransactionIdResponseProto() {
  SharedDtor();
}

void GetTransactionIdResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetTransactionIdResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetTransactionIdResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetTransactionIdResponseProto_descriptor_;
}

const GetTransactionIdResponseProto& GetTransactionIdResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetTransactionIdResponseProto* GetTransactionIdResponseProto::default_instance_ = NULL;

GetTransactionIdResponseProto* GetTransactionIdResponseProto::New() const {
  return new GetTransactionIdResponseProto;
}

void GetTransactionIdResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    txid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetTransactionIdResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 txId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &txid_)));
          set_has_txid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetTransactionIdResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 txId = 1;
  if (has_txid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->txid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetTransactionIdResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 txId = 1;
  if (has_txid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->txid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetTransactionIdResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 txId = 1;
    if (has_txid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->txid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetTransactionIdResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetTransactionIdResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetTransactionIdResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetTransactionIdResponseProto::MergeFrom(const GetTransactionIdResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_txid()) {
      set_txid(from.txid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetTransactionIdResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetTransactionIdResponseProto::CopyFrom(const GetTransactionIdResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTransactionIdResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetTransactionIdResponseProto::Swap(GetTransactionIdResponseProto* other) {
  if (other != this) {
    std::swap(txid_, other->txid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetTransactionIdResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetTransactionIdResponseProto_descriptor_;
  metadata.reflection = GetTransactionIdResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

RollEditLogRequestProto::RollEditLogRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RollEditLogRequestProto::InitAsDefaultInstance() {
}

RollEditLogRequestProto::RollEditLogRequestProto(const RollEditLogRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RollEditLogRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RollEditLogRequestProto::~RollEditLogRequestProto() {
  SharedDtor();
}

void RollEditLogRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void RollEditLogRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RollEditLogRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RollEditLogRequestProto_descriptor_;
}

const RollEditLogRequestProto& RollEditLogRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

RollEditLogRequestProto* RollEditLogRequestProto::default_instance_ = NULL;

RollEditLogRequestProto* RollEditLogRequestProto::New() const {
  return new RollEditLogRequestProto;
}

void RollEditLogRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RollEditLogRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void RollEditLogRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RollEditLogRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RollEditLogRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RollEditLogRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RollEditLogRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RollEditLogRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RollEditLogRequestProto::MergeFrom(const RollEditLogRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RollEditLogRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RollEditLogRequestProto::CopyFrom(const RollEditLogRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RollEditLogRequestProto::IsInitialized() const {

  return true;
}

void RollEditLogRequestProto::Swap(RollEditLogRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RollEditLogRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RollEditLogRequestProto_descriptor_;
  metadata.reflection = RollEditLogRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RollEditLogResponseProto::kSignatureFieldNumber;
#endif  // !_MSC_VER

RollEditLogResponseProto::RollEditLogResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RollEditLogResponseProto::InitAsDefaultInstance() {
  signature_ = const_cast< ::hadoop::hdfs::CheckpointSignatureProto*>(&::hadoop::hdfs::CheckpointSignatureProto::default_instance());
}

RollEditLogResponseProto::RollEditLogResponseProto(const RollEditLogResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RollEditLogResponseProto::SharedCtor() {
  _cached_size_ = 0;
  signature_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RollEditLogResponseProto::~RollEditLogResponseProto() {
  SharedDtor();
}

void RollEditLogResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete signature_;
  }
}

void RollEditLogResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RollEditLogResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RollEditLogResponseProto_descriptor_;
}

const RollEditLogResponseProto& RollEditLogResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

RollEditLogResponseProto* RollEditLogResponseProto::default_instance_ = NULL;

RollEditLogResponseProto* RollEditLogResponseProto::New() const {
  return new RollEditLogResponseProto;
}

void RollEditLogResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_signature()) {
      if (signature_ != NULL) signature_->::hadoop::hdfs::CheckpointSignatureProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RollEditLogResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_signature()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RollEditLogResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
  if (has_signature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->signature(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RollEditLogResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
  if (has_signature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->signature(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RollEditLogResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.CheckpointSignatureProto signature = 1;
    if (has_signature()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->signature());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RollEditLogResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RollEditLogResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RollEditLogResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RollEditLogResponseProto::MergeFrom(const RollEditLogResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_signature()) {
      mutable_signature()->::hadoop::hdfs::CheckpointSignatureProto::MergeFrom(from.signature());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RollEditLogResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RollEditLogResponseProto::CopyFrom(const RollEditLogResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RollEditLogResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_signature()) {
    if (!this->signature().IsInitialized()) return false;
  }
  return true;
}

void RollEditLogResponseProto::Swap(RollEditLogResponseProto* other) {
  if (other != this) {
    std::swap(signature_, other->signature_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RollEditLogResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RollEditLogResponseProto_descriptor_;
  metadata.reflection = RollEditLogResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

GetMostRecentCheckpointTxIdRequestProto::GetMostRecentCheckpointTxIdRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetMostRecentCheckpointTxIdRequestProto::InitAsDefaultInstance() {
}

GetMostRecentCheckpointTxIdRequestProto::GetMostRecentCheckpointTxIdRequestProto(const GetMostRecentCheckpointTxIdRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetMostRecentCheckpointTxIdRequestProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetMostRecentCheckpointTxIdRequestProto::~GetMostRecentCheckpointTxIdRequestProto() {
  SharedDtor();
}

void GetMostRecentCheckpointTxIdRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetMostRecentCheckpointTxIdRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetMostRecentCheckpointTxIdRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetMostRecentCheckpointTxIdRequestProto_descriptor_;
}

const GetMostRecentCheckpointTxIdRequestProto& GetMostRecentCheckpointTxIdRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetMostRecentCheckpointTxIdRequestProto* GetMostRecentCheckpointTxIdRequestProto::default_instance_ = NULL;

GetMostRecentCheckpointTxIdRequestProto* GetMostRecentCheckpointTxIdRequestProto::New() const {
  return new GetMostRecentCheckpointTxIdRequestProto;
}

void GetMostRecentCheckpointTxIdRequestProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetMostRecentCheckpointTxIdRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void GetMostRecentCheckpointTxIdRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetMostRecentCheckpointTxIdRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetMostRecentCheckpointTxIdRequestProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetMostRecentCheckpointTxIdRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetMostRecentCheckpointTxIdRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetMostRecentCheckpointTxIdRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetMostRecentCheckpointTxIdRequestProto::MergeFrom(const GetMostRecentCheckpointTxIdRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetMostRecentCheckpointTxIdRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetMostRecentCheckpointTxIdRequestProto::CopyFrom(const GetMostRecentCheckpointTxIdRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetMostRecentCheckpointTxIdRequestProto::IsInitialized() const {

  return true;
}

void GetMostRecentCheckpointTxIdRequestProto::Swap(GetMostRecentCheckpointTxIdRequestProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetMostRecentCheckpointTxIdRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetMostRecentCheckpointTxIdRequestProto_descriptor_;
  metadata.reflection = GetMostRecentCheckpointTxIdRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetMostRecentCheckpointTxIdResponseProto::kTxIdFieldNumber;
#endif  // !_MSC_VER

GetMostRecentCheckpointTxIdResponseProto::GetMostRecentCheckpointTxIdResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetMostRecentCheckpointTxIdResponseProto::InitAsDefaultInstance() {
}

GetMostRecentCheckpointTxIdResponseProto::GetMostRecentCheckpointTxIdResponseProto(const GetMostRecentCheckpointTxIdResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetMostRecentCheckpointTxIdResponseProto::SharedCtor() {
  _cached_size_ = 0;
  txid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetMostRecentCheckpointTxIdResponseProto::~GetMostRecentCheckpointTxIdResponseProto() {
  SharedDtor();
}

void GetMostRecentCheckpointTxIdResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetMostRecentCheckpointTxIdResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetMostRecentCheckpointTxIdResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetMostRecentCheckpointTxIdResponseProto_descriptor_;
}

const GetMostRecentCheckpointTxIdResponseProto& GetMostRecentCheckpointTxIdResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetMostRecentCheckpointTxIdResponseProto* GetMostRecentCheckpointTxIdResponseProto::default_instance_ = NULL;

GetMostRecentCheckpointTxIdResponseProto* GetMostRecentCheckpointTxIdResponseProto::New() const {
  return new GetMostRecentCheckpointTxIdResponseProto;
}

void GetMostRecentCheckpointTxIdResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    txid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetMostRecentCheckpointTxIdResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 txId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &txid_)));
          set_has_txid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetMostRecentCheckpointTxIdResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 txId = 1;
  if (has_txid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->txid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetMostRecentCheckpointTxIdResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 txId = 1;
  if (has_txid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->txid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetMostRecentCheckpointTxIdResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 txId = 1;
    if (has_txid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->txid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetMostRecentCheckpointTxIdResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetMostRecentCheckpointTxIdResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetMostRecentCheckpointTxIdResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetMostRecentCheckpointTxIdResponseProto::MergeFrom(const GetMostRecentCheckpointTxIdResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_txid()) {
      set_txid(from.txid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetMostRecentCheckpointTxIdResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetMostRecentCheckpointTxIdResponseProto::CopyFrom(const GetMostRecentCheckpointTxIdResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetMostRecentCheckpointTxIdResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetMostRecentCheckpointTxIdResponseProto::Swap(GetMostRecentCheckpointTxIdResponseProto* other) {
  if (other != this) {
    std::swap(txid_, other->txid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetMostRecentCheckpointTxIdResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetMostRecentCheckpointTxIdResponseProto_descriptor_;
  metadata.reflection = GetMostRecentCheckpointTxIdResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int ErrorReportRequestProto::kRegistrationFieldNumber;
const int ErrorReportRequestProto::kErrorCodeFieldNumber;
const int ErrorReportRequestProto::kMsgFieldNumber;
#endif  // !_MSC_VER

ErrorReportRequestProto::ErrorReportRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ErrorReportRequestProto::InitAsDefaultInstance() {
  registration_ = const_cast< ::hadoop::hdfs::NamenodeRegistrationProto*>(&::hadoop::hdfs::NamenodeRegistrationProto::default_instance());
}

ErrorReportRequestProto::ErrorReportRequestProto(const ErrorReportRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ErrorReportRequestProto::SharedCtor() {
  _cached_size_ = 0;
  registration_ = NULL;
  errorcode_ = 0u;
  msg_ = const_cast< ::std::string*>(&::google::protobuf::internal::kEmptyString);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ErrorReportRequestProto::~ErrorReportRequestProto() {
  SharedDtor();
}

void ErrorReportRequestProto::SharedDtor() {
  if (msg_ != &::google::protobuf::internal::kEmptyString) {
    delete msg_;
  }
  if (this != default_instance_) {
    delete registration_;
  }
}

void ErrorReportRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ErrorReportRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ErrorReportRequestProto_descriptor_;
}

const ErrorReportRequestProto& ErrorReportRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

ErrorReportRequestProto* ErrorReportRequestProto::default_instance_ = NULL;

ErrorReportRequestProto* ErrorReportRequestProto::New() const {
  return new ErrorReportRequestProto;
}

void ErrorReportRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_registration()) {
      if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
    }
    errorcode_ = 0u;
    if (has_msg()) {
      if (msg_ != &::google::protobuf::internal::kEmptyString) {
        msg_->clear();
      }
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ErrorReportRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_registration()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(16)) goto parse_errorCode;
        break;
      }

      // required uint32 errorCode = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
         parse_errorCode:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &errorcode_)));
          set_has_errorcode();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(26)) goto parse_msg;
        break;
      }

      // required string msg = 3;
      case 3: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_msg:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_msg()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8String(
            this->msg().data(), this->msg().length(),
            ::google::protobuf::internal::WireFormat::PARSE);
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void ErrorReportRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->registration(), output);
  }

  // required uint32 errorCode = 2;
  if (has_errorcode()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->errorcode(), output);
  }

  // required string msg = 3;
  if (has_msg()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->msg(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ErrorReportRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->registration(), target);
  }

  // required uint32 errorCode = 2;
  if (has_errorcode()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->errorcode(), target);
  }

  // required string msg = 3;
  if (has_msg()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE);
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->msg(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ErrorReportRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
    if (has_registration()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->registration());
    }

    // required uint32 errorCode = 2;
    if (has_errorcode()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->errorcode());
    }

    // required string msg = 3;
    if (has_msg()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->msg());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ErrorReportRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ErrorReportRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ErrorReportRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ErrorReportRequestProto::MergeFrom(const ErrorReportRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_registration()) {
      mutable_registration()->::hadoop::hdfs::NamenodeRegistrationProto::MergeFrom(from.registration());
    }
    if (from.has_errorcode()) {
      set_errorcode(from.errorcode());
    }
    if (from.has_msg()) {
      set_msg(from.msg());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ErrorReportRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ErrorReportRequestProto::CopyFrom(const ErrorReportRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ErrorReportRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  if (has_registration()) {
    if (!this->registration().IsInitialized()) return false;
  }
  return true;
}

void ErrorReportRequestProto::Swap(ErrorReportRequestProto* other) {
  if (other != this) {
    std::swap(registration_, other->registration_);
    std::swap(errorcode_, other->errorcode_);
    std::swap(msg_, other->msg_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ErrorReportRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ErrorReportRequestProto_descriptor_;
  metadata.reflection = ErrorReportRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

ErrorReportResponseProto::ErrorReportResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void ErrorReportResponseProto::InitAsDefaultInstance() {
}

ErrorReportResponseProto::ErrorReportResponseProto(const ErrorReportResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void ErrorReportResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

ErrorReportResponseProto::~ErrorReportResponseProto() {
  SharedDtor();
}

void ErrorReportResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void ErrorReportResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ErrorReportResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ErrorReportResponseProto_descriptor_;
}

const ErrorReportResponseProto& ErrorReportResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

ErrorReportResponseProto* ErrorReportResponseProto::default_instance_ = NULL;

ErrorReportResponseProto* ErrorReportResponseProto::New() const {
  return new ErrorReportResponseProto;
}

void ErrorReportResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool ErrorReportResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void ErrorReportResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* ErrorReportResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int ErrorReportResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ErrorReportResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const ErrorReportResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const ErrorReportResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void ErrorReportResponseProto::MergeFrom(const ErrorReportResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void ErrorReportResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ErrorReportResponseProto::CopyFrom(const ErrorReportResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ErrorReportResponseProto::IsInitialized() const {

  return true;
}

void ErrorReportResponseProto::Swap(ErrorReportResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata ErrorReportResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ErrorReportResponseProto_descriptor_;
  metadata.reflection = ErrorReportResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RegisterRequestProto::kRegistrationFieldNumber;
#endif  // !_MSC_VER

RegisterRequestProto::RegisterRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RegisterRequestProto::InitAsDefaultInstance() {
  registration_ = const_cast< ::hadoop::hdfs::NamenodeRegistrationProto*>(&::hadoop::hdfs::NamenodeRegistrationProto::default_instance());
}

RegisterRequestProto::RegisterRequestProto(const RegisterRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RegisterRequestProto::SharedCtor() {
  _cached_size_ = 0;
  registration_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RegisterRequestProto::~RegisterRequestProto() {
  SharedDtor();
}

void RegisterRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete registration_;
  }
}

void RegisterRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RegisterRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RegisterRequestProto_descriptor_;
}

const RegisterRequestProto& RegisterRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

RegisterRequestProto* RegisterRequestProto::default_instance_ = NULL;

RegisterRequestProto* RegisterRequestProto::New() const {
  return new RegisterRequestProto;
}

void RegisterRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_registration()) {
      if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RegisterRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_registration()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RegisterRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->registration(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RegisterRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->registration(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RegisterRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
    if (has_registration()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->registration());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RegisterRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RegisterRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RegisterRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RegisterRequestProto::MergeFrom(const RegisterRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_registration()) {
      mutable_registration()->::hadoop::hdfs::NamenodeRegistrationProto::MergeFrom(from.registration());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RegisterRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegisterRequestProto::CopyFrom(const RegisterRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegisterRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_registration()) {
    if (!this->registration().IsInitialized()) return false;
  }
  return true;
}

void RegisterRequestProto::Swap(RegisterRequestProto* other) {
  if (other != this) {
    std::swap(registration_, other->registration_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RegisterRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RegisterRequestProto_descriptor_;
  metadata.reflection = RegisterRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int RegisterResponseProto::kRegistrationFieldNumber;
#endif  // !_MSC_VER

RegisterResponseProto::RegisterResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void RegisterResponseProto::InitAsDefaultInstance() {
  registration_ = const_cast< ::hadoop::hdfs::NamenodeRegistrationProto*>(&::hadoop::hdfs::NamenodeRegistrationProto::default_instance());
}

RegisterResponseProto::RegisterResponseProto(const RegisterResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void RegisterResponseProto::SharedCtor() {
  _cached_size_ = 0;
  registration_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

RegisterResponseProto::~RegisterResponseProto() {
  SharedDtor();
}

void RegisterResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete registration_;
  }
}

void RegisterResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RegisterResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RegisterResponseProto_descriptor_;
}

const RegisterResponseProto& RegisterResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

RegisterResponseProto* RegisterResponseProto::default_instance_ = NULL;

RegisterResponseProto* RegisterResponseProto::New() const {
  return new RegisterResponseProto;
}

void RegisterResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_registration()) {
      if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool RegisterResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_registration()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void RegisterResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->registration(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* RegisterResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->registration(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int RegisterResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
    if (has_registration()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->registration());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RegisterResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const RegisterResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const RegisterResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void RegisterResponseProto::MergeFrom(const RegisterResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_registration()) {
      mutable_registration()->::hadoop::hdfs::NamenodeRegistrationProto::MergeFrom(from.registration());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void RegisterResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegisterResponseProto::CopyFrom(const RegisterResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegisterResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_registration()) {
    if (!this->registration().IsInitialized()) return false;
  }
  return true;
}

void RegisterResponseProto::Swap(RegisterResponseProto* other) {
  if (other != this) {
    std::swap(registration_, other->registration_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata RegisterResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RegisterResponseProto_descriptor_;
  metadata.reflection = RegisterResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int StartCheckpointRequestProto::kRegistrationFieldNumber;
#endif  // !_MSC_VER

StartCheckpointRequestProto::StartCheckpointRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void StartCheckpointRequestProto::InitAsDefaultInstance() {
  registration_ = const_cast< ::hadoop::hdfs::NamenodeRegistrationProto*>(&::hadoop::hdfs::NamenodeRegistrationProto::default_instance());
}

StartCheckpointRequestProto::StartCheckpointRequestProto(const StartCheckpointRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void StartCheckpointRequestProto::SharedCtor() {
  _cached_size_ = 0;
  registration_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

StartCheckpointRequestProto::~StartCheckpointRequestProto() {
  SharedDtor();
}

void StartCheckpointRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete registration_;
  }
}

void StartCheckpointRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StartCheckpointRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StartCheckpointRequestProto_descriptor_;
}

const StartCheckpointRequestProto& StartCheckpointRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

StartCheckpointRequestProto* StartCheckpointRequestProto::default_instance_ = NULL;

StartCheckpointRequestProto* StartCheckpointRequestProto::New() const {
  return new StartCheckpointRequestProto;
}

void StartCheckpointRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_registration()) {
      if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool StartCheckpointRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_registration()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void StartCheckpointRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->registration(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* StartCheckpointRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->registration(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int StartCheckpointRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
    if (has_registration()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->registration());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StartCheckpointRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const StartCheckpointRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const StartCheckpointRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void StartCheckpointRequestProto::MergeFrom(const StartCheckpointRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_registration()) {
      mutable_registration()->::hadoop::hdfs::NamenodeRegistrationProto::MergeFrom(from.registration());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void StartCheckpointRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StartCheckpointRequestProto::CopyFrom(const StartCheckpointRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartCheckpointRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_registration()) {
    if (!this->registration().IsInitialized()) return false;
  }
  return true;
}

void StartCheckpointRequestProto::Swap(StartCheckpointRequestProto* other) {
  if (other != this) {
    std::swap(registration_, other->registration_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata StartCheckpointRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StartCheckpointRequestProto_descriptor_;
  metadata.reflection = StartCheckpointRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int StartCheckpointResponseProto::kCommandFieldNumber;
#endif  // !_MSC_VER

StartCheckpointResponseProto::StartCheckpointResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void StartCheckpointResponseProto::InitAsDefaultInstance() {
  command_ = const_cast< ::hadoop::hdfs::NamenodeCommandProto*>(&::hadoop::hdfs::NamenodeCommandProto::default_instance());
}

StartCheckpointResponseProto::StartCheckpointResponseProto(const StartCheckpointResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void StartCheckpointResponseProto::SharedCtor() {
  _cached_size_ = 0;
  command_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

StartCheckpointResponseProto::~StartCheckpointResponseProto() {
  SharedDtor();
}

void StartCheckpointResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete command_;
  }
}

void StartCheckpointResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StartCheckpointResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StartCheckpointResponseProto_descriptor_;
}

const StartCheckpointResponseProto& StartCheckpointResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

StartCheckpointResponseProto* StartCheckpointResponseProto::default_instance_ = NULL;

StartCheckpointResponseProto* StartCheckpointResponseProto::New() const {
  return new StartCheckpointResponseProto;
}

void StartCheckpointResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_command()) {
      if (command_ != NULL) command_->::hadoop::hdfs::NamenodeCommandProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool StartCheckpointResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.NamenodeCommandProto command = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_command()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void StartCheckpointResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.NamenodeCommandProto command = 1;
  if (has_command()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->command(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* StartCheckpointResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.NamenodeCommandProto command = 1;
  if (has_command()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->command(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int StartCheckpointResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.NamenodeCommandProto command = 1;
    if (has_command()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->command());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StartCheckpointResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const StartCheckpointResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const StartCheckpointResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void StartCheckpointResponseProto::MergeFrom(const StartCheckpointResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_command()) {
      mutable_command()->::hadoop::hdfs::NamenodeCommandProto::MergeFrom(from.command());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void StartCheckpointResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StartCheckpointResponseProto::CopyFrom(const StartCheckpointResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartCheckpointResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_command()) {
    if (!this->command().IsInitialized()) return false;
  }
  return true;
}

void StartCheckpointResponseProto::Swap(StartCheckpointResponseProto* other) {
  if (other != this) {
    std::swap(command_, other->command_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata StartCheckpointResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StartCheckpointResponseProto_descriptor_;
  metadata.reflection = StartCheckpointResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int EndCheckpointRequestProto::kRegistrationFieldNumber;
const int EndCheckpointRequestProto::kSignatureFieldNumber;
#endif  // !_MSC_VER

EndCheckpointRequestProto::EndCheckpointRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void EndCheckpointRequestProto::InitAsDefaultInstance() {
  registration_ = const_cast< ::hadoop::hdfs::NamenodeRegistrationProto*>(&::hadoop::hdfs::NamenodeRegistrationProto::default_instance());
  signature_ = const_cast< ::hadoop::hdfs::CheckpointSignatureProto*>(&::hadoop::hdfs::CheckpointSignatureProto::default_instance());
}

EndCheckpointRequestProto::EndCheckpointRequestProto(const EndCheckpointRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void EndCheckpointRequestProto::SharedCtor() {
  _cached_size_ = 0;
  registration_ = NULL;
  signature_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

EndCheckpointRequestProto::~EndCheckpointRequestProto() {
  SharedDtor();
}

void EndCheckpointRequestProto::SharedDtor() {
  if (this != default_instance_) {
    delete registration_;
    delete signature_;
  }
}

void EndCheckpointRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EndCheckpointRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EndCheckpointRequestProto_descriptor_;
}

const EndCheckpointRequestProto& EndCheckpointRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

EndCheckpointRequestProto* EndCheckpointRequestProto::default_instance_ = NULL;

EndCheckpointRequestProto* EndCheckpointRequestProto::New() const {
  return new EndCheckpointRequestProto;
}

void EndCheckpointRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_registration()) {
      if (registration_ != NULL) registration_->::hadoop::hdfs::NamenodeRegistrationProto::Clear();
    }
    if (has_signature()) {
      if (signature_ != NULL) signature_->::hadoop::hdfs::CheckpointSignatureProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool EndCheckpointRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_registration()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectTag(18)) goto parse_signature;
        break;
      }

      // required .hadoop.hdfs.CheckpointSignatureProto signature = 2;
      case 2: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
         parse_signature:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_signature()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void EndCheckpointRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->registration(), output);
  }

  // required .hadoop.hdfs.CheckpointSignatureProto signature = 2;
  if (has_signature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->signature(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* EndCheckpointRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
  if (has_registration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->registration(), target);
  }

  // required .hadoop.hdfs.CheckpointSignatureProto signature = 2;
  if (has_signature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        2, this->signature(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int EndCheckpointRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.NamenodeRegistrationProto registration = 1;
    if (has_registration()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->registration());
    }

    // required .hadoop.hdfs.CheckpointSignatureProto signature = 2;
    if (has_signature()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->signature());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EndCheckpointRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const EndCheckpointRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const EndCheckpointRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void EndCheckpointRequestProto::MergeFrom(const EndCheckpointRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_registration()) {
      mutable_registration()->::hadoop::hdfs::NamenodeRegistrationProto::MergeFrom(from.registration());
    }
    if (from.has_signature()) {
      mutable_signature()->::hadoop::hdfs::CheckpointSignatureProto::MergeFrom(from.signature());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void EndCheckpointRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EndCheckpointRequestProto::CopyFrom(const EndCheckpointRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EndCheckpointRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  if (has_registration()) {
    if (!this->registration().IsInitialized()) return false;
  }
  if (has_signature()) {
    if (!this->signature().IsInitialized()) return false;
  }
  return true;
}

void EndCheckpointRequestProto::Swap(EndCheckpointRequestProto* other) {
  if (other != this) {
    std::swap(registration_, other->registration_);
    std::swap(signature_, other->signature_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata EndCheckpointRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = EndCheckpointRequestProto_descriptor_;
  metadata.reflection = EndCheckpointRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
#endif  // !_MSC_VER

EndCheckpointResponseProto::EndCheckpointResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void EndCheckpointResponseProto::InitAsDefaultInstance() {
}

EndCheckpointResponseProto::EndCheckpointResponseProto(const EndCheckpointResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void EndCheckpointResponseProto::SharedCtor() {
  _cached_size_ = 0;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

EndCheckpointResponseProto::~EndCheckpointResponseProto() {
  SharedDtor();
}

void EndCheckpointResponseProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void EndCheckpointResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EndCheckpointResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EndCheckpointResponseProto_descriptor_;
}

const EndCheckpointResponseProto& EndCheckpointResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

EndCheckpointResponseProto* EndCheckpointResponseProto::default_instance_ = NULL;

EndCheckpointResponseProto* EndCheckpointResponseProto::New() const {
  return new EndCheckpointResponseProto;
}

void EndCheckpointResponseProto::Clear() {
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool EndCheckpointResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      return true;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
  return true;
#undef DO_
}

void EndCheckpointResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* EndCheckpointResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int EndCheckpointResponseProto::ByteSize() const {
  int total_size = 0;

  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EndCheckpointResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const EndCheckpointResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const EndCheckpointResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void EndCheckpointResponseProto::MergeFrom(const EndCheckpointResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void EndCheckpointResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EndCheckpointResponseProto::CopyFrom(const EndCheckpointResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EndCheckpointResponseProto::IsInitialized() const {

  return true;
}

void EndCheckpointResponseProto::Swap(EndCheckpointResponseProto* other) {
  if (other != this) {
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata EndCheckpointResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = EndCheckpointResponseProto_descriptor_;
  metadata.reflection = EndCheckpointResponseProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetEditLogManifestRequestProto::kSinceTxIdFieldNumber;
#endif  // !_MSC_VER

GetEditLogManifestRequestProto::GetEditLogManifestRequestProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetEditLogManifestRequestProto::InitAsDefaultInstance() {
}

GetEditLogManifestRequestProto::GetEditLogManifestRequestProto(const GetEditLogManifestRequestProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetEditLogManifestRequestProto::SharedCtor() {
  _cached_size_ = 0;
  sincetxid_ = GOOGLE_ULONGLONG(0);
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetEditLogManifestRequestProto::~GetEditLogManifestRequestProto() {
  SharedDtor();
}

void GetEditLogManifestRequestProto::SharedDtor() {
  if (this != default_instance_) {
  }
}

void GetEditLogManifestRequestProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetEditLogManifestRequestProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetEditLogManifestRequestProto_descriptor_;
}

const GetEditLogManifestRequestProto& GetEditLogManifestRequestProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetEditLogManifestRequestProto* GetEditLogManifestRequestProto::default_instance_ = NULL;

GetEditLogManifestRequestProto* GetEditLogManifestRequestProto::New() const {
  return new GetEditLogManifestRequestProto;
}

void GetEditLogManifestRequestProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    sincetxid_ = GOOGLE_ULONGLONG(0);
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetEditLogManifestRequestProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required uint64 sinceTxId = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_VARINT) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &sincetxid_)));
          set_has_sincetxid();
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetEditLogManifestRequestProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required uint64 sinceTxId = 1;
  if (has_sincetxid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->sincetxid(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetEditLogManifestRequestProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required uint64 sinceTxId = 1;
  if (has_sincetxid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->sincetxid(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetEditLogManifestRequestProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required uint64 sinceTxId = 1;
    if (has_sincetxid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->sincetxid());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetEditLogManifestRequestProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetEditLogManifestRequestProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetEditLogManifestRequestProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetEditLogManifestRequestProto::MergeFrom(const GetEditLogManifestRequestProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_sincetxid()) {
      set_sincetxid(from.sincetxid());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetEditLogManifestRequestProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetEditLogManifestRequestProto::CopyFrom(const GetEditLogManifestRequestProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetEditLogManifestRequestProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void GetEditLogManifestRequestProto::Swap(GetEditLogManifestRequestProto* other) {
  if (other != this) {
    std::swap(sincetxid_, other->sincetxid_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetEditLogManifestRequestProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetEditLogManifestRequestProto_descriptor_;
  metadata.reflection = GetEditLogManifestRequestProto_reflection_;
  return metadata;
}


// ===================================================================

#ifndef _MSC_VER
const int GetEditLogManifestResponseProto::kManifestFieldNumber;
#endif  // !_MSC_VER

GetEditLogManifestResponseProto::GetEditLogManifestResponseProto()
  : ::google::protobuf::Message() {
  SharedCtor();
}

void GetEditLogManifestResponseProto::InitAsDefaultInstance() {
  manifest_ = const_cast< ::hadoop::hdfs::RemoteEditLogManifestProto*>(&::hadoop::hdfs::RemoteEditLogManifestProto::default_instance());
}

GetEditLogManifestResponseProto::GetEditLogManifestResponseProto(const GetEditLogManifestResponseProto& from)
  : ::google::protobuf::Message() {
  SharedCtor();
  MergeFrom(from);
}

void GetEditLogManifestResponseProto::SharedCtor() {
  _cached_size_ = 0;
  manifest_ = NULL;
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
}

GetEditLogManifestResponseProto::~GetEditLogManifestResponseProto() {
  SharedDtor();
}

void GetEditLogManifestResponseProto::SharedDtor() {
  if (this != default_instance_) {
    delete manifest_;
  }
}

void GetEditLogManifestResponseProto::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetEditLogManifestResponseProto::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return GetEditLogManifestResponseProto_descriptor_;
}

const GetEditLogManifestResponseProto& GetEditLogManifestResponseProto::default_instance() {
  if (default_instance_ == NULL) protobuf_AddDesc_NamenodeProtocol_2eproto();
  return *default_instance_;
}

GetEditLogManifestResponseProto* GetEditLogManifestResponseProto::default_instance_ = NULL;

GetEditLogManifestResponseProto* GetEditLogManifestResponseProto::New() const {
  return new GetEditLogManifestResponseProto;
}

void GetEditLogManifestResponseProto::Clear() {
  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (has_manifest()) {
      if (manifest_ != NULL) manifest_->::hadoop::hdfs::RemoteEditLogManifestProto::Clear();
    }
  }
  ::memset(_has_bits_, 0, sizeof(_has_bits_));
  mutable_unknown_fields()->Clear();
}

bool GetEditLogManifestResponseProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!(EXPRESSION)) return false
  ::google::protobuf::uint32 tag;
  while ((tag = input->ReadTag()) != 0) {
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
      case 1: {
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_manifest()));
        } else {
          goto handle_uninterpreted;
        }
        if (input->ExpectAtEnd()) return true;
        break;
      }

      default: {
      handle_uninterpreted:
        if (::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          return true;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
  return true;
#undef DO_
}

void GetEditLogManifestResponseProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
  if (has_manifest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->manifest(), output);
  }

  if (!unknown_fields().empty()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
}

::google::protobuf::uint8* GetEditLogManifestResponseProto::SerializeWithCachedSizesToArray(
    ::google::protobuf::uint8* target) const {
  // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
  if (has_manifest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteMessageNoVirtualToArray(
        1, this->manifest(), target);
  }

  if (!unknown_fields().empty()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  return target;
}

int GetEditLogManifestResponseProto::ByteSize() const {
  int total_size = 0;

  if (_has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    // required .hadoop.hdfs.RemoteEditLogManifestProto manifest = 1;
    if (has_manifest()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->manifest());
    }

  }
  if (!unknown_fields().empty()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = total_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetEditLogManifestResponseProto::MergeFrom(const ::google::protobuf::Message& from) {
  GOOGLE_CHECK_NE(&from, this);
  const GetEditLogManifestResponseProto* source =
    ::google::protobuf::internal::dynamic_cast_if_available<const GetEditLogManifestResponseProto*>(
      &from);
  if (source == NULL) {
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
    MergeFrom(*source);
  }
}

void GetEditLogManifestResponseProto::MergeFrom(const GetEditLogManifestResponseProto& from) {
  GOOGLE_CHECK_NE(&from, this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_manifest()) {
      mutable_manifest()->::hadoop::hdfs::RemoteEditLogManifestProto::MergeFrom(from.manifest());
    }
  }
  mutable_unknown_fields()->MergeFrom(from.unknown_fields());
}

void GetEditLogManifestResponseProto::CopyFrom(const ::google::protobuf::Message& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetEditLogManifestResponseProto::CopyFrom(const GetEditLogManifestResponseProto& from) {
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetEditLogManifestResponseProto::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  if (has_manifest()) {
    if (!this->manifest().IsInitialized()) return false;
  }
  return true;
}

void GetEditLogManifestResponseProto::Swap(GetEditLogManifestResponseProto* other) {
  if (other != this) {
    std::swap(manifest_, other->manifest_);
    std::swap(_has_bits_[0], other->_has_bits_[0]);
    _unknown_fields_.Swap(&other->_unknown_fields_);
    std::swap(_cached_size_, other->_cached_size_);
  }
}

::google::protobuf::Metadata GetEditLogManifestResponseProto::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = GetEditLogManifestResponseProto_descriptor_;
  metadata.reflection = GetEditLogManifestResponseProto_reflection_;
  return metadata;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace namenode
}  // namespace hdfs
}  // namespace hadoop

// @@protoc_insertion_point(global_scope)
