/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __TCP_PARSER_H__
#define __TCP_PARSER_H__

#include <vector>
#include <string>
#include "utils_core.h"

#include "cap_data.h"

//typedef void(fastcall *TCP_CALLBACK)(void *, struct tcp_stream *, void **);
typedef void(fastcall *TCP_CALLBACK)(void *, struct tcp_stream *, void **, struct conn *);

#define WORKER_STATE_UNKNOWN 0
#define WORKER_STATE_RUN 1
#define WORKER_STATE_IDLE 2

#define TCP_WQ_MAX_NUM 1
#define TCP_WQ_UPLOAD_MSG 0

typedef struct 
{
  volatile uint64_t cnt;             // 总数
  volatile uint64_t cnt_bytes;       // 字节总数

  volatile uint64_t cnt_ipv4;        // IPv4的总数
  volatile uint64_t cnt_ipv4_bytes;  // IPv4的字节总数

  volatile uint64_t cnt_ipv6;        // IPv6的总数
  volatile uint64_t cnt_ipv6_bytes;  // IPv6的字节总数
}stats_cnt_t;

// typedef struct 
// {
//   volatile uint64_t cnt_fwd;                 // 转发数量
//   volatile uint64_t cnt_bytes_fwd;           // 转发总字节数

//   volatile uint64_t cnt_fwd_ipv4;            // IPv4转发数量
//   volatile uint64_t cnt_fwd_ipv4_bytes;      // IPv4转发字节总数

//   volatile uint64_t cnt_fwd_ipv6;            // IPv6转发数量
//   volatile uint64_t cnt_fwd_ipv6_bytes;      // IPv6转发字节总数
// }stats_cnt_fwd_t;

typedef struct
{
  int cb; // 当前结构体大小

  volatile stats_cnt_t st_cnt;             // 数据包总数信息
  volatile stats_cnt_t st_cnt_fwd;     // 转发总数信息
  volatile stats_cnt_t st_cnt_drop;   // 丢弃总数信息
  // volatile uint64_t cnt_drop; 
  // volatile uint64_t cnt_bytes_drop;        // 未转发字节总数
  // volatile uint64_t cnt_fwd_hit;           // 命中规则转发数量
  // volatile uint64_t cnt_fwd_unknown;       // 未命中转发数量
} stats_forward_t;


class CGwCommon;
class CFilterRule;
class CIpfilterRule;
class CPortfilterRule;
class CUpload;
class CWorkerQueue;
class CTaskWorker;
struct tcp_upload_info;

class  CTcpParser
{
public:
  CTcpParser();
  virtual ~CTcpParser();

public:
  virtual void init(std::vector<std::string> &vec_source_name);

  virtual void fini();

  virtual void run();

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置TCP层回调函数。
   * @param TCP_CALLBACK callback
   */
  virtual void set_tcp_callback(TCP_CALLBACK callback);

  /**
   * 设置IP过滤规则。
   * @param CFilterRule*rule
   */
  virtual void set_ip_filter_rule(CFilterRule *rule);

  /**
   * 设置PORT过滤规则。
   * @param CFilterRule*rule
   */
  virtual void set_port_filter_rule(CFilterRule *rule);

  /**
   * 获取工作线程的状态
   * @param int no
   */
  virtual int get_worker_state(int no) const;

  /**
   * 获取工作线程的调用次数
   * @param int no
   */
  virtual uint64_t get_worker_count(int no) const;

  /**
   *  等待旧配置不再使用 
   */
  virtual int wait_for_worker_use_conf(void);

  void tcp_drop_data(struct tcp_stream *a_tcp, int drop_reason = 0);
  bool tcp_discard(struct tcp_stream *a_tcp, int dir, int num);
  bool tcp_discard_and_update(struct tcp_stream *a_tcp, int dir, int num);
  const char *get_data(const struct tcp_stream *a_tcp, int dir, int *data_len, int *offset_out);

  // 获取当前接收数据的方向
  int get_data_dir(const struct tcp_stream *a_tcp);

  // 获取连接地址
  void get_conn_addr(const struct tcp_stream *a_tcp, struct conn *pcon_out);

  // 获取连接的时间戳信息
  double get_ts(const struct tcp_stream *a_tcp) const;

  // 获取连接的时间戳信息(毫秒级)
  int64_t get_ts_ms(const struct tcp_stream *a_tcp) const;

  struct tcp_stream *clone_stream_data(const struct tcp_stream *a_tcp);
  void delete_stream_data(const struct tcp_stream *a_tcp);

  void get_forward_info(char *log_buf, size_t log_buf_len) const;

  void get_drop_info(char *log_buf, size_t log_buf_len) const;

  void get_tcp_stream_info(char *log_buf, size_t log_buf_len) const;

protected:
  CGwCommon *m_comm;
  volatile int m_quit_signal;

public:
  int callback_ip(pkt_info_t *ppi, unsigned int size, void *userdata);
  void callback_tcp(pcap_info_t *ppi, void *userdata, int st);
  

protected:
  static CTcpParser *m_tcp_parser;
  static void fastcall pp_tcp_callback(void *userdata, struct tcp_stream *a_tcp, void **this_time_not_needed);
  void tcp_callback(void *userdata, struct tcp_stream *a_tcp, void **this_time_not_needed);

  static int fastcall pp_tcp_port_filter_hit(void *userdata, unsigned short port);
  static int fastcall pp_tcp_port_white_hit(void *userdata, unsigned short port);
  int tcp_port_filter_hit(void *userdata, unsigned short port);
  int tcp_port_white_hit(void *userdata, unsigned short port);

  TCP_CALLBACK m_tcp_callback;

protected:
  static void print_tcp_stats_callback(void *);
  // static void print_tcp_stats_callback(void *);
  // static void print_drop_stats_callback(void *);
  void print_worker_stats(void) const;
  void print_drop_stats() const;
  void print_forward_stats(void) const;
  void print_tcp_stream_stats(void) const;
  int check_worker_use_conf(int worker_state[WORKER_PARAMS_MAX_NUM]);

  void stats_ip_fwd_register(stats_forward_t *stats_ip_fwd);
  void worker_cnt_register(uint64_t* worker_cnt, int no);
  void worker_state_register(int* worker_state, int no);
public:
  int worker_rutine_tcp_upload_info_inner(const tcp_upload_info*);
  void free_tcp_upload_info(const tcp_upload_info *);
  void send_tcp_session_info(const struct tcp_stream *a_tcp);

  void stats_ip_fwd_collect();

protected:
  static void free_upload_msg(const UploadMsg *);
  void add_id_json(char *p_event_id);
  void send_cb_tcp_upload_msg(const char *s);
  CWorkerQueue *new_wq_upload_msg();
  inline CWorkerQueue *get_wq_upload_msg(void) const
  {
    return m_p_wq[TCP_WQ_UPLOAD_MSG];
  }
  void free_worker_queue(CWorkerQueue *p);
  void free_task_worker(CTaskWorker *p);


  // 调用次数
  volatile static uint64_t m_worker_cnt[WORKER_PARAMS_MAX_NUM];
  // 状态
  volatile static int m_worker_state[WORKER_PARAMS_MAX_NUM];

protected:
  stats_forward_t *m_p_stats_ip_fwd;
  stats_forward_t *m_p_stats_port_fwd;
  CWorkerQueue *m_p_wq[TCP_WQ_MAX_NUM];
  CTaskWorker *m_p_tw[TCP_WQ_MAX_NUM];
  uint64_t m_tcp_msg_cnt;

  uint8_t m_p_stats_ip_fwd_count;
  pthread_mutex_t m_p_stats_ip_fwd_mutex;
  static __thread stats_forward_t *m_p_stats_ip_fwd_per_thread;
  stats_forward_t *m_p_stats_ip_fwd_array[WORKER_PARAMS_MAX_NUM];

  static __thread uint64_t* m_worker_cnt_per_thread;
  uint64_t* m_worker_cnt_array[WORKER_PARAMS_MAX_NUM];

  static __thread int* m_worker_state_per_thread;
  int* m_worker_state_array[WORKER_PARAMS_MAX_NUM];

protected:
  CIpfilterRule *m_ipfilter_rule;
  CPortfilterRule *m_portfilter_rule;
  int m_conf_tcp_lost_packet_neighbour_ignore;
  int m_conf_split_flow_mode;
  int m_work_param_num;
  bool m_need_mac;
  CUpload *m_upload;
  uint64_t g_u64_tcp_upload_ms;
  uint64_t g_u32_tcp_upload_index;
  std::string m_str_gw_ip;
  std::string m_conf_upload_name;
  int m_conf_tcp_upload;
  int m_conf_tcp_upload_msg_queue_max_num;
  int m_conf_tcp_upload_msg_queue_memory_max_size_bytes;
  int m_conf_tcp_upload_msg_thread_num;
};

// // 地址转字符串
// char *adres(char buf[256], struct tuple4 addr);

#endif // __TCP_PARSER_H__