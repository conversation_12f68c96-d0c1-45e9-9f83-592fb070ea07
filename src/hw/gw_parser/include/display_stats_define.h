/*
 * @Author: youweizhi
 * @LastEditors: youweizhi
 */


#ifndef __DISPLAY_STATS_DEFINE_H__
#define __DISPLAY_STATS_DEFINE_H__

#define HDFS_SHOW "hdfs_show"
#define YARN_SHOW "yanh_show"
#define HIVE_SHOW "hive_show"
#define HTTP_SHOW "http_show"
#define SMB_SHOW "SMB_SHOW"
#define NFS_SHOW "NFS_SHOW"
#define MONGO_SHOW "mongo_show"
#define STATS_DPDK "stats_dpdk"
#define STATS_NIC "stats_nic"
#define STATS_CAP  "stats_cap"
#define STATS_AGENT "stats_agent"
#define STATS_TCP "stats_tcp"
#define STATS_STREAM "stats_stream"
#define FTP_SHOW "ftp_show"
#define MAIL_SHOW "mail_show"
#define FLOW_SHOW "flow_show"

#define IP_FORWATD "ip forward"
#define IPV4_FORWARD "ipv4 forward"
#define IPV6_FORWARD "ipv6 forward"
#define PORT_FORWARD "port forward"
#define URL_FORWARD "url forward"
#define IP_FORWATD_BYTES "ip forward bytes"

#define LOG_QUEUE "_logger"
#define HTTP_GZIP_QUEUE "http gzip queue"
#define HTTP_GZIP_PARSER_QUEUE "http gzip parser queue"
#define HTTP_MSG_QUEUE "http msg queue"
#define HTTP_UPSTREAM_QUEUE "http upstream queue"
#define HTTP_MINIO_QUEUE "http minio queue"
#define SSL_DECRYPT_MSG_QUEUE "ssl decrypt msg"
#define SSL_PARSER_DATA_QUEUE "ssl parser data"
#define MONGO_WIRE_QUEUE "mongo wire queue"
#define MAIL_MSG_QUEUE "mail parser queue"
#define SMB_MSG_QUEUE  "smb parser_queue"
#define NFS_MSG_QUEUE  "nfs parser_queue"
#define SSL_MSG_QUEUE  "ssl parser queue"
#define UPLOAD_MSG "upload_msg"
#define TCP_UPLOAD_MSG "tcp upload msg"
#define PCAKET_FLOW "packet flow queue"

#define IP_BYTES_QPS "ip bytes"
#define IP_FORWATD_BYTES_QPS "ip forward bytes"
#define IP_PACKET_QPS "ip packet"
#define HDFS_SESSION_QPS "hdfs session"
#define YARN_SESSION_QPS "yarn session"
#define HIVE_SESSION_QPS "hive session"
#define HTTP_GZIP_QPS "http gzip inflate"
#define HTTP_GZIP_PARSER_QPS "http gzip parser"
#define HTTP_SESSSION_QPS "http session qps"
#define HTTP_MINIO_QPS "http minio qps"
#define SMB_SESSION_QPS "smb session qps"
#define NFS_SESSION_QPS "nfs session qps"
#define MONGO_WIRE_QPS  "mongo wire"
#define MONGO_SESSION_QPS "mongo session"
#define MAIL_SMTP_QPS "smtp qps"
#define MAIL_POP3_QPS "pop3 qps"
#define MAIL_IMAP_QPS "imap qps"
#define UPLOAD_MSA_QPS "upload_msg"
#define UPLOAD_BYTES_QPS "upload_bytes"
#define UPLOAD_FORWARD_BYTES_QPS "upload_forward_bytes"
#define TCP_UPLOAD_QPS "tcp upload qps"
#define PCAKET_FLOW_QPS "packet flow qps"

#define IP_BYTES "ip_bytes"

#endif //__DISPLAY_STATS_DEFINE_H__
